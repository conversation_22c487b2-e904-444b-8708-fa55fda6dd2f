
.searchBar {
    margin-bottom: 10px;
  
    .title {
      font-weight: 400;
      font-size: 14px;
      color: #9999a1;
      margin-top: 10px;
      margin-bottom: 12px;
    }
  
    .detail {
      margin-left: 5px;
      font-weight: 400;
      font-size: 14px;
      color: #116ef9;
      position: absolute;
      top: -9px;
      right: -28px;
      cursor: pointer;
    }
  
    ::v-deep {
      .el-form-item {
        margin: 0;
      }
  
      .el-dialog__body {
        padding: 0;
      }
    }
  
    .search {
      width: 100%;
      min-height: 72px;
      background: #ffffff;
      border-radius: 8px;
      padding: 16px;
  
      .search-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
  
        &-l {
          display: flex;
          align-items: center;
          justify-content: flex-start;
  
          // 任务
          &-task {
            display: flex;
            align-items: center;
            margin-right: 16px;
  
            span {
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
              text-align: right;
              color: #767684;
            }
  
            .el-select {
              margin: 0 !important;
              width: 260px;
            }
  
            ::v-deep {
              .el-input__inner {
                height: 32px !important;
              }
  
              .el-input__suffix {
                height: 48px;
                top: -8px;
              }
  
              .el-tag {
                padding: 0 10px !important;
              }
            }
          }
          // 时间范围
          &-timerange {
            display: flex;
            align-items: center;
  
            span {
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
              text-align: right;
              color: #767684;
            }
  
            ::v-deep {
              .el-range-editor.el-input__inner {
                width: 390px !important;
                height: 32px;
              }
  
              .el-date-editor .el-range__icon {
                height: 30px;
              }
  
              .el-range-separator {
                line-height: 25px;
                color: #cecece;
              }
  
              .el-date-editor .el-range__close-icon {
                text-align: right;
                height: 30px !important;
              }
            }
          }
  
          // 折线图开关按钮
          .brokenLine {
            margin-left: 16px;
            display: flex;
            align-items: center;
  
            .text {
              font-weight: 400;
              font-size: 14px;
              text-align: right;
            }
  
            .activestyle {
              font-weight: 400;
              font-size: 14px;
              text-align: right;
              color: #116ef9 !important;
            }
          }
        }
  
        &-r {
          display: flex;
  
          .reset {
            ::v-deep {
              .el-button {
                color: #2c2c35;
              }
            }
          }
          .reset {
            ::v-deep .el-button {
              margin-right: 8px;
              width: 60px;
              height: 32px;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
  
          .add {
            width: 88px;
          }
  
          .seach {
            color: #fff;
            width: 80px;
            border: none;
          }
        }
      }
  
      .search-echarts {
        position: relative;
        margin-top: 16px;
  
        .texthover {
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
  
          .texthover-text {
            position: absolute;
            bottom: -12px;
            text-align: center;
            margin-top: 2px;
            display: none;
          }
        }
  
        .texthover:hover {
          .texthover-text {
            text-align: center;
            margin-top: 2px;
            display: block;
          }
        }
  
        &-head {
          height: 35px;
          position: absolute;
          top: 0;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
  
          &-title {
            font-weight: 400;
            font-size: 12px;
            color: #9999a1;
          }
  
          &-toold {
            z-index: 999;
            display: flex;
          }
        }
  
        &-foot {
          width: 100%;
          height: 200px;
          display: flex;
          justify-content: center;
          box-sizing: border-box;
        }
      }
    }
  
    .dialogbox {
      ::v-deep .el-dialog {
        width: 650px;
      }
  
      ::v-deep .el-tabs__content {
        overflow: visible;
      }
  
      ::v-deep.el-tabs__item.is-active {
        color: #116ef9;
      }
  
      .tabs-num {
        color: #9999a1;
      }
  
      ::v-deep .el-tabs__item {
        color: #2c2c35;
      }
  
      ::v-deep .el-tabs__nav-wrap::after {
        height: 1px;
      }
  
      ::v-deep .el-tabs__nav {
        height: 54px;
        display: flex;
        align-items: flex-start;
      }
  
      ::v-deep {
        .el-dialog__headerbtn {
          top: 10px;
        }
  
        .el-dialog {
          box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
          border-radius: 8px;
        }
      }
  
      .indialog {
        ::v-deep .el-dialog {
          width: 700px !important;
          height: 460px;
        }
      }
      .session-query {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        &-tag {
          flex: 1;
        }
        &-add {
          margin-top: 10px;
          width: 120px;
        }
      }
      .tab-box {
        width: 100%;
        background: #ffffff;
        border-radius: 8px;
        box-sizing: border-box;
        padding: 12px 24px;
        padding-top: 0;
        &-top {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
  
          ::v-deep {
            .el-radio-group {
              display: flex;
            }
  
            .el-radio {
              margin-right: 20px;
            }
  
            .el-radio__label {
              padding-left: 9px;
            }
          }
        }
  
        &-mid {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
  
          .tagbox {
            width: 400px;
            min-height: 100px;
            border: 1px solid #cecece;
            border-radius: 8px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            flex-wrap: wrap;
            align-content: flex-start;
  
            span {
              display: flex;
              justify-content: center;
              align-items: center;
  
              ::v-deep {
                .el-tag .el-icon-close {
                  top: 0px !important;
                }
              }
  
              ::v-deep {
                .el-tag {
                  background: #e7f0fe;
                  border-radius: 2px;
                  color: #1b428d;
                }
  
                .el-tag.el-tag--warning {
                  background: #f9eddf;
                  border-radius: 2px;
                  color: #b76f1e;
                }
  
                .el-tag.el-tag--success {
                  background: #e0f5ee;
                  border-radius: 2px;
                  color: #006157;
                }
  
                .el-tag.el-tag--danger {
                  background: #fce7e7;
                  border-radius: 2px;
                  color: #a41818;
                }
  
                .el-tag.el-tag--info {
                  background: #f2f3f7;
                  border-radius: 2px;
                  color: #2c2c35;
                }
              }
            }
  
            .tag {
              margin: 5px 0;
              margin-left: 4px;
              height: 20px;
              line-height: 20px;
              font-weight: 400;
              font-size: 12px;
            }
          }
        }
  
        &-down {
          margin-top: 50px;
          padding: 16px 0;
          border-top: 1px solid #f2f3f7;
          display: flex;
          justify-content: space-between;
          align-items: center;
  
          .text {
            font-family: "Alibaba PuHuiTi";
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: #9999a1;
  
            span {
              color: #2c2c35;
            }
          }
  
          div:nth-child(2) {
            display: flex;
          }
  
          .el-button {
            display: flex;
            align-items: center;
            // width: 74px;
            height: 32px;
            border-color: #cecece;
            color: #2c2c35;
          }
        }
  
        // 五元组检索样式
        .quintuple-top {
          display: flex;
          justify-content: flex-start;
  
          .title {
            font-weight: 400;
            font-size: 14px;
            color: #9999a1;
          }
  
          ::v-deep {
            .el-textarea__inner {
              min-height: 200px;
            }
          }
  
          &-l {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-right: 20px;
          }
  
          &-r {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
          }
        }
  
        .quintuple-mid {
          margin-top: 24px;
          display: flex;
          justify-content: flex-start;
  
          ::v-deep {
            .el-input__inner {
              height: 32px;
            }
          }
  
          &-l {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-right: 20px;
          }
  
          &-r {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
          }
        }
  
        .quintuple-down {
          margin-top: 24px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
  
          .to-line {
            color: #767684;
          }
  
          ::v-deep {
            .el-input__inner {
              height: 32px;
            }
          }
        }
  
        // ES字段检索
        .thirdbox {
          ::v-deep {
            .el-input__inner {
              width: 314px;
              height: 32px;
            }
          }
  
          &-top {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
          }
  
          &-mid {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
          }
  
          &-midinput {
            margin-top: 10px;
            display: flex;
            flex-direction: row;
            align-items: flex-start;
  
            &-top {
              margin-right: 8px;
  
              ::v-deep {
                .el-input__inner {
                  width: 100px;
                }
              }
            }
  
            &-down {
              ::v-deep {
                .el-input__inner {
                  width: 206px;
                }
              }
            }
          }
        }
  
        ::v-deep .el-tabs__content {
          position: static;
        }
  
        ::v-deep .el-tabs__header {
          width: 100%;
        }
  
        ::v-deep .el-tabs__nav-scroll {
          height: 26px;
          font-size: 14px;
          position: relative;
  
          .el-tabs__nav {
            height: 100%;
          }
  
          .el-tabs__active-bar {
            width: 75px !important;
            height: 2px;
            position: absolute;
            bottom: 0;
          }
  
          .el-tabs__item {
            height: auto;
            line-height: 1;
          }
  
          .el-tabs__item.is-active {
            font-weight: 700;
          }
        }
      }
  
      // 暂停时的样式
      .sessionTagspause {
        background: #ffead6;
        border-radius: 4px;
        margin-top: 16px;
        margin-bottom: 6px;
        color: #b76f1e;
        margin-right: 8px;
      }
  
      ::v-deep .sessionTagspause .el-tag:hover {
        background-color: #ffead6 !important;
        border-color: #ffead6 !important;
      }
  
      ::v-deep .sessionTagspause.el-tag {
        .el-tag__close {
          color: #fff;
        }
  
        .el-tag__close:hover {
          color: #fff;
          background-color: #494c47;
        }
  
        .sessionTags1 {
          max-width: 800px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
  
        .sessionTags1:hover span {
          display: none;
        }
  
        .sessionTags1:hover div {
          display: block;
          margin-right: 10px;
          text-align: center;
          margin: 0 auto;
        }
  
        .sessionTags1 div i {
          width: 34%;
        }
  
        .sessionTags1 div {
          display: none;
          float: left;
        }
      }
  
      .sessionTags {
        background: rgba(6, 194, 81, 0.1);
        border-radius: 4px;
        margin-top: 12px;
        color: #2c6c46;
        margin-right: 8px;
      }
  
      ::v-deep .sessionTags.el-tag:hover {
        background-color: #b9eecf !important;
        border-color: #b9eecf !important;
      }
  
      ::v-deep .sessionTags.el-tag {
        .el-tag__close {
          color: #fff;
        }
  
        .el-tag__close:hover {
          color: #fff;
          background-color: #494c47;
        }
  
        .sessionTags1 {
          max-width: 800px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
  
        .sessionTags1:hover span {
          display: none;
          visibility: hidden;
        }
  
        .sessionTags1:hover div {
          display: block;
          margin-right: 10px;
          text-align: center;
          margin: 0 auto;
        }
  
        .sessionTags1 div i {
          width: 34%;
        }
  
        .sessionTags1 div {
          display: none;
          float: left;
        }
      }
    }
  }
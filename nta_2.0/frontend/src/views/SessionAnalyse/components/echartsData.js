import Vue from 'vue';
import * as echarts from 'echarts';
Vue.prototype.$echarts = echarts;
let graph = {
  nodes: [
    // {
    //   id: "c8:e7:f0:6d:33:d8",
    //   name: "c8:e7:f0:6d:33:d8",
    //   symbolSize: 20,
    //   value: "c8:e7:f0:6d:33:d8"
    // },
    // {
    //   id: "3c:8a:b0:32:59:f1",
    //   name: "3c:8a:b0:32:59:f1",
    //   symbolSize: 20,
    //   value: "3c:8a:b0:32:59:f1"
    // },
    // {
    //   id: "c8:e7:f0:6c:f3:33",
    //   name: "c8:e7:f0:6c:f3:33",
    //   symbolSize: 20,
    //   value: "c8:e7:f0:6c:f3:33"
    // },
    // {
    //   id: "3c:8a:b0:86:69:a8",
    //   name: "3c:8a:b0:86:69:a8",
    //   symbolSize: 20,
    //   value: "3c:8a:b0:86:69:a8"
    // }
  ],
  links: [

    // {
    //   source: "c8:e7:f0:6d:33:d8",
    //   target: "3c:8a:b0:32:59:f1"
    // },
    // {
    //   source: "3c:8a:b0:86:69:a8",
    //   target: "c8:e7:f0:6c:f3:33"
    // },
    // {
    //   source: "c8:e7:f0:6c:f3:33",
    //   target: "3c:8a:b0:86:69:a8"
    // },
    // {
    //   source: "3c:8a:b0:32:59:f1",
    //   target: "c8:e7:f0:6d:33:d8"
    // },
  ],
  // "categories": [{
  //     "name": "内网地址"
  //   },
  //   {
  //     "name": "外网地址"
  //   }
  // ]

};
export let mac_options = {

  tooltip: {
    formatter: (data) => {
      if (data.dataType === 'edge') {
        return `<div>
                    <b>起始点:</b>
                    <span>${data.data.source}</span>
                </div>
                <div>
                    <b>目标点:</b>
                    <span>${data.data.target}</span>
                </div>
                `;
      }
      if (data.dataType === 'node') {
        return `<div>
                    <b>Mac地址:</b>
                    <span>${data.data.name}</span>
                </div>
               
                `;
        //     <div>
        //     <b>厂商地址:</b>
        //     <span>${data.data.organization_name}</span>
        // </div>
        // <div>
        //     <b>内/互联网:</b>
        //     <span>${data.data.mac_inter}</span>
        // </div>
        // <div>
        //     <b>关联网段:</b>
        //     <span>${data.data.ip}</span>
        // </div>
        // <div>
        //     <b>掩码:</b>
        //     <span>${data.data.mask}</span>
        // </div>
      }


    },
  },
  // legend: [{
  //   data: graph.categories.map(function (a) {
  //     return a.name;
  //   })
  // }],
  animationDurationUpdate: 1500,
  animationEasingUpdate: 'quinticInOut',

  series: [{
    // top: 0,
    // right: 0,
    // bottom: 0,
    // left: 0,
    zoom: 0.8,
    // name: 'Les Miserables',
    type: 'graph',
    layout: 'circular',
    circular: {
      rotateLabel: true
    },
    data: graph.nodes,
    links: graph.links,
    // categories: graph.categories,
    // 是否可缩放：缩放:'scale',拖动:'move',全开：true
    roam: false,
    label: {
      show: true,
      position: 'right',
      formatter: '{b}',
      distance: 30,
      color: '#000',
      fontSize: 12,
      rotate: 0
    },
    itemStyle: {

    },
    // 连接线的样式
    lineStyle: {
      width: 1,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: 'blue' // 0% 处的颜色
        },
        {
          offset: 1,
          color: 'red' // 100% 处的颜色
        }
        ],
      },
      // 节点之间连线的弧度
      curveness: 0.3,
      // 阴影
      shadowColor: 'rgba(54, 36, 226, 0.7)',
      shadowBlur: 40,
      shadowOffsetX: 0,
      shadowOffsetY: 10
    }
  }]

};
export let stat_options = {
  tooltip: {
    padding: [5, 5],
    borderColor: '#ffffff',
    formatter: (data) => {
      console.log(data);
      return `<div style="font-size: 16px;
                          display: flex;
                          justify-content: space-between;
                          align-items: center;
                          min-width: 106px;
                          margin-bottom: 8px;">
                    <b style="color: #9999A1;">会话数</b>
                    <span style="color: #116EF9;" >${data.data}</span>
                </div>
                <div style="font-size: 16px;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            min-width: 106px;">
                    <b style="color: #9999A1">时间</b>
                    <span>${data.dataIndex} 时</span>
                </div>
                `;
    },
  },

  xAxis: {
    type: 'category',
    data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20', '20']
  },
  yAxis: {
    type: 'value'
  },
  grid: {
    left: '1px',
    right: '1px',
    bottom: '6%',
    top: '50px',
    containLabel: true
  },
  series: [{
    data: [120, 200, 150, 80, 70, 110, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130, 130],
    type: 'bar',
    showBackground: true,
    barGap: 0,
    emphasis: {
      focus: 'self'
    },
    itemStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
        offset: 0,
        color: '#11C1F999'
      },
      {
        offset: 1,
        color: '#116EF9'
      }
      ])
    },

  }]
};
export let ip_options = {
  bacbackgroundColor: '#f7f8fa',
  series: [{
    type: 'sankey',
    layout: 'none',
    layoutIterations: 0,
    label: {
      show: true,
      color: '#2C2C35'
    },
    emphasis: {
      focus: 'adjacency'
    },
    borderCap: 'butt',
    levels: [{
      depth: 0,
      itemStyle: {
        color: '#116EF9',
        shadowColor: 'rgba(108, 73, 172, 0.1)',
        shadowBlur: 15,
        shadowOffsetY: 10
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    }, {
      depth: 1,
      itemStyle: {
        color: '#8ABCFF'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },
    {
      depth: 2,
      itemStyle: {
        color: '#C699FF'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },
    {
      depth: 3,
      itemStyle: {
        color: '#7D4BE8'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },

    ],
    lineStyle: {
      color: 'gradient',
      curveness: 0.5, //设置边的曲度
      opacity: 0.2 //设置边的透明度
    },
    top: '2%',
    right: '15%',
    bottom: '2%',
    left: '1%',
    // 节点宽度
    nodeWidth: 6,
    // 是否可拖动
    draggable: true,
    // 左对齐
    nodeAlign: 'left',
    data: [{ "name": "8.8.8.8 " }, { "name": "端口: 53" }, { "name": "APP_DNS" }, { "name": "166.111.5.203" }, { "name": "208.67.222.222 " }, { "name": "166.111.68.23" }, { "name": "203.208.41.87 " }, { "name": "端口: 80" }, { "name": "TCP_Other" }, { "name": "166.111.73.28" }, { "name": "157.0.164.95 " }, { "name": "166.111.8.29" }, { "name": "182.254.20.44 " }, { "name": "166.111.8.28" }, { "name": "180.97.87.94 " }, { "name": "************** " }, { "name": "端口: 443" }, { "name": "TCP_QueryOnly" }, { "name": "*************" }, { "name": "************ " }, { "name": "************* " }, { "name": "**************" }, { "name": "************* " }],
    links: [{ "source": "166.111.5.203", "target": "APP_DNS", "value": 90 }, { "source": "APP_DNS", "target": "端口: 53", "value": 194 }, { "source": "端口: 53", "target": "8.8.8.8 ", "value": 90 }, { "source": "166.111.68.23", "target": "APP_DNS", "value": 28 }, { "source": "端口: 53", "target": "208.67.222.222 ", "value": 28 }, { "source": "166.111.73.28", "target": "TCP_Other", "value": 18 }, { "source": "TCP_Other", "target": "端口: 80", "value": 31 }, { "source": "端口: 80", "target": "203.208.41.87 ", "value": 18 }, { "source": "166.111.8.29", "target": "APP_DNS", "value": 47 }, { "source": "端口: 53", "target": "157.0.164.95 ", "value": 18 }, { "source": "166.111.8.28", "target": "APP_DNS", "value": 29 }, { "source": "端口: 53", "target": "182.254.20.44 ", "value": 16 }, { "source": "端口: 53", "target": "180.97.87.94 ", "value": 15 }, { "source": "*************", "target": "TCP_QueryOnly", "value": 14 }, { "source": "TCP_QueryOnly", "target": "端口: 443", "value": 14 }, { "source": "端口: 443", "target": "************** ", "value": 14 }, { "source": "端口: 53", "target": "************ ", "value": 14 }, { "source": "**************", "target": "TCP_Other", "value": 13 }, { "source": "端口: 80", "target": "************* ", "value": 13 }, { "source": "端口: 53", "target": "************* ", "value": 13 }]
  }],
  tooltip: {
    trigger: 'item'
  }
};
export let com_ip_options = {
  bacbackgroundColor: '#f7f8fa',
  series: [{
    type: 'sankey',
    layout: 'none',
    layoutIterations: 0,
    label: {
      show: true,
      color: '#2C2C35'
    },
    emphasis: {
      focus: 'adjacency'
    },
    borderCap: 'butt',
    levels: [{
      depth: 0,
      itemStyle: {
        color: '#116EF9',
        shadowColor: 'rgba(108, 73, 172, 0.1)',
        shadowBlur: 15,
        shadowOffsetY: 10
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    }, {
      depth: 1,
      itemStyle: {
        color: '#8ABCFF'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },
    {
      depth: 2,
      itemStyle: {
        color: '#C699FF'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },
    {
      depth: 3,
      itemStyle: {
        color: '#7D4BE8'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },

    ],
    lineStyle: {
      color: 'gradient',
      curveness: 0.5, //设置边的曲度
      opacity: 0.2 //设置边的透明度
    },
    top: '2%',
    right: '15%',
    bottom: '2%',
    left: '1%',
    // 节点宽度
    nodeWidth: 6,
    // 是否可拖动
    draggable: true,
    // 左对齐
    nodeAlign: 'left',
    data: [],
    links: []
  }],
  tooltip: {
    trigger: 'item'
  }
};

export default {
  data() {
    return {
      lxList: {
        2: [
          { key: 1, label: "探针数据" },
          { key: 2, label: "ES语句" },
        ],
        0: [
          { key: true, label: "服务器文件" },
          { key: false, label: "服务器文件列表" },
        ],
      },
      tabFirst: [
        { key: 1, label: "PCAP" },
        { key: 2, label: "证书" },
      ],
      tSecond: [
        { key: 1, label: "服务器数据" },
        { key: 2, label: "本地上传" }
      ],
    };
  },
  computed: {
    lxData() {
      return this.lxList[this.tabSecondIndex] || [];
    },
  },
};

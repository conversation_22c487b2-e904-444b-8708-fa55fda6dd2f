<template>
  <el-dialog
    :visible.sync="isShow"
    :width="width"
    append-to-body
    custom-class="confirm"
  >
    <div slot="title" class="title">
      <i v-if="icon" class="iconfont" :class="icon" :style="{ color }"></i>
      {{ headerTitle }}
    </div>
    <div class="content">
      <div v-if="taskName">任务名称：{{ taskName }}</div>
      <div :style="{ color }">该操作将删除已导入的所有数据，确认删除吗？</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <slot name="footer">
        <el-button @click="isShow = false">取 消</el-button>
        <el-button type="danger" @click="isShow = false">删 除</el-button>
      </slot>
    </span>
  </el-dialog>
</template>
  
<script>
import mixins from "@/mixins";
export default {
  name: "ConfirmDialog",
  mixins: [mixins],
  props: {
    width: {
      type: String,
      default: "480px",
    },
    icon: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "",
    },
    taskName: {
      type: String,
      default: "",
    },
  },
};
</script>

import Vue from 'vue';
import * as echarts from 'echarts';
Vue.prototype.$echarts = echarts;
export let taskline_options = {
  series: [
    {
      radius: '100%',
      type: 'gauge',
      center: ['50%', '50%'],
      startAngle: 200,
      endAngle: -20,
      min: 0,
      max: 100,
      splitNumber: 12,
      itemStyle: {
        color: function (params) {
          let colordata = '';
          if (1 < params.data.value && params.data.value <= 59) {
            colordata = new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
              offset: 0,
              color: " #116EF9 " // 0% 处的颜色
            }, {
              offset: 1,
              color: "#116EF9" // 100% 处的颜色
            }], false);
          } else if (60 <= params.data.value && params.data.value <= 89) {
            colordata = new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
              offset: 0,
              color: " #FA8213 " // 0% 处的颜色
            }, {
              offset: 1,
              color: "#FA8213" // 100% 处的颜色
            }], false);
          } else if (90 <= params.data.value && params.data.value <= 100) {
            colordata = new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
              offset: 0,
              color: "#F91111" // 0% 处的颜色
            }, {
              offset: 1,
              color: "#F91111" // 100% 处的颜色
            }], false);
          }
          return colordata;
        }

      },
      progress: {
        roundCap: true,
        show: true,
        width: 20,

      },
      pointer: {
        show: false
      },
      axisLine: {
        roundCap: true,
        lineStyle: {
          width: 20
        }
      },
      axisTick: {
        // distance: -45,
        splitNumber: 1,
        length: 13,
        lineStyle: {
          // width: 2,
          color: '#cecece'
        }
      },
      splitLine: {
        show: false,
        distance: -52,
        length: 14,
        lineStyle: {
          width: 3,
          color: '#999'
        }
      },
      axisLabel: {
        show: false,
        distance: -20,
        color: '#999',
        fontSize: 20
      },
      anchor: {
        show: false
      },
      title: {
        show: false
      },
      detail: {
        valueAnimation: true,
        width: '30%',
        lineHeight: 40,
        borderRadius: 8,
        offsetCenter: [0, '-2%'],
        fontSize: 45,
        fontWeight: 'bolder',
        formatter: '{value} %',
        color: 'auto'
      },
      data: [
        {
          value: 0
        }
      ]
    }
  ]
};
export let category_options = {
  xAxis: {
    show: false,
    type: 'category',
    data: []
  },
  yAxis: {
    show: false,
    type: 'value'
  }, grid: {
    top: 0,
    right: 0,
    bottom: 0
  },
  series: [
    {
      data: [],
      type: 'bar',
      showBackground: true,
      backgroundStyle: {
        color: '#E7F0FE'
      },
      label: {
        show: true,
        position: 'insideBottom',
        color: '#ffffff',
        fontSize: 12,
        formatter: function (params) {
          // console.log(params)
          let txtArry = params.name.split('');
          let rs = "";
          for (var i = 0; i < txtArry.length; i++) {
            rs += txtArry[i];
          }
          return rs;
        }

      },
      itemStyle: {
        normal: {
          color: "#4A97FF"
        }
      }
    }
  ]
};
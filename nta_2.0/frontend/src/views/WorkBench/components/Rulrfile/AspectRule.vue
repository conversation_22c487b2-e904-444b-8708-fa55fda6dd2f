/* 特征规则 */
<template>
  <div class="aspectrule p-relative">
    <el-form ref="keyRule" :rules="rules" :model="formData">
      <div class="aspectrule-top">
        <div class="aspectrule-top-l">
          <div class="title">协议名称</div>
          <el-form-item prop="pro_id" :rules="rules.ruleEmpty">
            <el-select
              v-model="formData.pro_id"
              placeholder="请选择"
              value-key="value"
              filterable
              :filter-method="handleIDProFilter"
              @visible-change="handleIDPrpVisibleChange"
            >
              <el-option
                v-for="item in id_pro_options"
                :key="item.id"
                :label="item.protocol_value"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <div class="aspectrule-top-r">
          <div class="title">特征字</div>
          <el-form-item prop="keyword" :rules="rules.ruleReg">
            <el-input
              v-model="formData.keyword"
              placeholder="请输入特征字，如：\x12\xff\x62 或 baidu 
"
            ></el-input>
          </el-form-item>
        </div>
        <el-form-item>
          <el-checkbox
            v-model="formData.is_case_sensive"
            :true-label="1"
            :false-label="0"
            >不区分大小写</el-checkbox
          >
        </el-form-item>
      </div>
    </el-form>
    <div class="aspectrule-down">
      <el-table :data="keyRuleList" style="width: 100%" ref="keyRuleList">
        <el-table-column type="index" label="序号" width="100" fixed>
        </el-table-column>
        <el-table-column prop="pro_id" label="协议ID"> </el-table-column>
        <el-table-column prop="ProName" label="协议名称"></el-table-column>
        <el-table-column prop="keyword" label="特征字"> </el-table-column>
        <el-table-column prop="is_case_sensive" label="区分大小写">
          <template slot-scope="scope">
            <span>{{ scope.row.is_case_sensive ? "不区分" : "区分" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <div class="btn">
              <el-button
                type="text"
                size="mini"
                @click="deleteRuleInfo(scope.row)"
                >删除</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="editRuleInfo(scope.row)"
                >修改</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <tablescroll :table-ref="$refs.keyRuleList"></tablescroll>
    </div>
    <!-- 添加\保存 按钮 -->
    <div class="tab-box-btn">
      <div class="tab-box-btn-l" @click="addToList('ipRule')" v-if="showsave">
        <el-button>
          <svg-icon icon-class="frule-add" />
        </el-button>
      </div>
      <div class="tab-box-btn-r" @click="editKeyRule" v-if="!showsave">
        <el-button>
          <svg-icon icon-class="saveRule" />
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import tablescroll from "../../../../components/TableScroll/idnex.vue";
import { aspectRuleName } from "@/utils";
export default {
  name: "Aspectrule",
  props: ["keyRuleData"],
  components: {
    tablescroll,
  },
  data() {
    return {
      showsave: true,
      formData: {},
      keyRuleList: [],
      keyRuleParam: [],
      id_pro_options: [],
      id_pro_full_options: [],
      showAddForm: this.showForm,
      startPort: 0,
      endPort: 65535,
      proType: { 1: "连接", 2: "单包", 3: "负载" },
      rules: {
        ruleEmpty: [{ required: true, message: "不能为空", trigger: [] }],
        ruleReg: [
          {
            required: true,
            message: "不能为空",
            trigger: ["blur"],
          },
          {
            min: 4,
            max: 1000,
            message: "长度在4-1000字符",
            trigger: "blur",
            equired: true,
          },
          {
            required: true,
            message: "请输入正确的特征字",
            trigger: ["blur"],
            validator: aspectRuleName,
          },
        ],
      },
    };
  },
  computed: {
    dict() {
      return this.$store.state.long.Dict;
    },
  },
  mounted() {
    this.initIDProOptions();
    this.initFormData();
    this.keyRuleList = [];
    this.keyRuleParam = [];
    // this.showAddForm = true
  },
  watch: {
    keyRuleData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.keyRule.clearValidate();
        });
        this.changeShowFormat();
      },
      deep: true,
    },
  },
  methods: {
    initFormData() {
      this.formData = {
        pro_id: "",
        keyword: "",
        is_case_sensive: "",
      };
    },
    // 展示数据转换为规则传参数据
    showDataToRuleParams() {
      this.keyRuleParam = [];
      this.keyRuleList.forEach((item) => {
        let tempKeyRule = {
          pro_id: item.pro_id,
          keyword: item.keyword,
          is_case_sensive: +item.is_case_sensive,
        };

        this.keyRuleParam.push(tempKeyRule);
      });
      this.$emit("getKeyRuleParam", this.keyRuleParam);
    },
    // 将后端返回数据规范为前端可展示绑定数据
    changeShowFormat() {
      this.initFormData();
      this.keyRuleList = [];
      this.keyRuleParam = [];
      // this.showAddForm = true
      if (this.keyRuleData.length !== 0) {
        for (let keyRuleItem of this.keyRuleData) {
          let tempItem = {
            index: Math.floor(Math.random() * 10000),
            pro_id: keyRuleItem.pro_id,
            keyword: keyRuleItem.keyword,
            is_case_sensive: keyRuleItem.is_case_sensive,
          };
          this.id_pro_options.forEach((item) => {
            if (item.id === keyRuleItem.pro_id) {
              tempItem.ProName = item.protocol_value;
            }
          });
          this.keyRuleList.push(tempItem);
        }
      }
    },
    // 将表单数据转换为表格可用数据
    formDataToTableData() {
      let keyRuleItem = {
        index: Math.floor(Math.random() * 10000),
        pro_id: this.formData.pro_id,
        keyword: this.formData.keyword,
        is_case_sensive: this.formData.is_case_sensive,
      };
      this.id_pro_options.forEach((item) => {
        if (item.id === this.formData.pro_id) {
          keyRuleItem.ProName = item.protocol_value;
        }
      });
      return keyRuleItem;
    },
    addToList() {
      this.$refs["keyRule"].validate((valid) => {
        if (valid) {
          this.keyRuleList.unshift(this.formDataToTableData());
          this.showDataToRuleParams();
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 修改特征字规则
    editKeyRule() {
      this.$refs["keyRule"].validate((valid) => {
        if (valid) {
          this.keyRuleList = this.keyRuleList.map((item, index) => {
            if (item.index === this.formData.index) {
              item = this.formDataToTableData();
            }
            return item;
          });
          this.showDataToRuleParams();
          this.initFormData();
          this.showAddForm = false;
          this.$message.success("保存成功");
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 将要修改的特征字规则反写到表单里
    editRuleInfo(row) {
      this.formData = JSON.parse(JSON.stringify(row));
      this.showAddForm = true;
      var scrollTop = document.querySelector("#addRules");
      if (scrollTop.scrollTop > 390) {
        scrollTop.scrollTop = 390;
      }
      this.showsave = false;
    },
    deleteRuleInfo(row) {
      this.keyRuleList.forEach((item, index) => {
        if (item.index === row.index) {
          this.keyRuleList.splice(index, 1);
        }
      });
      this.showDataToRuleParams();
    },
    // 获取协议ID
    // 获取协议ID
    initIDProOptions() {
      for (let key in this.dict.app_id) {
        this.id_pro_full_options.push({
          id: parseInt(key),
          protocol_value:
            this.proType[this.dict.app_type_map[parseInt(key)]] +
            " - " +
            this.dict.app_id[key] +
            "(" +
            this.dict.app_value[parseInt(key)] +
            ")",
        });
      }
      this.id_pro_options = this.id_pro_full_options;
    },
    handleIDProFilter(query) {
      if (query === "") {
        this.id_pro_options = this.id_pro_full_options;
      } else {
        this.id_pro_options = this.id_pro_full_options.filter((item) => {
          return (
            item.id.toString().indexOf(query.toLowerCase()) > -1 ||
            item.protocol_value.toLowerCase().indexOf(query.toLowerCase()) > -1
          );
        });
      }
    },
    handleIDPrpVisibleChange(visible) {
      if (!visible) {
        this.id_pro_options = this.id_pro_full_options;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.p-relative {
  position: relative;
  .tab-box-btn {
    position: absolute;
    top: 0px;
    right: 20px;

    .el-button {
      border: 1px solid #cecece;
      width: 35px;
      height: 27px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.aspectrule {
  &-top {
    display: flex;
    align-items: flex-end;

    ::v-deep .el-input .el-input__inner {
      height: 27px;
      width: 260px;
      font-weight: 400;
      font-size: 12px;
      padding-left: 5px;
    }

    ::v-deep .el-checkbox__label {
      font-weight: 400;
      font-size: 12px;
    }

    ::v-deep .el-input__suffix {
      height: 54px;
      top: -8px;
    }

    .el-form-item {
      margin: 0;
    }

    .title {
      display: inline-block;
    }

    &-l {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
    }

    &-r {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
    }
  }

  &-down {
    margin-top: 24px;

    .btn {
      ::v-deep .el-button {
        border: none;
      }
    }
  }
}
</style>

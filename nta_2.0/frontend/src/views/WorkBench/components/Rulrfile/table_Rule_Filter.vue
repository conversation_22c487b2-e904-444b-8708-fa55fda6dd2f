<template>
  <el-popover placement="bottom" v-model="visible">
    <div class="body">
      <div v-if="prop !== 'tags'" style="margin-bottom:10px;">
        <span class="label">排序</span>
        <!-- <div>
          <el-radio-group v-model="sort" size="mini">
            <el-radio-button :label="true">升序</el-radio-button>
            <el-radio-button :label="false">降序</el-radio-button>
          </el-radio-group>
        </div> -->
      </div>
      <div>
        <el-checkbox
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          >全选</el-checkbox
        >
        <el-scrollbar>
          <el-checkbox-group
            v-model="checkedfilters"
            @change="handleCheckedChange"
          >
            <div v-for="item in options" :key="item.value">
              <el-checkbox :label="item.value">{{ item.name }}</el-checkbox>
            </div>
          </el-checkbox-group>
        </el-scrollbar>
      </div>
    </div>
    <div class="footer">
      <el-button type="text" size="mini" @click="reset">重置</el-button>
      <el-button size="mini" type="text" :disabled="disable" @click="filter">筛选</el-button>
    </div>
    <span slot="reference">
      <span :class="{ filtered: is_filtered }">{{ label }}</span>
      <span>
        <i :class="visible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </span>
    </span>
  </el-popover>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: ''
    },
    prop: {
      type: String,
      default: ''
    },
    reset_stamp: {
      type: Number,
      default: 0
    },
    order_prop: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      sort: null,
      isIndeterminate: true,
      checkedfilters: [],
      is_filtered: false,
      checkAll: false,
      options: [],
      common_app_filter:[
        {
          name: 'IP规则',
          value: 'ip_rules'
        },
        {
          name: '协议规则',
          value: 'pro_rules'
        },
        {
          name: '特征字规则',
          value: 'key_rules'
        },
        {
          name: '正则规则',
          value: 'regex_rules'
        },
        // {
        //   name: '动态库规则',
        //   value: 'Lib_Rule'
        // },
        {
          name: '域名规则',
          value: 'domain_rules'
        }
      ]
    };
  },
  mounted() {
    this.options = this.common_app_filter;
  },
  watch: {
    reset_stamp() {
      this.is_filtered = false;
      this.visible = false;
      this.checkedfilters = [];
      this.sort = null;
    },
    order_prop(val) {
      if (val !== this.prop) {
        this.sort = null;
        if (this.checkedfilters.length === 0) {
          this.is_filtered = false;
        }
      }
    },
    options() {
      let checkedCount = this.checkedfilters.length;
      if(this.options.length !== 0 )
        this.checkAll = checkedCount === this.options.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.options.length;
    }
  },
  computed: {
    disable() {
      return (
        this.checkedfilters.length === 0 &&
        this.sort === null
      );
    }
  },
  methods: {
    handleCheckAllChange(val) {
      this.checkedfilters = val ? this.options.map(item => item.value) : [];
      this.isIndeterminate = false;
    },
    handleCheckedChange(value) {
      let checkedCount = value.length;
      if(this.options.length !== 0)
        this.checkAll = checkedCount === this.options.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.options.length;
    },
    filter() {
      this.is_filtered = true;
      this.visible = false;
      this.$emit(
        'filterHandler',
        this.prop,
        [...new Set(this.checkedfilters)],
        this.sort
      );
    },
    initData() {
      this.is_filtered = false;
      this.visible = false;
      this.checkedfilters = [];
      this.sort = null;
    },
    reset() {
      this.initData()
      this.$emit('filterHandler', null, [], null);
    }
  }
};
</script>

<style lang="scss" scoped>
// .filtered {
//   color: #409eff;
// }

.body {
  margin-top: 5px;
  margin-bottom: 15px;

  ::v-deep .el-scrollbar__wrap {
    overflow-x: hidden;
    margin-bottom: 0 !important;
  }
}

.footer {
  text-align: right;
  margin: 0 -12px 0;
  padding: 10px 12px 0;
  border-top: 1px solid #ebeef5;
  .el-button {
    padding-top: 0;
    padding-bottom: 0;
  }
}
::v-deep.el-scrollbar__view {
  max-height: 150px;
}
.el-checkbox-group {
  margin-right: 10px;
}

.label {
  font-weight: bold;
  color: black;
  display: block;
  margin-bottom: 10px;
}

::v-deep .el-radio-button .el-radio-button__inner {
  margin-right: 8px;
  border: 1px solid #DCDFE6;
  border-radius: 14px;
  box-shadow: none;
}
</style>

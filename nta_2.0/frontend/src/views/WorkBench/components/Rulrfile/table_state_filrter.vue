<!-- 自定义表头范围筛选 -->
<template>
  <el-popover placement="bottom" v-model="visible">
    <div class="body">
      <div style="margin-bottom: 10px">
        <span class="label" style="margin-bottom: 10px">排序</span>
        <div>
          <el-radio-group v-model="sort" size="mini">
            <el-radio-button :label="true">升序</el-radio-button>
            <el-radio-button :label="false">降序</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div style="margin-bottom: 10px">
        <span class="label" style="margin-bottom: 10px">状态</span>
        <div>
          <el-radio-group v-model="state" size="mini">
            <el-radio-button :label="true">生效</el-radio-button>
            <el-radio-button :label="false" @click.native="aaa"
              >失效</el-radio-button
            >
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button type="text" size="mini" @click="reset">重置</el-button>
      <el-button size="mini" type="text" :disabled="disable" @click="filter"
        >筛选</el-button
      >
    </div>
    <span slot="reference">
      <span :class="{ filtered: is_filtered }">{{ label }}</span>
      <span>
        <i :class="visible ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </span>
    </span>
  </el-popover>
</template>

<script>
export default {
  props: {
    label: {
      type: String,
      default: "",
    },
    prop: {
      type: String,
      default: "",
    },
    reset_stamp: {
      type: Number,
      default: 0,
    },
    order_prop: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      visible: false,
      sort: null,
      state: null,
      isIndeterminate: true,
      is_filtered: false,
    };
  },
  computed: {
    disable() {
      return this.sort === null && this.state === null;
    },
  },
  watch: {
    reset_stamp() {
      this.is_filtered = false;
      this.visible = false;
      this.sort = null;
      this.state = null;
    },
    order_prop(val) {
      if (val !== this.prop) {
        this.sort = null;
        this.state = null;
      }
    },
  },
  methods: {
    aaa() {},
    filter() {
      this.is_filtered = true;
      this.visible = false;
      let state = this.state;
      if (state === null) {
      } else {
        state = this.state ? "生效" : "失效";
      }

      this.$emit("filterHandler", this.prop, state, this.sort);
    },
    initData() {
      this.is_filtered = false;
      this.visible = false;
      this.sort = null;
      this.state = null;
    },
    reset() {
      this.initData();
      this.$emit("filterHandler", null, null, null);
    },
  },
};
</script>

<style lang="scss" scoped>
// .filtered {
//   color: #409eff;
// }
.body {
  margin-top: 10px;
  ::v-deep.el-input__inner {
    padding: 0 5px;
    height: 25px;
  }
  ::v-deep.el-form-item {
    margin-right: 0 !important;
  }
}

.footer {
  text-align: right;
  margin: 0 -12px 0;
  padding: 10px 12px 0;
  border-top: 1px solid #ebeef5;
  .el-button {
    padding-top: 0;
    padding-bottom: 0;
  }
}

.el-form-item {
  margin-bottom: 0;
}
.label {
  font-weight: bold;
  color: black;
  display: block;
}

::v-deep .el-radio-button .el-radio-button__inner {
  margin-right: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 14px;
  box-shadow: none;
}
</style>



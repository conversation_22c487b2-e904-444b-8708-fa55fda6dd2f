<template>
  <el-drawer
    title="任务配置"
    custom-class="lcz-drawer"
    :visible.sync="isShow"
  >
    <div class="content">
      <el-form ref="form" :model="form" label-width="80px" label-position="top">
        <el-form-item label="任务名">
          <el-input v-model="form.task_name" disabled></el-input>
        </el-form-item>
        <el-form-item label="任务描述">
          <el-input v-model="form.batch_remark" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="流量留存" prop="fullflow_state">
          <el-radio-group v-model="form.fullflow_state">
            <el-radio label="ON">全部流量</el-radio>
            <el-radio label="OFF">规则命中流量</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="元数据留存" prop="parse_proto_should_log_def_list">
          <el-tree ref="taskConfigRef" :default-checked-keys="checkedKeys" :data="taskConfig1" :props="defaultProps" show-checkbox default-expand-all
                   node-key="key"
          ></el-tree>
        </el-form-item>
        <el-form-item prop="ddos_state">
          <el-checkbox v-model="form.ddos_state">DDOS流量过滤</el-checkbox>
        </el-form-item>
      </el-form>
    </div>
    <div class="btn lcz-flex">
      <div>
        <el-button @click="TASK_CONFIG_RESET">回复默认配置</el-button>
      </div>
      <div>
        <el-button @click="isShow=false">取消</el-button>
        <el-button type="primary" :loading="taskConfigLoading" @click="handleSave">确定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { config_edit } from "@/api/MbpsData";
export default {
  name:'TaskConfigDrawer',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    taskConfigData:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      form: {
        task_name: '',
        batch_remark:'',
        parse_proto_should_log_def_list:[],// 元数据留存
        fullflow_state:'OFF', // 全流量留存
        ddos_state:0, // DDos数据留存
        full_flow_should_log_def:0, // 会话元数据保留
        parse_proto_should_log_def:0, // 协议元数据保留
      },
      checkedKeys:[], // 回显选中的节点
      taskConfigLoading:false,
      defaultProps: {
        children: "children",
        label: "label",
      },
      taskConfig1: [
        {
          label: "协议元数据",
          key: "parse_proto_should_log_def",
          children: [
            {
              label: "SSL",
              key: "10638",
            },
            {
              label: "HTTP",
              key: "10637",
            },
            {
              label: "DNS",
              key: "10071",
            },
          ],
        },
        {
          label: "会话元数据",
          key: "full_flow_should_log_def",
        },
      ],
    };
  },
  computed: {
    isShow: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit('input',val);
      }
    }
  },
  watch: {
    isShow(val) {
      if(val){
        this.checkedKeys=[];
        this.form={...this.form,...this.taskConfigData};
        this.form.ddos_state=!!this.form.ddos_state;
        if(this.form.parse_proto_should_log_def_list){
          this.checkedKeys=JSON.parse(JSON.stringify(this.form.parse_proto_should_log_def_list));
        }
        if(this.form.full_flow_should_log_def){
          this.checkedKeys.push('full_flow_should_log_def');
        }
        this.$refs.taskConfigRef?.setCheckedNodes(this.checkedKeys);
      }
    }
  },
  methods: {
    // 确定 =
    handleSave(){
      this.taskConfigLoading=true;
      let obj={...this.form};
      let checkedKeys=this.$refs.taskConfigRef.getCheckedKeys();
      const params={
        system: "probe",
        task_id:obj.task_id,
        batch_id: obj.task_id === '0' ? 100001 : 100002,
        batch_remark:obj.batch_remark,
        fullflow_state:obj.fullflow_state,
        ddos_state:+obj.ddos_state,
        full_flow_should_log_def:1, // 会话元数据
        parse_proto_should_log_def:1,  // 协议元数据
        parse_proto_should_log_def_list:checkedKeys, // 协议元数据集合
      };
      params.full_flow_should_log_def=+checkedKeys.includes('full_flow_should_log_def');
      params.parse_proto_should_log_def=+checkedKeys.some(key=>{
        return ['parse_proto_should_log_def','10638','10637','10071'].includes(key);
      });
      params.parse_proto_should_log_def_list=checkedKeys.filter(key=>{
        return !['parse_proto_should_log_def','full_flow_should_log_def'].includes(key);
      });
      config_edit(params)
        .then((res) => {
          if (res.err === 0) {
            this.$message.success(res.msg);
            this.isShow=false;
            this.taskConfigLoading=false;
            this.$emit('updateTaskInfo');
          }
        })
        .catch((err) => {
          this.taskConfigLoading = false;
        });
    },
    // 任务配置恢复默认
    TASK_CONFIG_RESET () {
      this.isShow=false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep{
    .el-drawer__body{
        display: flex;
        flex-direction: column;
    }
}
.content{
    flex: 1;
    overflow: auto;
    box-sizing: border-box;
}
</style>
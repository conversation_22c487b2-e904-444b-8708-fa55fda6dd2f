<template>
  <div v-loading="isLoading" class="workbench">
    <Manage @toggle="toggle" />
    <div v-if="!isLoading" class="tab-box">
      <el-tabs v-model="activeName" class="tabs">
        <el-tab-pane label="任务态势" name="first">
          <task-state v-if="activeName === 'first'" ref="task" />
        </el-tab-pane>
        <el-tab-pane label="过滤规则" name="second">
          <filtrate-rule v-if="activeName === 'second'" />
        </el-tab-pane>
        <el-tab-pane label="采集规则" name="third">
          <CollectRule v-if="activeName === 'third'" />
        </el-tab-pane>
        <el-tab-pane label="威胁情报" name="threat">
          <ThreatIsr v-if="activeName === 'threat'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import TaskState from "./components/TaskState";
import Manage from "./components/manage";
import FiltrateRule from "./components/FiltrateRule";
import CollectRule from "./components/CollectRule";
import ThreatIsr from "./components/ThreatISR";
import {getdiskmode } from '@/api/SystemData/systemdata';
export default {
  name: "Workbench",
  components: {
    TaskState,
    Manage,
    FiltrateRule,
    CollectRule,
    ThreatIsr
  },
  data () {
    return {
      activeName: "first",
      isLoading:false
    };
  },
  created () {
    this.CHECK_CACHE();
  },
  mounted () {
    // 获取当前硬盘模式
    this.Getdiskmode();
    this.A();
  },
  methods: {
    toggle(){
      this.isLoading=true;
      setTimeout(()=>{
        this.isLoading=false;
      },1000);
    },
    // 获取硬盘操作当前模式
    Getdiskmode () {
      getdiskmode().then((res) => {
        this.$store.commit('ifonconduct/getdiskmode', res.data);
      });
    },
    // 检查缓存并初始化最新websocket数据
    CHECK_CACHE () {
      if (!localStorage.getItem("task_id")) {
        localStorage.setItem("task_id", 0);
      }
    },
    A () {
      this.$store.dispatch("WS_MESSAGE", "request-data");
    },
  },
};
</script>

<style lang="scss" scoped>
.workbench {
  width: 100%;
  height: 100%;
  background: #F2F3F7;
  border-radius: 8px;
  box-sizing: border-box;
}

.tab-box {
 

  .temp {
    width: 100%;
    height: 130px;
  }
}

.tabs {
  ::v-deep .el-tabs__content {
    overflow: visible;
  }

  ::v-deep.el-tabs__item.is-active {
    color: #116EF9;
  }

  .tabs-num {
    color: #9999a1;
  }

  ::v-deep .el-tabs__item {
    color: #2c2c35;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    height: 2px;
    background-color: #F2F3F7;
  }

  ::v-deep .el-tabs__nav {
    height: 54px;
    display: flex;
    align-items: center;
  }
}

.tab-box {
  width: 100%;
  background: #ffffff;
  padding: 0px 16px 12px;
  box-sizing: border-box;

  .temp {
    width: 100%;
    height: 130px;
  }

}

.tabs {

  ::v-deep .el-tabs__content {
    overflow: visible;
  }

  ::v-deep.el-tabs__item.is-active {
    color: #116ef9;
  }

  .tabs-num {
    color: #9999a1;
  }

  ::v-deep .el-tabs__item {
    color: #2c2c35;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #f2f3f7;
  }

  ::v-deep .el-tabs__nav {
    height: 54px;
    display: flex;
    align-items: center;
  }
}
</style>
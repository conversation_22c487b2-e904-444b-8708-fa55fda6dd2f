/* eslint-disable indent */
import $store from '../../store';
export function formatGraphData(data) {
  try {
    let nodes = data.vertex;
    if (data.label_vertex) {
      nodes = nodes.concat(data.label_vertex);
    }
    let lines = data.edge || [];
    let contains = data.contains || [];
    // 节点数据匹配
    let tempNodes = [];
    nodes.forEach(i => {
      let label = i.label || 'N/A';
      let labels = i.labels || '';
      let vid = i.vid || '';
      let img;
      let disableDefaultClickEffect = false;
      if (i.type === 'Folder') {
        label = folder_fmt(i);
        img = folder_img_fmt(i.label).img;
        disableDefaultClickEffect = true;
      } else if (i.type === 'LABELS') {
        label = ' ';
        disableDefaultClickEffect = true;
      } else {
        // eslint-disable-next-line eqeqeq
        if (i.identity == 'attacker') {
          img = require('../../assets/graph/icon_attacker_wihte.svg');
          // eslint-disable-next-line eqeqeq
        } else if (i.identity == 'victim') {
          img = require('../../assets/graph/icon_sufferer_wihte.svg');
        } else {
          img = node_img_fmt(i.type).icon.img;
        }
        disableDefaultClickEffect = false;
      }

      let temp = {
        id: i.id,
        text: i.label,
        styleClass: 'raw-node',
        borderWidth: '0',
        width: 40,
        height: 40,
        disableDefaultClickEffect: disableDefaultClickEffect,
        data: {
          id: i.id,
          label: label,
          hideLabel: label,
          type: i.type,
          img: img,
          labels: labels,
          vid: vid,
          identity: i.identity || '',
          sign: false
        }
      };
      tempNodes.push({
        ...temp
      });

    });
    // 线条数据匹配
    let templines = [];
    lines.forEach(k => {
      let label = k.label || '';
      let isHide = k.isHide = k.isHide || false;
      if (label !== '') label = folder_fmt(k);
      let temp = {
        id: k.id || '',
        from: k.from,
        to: k.to,
        text: label,
        color: '#2C2C35',
        fontColor: '#2C2C35',
        data: k,
        x: 3,
        isHide
      };
      templines.push({
        ...temp
      });
    });
    let send = {
      rootId: 'a',
      nodes: tempNodes,
      lines: templines
    };
    return send;
  } catch (error) {
    console.log(error);
  }
}

function folder_fmt(node) {
  let label = node.label || 'N/A';
  let text = node.label || 'N/A';
  let reg = new RegExp('s_');
  let reg2 = new RegExp('d_');
  let reg3 = new RegExp('r_');
  // eslint-disable-next-line eqeqeq
  if (label.substr(0, 4) == 'APP_') {
    return label;
  }
  if (node.label && $store.state.long.Dict.nebula_type[node.label.replace(reg, '')]) {
    text = $store.state.long.Dict.nebula_type[node.label.replace(reg, '')].desc || node.label || 'N/A';
  }
  if (node.label && $store.state.long.Dict.nebula_type[node.label.replace(reg2, '')]) {
    text = $store.state.long.Dict.nebula_type[node.label.replace(reg2, '')].desc || node.label || 'N/A';
  }
  if (node.label && $store.state.long.Dict.nebula_type[node.label.replace(reg3, '')]) {
    text = $store.state.long.Dict.nebula_type[node.label.replace(reg3, '')].desc || node.label || 'N/A';
  }
  label = node.label.replace(reg, '').replace(reg2, '').replace(reg3, '');
  return text;
}

function folder_img_fmt(label) {
  let reg = new RegExp('s_');
  let reg2 = new RegExp('d_');
  let reg3 = new RegExp('r_');
  label = label.replace(reg, '').replace(reg2, '').replace(reg3, '');
  if (label === "IP") {
    return {
      img: require('../../assets/icon/file-IP.svg')
    };
  } else if (label === 'MAC') {
    return {
      img: require('../../assets/icon/file-MAC.svg')
    };
  } else if (label === 'UA') {
    return {
      img: require('../../assets/icon/file-UA.svg')
    };
  } else if (label === 'DOMAIN') {
    return {
      img: require('../../assets/icon/file-DOMAIN.svg')
    };
  } else if (label === 'APPSERVICE') {
    return {
      img: require('../../assets/icon/file-APPSERVICE.svg')
    };
  } else if (label === 'FDOMAIN') {
    return {
      img: require('../../assets/icon/file-FDOMAIN.svg')
    };
  } else if (label === 'CERT') {
    return {
      img: require('../../assets/icon/file-CERT.svg')
    };
  } else if (label === 'SSLFINGER') {
    return {
      img: require('../../assets/icon/file-SSLFINGER.svg')
    };
  } else if (label === 'URL') {
    return {
      img: require('../../assets/icon/file-URL.svg')
    };
  } else if (label === 'ORG') {
    return {
      img: require('../../assets/icon/file-ORG.svg')
    };
  } else if (label === 'ISSUER') {
    return {
      img: require('../../assets/icon/file-ISSUER.svg')
    };
  } else if (label === 'SUBJECT') {
    return {
      img: require('../../assets/icon/file-OWNER.svg')
    };
  } else if (label === 'APP') {
    return {
      img: require('../../assets/graph/file-APPSERVICE.svg')
    };
  } else {
    return {
      img: require('../../assets/graph/URL_默认.svg')
    };
  }
}

function node_img_fmt(type) {
  switch (type) {
    // IP
    case 'IP':
      return {
        icon: {
          img: require('../../assets/graph/Node_IP_Blue.svg')
        }
      };
    // 域名
    case 'DOMAIN':
      return {
        icon: {
          img: require('../../assets/graph/Node_web_Blue.svg')
        }
      };
    // MAC
    case 'MAC':
      return {
        icon: {
          img: require('../../assets/graph/Node_MAC_Blue.svg')
        }
      };
    // 应用服务
    case 'APPSERVICE':
      return {
        icon: {
          img: require('../../assets/graph/Node_APP_Blue.svg')
        }
      };
    // 锚域名
    case 'FDOMAIN':
      return {
        icon: {
          img: require('../../assets/graph/Node_anchor_Blue.svg')
        }
      };
    // 证书CERT
    case 'CERT':
      return {
        icon: {
          img: require('../../assets/graph/Node_certificate_Blue.svg')
        }
      };
    // 企业
    case 'ORG':
      return {
        icon: {
          img: require('../../assets/graph/Node_enterprise_Blue.svg')
        }
      };
    // 指纹
    case 'SSLFINGER':
      return {
        icon: {
          img: require('../../assets/graph/Node_fingerprint_Blue.svg')
        }
      };
    case 'FINGER':
      return {
        icon: {
          img: require('../../assets/graph/Node_fingerprint_Blue.svg')
        }
      };
    // UA
    case 'UA':
      return {
        icon: {
          img: require('../../assets/graph/Node_UA_Blue.svg')
        }
      };
    // 硬件类型
    case 'DEVICE':
      return {
        icon: {
          img: require('../../assets/graph/Node_hardware_Blue.svg')
        }
      };
    // 系统类型
    case 'OS':
      return {
        icon: {
          img: require('../../assets/graph/Node_system_Blue.svg')
        }
      };
    // 应用类型
    case 'APP':
      return {
        icon: {
          img: require('../../assets/graph/Node_Apptype_Blue.svg')
        }
      };
    // 资产测绘
    case 'SURVEY_DATA':
      return {
        icon: {
          img: require('../../assets/graph/icon_资产测绘_默认.svg')
        }
      };
    // URL
    case 'URL':
      return {
        icon: {
          img: require('../../assets/graph/URL_默认.svg')
        }
      };
    // 区块链
    case 'BLOCKCHAIN':
      return {
        icon: {
          img: require('../../assets/graph/icon_blockchain.svg')
        }
      };
    // keyID
    case 'KEYID':
      return {
        icon: {
          img: require('../../assets/graph/Node_KeyID_Blue.svg')
        }
      };
    // AUTHORITYKEY
    case 'AUTHORITYKEY':
      return {
        icon: {
          img: require('../../assets/graph/Node_Authority_Blue.svg')
        }
      };
    // 所有者
    case 'ISSUER':
      return {
        icon: {
          img: require('../../assets/graph/Node_organization_Blue.svg')
        }
      };
    // SUBJECT
    case 'SUBJECT':
      return {
        icon: {
          img: require('../../assets/graph/Node_owner_Blue.svg')
        }
      };
    // VICTIM
    case 'VICTIM':
      return {
        icon: {
          img: require('../../assets/graph/Sufferer.svg')
        }
      };
    // VICTIM
    case 'ATTACKER':
      return {
        icon: {
          img: require('../../assets/graph/Attack.svg')
        }
      };
    default:
      return {
        icon: {
          img: require('../../assets/graph/Sufferer.svg')
        }
      };

  }
}



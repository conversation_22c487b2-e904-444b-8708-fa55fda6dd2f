<template>
  <div>
    <el-table
      :data="tableData"
      stripe
      style="overflow:auto;"
      :default-sort="{prop: 'session_cnt', order: 'descending'}"
    >
      <el-table-column
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="mac"
        label="MAC"
        min-width="150"
      >
      </el-table-column>
      <el-table-column
        prop="black_list"
        label="黑名单权值"
        min-width="120"
        sortable
      >
      </el-table-column>
      <el-table-column
        prop="white_list"
        label="白名单权值"
        min-width="120"
        sortable
      >
      </el-table-column>
      <!-- <el-table-column
        prop="packets"
        label="包数"
        width="100"
        sortable
      >
      </el-table-column> -->
      <el-table-column
        prop="first_seen"
        label="首次出现时间"
        :formatter="FIRST_DATE"
        min-width="105"
      >
      </el-table-column>
      <el-table-column
        prop="last_seen"
        label="末次出现时间"
        :formatter="LAST_TIME"
        min-width="105"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  methods: {
    SORT_CHANGE(field){
      let data ={
        order_field:field.prop,
        sort_order:''
      };
      if(field.prop === 'times') data.order_field = 'session_cnt';
      if(field.order === 'descending'){
        data.sort_order = 'desc';
      }else if (field.order === 'ascending'){
        data.sort_order = 'asc';
      }else{
        data.sort_order = 'desc';
      }
      this.$emit('SORT_CHANGE_GETDATA',data);
    },
    FIRST_DATE (date) {
      return dayjs(date.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (date) {
      return dayjs(date.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
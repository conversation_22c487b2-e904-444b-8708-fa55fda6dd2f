<template>
  <div class="pr30">
    <el-table
      style="width:100%;overflow:auto"
      :data="tableData"
      border
      :default-sort="{prop: 'session_cnt', order: 'descending'}"
      @sort-change="SORT_CHANGE"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="AppName"
        label="应用"
      >
      </el-table-column>
      <el-table-column
        prop="dPort"
        label="端口"
      >
      </el-table-column>
      <el-table-column
        prop="IPPro"
        label="IP-Pro"
        :formatter="IP_PRO"
      >
      </el-table-column>
      <el-table-column
        prop="ip_addr"
        label="IP地址"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="session_cnt"
        label="会话数"
        width="100"
        sortable="custom"
      >
      </el-table-column>
      <el-table-column
        prop="first_seen"
        label="首次出现时间"
        :formatter="FIRST_DATE"
        width="105"
      >
      </el-table-column>
      <el-table-column
        prop="last_seen"
        label="末次出现时间"
        :formatter="LAST_TIME"
        width="105"
      > -->
      <!-- </el-table-column> -->
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  methods: {
    SORT_CHANGE(field){
      let data ={
        order_field:field.prop,
        sort_order:''
      };
      if(field.prop === 'times') data.order_field = 'session_cnt';
      if(field.order === 'descending'){
        data.sort_order = 'desc';
      }else if (field.order === 'ascending'){
        data.sort_order = 'asc';
      }else{
        data.sort_order = 'desc';
      }
      this.$emit('SORT_CHANGE_GETDATA',data);
    },
    IP_PRO (row) {
      return this.$store.state.long.Dict.protocol_type[row.IPPro]?.protocol_type || 'N/A';
    },
    FIRST_DATE (row) {
      return dayjs(row.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (row) {
      return dayjs(row.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
  }
};
</script>

<style lang="scss" scoped>
.pr30{
  padding-right: 30px;
}
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
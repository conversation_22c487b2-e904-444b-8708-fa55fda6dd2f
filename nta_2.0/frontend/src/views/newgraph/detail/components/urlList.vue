<template>
  <div>
    <el-table
      :data="tableData"
      stripe
      style="overflow:auto"
    >
      <el-table-column
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="url_key"
        label="相关URL"
        width="200"
      >
      </el-table-column>
      <el-table-column
        prop="black_list"
        label="黑名单权值"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="white_list"
        label="白名单权值"
        width="100"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  methods: {
    FIRST_DATE (date) {
      return dayjs(date.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (date) {
      return dayjs(date.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
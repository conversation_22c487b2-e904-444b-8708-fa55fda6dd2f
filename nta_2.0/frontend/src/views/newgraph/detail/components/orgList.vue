<template>
  <div class="pr30">
    <el-table
      :data="tableData"
      border
      stripe
      style="overflow:auto"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="org_name"
        label="企业名"
      >
      </el-table-column>
      <el-table-column
        prop="org_desc"
        label="企业说明"
      >
      </el-table-column>
      <el-table-column
        prop="black_list"
        label="黑名单权值"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="white_list"
        label="白名单权值"
        width="100"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  methods: {
    FIRST_DATE (date) {
      return dayjs(date.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (date) {
      return dayjs(date.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    }
  }
};
</script>

<style lang="scss" scoped>
.pr30{
  padding-right: 30px;
}
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
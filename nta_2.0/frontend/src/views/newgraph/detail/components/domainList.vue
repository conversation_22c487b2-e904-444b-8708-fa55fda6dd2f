<template>
  <div class="pr30">
    <el-table
      :data="tableData"
      stripe
      border
      style="overflow:auto"
      :default-sort="{prop: 'session_cnt', order: 'descending'}"
      @sort-change="SORT_CHANGE"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="domain_addr"
        label="域名"
        min-width="150"
      >
      </el-table-column>
      <el-table-column
        prop="black_list"
        label="黑名单权值"
        min-width="115"
        sortable="custom"
      >
      </el-table-column>
      <el-table-column
        prop="white_list"
        label="白名单权值"
        min-width="115"
        sortable="custom"
      >
      </el-table-column>
      <el-table-column
        prop="fDomain"
        label="锚域名"
        min-width="100"
      >
      </el-table-column>
      <el-table-column
        prop="alexa_rank"
        label="Alex排名"
      >
      </el-table-column>
      <el-table-column
        prop="whois"
        label="WhoIS"
        min-width="100"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="session_cnt"
        label="次数"
        sortable="custom"
      >
      </el-table-column> -->
      <el-table-column
        prop="first_seen"
        label="首次出现时间"
        :formatter="FIRST_DATE"
        min-width="110"
      >
      </el-table-column>
      <el-table-column
        prop="last_seen"
        label="末次出现时间"
        :formatter="LAST_TIME"
        min-width="110"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  methods: {
    SORT_CHANGE(field){
      let data ={
        order_field:field.prop,
        sort_order:''
      };
      if(field.prop === 'times') data.order_field = 'session_cnt';
      if(field.order === 'descending'){
        data.sort_order = 'desc';
      }else if (field.order === 'ascending'){
        data.sort_order = 'asc';
      }else{
        data.sort_order = 'desc';
      }
      this.$emit('SORT_CHANGE_GETDATA',data);
    },
    FIRST_DATE (date) {
      return dayjs(date.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (date) {
      return dayjs(date.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    }
  }
};
</script>

<style lang="scss" scoped>
.pr30{
  padding-right: 30px;
}
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
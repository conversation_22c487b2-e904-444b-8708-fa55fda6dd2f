<template>
  <div>
    <el-table
      :data="tableData"
      stripe
      style="overflow:auto"
    >
      <el-table-column
        type="index"
        width="50"
        label="序号"
      >
      </el-table-column>
      <el-table-column
        prop="ua_str"
        label="UA"
        min-width="200"
      >
      </el-table-column>
      <el-table-column
        prop="ua_id"
        label="UA说明"
        min-width="200"
      >
      </el-table-column>
    
      <el-table-column
        prop="first_seen"
        label="首次出现时间"
        :formatter="FIRST_DATE"
        min-width="105"
      >
      </el-table-column>
      <el-table-column
        prop="last_seen"
        label="末次出现时间"
        :formatter="LAST_TIME"
        min-width="105"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs';
export default {
  props: ['tableData'],
  methods: {
    FIRST_DATE (date) {
      return dayjs(date.first_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    LAST_TIME (date) {
      return dayjs(date.last_seen * 1000).format("YYYY-MM-DD HH:mm:ss");
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
<template>
  <div class="pr30">
    <el-table
      :data="tableData"
      stripe
      border
      style="overflow:auto"
    >
      <el-table-column
        label="序号"
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="app_name"
        label="应用名称"
      >
      </el-table-column>
      <el-table-column
        prop="app_version"
        label="应用版本"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  props: ['tableData']
};
</script>

<style lang="scss" scoped>
.pr30{
  padding-right: 30px;
}
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
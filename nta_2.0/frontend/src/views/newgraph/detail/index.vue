<template>
  <div>
    <el-drawer
      :visible.sync="visibleShow"
      destroy-on-close
      :show-close="false"
      :before-close="BEFORE_CLOSE"
      :with-header="false"
      :append-to-body="false"
      size="40%"
    >
      <div
        v-loading="detailLoading"
        element-loading-text="数据请求中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <div class="header">
          <div>
            <img :src="typeImg" alt="" />
          </div>
          <div class="title">
            {{ title }}
          </div>
          <div class="blackList">
            <img v-if="detailType !== 'APP'" :src="FMT_BLACK()" alt="" />
          </div>
        </div>
        <div v-if="detailType === 'DOMAIN'" class="cell">
          <div class="label">
            <div>锚域名</div>
          </div>
          <div class="text">{{ fdomain }}</div>
        </div>
        <div v-if="detailType === 'ORG'" class="cell">
          <div class="label">
            <div></div>
            <div>简介</div>
          </div>
          <div class="text">{{ org_desc }}</div>
        </div>
        <div v-if="detailType === 'CERT'" class="cell">
          <div class="label">
            <div></div>
            <div>父证书</div>
          </div>
          <div class="text">{{ fatherId }}</div>
        </div>
        <div v-if="detailType === 'IP'" class="cell">
          <div class="label">
            <div>
              <img src="../../../assets/images/Subtract.svg" alt="" />
            </div>
            <div>位置</div>
          </div>
          <div class="text">{{ position }}</div>
        </div>
        <div
          v-if="
            ['CERT', 'IP', 'DOMAIN', 'SSLFINGER', 'APP'].includes(detailType)
          "
          class="cell"
        >
          <div class="label">
            <div>
              <img src="../../../assets/images/detail-label.svg" alt="" />
            </div>
            <div>标签</div>
          </div>
          <div id="tag">
            <div class="content">
              <div
                class="labels"
                :style="{ maxHeight: labelsView ? '' : '54px' }"
              >
                <div
                  v-for="(item, index) of labels"
                  id="label"
                  :key="index"
                  :class="
                    item.tag_level == 'danger'
                      ? 'redTag'
                      : item.tag_level == 'warning'
                        ? 'yellowTag'
                        : item.tag_level == 'info'
                          ? 'grayTag'
                          : 'blueTag'
                  "
                >
                  {{ item.tag_text }}
                  <!-- <span class="del" @click="DEL_TAG(item)">×</span> -->
                </div>
              </div>
              <div class="add" @click="ADD_TAG">+ 标签</div>
            </div>
            <div
              v-if="labelsView"
              class="more"
              @click="
                () => {
                  labelsView = !labelsView;
                }
              "
            >
              收起
            </div>
            <div
              v-else
              class="more"
              @click="
                () => {
                  labelsView = !labelsView;
                }
              "
            >
              展开
            </div>
          </div>
        </div>
        <div
          v-if="
            detailType === 'IP' ||
              detailType === 'CERT' ||
              detailType === 'DOMAIN' ||
              detailType === 'ORG'
          "
          class="cell"
        >
          <div class="label">
            <div>
              <img src="../../../assets/images/Union.svg" alt="" />
            </div>
            <div>备注</div>
          </div>
          <div class="remark">
            <div class="content">
              <div class="labels">
                <div
                  v-for="(item, index) of remarks"
                  :key="index"
                  class="label"
                >
                  {{ item }}
                  <span class="del" @click="DEL_REMARK(item)">×</span>
                </div>
                <el-input
                  v-if="remarkShow"
                  v-model="remark"
                  size="mini"
                  placeholder=""
                  @change="REMARK_SUBMIT"
                ></el-input>
                <div
                  class="add"
                  @click="
                    () => {
                      (remark = ''), (remarkShow = true);
                    }
                  "
                >
                  + 备注
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="detailType === 'IP'" class="flex-list">
          <div
            v-for="(item, index) of ip_tag_list"
            :key="index"
            class="flex-list-item"
          >
            <div>{{ item.name }}</div>
            <div>{{ item.num }}</div>
            <aside></aside>
          </div>
        </div>
        <div v-if="detailType === 'DOMAIN'" class="flex-list">
          <div
            v-for="(item, index) of domain_tag_list"
            :key="index"
            class="flex-list-item"
          >
            <div>{{ item.name }}</div>
            <div>{{ item.num }}</div>
            <aside></aside>
          </div>
        </div>
        <div v-if="detailType === 'CERT'" class="flex-list">
          <div
            v-for="(item, index) of cert_tag_list"
            :key="index"
            class="flex-list-item"
          >
            <div>{{ item.name }}</div>
            <div>{{ item.num }}</div>
            <aside></aside>
          </div>
        </div>
        <div v-if="detailType === 'BLOCKCHAIN'" class="flex-list">
          <div
            v-for="(item, index) of blo_tag_list"
            :key="index"
            class="flex-list-item"
          >
            <div>{{ item.name }}</div>
            <div>{{ item.num }}</div>
            <aside></aside>
          </div>
        </div>
        <div v-if="detailType === 'SSLFINGER'" class="flex-list">
          <div
            v-for="(item, index) of ssl_tag_list"
            :key="index"
            class="flex-list-item"
          >
            <div>{{ item.name }}</div>
            <div>{{ item.num }}</div>
            <aside></aside>
          </div>
        </div>
        <div v-if="detailType === 'APPSERVICE'" class="flex-list">
          <div
            v-for="(item, index) of app_tag_list"
            :key="index"
            class="flex-list-item"
          >
            <div>{{ item.name }}</div>
            <div>{{ item.num }}</div>
            <aside></aside>
          </div>
        </div>
        <div
          v-if="detailType === 'APP'"
          class="flex-list"
          style="justify-content: flex-start"
        >
          <div class="flex-list-item">
            <div>应用名称</div>
            <div>{{ appInfo.app_name ? appInfo.app_name : "-" }}</div>
            <aside></aside>
          </div>
          <div class="flex-list-item">
            <div>应用版本</div>
            <div>{{ appInfo.app_version ? appInfo.app_version : "-" }}</div>
            <aside></aside>
          </div>
        </div>
        <!-- ==========================分割线=============================== -->
        <div>
          <div class="table-tile">
            <div>关系</div>
            <el-select
              v-model="tIndex"
              placeholder="请选择"
              filterable
              @change="DETAIL_TABLE_CHANGE"
            >
              <el-option
                v-for="item in tOpt"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="page">
            <el-pagination
              layout="total,prev, pager, next"
              :total="pageTotal"
              :current-page="page"
              hide-on-single-page
              :pager-count="5"
              @current-change="CURRENT_CHANGE"
            >
            </el-pagination>
          </div>
          <div>
            <MacList
              v-if="tableType === 'MAC'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Iplist
              v-if="tableType === 'IP'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Applist
              v-if="tableType === 'APPSERVICE'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <!-- <Surveylist
              v-if="tableType === 'SURVEY_DATA'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            /> -->
            <Fdomainlist
              v-if="tableType === 'FDOMAIN'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Domainlist
              v-if="tableType === 'DOMAIN'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Certlist
              v-if="tableType === 'CERT'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Ualist
              v-if="tableType === 'UA'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Orglist
              v-if="tableType === 'ORG'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Urllist
              v-if="tableType === 'URL'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <Sslfingerlist
              v-if="tableType === 'SSLFINGER'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            />
            <!-- <Blolist
              v-if="tableType === 'BLOCKCHAIN'"
              :table-data="tableData"
              @SORT_CHANGE_GETDATA="SORT_CHANGE_GETDATA"
            /> -->
            <Issuer v-if="tableType === 'ISSUER'" :table-data="tableData" />
            <Subject v-if="tableType === 'SUBJECT'" :table-data="tableData" />
            <AppName v-if="tableType === 'APP'" :table-data="tableData" />
            <el-empty
              v-if="tableData === '' || !tableData || tableData === []"
              :image-size="200"
              description="暂无数据"
            ></el-empty>
          </div>
        </div>
      </div>
    </el-drawer>
    <tag-view
      v-model="tagView"
      :tag_target_type="tag_target_type"
      :tag-labels="labels"
      :is-show-aside="false"
      @modifyLabels="getTagValue"
    />
  </div>
</template>

<script>
import dayjs from "dayjs";
import { modifyLabels } from "@/api/import";
import {
  node_ip,
  node_domain,
  node_cert,
  remark,
  tag,
  set_tags,
  node_list,
  node_org,
  node_blo,
  node_finger,
  visibleSideRelation,
  appservice,
  node_app,
} from "@/api/graph";
/* 引入标签api */
import { editTargetTag } from "@/api/TagList";
import MacList from "./components/macList.vue";
import Iplist from "./components/ipList.vue";
import Applist from "./components/appList.vue";
import Surveylist from "./components/surveyList.vue";
import Fdomainlist from "./components/fdomainList.vue";
import Domainlist from "./components/domainList.vue";
import Certlist from "./components/certList.vue";
import Ualist from "./components/uList.vue";
import Orglist from "./components/orgList.vue";
import Urllist from "./components/urlList.vue";
import Sslfingerlist from "./components/sslfingerList.vue";
import Blolist from "./components/bloList.vue";
import Issuer from "./components/issuer.vue";
import Subject from "./components/subject.vue";
import AppName from "./components/appName.vue";
import { get_tag } from "@/api/search.js";
import TagLib from "@/components/TagLib";

import TagView from "@/components/TagView";
import { message } from "@/utils/resetMessage";
export default {
  name: "Detail",
  components: {
    Subject,
    // tagsdeta,
    MacList,
    Iplist,
    Applist,
    Surveylist,
    Fdomainlist,
    Domainlist,
    Certlist,
    Ualist,
    Orglist,
    Urllist,
    Sslfingerlist,
    Blolist,
    TagView,
    Issuer,
    AppName,
  },
  props: ["visibleShow", "detailType", "detailValue", "detailLabel"],
  data() {
    return {
      tagType: {
        IP: 0,
        APP: 2,
        DOMAIN: 3,
        CERT: 4,
        SSLFINGER: 7,
      },
      visibleSideRelationListData: [],
      // 标签库弹出框
      tagView: false,
      selectTags: [],
      detailLoading: false,
      pageTotal: 0,
      page: 1,
      title: this.detailValue,
      // 黑名单权重
      blackList: "",
      // 位置
      position: "",
      // 标签
      labelsView: false,
      labels: [],
      // 备注
      remarks: [],
      ipKey: "",
      // 企业信息
      org_name: "",
      org_desc: "",
      // 开放端口数
      ip_tag_list: [
        {
          name: "开放端口数",
          num: "",
        },
        {
          name: "所属锚域名数",
          num: "",
        },
        {
          name: "访问端口数",
          num: "",
        },
        {
          name: "平均流量",
          num: "",
        },
        {
          name: "发送总字节",
          num: "",
        },
        {
          name: "接收总字节",
          num: "",
        },
        {
          name: "首次出现时间",
          num: "",
        },
        {
          name: "末次出现时间",
          num: "",
        },
      ],
      // DOMAINflex
      domain_tag_list: [
        {
          name: "Alex排名",
          num: "",
        },
        {
          name: "兄弟域名数量",
          num: "",
        },
        {
          name: "指向IP数量",
          num: "",
        },
        {
          name: "WhoIS",
          num: "",
        },
        {
          name: "客户端热度",
          num: "",
        },
        {
          name: "关联证书数量",
          num: "",
        },
        {
          name: "首次出现时间",
          num: "",
        },
        {
          name: "末次出现时间",
          num: "",
        },
      ],
      // CERTflex
      cert_tag_list: [
        {
          name: "签发机构",
          num: "",
        },
        {
          name: "所有者",
          num: "",
        },
        {
          name: "签发时间",
          num: "",
        },
        {
          name: "有效时间",
          num: "",
        },
        {
          name: "服务器热度",
          num: "",
        },
        {
          name: "客户端热度",
          num: "",
        },
        {
          name: "首次出现时间",
          num: "",
        },
        {
          name: "末次出现时间",
          num: "",
        },
      ],
      blo_tag_list: [
        {
          name: "余额",
          num: "",
        },
        {
          name: "来源",
          num: "",
        },
        {
          name: "地址",
          num: "",
        },
      ],
      ssl_tag_list: [
        {
          name: "指纹ID",
          num: "",
        },
        {
          name: "Ja3指纹Hash",
          num: "",
        },
        {
          name: "指纹说明",
          num: "",
        },
        {
          name: "类型",
          num: "",
        },
      ],
      app_tag_list: [
        {
          name: "应用服务key",
          num: "",
        },
        {
          name: "ip地址",
          num: "",
        },
        {
          name: "应用名称",
          num: "",
        },
        {
          name: "服务端口",
          num: "",
        },
        {
          name: "ip协议",
          num: "",
        },
      ],
      // 锚域名
      fdomain: "",
      // 列表类型
      tType: "",
      tItem: {},
      tIndex: "",
      tOpt: [],
      // 列表数据
      tableType: "",
      tableData: [],
      // 备注输入框
      remark: "",
      remarkShow: false,
      // 标签弹窗字段
      opentag: false,
      tagval: "",
      allTags: [],
      // 父证书
      fatherId: "",
      /* APP详情数据 */
      appInfo: {
        app_name: "",
        app_version: "",
      },
    };
  },
  computed: {
    // eslint-disable-next-line vue/return-in-computed-property
    typeImg: function () {
      switch (this.detailType) {
      case "DOMAIN":
        return require("../../../assets/graph/icon-Domaindetail.svg");
      case "IP":
        return require("../../../assets/graph/icon-IPdetails.svg");
      case "CERT":
        return require("../../../assets/graph/icon-Certdetail.svg");
      case "MAC":
        return require("../../../assets/graph/icon_Macdetail.svg");
      case "SURVEY_DATA":
        return require("../../../assets/graph/icon_资产测绘_默认.svg");
      case "ORG":
        return require("../../../assets/graph/企业详情.svg");
      case "BLOCKCHAIN":
        return require("../../../assets/graph/icon_blockchain.svg");
      case "SSLFINGER":
        return require("../../../assets/graph/icon_fingerprint.svg");
      case "APP":
        return require("../../../assets/graph/Node_Apptype_Blue.svg");
      default:
        return require("../../../assets/graph/icon-Certdetail.svg");
      }
    },
    tag_target_type() {
      return this.tagType[this.detailType];
    },
    all_tag_category() {
      return this.$store.state.long.Dict.all_tag_category;
    },
  },
  watch: {
    tIndex: {
      handler(val, old) {
        this.page = 1;
        this.pageTotal = 0;
        let item = "";
        for (let i = 0; i < this.tOpt.length; i++) {
          if (this.tOpt[i].value === val) {
            item = this.tOpt[i];
            this.tItem = item;
          }
        }
        let edge_type = item.value;
        if(item.value.slice(0, 2) === "r_"){
          edge_type = edge_type.substring(2);
        }
        node_list({
          search: this.detailValue,
          edge_type: edge_type,
          tag_type: this.detailType,
          direct: item.position,
          page_size: 10,
          current_page: this.page,
        }).then((res) => {
          if (res.err === 0 || res.code === 200) {
            this.tableData = res.data.list;
            this.tableType = res.data.type;
            this.pageTotal = res.data.total;
          }
        });
      },
    },
  },
  async mounted() {
    await this.visibleSideRelationList();
    this.DETAIL_TABLE_SETTINGS();
    // this.GET_DATA();
  },
  methods: {
    getLabels(ids) {
      const lablesGroup =
        this.all_tag_category.filter((item) => ids.includes(item.tag_id));
      return lablesGroup;
    },
    /* 获取type对应的值 */
    transferType(type) {
      const { alarm_type } = this.dicts;
      let typeNum;
      Object.values(alarm_type).map((key, value) => {
        if (key == type) {
          typeNum = value;
        }
      });
      return typeNum;
    },
    // 获取关系数据
    async visibleSideRelationList() {
      try {
        const res = await visibleSideRelation(
          this.detailValue,
          this.detailType
        );
        this.visibleSideRelationListData = res.data;
      } catch (error) {
        console.log(error);
      }
    },
    // 数据获取
    GET_DATA() {
      // this.detailLoading = true;
      let URL = "";
      switch (this.detailType) {
      case "IP":
        node_ip({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      case "DOMAIN":
        node_domain({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      case "CERT":
        node_cert({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      case "ORG":
        node_org({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      case "BLOCKCHAIN":
        node_blo({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      case "SSLFINGER":
        node_finger({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      case "APPSERVICE":
        appservice({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      case "APP":
        node_app({
          str: this.detailValue,
        })
          .then((res) => {
            this.HAND_DATA(res);
          })
          .catch((err) => {
            throw err;
          });
        break;
      default:
        break;
      }
    },
    // 数据统一处理
    HAND_DATA(res) {
      // 初始化开启展示列表
      if (res.err === 0 || res.code === 200) {
        if (this.detailType === "IP") {
          let data = res.data;
          let ids = data.labels;
          this.labels = this.getLabels(ids);
          this.blackList = data?.blackList;
          this.position = data?.country || "N/A";
          this.remarks = data.remarks;
          this.ip_tag_list[0].num = data.openPortNum;
          this.ip_tag_list[1].num = data.fdomainNum;
          this.ip_tag_list[4].num = data.sendBytes;
          this.ip_tag_list[2].num = data.accessPortNum;
          this.ip_tag_list[3].num = `${this.FMT_THOU(data.averageBps).num}${
            this.FMT_THOU(data.averageBps).unit
          } / s`;
          this.ip_tag_list[5].num = data.recvBytes;
          this.ip_tag_list[6].num = this.FMT_TIME(data.firstTime);
          this.ip_tag_list[7].num = this.FMT_TIME(data.lastTime);
          console.log("处理好的ipListt", this.ip_tag_list);
        } else if (this.detailType === "DOMAIN") {
          const { data } = res;
          let ids = data.labels;
          this.labels = this.getLabels(ids);
          this.blackList = data?.blackList;
          this.fdomain = data.fdomain || "N/A";
          this.remarks = data.remarks;
          this.domain_tag_list[0].num = data.alexaRank;
          this.domain_tag_list[1].num = data.brotherNum;
          this.domain_tag_list[2].num = data.toIpNum;
          this.domain_tag_list[3].num = data.whoIs || "N/A";
          this.domain_tag_list[4].num = data.clientHeat;
          this.domain_tag_list[5].num = data.certNum || "0";
          this.domain_tag_list[6].num = this.FMT_TIME(data.firstTime);
          this.domain_tag_list[7].num = this.FMT_TIME(data.lastTime);
        } else if (this.detailType === "CERT") {
          this.cert = res.data.basic_data.cert;
          this.labels = res.data.basic_data.certTagList;
          let data = res.data.basic_data;
          this.blackList = data?.black_list;
          this.remarks = data.remarks;
          this.fatherId = res.data.basic_data.fatherIdList?.[0] || "";
          this.cert_tag_list[0].num = data.issuerO || "-";
          this.cert_tag_list[1].num = data.subjectO || "-";
          this.cert_tag_list[2].num = data.notBefore;
          this.cert_tag_list[3].num = data.notAfter;
          this.cert_tag_list[4].num = data.serverHeat;
          this.cert_tag_list[5].num = data.clientHeat;
          this.cert_tag_list[6].num = this.FMT_TIME(data.firstTime);
          this.cert_tag_list[0].num = data.issuerO || "-";
          this.cert_tag_list[1].num = data.subjectO || "-";
          this.cert_tag_list[2].num = data.notBefore;
          this.cert_tag_list[3].num = data.notAfter;
          this.cert_tag_list[4].num = data.serverHeat;
          this.cert_tag_list[5].num = data.clientHeat;
          this.cert_tag_list[6].num = this.FMT_TIME(data.firstTime);
          this.cert_tag_list[7].num = this.FMT_TIME(data.lastTime);
        } else if (this.detailType === "ORG") {
          let data = res.data;
          this.title = this.detailLabel;
          this.org_name = data.org_name;
          this.org_desc = data.org_desc;
          this.remarks = data.remarks;
        } else if (this.detailType === "BLOCKCHAIN") {
          let data = res.data;
          this.blo_tag_list[0].num = data.balance_account;
          this.blo_tag_list[1].num = data.chain_source;
          this.blo_tag_list[2].num = data.addr;
        } else if (this.detailType === "SSLFINGER") {
          let data = res.data;
          let ids = data.labels;
          this.labels = this.getLabels(ids);
          this.ssl_tag_list[0].num = data.finger_id;
          this.ssl_tag_list[1].num = data.finger_id;
          this.ssl_tag_list[2].num = data.desc;
          // eslint-disable-next-line eqeqeq
          this.ssl_tag_list[3].num =
            data.type == "client" ? "客户端" : "服务端";
        } else if (this.detailType === "APPSERVICE") {
          let data = res.data;
          this.title = this.detailLabel;
          this.app_tag_list[0].num = data.service_key;
          this.app_tag_list[1].num = data.ip_addr;
          this.app_tag_list[2].num = data.app_name;
          this.app_tag_list[3].num = data.d_port;
          this.app_tag_list[4].num =
            this.$store.state.long.Dict.protocol_type[data.ip_pro]
              .protocol_type || "-";
        } else if (this.detailType === "APP") {
          const { app_name, app_version } = res.data;
          let ids = res.data.labels;
          this.labels = this.getLabels(ids);
          this.appInfo = { app_name, app_version };
        }
      }
    },
    // 详情中的列表需要的设置
    DETAIL_TABLE_SETTINGS() {
      this.tOpt = [];
      let relation = this.visibleSideRelationListData;
      // if (localStorage.getItem("detailLablePosition") === "all") {
      //   for (let i = 0; i < relation.length; i++) {
      //     let item = relation[i];
      //     if (item.slice(0, 2) === "f-") {
      //       let val = item.substring(2);
      //       this.tOpt.push({
      //         label:
      //           this.$store.state.long.Dict.nebula_type[val]?.desc +
      //             "-流出" || "N/A",
      //         value: val,
      //         position: true,
      //         index: i,
      //       });
      //     }
      //     if (item.slice(0, 2) === "r-") {
      //       let val = item.substring(2);
      //       this.tOpt.push({
      //         label:
      //           this.$store.state.long.Dict.nebula_type[val]?.desc +
      //             "-流入" || "N/A",
      //         value: val,
      //         position: false,
      //         index: i,
      //       });
      //     }
      //   }
      // } else if (localStorage.getItem("detailLablePosition") === "out") {
      //   for (let i = 0; i < relation.length; i++) {
      //     let item = relation[i];
      //     if (item.slice(0, 2) === "f-") {
      //       let val = item.substring(2);
      //       this.tOpt.push({
      //         label:
      //           this.$store.state.long.Dict.nebula_type[val]?.desc +
      //             "-流出" || "N/A",
      //         value: val,
      //         position: true,
      //         index: i,
      //       });
      //     }
      //   }
      // } else if (localStorage.getItem("detailLablePosition") === "in") {
      //   for (let i = 0; i < relation.length; i++) {
      //     let item = relation[i];
      //     if (item.slice(0, 2) === "r-") {
      //       let val = item.substring(2);
      //       this.tOpt.push({
      //         label:
      //           this.$store.state.long.Dict.nebula_type[val]?.desc +
      //             "-流入" || "N/A",
      //         value: val,
      //         position: false,
      //         index: i,
      //       });
      //     }
      //   }
      // }
      if (relation.length == 0) {
        return;
      }
      for (let i = 0; i < relation.length; i++) {
        let item = relation[i];
        // if (item.slice(0, 2) === "f-") {
        //   let val = item.substring(2);
        //   this.tOpt.push({
        //     label:
        //       this.$store.state.long.Dict.nebula_type[val]?.desc + "-流出" ||
        //       "N/A",
        //     value: item,
        //     position: true,
        //     index: i,
        //   });
        // }
        if (item.slice(0, 2) === "r_") {
          let val = item;
          this.tOpt.push({
            label:
              this.$store.state.long.Dict.nebula_type[val]?.desc  ||
              "N/A",
            value: item,
            position: false,
            index: i,
          });
        }else{
          let val = item;
          this.tOpt.push({
            label:
              this.$store.state.long.Dict.nebula_type[val]?.desc  ||
              "N/A",
            value: item,
            position: true,
            index: i,
          });
        }
      }
      this.tIndex = this.tOpt[0]?.value;
      this.GET_DATA();
    },
    // 页码当前页改变
    CURRENT_CHANGE(val) {
      this.page = val;
      let edge_type = this.tItem.value;
      if(edge_type.slice(0, 2) === "r_"){
        edge_type = edge_type.substring(2);
      }
      node_list({
        search: this.detailValue,
        edge_type: edge_type,
        tag_type: this.detailType,
        direct: this.tItem.position,
        page_size: 10,
        current_page: this.page,
        order_field: this.order_field,
        sort_order: this.sort_order,
      }).then((res) => {
        if (res.err === 0 || res.code === 200) {
          this.tableData = res.data.list;
          this.tableType = res.data.type;
          this.pageTotal = res.data.total;
        }
      });
    },
    // 排序改变
    SORT_CHANGE_GETDATA(data) {
      this.order_field = data.order_field;
      this.sort_order = data.sort_order;
      let edge_type = this.tItem.value;
      if(edge_type.slice(0, 2) === "r_"){
        edge_type = edge_type.substring(2);
      }
      node_list({
        search: this.detailValue,
        edge_type: edge_type,
        tag_type: this.detailType,
        direct: this.tItem.position,
        page_size: 10,
        current_page: this.page,
        order_field: this.order_field,
        sort_order: this.sort_order,
      }).then((res) => {
        if (res.err === 0 || res.code === 200) {
          this.tableData = res.data.list;
          this.tableType = res.data.type;
          this.pageTotal = res.data.total;
        }
      });
    },
    // 切换列表
    DETAIL_TABLE_CHANGE() {
      // console.log(this.tItem);
    },

    // 关闭前的回调
    BEFORE_CLOSE(done) {
      this.$emit("DETAIL_CLOSE", false);
    },
    // 黑名单权重格式化
    FMT_BLACK() {
      if (this.blackList !== "") {
        if (this.blackList < 1) {
          return require("../../../assets/images/ipsafety.png");
        } else if (this.blackList < 61) {
          return require("../../../assets/images/state1.png");
        } else if (this.blackList < 91) {
          return require("../../../assets/images/dubious.png");
        } else if (this.blackList > 90) {
          return require("../../../assets/images/highrisk.png");
        } else {
          return require("../../../assets/images/state1.png");
        }
      } else {
        return require("../../../assets/images/state1.png");
      }
    },
    // 时间戳格式化
    FMT_TIME(date) {
      return dayjs(date * 1000).format("YYYY-MM-DD HH:mm:ss");
    },
    // 提交备注
    REMARK_SUBMIT(val) {
      this.remarks.push(val);

      remark({
        str: this.detailValue,
        type: this.detailType,
        remarks: this.remarks,
      }).then((res) => {
        if (res.err === 0 || res.code === 200) {
          this.$message.success(res.msg);
          this.remark = "";
          this.remarkShow = false;
        }
      });
    },
    // 新增标签
    ADD_TAG() {
      // 打开标签库
      // this.opentag = true;
      // let tag_text = this.labels.map((item) => {
      //   return item.tag_text;
      // });
      // if (tag_text.length) {
      //   this.activetags = tag_text.join(",");
      // }
      this.selectTags = this.labels.filter((item) => item.tag_status);
      this.tagView = true;
    },
    handleOpened() {
      // 获取到id返回新的数组
      let tagText = this.labels.map((item) => {
        return item.tagText;
      });
      this.tagval = tagText.join(",");
    },
    cleartagdialog() {
      this.opentag = false;
    },
    // 选择标签
    async SUBMIT_TAGS(tags, selectTags, callback) {
      this.selectTags = selectTags;
      const params = {
        cert_sha1: this.cert,
        es_id: this.cert,
        labels: this.selectTags.map((item) => String(item.tag_id)),
      };
      await modifyLabels(params);
      callback && callback(true);
      this.GET_DATA();
      this.$message.success("操作成功");
    },
    // 获取到标签列表数组
    getTagValue(val) {
      if (val.length !== 0) {
        // let newarr = [];
        // val.forEach((item) => {
        //   newarr.push(item.tagID + "");
        // });
        // this.labels = val;
        const labels = val.map((item) => item.toString());
        let param = {
          str: this.detailValue,
          type: this.detailType,
          labels: labels,
        };
        set_tags(param).then((res) => {
          const { err, msg, data } = res;
          if (err === 0) {
            this.$message({
              message: "标签添加成功",
              type: "success",
            });
          } else {
            this.$message.error(msg);
          }
          this.tagView = false;
          this.GET_DATA();
        });
      }
    },
    // 获取全量标签库
    GET_TAG() {
      get_tag({
        search_type: "all",
      })
        .then((res) => {
          if (res.err === 0 || res.code === 200) {
            this.allTags = res.data.data;
          } else {
            this.$message("标签获取失败");
          }
        })
        .catch((err) => {});
    },
    // 格式化流量大小
    FMT_THOU(size) {
      if (size === 0) {
        return {
          num: "0",
          unit: "B",
        };
      }
      let bytes = parseInt(size);
      if (bytes === 0) return "0 B";
      let k = 1024, // or 1024
        sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      // return (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
      return {
        num: (bytes / Math.pow(k, i)).toPrecision(3),
        unit: sizes[i],
      };
    },
    // 删除备注
    DEL_REMARK(item) {
      let delIndex = this.remarks.indexOf(item);
      this.remarks.splice(delIndex, 1);
      remark({
        str: this.detailValue,
        type: this.detailType,
        remarks: this.remarks,
      }).then((res) => {
        if (res.err === 0 || res.code === 200) {
          this.$message.success("删除备注");
          this.remark = "";
          this.remarkShow = false;
        }
      });
    },
    // 删除标签
    DEL_TAG(item) {
      let delIndex = this.labels.indexOf(item);
      this.labels.splice(delIndex, 1);
      let arr = [];
      this.labels.forEach((i) => {
        arr.push(i.tag_id);
      });
      tag({
        str: this.detailValue,
        type: this.detailType,
        labels: arr,
      }).then((res) => {
        if (res.err === 0 || res.code === 200) {
          this.$message.success("删除标签");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  width: 100%;
  padding: 16px;
  overflow-y: auto;

  .header {
    width: 100%;
    height: 22px;
    display: flex;
    align-items: center;

    > div:nth-of-type(1) {
      width: 20px;
      height: 20px;
      margin-right: 10px;

      > img {
        width: 100%;
        height: 100%;
      }
    }

    .title {
      max-width: 75%;
      font-size: 16px;
      color: #2c2c35;
      margin-right: 6px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .blackList {
      width: 62px;
      height: 22px;

      > img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .cell {
    width: 100%;
    height: auto;
    display: flex;
    margin-top: 12px;
    > div {
      font-size: 12px;
    }

    > div:nth-of-type(2) {
      max-width: 90%;
    }

    .label {
      width: 42px;
      min-height: 22px;
      color: #9999a1;
      font-size: 14px;
      margin-right: 12px;
      display: flex;
      justify-content: space-between;
    }

    .text {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #2c2c35;
    }

    #tag {
      display: flex;
      justify-content: space-between;

      .content {
        width: 90%;
        display: flex;
        flex-direction: column;

        .labels {
          width: 100%;
          overflow-y: hidden;
          display: flex;
          flex-wrap: wrap;

          #label {
            width: auto;
            height: 20px;
            border-radius: 2px;
            padding: 0 4px;
            font-size: 12px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            margin-right: 4px;
          }

          .redTag {
            color: #a41818;
            background: #fce7e7;
          }

          .blueTag {
            color: #1b428d;
            background: #e7f0fe;
          }

          .yellowTag {
            color: #b76f1e;
            background: #f9eddf;
          }

          .grayTag {
            color: #2c2c35;
            background: #f2f3f7;
          }

          .del {
            display: inline-block;
            margin-left: 4px;
            cursor: pointer;
            text-align: center;
          }

          #label:hover {
            .del {
              display: inline-block;
              margin-left: 4px;
              cursor: pointer;
              background: gray;
              border-radius: 7px;
              color: #ffffff;
            }
          }
        }

        .add {
          width: 42px;
          height: 20px;
          background: #ffffff;
          border: 1px solid #116ef9;
          color: #117ef9;
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
      }

      .more {
        width: 46px;
        height: 22px;
        font-size: 14px;
        color: #116ef9;
        cursor: pointer;
      }
    }

    .remark {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .content {
        width: 100%;
        max-height: 76px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .labels {
          width: 100%;
          display: flex;
          flex-wrap: wrap;

          .label {
            width: auto;
            min-height: 20px;
            color: #2c2c35;
            background: #eef0f5;
            border-radius: 2px;
            padding: 4px 4px;
            font-size: 12px;
            margin-bottom: 8px;

            .del {
              width: 8px;
              margin-left: 4px;
              cursor: pointer;
              text-align: center;
            }
          }

          .add {
            width: 42px;
            height: 20px;
            background: #ffffff;
            border: 1px solid #116ef9;
            color: #117ef9;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
          }
        }

        .label:hover {
          .del {
            display: inline-block;
            margin-left: 4px;
            cursor: pointer;
            background: gray;
            border-radius: 7px;
            color: #ffffff;
          }
        }
      }
    }
  }

  .flex-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-top: 25px;
    justify-content: space-between;

    &-item {
      min-width: 33%;
      height: 44px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-size: 14px;
      position: relative;
      padding-left: 11px;
      margin-bottom: 12px;

      > div:nth-of-type(1) {
        width: 100%;
        height: 22px;
        color: #9999a1;
        display: flex;
        align-items: center;
      }

      > div:nth-of-type(2) {
        width: 100%;
        height: 22px;
        display: flex;
        align-items: center;
      }

      > aside {
        width: 1px;
        height: 30px;
        background: #dee0e7;
        position: absolute;
        left: 0;
        top: 7px;
      }
    }
  }

  .app_list {
    margin-top: 25px;
    display: flex;

    .item {
      margin-bottom: 8px;
      margin-right: 8px;

      .title {
        font-size: 14px;
        color: #9999a1;
        margin-right: 4px;
      }

      .content {
        font-size: 14px;
      }
    }
  }

  .table-tile {
    width: 100%;
    height: 32px;
    font-size: 14px;
    color: #9999a1;
    display: flex;
    align-items: center;
    margin: 12px 0;
    position: relative;

    > div:nth-of-type(1) {
      margin-right: 12px;
    }

    .el-input__icon {
      line-height: unset;
    }
  }

  .el-table--scrollable-x .el-table__body-wrapper {
    overflow-x: auto;
  }

  .indialog {
    ::v-deep .el-dialog {
      height: 460px;

      .el-dialog__headerbtn {
        top: 10px;
      }

      .el-dialog {
        box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
        border-radius: 8px;
      }

      .el-dialog__body {
        padding: 0;
      }

      .el-dialog__header {
        padding: 12px 24px;
      }
    }
  }

  .page {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>
>

<template>
  <div
    v-loading="loading"
    class="search"
    element-loading-text="数据请求中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255, 255, 255, 0.8)"
  >
    <header class="layout-header">
      <nav-bar />
    </header>
    <img
      src="@/assets/images/Frame3298.svg"
      alt=""
    >
    <div class="a2">图探索</div>
    <div class="a3">图探索主要用来构建图联通关系，并实现在长时空维度下的图空间检索。在构建知识图谱的基础上，通过图关系的约束，实现聚维图谱分析。</div>
    <el-form
      ref="form"
      @submit.native.prevent
    >
      <div class="search-box">
        <el-form-item style="width:80%;margin-bottom:0;">
          <el-input
            v-model="value"
            type="textarea"
            :autosize="{ minRows: 1, maxRows: 10}"
            placeholder="请输入VID,一行一个数据，最多十条"
            @input="INPUT"
          ></el-input>
        </el-form-item>
        <el-button @click="SUBMIT">添加节点</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import NavBar from '@/layout/components/navbar';
import { search_node } from '@/api/graph';
export default {
  components: {
    NavBar
  },
  data () {
    return {
      value: '',
      submitData: [],
      loading: false
    };
  },
  methods: {
    INPUT (val) {
      console.log(val.split('\n'));
      this.submitData = val.split('\n');
    },
    SUBMIT () {
      this.loading = true;
      search_node(this.submitData).then(res => {
        console.log(res);
        this.loading = false;
        if (res.msg === '成功') {
          if (res.data.vertex.length > 0) {
            this.$router.push({
              path: 'newgraph',
              query: {
                initValue: this.submitData,
                initType: 'SEARCH'
              }
            });
          } else {
            this.$message.warning('未查询到数据');
          }

        }
      }).catch(err => {
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.search {
  width: 100%;
  height: 100%;
  background-color: #eef0f5;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  .layout-header {
    width: 100%;
    height: 46px;
    position: relative;
    background-color: #ffffff;
    // box-shadow: 0px 15px 19px rgba(0, 0, 0, 0.05);
    border-radius: 0px 0px 10px 10px;
    z-index: 701;
  }
  > img {
    margin-top: 200px;
  }
  .a2 {
    color: #2c2c35;
    font-size: 20px;
    font-weight: 500;
    margin-top: 16px;
  }
  .a3 {
    width: 508px;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    color: #767684;
    margin-top: 8px;
    margin-bottom: 32px;
  }
  .search-box {
    width: 548px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    ::v-deep .el-input__inner {
      width: 452px;
      height: 32px;
      background: #ffffff;
      border: 1px solid #cecece;
      border-radius: 4px;
    }
    .el-button {
      background: #116ef9;
      border-radius: 4px;
      width: 88px;
      height: 32px;
      color: #ffffff;
      padding: 0;
      font-size: 14px;
    }
   
    ::v-deep .el-form-item__content {
      line-height: inherit;
    }
  }
}
</style>
<template>
  <el-dialog
    :title="headerTitle"
    :visible.sync="isShow"
    width="480px"
    custom-class="custom--dialog"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="目标类型" prop="tag_name">
        <el-select
          v-model="form.tag_name"
          placeholder="请选择目标类型"
          @change="selectChange"
        >
          <el-option
            v-for="item in nebula_properties"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <div class="flex">
        <el-form-item
          label="检索条件"
          prop="properties_name"
          style="width: 130px"
        >
          <el-select v-model="form.properties_name" placeholder="请选择属性">
            <el-option
              v-for="item in nebula_properties_query"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="properties_value" label="属性值" class="value">
          <el-input
            v-model="form.properties_value"
            style="width: 308px"
            placeholder="请输入属性值"
            @input="resetValidate"
          ></el-input>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="isShow = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSearch">开始探索</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from "@/mixins";
import { mapState } from "vuex";
export default {
  name: "KeywordDialog",
  mixins: [mixins],
  data() {
    return {
      form: {
        tag_name: "",
        properties_name: "",
        properties_value: "",
      },
      rules: {
        tag_name: [
          { required: true, message: "请选择目标类型", trigger: "change" },
        ],
        properties_name: [
          { required: true, message: "请选择属性", trigger: "change" },
        ],
        properties_value: [
          { required: true, message: "请输入属性值", trigger: "blur" },
        ],
      },
      nebula_properties: [],
      nebula_properties_query: [],
      loading: false,
    };
  },
  computed: {
    ...mapState("long", ["Dict"]),
  },
  created() {
    this.nebula_properties = this.Dict.nebula_properties;
  },
  methods: {
    resetValidate(){
      this.$refs.formRef.validateField('properties_value');
    },
    selectChange(val) {
      this.form.properties_name = "";
      this.form.properties_value = "";
      this.$refs.formRef.clearValidate();
      this.nebula_properties_query = this.nebula_properties.filter(
        (item) => item.key === val
      )[0].properties;
      console.log(this.nebula_properties_query[0]);
      this.form.properties_name = this.nebula_properties_query[0]?this.nebula_properties_query[0].key:'';
    },
    handleSearch() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return false;
        const params = { ...this.form };
        this.$emit("keywordDialog", params,()=>{
          this.isShow=false;
        });
      });
    },
    handleClose() {
      this.nebula_properties_query = [];
      this.$refs.formRef.resetFields();
    },
  },
};
</script>
<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .value {
    ::v-deep .el-form-item__label {
      opacity: 0;
    }
  }
}
</style>
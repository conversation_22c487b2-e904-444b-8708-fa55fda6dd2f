<template>
  <el-dialog
    :title="headerTitle"
    :visible.sync="isShow"
    width="480px"
    custom-class="custom--dialog"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="目标ID" prop="vid_list">
        <el-input
          v-model="form.vid_list"
          placeholder="请输入目标ID，一行一个目标，最多1000条"
          type="textarea"
        ></el-input>
      </el-form-item>
      <el-form-item label="最大步数" prop="step_count">
        <div slot="label">
          <span>最大步数</span>
          <el-tooltip
            class="item"
            effect="dark"
            content="分析潜在关联关系的最大跳数，默认为1，5最大步数步数多可能会极大消耗系统性能，请谨慎使用"
            placement="top-start"
          >
            <i class="el-icon-warning-outline" style="margin-left: 4px"></i>
          </el-tooltip>
        </div>
        <el-select v-model="form.step_count">
          <el-option
            v-for="item in 5"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="isShow = false">取 消</el-button>
      <el-button type="primary" @click="handleSearch">开始探索</el-button>
    </span>
  </el-dialog>
</template>

<script>
import mixins from "@/mixins";
export default {
  name: "SubgraphDialog",
  mixins: [mixins],
  data() {
    return {
      form: {
        vid_list: "",
        step_count: 1,
      },
      rules: {
        vid_list: [{ required: true, message: "请输入目标ID", trigger: "blur" }],
      },
    };
  },
  methods: {
    handleSearch() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) return false;
        const params = {
          vid_list: this.form.vid_list.split("\n"),
          step_count:this.form.step_count
        };
        this.$emit("SUBMIT_NODE_SEARCH", params,()=>{
          this.isShow=false;
        });
      });
    },
    handleClose() {
      this.$refs.formRef.resetFields();
    },
  },
};
</script>
.newgraph {
    width: 100%;
    height: 100%;
    position: relative;
    background: #eef0f5;
    display: flex;
    flex-direction: column;
  
    .header {
      width: 100%;
      padding: 0 16px;
      height: 32px;
      position: relative;
      top: 16px;
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      z-index: 2000;
      box-sizing: border-box;
  
      .tool {
        height: 100%;
        display: flex;
        align-items: center;
        .el-date-editor--date {
          margin-left: 10px;
        }
        .zoom {
          width: 108px;
          height: 100%;
          border-right: 2px solid #eef0f5;
          display: flex;
          justify-content: space-around;
          align-items: center;
  
          > img {
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }
  
        .full {
          width: 32px;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          border-right: 2px solid #eef0f5;
  
          > img {
            cursor: pointer;
          }
        }
  
        .layout {
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-left: 10px;
          padding-right: 10px;
          border-right: 2px solid #eef0f5;
  
          > div:nth-of-type(1) {
            margin-left: 4px;
          }
        }
      }
  
      .search {
        &-sign{
          margin-right: 10px;
        }
        display: flex;
        align-items: center;
        .el-dropdown {
          margin-left: 10px;
          ::v-deep .el-button--small {
            font-size: 14px;
          }
        }
        .full {
          width: 32px;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 10px;
  
          > img {
            cursor: pointer;
          }
        }
  
        ::v-deep .el-input__inner {
          width: 452px;
          height: 32px;
          background: #ffffff;
          border: 1px solid #cecece;
          border-radius: 4px;
        }
    
        // .el-button {
        //   border-radius: 4px;
        //   width: 88px;
        //   height: 32px;
        //   color: #ffffff;
        //   padding: 0;
        //   font-size: 14px;
        //   margin: 0 8px;
        // }
  
        .el-button.clear {
          width: 60px;
          background: #ffffff;
          color: #2c2c35;
          border: 1px solid #cecece;
          border-radius: 4px;
          cursor: pointer;
        }
  
        .el-button.clear:hover {
          background-color: #b3d8ff;
        }
  
        .el-textarea {
          width: 290px;
        }
  
        ::v-deep .el-form-item__content {
          line-height: inherit;
        }
      }
    }
  
    .graph-box {
      height: calc(100vh - 50px);
      background: #eef0f5;
    }
  
    ::v-deep .rel-map {
      background: #eef0f5;
    }
  
    .type-box {
      position: absolute;
      left: 18px;
      bottom: 1px;
      z-index: 99;
  
      &-cell {
        width: 105px;
        height: 20px;
        margin-bottom: 16px;
        font-size: 12px;
        color: #2c2c35;
        display: flex;
        align-items: center;
        cursor: context-menu;
  
        > div:nth-of-type(1) {
          width: 20px;
          height: 20px;
          margin-right: 6px;
  
          > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }
  
  ::v-deep .rel-node-shape-0:hover {
    box-shadow: none;
  }
  
  ::v-deep .rel-node-shape-0 {
    width: auto;
    height: auto;
    padding: 0;
    background-color: transparent !important;
    #node{
      .sign{
        width:100%;
        height:100%;
        background-color: #8ABCFF;
        position: absolute;
        z-index:-1;
        border-radius: 50%;
        >div{
          width: 16px;
          height: 16px;
          border-radius: 50%;
          position: absolute;
          right:-8px;
          top:-5px;
          >img{
            width: 100%;
            height:100%;
          }
        }
      }
    }
    .node {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #fff;
      border: 1px solid #767684;
      display: flex;
      justify-content: center;
      align-items: center;
  
      > img {
        width: 24px;
        height: 24px;
      }
    }
  
    .attacker-node {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #f91111;
      display: flex;
      justify-content: center;
      align-items: center;
  
      > img {
        width: 24px;
        height: 24px;
      }
    }
  
    .neutrality-node {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #8b11eb;
      display: flex;
      justify-content: center;
      align-items: center;
  
      > img {
        width: 24px;
        height: 24px;
      }
    }
  
    .victim-node {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #116ef9;
      display: flex;
      justify-content: center;
      align-items: center;
  
      > img {
        width: 24px;
        height: 24px;
      }
    }
  
    .folder-node {
      width: 42px;
      height: 32px;
  
      > img {
        width: 100%;
        height: 100%;
      }
    }
  
    .labels-node {
      // max-width: 230px;
      // min-width: auto;
      // min-height: 36px;
      background: #ffffff;
      border: 1px dashed #dee0e7;
      border-radius: 4px;
      padding: 8px;
      padding-bottom: 4px;
      box-sizing: border-box;
      display: flex;
      flex-wrap: wrap;
      // position: relative;
      cursor: pointer;
  
      > section {
        min-height: 20px;
        line-height: 20px;
        background: #eef0f5;
        border-radius: 2px;
        font-size: 12px;
        color: #2c2c35;
        margin-right: 4px;
        margin-bottom: 4px;
        padding: 0 4px;
      }
  
      .del {
        width: 24px;
        height: 24px;
        line-height: 24px;
        border-radius: 12px;
        background: #ffffff;
        border: 1px solid #dee0e7;
        display: none;
        position: absolute;
        right: -12px;
        top: -12px;
  
        > i {
          width: 100%;
          height: 100%;
          color: #000;
        }
      }
  
      .danger {
        background: #fce7e7;
        color: #a41818;
      }
  
      .warning {
        background: #f9eddf;
        color: #b76f1e;
      }
  
      .success {
        background: #e7f0fe;
        color: #1b428d;
      }
    }
  
    .labels-node:hover {
      .del {
        display: inline-block;
      }
    }
  
    .node-label {
      width: 80px;
      min-height: 20px;
      position: absolute;
      text-align: center;
      left: -10px;
      bottom: -14px;
      font-size: 12px;
      color: #2c2c35;
      overflow: hidden;
      /*隐藏*/
      white-space: nowrap;
      /*不换行*/
      text-overflow: ellipsis;
      /* 超出部分省略号 */
    }
  
    .node-label-hide {
      display: none;
    }
  }
  
  .clickmenu {
    width: 112px;
    padding: 8px 0;
    display: flex;
    flex-direction: column;
    z-index: 9999;
    position: absolute;
    background: #ffffff;
    box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
    border-radius: 4px;
  
    > section {
      width: 100%;
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      cursor: pointer;
      font-size: 14px;
      color: #2c2c35;
      box-sizing: border-box;
    }
  
    > section:nth-of-type(3) {
      color: #f91111;
    }
  
    > section:hover {
      background: #e7f0fe;
    }
  }
  
  // 关联弹出框的样式
  ::v-deep .el-dialog__body {
    max-height: 650px;
    overflow-y: auto;
  }
  
  ::v-deep .el-dialog__footer {
    border-top: 1px solid #f2f3f7;
    padding: 12px 24px;
  
    .el-button--primary {
      background-color: #116ef9;
      border-color: #116ef9;
      color: #ffffff;
    }
  }
  
  ::v-deep .el-checkbox__label {
    font-size: 14px;
    color: #2c2c35;
  }
  
  ::v-deep.el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
  
  .el-button--primary.is-disabled,
  .el-button--primary.is-disabled:active,
  .el-button--primary.is-disabled:focus,
  .el-button--primary.is-disabled:hover {
    color: #fff;
    background-color: #a0cfff;
    border-color: #a0cfff;
  }
  
  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
  
  ::v-deep .el-checkbox-group {
    display: flex;
    flex-direction: column;
    font-size: 14px;
  
    .check-cell {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
  
    > label {
      margin-bottom: 12px;
      color: #2c2c35;
    }
  
    .el-checkbox__label {
      color: #2c2c35;
    }
  }
  
  ::v-deep .rela-dialog {
    .el-dialog {
      margin-top: 5vh !important;
  
      .el-dialog__footer {
        position: sticky;
        bottom: 0;
        z-index: 5000;
        background-color: #fff;
      }
    }
  }
  
  ::v-deep .c-mini-toolbar > div:nth-of-type(1) {
    display: none;
  }
  
  .caution-time {
    ::v-deep .el-button {
      border: 0;
    }
  }
  ::v-deep .children-num {
    width: 80px;
  }
  // 进入界面默认样式
  .default {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 180px;
    .a2 {
      color: #2c2c35;
      font-weight: bold;
      font-size: 20px;
      line-height: 56px;
    }
    .a3 {
      color: #767684;
      width: 450px;
      text-align: center;
      line-height: 22px;
      font-size: 14px;
    }
  }
  ::v-deep{
    .node-label{
        min-width: 80px !important;
    }
    .node-label:hover{
        width:auto !important;
    }
  }
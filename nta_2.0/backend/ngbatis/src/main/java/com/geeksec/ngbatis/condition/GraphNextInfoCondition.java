package com.geeksec.ngbatis.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: qiuwen
 * @date: 2022/8/8
 * @Description:
 **/
@Data
public class GraphNextInfoCondition {
    /**
     * 当前延展关联点类型
     */
    private String type;

    /**
     * 当前关联延展点VID
     */
    private String str;

    /**
     * 待查询关系条件
     */
    private List<EdgeNum> edgeInfo;

    @Data
    public static class EdgeNum{

        /**
         * 边类型
         */
        private String edge;

        /**
         * 查询数量
         */
        private Integer num;

        /**
         * 黑名单权重范围
         */
        @JsonProperty(value = "weight_limit")
        private Integer[] weightLimit;
    }
}

package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 系统类型
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "OS")
@Data
public class OsVertex {

  /**
  * 系统名称
  */
  @Id
  @Column(name = "os_name")
  private String osName;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 应用服务
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "APPSERVICE")
@Data
public class AppServiceVertex {

  /**
  * ip_key+dPort+AppName
  */
  @Id
  @Column(name = "service_key")
  private String serviceKey;

  /**
  * ip地址
  */
  @Column(name = "ip_addr")
  private String ipAddr;

  /**
  * 应用协议
  */
  private String AppName;

  /**
  * 服务端口
  */
  private Long dPort;

  /**
  * IP协议
  */
  private String IPPro;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

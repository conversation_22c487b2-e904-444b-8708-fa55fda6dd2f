package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.OrgVertex;
import com.geeksec.ngbatis.vo.OrgVo;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface OrgDao extends NebulaDaoBasic<OrgVertex, String> {

    /**
     * 查询Org所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listOrgAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    /**
     * 查询机构详情
     */
    OrgVo getOrg(@Param("orgId") String orgId);

}

package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: IP地址信息
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "IP")
@Data
public class IpVertex {

  /**
  * 内网：任务 + IP / 外网：IP地址
  */
  @Id
  @Column(name = "ip_key")
  private String ipKey;

  /**
  * ip地址
  */
  @Column(name = "ip_addr")
  private String ipAddr;

  /**
  * IP使用版本 V4/V6
  */
  private String version;

  /**
  * 城市
  */
  private String city;

  /**
  * 国家
  */
  private String country;

  /**
  * 字节数
  */
  private Long bytes;

  /**
  * 包数
  */
  private Long packets;

  /**
  * 接收的流量大小
  */
  @Column(name = "recv_bytes")
  private Long recvBytes;

  /**
  * 发送的流量大小
  */
  @Column(name = "send_bytes")
  private Long sendBytes;

  /**
  * 平均流量 bps
  */
  @Column(name = "average_bps")
  private Long averageBps;

  /**
  * 出现的连接数
  */
  private Long times;

  /**
  * 首次出现时间
  */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
  * 末次出现时间
  */
  @Column(name = "last_seen")
  private Timestamp lastTime;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

  /**
  * 备注
  */
  private String remark;

}

package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.OrgDao;
import com.geeksec.ngbatis.service.OrgService;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Org服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class OrgServiceImpl implements OrgService {

    @Autowired
    private OrgDao orgDao;

    /**
     * Org关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getOrgNebulaAssociationNext(GraphNextInfoCondition condition) {
        return orgDao.listOrgAllEdgeTypeAssociationNext(condition);
    }
}

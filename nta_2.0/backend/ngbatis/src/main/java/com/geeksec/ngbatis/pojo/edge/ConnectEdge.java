package com.geeksec.ngbatis.pojo.edge;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: MAC链路访问（MAC->MAC），IP访问（IP->IP）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "connect")
@Data
public class ConnectEdge {

  /**
   * 应用名称
   */
  @Column(name = "app_name")
  private String appName;

  /**
   * 服务端口
   */
  private Long dport;

  /**
   * 接收的流量大小
   */
  @Column(name = "recv_bytes")
  private Long recvBytes;

  /**
   * 发送的流量大小
   */
  @Column(name = "send_bytes")
  private Long sendBytes;

  /**
   * 接收的包数
   */
  @Column(name = "recv_packets")
  private Long recvPackets;

  /**
   * 发送的包数
   */
  @Column(name = "send_packets")
  private Long sendPackets;

  /**
   * 接收的流量大小
   */
  private Long bytes;

  /**
   * 接收的包数
   */
  private Long packets;

  /**
   * 平均流量 bps
   */
  @Column(name = "average_bps")
  private Long averageBps;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}

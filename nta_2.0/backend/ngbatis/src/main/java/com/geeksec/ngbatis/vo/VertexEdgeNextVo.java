package com.geeksec.ngbatis.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
*@description: 点边集合Vo
*@author: shiwenxu
*@createtime: 2023/8/30 11:28
**/
@Data
public class VertexEdgeNextVo {

    private String fromType;

    private Long fromBlackList;

    private String fromAddr;

    private String fromLabel;

    private String toType;

    private Long toBlackList;

    private String toAddr;

    private String toLabel;

    private Boolean sourceStatus;

    private Boolean directionStatus;

    private String teage;

    @JsonProperty("v_info")
    private Map<String,Object> vInfo;


}

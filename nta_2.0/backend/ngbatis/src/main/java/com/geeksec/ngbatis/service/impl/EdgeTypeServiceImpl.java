package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.repository.HasLabelDao;
import com.geeksec.ngbatis.repository.ParseToDao;
import com.geeksec.ngbatis.service.EdgeTypeService;
import com.geeksec.ngbatis.vo.DomainIpVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: 边服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class EdgeTypeServiceImpl implements EdgeTypeService {

    @Autowired
    private ParseToDao parseToDao;
    @Autowired
    private HasLabelDao hasLabelDao;

    /**
     * 根据域名查询边类型为parse_to和cname_result的实体
     */
    @Override
    public List<DomainIpVo> listParseToCnameResultByDomains(List<String> domains) {
        return parseToDao.listParseToCnameResultByDomains(domains);
    }

    /**
     * 删除标签
     */
    @Override
    public void deleteHasLabel(String id) {
        hasLabelDao.deleteHasLabel(id);
    }

    /**
     * 插入标签
     */
    @Override
    public void insertHasLabel(String id, List<String> labels) {
        hasLabelDao.insertHasLabel(id,labels);
    }
}

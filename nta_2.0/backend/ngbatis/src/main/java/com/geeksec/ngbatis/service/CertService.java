package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.vo.*;

import java.util.List;
import java.util.Map;

/**
*@description: Cert服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface CertService {

    /**
     * 证书关联查询
     */
    List<VertexEdgeVo> getCertNebulaAssociation(String cert);

    /**
     * 证书关联查询Next
     */
    List<VertexEdgeNextVo> getCertNebulaAssociationNext(GraphNextInfoCondition condition);

    /**
     * 证书属性关联查询Next
     */
    List<VertexEdgeNextVo> listCertNebulaNextByProperties(GraphPropertiesNextCondition condition);

    /**
     * 根据ids查询存在证书属性
     */
    List<ExistCertPropertiesVo> listExistCertProperties(List<String> certs);

    /**
     * 根据ids查询证书详情
     */
    List<CertVo> listByCerts(List<String> certs);

    /**
     * 根据ids查询客户端热度
     */
    List<CertRelatedIpsVo> listRelatedIpsByCerts(List<String> certs);

    /**
     * 根据ids查询关联域名个数
     */
    List<CertCountVo> countDomainNumByCerts(List<String> certs);

    /**
     * 根据ids查询关联服务器ip个数
     */
    List<CertCountVo> countServerIpNumByCerts(List<String> certs);

    /**
     * 根据ids查询关联SSL个数
     */
    List<CertCountVo> countSslIpNumByCerts(List<String> certs);

    /**
     * 查询证书详情
     */
    Map<String, Object> getCertInfo(String cert);

    /**
     * 根据id查询标签
     */
    List<Long> listHasLabelByCert(String cert);

    /**
     * 根据id查询服务端热度
     */
    List<String> listRelatedServerIpsByCert(String cert);

    /**
     * 根据id查询客户端热度
     */
    List<String> listRelatedClientIpsByCert(String cert);

}

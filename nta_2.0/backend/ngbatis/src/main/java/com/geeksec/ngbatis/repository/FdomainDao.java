package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.FdomainVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FdomainDao extends NebulaDaoBasic<FdomainVertex, String> {

    /**
     * 查询Fdomain所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listFdomainAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}

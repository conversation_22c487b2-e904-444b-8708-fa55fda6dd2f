package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.AppServiceVertex;
import com.geeksec.ngbatis.vo.AppServiceVo;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AppServiceDao extends NebulaDaoBasic<AppServiceVertex, String> {

    /**
     * 查询AppService所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listAppServiceAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    AppServiceVo getAppService(@Param("serviceKey") String serviceKey);

}

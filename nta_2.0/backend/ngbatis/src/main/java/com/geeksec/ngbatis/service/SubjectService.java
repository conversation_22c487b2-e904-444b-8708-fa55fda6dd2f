package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;

import java.util.List;

/**
*@description: 所有者服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface SubjectService {

    /**
     * 所有者关联查询Next
     */
    List<VertexEdgeNextVo> getSubjectNebulaAssociationNext(GraphNextInfoCondition condition);

    /**
     * 所有者属性关联查询Next
     */
    List<VertexEdgeNextVo> listSubjectNebulaNextByProperties(GraphPropertiesNextCondition condition);

}

package com.geeksec.ngbatis.pojo.edge;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: DNS提供域名解析（IP->域名）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "dns_server_domain")
@Data
public class DnsServerDomainEdge {

  /**
   * dns类型
   */
  @Column(name = "dns_type")
  private String dnsType;

  /**
   * 回复类型
   */
  @Column(name = "answer_type")
  private Long answerType;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}

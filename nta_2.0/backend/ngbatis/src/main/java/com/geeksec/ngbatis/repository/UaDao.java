package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.UaVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface UaDao extends NebulaDaoBasic<UaVertex, String> {

    /**
     * 查询Ua所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listUaAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}

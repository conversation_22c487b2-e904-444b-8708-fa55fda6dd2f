package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.pojo.vertex.IssuerVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface IssuerDao extends NebulaDaoBasic<IssuerVertex, String> {

    /**
     * 查询签发机构所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listIssuerAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    /**
     * 签发机构属性关联查询Next
     */
    List<VertexEdgeNextVo> listIssuerNebulaNextByProperties(@Param("condition") GraphPropertiesNextCondition condition);

}

package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.MacVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface MacDao extends NebulaDaoBasic<MacVertex, String> {

    /**
     * 查询Mac所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listMacAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}

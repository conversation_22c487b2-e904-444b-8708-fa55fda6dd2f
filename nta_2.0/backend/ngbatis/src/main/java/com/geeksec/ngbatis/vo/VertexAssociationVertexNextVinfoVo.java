package com.geeksec.ngbatis.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
*@description: 点关联关系点VinfoVo
*@author: shiwenxu
*@createtime: 2023/8/30 11:28
**/
@Data
public class VertexAssociationVertexNextVinfoVo {

    @JsonProperty("average_bps")
    private String averageBps = "";

    @JsonProperty("white_list")
    private String whiteList = "";

    @JsonProperty("domain_addr")
    private String domainAddr = "";

    @JsonProperty("alexa_rank")
    private String alexaRank = "";

    @JsonProperty("whois")
    private String whois = "";

    @JsonProperty("black_list")
    private String blackList = "";

    @JsonProperty("bytes")
    private String bytes = "";

    @JsonProperty("lastTime")
    private String lastTime = "";

    @JsonProperty("firstTime")
    private String firstTime = "";

    @JsonProperty("remark")
    private String remark = "";

    @JsonProperty("task_id")
    private String taskId = "";

}


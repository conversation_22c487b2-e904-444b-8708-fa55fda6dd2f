package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.vo.DomainIpVo;

import java.util.List;

/**
*@description: 边服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface EdgeTypeService {

    /**
     * 根据域名查询边类型为parse_to和cname_result的实体
     */
    List<DomainIpVo> listParseToCnameResultByDomains(List<String> domains);

    /**
    * 删除标签
    */
    void deleteHasLabel(String id);

    /**
     * 插入标签
     */
    void insertHasLabel(String id,List<String> labels);

}

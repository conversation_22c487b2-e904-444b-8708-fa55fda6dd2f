package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.IpVertex;
import com.geeksec.ngbatis.vo.*;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface IpDao extends NebulaDaoBasic<IpVertex, String> {

    /**
    * 查询IP所有边类型关联数据
    */
    List<VertexEdgeVo> listIpAllEdgeTypeAssociation(@Param("ip") String ip);

    /**
     * 查询IP所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listIpAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    /**
     * 根据app_server边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByAppServerEdge(@Param("ips") List<String> ips);

    /**
     * 根据client_app边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByClientAppEdge(@Param("ips") List<String> ips);

    /**
     * 根据ids查询ip详情
     */
    List<IpVo> listByIps(@Param("ips") List<String> ips);

    /**
     * 根据ids查询标签
     */
    List<IpHasLabelVo> listHasLabelByIps(@Param("ips") List<String> ips);

    /**
     * 根据域名的边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByDomainEdge(@Param("ips") List<String> ips);

    /**
     * 根据证书的边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByCertEdge(@Param("ips") List<String> ips);

    /**
     * 根据ids查询存在IP属性
     */
    List<ExistIpPropertiesVo> listExistIpProperties(@Param("ips") List<String> ips);

    /**
     * 查询ip详情
     */
    Map<String, Object> getIpInfo(@Param("ip") String ip);

    /**
     * 统计开放端口数
     */
    IpCountVo countAppServerByIp(@Param("ip") String ip);

    /**
     * 统计访问端口数
     */
    IpCountVo countClientAppByIp(@Param("ip") String ip);

    /**
     * 关联锚域名（parse_to）
     */
    IpRelatedDomainsVo listParseToRelatedDomainsByIp(@Param("ip") String ip);

    /**
     * 关联锚域名（server_ssl_connect_domain SSL应用 server_http_connect_domain HTTP应用）
     */
    IpRelatedDomainsVo listServerRelatedDomainsByIp(@Param("ip") String ip);

    /**
     * 根据domains统计所属锚域名数
     */
    Integer countFDomainNumByDomains(@Param("domains") Set<String> domains);

    /**
     * 根据id查询标签
     */
    List<Long> listHasLabelByIp(@Param("ip") String ip);

    /**
     * 修改备注
     */
    void updateRemark(@Param("type") String type,@Param("id") String id,@Param("remark") String remark);

}

package com.geeksec.ngbatis.service.impl;

// Copyright (c) 2022 All project authors. All rights reserved.
//
// This source code is licensed under Apache 2.0 License.

import com.geeksec.ngbatis.pojo.vertex.Person;
import com.geeksec.ngbatis.repository.PersonDao;
import com.geeksec.ngbatis.service.PersonService;
import org.nebula.contrib.ngbatis.utils.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>Person业务类实例</p>
 * <AUTHOR>
 * @since 2022-06-17 7:18
 * <br>Now is history!
 */
@Service
public class PersonServiceImpl implements PersonService {

    @Autowired
    private PersonDao dao;

    // 不管属性是否为空，如果数据库中已有对应 ID 的值，则覆盖
    public void insert( Person person ) {
        dao.insert( person );
    }

    // 仅写入非空属性
    public void insertSelective( Person person ) {
        dao.insertSelective( person );
    }

    // 此处，Person 的主键栏 name 为 String ，则入参为 String
    public Person selectById( String id ) {
        return dao.selectById( id );
    }

    // 按属性查询
    public List<Person> selectBySelective(Person person ) {
        return dao.selectBySelective( person );
    }

    // FIXME 当前版本，这个接口尚不是逻辑删除，待修改。
    public void deleteLogicById( String id ) {
        dao.deleteLogicById( id );
    }

    // Page 为 {@link org.nebula.contrib.ngbatis.utils.Page}
    public List<Person> selectPage( Page<Person> page ) {
        return dao.selectPage( page );
    }

    // 判断两个节点是否存在某种关系
    public boolean existsEdge( String startId, Class edgeType, String endId ) {
        return dao.existsEdge( startId, edgeType, endId );
    }

    // 查找一个节点某种关系中的所有上游节点
    public List<Person> listStartNodes( Class edgeType, String endId ) {
        return dao.listStartNodes( edgeType, endId );
    }

    // 查找一个节点中，某种关系的唯一一个上游节点
    public Person startNode( Class edgeType, String endId ) {
        return dao.startNode( edgeType, endId );
    }

}

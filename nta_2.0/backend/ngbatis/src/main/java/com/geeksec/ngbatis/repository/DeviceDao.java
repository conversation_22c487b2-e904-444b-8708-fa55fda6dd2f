package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.pojo.vertex.DeviceVertex;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DeviceDao extends NebulaDaoBasic<DeviceVertex, String> {

    /**
     * 查询Device所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listDeviceAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}

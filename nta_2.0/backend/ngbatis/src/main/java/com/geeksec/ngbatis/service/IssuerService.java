package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;

import java.util.List;

/**
*@description: 签发机构服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface IssuerService {

    /**
     * 签发机构关联查询Next
     */
    List<VertexEdgeNextVo> getIssuerNebulaAssociationNext(GraphNextInfoCondition condition);

    /**
     * 签发机构属性关联查询Next
     */
    List<VertexEdgeNextVo> listIssuerNebulaNextByProperties(GraphPropertiesNextCondition condition);

}

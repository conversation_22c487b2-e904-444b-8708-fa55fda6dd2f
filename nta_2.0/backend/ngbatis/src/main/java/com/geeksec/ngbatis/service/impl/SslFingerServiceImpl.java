package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.SslFingerDao;
import com.geeksec.ngbatis.service.SslFingerService;
import com.geeksec.ngbatis.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: SSL指纹服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class SslFingerServiceImpl implements SslFingerService {

    @Autowired
    private SslFingerDao sslFingerDao;

    /**
     * 根据ids查询SSL指纹描述
     */
    @Override
    public List<SslFingerDescVo> listFingerDescribeByFingers(List<String> fingers) {
        return sslFingerDao.listFingerDescribeByFingers(fingers);
    }

    /**
     * 根据ids查询SSL指纹标签
     */
    @Override
    public List<SslFingerLabelVo> listFingerLabelByFingers(List<String> fingers) {
        return sslFingerDao.listFingerLabelByFingers(fingers);
    }

    /**
     * 根据ids查询SSL指纹服务端IP热度
     */
    @Override
    public List<SslFingerRelatedIpsVo> listServerRelatedIpsByFingers(List<String> fingers) {
        return sslFingerDao.listServerRelatedIpsByFingers(fingers);
    }

    /**
     * 根据ids查询SSL指纹客户端IP热度
     */
    @Override
    public List<SslFingerRelatedIpsVo> listClientRelatedIpsByFingers(List<String> fingers) {
        return sslFingerDao.listClientRelatedIpsByFingers(fingers);
    }

    /**
     * 根据ids查询关联证书数量
     */
    @Override
    public List<SslFingerCountVo> countCertByFingers(List<String> fingers) {
        return sslFingerDao.countCertByFingers(fingers);
    }

    /**
     * SslFinger关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getSslFingerNebulaAssociationNext(GraphNextInfoCondition condition) {
        return sslFingerDao.listSslFingerAllEdgeTypeAssociationNext(condition);
    }

}

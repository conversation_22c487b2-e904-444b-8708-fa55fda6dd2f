package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;

import java.util.List;

/**
*@description: Victim服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface VictimService {

    /**
     * Victim关联查询Next
     */
    List<VertexEdgeNextVo> getVictimNebulaAssociationNext(GraphNextInfoCondition condition);

}

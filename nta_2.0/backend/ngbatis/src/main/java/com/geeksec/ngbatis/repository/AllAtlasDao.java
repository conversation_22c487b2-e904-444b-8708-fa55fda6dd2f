package com.geeksec.ngbatis.repository;


import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.condition.SubGraphNextCondition;
import com.vesoft.nebula.client.graph.data.ResultSet;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * @author: jerryzhou
 * @date: 2023/12/7 09:51
 * @Description: 通用ngbatisDao，用于查询多种点和边类型
 **/
public interface AllAtlasDao {

    /**
     * 通过Fetch on 进行VID探索，只返回存在的点相关数据
     */
    List<Map<String, Object>> fetchOnTagInfoByVids(@Param("vids") List<String> vids);

    /**
     * 通过Match进行属性探索，只返回存在的点相关数据
     */
    List<Map<String, Object>> matchTagInfoByProperties(@Param("condition") GraphPropertiesNextCondition condition);

    /**
     * 子图遍历
     */
    ResultSet vidSubSearch(@Param("condition") SubGraphNextCondition condition);

    /**
     * 通过当前查找到的节点，查找他们之间有没有相互的关联关系
     *
     * @param allVid
     * @return
     */
    List<Map<String, Object>> findPathByVids(@Param("vids") List<String> allVid);

    /**
     * 图探索->侧拉框点各类边关系进行列表查询
     *
     * @param vid         点VID
     * @param edgeType    查询边类型
     * @param directStr   是否为单向边or双向边
     * @param orderField  排序字段
     * @param sortOrder   排序方式
     * @param limit       限制条数
     * @param currentPage 当前页
     * @param offset      偏移量
     * @return
     */
    List<Map<String, Object>> getTagRelationList(@Param("vid") String vid,
                                                 @Param("edgeType") String edgeType,
                                                 @Param("direct") Boolean direct,
                                                 @Param("orderField") String orderField,
                                                 @Param("sortOrder") String sortOrder,
                                                 @Param("limit") Integer limit,
                                                 @Param("currentPage") Integer currentPage,
                                                 @Param("offset") Integer offset,
                                                 @Param("targetType") String targetType);

    /**
     * 图探索->侧拉框点各类边关系进行条目数数量查询
     *
     * @param vid      点VID
     * @param edgeType 查询边类型
     * @param direct   正反向
     * @return
     */
    Integer countTagRelation(@Param("vid") String vid,
                             @Param("edgeType") String edgeType,
                             @Param("direct") Boolean direct);

    /**
     * 图探索->获取当前所有企业店点的信息
     *
     * @return
     */
    List<Map<String, Object>> getAllOrgInfo();

    /**
     * 图探索->获取企业关联的标签
     */
    List<Map<String,Object>> getOrgHasLabel(@Param("vid") String vid);

    /**
     * 图探索->获取当前点的标签信息
     *
     * @param str
     * @return
     */
    List<List<String>> getTagLabels(@Param("str") String str);

    /**
     * 图探索->获取当前点支持的边类型列表
     *
     * @param vid
     * @param edgeStr
     * @return
     */
    List<String> getVisibleRelation(@Param("vid") String vid,
                                    @Param("direct") String direct,
                                    @Param("edge_str") String edgeStr);

    /**
     * 图探索->比节点关注查询
     * @param startVid
     * @param endVid
     * @param startType
     * @param endType
     * @return
     */
    List<Map<String, Object>> getFocusTagRelation(@Param("start_vid") String startVid,
                                                  @Param("end_vid") String endVid,
                                                  @Param("start_type") String startType,
                                                  @Param("end_type") String endType);



}

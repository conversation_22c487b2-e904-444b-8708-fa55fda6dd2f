package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.repository.DomainDao;
import com.geeksec.ngbatis.service.DomainService;
import com.geeksec.ngbatis.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
*@description: Domain服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class DomainServiceImpl implements DomainService {

    @Autowired
    private DomainDao domainDao;

    /**
     * 域名关联查询
     */
    public List<VertexEdgeVo> getDomainNebulaAssociation(String domain){
        return domainDao.listDomainAllEdgeTypeAssociation(domain);
    }

    /**
     * 域名关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getDomainNebulaAssociationNext(GraphNextInfoCondition condition){
        return domainDao.listDomainAllEdgeTypeAssociationNext(condition);
    }

    /**
     * 域名属性关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> listDomainNebulaNextByProperties(GraphPropertiesNextCondition condition){
        return domainDao.listDomainNebulaNextByProperties(condition);
    }

    /**
     * 根据ids查询存在域名属性
     */
    @Override
    public List<ExistDomainPropertiesVo> listExistDomainProperties(List<String> domains) {
        return domainDao.listExistDomainProperties(domains);
    }

    /**
     * 根据ids查询域名详情
     */
    @Override
    public List<DomainVo> listByDomains(List<String> domains) {
        return domainDao.listByDomains(domains);
    }

    /**
     * 根据ids查询标签
     */
    @Override
    public List<DomainHasLabelVo> listHasLabelByDomains(List<String> domains) {
        return domainDao.listHasLabelByDomains(domains);
    }

    /**
    * 根据ids查询锚域名
    */
    @Override
    public List<FDomainVo> listFDomainByDomains(List<String> domains) {
        return domainDao.listFDomainByDomains(domains);
    }

    /**
    * 根据id查询标签
    */
    @Override
    public DomainHasLabelVo getHasLabelByDomain(String domain) {
        return domainDao.getHasLabelByDomain(domain);
    }

    /**
     * 根据id查询兄弟域名数量
     */
    @Override
    public DomainCountVo countBrotherNumByDomain(String domain) {
        return domainDao.countBrotherNumByDomain(domain);
    }

    /**
     * 根据ids查询反查ip
     */
    @Override
    public List<DomainRelatedIpsVo> listRelatedIpsByDomains(List<String> domains) {
        return domainDao.listRelatedIpsByDomains(domains);
    }

    /**
     * 根据ids查询CName该域名数量
     */
    @Override
    public List<DomainCountVo> countCnameDomainsByDomains(List<String> domains) {
        return domainDao.countCnameDomainsByDomains(domains);
    }

    /**
     * 根据ids查询CName指向该域名数量
     */
    @Override
    public List<DomainCountVo> countCnamePointDomainsByDomains(List<String> domains) {
        return domainDao.countCnamePointDomainsByDomains(domains);
    }

    /**
     * 根据ids查询请求该域名的ips
     */
    @Override
    public List<DomainRelatedIpsVo> listRequestDomainIpsByDomains(List<String> domains) {
        return domainDao.listRequestDomainIpsByDomains(domains);
    }

    /**
     * 根据ids查询回应类型具有cname的边
     */
    @Override
    public List<DomainRelatedIpsVo> listResponseTypeIpsByDomains(List<String> domains) {
        return domainDao.listResponseTypeIpsByDomains(domains);
    }

    /**
     * 根据ids查询dns_server_domain出现位置
     */
    @Override
    public List<DomainCountVo> countDnsServerDomainByDomains(List<String> domains) {
        return domainDao.countDnsServerDomainByDomains(domains);
    }

    /**
     * 根据ids查询server_ssl_connect_domain出现位置
     */
    @Override
    public List<DomainCountVo> countServerSslConnectDomainByDomains(List<String> domains) {
        return domainDao.countServerSslConnectDomainByDomains(domains);
    }

    /**
     * 根据ids查询server_http_connect_domain出现位置
     */
    @Override
    public List<DomainCountVo> countServerHttpConnectDomainByDomains(List<String> domains) {
        return domainDao.countServerHttpConnectDomainByDomains(domains);
    }

    /**
     * 根据ids查询sni_bind出现位置
     */
    @Override
    public List<DomainCountVo> countSniBindByDomains(List<String> domains) {
        return domainDao.countSniBindByDomains(domains);
    }

    /**
     * 查询域名详情
     */
    @Override
    public Map<String, Object> getDomainInfo(String domain) {
        return domainDao.getDomainInfo(domain);
    }

    /**
    * 根据id查询标签
    */
    @Override
    public List<Long> listHasLabelByDomain(String domain) {
        return domainDao.listHasLabelByDomain(domain);
    }

    /**
    * 根据id查询锚域名
    */
    @Override
    public String getFDomainByDomain(String domain) {
        return domainDao.getFDomainByDomain(domain);
    }

    /**
     * 根据fid查询标签
     */
    @Override
    public List<Long> listHasLabelByFDomain(String domain) {
        return domainDao.listHasLabelByFDomain(domain);
    }

    /**
    * 根据id查询cname_result指向IP数量
    */
    @Override
    public Integer countCnameResultByDomain(String domain) {
        return domainDao.countCnameResultByDomain(domain);
    }

    /**
    * 根据id查询parse_to指向IP数量
    */
    @Override
    public Integer countParseToByDomain(String domain) {
        return domainDao.countParseToByDomain(domain);
    }

    /**
    * 根据id查询客户端热度
    */
    @Override
    public DomainRelatedIpsVo listRelatedClientIpsByDomain(String domain) {
        return domainDao.listRelatedClientIpsByDomain(domain);
    }

    /**
    * 根据id查询关联证书数量
    */
    @Override
    public Integer countCertNumByDomain(String domain) {
        return domainDao.countCertNumByDomain(domain);
    }

}

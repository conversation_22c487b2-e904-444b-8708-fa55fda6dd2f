package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.repository.AppDao;
import com.geeksec.ngbatis.service.AppService;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: APP服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private AppDao appDao;

    /**
     * APP关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getAppNebulaAssociationNext(GraphNextInfoCondition condition) {
        return appDao.listAppAllEdgeTypeAssociationNext(condition);
    }


}

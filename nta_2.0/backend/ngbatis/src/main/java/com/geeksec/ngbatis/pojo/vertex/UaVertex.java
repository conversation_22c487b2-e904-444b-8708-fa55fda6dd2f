package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: UA
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "UA")
@Data
public class UaVertex {

  /**
  * ua的ID
  */
  @Id
  @Column(name = "ua_id")
  private String uaId;

  /**
  * UA内容
  */
  @Column(name = "ua_str")
  private String uaStr;

  /**
  * UA说明
  */
  @Column(name = "ua_desc")
  private String uaDesc;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

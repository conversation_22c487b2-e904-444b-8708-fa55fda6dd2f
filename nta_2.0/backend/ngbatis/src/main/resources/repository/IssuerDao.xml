<mapper namespace="com.geeksec.ngbatis.repository.IssuerDao">

    <select id="listIssuerAllEdgeTypeAssociationNext" resultType="com.geeksec.ngbatis.vo.VertexEdgeNextVo">
        MATCH (ISSUER:ISSUER)
        WHERE id(ISSUER) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "ISSUER" AS fromType,
        0 AS fromBlackList,
        id(ISSUER) AS fromAddr,
        ISSUER.ISSUER.common_name AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'issuer_related'){
                UNION ALL
                MATCH
                (ISSUER:ISSUER)-[issuer_related:issuer_related]-(CERT:CERT)
                WHERE
                id(ISSUER) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "ISSUER" AS toType,
                0 AS toBlackList,
                id(ISSUER) AS toAddr,
                ISSUER.ISSUER.common_name AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(ISSUER) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

    <select id="listIssuerNebulaNextByProperties" resultType="com.geeksec.ngbatis.vo.VertexEdgeNextVo">

    </select>

</mapper>
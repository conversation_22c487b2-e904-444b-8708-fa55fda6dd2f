<mapper namespace="com.geeksec.ngbatis.repository.FdomainDao">

    <select id="listFdomainAllEdgeTypeAssociationNext" resultType="com.geeksec.ngbatis.vo.VertexEdgeNextVo">
        MATCH (FDOMAIN:FDOMAIN)
        WHERE id(FDOMAIN) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "FDOMAIN" AS fromType,
        0 AS fromBlackList,
        FDOMAIN.FDOMAIN.fdomain_addr AS fromAddr,
        FDOMAIN.FDOMAIN.fdomain_addr AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'domain_belong_to'){
                UNION ALL
                MATCH
                (FDOMAIN:FDOMAIN)-[domain_belong_to:domain_belong_to]-(DOMAIN:DOMAIN)
                WHERE
                id(FDOMAIN) == ${ng.valueFmt(condition.str)}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "FDOMAIN" AS toType,
                0 AS toBlackList,
                FDOMAIN.FDOMAIN.fdomain_addr AS toAddr,
                FDOMAIN.FDOMAIN.fdomain_addr AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_validate_fDomain'){
                UNION ALL
                MATCH
                (FDOMAIN:FDOMAIN)-[cert_validate_fDomain:cert_validate_fDomain]-(CERT:CERT)
                WHERE
                id(FDOMAIN) == ${ng.valueFmt(condition.str)}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'fDomain_belong_to_org'){
                UNION ALL
                MATCH
                (FDOMAIN:FDOMAIN)-[fDomain_belong_to_org:fDomain_belong_to_org]->(ORG:ORG)
                WHERE
                id(FDOMAIN) == ${ng.valueFmt(condition.str)}
                RETURN
                "FDOMAIN" AS fromType,
                0 AS fromBlackList,
                FDOMAIN.FDOMAIN.fdomain_addr AS fromAddr,
                FDOMAIN.FDOMAIN.fdomain_addr AS fromLabel,
                "ORG" AS toType,
                ORG.ORG.black_list AS toBlackList,
                id(ORG) AS toAddr,
                ORG.ORG.org_name AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(ORG) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>
<mapper namespace="com.geeksec.ngbatis.repository.DeviceDao">

    <select id="listDeviceAllEdgeTypeAssociationNext" resultType="com.geeksec.ngbatis.vo.VertexEdgeNextVo">
        MATCH (DEVICE:DEVICE)
        WHERE id(DEVICE) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "DEVICE" AS fromType,
        0 AS fromBlackList,
        DEVICE.DEVICE.device_name AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'ua_belong_device'){
                UNION ALL
                MATCH
                (DEVICE:DEVICE)-[ua_belong_device:ua_belong_device]-(UA:UA)
                WHERE
                id(DEVICE) == ${ng.valueFmt(condition.str)}
                RETURN
                "UA" AS fromType,
                0 AS fromBlackList,
                UA.UA.ua_id AS fromAddr,
                "DEVICE" AS toType,
                0 AS toBlackList,
                DEVICE.DEVICE.device_name AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(UA) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>
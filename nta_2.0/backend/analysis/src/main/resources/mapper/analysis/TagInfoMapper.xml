<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.TagInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.TbTagInfo">
        <id column="tag_id" property="tagId"/>
        <result column="tag_type" property="tagType"/>
        <result column="tag_remark" property="tagRemark"/>
        <result column="tag_explain" property="tagExplain"/>
        <result column="tag_text" property="tagText"/>
        <result column="tag_num" property="tagNum"/>
        <result column="tag_target_type" property="tagTargetType"/>
        <result column="default_black_list" property="defaultBlackList"/>
        <result column="default_white_list" property="defaultWhiteList"/>
        <result column="black_list" property="blackList"/>
        <result column="white_list" property="whiteList"/>
        <result column="created_time" property="createdTime"/>
        <result column="last_created_time" property="lastCreatedTime"/>
        <result column="tag_family" property="tagFamily"/>
    </resultMap>

    <select id="getListForInit" resultMap="BaseResultMap">
        select tag_id, tag_text, black_list, white_list
        from tb_tag_info t1
        where t1.tag_id &lt; 35000
           or t1.tag_id &gt; 150000
        union
        select rule_id as tag_id, concat('规则_', rule_name) as tag_text, rule_level as black_list, 0 as white_list
        from tb_rule t2
        where t2.rule_id > 35000
    </select>

    <select id="getListForInitModel" resultMap="BaseResultMap">
        select model_id as tag_id, model_name as tag_text, 0 as black_list, 0 as cnt, 0 as white_list
        from tb_model
    </select>

    <select id="getTagInfoById" resultType="com.geeksec.analysis.entity.TbTagInfo"
            parameterType="java.lang.Integer">
        select *
        from tb_tag_info
        where tag_type != 0
          and tag_id = ${knowledge_id}
    </select>

    <select id="getAllTagListByTargetType" resultType="com.geeksec.analysis.entity.vo.TagLibraryVo">
        select DISTINCT tti.tag_id,
        tti.tag_text,
        tti.tag_target_type,
        tti.tag_explain,
        tti.black_list,
        tti.white_list,
        ifnull(tta.attribute_name, '') as attributeName,
        tta.attribute_id as attrbuteId
        from tb_tag_info tti
        left join tb_tag_attribute_rate ttar on tti.tag_id = ttar.tag_id
        left join tb_tag_attribute tta on ttar.attribute_id = tta.attribute_id
        <where>
            1=1
            #             and tti.tag_attr != ${shield_pro_type}
            and tti.tag_type = 1
            <if test="search_name !=null and search_name!=''">
                and tti.tag_text like CONCAT('%',#{search_name},'%')
            </if>
            <if test="tag_target_type !=null">
                and tti.tag_target_type = ${tag_target_type}
            </if>
        </where>
    </select>

    <select id="getAllTagListByAttributeId" resultType="com.geeksec.analysis.entity.vo.TagLibraryVo">
        select DISTINCT tti.tag_id,
        tti.tag_text,
        tti.tag_target_type,
        tti.tag_explain,
        tti.black_list,
        tti.white_list,
        ifnull(tta.attribute_name, '') as attributeName,
        tta.attribute_id as attrbuteId
        from tb_tag_info tti
        left join tb_tag_attribute_rate ttar on tti.tag_id = ttar.tag_id
        left join tb_tag_attribute tta on ttar.attribute_id = tta.attribute_id
        <where>
            1=1
            #             and tti.tag_attr != ${shield_pro_type}
            and tti.tag_type = 1
            <if test="search_name !=null and search_name!=''">
                and tti.tag_text like CONCAT('%',#{search_name},'%')
            </if>
            and tta.attribute_id = ${attribute_id}
        </where>
    </select>

    <select id="getTargetCountBySearchName" resultType="java.util.Map">
        SELECT DISTINCT tag_target_type, count(*) as count_num
        from (SELECT * from tb_tag_info where tag_class = 0 union all (SELECT * from tb_tag_info where tag_class = 1 and user_id = #{userId})) tti
        <where>
            tti.black_list between ${startBlack} and ${endBlack}
            and tti.white_list between ${startWhite} and ${endWhite}
            #             and tti.tag_attr != ${shield_pro_type}
            and tti.tag_type = 1
            <if test="search_name !=null and search_name!=''">
                and tag_text like CONCAT('%',#{search_name},'%')
            </if>
        </where>
        group by tag_target_type;
    </select>

    <select id="getAttributeCountBySearchName" resultType="java.util.Map">
        select DISTINCT ifnull(tta.attribute_name, '无细分类') as attributeName,
        ifnull(tta.attribute_id, 0) as attributeId,
        ifnull(tta.target_id, 0) as targetId,
        count(*) as count_num
        from tb_tag_info tti
        left join tb_tag_attribute_rate ttar on tti.tag_id = ttar.tag_id
        left join tb_tag_attribute tta on ttar.attribute_id = tta.attribute_id
        <where>
            tti.black_list between ${startBlack} and ${endBlack}
            and tti.white_list between ${startWhite} and ${endWhite}
            #             and tti.tag_attr != ${shield_pro_type}
            and tti.tag_type = 1
            <if test="search_name !=null and search_name!=''">
                and tti.tag_text like CONCAT('%',#{search_name},'%')
            </if>
        </where>
        group by attributeId
        ORDER BY targetId ASC
    </select>

    <select id="getTagListByCondition" resultType="com.geeksec.analysis.entity.TbTagInfo">
        select tti.tag_id,
        tti.tag_text,
        tti.black_list,
        tti.white_list,
        tti.tag_explain,
        tti.tag_target_type,
        tta.attribute_id,
        tta.attribute_name
        from tb_tag_info tti
        left join tb_tag_attribute_rate ttar on tti.tag_id = ttar.tag_id
        left join tb_tag_attribute tta on ttar.attribute_id = tta.attribute_id
        <where>
            tti.black_list between ${startBlack} and ${endBlack}
            and tti.white_list between ${startWhite} and ${endWhite}
            #             and tti.tag_attr != ${shield_pro_type}
            and tti.tag_type = 1
            <if test="searchName !=null and searchName!=''">
                and tti.tag_text like CONCAT('%',#{searchName},'%')
            </if>
            <if test="attributeId !=null">
                and tta.attribute_id = ${attributeId}
            </if>
        </where>
    </select>

    <select id="getTagListCountByCondition" resultType="java.lang.Integer">
        select count(*)
        from tb_tag_info tti
        left join tb_tag_attribute_rate ttar on tti.tag_id = ttar.tag_id
        left join tb_tag_attribute tta on ttar.attribute_id = tta.attribute_id
        <where>
            tti.black_list between ${startBlack} and ${endBlack}
            and tti.white_list between ${startWhite} and ${endWhite}
            and tti.tag_attr != ${shield_pro_type}
            and tti.tag_type = 1
            <if test="searchName !=null and searchName!=''">
                and tti.tag_text like CONCAT('%',#{searchName},'%')
            </if>
            <if test="tagTargetType !=null">
                and tti.tag_target_type = ${tagTargetType}
            </if>
            <if test="attributeId !=null">
                and tta.attribute_id = ${attributeId}
            </if>
        </where>
    </select>

    <select id="getMaxTagId" resultType="java.lang.Integer">
        select max(tag_id) from tb_tag_info;
    </select>

    <select id="getTagTextByTagList" resultType="java.lang.String">
        select tag_text from tb_tag_info where tag_id in
        <foreach collection="tag_id_list" item="tag_id" open="(" separator="," close=")">
            #{tag_id}
        </foreach>
    </select>

    <select id="queryList" resultType="com.geeksec.analysis.entity.TbTagInfo">
        SELECT
        *
        FROM
        (SELECT * from tb_tag_info where tag_class = 0 union all (SELECT * from tb_tag_info where tag_class = 1 and user_id = #{userId})) tti
        WHERE
        tti.black_list between ${startBlack} and ${endBlack}
        and tti.white_list between ${startWhite} and ${endWhite}
        and tti.tag_attr != ${shieldProType}
        <if test="searchName !=null and searchName!=''">
            and tti.tag_text like CONCAT('%',#{searchName},'%')
        </if>
        <if test="tagTargetType !=null and tagTargetType!=9999">
            and tti.tag_target_type = #{tagTargetType}
        </if>
    </select>

    <select id="listTagAttribute" resultType="com.geeksec.analysis.entity.TbTagAttribute">
        SELECT
            *
        FROM
            tb_tag_attribute
    </select>

    <select id="getTagInfoListByIds" parameterType="java.util.List" resultType="com.geeksec.analysis.entity.TbTagInfo">
        select *
        from tb_tag_info
        where tag_id in
        <foreach collection="tag_id_list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>

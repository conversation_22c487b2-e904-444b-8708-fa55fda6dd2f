<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.CertTagInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.CertTagInfoEntity">
        <id column="tag_id" property="tagId" />
        <result column="tag_type" property="tagType" />
        <result column="tag_text" property="tagText" />
        <result column="tag_remark" property="tagRemark" />
        <result column="tag_num" property="tagNum" />
        <result column="default_black_list" property="defaultBlackList" />
        <result column="default_white_list" property="defaultWhiteList" />
        <result column="black_list" property="blackList" />
        <result column="white_list" property="whiteList" />
        <result column="created_time" property="createdTime" />
        <result column="last_created_time" property="lastCreatedTime" />
        <result column="tag_family" property="tagFamily" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        tag_id, tag_type, tag_text, tag_remark, tag_num, default_black_list, default_white_list, black_list, white_list, created_time, last_created_time, tag_family
    </sql>

</mapper>

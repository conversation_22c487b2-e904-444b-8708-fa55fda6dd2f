<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.ThreatInfoDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.ThreatInfo">
        <id column="id" property="id"/>
        <result column="target" property="target"/>
        <result column="target_type" property="targetType"/>
        <result column="tag_name" property="tagName"/>
        <result column="source" property="source"/>
        <result column="version" property="version"/>
        <result column="shash" property="shash"/>
        <result column="create_time" property="createTime"/>
        <result column="valid_from" property="validFrom"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, target, target_type, tag_name, source, version, shash, create_time, valid_from, update_time
    </sql>
    <insert id="insertBatch">
        insert into tb_threat_info(target,target_type,tag_name,source,version,shash,valid_from) values
        <foreach collection="list" item="ThreatInfo" separator=",">
            (#{ThreatInfo.target},#{ThreatInfo.targetType},#{ThreatInfo.tagName},#{ThreatInfo.source},#{ThreatInfo.version},#{ThreatInfo.shash},#{ThreatInfo.validFrom})
        </foreach>
    </insert>
    <delete id="truncate">
       truncate tb_threat_info;
    </delete>

</mapper>

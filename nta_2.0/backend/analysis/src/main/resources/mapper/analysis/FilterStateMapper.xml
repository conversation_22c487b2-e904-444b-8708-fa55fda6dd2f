<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.FilterStateDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.FilterState">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, state
    </sql>

    <insert id="createFilterState">
        insert into tb_filter_state (task_id, state) values (#{task_id},#{state});
    </insert>

    <update id="updateStateByTaskId" parameterType="com.geeksec.analysis.entity.condition.FilterRuleCondition">
        update tb_filter_state set state=#{state} where task_id = #{taskId}
    </update>

    <select id="getFilterStateByTaskId" resultType="com.geeksec.analysis.entity.FilterState">
        select <include refid="Base_Column_List"></include>
        from tb_filter_state where task_id = #{taskId}
    </select>
</mapper>

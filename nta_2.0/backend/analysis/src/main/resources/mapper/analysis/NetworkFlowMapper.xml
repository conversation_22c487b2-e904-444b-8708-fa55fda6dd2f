<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.analysis.dao.NetworkFlowDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.analysis.entity.NetworkFlow">
        <id column="id" property="id" />
        <result column="pcie_id" property="pcieId" />
        <result column="flow_name" property="flowName" />
        <result column="network_type" property="networkType" />
        <result column="created_time" property="createdTime" />
        <result column="state" property="state" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, pcie_id, flow_name, network_type, created_time, state
    </sql>

</mapper>

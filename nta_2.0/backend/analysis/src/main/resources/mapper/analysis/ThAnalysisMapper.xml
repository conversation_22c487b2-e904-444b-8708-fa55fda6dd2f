<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.geeksec.analysis.dao.ThAnalysisDao">
    <select id="getCaptureMode" resultType="com.geeksec.analysis.entity.dict.ValSetEntity">
        select * from th_analysis.tb_valset where valset_id = 'Trance'
    </select>

    <select id="getTagInfo" resultType="com.geeksec.analysis.entity.dict.AnalysisTagInfoEntity">
        select * from tb_tag_info t1 where t1.tag_attr != ${proId} and t1.tag_type = 1
    </select>

    <select id="getAllTagInfo" resultType="com.geeksec.analysis.entity.dict.AnalysisTagInfoEntity">
        select * from tb_tag_info t1 where t1.tag_type = 1;
    </select>

    <select id="getTagInfoByIds" resultType="com.geeksec.analysis.entity.dict.AnalysisTagInfoEntity">
        select * from tb_tag_info where tag_id in
        <if test="labels.size() != 0 and labels != null">
            <foreach collection="labels" item="label" open="(" close=")" separator=",">
                ${label}
            </foreach>
        </if>
    </select>


    <select id="getRule" resultType="com.geeksec.analysis.entity.dict.RuleEntity">
        select * from tb_rule where status = 1
    </select>
    <select id="getAppProValueDict" resultType="com.geeksec.analysis.entity.dict.AppProValue">
        select Pro_Id    as Pro_Id,
        pro_value as Pro_Value,
        pro_name  as Pro_Name,
        pro_type  as Pro_Type,
        type      as Type,
        pro_exp as Pro_Exp
        from app_pro_value
        where 1 > Pro_Id  or pro_id > 0
    </select>

    <select id="getAppRuleDict" resultType="com.geeksec.analysis.entity.dict.AppProValue">
        select Pro_Id    as Pro_Id,
        pro_value as Pro_Value,
        pro_name  as Pro_Name,
        pro_type  as Pro_Type,
        type      as Type,
        pro_exp as Pro_Exp
        from app_pro_value
        where pro_id > 0
    </select>

    <select id="getTagAttribute" resultType="com.geeksec.analysis.entity.dict.TagAttribute">
        select group_concat(distinct tag_id) as tag_str, tae.attribute_id, ta.attribute_name
        from tb_tag_attribute_rate tae
                 join tb_tag_attribute ta on tae.attribute_id = ta.attribute_id
        group by attribute_id
    </select>

    <select id="getSessionTag" resultType="com.geeksec.analysis.entity.session.SessionTagEntity">
        select * from tb_tag_info
        where tag_id in
        <if test="lables.size() != 0 and lables != null">
            <foreach collection="lables" item="lable" open="(" close=")" separator=",">
                ${lable}
            </foreach>
        </if>
    </select>

    <select id="getSessionIdInfo" resultType="com.geeksec.analysis.entity.session.SessionIdEntity">
        select * from tb_session_id where session_id = #{sessionId}
    </select>

    <select id="existSessionIdInfo" resultType="java.lang.Integer">
        select count(*) from tb_session_id where session_id =#{sessionId}
    </select>


    <select id="getTaskNameByTaskId" resultType="java.lang.String">
        select task_name from tb_task_analysis where task_id = ${taskId};
    </select>

    <insert id="addSessionIdInfo">
        insert into tb_session_id (session_id,black_list,white_list,remark) values (#{sessionId},0,0,#{remark})
    </insert>

    <insert id="addTargetRemark">
        insert into tb_target_remark (target_key,target_type,remark) values (#{targetKey},#{targetType},#{remark})
    </insert>

    <update id="updateSessionIdRemark">
        update tb_session_id
        <trim prefix="set">
            <if test="remarkStr != null and remarkStr != ''">
                remark = #{remarkStr}
            </if>
        </trim>
        where sessionId = #{sessionId}
    </update>

    <select id="getTargetRemarkByKey" resultType="com.geeksec.analysis.entity.session.TargetRemarkInfo">
        select * from tb_target_remark where target_key = #{targetKey} and target_type = #{targetType}
    </select>


    <delete id="deleteTargetRemarkById">
        delete from tb_target_remark where id = #{remarkId}
    </delete>



</mapper>

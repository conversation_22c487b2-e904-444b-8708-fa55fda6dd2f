package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.TbTagAttribute;
import com.geeksec.analysis.entity.TbTagInfo;
import com.geeksec.analysis.entity.vo.TagLibraryVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-13
 */
public interface TagInfoDao extends BaseMapper<TbTagInfo> {

    /**
     * 初始化标签信息
     *
     * @return
     */
    List<TbTagInfo> getListForInit();

    /**
     * 根据标签id获取标签信息
     *
     * @param knowledgeId
     * @return
     */
    TbTagInfo getTagInfoById(@Param("knowledge_id") Integer knowledgeId);

    /**
     * 根据标签ID的集合查询对应的标签名称集合（日志导出用）
     */
    List<String> getTagTextByTagList(@Param("tag_id_list") List<String> tagList);

    /**
     * 根据标签id获取标签信息
     *
     * @param knowledgeId
     * @return
     */
    List<TbTagInfo> getListForInitModel();

    /**
     * 通过标签类型和名称查询全部标签返回信息
     *
     * @param searchName
     * @param SHIELD_PRO_TYPE
     * @return
     */
    List<TagLibraryVo> getAllTagListByTargetType(@Param("search_name") String searchName,
                                                 @Param("tag_target_type") Integer tagTargetType,
                                                 @Param("shield_pro_type") Integer sheidProType);

    /**
     * 通过标签细分类和名称查询全部标签返回信息
     *
     * @param attrbuteId
     * @param searchName
     * @param tagTargetType
     * @param SHIELD_PRO_TYPE
     * @return
     */
    List<TagLibraryVo> getAllTagListByAttributeId(@Param("search_name") String searchName,
                                                  @Param("tag_target_type") Integer tagTargetType,
                                                  @Param("attribute_id") Integer attributeId,
                                                  @Param("shield_pro_type") Integer sheidProType);

    /**
     * 查询以目标类型聚合的标签数量
     *
     * @param tagText
     * @return
     */
    List<Map<String, Object>> getTargetCountBySearchName(@Param("search_name") String searchName, @Param("startBlack") Integer startBlack,
                                                         @Param("endBlack") Integer endBlack,
                                                         @Param("startWhite") Integer startWhite,
                                                         @Param("endWhite") Integer endWhite,
                                                         @Param("shield_pro_type") Integer shiedProType,
                                                         @Param("userId") Integer userId);

    /**
     * 查询以标签细分类聚合的标签数量
     *
     * @param tagText
     * @param SHIELD_PRO_TYPE
     * @return
     */
    List<Map<String, Object>> getAttributeCountBySearchName(@Param("search_name") String searchName, @Param("startBlack") Integer startBlack,
                                                            @Param("endBlack") Integer endBlack,
                                                            @Param("startWhite") Integer startWhite,
                                                            @Param("endWhite") Integer endWhite,
                                                            @Param("shield_pro_type") Integer shieldProType);

    /**
     * 根据条件进行分页和权值选择查询
     *
     * @param offset
     * @param pageSize
     * @param searchName
     * @param tagTargetType
     * @param startBlack
     * @param endBlack
     * @param startWhite
     * @param endWhite
     * @param SHIELD_PRO_TYPE
     * @return
     */
    List<TbTagInfo> getTagListByCondition(@Param("searchName") String searchName,
                                             @Param("attributeId") Integer attributeId,
                                             @Param("startBlack") Integer startBlack,
                                             @Param("endBlack") Integer endBlack,
                                             @Param("startWhite") Integer startWhite,
                                             @Param("endWhite") Integer endWhite,
                                             @Param("shield_pro_type") Integer shieldProType);

    /**
     * 根据条件查询标签总数
     * @param searchName
     * @param tagTargetType
     * @param attributeId
     * @param startBlack
     * @param endBlack
     * @param startWhite
     * @param endWhite
     * @param shield_pro_type
     * @return
     */
    Integer getTagListCountByCondition(@Param("searchName") String searchName,
                                       @Param("tagTargetType") Integer tagTargetType,
                                       @Param("attributeId") Integer attributeId,
                                       @Param("startBlack") Integer startBlack,
                                       @Param("endBlack") Integer endBlack,
                                       @Param("startWhite") Integer startWhite,
                                       @Param("endWhite") Integer endWhite,
                                       @Param("shield_pro_type") Integer shieldProType);

    /**
     * 查询当前标签最大的tagID
     * @return
     */
    Integer getMaxTagId();


    List<TbTagInfo> queryList(@Param("searchName") String searchName,
                              @Param("startBlack") Integer startBlack,
                              @Param("endBlack") Integer endBlack,
                              @Param("startWhite") Integer startWhite,
                              @Param("endWhite") Integer endWhite,
                              @Param("tagTargetType") Integer tagTargetType,
                              @Param("shieldProType") Integer shieldProType,
                              @Param("userId") Integer userId);

    List<TbTagAttribute> listTagAttribute();

    /**
     * 通过标签集合返回标签对象集合
     */
    List<TbTagInfo> getTagInfoListByIds(@Param("tag_id_list") List<String> tagIdList);

}

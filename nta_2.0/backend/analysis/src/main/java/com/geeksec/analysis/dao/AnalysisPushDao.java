package com.geeksec.analysis.dao;

import com.geeksec.analysis.entity.condition.AddInternalNetCondition;
import com.geeksec.analysis.entity.condition.UpdateInternalNetCondition;
import com.geeksec.analysis.entity.vo.InternalNetVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Mapper
public interface AnalysisPushDao {
    /**
     * 获取网络配置信息
     * @return
     */
    @MapKey("mac")
    List<HashMap<String,Object>> selectNetWorkConfigList();

    /**
     * 根据mac信息获取网络配置信息
     */
    HashMap<String, Object> selectNetWorkConfigByMac(@Param("mac") String mac);

    /**
     * Mac信息对照表
     * @return
     */
    @MapKey("assignment")
    List<HashMap<String, Object>> selectMacInfoList();

    /**
     * 查询内网IP列表
     */
    List<InternalNetVo> getInternalNet(@Param("taskId")Integer taskId,@Param("orderField") String orderFiled, @Param("order") String sortOrder, @Param("limit") Integer limit, @Param("offset") Integer offset);

    /**
     * 添加内网IP
     */
    void addInternalInfo(@Param("condition") AddInternalNetCondition condition);

    /**
     * 查询内网IP列表总数
     * @return
     * @param taskId
     */
    Integer getInternalNetCount(@Param("taskId") Integer taskId);

    /**
     * 批量删除内网IP信息
     * @param ids
     */
    void batchDeleteInterInfo(@Param("ids") List<Integer> ids);

    /**
     * 更新内网IP信息
     * @param condition
     */
    void updateInterInfo(@Param("condition") UpdateInternalNetCondition condition);

    Integer existInternalIp(@Param("ip") String ip);
}

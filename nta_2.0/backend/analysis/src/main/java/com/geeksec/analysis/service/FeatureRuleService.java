package com.geeksec.analysis.service;

import cn.hutool.core.text.csv.CsvRow;
import com.geeksec.analysis.entity.condition.FeatureRuleCondition;
import com.geeksec.analysis.entity.condition.FeatureRuleSearchCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.vo.FeatureCsvVo;
import com.geeksec.analysis.entity.vo.FeatureRuleVo;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;

import java.util.List;


/**
 * 特征规则
 */
public interface FeatureRuleService {

    /**
     * 新增规则
     * @param condition
     * @return
     */
    ResultVo addFeatureRule(FeatureRuleCondition condition);

    /**
     * 删除，多选，或任务全部
     * @param condition
     * @return
     */
    Integer deleteFeatureRule(FilterDeleteCondition condition);

    /**
     * 更新规则
     * @param condition
     * @return
     */
    ResultVo updateFeatureRule(FeatureRuleCondition condition);

    /**
     * 规则列表
     * @param condition
     * @return
     */
    PageResultVo<FeatureRuleVo> getFeatureRules(FeatureRuleSearchCondition condition);

    FeatureRuleVo getFeatureRule(Long id);

    /**
     * 规则生效/失效
     * @param id
     * @param state
     * @return
     */
    Integer updateRuleState(Long id, String state);

    /**
     * 特征规则导入
     * @param rows
     * @param taskId
     * @return
     */
    ResultVo<FeatureCsvVo> csvImport(Integer taskId, List<CsvRow> rows);

    List<List<String>> getCsv(FilterDeleteCondition condition);
}

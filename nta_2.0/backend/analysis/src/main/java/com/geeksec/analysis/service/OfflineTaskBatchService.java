package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.OfflineTaskBatchQueryCondition;
import com.geeksec.analysis.entity.dto.OfflineTaskBatchCancelDto;
import com.geeksec.analysis.entity.dto.OfflineTaskBatchDto;
import com.geeksec.analysis.entity.vo.FileTreeNodeVo;
import com.geeksec.analysis.entity.vo.OfflineTaskBatchPageVo;
import com.geeksec.analysis.entity.vo.OfflineTaskBatchProgressVo;
import com.geeksec.analysis.entity.vo.PageVo;
import com.geeksec.entity.common.ResultVo;

import java.util.List;

/**
* 离线任务批次服务
*/
public interface OfflineTaskBatchService {

    /**
     * 根据任务id查询批次信息
     */
    PageVo<OfflineTaskBatchPageVo> pageBatch(OfflineTaskBatchQueryCondition condition);

    /**
     * 数据导入
     */
    ResultVo addBatch(OfflineTaskBatchDto dto);

    /**
     * 取消数据导入
     */
    ResultVo cancelBatch(OfflineTaskBatchCancelDto dto);

    /**
     * 查询服务器文件路径
     */
    List<FileTreeNodeVo> listServerPath(String directoryPath);

}

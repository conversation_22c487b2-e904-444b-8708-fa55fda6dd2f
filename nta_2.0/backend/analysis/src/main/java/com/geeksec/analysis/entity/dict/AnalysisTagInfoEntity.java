package com.geeksec.analysis.entity.dict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_tag_info")
public class AnalysisTagInfoEntity {


    private static final long serialVersionUID = 1L;

    /**
     * 标签ID //标签表
     */
    @TableId(value = "tag_id", type = IdType.AUTO)
    private Integer tagId;

    /**
     * 标签类型 //0删除，1生效
     */
    @TableField("tag_type")
    private Integer tagType;

    /**
     * 标签备注
     */
    @TableField("tag_remark")
    private String tagRemark;

    /**
     * 标签中文说明
     */
    @TableField("tag_explain")
    private String tagExplain;

    /**
     * 标签内容
     */
    @TableField("tag_text")
    private String tagText;

    /**
     * 标签数量
     */
    @TableField("tag_num")
    private Integer tagNum;

    /**
     * //0代表ip目标，1代表端口目标，2代表应用目标，3代表域名目标，4代表证书目标，5代表MAC目标，6代表连接目标 {目标类型} , 7 指纹 9999 所有
     */
    @TableField("tag_target_type")
    private Integer tagTargetType;

    /**
     * 默认黑名单
     */
    @TableField("default_black_list")
    private Integer defaultBlackList;

    /**
     * 默认白名单
     */
    @TableField("default_white_list")
    private Integer defaultWhiteList;

    /**
     * 黑名单
     */
    @TableField("black_list")
    private Integer blackList;

    /**
     * 白名单
     */
    @TableField("white_list")
    private Integer whiteList;

    /**
     * 添加时间
     */
    @TableField("created_time")
    private Integer createdTime;

    /**
     * 最后添加时间
     */
    @TableField("last_created_time")
    private Integer lastCreatedTime;

    /**
     * 0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作
     */
    @TableField("tag_family")
    private Integer tagFamily;
}

package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_knowledge_alarm")
public class KnowledgeAlarm implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * //告警id
     */
    @TableField("knowledge_alarm_id")
    private Integer knowledgeAlarmId;

    /**
     * //告警名称
     */
    @TableField("alarm_name")
    private String alarmName;

    /**
     * 攻击类型 1 非法接入          
     */
    @TableField("attack_type")
    private Integer attackType;

    /**
     * 可能关联的告警名称 , 逗号分割
     */
    @TableField("relation_tag_id")
    private String relationTagId;

    /**
     * 的告警名称 , 逗号分割
     */
    @TableField("exclude_tag_id")
    private String excludeTagId;

    /**
     * 黑名单权重
     */
    @TableField("black_list")
    private Integer blackList;

    /**
     * //备注 , 说明
     */
    @TableField("remark")
    private String remark;

    /**
     * //自增id  ---告警表---
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


}

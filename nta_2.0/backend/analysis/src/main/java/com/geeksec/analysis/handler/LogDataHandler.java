package com.geeksec.analysis.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：PB文件日志信息处理类
 */
public class LogDataHandler {
    private static Map<String, Object> macMap;

    // 私有化构造器，防止外部直接实例化
    private LogDataHandler(){}

    /**
     * 抽取对照连接的信息
     *
     * @param pbMap 原生产生的PB里的数据转成map
     * @return
     */
    public static List<Map<String, Object>> extractConnectMap(Map<String, Object> pbMap) {

        List<Map<String, Object>> connectList = new ArrayList<>();
        Map<String, Object> pktMap = pbMap.get("pkt") == null ? null : (Map<String, Object>) pbMap.get("pkt");

        // Mac
        String sMac = pbMap.get("sMac").toString();
        String dMap = pbMap.get("dMac").toString();
        Map<String, Object> macMap = new HashMap<>(3);
        macMap.put("client", sMac);
        macMap.put("server", dMap);
        macMap.put("type", "Mac");

        connectList.add(macMap);

        // IP
        String sIp = pbMap.get("sIp").toString();
        String dIp = pbMap.get("dIp").toString();
        Map<String, Object> ipMap = new HashMap<>(3);
        ipMap.put("client", sIp);
        ipMap.put("server", dIp);
        ipMap.put("type", "IP");

        connectList.add(ipMap);

        // 端口
        Object sPort = pbMap.get("sPort");
        Object dPort = pbMap.get("dPort");
        Map<String, Object> portMap = new HashMap<>(3);
        portMap.put("client", sPort);
        portMap.put("server", dPort);
        portMap.put("type", "Port");

        connectList.add(portMap);

        // 使用指纹
        String sSSLFinger = pbMap.get("sSSLFinger").toString();
        String dSSLFinger = pbMap.get("dSSLFinger").toString();
        Map<String, Object> sslFingerMap = new HashMap<>(3);
        sslFingerMap.put("client", sSSLFinger);
        sslFingerMap.put("server", dSSLFinger);
        sslFingerMap.put("type", "SSLFinger");

        connectList.add(sslFingerMap);

        // 所在国家
        String sIpCountry = pbMap.get("sIpCountry").toString() ;
        String dIpCountry = pbMap.get("dIpCountry").toString();
        Map<String, Object> countryMap = new HashMap<>(3);
        countryMap.put("client", sIpCountry);
        countryMap.put("server", dIpCountry);
        countryMap.put("type", "IpCountry");

        connectList.add(countryMap);

        // 所在省份
        Object sIpSubdivisions = pbMap.getOrDefault("sIpSubdivisions", "");
        Object dIpSubdivisions = pbMap.getOrDefault("dIpSubdivisions", "");

        Map<String, Object> subdivisionsMap = new HashMap<>(3);
        subdivisionsMap.put("client", sIpSubdivisions);
        subdivisionsMap.put("server", dIpSubdivisions);
        subdivisionsMap.put("type", "IpSubdivisions");

        connectList.add(subdivisionsMap);

        // 所在城市
        Object sIpCity = pbMap.getOrDefault("sIpCity", "");
        Object dIpCity = pbMap.getOrDefault("dIpCity", "");
        Map<String, Object> cityMap = new HashMap<>(3);
        cityMap.put("client", sIpCity);
        cityMap.put("server", dIpCity);
        cityMap.put("type", "IpCity");

        connectList.add(cityMap);

        // 发送原始TTL
        Object sInitialTTL = pbMap.get("sInitialTTL");
        Object dInitialTTL = pbMap.get("dInitialTTL");
        Map<String, Object> initialTTLMap = new HashMap<>(3);
        initialTTLMap.put("client", sInitialTTL);
        initialTTLMap.put("server", dInitialTTL);
        initialTTLMap.put("type", "InitialTTL");

        connectList.add(initialTTLMap);

        // 发送最大TTL
        Object sTTLMax = pktMap.get("sTTLMax");
        Object dTTLMax = pbMap.get("dTTLMax");
        Map<String, Object> ttlMaxMap = new HashMap<>(3);
        ttlMaxMap.put("client", sTTLMax);
        ttlMaxMap.put("server", dTTLMax);
        ttlMaxMap.put("type", "TTLMax");

        connectList.add(ttlMaxMap);

        // 发送最小TTL
        Object sTTLMin = pktMap.get("sTTLMin");
        Object dTTLMin = pktMap.get("dTTLMin");
        Map<String, Object> ttlMinMap = new HashMap<>(3) ;
        ttlMinMap.put("client", sTTLMin);
        ttlMinMap.put("server", dTTLMin);
        ttlMinMap.put("type", "TTLMin");
        connectList.add(ttlMinMap);

        // 发送TTL最大距离
        Object sMaxHopCount = pbMap.get("sMaxHopCount");
        Object dMaxHopCount = pbMap.get("dMaxHopCount");
        Map<String, Object> maxHopCountMap = new HashMap<>(3);
        maxHopCountMap.put("client", sMaxHopCount);
        maxHopCountMap.put("server", dMaxHopCount);
        maxHopCountMap.put("type", "MaxHopCount");

        connectList.add(maxHopCountMap);

        // 发送TTL最小距离
        Object sMinHopCount = pbMap.get("sMinHopCount");
        Object dMinHopCount = pbMap.get("dMinHopCount");
        Map<String, Object> minHopCountMap = new HashMap<>(3);
        minHopCountMap.put("client", sMinHopCount);
        minHopCountMap.put("server", dMinHopCount);
        minHopCountMap.put("type", "MinHopCount");

        connectList.add(minHopCountMap);

        // 包长分布
        List<Integer> sDistLen = (List<Integer>) pbMap.get("sDistLen");
        List<Integer> dDistLen = (List<Integer>) pbMap.get("dDistLen");
        Map<String, Object> distLenMap = new HashMap<>(3);
        distLenMap.put("client", sDistLen);
        distLenMap.put("server", dDistLen);
        distLenMap.put("type", "DistLen");

        connectList.add(distLenMap);

        // 发送最大包长
        Object sMaxLen = pktMap.get("sMaxLen");
        Object dMaxLen = pktMap.get("dMaxLen");
        Map<String, Object> maxLenMap = new HashMap<>(3);
        maxLenMap.put("client", sMaxLen);
        maxLenMap.put("server", dMaxLen);
        maxLenMap.put("type", "MaxLen");

        connectList.add(maxLenMap);

        // 发送FIN次数
        Object sFinNum = pktMap.get("sFinNum");
        Object dFinNum = pktMap.get("dFinNum");
        Map<String, Object> finNumMap = new HashMap<>(3);
        finNumMap.put("client", sFinNum);
        finNumMap.put("server", dFinNum);
        finNumMap.put("type", "FinNum");

        connectList.add(finNumMap);

        // 发送RST次数
        Object sRSTNum = pktMap.get("sRSTNum");
        Object dRSTNum = pktMap.get("dRSTNum");
        Map<String, Object> rstNumMap = new HashMap<>(3);
        rstNumMap.put("client", sRSTNum);
        rstNumMap.put("server", dRSTNum);
        rstNumMap.put("type", "RSTNum");

        connectList.add(rstNumMap);

        // 发送PSH次数
        Object sPSHNum = pktMap.get("sPSHNum");
        Object dPSHNum = pktMap.get("dPSHNum");
        Map<String, Object> pshNumMap = new HashMap<>(3);
        pshNumMap.put("client", sPSHNum);
        pshNumMap.put("server", dPSHNum);
        pshNumMap.put("type", "PSHNum");

        connectList.add(pshNumMap);

        // 发送字节数
        Object sBytes = pktMap.get("sBytes");
        Object dBytes = pktMap.get("dBytes");
        Map<String, Object> bytesMap = new HashMap<>(3);
        bytesMap.put("client", sBytes);
        bytesMap.put("server", dBytes);
        bytesMap.put("type", "Bytes");

        connectList.add(bytesMap);

        // 发送负载字节数
        Object sPayloadBytes = pktMap.get("sPayloadBytes");
        Object dPayloadBytes = pktMap.get("dPayloadBytes");
        Map<String, Object> payloadBytesMap = new HashMap<>(3);
        payloadBytesMap.put("client", sPayloadBytes);
        payloadBytesMap.put("server", dPayloadBytes);
        payloadBytesMap.put("type", "PayloadBytes");

        connectList.add(payloadBytesMap);

        // 发送IPID分布
        List<Integer> sIpIdOffset = (List<Integer>) pbMap.get("sIpIdOffset");
        List<Integer> dIpIdOffset = (List<Integer>) pbMap.get("dIpIdOffset");
        Map<String, Object> ipIdOffsetMap = new HashMap<>(3);
        ipIdOffsetMap.put("client", sIpIdOffset);
        ipIdOffsetMap.put("server", dIpIdOffset);
        ipIdOffsetMap.put("type", "IpIdOffset");

        connectList.add(ipIdOffsetMap);

        // 发送包数
        Object sNum = pktMap.get("sNum");
        Object dNum = pktMap.get("dNum");
        Map<String, Object> numMap = new HashMap<>(3);
        numMap.put("client", sNum);
        numMap.put("server", dNum);
        numMap.put("type", "Num");

        connectList.add(numMap);

        // 发送SYN次数
        Object sSYNNum = pktMap.get("sSYNNum");
        Object dSYNNum = pktMap.get("dSYNNum");
        Map<String, Object> synNumMap = new HashMap<>(3);
        synNumMap.put("client", sSYNNum);
        synNumMap.put("server", dSYNNum);
        synNumMap.put("type", "SYNNum");

        connectList.add(synNumMap);

        // 发送SYN包长
        Object sSYNBytes = pktMap.get("sSYNBytes");
        Object dSYNBytes = pktMap.get("dSYNBytes");
        Map<String, Object> synBytesMap = new HashMap<>(3);
        synBytesMap.put("client", sSYNBytes);
        synBytesMap.put("server", dSYNBytes);
        synBytesMap.put("type", "SYNBytes");

        connectList.add(synBytesMap);

        // 发送负载包数
        Object sPayloadNum = pktMap.get("sPayloadNum");
        Object dPayloadNum = pktMap.get("dPayloadNum");
        Map<String, Object> payloadNumMap = new HashMap<>(3);
        payloadNumMap.put("client", sPayloadNum);
        payloadNumMap.put("server", dPayloadNum);
        payloadNumMap.put("type", "PayloadNum");

        connectList.add(payloadNumMap);

        // 目的包数差异
        Object sbytesDividBytes = pbMap.get("SbytesDiviDbytes");
        Map<String, Object> sbytesDividBytesMap = new HashMap<String, Object>(2);
        sbytesDividBytesMap.put("client", sbytesDividBytes);
        sbytesDividBytesMap.put("type", "SbytesDiviDbytes");
        connectList.add(sbytesDividBytesMap);


        return connectList;
    }
}

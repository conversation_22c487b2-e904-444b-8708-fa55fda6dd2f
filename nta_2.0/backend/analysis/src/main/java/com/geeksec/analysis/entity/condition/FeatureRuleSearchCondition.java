package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FeatureRuleSearchCondition extends BaseCondition {

    @JsonProperty("task_id")
    private Integer taskId;

    @JsonProperty("rule_id")
    private Integer ruleId;

    @JsonProperty("rule_name")
    private String ruleName;
    //规则过滤 展示  查询使用  逗号分割 1:ip规则 2:协议规则 3:特征规则 4:正则规则 5:域名规则
    @JsonProperty("rule_type_filter")
    private List<String> ruleTypeFilter;

    @JsonProperty("rule_state")
    private String ruleState;

    @JsonProperty("rule_data")
    private String ruleData;

    @JsonProperty("order_field")
    @ApiModelProperty("排序字段，可填入created_time,total_sum_bytes,last_size_time,rule_state")
    private String orderField="created_time";
}

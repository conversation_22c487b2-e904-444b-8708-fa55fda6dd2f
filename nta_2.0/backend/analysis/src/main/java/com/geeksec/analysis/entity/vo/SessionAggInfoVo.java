package com.geeksec.analysis.entity.vo;

import lombok.Data;

@Data
public class SessionAggInfoVo {

    //客户端IP、服务端IP、服务端端口、应用协议、客户端MAC、客户端IP所在国家、服务端MAC、服务端IP所在国家
    //客户端=s、源   服务端=d、目标
    //再处理聚合的时候  也按照以下顺序

    private String sIp;

    private String dIp;

    private Integer dPort;

    private String appName;

    private String sMac;

    private String sIpCountry;

    private String dMac;

    private String dIpCountry;


    //后3个字段 是sum    是子聚合
    private Long sessionCount;
    //s是发送 d是接收     负载数量~取的字节数这个字段
    private Long sPayloadByteSum;
    private Long dPayloadByteSum;
}

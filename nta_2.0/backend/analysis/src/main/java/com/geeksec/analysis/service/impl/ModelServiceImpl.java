package com.geeksec.analysis.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.condition.ModelCondition;
import com.geeksec.analysis.dao.ModelInfoDao;
import com.geeksec.analysis.entity.TbModelInfo;
import com.geeksec.analysis.service.ModelService;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class ModelServiceImpl implements ModelService {

    private static final Logger logger = LoggerFactory.getLogger(ModelServiceImpl.class);

    @Autowired
    private ModelInfoDao modelInfoDao;

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String MODEL_SWITCH_TOPIC = "model_switch";

    @Override
    public HashMap<String, Object> getModelInfolList(ModelCondition condition) {

        HashMap<String, Object> resultMap = new HashMap<>();

        Integer limit = condition.getPageSize();
        Integer currentPage = condition.getCurrentPage();
        Integer offset = (currentPage - 1) * limit;

        try {
            if (StringUtils.isNotBlank(condition.getModelName())) {
                String modelName = condition.getModelName();
                // 去掉前后空格
                condition.setModelName(modelName.trim());
            }
            List<TbModelInfo> tbModelInfoList = modelInfoDao.getList(condition, limit, offset);
            QueryWrapper<TbModelInfo> queryWrapper = new QueryWrapper<TbModelInfo>();
            queryWrapper.like("model_name", condition.getModelName());
            Integer total = Math.toIntExact(modelInfoDao.selectCount(queryWrapper));
            resultMap.put("data", tbModelInfoList);
            resultMap.put("count", total);
            return resultMap;
        } catch (Exception e) {
            logger.error("查询模型列表失败！condition --->{},error-->", condition, e);
        }
        return new HashMap<>();
    }

    @Override
    public Boolean updateModelState(Integer modelId, Integer state) {

        try {
            int result = modelInfoDao.updateModelState(modelId, state);
            if (result > 0) {
                logger.info("修改模型状态成功，modelId--->{},state--->{}", modelId, state);
                // 给Flink的Kafka那边传送一条message
                JSONObject switchJson = new JSONObject();
                switchJson.put(modelId.toString(), state);
                String jsonStr = JSONObject.toJSONString(switchJson);
                // 发送消息
                ListenableFuture<SendResult<String, Object>> future = kafkaTemplate.send(MODEL_SWITCH_TOPIC, "switch_change", jsonStr);
                future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
                    @Override
                    public void onFailure(Throwable throwable) {
                        logger.warn("模型开关kafka消息推送失败，modelId--->{},state--->{}", modelId, state);
                        throw new GkException(GkErrorEnum.MODEL_SWITCH_ERROR);
                    }

                    @Override
                    public void onSuccess(SendResult<String, Object> stringObjectSendResult) {    //成功消费
                        logger.info("模型开关kafka消息推送成功，modelId--->{},state--->{}", modelId, state);
                    }
                });
                return true;
            } else {
                logger.info("未修改到对应模型状态，modelId--->{},state--->{}", modelId, state);
                return false;
            }
        } catch (Exception e) {
            logger.error("修改模型状态失败，modelId--->{},state--->{}", modelId, state);
        }
        return false;
    }
}

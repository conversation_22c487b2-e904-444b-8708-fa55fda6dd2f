package com.geeksec.analysis.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.dao.TagInfoDao;
import com.geeksec.analysis.entity.TbTagInfo;
import com.geeksec.analysis.entity.condition.*;
import com.geeksec.analysis.entity.vo.LabelAggVo;
import com.geeksec.analysis.entity.vo.RangeSessionVo;
import com.geeksec.analysis.entity.vo.SessionAggInfoVo;
import com.geeksec.analysis.service.AggrTargetService;
import com.geeksec.analysis.service.MetadataService;
import com.geeksec.analysis.utils.ESQueryUtil;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.*;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.lmdb.LmdbService;
import com.geeksec.util.IpUtils;
import com.geeksec.util.RedisUtil;
import com.geeksec.util.TimeUtil;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.range.ParsedRange;
import org.elasticsearch.search.aggregations.bucket.range.Range;
import org.elasticsearch.search.aggregations.bucket.range.RangeAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.ParsedSum;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
@DS("nta-db")
public class MetadataServiceImpl implements MetadataService {

    private static final Map<String, AppTypeInfo> APP_TYPE_MAP = new HashMap<>();

    static {
        APP_TYPE_MAP.put("SSL", new AppTypeInfo("APP_SSL", "ssl*"));
        APP_TYPE_MAP.put("HTTP", new AppTypeInfo("APP_HTTP", "http*"));
        APP_TYPE_MAP.put("DNS", new AppTypeInfo("APP_DNS", "dns*"));
        APP_TYPE_MAP.put("SSH", new AppTypeInfo("APP_SSH", "ssh*"));
        APP_TYPE_MAP.put("Telnet", new AppTypeInfo("APP_Telnet", "telnet*"));
        APP_TYPE_MAP.put("Rlogin", new AppTypeInfo("APP_Rlogin", "rlogin*"));
        APP_TYPE_MAP.put("VNC", new AppTypeInfo("APP_VNC", "vnc*"));
        APP_TYPE_MAP.put("RDP", new AppTypeInfo("APP_RDP", "rdp*"));
        APP_TYPE_MAP.put("XDMCP", new AppTypeInfo("APP_XDMCP", "xdmcp*"));
    }

    @Data
    @AllArgsConstructor
    private static class AppTypeInfo {
        private String appName;
        private String index;
    }

    @Value("${elasticsearch.es_connect_index}")
    private String esConnectIndex;

    @Value("${query.es_limit}")
    private Integer esLimit;

    @Autowired
    private EsearchService esearchService;

    @Autowired
    private AggrTargetService aggrTargetService;

    @Autowired
    private TagInfoDao tagInfoDao;

    @Autowired
    private LmdbService lmdbService;

    /**
     * 通过查询对应的10000条sessionId，到SSL元数据索引中查询数据，标签页总量上应显示元数据总条目数
     *
     * @param condition
     * @return
     */
    @Override
    public ResultVo getMetadataList(MetadataCondition condition) {
        log.info("会话分析-元数据查询，condition={}", condition);

        // 参数校验
        ResultVo errorVo = checkParam(condition);
        if (errorVo != null) {
            return errorVo;
        }

        // 获取应用类型信息
        AppTypeInfo appTypeInfo = APP_TYPE_MAP.get(condition.getDataKey());
        if (appTypeInfo == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        // 获取索引并检查数据
        List<String> indexNames = queryFirstIndexName(condition);
        if (CollectionUtils.isEmpty(indexNames)) {
            log.info("当前时间范围内无数据内容");
            return ResultVo.success(new PageResultVo<>());
        }

        PageResultVo pageResultVo = new PageResultVo<>();
        pageResultVo.setTotal(0);

        try {
            // 元数据查询
            Map<String, BoolQueryBuilder> mateDataMap = ESQueryUtil.mateDataQuery(condition);
            List<String> sessionIdsByMateData = esearchService.getSessionIdsByMateDataQuery(mateDataMap);
            if (sessionIdsByMateData != null && sessionIdsByMateData.isEmpty()) {
                return ResultVo.success(pageResultVo);
            }

            // 构建基础查询条件
            BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
            if (sessionIdsByMateData != null && !sessionIdsByMateData.isEmpty()) {
                queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateData));
            }

            // 处理标签查询条件
            queryBuilder = aggrTargetService.getTargetTagCondition(condition, condition.getDataKey(), queryBuilder);
            if (queryBuilder == null) {
                return ResultVo.success(pageResultVo);
            }
            queryBuilder.must(QueryBuilders.termQuery("AppName", appTypeInfo.getAppName()));

            // 查询元数据数量
            List<String> metaDataIndexs = queryMetaDataIndexName(condition);
            SearchSourceBuilder countSourceBuilder = new SearchSourceBuilder();
            countSourceBuilder.query(queryBuilder);
            String[] indexArray = indexNames.toArray(new String[0]);
            CountRequest countRequest = new CountRequest(indexArray, countSourceBuilder);
            CountResponse countResponse = esearchService.esSearchForCount(countRequest);
            long sessionCnt = countResponse.getCount();

            // 构建会话查询条件
            BoolQueryBuilder sessionQueryBuilder = ESQueryUtil.query(condition);
            if (sessionIdsByMateData != null && !sessionIdsByMateData.isEmpty()) {
                sessionQueryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateData));
            }
            sessionQueryBuilder = aggrTargetService.getTargetTagCondition(condition, "ALL", sessionQueryBuilder);
            if (sessionQueryBuilder == null) {
                return ResultVo.success(pageResultVo);
            }
            sessionQueryBuilder.must(QueryBuilders.termQuery("AppName", appTypeInfo.getAppName()));

            // 获取会话IDs
            List<String> sessionIds = getSessionIdForList(sessionQueryBuilder, indexNames);
            if (sessionIds.isEmpty()) {
                return ResultVo.success(pageResultVo);
            }

            // 执行搜索
            SearchResponse searchResponse = delEs(
                    sessionIds,
                    condition.getCurrentPage(),
                    condition.getPageSize(),
                    appTypeInfo.getIndex(),
                    metaDataIndexs,
                    condition.getOrderField(),
                    condition.getAsc()
            );

            // 处理结果
            List<JSONObject> resultList = new ArrayList<>();
            SearchHit[] hits = searchResponse.getHits().getHits();
            Long total = searchResponse.getHits().getTotalHits();

            for (SearchHit hit : hits) {
                Map<String, Object> result = hit.getSourceAsMap();
                result.put("es_index", hit.getIndex());
                resultList.add(new JSONObject(result));
            }

            pageResultVo.setRecords(resultList);
            pageResultVo.setTotal(total == 0 ? 0 : Math.min(sessionCnt, total));

            return ResultVo.success(pageResultVo);

        } catch (Exception e) {
            log.error("查询元数据失败", e);
            throw new GkException(GkErrorEnum.PROTOCOL_METADATA_QUERY_ERROR);
        }
    }

    /**
     * 只处理会话Hkey的转义查询
     *
     * @param condition
     * @return
     */
    private BoolQueryBuilder handleHkeyQuery(BoolQueryBuilder boolQueryBuilder, MetadataCondition condition) {
        List<AnalysisBaseCondition.QueryOb> query = condition.getQuery();
        String dataKey = condition.getDataKey();
        //可能会有元数据的查询，收集元数据的查询参数   最终获得一组 SessionIds
        for (AnalysisBaseCondition.QueryOb queryOb : query) {
            List<String> queryHkeyList = new ArrayList<>();
            String boolSearch = queryOb.getBoolSearch();
            List<AnalysisBaseCondition.SearchInfo> search = queryOb.getSearch();
            for (AnalysisBaseCondition.SearchInfo searchInfo : search) {
                String target = searchInfo.getTarget();
                if (!"Hkey".equals(target)) {
                    continue;
                }
                List<String> hkeylist = searchInfo.getVal();
                for (String Hkey : hkeylist) {
                    if (Hkey.startsWith("connectinfo")) {
                        queryHkeyList.add(queryMetadataHkey(Hkey, dataKey));

                    } else {
                        queryHkeyList.add(Hkey);
                    }
                }

            }
            BoolQueryBuilder shouldBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("Hkey", queryHkeyList));

        }

        return boolQueryBuilder;
    }

    /**
     * 通过会话信息的Hkey去查询对应的SessionId，拿到元数据信息的Hkey进行查询
     *
     * @param hkey
     * @param dataKey
     * @return
     */
    private String queryMetadataHkey(String hkey, String dataKey) {
        String metaKey = StringUtils.EMPTY;

        Map<String, Object> esMap = new HashMap<>();
        esMap.put("Hkey", hkey);
        Map<String, Object> pbMap = lmdbService.hLmdbHandle(esMap, hkey);
        String sessionId = (String) pbMap.get("SessionId");
        SearchRequest searchRequest = new SearchRequest();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("SessionId", sessionId));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder).size(10);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(dataKey.toLowerCase() + "_*");
        try {
            SearchResponse response = esearchService.esSearch(searchRequest);
            SearchHit[] hits = response.getHits().getHits();
            if (hits.length != 0) {
                Map<String, Object> hitMap = hits[0].getSourceAsMap();
                metaKey = (String) hitMap.get("Hkey");
            } else {
                return StringUtils.EMPTY;
            }
        } catch (Exception e) {
            log.error("通过会话Hkey转义查询元数据Hkey失败！connect hkey :{},dataKey:{}", hkey, dataKey);
        }
        return metaKey;
    }

    private SearchResponse delEs(List<String> sessionIds, Integer page, Integer limit, String index, List<String> metaDataIndexs, String orderFiled, Boolean asc) {

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIds));
        Integer from = (page - 1) * limit;
        Integer size = limit;
        if (size == -1) {
            // 若size == -1则为日志导出做准备
            size = 10000;
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(size);
        searchSourceBuilder.from(from);
        searchSourceBuilder.query(boolQueryBuilder);
        //设置排序
        if (StringUtils.isNotEmpty(orderFiled)) {
            SortOrder sortOrder = null;
            if (asc) {
                sortOrder = SortOrder.ASC;
            } else {
                sortOrder = SortOrder.DESC;
            }
            searchSourceBuilder.sort(orderFiled, sortOrder);
        }

        //保证同一会话的数据被连续查出
        searchSourceBuilder.sort("SessionId", SortOrder.ASC);
        String[] indexArr = metaDataIndexs.toArray(new String[metaDataIndexs.size()]);
        SearchRequest searchRequest = new SearchRequest(indexArr, searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        //这里判断返回值 esResultMapList
        return searchResponse;
    }

    private ResultVo checkParam(MetadataCondition condition) {
        String dataKey = condition.getDataKey();
        if (StringUtil.isEmpty(dataKey) && !(("SSL").equals(dataKey) || ("HTTP").equals(dataKey) || ("DNS").equals(dataKey))) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        List<Integer> taskId = condition.getTaskId();
        if (taskId == null || taskId.size() < 1) {
            taskId = new ArrayList<>();
            taskId.add(0);
            //默认0对象查询
            condition.setTaskId(taskId);
        }
        Integer limit = condition.getPageSize();
        Integer page = condition.getCurrentPage();
        if (limit == null || limit < 1) {
            condition.setCurrentPage(1);
        }
        if (page == null || page < 1) {
            condition.setPageSize(10);
        }
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }
        Long left = timeRange.getLeft();
        Long right = timeRange.getRight();
        if (left == null || left < 0L) timeRange.setLeft(-1L);
        if (right == null || right < 0L) timeRange.setRight(-1L);
        return null;
    }

    private ResultVo checkParam(AnalysisBaseCondition condition) {
        // 判断condition中的query字段，有无条件target中的查询，判断其是否满足格式
        List<AnalysisBaseCondition.QueryOb> queryList = condition.getQuery();
        for (AnalysisBaseCondition.QueryOb queryOb : queryList) {
            List<AnalysisBaseCondition.SearchInfo> search = queryOb.getSearch();
            for (AnalysisBaseCondition.SearchInfo searchInfo : search) {
                String target = searchInfo.getTarget();
                if ("IP".equals(target)) {
                    List<String> values = searchInfo.getVal();
                    for (String value : values) {
                        if (IpUtils.isIpv4Str(value)) {
                            continue;
                        }
                        if (IpUtils.isIpv6Str(value)) {
                            continue;
                        }
                        return ResultVo.fail("IP地址格式错误，请检查");
                    }
                }
            }
        }


        List<Integer> taskId = condition.getTaskId();
        if (taskId == null || taskId.isEmpty()) {
            taskId = Collections.singletonList(0);
            // 默认0对象查询
            condition.setTaskId(taskId);
        }

        Integer limit = condition.getPageSize();
        Integer page = condition.getCurrentPage();
        if (limit == null || limit < 1) {
            condition.setPageSize(10);
        }

        if (page == null || page < 1) {
            condition.setCurrentPage(1);
        }

        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }

        long left = timeRange.getLeft() != null && timeRange.getLeft() >= 0L ? timeRange.getLeft() : -1L;
        timeRange.setLeft(left);

        long right = timeRange.getRight() != null && timeRange.getRight() >= 0L ? timeRange.getRight() : -1L;
        timeRange.setRight(right);

        return null;
    }


    /**
     * es_index下获取索引集
     *
     * @param condition
     * @return
     */
    private List<String> queryFirstIndexName(AnalysisBaseCondition condition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest("es_index");
        boolQueryBuilder.must(QueryBuilders.wildcardQuery("index.keyword", esConnectIndex));
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }
        //es_index的时间范围特殊
        ESQueryUtil.delEsIndexTime(boolQueryBuilder, timeRange.getLeft(), timeRange.getRight());
        List<Integer> taskId = condition.getTaskId();
        if (taskId != null && taskId.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("task", taskId));
        }
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        List<Map<String, Object>> esResultMapList = esearchService.normalSearch(searchRequest);
        //这里判断返回值 esResultMapList

        Map<String, String> map = new HashMap<>();
        List<String> indexNames = new ArrayList<>();
        if (esResultMapList.size() > 1000) {
            for (Map<String, Object> esResultMap : esResultMapList) {
                String index = esResultMap.get("index").toString();
                String[] sliV = index.split("_");
                map.put(sliV[0] + "_" + sliV[1] + "_*", "");
            }
            Set<String> keySet = map.keySet();
            indexNames = new ArrayList<>(keySet);
            return indexNames;
        }
        for (Map<String, Object> esResultMap : esResultMapList) {
            String index = esResultMap.get("index").toString();
            indexNames.add(index);
        }
        return indexNames;
    }

    /**
     * 获取SessionId  聚合使用 聚合上线等情况不一样
     *
     * @param boolQueryBuilder
     * @param indexNames
     */
    private List<String> getSessionIdForAggs(BoolQueryBuilder boolQueryBuilder, List<String> indexNames) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //searchSourceBuilder.fetchSource("SessionId",null);
        searchSourceBuilder.query(boolQueryBuilder);
        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        countRequest.source(searchSourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        Integer limit = esLimit;
        if (count < esLimit) {
            //设置循环参数
            limit = (int) count;
        }
        ResultVo<List<String>> resultVoIds = esearchService.getEsIds(10000, limit, boolQueryBuilder, indexNames, "CreateTime", "SessionId");
        List<String> sessionIds = resultVoIds.getData();
        return sessionIds;
//        searchSourceBuilder.size(esLimit);
//        searchSourceBuilder.sort("CreateTime", SortOrder.DESC);
//        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()]),searchSourceBuilder);
//        List<Map<String, Object>> esResultMapList = esearchService.normalSearch(searchRequest);
//        //这里判断返回值 esResultMapList
//
//        List<String> ids = Lists.newArrayListWithCapacity(esResultMapList.size());
//        for (Map<String, Object> esResultMap : esResultMapList) {
//            String sessionId = esResultMap.get("SessionId").toString();
//            ids.add(sessionId);
//        }
//        return ids;
    }

    /**
     * 获取SessionId  列表使用 只需1W条 (1万SessionId至少会查出1万个对应数据)
     *
     * @param boolQueryBuilder
     * @param indexNames
     * @return
     */
    private List<String> getSessionIdForList(BoolQueryBuilder boolQueryBuilder, List<String> indexNames) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from(0);
        searchSourceBuilder.fetchSource("SessionId", null);
        // 一次滚动查询最多获取10000条
        searchSourceBuilder.size(10000);
        searchSourceBuilder.sort("StartTime", SortOrder.DESC);
        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()]), searchSourceBuilder);

        long l1 = System.currentTimeMillis();
        List<Map<String, Object>> esResultMapList = esearchService.normalSearch(searchRequest);
        long l2 = System.currentTimeMillis();
        log.info("常规查询耗时:{}", l2 - l1);

        //这里判断返回值 esResultMapList
        List<String> sessionIds = Lists.newArrayListWithCapacity(esResultMapList.size());
        for (Map<String, Object> esResultMap : esResultMapList) {
            String sessionId = esResultMap.get("SessionId").toString();
            sessionIds.add(sessionId);
        }
        return sessionIds;
    }

    @Override
    public ResultVo<LabelAggVo> getLabelsInfoList(AnalysisBaseCondition condition) {
        log.info("会话分析-标签聚合 列表，condition={}", condition);
        //1.参数check
        ResultVo erroVo = checkParam(condition);
        if (erroVo != null) {
            return erroVo;
        }
        LabelAggVo labelAggVo = new LabelAggVo();
        labelAggVo.setTotal(0);
        //2. 获取索引集合
        List<String> indexNames = queryFirstIndexName(condition);
        if (indexNames == null || indexNames.size() < 1) {
            return ResultVo.success(labelAggVo);
        }
        //3.query
        //先判断元数据查询
        Map<String, BoolQueryBuilder> map = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(map);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() < 1) {
            return ResultVo.success(labelAggVo);
        }
        BoolQueryBuilder boolQueryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() > 0) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        boolQueryBuilder = aggrTargetService.getTargetTagCondition(condition, "ALL", boolQueryBuilder);
        if (boolQueryBuilder == null) {
            return ResultVo.success(labelAggVo);
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(0);
        searchSourceBuilder.fetchSource(false);

        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        //标签聚合
        TermsAggregationBuilder labelAggBuild = AggregationBuilders.terms("labelAgg").size(1000).field("Labels");


        searchSourceBuilder.aggregation(labelAggBuild);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        Aggregations aggregations = searchResponse.getAggregations();

        ParsedLongTerms labelAgg = aggregations.get("labelAgg");
        List<? extends Terms.Bucket> buckets = labelAgg.getBuckets();
        List<LabelAggVo.LabelInfoVo> list = Lists.newArrayListWithCapacity(buckets.size());
        for (Terms.Bucket bucket : buckets) {
            LabelAggVo.LabelInfoVo aggVo = new LabelAggVo.LabelInfoVo();
            aggVo.setTagId(Integer.valueOf(bucket.getKeyAsString()));
            aggVo.setCnt(bucket.getDocCount());
            TbTagInfo tagInfoForCache = getTagInfoForCache(aggVo.getTagId());
            aggVo.setTagText(tagInfoForCache.getTagText());
            Integer blackList = tagInfoForCache.getBlackList();
            if (blackList == null) {
                aggVo.setBlackList(0);
            } else {
                aggVo.setBlackList(tagInfoForCache.getBlackList());
            }
            Integer whiteList = tagInfoForCache.getWhiteList();
            if (whiteList == null) {
                aggVo.setWhiteList(0);
            } else {
                aggVo.setWhiteList(tagInfoForCache.getWhiteList());
            }
            list.add(aggVo);
        }

        //取分页
        Integer page = condition.getCurrentPage();
        Integer limit = condition.getPageSize();
        //排序黑名单
        List<LabelAggVo.LabelInfoVo> list1 = list.stream().sorted(Comparator.comparing(LabelAggVo.LabelInfoVo::getBlackList).reversed()).collect(Collectors.toList());
        labelAggVo.setBlackOrderDesc(PageResultVo.pageList(list1, page, limit));
        //排序黑名单
        List<LabelAggVo.LabelInfoVo> list2 = list.stream().sorted(Comparator.comparing(LabelAggVo.LabelInfoVo::getBlackList)).collect(Collectors.toList());
        labelAggVo.setBlackOrderAsc(PageResultVo.pageList(list2, page, limit));

        //排序会话数量
        List<LabelAggVo.LabelInfoVo> list3 = list.stream().sorted(Comparator.comparing(LabelAggVo.LabelInfoVo::getCnt).reversed()).collect(Collectors.toList());
        labelAggVo.setCntOrderDesc(PageResultVo.pageList(list3, page, limit));
        //排序会话数量
        List<LabelAggVo.LabelInfoVo> list4 = list.stream().sorted(Comparator.comparing(LabelAggVo.LabelInfoVo::getCnt)).collect(Collectors.toList());
        labelAggVo.setCntOrderAsc(PageResultVo.pageList(list4, page, limit));
        labelAggVo.setTotal(list.size());
        return ResultVo.success(labelAggVo);
    }

    private Map<Integer, TbTagInfo> tagsCacheMap;

    @Override
    @PostConstruct
    public void initTagCache() {
        long t1 = System.currentTimeMillis();
        List<TbTagInfo> listForInit = tagInfoDao.getListForInit();
        tagsCacheMap = new HashMap<>(listForInit.size());
        for (TbTagInfo tbTagInfo : listForInit) {
            tagsCacheMap.put(tbTagInfo.getTagId(), tbTagInfo);
        }
        List<TbTagInfo> listForInitModel = tagInfoDao.getListForInitModel();
        for (TbTagInfo tbTagInfo : listForInitModel) {
            tagsCacheMap.put(tbTagInfo.getTagId(), tbTagInfo);
        }
    }

    @Override
    public TbTagInfo getTagInfoForCache(Integer tagId) {
        TbTagInfo tbTagInfo = tagsCacheMap.get(tagId);
        if (tbTagInfo != null) {
            return tbTagInfo;
        }
        //重新初始化
        initTagCache();
        tbTagInfo = tagsCacheMap.get(tagId);
        if (tbTagInfo != null) {
            return tbTagInfo;
        }
        //重新初始化还是拿不到
        return new TbTagInfo();
    }

    @Override
    public ResultVo<List<RangeSessionVo>> getRangeSessionList(RangeSessionCondition condition) {
        log.info("会话分析-时间分段聚合，condition={}", condition);
        ResultVo errorVo = checkParam(condition);
        if (errorVo != null) return errorVo;
        //参数校验
        List<RangeSessionVo> resultList = new ArrayList<>();
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        Long left = 0L;
        Long right = 0L;
        if (timeRange == null || timeRange.getLeft() <= 0 || timeRange.getRight() <= 0 || timeRange.getLeft() >= timeRange.getRight()) {
            //时间范围没有选择，默认返回7天的数据
            long currentTime = System.currentTimeMillis() / 1000;
            assert timeRange != null;
            timeRange.setRight(currentTime);
            Long beforeTime = currentTime - 60 * 60 * 24 * 7;
            timeRange.setLeft(beforeTime);
        }
        left = timeRange.getLeft();
        right = timeRange.getRight();
        Integer segmentNum = condition.getSegmentNum();
        //无法切分的格局   *2 是为了有余地 给出S1-S2的中间时间。
        if ((right - left) < (segmentNum * 2)) {
            return ResultVo.success(resultList);
        }
        //获取索引集合
        List<String> indexNames = queryFirstIndexName(condition);
        if (indexNames.isEmpty()) {
            return ResultVo.success(resultList);
        }
        //先判断元数据查询
        Map<String, BoolQueryBuilder> map1 = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(map1);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.isEmpty()) {
            return ResultVo.success(resultList);
        }

        //获取Range查询对象
        List<RangeSessionVo> rangeSessionVos = delTimeGroup(condition);
        //获取公共query
        BoolQueryBuilder query = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && !sessionIdsByMateDataQuery.isEmpty())
            query.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));

        RangeAggregationBuilder timeRangeAgg = AggregationBuilders.range("time_range").field("StartTime");

        Map<String, RangeSessionVo> map = new HashMap<>();
        for (RangeSessionVo rangeSessionVo : rangeSessionVos) {
            timeRangeAgg.addRange(rangeSessionVo.getRangeKey(), rangeSessionVo.getStartTime(), rangeSessionVo.getEndTime());
            map.put(rangeSessionVo.getRangeKey(), rangeSessionVo);
        }
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(query);
        searchSourceBuilder.size(0);
        searchSourceBuilder.aggregation(timeRangeAgg).size(rangeSessionVos.size());
        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()]));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);

        Aggregations aggregations = searchResponse.getAggregations();

        ParsedRange timeRangeParsed = aggregations.get("time_range");
        List<? extends Range.Bucket> buckets = timeRangeParsed.getBuckets();
        for (Range.Bucket bucket : buckets) {
            String key = bucket.getKeyAsString();
            RangeSessionVo rangeSessionVo = map.get(key);
            rangeSessionVo.setCnt(bucket.getDocCount());
            resultList.add(rangeSessionVo);
        }
        return ResultVo.success(resultList);
    }

    /**
     * 处理按时间分组的 集合
     *
     * @param condition
     */
    private List<RangeSessionVo> delTimeGroup(RangeSessionCondition condition) {
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        Long left = timeRange.getLeft();
        Long right = timeRange.getRight();
        Integer segmentNum = condition.getSegmentNum();

        //获取一个单位的值
        for (int i = 0; i < segmentNum; i++) {
            if ((right - left) % segmentNum == 0) {
                break;
            }
            //右边时间+1 直到 能整除
            right += 1L;
        }
        //获取增加的单位值
        long avgUnit = (right - left) / segmentNum;
        //相当于 增加的单位值   加 一半，等于  S1-S2的中间值作用
        long middlePoint = avgUnit / 2;
        List<RangeSessionVo> list = new ArrayList<>();
        int i = 0;
        while (true) {
            i++;
            RangeSessionVo rangeSessionVo = new RangeSessionVo();
            String startStr = TimeUtil.getNormalTime(left * 1000L);
            rangeSessionVo.setMiddleKey(TimeUtil.getNormalTime((left + middlePoint) * 1000L));
            rangeSessionVo.setStartTime(left);
            left += avgUnit;
            String endStr = TimeUtil.getNormalTime(left * 1000L);
            rangeSessionVo.setRangeKey(startStr + "-" + endStr);
            rangeSessionVo.setEndTime(left);
            list.add(rangeSessionVo);

            if (left.equals(right)) break;
            //跳出循环的保险
            if (i == segmentNum) break;
        }
        return list;
    }

    @Override
    public ResultVo<PageResultVo<SessionAggInfoVo>> getSessionAggList(SessionAggInfoCondition condition) {

        // 此处应该做redis缓存查询，否则聚合起来压力过大
        String rediskey = "session_aggr_list:" + ESQueryUtil.getRedisKey2(condition);
        try {
            String redisJson = RedisUtil.get(rediskey);
            if (redisJson != null) {
                List<SessionAggInfoVo> list = JSONObject.parseArray(redisJson, SessionAggInfoVo.class);
                log.info("五元组碰撞聚合查询，redis查询成功，数组长度", rediskey);
                Integer page = condition.getCurrentPage();
                Integer limit = condition.getPageSize();
                PageResultVo<SessionAggInfoVo> pageResultVo = new PageResultVo<>();
                pageResultVo.setRecords(PageResultVo.pageList(list, page, limit));
                pageResultVo.setTotal(list.size());
                RedisUtil.expire(rediskey, 10);
                return ResultVo.success(pageResultVo);
            }
        } catch (Exception e) {
            log.error("五元组碰撞聚合查询，redis查询异常", e);
            throw new GkException(GkErrorEnum.REDIS_QUERY_ERROR);
        }

        log.info("会话分析列表：聚合，condition={}", condition);
        ResultVo errorVo = checkParam(condition);
        if (errorVo != null) return errorVo;
        long t1 = System.currentTimeMillis();
        List<String> fieldNames = condition.getFieldNames();
        if (fieldNames == null || fieldNames.size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        for (String fieldName : fieldNames) {
            if (!SessionAggEnum.checkField(fieldName)) {
                //判断字段的准确性
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
            }
        }
        List<String> indexNames = queryFirstIndexName(condition);

        PageResultVo pageResultVo = new PageResultVo();
        pageResultVo.setRecords(new ArrayList());
        pageResultVo.setTotal(0);

        if (indexNames == null || indexNames.size() < 1) {
            return ResultVo.success(pageResultVo);
        }
        //先判断元数据查询
        Map<String, BoolQueryBuilder> map = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(map);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() < 1) {
            return ResultVo.success(pageResultVo);
        }
        //配置基本的查询条件
        BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() > 0)
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 处理标签的查询条件
        queryBuilder = aggrTargetService.getTargetTagCondition(condition, "ALL", queryBuilder);
        if (queryBuilder == null) {
            return ResultVo.success(pageResultVo);
        }
        searchSourceBuilder.query(queryBuilder);

        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        countRequest.source(searchSourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        Integer limitSize = esLimit;
        if (count < limitSize) {
            //未超过限制数量  按 当前count计算上限
            limitSize = (int) count;
        }
        ResultVo<List<String>> resultVoIds = esearchService.getEsIds(10000, limitSize, queryBuilder, indexNames, "CreateTime", "_id");
        List<String> ids = resultVoIds.getData();
        if (ids.size() > 0) {
            queryBuilder = new BoolQueryBuilder();
            queryBuilder.filter(QueryBuilders.termsQuery("_id", ids));
            searchSourceBuilder = searchSourceBuilder.clearRescorers();
            searchSourceBuilder.query(queryBuilder);
        } else {
            return ResultVo.success(pageResultVo);
        }
        //"script" : {
        //          "inline" : "doc['字段名称1'].values +'-'+ doc['字段名称2'].values",
        //          "lang" : "painless"
        //        },
        //.script(new Script("doc['ip_dst_addr'].value+'='+doc['ip_src_addr'].value"));

        //前端需求的字段名  重新排序  //sIp  dIp dPort sMac   sIpCountry   dMac   dIpCountry
        List<String> allField = SessionAggEnum.getAllField();
        //所有字段  去  取交集，使前端过来的集合排序
        allField.retainAll(fieldNames);
        //组装脚本聚合条件
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < allField.size(); i++) {
            String fieldName = allField.get(i);
            if (fieldName.equals("sessionCount") || fieldName.equals("sPayloadByteSum") || fieldName.equals("dPayloadByteSum")) {
                continue;
            }
            if (i != 0) {
                //字段的分隔符
                builder.append("+'" + Constants.AGGS_SPLIT + "'+");
            }
            String esField = SessionAggEnum.getEsField(fieldName);
            builder.append("doc['").append(esField).append("'].value");

        }
        TermsAggregationBuilder groupAggBuilder = AggregationBuilders.terms("group");
        groupAggBuilder.script(new Script(builder.toString()));
        groupAggBuilder.size(10000);
        groupAggBuilder.subAggregation(AggregationBuilders.sum("sByte").field("pkt.sPayloadBytes"));
        groupAggBuilder.subAggregation(AggregationBuilders.sum("dByte").field("pkt.dPayloadBytes"));
        searchSourceBuilder.aggregation(groupAggBuilder).size(0);
        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()]));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = null;
        try {
            long l1 = System.currentTimeMillis();
            searchResponse = esearchService.esSearch(searchRequest);
            long l2 = System.currentTimeMillis();
            log.info("会话分析列表聚合，ids入参，耗时t={}", (l2 - l1));
        } catch (Exception e) {
            log.error("查询聚合日志导出文件失败！", e);
        }
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStringTerms groupAgg = aggregations.get("group");
        List<? extends Terms.Bucket> buckets = groupAgg.getBuckets();
        List<SessionAggInfoVo> list = Lists.newArrayListWithCapacity(buckets.size());
        for (Terms.Bucket bucket : buckets) {
            String key = bucket.getKeyAsString();
            String[] split = key.split(Constants.AGGS_SPLIT);
            SessionAggInfoVo vo = new SessionAggInfoVo();
            for (int i = 0; i < split.length; i++) {
                //此时对应的字段
                String field = allField.get(i);
                //对应的es值
                String value = split[i];
                switch (field) {
                    case "sIp":
                        vo.setSIp(value);
                        break;
                    case "dIp":
                        vo.setDIp(value);
                        break;
                    case "dPort":
                        vo.setDPort(Integer.valueOf(value));
                        break;
                    case "appName":
                        vo.setAppName(value);
                        break;
                    case "sMac":
                        vo.setSMac(value);
                        break;
                    case "sIpCountry":
                        vo.setSIpCountry(value);
                        break;
                    case "dMac":
                        vo.setDMac(value);
                        break;
                    case "dIpCountry":
                        vo.setDIpCountry(value);
                        break;
                }
            }
            vo.setSessionCount(bucket.getDocCount());
            Aggregations subAggregations = bucket.getAggregations();
            ParsedSum sByte = subAggregations.get("sByte");
            ParsedSum dByte = subAggregations.get("dByte");
            vo.setSPayloadByteSum((long) sByte.getValue());
            vo.setDPayloadByteSum((long) dByte.getValue());
            list.add(vo);
        }
        long t2 = System.currentTimeMillis();
        log.info(" 会话分析列表聚合共耗时t={}", (t2 - t1));
        Integer page = condition.getCurrentPage();
        Integer limit = condition.getPageSize();
        pageResultVo.setTotal(buckets.size());
        pageResultVo.setRecords(list);
        if (limit < 0) {
            //这里方便创建csv的逻辑  -1表示获取全部
            return ResultVo.success(pageResultVo);

        }

        // 查询出结果后根据条件组装redisKey进行redis保存
        RedisUtil.setEx(rediskey, JSON.toJSONString(list), 10);

        //正常的前端返回的list
        pageResultVo.setRecords(PageResultVo.pageList(list, page, limit));
        return ResultVo.success(pageResultVo);
    }

    //元数据列表查询和聚合列表查询总结：
    //1：taskId和时间范围  查es_index   找到一大堆  索引名字
    //2：组装前端的搜索条件，以及  APP_NAME="APP_HTTP"  查到一大堆sessionId
    //3：再用sessionId集合，查http* 索引
    //这里的第三步，普通列表查询直接返回es对象   聚合查询对sessionId集合 再到相应的索引下聚合
    @Override
    public ResultVo<PageResultVo<JSONObject>> getMetadataAggList(MetadataAggInfoCondition condition) {
        log.info("元数据列表：聚合，condition={}", JSON.toJSON(condition));

        // 此处应该做redis缓存查询，否则聚合起来压力过大
        String rediskey = condition.getDataKey().toLowerCase() + "_metadata_aggr_list:" + ESQueryUtil.getRedisKey3(condition);
        try {
            String redisJson = RedisUtil.get(rediskey);
            if (redisJson != null) {
                List<JSONObject> list = JSONObject.parseArray(redisJson, JSONObject.class);
                log.info(condition.getDataKey() + "元数据碰撞聚合查询，redis查询成功，数组长度", rediskey);
                Integer page = condition.getCurrentPage();
                Integer limit = condition.getPageSize();
                PageResultVo<JSONObject> pageResultVo = new PageResultVo<>();
                pageResultVo.setRecords(PageResultVo.pageList(list, page, limit));
                pageResultVo.setTotal(list.size());
                RedisUtil.expire(rediskey, 10);
                return ResultVo.success(pageResultVo);
            }
        } catch (Exception e) {
            log.error("五元组碰撞聚合查询，redis查询异常", e);
            throw new GkException(GkErrorEnum.REDIS_QUERY_ERROR);
        }

        ResultVo errorVo = checkParam(condition);
        if (errorVo != null) return errorVo;
        String dataKey = condition.getDataKey();
        List<String> fieldNames = condition.getFieldNames();
        if (fieldNames == null || fieldNames.size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        String index;
        String appName;
        switch (dataKey) {
            //for  判断字段的准确性
            case "SSL":
                for (String fieldName : fieldNames) {
                    if (!SslAggEnum.checkField(fieldName)) {
                        throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                    }
                }
                appName = "APP_SSL";
                index = "ssl*";
                break;
            case "HTTP":
                for (String fieldName : fieldNames) {
                    if (fieldName.equals("appName")) {
                        continue;
                    }
                    if (!HttpAggEnum.checkField(fieldName)) {
                        throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                    }
                }
                appName = "APP_HTTP";
                index = "http*";
                break;
            case "DNS":
                for (String fieldName : fieldNames) {
                    if (!DNSAggEnum.checkField(fieldName)) {
                        throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
                    }
                }
                appName = "APP_DNS";
                index = "dns*";
                break;
            default:
                throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        List<String> indexNames = queryFirstIndexName(condition);
        PageResultVo pageResultVo = new PageResultVo();
        pageResultVo.setTotal(0);
        pageResultVo.setRecords(new ArrayList());
        if (indexNames == null || indexNames.size() < 1) {
            return ResultVo.success(pageResultVo);
        }
        //先判断元数据查询
        Map<String, BoolQueryBuilder> map = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(map);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() < 1) {
            return ResultVo.success(pageResultVo);
        }
        BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() > 0)
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        queryBuilder.must(QueryBuilders.termQuery("AppName", appName));

        // 处理标签的查询条件
        queryBuilder = aggrTargetService.getTargetTagCondition(condition, "ALL", queryBuilder);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(0);

        //获取 SessionIds
        long t1 = System.currentTimeMillis();
        List<String> sessionIds = getSessionIdForAggs(queryBuilder, indexNames);
        long t2 = System.currentTimeMillis();
        log.info("元数据聚合获取SessionIds耗时time={}", (t2 - t1));
        if (sessionIds.size() < 1) {
            return ResultVo.success(pageResultVo);
        }

        SearchSourceBuilder idsSourceBuilder = new SearchSourceBuilder();
        queryBuilder = new BoolQueryBuilder();
        queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIds));
        idsSourceBuilder.query(queryBuilder);

        if (sessionIds.size() < 1) {
            return ResultVo.success(pageResultVo);
        }

        //处理聚合字段排序  生成脚本语句
        List<String> allFields = getAllField(fieldNames, dataKey);
        StringBuilder builder = delAggBuilder(allFields, fieldNames, dataKey);
        TermsAggregationBuilder groupAggBuilder = AggregationBuilders.terms("group");
        groupAggBuilder.script(new Script(builder.toString()));
        groupAggBuilder.size(10000);
        idsSourceBuilder.aggregation(groupAggBuilder);
        idsSourceBuilder.size(0);
        SearchRequest searchRequest = new SearchRequest(new String[]{index}, idsSourceBuilder);
        long t3 = System.currentTimeMillis();
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long t4 = System.currentTimeMillis();
        log.info("元数据聚合耗时time={}", (t4 - t3));
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStringTerms groupAgg = aggregations.get("group");
        List<? extends Terms.Bucket> buckets = groupAgg.getBuckets();
        List<JSONObject> resultList = Lists.newArrayListWithCapacity(buckets.size());
        for (Terms.Bucket bucket : buckets) {
            //这里不使用Vo对象，直接使用 enum类  控制JSON的key。方便前后端一致以及3个元数据结构共用
            JSONObject jsonObject = new JSONObject();
            String key = bucket.getKeyAsString();
            String[] split = key.split(Constants.AGGS_SPLIT);
            jsonObject.put("cnt", bucket.getDocCount());
            //获取完整对象的组装   以下字段是通过枚举类校验过后  所以直接进json
            for (int i = 0; i < split.length; i++) {
                String value = split[i];
                String field = allFields.get(i);
                if (fieldUseMap.containsKey(field)) {
                    //此字段是转换为数组字符串了
                    value = StringUtils.strip(value, "[]");
                }
                jsonObject.put(field, value);
            }
            if (key.endsWith(Constants.AGGS_SPLIT)) {
                //split会缺失一个空对象
                String field = allFields.get(allFields.size() - 1);
                jsonObject.put(field, "");
            }
            resultList.add(jsonObject);
        }
        Integer page = condition.getCurrentPage();
        Integer limit = condition.getPageSize();
        pageResultVo.setTotal(buckets.size());
        pageResultVo.setRecords(resultList);
        if (limit < 0) {
            //这里方便创建csv的逻辑  -1表示获取全部
            return ResultVo.success(pageResultVo);
        }
        //正常的前端返回的list
        pageResultVo.setRecords(PageResultVo.pageList(resultList, page, limit));

        // 查询出结果后根据条件组装redisKey进行redis保存
        RedisUtil.setEx(rediskey, JSON.toJSONString(resultList), 10);

        return ResultVo.success(pageResultVo);
    }

    /**
     * 获取元数据 前端字段排序
     *
     * @param fieldNames
     * @param dataKey
     * @return
     */
    private List<String> getAllField(List<String> fieldNames, String dataKey) {
        List<String> allField;
        switch (dataKey) {
            case "SSL":
                allField = SslAggEnum.getAllField();
                break;
            case "HTTP":
                allField = HttpAggEnum.getAllField();
                break;
            case "DNS":
                allField = DNSAggEnum.getAllField();
                break;
            default:
                //前面已经校验了dataKey
                allField = new ArrayList<>();
        }
        //所有字段  去  取交集，使前端过来的集合排序
        allField.retainAll(fieldNames);
        return allField;
    }


    /**
     * 获取查询元数据索引下的当前索引
     *
     * @param condition
     * @return
     */
    private List<String> queryMetaDataIndexName(MetadataCondition condition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest("es_index");
        String dataKey = condition.getDataKey().toLowerCase() + "*";
        boolQueryBuilder.must(QueryBuilders.wildcardQuery("index.keyword", dataKey));
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }
        //es_index的时间范围特殊
        ESQueryUtil.delEsIndexTime(boolQueryBuilder, timeRange.getLeft(), timeRange.getRight());
        List<Integer> taskId = condition.getTaskId();
        if (taskId != null && taskId.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("task", taskId));
        }

        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        List<Map<String, Object>> esResultMapList = esearchService.normalSearch(searchRequest);
        Map<String, String> map = new HashMap<>();
        List<String> indexNames = new ArrayList<>();
        if (esResultMapList.size() > 1000) {
            for (Map<String, Object> esResultMap : esResultMapList) {
                String index = esResultMap.get("index").toString();
                String[] sliV = index.split("_");
                map.put(sliV[0] + "_" + sliV[1] + "_*", "");
            }
            Set<String> keySet = map.keySet();
            indexNames = new ArrayList<>(keySet);
            return indexNames;
        }
        for (Map<String, Object> esResultMap : esResultMapList) {
            String index = esResultMap.get("index").toString();
            indexNames.add(index);
        }
        return indexNames;
    }

    /**
     * 生成脚本对象  对应3个元数据对象
     *
     * @param allFields
     * @param fieldNames
     * @param dataKey
     * @return
     */
    private StringBuilder delAggBuilder(List<String> allFields, List<String> fieldNames, String dataKey) {
        //组装脚本聚合条件
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < allFields.size(); i++) {
            if (i != 0) {
                //字段的分隔符
                builder.append("+'" + Constants.AGGS_SPLIT + "'+");
            }
            String fieldName = allFields.get(i);
            String esField = StringUtils.EMPTY;
            switch (dataKey) {
                case "SSL":
                    esField = SslAggEnum.getEsField(fieldName);
                    break;
                case "HTTP":
                    esField = HttpAggEnum.getEsField(fieldName);
                    break;
                case "DNS":
                    esField = DNSAggEnum.getEsField(fieldName);
                    break;
            }
            //如果字段可能为null  或者  []  后续不跟.value
            if (esUseMap.containsKey(esField)) {
                builder.append("doc['").append(esField).append("']");
            } else {
                builder.append("doc['").append(esField).append("'].value");
            }

        }
        return builder;
    }

    //es使用
    private Map<String, Object> esUseMap = new HashMap<>();
    //前端使用
    private Map<String, Object> fieldUseMap = new HashMap<>();

    @PostConstruct
    public void intValueMap() {
        esUseMap.put(DNSAggEnum.DomainIp.getEsUse(), null);
        esUseMap.put(HttpAggEnum.USER_AGENT.getEsUse(), null);
        esUseMap.put(SslAggEnum.DCertHashStr.getEsUse(), null);

        fieldUseMap.put(DNSAggEnum.DomainIp.getField(), null);
        fieldUseMap.put(HttpAggEnum.USER_AGENT.getField(), null);
        fieldUseMap.put(SslAggEnum.DCertHashStr.getField(), null);
    }
}

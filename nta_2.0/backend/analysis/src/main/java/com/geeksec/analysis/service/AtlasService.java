package com.geeksec.analysis.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.condition.AtlasCondition;
import com.geeksec.analysis.condition.FocusTag;
import com.geeksec.analysis.condition.VidsSearchCondition;
import com.geeksec.analysis.entity.storage.OrgInfoVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: qiuwen
 * @date: 2022/9/16
 * @Description:
 **/
public interface AtlasService {

    /**
     * 根据vid集合查询对应点关联数据
     * @return
     */
    ResultVo vidSearch(VidsSearchCondition condition);

    /**
     * 根据点属性查询对应的第一层数据
     */
    ResultVo propertiesSearch(GraphPropertiesNextCondition condition);

    /**
     * 新增查询历史
     */
    void craeteAtlasRecord(Integer type,String atlasCondition);

    /**
     * 旧方法 使用MATCH和字符串替换
     * 根据vid，节点类型，边关系类型查询关联数据列表(带分页)
     * @return
     */
    HashMap<String,Object> relationList(AtlasCondition condition);

    /**
     * 根据关联查询出的节点查询对应的标签信息
     * @param params
     * @return
     */
    ResultVo getTagLabels(List<Map<String, Object>> params);

    /**
     * 通过vid与点类型查询可关联的边列表
     *
     * @param str
     * @param type
     * @param direct
     * @return
     */
    ResultVo getVisibleRelation(String str, String type, String direct) throws IOException;

    /**
     * 查询关注节点之间的关联关系
     * @param tags
     * @return
     */
    ResultVo getFocusTagRelation(List<FocusTag> tags);

    /**
     * 查询侧拉框关系的可查询关系
     * @param str
     * @param type
     * @return
     */
    ResultVo getVisibleSideRelation(String str, String type);

    /**
     * 查询图探索历史
     */
    ResultVo queryAtlasHistory(JSONObject params);

    /**
     * 删除图探索历史
     * @param params
     * @return
     */
    ResultVo deleteAtlasHistory(JSONObject params);

}

package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.storage.*;
import com.geeksec.analysis.entity.vo.FingerprintVo;
import com.geeksec.entity.common.PageResultVo;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：Nebula关联查询服务
 */
public interface NebulaRelatedService {

    /**
     * 通过目标标签查询对应存在的目标
     *
     * @param tagListMap
     * @param boolQueryBuilder
     * @param type
     * @return
     */
    Map<String, Object> handleTargetTagQuery(Map<String, List<List<String>>> tagListMap, BoolQueryBuilder boolQueryBuilder, String type);

    /**
     * IP列表 Nebula关联数据查询
     *
     * @param ipStrList
     * @return
     */
    List<IPListVo> getIpListNebulaData(List<IpSortVo> allIpList, List<String> ipStrList);

    /**
     * 域名列表 Nebula关联数据查询
     *
     * @param allDomainList
     * @param result
     * @return
     */
    PageResultVo<DomainListVo> getDomainListNebulaData(List<DomainSortVo> allDomainList, PageResultVo<DomainListVo> result) throws UnsupportedEncodingException;

    /**
     * 证书列表 Nebula关联数据查询
     *
     * @param allCertList
     * @param certStrList
     * @return
     */
    List<CertListVo> getCertListNebulaData(List<CertListVo> allCertList, List<String> certStrList);

    /**
     * 指纹列表 Nebula关联数据
     * @param indexNames
     * @param fingerList
     * @param condition
     * @return
     */
    List<FingerprintVo> getFingerListNebulaData(List<String> indexNames, List<FingerprintVo> fingerList, AnalysisBaseCondition condition) throws UnsupportedEncodingException;
}

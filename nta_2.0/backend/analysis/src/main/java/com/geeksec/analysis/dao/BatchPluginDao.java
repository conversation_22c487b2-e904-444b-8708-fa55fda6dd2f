package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.BatchPlugin;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
public interface BatchPluginDao extends BaseMapper<BatchPlugin> {

    List<BatchPlugin> selectByBatchId(@Param("batchId") Integer batchId);

    /**
     * 时间：2022年5月6日
     * 会话元数据开启  和  协议元数据开启的plugin在一张表。目前pluginId=1120 为会话，其他id都是协议的
     * @param batchId
     * @param pluginIds
     * @param shouldLogDef
     * @param type 1:会话元数据   2：协议
     * @return
     */
    Integer updateList(@Param("batchId") Integer batchId,
                       @Param("pluginIds") List<Integer> pluginIds,
                       @Param("shouldLogDef") Integer shouldLogDef,
                       @Param("type") Integer type);
}

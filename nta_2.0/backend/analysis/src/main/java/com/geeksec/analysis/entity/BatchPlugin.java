package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_batch_plugin")
public class BatchPlugin implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * //自增ID ---任务信息表---
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * //批次ID
     */
    @TableField("batch_id")
    private Integer batchId;

    /**
     * //插件ID
     */
    @TableField("plugin_id")
    private Integer pluginId;

    /**
     * //开启的插件iD默认值 ， 1 为开启 0  位关闭
     */
    @TableField("should_log_def")
    private Integer shouldLogDef;


}

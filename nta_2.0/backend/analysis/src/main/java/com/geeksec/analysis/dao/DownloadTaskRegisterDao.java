package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.condition.TaskRegisterCondition;
import com.geeksec.analysis.entity.DownloadTaskRegister;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-19
 */
public interface DownloadTaskRegisterDao extends BaseMapper<DownloadTaskRegister> {

    /**
     * 获取下一个执行的任务
     *
     * @return
     */
    DownloadTaskRegister getNextTask();

    /**
     * 下载列表查询
     *
     * @param taskRegister 查询对象
     * @return
     */
    List<DownloadTaskRegister> listTaskRegister(TaskRegisterCondition taskRegister);
}

package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class OfflineTaskQueryCondition extends BaseCondition {


    @ApiModelProperty("任务名称或任务描述")
    @JsonProperty("task_name")
    private String taskName;

    @ApiModelProperty("用户id")
    @JsonProperty("user_id")
    private Integer userId;

}

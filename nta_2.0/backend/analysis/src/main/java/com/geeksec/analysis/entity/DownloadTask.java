package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_download_task")
public class DownloadTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户名id
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 文件路径
     */
    @TableField("path")
    private String path;

    /**
     * ES 下载 检索条件
     */
    @TableField("query")
    private String query;

    /**
     * 前端展示字段
     */
    @TableField("show_query")
    private String showQuery;

    /**
     * 全量下载为1，部分下载为0
     */
    @TableField("type")
    private Integer type;

    /**
     * session 列表信息
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 0 准备数据 1可下载 2重新下载 3已删除 4待删除
     */
    @TableField("state")
    private Integer state;

    @TableField("created_time")
    private Long createdTime;

    /**
     * 数据存储时间
     */
    @TableField("end_time")
    private Long endTime;

    /**
     * 数据状态 0 删除 1存在
     */
    @TableField("status")
    private Integer status;

    /**
     * 任务ID 数组
     */
    @TableField("task_id")
    private String taskId;

}

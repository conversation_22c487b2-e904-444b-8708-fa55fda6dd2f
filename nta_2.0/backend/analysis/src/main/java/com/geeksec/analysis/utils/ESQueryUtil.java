package com.geeksec.analysis.utils;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.MetadataAggInfoCondition;
import com.geeksec.analysis.entity.condition.SessionAggInfoCondition;
import com.geeksec.authentication.util.Md5Util;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.EsTextToKeywordEnum;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.IpUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.io.netty.util.internal.StringUtil;
import org.elasticsearch.index.query.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：ES 查询语句处理工具
 */
public class ESQueryUtil {
    private static final Logger logger = LoggerFactory.getLogger(ESQueryUtil.class);

    private static List<String> keywordQueryList = Lists.newArrayList("Domain", "Query.name", "Answer.value", "Answer.name");

    /**
     * ES语句组装入口
     *
     * @param queryList
     * @param builder
     * @return
     */
    public static BoolQueryBuilder buildQueryEntrance(List<HashMap<String, Object>> queryList, BoolQueryBuilder builder) {
        for (HashMap<String, Object> queryMap : queryList) {
            buildFirstESQuery(queryMap, builder);
        }

        return builder;
    }

    /**
     * 第一步：判断条件内是must还是not
     *
     * @param queryMap
     * @param builder
     */
    private static void buildFirstESQuery(HashMap<String, Object> queryMap, BoolQueryBuilder builder) {
        // 判断当前条件是 and 还是 not
        String whether = (String) queryMap.get("bool_search");

        switch (whether) {
            case "and":
                builder.must(buildSecondESQuery(queryMap));
                break;
            case "not":
                builder.mustNot(buildSecondESQuery(queryMap));
                break;
        }
    }

    /**
     * 分解单次提交条件下查询参数
     *
     * @param queryMap {target: "IP", val: ["192.168"]}
     * @return
     */
    private static BoolQueryBuilder buildSecondESQuery(HashMap<String, Object> queryMap) {
        BoolQueryBuilder resultQuery = QueryBuilders.boolQuery();
        List<HashMap<String, Object>> searchList = (List<HashMap<String, Object>>) queryMap.get("search");
        for (HashMap<String, Object> searchMap : searchList) {
            QueryBuilder builder = buildThirdEsQuery(searchMap);
            if (builder == null) {
                continue;
            }
            resultQuery.must(builder);
        }
        return resultQuery;
    }

    /**
     * 开始组装查询语句
     *
     * @param searchMap
     * @return
     */
    private static QueryBuilder buildThirdEsQuery(HashMap<String, Object> searchMap) {
        String target = (String) searchMap.get("target");
        // ip集合
        List<String> ips = new ArrayList<>();
        // tag标签id结婚
        List<String> tagIds = new ArrayList<>();
        List<String> vals = (List<String>) searchMap.get("val");
        switch (target) {
            case "noTransform":
                return null;
            case "sIp":
                ips = (List<String>) searchMap.get("val");
                return QueryBuilders.termsQuery(target, ips);
            case "dIp":
                ips = (List<String>) searchMap.get("val");
                return QueryBuilders.termsQuery(target, ips);
            case "IP":
                ips = (List<String>) searchMap.get("val");
                return QueryBuilders.termsQuery(target, ips);
            case "Tag":
                tagIds = (List<String>) searchMap.get("val");
                return QueryBuilders.termsQuery(target, tagIds);
            default:
                return QueryBuilders.termsQuery(target, vals);
        }
    }

    /**
     * 任务特殊处理
     *
     * @param boolQueryBuilder
     * @param taskId
     * @param startTime
     * @param endTime
     */
    public static void specialHandle(BoolQueryBuilder boolQueryBuilder, List<Integer> taskId, Long startTime, Long endTime) {

        // 1.处理任务id
//        HashMap<Integer, Integer> taskMap = new HashMap<>();
//
//        if (sliTask != null && sliTask.size() > 0){
//            boolQueryBuilder.must(QueryBuilders.termsQuery("task",sliTask));
//        }
        // 2.处理时间范围
        RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("StartTime");
        if (startTime > 0) {
            //gt  大于    这里只用流量的开始时间~
            timeRangeQuery.gte(startTime);
        }
        if (endTime > 0) {
            timeRangeQuery.lte(endTime);
        }
        if (startTime > 0 || endTime > 0)
            boolQueryBuilder.must(timeRangeQuery);
    }


    /**
     * 组装es的查询对象
     *
     * @param condition
     * @return
     */
    public static BoolQueryBuilder query(AnalysisBaseCondition condition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        List<AnalysisBaseCondition.QueryOb> query = condition.getQuery();
        //可能会有元数据的查询，收集元数据的查询参数   最终获得一组 SessionIds
        for (AnalysisBaseCondition.QueryOb queryOb : query) {
            String boolSearch = queryOb.getBoolSearch();
            List<AnalysisBaseCondition.SearchInfo> search = queryOb.getSearch();
            for (AnalysisBaseCondition.SearchInfo searchInfo : search) {
                String target = searchInfo.getTarget();
                List<String> val = searchInfo.getVal();
                if (target.startsWith("INDEX_")) {
                    //元数据查询 跳过
                    continue;
                }
                //某些前端传入字段 需要keyword;
                target = EsTextToKeywordEnum.getEsUse(target);
                searchInfo.setTarget(target);
                //域名需要模糊查询
                if ("AllDomain".equals(target)) {
                    BoolQueryBuilder shouldBuild = QueryBuilders.boolQuery();
                    List<String> termsDomain = new ArrayList<>();
                    for (String s : val) {
                        //这里包含 * 和 不包含的查询方式不同
                        if (s.contains("*")) {
                            shouldBuild.should(QueryBuilders.wildcardQuery("HTTP.Host", s));
                            shouldBuild.should(QueryBuilders.wildcardQuery("DNS.Domain", s));
                            shouldBuild.should(QueryBuilders.wildcardQuery("SSL.CH_ServerName", s));

                            // DNS SSL HTTP数据中的域名字段
                            shouldBuild.should(QueryBuilders.wildcardQuery("Domain", s));
                            shouldBuild.should(QueryBuilders.wildcardQuery("Host", s));
                            shouldBuild.should(QueryBuilders.wildcardQuery("CH_ServerName", s));
                            ;
                        } else {
                            termsDomain.add(s);
                        }
                    }
                    if (termsDomain.size() > 0) {
                        // 会话分析中字段
                        shouldBuild.should(QueryBuilders.termsQuery("HTTP.Host.keyword", termsDomain));
                        shouldBuild.should(QueryBuilders.termsQuery("DNS.Domain.keyword", termsDomain));
                        shouldBuild.should(QueryBuilders.termsQuery("SSL.CH_ServerName.keyword", termsDomain));
                        // DNS SSL HTTP数据中的域名字段
                        shouldBuild.should(QueryBuilders.termsQuery("Domain", termsDomain));
                        shouldBuild.should(QueryBuilders.termsQuery("Host.keyword", termsDomain));
                        shouldBuild.should(QueryBuilders.termsQuery("CH_ServerName", termsDomain));
                    }
                    mustSetShould(boolQueryBuilder, boolSearch, shouldBuild);
                } else if ("Finger".equals(target)) {
                    //指纹面向两个字段
                    BoolQueryBuilder shouldBuild = QueryBuilders.boolQuery();
                    for (String s : val) {
                        shouldBuild.should(QueryBuilders.termsQuery("sSSLFinger", s));
                        shouldBuild.should(QueryBuilders.termsQuery("dSSLFinger", s));
                        shouldBuild.should(QueryBuilders.termsQuery("sHTTPFinger", s));
                        shouldBuild.should(QueryBuilders.termsQuery("dHTTPFinger", s));
                    }
                    mustSetShould(boolQueryBuilder, boolSearch, shouldBuild);
                } else if ("IP".equals(target)) {
                    BoolQueryBuilder shouldBuild = QueryBuilders.boolQuery();
                    // 组装IP查询条件时，同时需要判定IP值是否合规，还要看是不是CIDR格式的IP进行查询
                    for (String s : val) {
                        if (IpUtils.isIpv4Str(s) || IpUtils.isIpv6Str(s)) {
                            // 如果是CIDR格式的，使用gte的格式去ES里面匹配信息
                            if (s.contains("/")) {
                                // 去掉后续的网段内容
                                String endIpAddr = IpUtils.getEndIpAddr(s);
                                s = s.split("/")[0];
                                shouldBuild.should(QueryBuilders.rangeQuery("sIp").gte(s).lte(endIpAddr));
                                shouldBuild.should(QueryBuilders.rangeQuery("dIp").gte(s).lte(endIpAddr));
                            } else {
                                // 不是CIDR格式的
                                shouldBuild.should(QueryBuilders.termsQuery("sIp", s));
                                shouldBuild.should(QueryBuilders.termsQuery("dIp", s));
                            }
                        } else {
                            throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                        }
                    }
                    mustSetShould(boolQueryBuilder, boolSearch, shouldBuild);
                } else if ("Cert".equals(target)) {
                    BoolQueryBuilder shouldBuild = QueryBuilders.boolQuery();
                    for (String s : val) {
                        shouldBuild.should(QueryBuilders.termsQuery("SSL.sCertHash", s));
                        shouldBuild.should(QueryBuilders.termsQuery("SSL.dCertHash", s));
                    }
                    mustSetShould(boolQueryBuilder, boolSearch, shouldBuild);
                } else if ("dPort".equals(target) || "sPort".equals(target)
                        || "pkt.sPayloadBytes".equals(target)
                        || "pkt.dPayloadBytes".equals(target)) {
                    //这里val  只有一个
                    for (String s : val) {
                        //有-表示  大于小于的需求
                        if (s.contains("-")) {
                            String[] split = s.split("-");
                            RangeQueryBuilder dPortRangeBuilder = QueryBuilders.rangeQuery(target);
                            String left = split[0];
                            if (split.length > 1) {
                                //x-这种格式  数组只有一个值。   -x能正确split两个值
                                String right = split[1];
                                if (StringUtils.isNotEmpty(right)) {
                                    dPortRangeBuilder.to(right);
                                }
                            }
                            if (StringUtils.isNotEmpty(left)) {
                                dPortRangeBuilder.from(left);
                            }
                            mustSetShould(boolQueryBuilder, boolSearch, dPortRangeBuilder);
                        } else {
                            if ("and".equals(boolSearch)) {
                                boolQueryBuilder.must(QueryBuilders.termsQuery(searchInfo.getTarget(), searchInfo.getVal()));
                            } else if ("not".equals(boolSearch)) {
                                boolQueryBuilder.mustNot(QueryBuilders.termsQuery(searchInfo.getTarget(), searchInfo.getVal()));
                            }
                        }
                    }
                } else {
                    if ("and".equals(boolSearch)) {
                        if ("Labels".equals(searchInfo.getTarget())) {
                            continue;
                        }
                        boolQueryBuilder.must(QueryBuilders.termsQuery(searchInfo.getTarget(), searchInfo.getVal()));
                    } else if ("not".equals(boolSearch)) {
                        if ("Labels".equals(searchInfo.getTarget())) {
                            continue;
                        }
                        boolQueryBuilder.mustNot(QueryBuilders.termsQuery(searchInfo.getTarget(), searchInfo.getVal()));
                    }
                }
            }
        }

        //时间对象查询拼接
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        Long left = timeRange.getLeft();
        Long right = timeRange.getRight();
        RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("StartTime");
        if (left > 0) {
            //gt  大于    这里只用流量的开始时间~
            timeRangeQuery.gte(left);
        }
        if (right > 0) {
            timeRangeQuery.lte(right);
        }
        if (left > 0 || right > 0) {
            boolQueryBuilder.must(timeRangeQuery);
        }

        return boolQueryBuilder;
    }

    //请求条件里带 元数据的 请求条件
    public static Map<String, BoolQueryBuilder> mateDataQuery(AnalysisBaseCondition condition) {
        List<AnalysisBaseCondition.QueryOb> query = condition.getQuery();
        //可能会有元数据字段的查询，收集元数据的查询参数   最终获得一组 SessionIds
        Map<String, BoolQueryBuilder> queryMap = new HashMap<>();
        for (AnalysisBaseCondition.QueryOb queryOb : query) {
            String boolSearch = queryOb.getBoolSearch();
            List<AnalysisBaseCondition.SearchInfo> search = queryOb.getSearch();
            for (AnalysisBaseCondition.SearchInfo searchInfo : search) {
                String target = searchInfo.getTarget();
                List<String> val = searchInfo.getVal();
                if (target.startsWith("INDEX_")) {
                    //元数据开头的  需要先检索元数据 获取SessionIds
                    String[] split = target.split(":");
                    String indexPath = split[0];
                    String fieldName = split[1];
                    fieldName = EsTextToKeywordEnum.getEsUse(fieldName);
                    String mateDataIndex = StringUtil.EMPTY_STRING;
                    if (indexPath.equals("INDEX_SSL")) {
                        mateDataIndex = "ssl";
                    } else if (indexPath.equals("INDEX_DNS")) {
                        mateDataIndex = "dns";
                    } else if (indexPath.equals("INDEX_HTTP")) {
                        mateDataIndex = "http";
                    } else if (indexPath.equals("INDEX_SSH")) {
                        mateDataIndex = "ssh";
                    } else if (indexPath.equals("INDEX_Telnet")) {
                        mateDataIndex = "telnet";
                    } else if (indexPath.equals("INDEX_Rlogin")) {
                        mateDataIndex = "rlogin";
                    } else if (indexPath.equals("INDEX_VNC")) {
                        mateDataIndex = "vnc";
                    } else if (indexPath.equals("INDEX_RDP")) {
                        mateDataIndex = "rdp";
                    } else if (indexPath.equals("INDEX_XDMCP")) {
                        mateDataIndex = "xdmcp";
                    }
                    if (!queryMap.containsKey(mateDataIndex)) {
                        //有查询条件  先初始化
                        queryMap = initMateDataQuery(condition, mateDataIndex);
                    }
                    //收集元数据查询
                    BoolQueryBuilder boolQueryBuilder = queryMap.get(mateDataIndex);
                    if ("and".equals(boolSearch)) {
                        // 若为元数据查询，判断keyword添加
                        if (indexPath.startsWith("INDEX")) {
                            fieldName = handleMetadataVal(fieldName);
                        }
                        boolQueryBuilder.must(QueryBuilders.termsQuery(fieldName, val));
                    } else if ("not".equals(boolSearch)) {
                        boolQueryBuilder.mustNot(QueryBuilders.termsQuery(fieldName, val));
                    }
                }
            }
        }
        return queryMap;
    }

    private static String handleMetadataVal(String fieldName) {
        StringBuilder stringBuilder = new StringBuilder(fieldName);
        if (keywordQueryList.contains(fieldName)) {
            stringBuilder.append(".keyword");
        }
        return stringBuilder.toString();
    }

    //初始化元数据查询容器
    private static Map<String, BoolQueryBuilder> initMateDataQuery(AnalysisBaseCondition condition, String index) {
        //时间对象查询拼接
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        Long left = timeRange.getLeft();
        Long right = timeRange.getRight();
        RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("StartTime");
        if (left > 0) {
            //gt  大于    这里只用流量的开始时间~
            timeRangeQuery.gte(left);
        }
        if (right > 0) {
            timeRangeQuery.lte(right);
        }
        Map<String, BoolQueryBuilder> map = new HashMap<>();
        BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
        map.put(index, boolQueryBuilder1);
        if (left > 0 || right > 0) {
            boolQueryBuilder1.must(timeRangeQuery);
        }
        List<Integer> taskId = condition.getTaskId();
        boolQueryBuilder1.must(QueryBuilders.termsQuery("TaskId", taskId));
        return map;
    }

    //元数据查询对象

    /**
     * 会话列表 ES查询语句拼装
     *
     * @return 查询语句
     */
    public static BoolQueryBuilder esAssemblySession(AnalysisBaseCondition sessionCondition) {
        // 设置时间范围
        AnalysisBaseCondition.TimeRange timeRange = sessionCondition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            sessionCondition.setTimeRange(timeRange);
        }
        Long left = timeRange.getLeft();
        if (left == null || left < 1) {
            left = -1L;
            timeRange.setLeft(left);
        }
        Long right = timeRange.getRight();
        if (right == null || right < 1) {
            right = -1L;
            timeRange.setRight(right);
        }
        // JSON query字段对应条件拼装
        return query(sessionCondition);
    }


    private static void mustSetShould(BoolQueryBuilder boolQueryBuilder, String boolSearch, AbstractQueryBuilder shouldBuild) {
        if ("and".equals(boolSearch)) {
            boolQueryBuilder.must(shouldBuild);
        } else if ("not".equals(boolSearch)) {
            boolQueryBuilder.mustNot(shouldBuild);
        }
    }


    /**
     * 查询es_index 需要严格的时间处理
     *
     * @param builder
     * @param startTime
     * @param endTime
     */
    public static void delEsIndexTime(BoolQueryBuilder builder, Long startTime, Long endTime) {
        List<QueryBuilder> musts = builder.must();
        //两个时间都有值。  需要索引的s1-s2有交集
        if (startTime > 0 && endTime > 0) {
            RangeQueryBuilder firstRange = QueryBuilders.rangeQuery("first_time");
            //gt  大于
            firstRange.gte(endTime);
            RangeQueryBuilder lastRange = QueryBuilders.rangeQuery("last_time");
            lastRange.lte(startTime);
            BoolQueryBuilder mustBuilder = new BoolQueryBuilder();
            //es的s1 或者 s2在  查询区间，即可
            mustBuilder.mustNot(firstRange);
            mustBuilder.mustNot(lastRange);
            musts.add(mustBuilder);
        }
        //开始时间有值   需要索引的s2 >  startTime
        if (startTime > 0 && endTime <= 0) {
            RangeQueryBuilder lastRange = QueryBuilders.rangeQuery("last_time");
            lastRange.gte(startTime);
            musts.add(lastRange);
        }
        //结束时间有值   需要索引的s1 <  lastTime
        if (startTime <= 0 && endTime > 0) {
            RangeQueryBuilder firstRange = QueryBuilders.rangeQuery("first_time");
            firstRange.lte(endTime);
            musts.add(firstRange);
        }
    }


    public static ResultVo checkParam(AnalysisBaseCondition condition) {
        List<Integer> taskId = condition.getTaskId();
        if (taskId == null || taskId.size() < 1) {
            taskId = new ArrayList<>();
            taskId.add(0);
            //默认0主任务为对象查询
            condition.setTaskId(taskId);
        }
        Integer limit = condition.getPageSize();
        Integer page = condition.getCurrentPage();
        if (limit == null || limit < 1) {
            condition.setCurrentPage(1);
        }
        if (page == null || page < 1) {
            condition.setPageSize(10);
        }

        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();

        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }

        Long left = timeRange.getLeft();
        Long right = timeRange.getRight();
        if (left == null || left < 0L) {
            timeRange.setLeft(-1L);
        }

        if (right == null || right < 0L) {
            timeRange.setRight(-1L);
        }

        // 判定IP为多目标时的合法合规
        List<AnalysisBaseCondition.QueryOb> query = condition.getQuery();
        for (AnalysisBaseCondition.QueryOb queryOb : query) {
            List<AnalysisBaseCondition.SearchInfo> search = queryOb.getSearch();
            for (AnalysisBaseCondition.SearchInfo searchInfo : search) {
                String target = searchInfo.getTarget();
                List<String> val = searchInfo.getVal();
                if ("IP".equals(target)) {
                    for (String ip : val) {
                        if (!IpUtils.isIpv4Str(ip) && !IpUtils.isIpv6Str(ip)) {
                            throw new GkException(GkErrorEnum.IP_FORMAT_ERROR);
                        }
                    }
                }
            }
        }


        return null;
    }

    public static String getRedisKey(AnalysisBaseCondition condition) {
        AnalysisBaseCondition copy = new AnalysisBaseCondition();
        BeanUtils.copyProperties(condition, copy);
        copy.setCurrentPage(null);
        copy.setPageSize(null);
        copy.setAsc(null);
        copy.setOrderField(condition.getOrderField());
        String json = JSONObject.toJSONString(copy);
        String md5 = Md5Util.encodeMd5(json);
        return md5;
    }

    public static String getRedisKey2(SessionAggInfoCondition condition) {
        SessionAggInfoCondition copy = new SessionAggInfoCondition();
        BeanUtils.copyProperties(condition, copy);
        copy.setCurrentPage(null);
        copy.setPageSize(null);
        copy.setAsc(null);
        copy.setOrderField(condition.getOrderField());
        String json = JSONObject.toJSONString(copy);
        String md5 = Md5Util.encodeMd5(json);
        return md5;
    }

    public static String getRedisKey3(MetadataAggInfoCondition condition) {
        MetadataAggInfoCondition copy = new MetadataAggInfoCondition();
        BeanUtils.copyProperties(condition, copy);
        copy.setCurrentPage(null);
        copy.setPageSize(null);
        copy.setAsc(null);
        copy.setOrderField(condition.getOrderField());
        String json = JSONObject.toJSONString(copy);
        String md5 = Md5Util.encodeMd5(json);
        return md5;
    }

}

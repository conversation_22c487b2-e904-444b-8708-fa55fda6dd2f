package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 离线任务详情
 */
@Data
public class OfflineTaskVo {

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 任务名
     */
    @JsonProperty("task_name")
    private String taskName;

    /**
     * 任务描述
     */
    @JsonProperty("task_description")
    private String taskDescription;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @JsonProperty("update_time")
    private String updateTime;

    /**
     * 数据总量
     */
    @JsonProperty("data_total")
    private Long dataTotal;

    /**
     * 会话总量
     */
    @JsonProperty("session_total")
    private Long sessionTotal;

    /**
     * 告警总量
     */
    @JsonProperty("alarm_total")
    private Long alarmTotal;

    /**
     * 高危目标
     */
    @JsonProperty("high_target_total")
    private Integer highTargetTotal;

    /**
     * 过滤数据量
     */
    @JsonProperty("filter_data_total")
    private Long filterDataTotal;

    /**
     * 采集规则命中数据量
     */
    @JsonProperty("rule_hits_data_total")
    private Long ruleHitsDataTotal;

    /**
     * 白名单过滤量
     */
    @JsonProperty("whitelist_filter_total")
    private Long whitelistFilterTotal;

    /**
     * 批次数量
     */
    @JsonProperty("batch_num")
    private Integer batchNum;

    /**
     * 正在导入批次列表
     */
    @JsonProperty("batch_progress_list")
    private List<OfflineTaskBatchProgressVo> batchProgressList;

    /**
     * 最近导入完成时间
     */
    @JsonProperty("last_import_time")
    private String lastImportTime;

}

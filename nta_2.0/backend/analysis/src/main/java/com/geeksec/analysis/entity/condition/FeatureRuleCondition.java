package com.geeksec.analysis.entity.condition;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
//忽略未知属性
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureRuleCondition implements Serializable {

    private static final long serialVersionUID = 6558380609875266858L;

    @JSONField(serialize = false)
    private Long id;

    /**
     * 规则ID
     */
    @JsonProperty("rule_id")
    @JSONField(name="APPID")
    private Integer ruleId;

    @JsonProperty("task_id")
    @JSONField(serialize = false)
    private Integer taskId;

    @JsonProperty("batch_id")
    @JSONField(serialize = false)
    private Integer batchId;


    /**
     * 规则级别
     */
    @JsonProperty("rule_level")
    @JSONField(name="Level")
    private Integer ruleLevel;

    /**
     * 规则名称
     */
    @JsonProperty("rule_name")
    @JSONField(serialize = false)
    private String ruleName;

    /**
     * 规则描述
     */
    @JsonProperty("rule_desc")
    @JSONField(serialize = false)
    private String ruleDesc;

    /**
     * 采集模式
     */
    @JsonProperty("capture_mode")
    @JSONField(name="Trance")
    private Integer captureMode;

    /**
     * 0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作
     */
    @JsonProperty("rule_family")
    @JSONField(serialize = false)
    private Integer ruleFamily;

    /**
     * 日志响应 pb留存  0保留  1丢弃
     */
    @JsonProperty("pb_drop")
    @JSONField(name="PbDrop")
    private Integer pbDrop;

    /**
     * 流量响应 pcap留存 0保留  1丢弃
     */
    @JsonProperty("pcap_drop")
    @JSONField(name="PcapDrop")
    private Integer pcapDrop;

    /**
     * 每秒限速
     */
    @JsonProperty("byte_ps")
    @JSONField(name="BytePs")
    private Long bytePs;

    /**
     * 存储大小，负数不限
     */
    @JsonProperty("save_bytes")
    @JSONField(name="SaveBytes")
    private Long saveBytes;
    /**
     * 开启动态库响应
     */
    @JsonProperty("lib_respond_open")
    @JSONField(serialize = false)
    private Integer libRespondOpen;

    /**
     * 库路径
     */
    @JsonProperty("lib_respond_lib")
    @JSONField(serialize = false)
    private String libRespondLib;

    /**
     * 库配置路径
     */
    @JsonProperty("lib_respond_config")
    @JSONField(serialize = false)
    private String libRespondConfig;

    @JsonProperty("lib_respond_pkt_num")
    @JSONField(serialize = false)
    private Long libRespondPktNum;

    @JSONField(name= "LibRespond")
    private FeatureRuleCondition.Lib libRespond;

    /**
     * so文件base64字符串
     */
    @JSONField(serialize = false)
    @JsonProperty("lib_data_so")
    private String libDataSo;

    /**
     * conf文件base64字符串
     */
    @JSONField(serialize = false)
    @JsonProperty("lib_data_conf")
    private String libDataConf;


    /** ip规则集 */
    @JsonProperty("ip_rules")
    @JSONField(name="IP_Rule")
    private List<FeatureRuleCondition.IpRule> ipRules;

    /** 协议规则集 */
    @JsonProperty("pro_rules")
    @JSONField(name="Protocol_Rule")
    private List<FeatureRuleCondition.ProRule> proRules;

    /** 特征规则集 */
    @JsonProperty("key_rules")
    @JSONField(name="Key_Rule")
    private List<FeatureRuleCondition.KeyRule> keyRules;

    /** 正则规则集 */
    @JsonProperty("regex_rules")
    @JSONField(name="Regex_Rule")
    private List<FeatureRuleCondition.RegexRule> regexRules;

    /** 域名规则集 */
    @JsonProperty("domain_rules")
    @JSONField(name="Domain_Rule")
    private List<FeatureRuleCondition.DomainRule> domainRules;

    /** 复杂规则响应  key是A,B,C......，value是对象内容
     * key:EXPR  存表达式 */
    @JsonProperty("detail_respond")
    @JSONField(name="DetailRespond")
    private Map<String,Object> detailRespond;

    //接收hash json
    @JSONField(serialize = false)
    private String ruleHash;
    @JSONField(serialize = false)
    private String ruleJson;

    //以下4个字段为默认值即可
    @JSONField(name="ReNewApp")
    private Integer reNewApp=1;
    @JSONField(name="Respond")
    private Integer respond=2;
    @JSONField(name="Reverse")
    private String reverse="ReverseInfor";
    @JSONField(name="Type")
    private Integer type=100;


    @Data
    /**
     * IP规则
     */
    public static class IpRule{
        public IpRule(){}
        @JsonProperty("ip_v4")
        @JSONField(name="IPV4")
        private String ipV4;

        @JsonProperty("ip_v6")
        @JSONField(name="IPV6")
        private String ipV6;

        @JsonProperty("ip_mask_v4")
        @JSONField(name="IPMask_V4")
        private IpV4 ipMaskV4;

        //Positive  正选   Negative 反选
        @JsonProperty("ip_pro")
        @JSONField(name="IPPro")
        private JSONObject ipPro;

        @JsonProperty("port_rule")
        @JSONField(name="Port_Rule")
        private FeatureRuleCondition.PortRule portRule;

        @JsonProperty("ip_type")
        /** 1:IPV4  2:IPV6  3:任意IP */
        @JSONField(serialize = false)
        private Integer ipType;
    }

    @Data
    /**
     * 协议规则
     */
    public static class ProRule{
        public ProRule(){}
        /** 协议id */
        @JsonProperty("pro_id")
        @JSONField(name="ProID")
        private Integer proId;

        @JsonProperty("port_rule")
        @JSONField(name="Port_Rule")
        private FeatureRuleCondition.PortRule portRule;
    }

    @Data
    /**
     * 特征规则
     */
    public static class KeyRule{
        public KeyRule(){}
        /** 协议id */
        @JsonProperty("pro_id")
        @JSONField(name="ProID")
        private Integer proId;

        /** 特征字 */
        @JSONField(name="Keyword")
        private String keyword;

        /** 大小写  0:区分 1:不区分 */
        @JsonProperty("is_case_sensive")
        @JSONField(name="IsCaseSensive")
        private Integer isCaseSensive;
    }

    @Data
    /**
     * 正则规则
     */
    public static class RegexRule{
        public RegexRule(){}
        /** 协议id */
        @JsonProperty("pro_id")
        @JSONField(name="ProID")
        private Integer proId;

        /** 正则表达式 */
        @JSONField(name="Regex")
        private String regex;

        /** 暂时未知 */
        @JSONField(name="Property")
        private Integer property;
    }

    @Data
    /**
     * 域名规则
     */
    public static class DomainRule{
        public DomainRule(){}

        /** 域名 */
        @JSONField(name="Domain")
        private String domain;

        //不需要这个字段了
        /**  1:精确域名   2:n级域名 */
        @JSONField(name="Type")
        private Integer type;
    }

    @Data
    /**
     * 复杂规则
     */
    public static class ComplexRule{
        public ComplexRule(){}
        /** 协议类型 */
        private String type;
        /** 公式内容 */
        private Map<String,String> rule;
    }

    @Data
    /**
     * 端口操作  common字段
     */
    public static class PortRule{
        public PortRule(){}
        /** 小端口 */
        @JsonProperty("low_port")
        @JSONField(name="LowPort")
        private Integer lowPort;

        /** 大端口 */
        @JsonProperty("high_port")
        @JSONField(name="HightPort")
        private Integer highPort;

        /**  */
        @JSONField(name="Property")
        private Integer property;

        /** 1:单选客户端端口  2:单选服务器端口  3:全选 */
        @JSONField(name="Sign")
        private Integer sign;
    }

    @Data
    public static class IpV4{
        public IpV4(){}
        @JSONField(name="IP")
        private String ip;
        @JSONField(name="Mask")
        private String mask;
    }
    @Data
    public static class Lib{
        public Lib(){}
        @JSONField(name= "Lib")
        private String lib;
        @JSONField(name= "PktNum")
        private Long pktNum;

    }

}

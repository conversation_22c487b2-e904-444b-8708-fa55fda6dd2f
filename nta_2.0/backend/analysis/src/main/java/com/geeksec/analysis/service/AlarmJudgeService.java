package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.AlarmRoleJudgeCondition;
import com.geeksec.entity.common.ResultVo;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description：告警研判相关服务
 */
public interface AlarmJudgeService {

    /**
     * 生成告警研判绘图
     * @param alarmMap
     * @return
     */
    ResultVo createAlarmJudgeGraph(Map<String, Object> alarmMap);

    /**
     * 通过角色生成告警研判绘图
     *
     * @param condition@return
     */
    ResultVo<Map<String, Object>> createAlarmJudgeGraphByRole(AlarmRoleJudgeCondition condition);
}

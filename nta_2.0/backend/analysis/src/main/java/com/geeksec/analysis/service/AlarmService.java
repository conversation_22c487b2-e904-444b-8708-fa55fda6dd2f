package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.AlarmCommonCondition;
import com.geeksec.analysis.entity.condition.AlarmListCondition;
import com.geeksec.analysis.entity.condition.AlarmStatusUpCondition;
import com.geeksec.analysis.entity.vo.AlarmTargetAggVo;
import com.geeksec.analysis.entity.vo.KnowledgeAlarmVo;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;

import java.util.List;
import java.util.Map;

public interface AlarmService {

    /**
     * 告警：初始化告警&采集规则字典
     */
    void initKnowledgeType();

    /**
     * 告警：指标信息
     *
     * @param condition
     * @return
     */
    ResultVo<AlarmTargetAggVo> getAlarmTargetAgg(AlarmCommonCondition condition);

    /**
     * 告警:获取模型告警攻击链
     *
     * @param condition
     * @return
     */
    ResultVo getModelAlarmAttackChainAggr(AlarmCommonCondition condition);

    /*
     * 获取完整的规则/知识库 列表
     * @return
     */
    List<KnowledgeAlarmVo> getKnowledgeAlarmList();

    /**
     * 告警：列表
     *
     * @param condition
     * @return
     */
    ResultVo<PageResultVo<Map<String, Object>>> getAlarmList(AlarmListCondition condition);

    /*
     * 告警：通过ES ALARM ID 查询告警详情
     * @param alarmEsId
     * @return
     */
    ResultVo<Map<String, Object>> getAlarmDetail(String alarmEsId);

    /**
     * 告警：通过ES index 和 alarm id查询告警详情
     *
     * @param esIndex
     * @param alarmId
     * @return
     */
    ResultVo<Map<String, Object>> getAlarmDetail2(String esIndex, String alarmId);

    /**
     * 修改文档
     *
     * @param alarmStatusUpCondition
     * @return
     */
    ResultVo updateDoc(AlarmStatusUpCondition alarmStatusUpCondition) throws InterruptedException;

    /**
     * 批量删除文档
     *
     * @param condition
     * @return
     */
    ResultVo deleteDoc(Map<Integer, List<String>> condition) throws InterruptedException;


    /**
     * 删除所有的告警信息
     *
     * @return
     */
    ResultVo deleteAllAlarm();

    /**
     * 导出告警报告PDF
     *
     * @param condition
     * @return
     */
    ResultVo exportAlarmReport(AlarmListCondition condition);

    /**
     * 告警：生成告警相关会话ID信息，生成pcap下载记录
     *
     * @param userId
     * @param sessionList
     * @param alarmType
     * @return
     */
    ResultVo prepareAlarmSessionPcap(Integer userId, List<String> alarmSessionList, String alarmType, Long alarmTime);
}

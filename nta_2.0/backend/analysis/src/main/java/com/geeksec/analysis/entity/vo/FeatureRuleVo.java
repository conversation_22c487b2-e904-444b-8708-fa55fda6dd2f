package com.geeksec.analysis.entity.vo;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.analysis.entity.condition.FeatureRuleCondition;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FeatureRuleVo {

    private Long id;

    /**
     * 规则ID
     */
    @JsonProperty("rule_id")
    @JSONField(name="APPID")
    private Integer ruleId;

    /**
     * 规则级别
     */
    @JsonProperty("rule_level")
    @JSONField(name="Level")
    private Integer ruleLevel;

    /**
     * 规则名称
     */
    @JsonProperty("rule_name")
    @JSONField(name="Name")
    private String ruleName;

    /**
     * 规则描述
     */
    @JsonProperty("rule_desc")
    //@JSONField(serialize = false)
    private String ruleDesc;

    /**
     * 采集模式
     */
    @JsonProperty("capture_mode")
    @JSONField(name="Trance")
    private Integer captureMode;

    /**
     * 0 其他 1 侦察探测 2 武器投递 3 攻击突防 4 命令控制 5。控守操作
     */
    @JsonProperty("rule_family")
    //@JSONField(serialize = false)
    private Integer ruleFamily;

    /**
     * 日志响应 pb留存  0保留  1丢弃
     */
    @JsonProperty("pb_drop")
    @JSONField(name="PbDrop")
    private Integer pbDrop;

    /**
     * 流量响应 pcap留存 0保留  1丢弃
     */
    @JsonProperty("pcap_drop")
    @JSONField(name="PcapDrop")
    private Integer pcapDrop;

    /**
    * 每秒限速
    */
    @JsonProperty("byte_ps")
    @JSONField(name="BytePs")
    private Long bytePs;

    /**
     * 存储大小，负数不限
     */
    @JsonProperty("save_bytes")
    @JSONField(name="SaveBytes")
    private Long saveBytes;
    /**
     * 开启动态库响应
     */
    @JsonProperty("lib_respond_open")
    //@JSONField(serialize = false)
    private Integer libRespondOpen;

    /**
     * 库路径
     */
    @JsonProperty("lib_respond_lib")
    //@JSONField(serialize = false)
    private String libRespondLib;


    /**
    * 包含的规则，逗号分割 1:ip规则 2:协议规则 3:特征规则 4:正则规则 5:域名规则
    */
    @JsonProperty("rule_type")
    @JSONField(serialize = false)
    private String ruleType;

    /**
    * 规则状态 失效 or 生效
    */
    @JsonProperty("rule_state")
    @JSONField(serialize = false)
    private String ruleState;

    /**
     * 库配置路径
     */
    @JsonProperty("lib_respond_config")
    @JSONField(serialize = false)
    private String libRespondConfig;

    /**
    * 包数
    */
    @JsonProperty("lib_respond_pkt_num")
    @JSONField(serialize = false)
    private Long libRespondPktNum;

    /** ip规则集 */
    @JsonProperty("ip_rules")
    @JSONField(name="IP_Rule")
    private List<FeatureRuleCondition.IpRule> ipRules;

    /** 协议规则集 */
    @JsonProperty("pro_rules")
    @JSONField(name="Protocol_Rule")
    private List<FeatureRuleCondition.ProRule> proRules;

    /** 特征规则集 */
    @JsonProperty("key_rules")
    @JSONField(name="Key_Rule")
    private List<FeatureRuleCondition.KeyRule> keyRules;

    /** 正则规则集 */
    @JsonProperty("regex_rules")
    @JSONField(name="Regex_Rule")
    private List<FeatureRuleCondition.RegexRule> regexRules;

    /** 域名规则集 */
    @JsonProperty("domain_rules")
    @JSONField(name="Domain_Rule")
    private List<FeatureRuleCondition.DomainRule> domainRules;

    /** 复杂规则响应  key是A,B,C......，value是对象内容
     * key:EXPR  存表达式 */
    @JsonProperty("detail_respond")
    @JSONField(name="DetailRespond")
    private Map<String,Object> detailRespond;


    @Data
    /**
     * IP规则
     */
    public static class IpRule{
        public IpRule(){}
        @JsonProperty("ip_v4")
        @JSONField(name="IPV4")
        private String ipV4;

        @JsonProperty("ip_v6")
        @JSONField(name="IPV6")
        private String ipV6;

        @JsonProperty("ip_mask_v4")
        @JSONField(name="IPMask_V4")
        private FeatureRuleCondition.IpV4 ipMaskV4;

        //Positive  正选   Negative 反选
        @JsonProperty("ip_pro")
        @JSONField(name="IPPro")
        private JSONObject ipPro;

        @JsonProperty("port_rule")
        @JSONField(name="PortRule")
        private FeatureRuleCondition.PortRule portRule;

    }

    @Data
    /**
     * 协议规则
     */
    public static class ProRule{
        public ProRule(){}
        /** 协议id */
        @JsonProperty("pro_id")
        @JSONField(name="ProId")
        private Integer proId;

        @JsonProperty("port_rule")
        @JSONField(name="Port_Rule")
        private FeatureRuleCondition.PortRule portRule;
    }

    @Data
    /**
     * 特征规则
     */
    public static class KeyRule{
        public KeyRule(){}
        /** 协议id */
        @JsonProperty("pro_id")
        @JSONField(name="ProId")
        private Integer proId;

        /** 特征字 */
        @JSONField(name="Keyword")
        private String keyword;

        /** 大小写  0:区分 1:不区分 */
        @JSONField(name="IsCaseSensive")
        @JsonProperty("is_case_sensive")
        private Integer isCaseSensive;
    }

    @Data
    /**
     * 正则规则
     */
    public static class RegexRule{
        public RegexRule(){}
        /** 协议id */
        @JsonProperty("pro_id")
        @JSONField(name="ProId")
        private Integer proId;

        /** 正则表达式 */
        @JSONField(name="Regex")
        private String regex;

        /** 暂时未知 */
        @JSONField(name="Property")
        private Integer property;
    }

    @Data
    /**
     * 域名规则
     */
    public static class DomainRule{
        public DomainRule(){}

        /** 域名 */
        @JSONField(name="Domain")
        private String domain;

        /**  1:精确域名   2:n级域名 */
        @JSONField(name="Type")
        private Integer type;
    }

    @Data
    /**
     * 复杂规则
     */
    public static class ComplexRule{
        public ComplexRule(){}
        /** 协议类型 */
        private String type;
        /** 公式内容 */
        private Map<String,String> rule;
    }

    @Data
    /**
     * 端口操作  common字段
     */
    public static class PortRule{
        public PortRule(){}
        /** 小端口 */
        @JsonProperty("low_port")
        @JSONField(name="LowPort")
        private Integer lowPort;

        /** 大端口 */
        @JsonProperty("high_port")
        @JSONField(name="HightPort")
        private Integer highPort;

        /**  */
        @JSONField(name="Property")
        private Integer property;

        /** 1:单选客户端端口  2:单选服务器端口  3:全选 */
        @JSONField(name="Sign")
        private Integer sign;
    }

    @Data
    public static class IpV4{
        public IpV4(){}
        @JSONField(name="Ip")
        private String ip;
        @JSONField(name="Mask")
        private String mask;
    }

    //查看的字段
    @JsonProperty("total_sum_bytes")
    private Long totalSumBytes;

    @JsonProperty("last_size_time")
    private Integer lastSizeTime;

    @JsonProperty("created_time")
    private Integer createdTime;

    @JsonProperty("updated_time")
    private Integer updatedTime;

    @JsonProperty("lib_respond_session_end")
    private Long libRespondSessionEnd;

    @JsonProperty("lib_data_so")
    private String libDataSo;

    @JsonProperty("lib_data_conf")
    private String libDataConf;
}

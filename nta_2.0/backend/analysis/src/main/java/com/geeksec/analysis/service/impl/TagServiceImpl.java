package com.geeksec.analysis.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.dao.FeatureRuleDao;
import com.geeksec.analysis.dao.TagAttributeRateDao;
import com.geeksec.analysis.dao.TagInfoDao;
import com.geeksec.analysis.dao.ThSessionDao;
import com.geeksec.analysis.entity.FeatureRule;
import com.geeksec.analysis.entity.TbTagAttribute;
import com.geeksec.analysis.entity.TbTagAttributeRate;
import com.geeksec.analysis.entity.TbTagInfo;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.TagSearchCondition;
import com.geeksec.analysis.entity.vo.TagAttributeVo;
import com.geeksec.analysis.entity.vo.TagLibraryVo;
import com.geeksec.analysis.service.AggrTargetService;
import com.geeksec.analysis.service.TagService;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.enumeration.TagTargetEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.util.RedisUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description：
 */
@DS("nta-db")
@Service
public class TagServiceImpl implements TagService {

    @Autowired
    EsearchService esearchService;

    @Autowired
    FeatureRuleDao featureRuleDao;

    @Autowired
    ThSessionDao thSessionDao;

    @Autowired
    private TagInfoDao tagInfoDao;

    @Autowired
    private AggrTargetService aggrTargetService;

    // 产品ID 0.全部 1.探针 2.分析平台 3.其他
    @Value("${shield_pro_type}")
    private Integer shieldProType;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private TagAttributeRateDao tagAttributeRateDao;

    private static final Map<Integer, Object> ATTRIBUTE_TAG_MAP = new HashMap<Integer, Object>() {{
        put(1, "威胁");
        put(2, "功能描述");
        put(3, "合法性");
        put(4, "行为描述");
        put(5, "APT");
        put(6, "远程控制");
        put(7, "基础属性");
        put(8, "指纹描述");
        put(9, "代理");
        put(10, "加密流量检测");
    }};

    private static final Logger logger = LoggerFactory.getLogger(TagServiceImpl.class);

    @Override
    public ResultVo tagSearch(TagSearchCondition condition) {

        Integer userId = tokenService.getUserInfoByToken();
        condition.setUserId(userId);
        Map<String, Object> resultMap = new HashMap<>();

        // 判断查询方式是列表还是全量 list or all
        String searchType = condition.getSearchType().toLowerCase();

        if (StringUtils.isEmpty(searchType)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        // 每次查询时需要返回每个类型标签的数量
        Map<String, Object> tagCountMap = getTagCountBySearchName(condition);
        resultMap.put("count_map", tagCountMap);
        List<TagLibraryVo> tagVolist = new ArrayList<>();
        if (searchType.equals("list")) {
            // 列表查询,带分页总数
            Map<String, Object> pageResult = getTagList(condition);
            tagVolist = (List<TagLibraryVo>) pageResult.get("data");
            Integer total = (Integer) pageResult.get("total");
            resultMap.put("total", total);
        } else if (searchType.equals("all")) {
            // 全量查询
            tagVolist = getTagAll(condition);
        } else {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        resultMap.put("tag_data", tagVolist);
        return ResultVo.success(resultMap);
    }

    @Override
    public ResultVo addTag(JSONObject params) {

        Integer blackList = params.getInteger("black_list");
        Integer whiteList = params.getInteger("white_list");
        Integer tagTargetType = params.getInteger("tag_target_type");
        Integer tagAttributeId = params.getInteger("tag_attribute_id");

        // 新增的标签的tagId从100000开始
        // 先查询最大的tagId
        Integer maxTagId = tagInfoDao.getMaxTagId();
        if (maxTagId >= 100000) {
            maxTagId = maxTagId + 1;
        } else {
            maxTagId = 100000;
        }
        // 新增标签
        Integer userId = tokenService.getUserInfoByToken();
        TbTagInfo newTag = new TbTagInfo();
        newTag.setTagId(maxTagId);
        newTag.setTagText(params.getString("tag_text"));
        newTag.setTagExplain(params.getString("tag_explain"));
        newTag.setBlackList(blackList);
        newTag.setWhiteList(whiteList);
        newTag.setTagFamily(0);
        newTag.setTagAttr(0);
        newTag.setTagRemark(StringUtils.EMPTY);
        Long currentTime = System.currentTimeMillis() / 1000;
        newTag.setCreatedTime(currentTime);
        newTag.setLastCreatedTime(currentTime);
        //默认为会话规则标签,且启用
        newTag.setTagTargetType(tagTargetType);
        newTag.setTagType(1);
        newTag.setTagClass(1);
        newTag.setUserId(userId);
        Integer result = tagInfoDao.insert(newTag);
        if (result == 1) {
            if(tagAttributeId!=null){
                TbTagAttributeRate tbTagAttributeRate = new TbTagAttributeRate();
                tbTagAttributeRate.setTagId(newTag.getTagId());
                tbTagAttributeRate.setAttributeId(tagAttributeId);
                tagAttributeRateDao.insert(tbTagAttributeRate);
            }
            return ResultVo.success("新增标签成功！");
        } else {
            logger.error("新增标签失败！", params);
            return ResultVo.fail("新增标签失败！");
        }
    }

    @Override
    public ResultVo listTagAttribute() {
        List<TagAttributeVo> tagAttributeVoList = new ArrayList<>();
        List<TbTagAttribute> tbTagAttributeInfoList = tagInfoDao.listTagAttribute();

        Map<Integer, List<TbTagAttribute>> attributeMap = new HashMap<>();
        for (TbTagAttribute tbTagAttributeInfo : tbTagAttributeInfoList) {
            attributeMap
                    .computeIfAbsent(tbTagAttributeInfo.getTargetId(), k -> new ArrayList<>())
                    .add(tbTagAttributeInfo);
        }
        for (TagTargetEnum tag : TagTargetEnum.values()) {
            int targetId = tag.getTagTargetNum();
            TagAttributeVo tagAttributeVo = new TagAttributeVo();
            tagAttributeVo.setAttributeLevel(1);
            tagAttributeVo.setAttributeId(tag.getTagTargetNum());
            tagAttributeVo.setAttributeName(tag.getTarTargetName());
            List<TbTagAttribute> matchingInfos = attributeMap.get(targetId);
            if (matchingInfos != null) {
                List<TagAttributeVo> tagAttributeVoItemList = new ArrayList<>();
                for (TbTagAttribute tbTagAttributeInfo : matchingInfos) {
                    TagAttributeVo tagAttributeItemVo = new TagAttributeVo();
                    tagAttributeItemVo.setAttributeLevel(2);
                    tagAttributeItemVo.setAttributeId(tbTagAttributeInfo.getAttributeId());
                    tagAttributeItemVo.setAttributeName(tbTagAttributeInfo.getAttributeName());
                    tagAttributeVoItemList.add(tagAttributeItemVo);
                }
                tagAttributeVo.setTagAttributeVoList(tagAttributeVoItemList);
            }
            tagAttributeVoList.add(tagAttributeVo);
        }
        return ResultVo.success(tagAttributeVoList);
    }


    /**
     * 判断当前条件下不同类型的标签数量
     *
     * @return
     */
    private Map<String, Object> getTagCountBySearchName(TagSearchCondition condition) {

        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Map<String, Object>> labelsMap = new LinkedHashMap<>();

        String searchName = condition.getTagText();
        Long allCount = 0L;

        // 查询标签黑名单权值范围
        List<Integer> blackList = condition.getBlackWeight();
        Integer startBlack = blackList.get(0);
        Integer endBlack = blackList.get(1);
        // 查询标签白名单权值范围
        List<Integer> whiteList = condition.getWhiteWeight();
        Integer startWhite = whiteList.get(0);
        Integer endWhite = whiteList.get(1);

        // 目标类型标签数量统计
        Map<String, Long> targetCountMap = new HashMap<>();
        List<Map<String, Object>> targetList = tagInfoDao.
                getTargetCountBySearchName(searchName, startBlack, endBlack, startWhite, endWhite, shieldProType, condition.getUserId());
        for (Map<String, Object> targetMap : targetList) {
            Integer type = (Integer) targetMap.get("tag_target_type");
            Long count = (Long) targetMap.get("count_num");
            String targetName = TagTargetEnum.getTarTargetNameByTagTargetNum(type);
            if (targetName.equals("会话")) {
                // 会话中含有规则的数量
                Long ruleCount = featureRuleDao.selectCountByUserId(searchName, condition.getUserId());
                count = count + ruleCount;
            }
            targetCountMap.put(targetName, count);
            allCount += count;
        }
        Map<String, Object> allCountMap = new HashMap<>();
        allCountMap.put("count", allCount);
        allCountMap.put("children", new ArrayList<>());
        allCountMap.put("label", "全部");
        allCountMap.put("attr_type", 9999);
        labelsMap.put("ALL", allCountMap);

        // 细分类类型标签数量统计
        Map<String, Object> attributeCountMap = new HashMap<>();
        List<Map<String, Object>> attributeList = tagInfoDao.
                getAttributeCountBySearchName(searchName, startBlack, endBlack, startWhite, endWhite, shieldProType);
        for (Map<String, Object> attributeMap : attributeList) {
            String attributeName = (String) attributeMap.get("attributeName");
            Integer targetId = ((Long) attributeMap.get("targetId")).intValue();
            String targetName = TagTargetEnum.getTarTargetNameByTagTargetNum(targetId);
            Long count = (Long) attributeMap.get("count_num");
            attributeCountMap.put(attributeName, count);
            Map<String, Object> attributeTag = new HashMap<>();
            attributeTag.put("label", attributeName);
            attributeTag.put("count", count);
            int attributeId = ((Long) attributeMap.get("attributeId")).intValue();
            attributeTag.put("attr_type", attributeId);
            if (attributeId != 0){
                if (labelsMap.containsKey(targetName)){
                    Map<String, Object> attributeTags = labelsMap.get(targetName);
                    List<Map<String, Object>> attribute = (List<Map<String, Object>>) attributeTags.get("children");
                    attribute.add(attributeTag);
                    attributeTags.put("children", attribute);
                    labelsMap.put(targetName, attributeTags);
                }else {
                    List<Map<String, Object>> attributeTags = new ArrayList<>();
                    attributeTags.add(attributeTag);
                    Map<String, Object> targetMap = new HashMap<>();
                    targetMap.put("count", targetCountMap.get(targetName));
                    targetMap.put("children", attributeTags);
                    targetMap.put("label", targetName);
                    targetMap.put("attr_type", TagTargetEnum.getTarTargetNumByTagTargetName(targetName));
                    labelsMap.put(targetName, targetMap);
                }
            }
            //allCount += count;
        }
        for (String key : targetCountMap.keySet()) {
            if (!labelsMap.containsKey(key)){
                Map<String, Object> targetMap = new HashMap<>();
                targetMap.put("count", targetCountMap.get(key));
                targetMap.put("children", new ArrayList<>());
                targetMap.put("label", key);
                targetMap.put("attr_type", TagTargetEnum.getTarTargetNumByTagTargetName(key));
                labelsMap.put(key, targetMap);
            }
        }
        // 统计全部标签数量
        resultMap.put("attribute", attributeCountMap);
        resultMap.put("target", targetCountMap);

        resultMap.put("ALL", allCount);
        resultMap.put("labels", labelsMap.values());

        return resultMap;
    }

    /**
     * 当前条件下标签全量查询
     *
     * @param condition
     * @return
     */
    private List<TagLibraryVo> getTagAll(TagSearchCondition condition) {

        Map<String, Object> resultMap = new HashMap<>();

        //获取子分类，如果为空则进行全部标签查询
        Integer tagTargetType = condition.getTagTargetType();
        String searchName = condition.getTagText();
        Integer attributeId = condition.getTagAttributeId();
        // 查询标签黑名单权值范围
        List<Integer> blackList = condition.getBlackWeight();
        Integer startBlack = blackList.get(0);
        Integer endBlack = blackList.get(1);
        // 查询标签白名单权值范围
        List<Integer> whiteList = condition.getWhiteWeight();
        Integer startWhite = whiteList.get(0);
        Integer endWhite = whiteList.get(1);

        // 查询全部标签
        List<FeatureRule> ruleList = new ArrayList<>();
        try {
            List<TbTagInfo> tagList;
            if (attributeId != null) {
                // 细分类全量查询
                tagList = tagInfoDao.getTagListByCondition(searchName, attributeId, startBlack, endBlack, startWhite, endWhite, shieldProType);
            } else {
                tagList = tagInfoDao.queryList(searchName, startBlack, endBlack, startWhite, endWhite, tagTargetType, shieldProType, condition.getUserId());
                if (tagTargetType.equals(6) || tagTargetType.equals(9999)) {
                    // 规则标签属于会话类型进行查询，如果是其他类型则不进行查询
                    ruleList = featureRuleDao.queryList(searchName, condition.getUserId());
                    if (CollectionUtils.isEmpty(tagList) && CollectionUtils.isEmpty(ruleList)) {
                        return new ArrayList<>();
                    }
                    if (CollectionUtils.isNotEmpty(ruleList)) {
                        for (FeatureRule rule : ruleList) {
                            TbTagInfo tag = new TbTagInfo();
                            tag.setBlackList(rule.getRuleLevel());
                            tag.setTagExplain(rule.getRuleDesc());
                            tag.setTagId(rule.getRuleId());
                            tag.setTagTargetType(6);
                            tag.setWhiteList(0);
                            tag.setTagText("规则_" + rule.getRuleName());
                            tagList.add(tag);
                        }
                    }
                }
            }

            // 将TbTagInfo转为TagLibraryVo
            List<TagLibraryVo> tagVoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(tagList)) {
                for (TbTagInfo tag : tagList) {
                    TagLibraryVo tagVo = new TagLibraryVo();
                    BeanUtils.copyProperties(tag, tagVo);
                    tagVo.setAttributeName((String) ATTRIBUTE_TAG_MAP.get(attributeId));
                    tagVoList.add(tagVo);
                }
            }

            // 处理返回标签的标签等级
            handleTagLevel(tagVoList);
            return tagVoList;
        } catch (Exception e) {
            logger.error("查询标签库失败", e);
            resultMap.put("data", new ArrayList<>());
            resultMap.put("total", 0);
            return new ArrayList<>();
        }
    }


    /**
     * 当前列表下标签列表查询
     *
     * @param condition
     * @return
     */
    private Map<String, Object> getTagList(TagSearchCondition condition) {

        Map<String, Object> tagMap = new HashMap<>();

        Integer currentPage = condition.getCurrentPage();
        Integer pageSize = condition.getPageSize();
        Integer offset = (currentPage - 1) * pageSize;

        String searchName = condition.getTagText();
        // 查询标签黑名单权值范围
        List<Integer> blackList = condition.getBlackWeight();
        Integer startBlack = blackList.get(0);
        Integer endBlack = blackList.get(1);
        // 查询标签白名单权值范围
        List<Integer> whiteList = condition.getWhiteWeight();
        Integer startWhite = whiteList.get(0);
        Integer endWhite = whiteList.get(1);

        Integer tagTargetType = condition.getTagTargetType();
        Integer attributeId = condition.getTagAttributeId();
        try {
            // 列表需要通过二次查询，去走细分类的转换，也就是说，对于细分类的标签，需要通过attributeId进行关联查询
            List<TbTagInfo> tagList = new ArrayList<>();
            if (attributeId != null) {
                // 细分类精准查询
                tagList = tagInfoDao.getTagListByCondition(searchName, attributeId, startBlack, endBlack, startWhite, endWhite, shieldProType);
            } else {
                tagList = tagInfoDao.queryList(searchName, startBlack, endBlack, startWhite, endWhite, tagTargetType, shieldProType, condition.getUserId());
                List<FeatureRule> ruleList = new ArrayList<>();
                if (tagTargetType.equals(6) || tagTargetType.equals(9999)) {
                    // 规则标签属于会话类型进行查询，如果是其他类型则不进行查询
                    ruleList = featureRuleDao.queryList(searchName, condition.getUserId());
                    if (CollectionUtils.isEmpty(tagList) && CollectionUtils.isEmpty(ruleList)) {
                        tagMap.put("data", new ArrayList<>());
                        tagMap.put("total", 0);
                        return tagMap;
                    }
                    if (CollectionUtils.isNotEmpty(ruleList)) {
                        for (FeatureRule rule : ruleList) {
                            TbTagInfo tag = new TbTagInfo();
                            tag.setBlackList(rule.getRuleLevel());
                            tag.setTagExplain(rule.getRuleDesc());
                            tag.setTagId(rule.getRuleId());
                            tag.setTagTargetType(6);
                            tag.setWhiteList(0);
                            tag.setTagText("规则_" + rule.getRuleName());
                            tagList.add(tag);
                        }
                    }
                }
            }

            // 将TbTagInfo转为TagLibraryVo
            List<TagLibraryVo> tagVoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(tagList)) {
                for (TbTagInfo tag : tagList) {
                    TagLibraryVo tagVo = new TagLibraryVo();
                    BeanUtils.copyProperties(tag, tagVo);
                    tagVo.setAttributeName((String) ATTRIBUTE_TAG_MAP.get(attributeId));
                    tagVoList.add(tagVo);
                }
            }

            // 查询当前条件下的标签总数
            Integer total = tagVoList.size();
            // 进行分页处理
            if (offset >= tagList.size()) {
                return new HashMap<>();
            }

            int toIndex = Math.min(offset + pageSize, tagVoList.size());
            tagVoList = tagVoList.stream().skip(offset).limit(toIndex - offset).collect(Collectors.toList());

            // 处理返回标签的标签等级
            tagVoList = handleTagLevel(tagVoList);
            tagMap.put("data", tagVoList);
            tagMap.put("total", total);

            return tagMap;
        } catch (Exception e) {
            logger.error("查询标签库失败", e);
            return new HashMap<>();
        }

    }

    private List<TagLibraryVo> handleTagLevel(List<TagLibraryVo> tagList) {
        for (TagLibraryVo info : tagList) {
            int blackList = info.getBlackList();
            int whiteList = info.getWhiteList();
            if (blackList >= 1 && blackList <= 100) {
                if (whiteList != 100) {
                    if (blackList >= 80) {
                        info.setTagLevel("danger");
                    } else {
                        info.setTagLevel("warning");
                    }
                } else {
                    info.setTagLevel("positive");
                }
            } else if (whiteList >= 1 && whiteList <= 100) {
                if (whiteList == 100) {
                    info.setTagLevel("success");
                } else {
                    info.setTagLevel("positive");
                }
            } else {
                info.setTagLevel("info");
            }
        }
        return tagList;
    }

    @Override
    public void updateTagRecommend(List<AnalysisBaseCondition.QueryOb> query) {
        Integer userId = tokenService.getUserInfoByToken();
        List<String> tagIds = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(query)) {
            for (AnalysisBaseCondition.QueryOb queryOb : query) {
                List<AnalysisBaseCondition.SearchInfo> searchList = queryOb.getSearch();
                for (AnalysisBaseCondition.SearchInfo searchInfo : searchList) {
                    String target = searchInfo.getTarget();
                    List<String> val = searchInfo.getVal();
                    if (target.equals("Labels")) {
                        // 获取当前查询条件中的标签ID
                        for (String tagId : val) {
                            RedisUtil.handleScore(Constants.TAG_RECOMMEND_KEY + ":" + userId, tagId);
                            tagIds.add(tagId);
                        }
                        continue;
                    }
                }
            }
        }
    }

    @Override
    public ResultVo getRecommendTagList() {
        Integer userId = tokenService.getUserInfoByToken();
        // 获取使用频率最高的N个标签，N默认为10，使用的方式为redis的zset
        List<TagLibraryVo> hotTagVoList = new ArrayList<>();
        String hotTagKey = Constants.TAG_RECOMMEND_KEY + ":" + userId;
        if (RedisUtil.existKey(hotTagKey)) {
            Set<ZSetOperations.TypedTuple<String>> hotTagSet = RedisUtil.getTopTenWithScores(hotTagKey);
            List<String> tagList = hotTagSet.stream()
                    .map(ZSetOperations.TypedTuple::getValue)
                    .collect(Collectors.toList());
            List<TbTagInfo> hotTagInfoList = tagInfoDao.getTagInfoListByIds(tagList);
            hotTagVoList = transTag2Vo(hotTagInfoList,hotTagSet);
            // 通过CertTagInfo中的BlackList进行排序，黑名单的标签排在前面
            Collections.sort(hotTagVoList, new Comparator<TagLibraryVo>() {
                @Override
                public int compare(TagLibraryVo o1, TagLibraryVo o2) {
                    return o2.getUseNum() - o1.getUseNum();
                }
            });
        }
        return ResultVo.success(hotTagVoList);
    }

    /**
     * 转换标签的VO类型
     *
     * @param tagInfos
     * @return
     */
    private List<TagLibraryVo> transTag2Vo(List<TbTagInfo> tagInfos,Set<ZSetOperations.TypedTuple<String>> hotTagSet) {
        List<TagLibraryVo> tagVos = new ArrayList<>();
        for (TbTagInfo info : tagInfos) {
            int blackList = info.getBlackList();
            int whiteList = info.getWhiteList();
            TagLibraryVo vo = new TagLibraryVo();
            vo.setTagId(info.getTagId());
            vo.setTagText(info.getTagText());
            vo.setTagExplain(info.getTagExplain());
            vo.setBlackList(info.getBlackList());
            vo.setWhiteList(info.getWhiteList());
            vo.setTagTargetType(info.getTagTargetType());
            // 判断标签等级
            if (blackList >= 1 && blackList <= 100 && whiteList != 100) {
                if (blackList >= 80) {
                    vo.setTagLevel("danger");
                } else {
                    vo.setTagLevel("warning");
                }
            }
            if (whiteList >= 1 && whiteList <= 100 && blackList == 0) {
                if (whiteList == 100) {
                    vo.setTagLevel("success");
                } else {
                    vo.setTagLevel("positive");
                }
            }

            if (whiteList == 0 && blackList == 0) {
                vo.setTagLevel("info");
            }
            for (ZSetOperations.TypedTuple<String> tuple : hotTagSet) {
                if (String.valueOf(info.getTagId()).equals(tuple.getValue())) {
                    vo.setUseNum(tuple.getScore().intValue());
                }
            }
            tagVos.add(vo);
        }

        return tagVos;
    }

}

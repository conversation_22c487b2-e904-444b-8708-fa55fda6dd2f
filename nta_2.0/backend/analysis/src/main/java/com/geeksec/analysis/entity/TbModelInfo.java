package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * <AUTHOR>
 * @since 2022-08-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_model_info")
public class TbModelInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模型ID
     */
    @TableId("model_id")
    private Integer modelId;

    @TableField("model_name")
    private String modelName;

    /**
     * 算法
     */
    @TableField("model_algorithm")
    private String modelAlgorithm;

    /**
     * 描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 1 生效  0 失效
     */
    @TableField("state")
    private Integer state;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Integer updateTime;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Integer createdTime;


}

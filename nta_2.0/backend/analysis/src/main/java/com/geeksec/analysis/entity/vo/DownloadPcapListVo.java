package com.geeksec.analysis.entity.vo;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: GuanHao
 * @Date: 2022/5/17 16:12
 * @Description： <Functions List>
 */
@Data
public class DownloadPcapListVo {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("query")
    private String query;

    @JsonProperty("query_array")
    private JSONArray queryArray;

    @JsonProperty("path")
    private String path;

    @JsonProperty("type")
    private int type;

    @JsonProperty("created_time")
    private int createdTime;

    @JsonProperty("state")
    private int state;

    @JsonProperty("front_num")
    private int frontNum;

    @JsonProperty("end_time")
    private int endTime;

    @JsonProperty("status")
    private int status;
}

package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.QueryTemplate;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 证书检索模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface QueryTemplateDao extends BaseMapper<QueryTemplate> {

    /**
     * 删除所有的模板记录
     */
    @Update("truncate table tb_query_template")
    void deleteAll();
}

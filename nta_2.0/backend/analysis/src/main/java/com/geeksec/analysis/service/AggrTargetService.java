package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.CommunicationCondition;
import com.geeksec.entity.common.ResultVo;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @Description：IP/域名/证书/指纹聚合查询
 */
public interface AggrTargetService {

    /**
     * 查询IP列表信息
     * @param condition
     * @return
     */
    ResultVo getAggrIpList(AnalysisBaseCondition condition);

    /**
     * 查询域名列表信息
     * @param condition
     * @return
     */
    ResultVo getAggrDomainList(AnalysisBaseCondition condition) throws UnsupportedEncodingException;

    /**
     * 查询证书域名列表信息
     * @param condition
     * @return
     */
    ResultVo getAggrCertList(AnalysisBaseCondition condition);

    /**
     * 获取实体相关标签数据
     * @param condition
     * @return
     */
    BoolQueryBuilder getTargetTagCondition(AnalysisBaseCondition condition, String targetType,BoolQueryBuilder queryBuilder);

    /**
     * 获取标签查询列表数据
     * @param condition
     * @return
     */
    ResultVo getAggrFingerList(AnalysisBaseCondition condition) throws UnsupportedEncodingException;

    /**
     * 获取通信信息桑基图
     * @param condition
     * @return
     */
    ResultVo getCommunicationSankey(CommunicationCondition condition);
}

package com.geeksec.analysis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.storage.*;
import com.geeksec.analysis.entity.vo.FingerprintVo;
import com.geeksec.analysis.service.NebulaRelatedService;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.general.service.EsearchService;
import com.geeksec.ngbatis.service.*;
import com.geeksec.ngbatis.vo.*;
import com.geeksec.util.IpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class NebulaRelatedServiceImpl implements NebulaRelatedService {

    private static final Logger logger = LoggerFactory.getLogger(NebulaRelatedServiceImpl.class);

    @Autowired
    private EsearchService esearchService;
    @Autowired
    private IpService ipService;
    @Autowired
    private DomainService domainService;
    @Autowired
    private CertService certService;
    @Autowired
    private SslFingerService sslFingerService;
    @Autowired
    private HasLabelService hasLabelService;

    /**
     * 单个and组合中的各项数组为must(and)关系，同一数组中的不同数值为should关系
     *
     * @param tagListMap
     * @param boolQueryBuilder
     * @param type
     * @return
     */
    @Override
    public Map<String, Object> handleTargetTagQuery(Map<String, List<List<String>>> tagListMap, BoolQueryBuilder boolQueryBuilder, String type) {

        Map<String, Object> resultMap = new HashMap<>();

        // 与目标相关标签集合 [[12,11],[5001]]
        List<List<String>> allQueryLabels = tagListMap.get("target");
        List<List<String>> allNotQueryLabels = tagListMap.get("targetNot");

        if (CollectionUtils.isEmpty(allQueryLabels) && CollectionUtils.isEmpty(allNotQueryLabels)) {
            //若不存在，返回原boolQueryBuilder和空查询字符串
            resultMap.put("queryBuilder", boolQueryBuilder);
            resultMap.put("targetFilterMap", new HashMap<>());
            resultMap.put("has_target", true);
            return resultMap;
        }

        // 符合与条件的目标集合
        List<List<String>> ipList = new ArrayList<>();
        List<List<String>> domainList = new ArrayList<>();
        List<List<String>> fingerList = new ArrayList<>();

        logger.info("根据目标标签进行Nebula查询,tags-->{},notTags-->{}", allQueryLabels, allNotQueryLabels);

        // 根据标签进行关联查询，查询当前符合标签条件的实体
        if (CollectionUtils.isNotEmpty(allQueryLabels)) {
            for (List<String> shouldQueryLabels : allQueryLabels) {
                List<String> shouldIpList = new ArrayList<>();
                List<String> shouldDomainList = new ArrayList<>();
                List<String> shouldFingerList = new ArrayList<>();
                // 将查询出的列表进行分组
                List<HasLabelVertexVo> hasLabelVertexVoList = hasLabelService.listTagVertexByTagIds(shouldQueryLabels);
                for(HasLabelVertexVo hasLabelVertexVo : hasLabelVertexVoList){
                    Set<Map<String,Object>> tagList = new HashSet<>(hasLabelVertexVo.getTagList());
                    for(Map<String,Object> target : tagList){
                        // 此处判断当前实体为哪种类型
                        if (target.containsKey("ip_addr")) {
                            String ipAddr = (String) target.get("ip_addr");
                            shouldIpList.add(ipAddr);
                        }
                        if (target.containsKey("domain_addr")) {
                            String domainAddr = (String) target.get("domain_addr");
                            shouldDomainList.add(domainAddr);
                        }
                        if (target.containsKey("finger_id")) {
                            String fingerId = (String) target.get("finger_id");
                            shouldFingerList.add(fingerId);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(shouldIpList)) {
                    ipList.add(shouldIpList);
                }
                if (CollectionUtils.isNotEmpty(shouldDomainList)) {
                    domainList.add(shouldDomainList);
                }
                if (CollectionUtils.isNotEmpty(shouldFingerList)) {
                    fingerList.add(shouldFingerList);
                }
            }

            // 遍历整个List，进行should和must的匹配
            switch (type) {
                case "IP":
                    for (List<String> ips : ipList) {
                        BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                        shouldBuilder.should(QueryBuilders.termsQuery("sIp", ips));
                        shouldBuilder.should(QueryBuilders.termsQuery("dIp", ips));
                        boolQueryBuilder.must(shouldBuilder);
                    }
                    break;
                case "DOMAIN":
                    for (List<String> domains : domainList) {
                        BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                        shouldBuilder.should(QueryBuilders.termsQuery("SSL.CH_ServerName.keyword", domains));
                        shouldBuilder.should(QueryBuilders.termsQuery("HTTP.Host.keyword", domains));
                        shouldBuilder.should(QueryBuilders.termsQuery("DNS.Domain.keyword", domains));
                        boolQueryBuilder.must(shouldBuilder);
                    }
                    break;
                case "SSLFINGER":
                    for (List<String> fingers : fingerList) {
                        BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                        shouldBuilder.should(QueryBuilders.termsQuery("sSSLFinger.keyword", fingers));
                        shouldBuilder.should(QueryBuilders.termsQuery("dSSLFinger.keyword", fingers));
                        boolQueryBuilder.must(shouldBuilder);
                    }
                case "HTTP":
                    if (CollectionUtils.isNotEmpty(ipList)) {
                        for (List<String> ips : ipList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("sIp", ips));
                            shouldBuilder.should(QueryBuilders.termsQuery("dIp", ips));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(domainList)) {
                        for (List<String> domains : domainList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("Host.keyword", domains));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    break;
                case "DNS":
                    if (CollectionUtils.isNotEmpty(ipList)) {
                        for (List<String> ips : ipList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("sIp", ips));
                            shouldBuilder.should(QueryBuilders.termsQuery("dIp", ips));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(domainList)) {
                        for (List<String> domains : domainList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("Domain.keyword", domains));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    break;
                case "SSL":
                    if (CollectionUtils.isNotEmpty(ipList)) {
                        for (List<String> ips : ipList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("sIp", ips));
                            shouldBuilder.should(QueryBuilders.termsQuery("dIp", ips));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(domainList)) {
                        for (List<String> domains : domainList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("CH_ServerName.keyword", domains));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(fingerList)) {
                        for (List<String> fingers : fingerList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("sSSLFinger.keyword", fingers));
                            shouldBuilder.should(QueryBuilders.termsQuery("dSSLFinger.keyword", fingers));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    break;
                case "ALL":
                    // 会话查询 元数据查询 判断全部
                    if (CollectionUtils.isNotEmpty(ipList)) {
                        for (List<String> ips : ipList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("sIp", ips));
                            shouldBuilder.should(QueryBuilders.termsQuery("dIp", ips));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(domainList)) {
                        for (List<String> domains : domainList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("SSL.CH_ServerName.keyword", domains));
                            shouldBuilder.should(QueryBuilders.termsQuery("HTTP.Host.keyword", domains));
                            shouldBuilder.should(QueryBuilders.termsQuery("DNS.Domain.keyword", domains));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(fingerList)) {
                        for (List<String> fingers : fingerList) {
                            BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                            shouldBuilder.should(QueryBuilders.termsQuery("sSSLFinger.keyword", fingers));
                            shouldBuilder.should(QueryBuilders.termsQuery("dSSLFinger.keyword", fingers));
                            boolQueryBuilder.must(shouldBuilder);
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        List<String> excludeIpList = new ArrayList<>();
        List<String> excludeDomainList = new ArrayList<>();
        List<String> excludeFingerList = new ArrayList<>();

        // 判断反选的not选项，进行实体剔除
        if (CollectionUtils.isNotEmpty(allNotQueryLabels)) {
            // 注入所有的not标签的相关目标标签
            List<String> notLabels = new ArrayList<>();
            for (List<String> labels : allNotQueryLabels) {
                notLabels.addAll(labels);
            }
            List<HasLabelVertexVo> hasLabelVertexVoList = hasLabelService.listTagVertexByTagIds(notLabels);
            for(HasLabelVertexVo hasLabelVertexVo : hasLabelVertexVoList) {
                Set<Map<String, Object>> tagList = new HashSet<>(hasLabelVertexVo.getTagList());
                for (Map<String, Object> target : tagList) {
                    // 此处判断当前实体为哪种类型
                    if (target.containsKey("ip_addr")) {
                        String ipAddr = (String) target.get("ip_addr");
                        excludeIpList.add(ipAddr);
                    }
                    if (target.containsKey("domain_addr")) {
                        String domainAddr = (String) target.get("domain_addr");
                        excludeDomainList.add(domainAddr);
                    }
                    if (target.containsKey("finger_id")) {
                        String fingerId = (String) target.get("finger_id");
                        excludeFingerList.add(fingerId);
                    }
                }
            }
            // 对所有上述目标进行目标剔除
            if (CollectionUtils.isNotEmpty(ipList) && CollectionUtils.isNotEmpty(excludeIpList)) {
                for (List<String> ips : ipList) {
                    ips.removeAll(excludeIpList);
                }
            }
            if (CollectionUtils.isNotEmpty(domainList) && CollectionUtils.isNotEmpty(excludeDomainList)) {
                for (List<String> domains : domainList) {
                    domains.removeAll(excludeDomainList);
                }
            }
            if (CollectionUtils.isNotEmpty(fingerList) && CollectionUtils.isNotEmpty(excludeFingerList)) {
                for (List<String> fingers : fingerList) {
                    fingers.removeAll(excludeFingerList);
                }
            }


            BoolQueryBuilder shouldNotBuilder = new BoolQueryBuilder();
            switch (type) {
                case "IP":
                    shouldNotBuilder.should(QueryBuilders.termsQuery("sIp", excludeIpList));
                    shouldNotBuilder.should(QueryBuilders.termsQuery("dIp", excludeIpList));
                    break;
                case "DOMAIN":
                    shouldNotBuilder.should(QueryBuilders.termsQuery("SSL.CH_ServerName.keyword", excludeDomainList));
                    shouldNotBuilder.should(QueryBuilders.termsQuery("HTTP.Host.keyword", excludeDomainList));
                    shouldNotBuilder.should(QueryBuilders.termsQuery("DNS.Domain.keyword", excludeDomainList));
                    break;
                case "SSLFINGER":
                    shouldNotBuilder.should(QueryBuilders.termsQuery("sSSLFinger.keyword", excludeFingerList));
                    shouldNotBuilder.should(QueryBuilders.termsQuery("dSSLFinger.keyword", excludeFingerList));
                    break;
                case "HTTP":
                    if (CollectionUtils.isNotEmpty(ipList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("sIp", excludeIpList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("dIp", excludeIpList));
                    }
                    if (CollectionUtils.isNotEmpty(excludeDomainList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("SSL.CH_ServerName.keyword", excludeDomainList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("HTTP.Host.keyword", excludeDomainList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("DNS.Domain.keyword", excludeDomainList));
                    }
                    break;
                case "DNS":
                    if (CollectionUtils.isNotEmpty(excludeIpList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("sIp", excludeIpList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("dIp", excludeIpList));
                    }
                    if (CollectionUtils.isNotEmpty(excludeDomainList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("SSL.CH_ServerName.keyword", excludeDomainList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("HTTP.Host.keyword", excludeDomainList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("DNS.Domain.keyword", excludeDomainList));
                    }
                    break;
                case "SSL":
                    if (CollectionUtils.isNotEmpty(excludeIpList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("sIp", excludeIpList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("dIp", excludeIpList));
                    }
                    if (CollectionUtils.isNotEmpty(excludeDomainList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("CH_ServerName.keyword", excludeDomainList));
                    }
                    if (CollectionUtils.isNotEmpty(excludeFingerList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("sSSLFinger.keyword", excludeFingerList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("dSSLFinger.keyword", excludeFingerList));
                    }
                    break;
                case "ALL":
                    // 会话查询 元数据查询 判断全部
                    if (CollectionUtils.isNotEmpty(excludeIpList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("sIp", excludeIpList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("dIp", excludeIpList));
                    }
                    if (CollectionUtils.isNotEmpty(excludeDomainList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("SSL.CH_ServerName.keyword", excludeDomainList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("HTTP.Host.keyword", excludeDomainList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("DNS.Domain.keyword", excludeDomainList));
                    }
                    if (CollectionUtils.isNotEmpty(excludeFingerList)) {
                        shouldNotBuilder.should(QueryBuilders.termsQuery("sSSLFinger.keyword", excludeFingerList));
                        shouldNotBuilder.should(QueryBuilders.termsQuery("dSSLFinger.keyword", excludeFingerList));
                    }
                    break;
                default:
                    break;
            }
            if (shouldNotBuilder.hasClauses()) {
                boolQueryBuilder.mustNot(shouldNotBuilder);
            }
        }
        resultMap.put("queryBuilder", boolQueryBuilder);

        // 目标过滤Set集合
        Map<String, Set<String>> targetFilterMap = new HashMap<>();
        Set<String> ipSet = new HashSet<>();
        Set<String> domainSet = new HashSet<>();
        Set<String> fingerSet = new HashSet<>();

        if (CollectionUtils.isNotEmpty(ipList)) {
            for (List<String> ips : ipList) {
                Set<String> singleSet = new HashSet<>(ips);
                ipSet.addAll(singleSet);
            }
            targetFilterMap.put("IP", ipSet);
        }
        if (CollectionUtils.isNotEmpty(domainList)) {
            for (List<String> domains : domainList) {
                Set<String> singleSet = new HashSet<>(domains);
                domainSet.addAll(singleSet);
            }
            targetFilterMap.put("DOMAIN", domainSet);
        }
        if (CollectionUtils.isNotEmpty(fingerList)) {
            for (List<String> fingers : fingerList) {
                Set<String> singleSet = new HashSet<>(fingers);
                fingerSet.addAll(singleSet);
            }
            targetFilterMap.put("FINGER", fingerSet);
        }

        resultMap.put("targetFilterMap", targetFilterMap);
        resultMap.put("has_target", determineHasTarget(
                targetFilterMap,
                allQueryLabels,
                excludeIpList,
                excludeDomainList,
                excludeFingerList
        ));
        return resultMap;
    }

    private boolean determineHasTarget(Map<String, ?> targetFilterMap,
                                       List<?> allQueryLabels,
                                       List<?>... excludeLists) {

        if (MapUtils.isNotEmpty(targetFilterMap)) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(allQueryLabels) && MapUtils.isEmpty(targetFilterMap)) {
            return false;
        }

        return Stream.of(excludeLists)
                .anyMatch(CollectionUtils::isNotEmpty);
    }

    @Override
    public List<IPListVo> getIpListNebulaData(List<IpSortVo> allIpList, List<String> ipStrList) {

        List<IPListVo> resultList = new ArrayList<>();
        Map<String, IPListVo> map = new ConcurrentHashMap(ipStrList.size());
        List<String> ips = new ArrayList<>();
        for (IpSortVo ipSortVo : allIpList) {
            IPListVo ipListVo = new IPListVo();
            Set<Long> labels = new HashSet<>();
            ipListVo.setLabels(labels);
            resultList.add(ipListVo);
            String ip = ipSortVo.getIp();
            ipListVo.setIp(ip);
            ipListVo.setSessionCount(ipSortVo.getCount());
            map.put(ip, ipListVo);
            ips.add(ip);
        }
        if(CollectionUtil.isEmpty(ipStrList)){
            return resultList;
        }

        delUnionAlarm(allIpList, ipStrList, map);

        // 全局开放服务(应用服务-->IP 服务归属)
        List<IpCountVo> appServerIpCountVoList = ipService.countIpByAppServerEdge(ips);
        for(IpCountVo ipCountVo : appServerIpCountVoList){
            IPListVo ipListVo = map.get(ipCountVo.getIpAddr());
            ipListVo.setOpenServiceNum(ipCountVo.getCount());
        }

        //全局访问服务(IP->应用服务 访问服务)
        List<IpCountVo> clientAppIpCountVoList = ipService.countIpByClientAppEdge(ips);
        for(IpCountVo ipCountVo : clientAppIpCountVoList){
            IPListVo ipListVo = map.get(ipCountVo.getIpAddr());
            ipListVo.setAccessServiceNum(ipCountVo.getCount());
        }

        //威胁权重(ip表的black_list)、地理位置(city,county;也是ip对象)  ip点
        List<IpVo> ipVoList = ipService.listByIps(ips);
        for(IpVo ipVo : ipVoList){
            IPListVo ipListVo = map.get(ipVo.getIp());
            ipListVo.setBlackList(ipVo.getBlackList());
            ipListVo.setCity(ipVo.getCity());
            ipListVo.setCountry(ipVo.getCountry());
            ipListVo.setStartTimeMin(ipVo.getFirstTime());
            ipListVo.setStartTimeMax(ipVo.getLastTime());
            ipListVo.setSPayloadBytes(ipVo.getSendBytes());
            ipListVo.setDPayloadBytes(ipVo.getRecvBytes());
        }

        //标签   IP--标签-->具有标签
        List<IpHasLabelVo> ipHasLabelVoList = ipService.listHasLabelByIps(ips);
        for(IpHasLabelVo ipHasLabelVo : ipHasLabelVoList){
            IPListVo ipListVo = map.get(ipHasLabelVo.getIpAddr());
            Set<Long> labels = new HashSet<>(ipHasLabelVo.getLabels());
            ipListVo.setLabels(labels);
        }

        //域名  域名很多情况IP-域名->N种边   域名--IP-->N种边  双向
        List<IpCountVo> domainIpCountVoList = ipService.countIpByDomainEdge(ips);
        for(IpCountVo ipCountVo : domainIpCountVoList){
            IPListVo ipListVo = map.get(ipCountVo.getIpAddr());
            ipListVo.setDomainNum(Math.toIntExact(ipCountVo.getCount()));
        }

        //关联证书  IP--证书-->N个边
        List<IpCountVo> certIpCountVoList = ipService.countIpByCertEdge(ips);
        for(IpCountVo ipCountVo : certIpCountVoList){
            IPListVo ipListVo = map.get(ipCountVo.getIpAddr());
            ipListVo.setCertNum(Math.toIntExact(ipCountVo.getCount()));
        }
        return resultList;
    }

    @Override
    public PageResultVo<DomainListVo> getDomainListNebulaData
            (List<DomainSortVo> allDomainList, PageResultVo<DomainListVo> result) throws UnsupportedEncodingException {

        List<String> domains = new ArrayList<>();
        Map<String, DomainListVo> dataMap = new HashMap<>(allDomainList.size());
        List<DomainListVo> voList = new ArrayList<>();
        Map<String, Set<String>> ipsMap = new HashMap<>();
        for (DomainSortVo domainSortVo : allDomainList) {
            String domain = domainSortVo.getDomain();
            long count = domainSortVo.getCount();
            DomainListVo domainListVo = new DomainListVo();
            domainListVo.setDomain(domain);
            domainListVo.setCount(count);
            // 由于domain存在长度超过200的存在，会导致vid查询异常，这个地方的domain转换为MD5的vid查询
            if (domain.length() > 200) {
                domain = SecureUtil.md5(domain);
            }
            domains.add(domain);
            dataMap.put(domain, domainListVo);
            voList.add(domainListVo);
            //初始化标签
            Set<Long> labels = new HashSet<>();
            Set<Long> fLabels = new HashSet<>();
            domainListVo.setFLabels(fLabels);
            domainListVo.setLabels(labels);
            //初始化反查ip
            Set<String> ips = new HashSet<>();
            domainListVo.setRelatedIps(ips);
            //后续放置访问ips，求ips的热度
            Set<String> reqIps = new HashSet<>();
            ipsMap.put(domain, reqIps);
            //初始化回应类型
            Set<String> respondTypes = new HashSet<>();
            domainListVo.setRespondTypes(respondTypes);
            //初始化 出现位置对象
            DomainListVo.Location location = new DomainListVo.Location();
            location.setCert(false);
            location.setHttp(false);
            location.setSsl(false);
            location.setDns(false);
            domainListVo.setLocation(location);
        }
        String sql = "";
        //域名对象
        List<DomainVo> domainVoList = domainService.listByDomains(domains);
        for(DomainVo domainVo : domainVoList){
            String domain = domainVo.getDomainAddr();
            if (domain.length() > 200){
                domain = SecureUtil.md5(domain);
            }
            DomainListVo domainListVo = dataMap.get(domain);
            domainListVo.setBlackList(domainVo.getBlackList());
            domainListVo.setFirstTime(domainVo.getFirstTime());
            domainListVo.setLastTime(domainVo.getLastTime());
            domainListVo.setWhoIs(domainVo.getWhoIs());
        }
        //自身的标签  标签=锚域名的标签 + 自身的标签
        List<DomainHasLabelVo> domainHasLabelVoList = domainService.listHasLabelByDomains(domains);
        for(DomainHasLabelVo domainHasLabelVo : domainHasLabelVoList){
            DomainListVo domainListVo = dataMap.get(domainHasLabelVo.getDomainAddr());
            Set<Long> labels = new HashSet<>(domainHasLabelVo.getLabels());
            domainListVo.setLabels(labels);
        }
        //锚域名
        List<FDomainVo> fDomainVoList = domainService.listFDomainByDomains(domains);
        for(FDomainVo fDomainVo : fDomainVoList){
            String domain = fDomainVo.getDomainAddr();
            if (domain.length() > 200){
                domain = SecureUtil.md5(domain);
            }
            DomainListVo domainListVo = dataMap.get(domain);
            domainListVo.setFDomain(fDomainVo.getFdomainAddr());
            //锚域名标签和兄弟域名都是以锚域名作查询    标签=锚域名的标签 + 自身的标签
            //锚域名标签
            DomainHasLabelVo domainHasLabelVo = domainService.getHasLabelByDomain(fDomainVo.getFdomainAddr());
            if(domainHasLabelVo!=null){
                Set<Long> flabels = new HashSet<>(domainHasLabelVo.getLabels());
                domainListVo.setFLabels(flabels);
            }
            //兄弟域名
            DomainCountVo domainCountVo = domainService.countBrotherNumByDomain(fDomainVo.getFdomainAddr());
            if(domainCountVo!=null && domainCountVo.getCount()!=0){
                domainListVo.setBrotherNum(domainCountVo.getCount()-1);
            }
        }
        //反查IP  domain-->ip 域名指向的IP parse_to and cname_result
        List<DomainRelatedIpsVo> domainRelatedIpsVoList = domainService.listRelatedIpsByDomains(domains);
        for(DomainRelatedIpsVo domainRelatedIpsVo : domainRelatedIpsVoList){
            DomainListVo domainListVo = dataMap.get(domainRelatedIpsVo.getDomainAddr());
            Set<String> relatedIps = new HashSet<>(domainRelatedIpsVo.getIpList());
            domainListVo.setRelatedIps(relatedIps);
        }
        //CName查询该域名数量  域名-域名->cname   是当前域名作为终点
        List<DomainCountVo> cnameDomainCountVoList = domainService.countCnameDomainsByDomains(domains);
        for(DomainCountVo domainCountVo : cnameDomainCountVoList){
            DomainListVo domainListVo = dataMap.get(domainCountVo.getDomainAddr());
            int num = domainCountVo.getCount();
            domainListVo.setCnameToMe(num);
            if (num > 0) {
                DomainListVo.Location location = domainListVo.getLocation();
                location.setDns(true);
            }
        }
        List<DomainCountVo> cnamePointDomainCountVoList = domainService.countCnamePointDomainsByDomains(domains);
        for(DomainCountVo domainCountVo : cnamePointDomainCountVoList){
            DomainListVo domainListVo = dataMap.get(domainCountVo.getDomainAddr());
            int num = domainCountVo.getCount();
            domainListVo.setCnameToOther(num);
            //回应类型  具有cname的边，类型加 CNAME
            Set<String> respondTypes = domainListVo.getRespondTypes();
            if (num > 0) {
                respondTypes.add("CNAME");
            }
        }
        //查询热度  IP--域名——>HTTP访问和SSL访问  查找请求该域名的ips
        List<DomainRelatedIpsVo> requestDomainIpsVoList = domainService.listRequestDomainIpsByDomains(domains);
        for(DomainRelatedIpsVo domainRelatedIpsVo : requestDomainIpsVoList){
            Set<String> ips = ipsMap.get(domainRelatedIpsVo.getDomainAddr());
            ips.addAll(domainRelatedIpsVo.getIpList());
        }

        //回应类型  具有cname的边，类型加 CNAME  parse_to：IP4:A  IP6:AAAA
        List<DomainRelatedIpsVo> responseTypeIpsVoList = domainService.listResponseTypeIpsByDomains(domains);
        for(DomainRelatedIpsVo domainRelatedIpsVo : responseTypeIpsVoList){
            DomainListVo domainListVo = dataMap.get(domainRelatedIpsVo.getDomainAddr());
            Set<String> respondTypes = domainListVo.getRespondTypes();
            Set<String> relatedIps = new HashSet<>(domainRelatedIpsVo.getIpList());
            for (String ip : relatedIps) {
                if (IpUtils.isIpv4Str(ip)) {
                    respondTypes.add("A");
                } else if (IpUtils.isIpv6Str(ip)) {
                    respondTypes.add("AAA");
                } else {
                    respondTypes.add(StringUtils.EMPTY);
                }
            }
        }
        //出现位置  DNS:dns_server_domain/cname   server_ssl_connect_domain   server_http_connect_domain 域名作终点
        //域名-证书  是域名作起点
        List<DomainCountVo> dnsServerDomainCountVoList = domainService.countDnsServerDomainByDomains(domains);
        for(DomainCountVo domainCountVo : dnsServerDomainCountVoList){
            DomainListVo domainListVo = dataMap.get(domainCountVo.getDomainAddr());
            int num = domainCountVo.getCount();
            if (num > 0) {
                DomainListVo.Location location = domainListVo.getLocation();
                location.setDns(true);
            }
        }

        List<DomainCountVo> serverSslConnectDomainCountVoList = domainService.countServerSslConnectDomainByDomains(domains);
        for(DomainCountVo domainCountVo : serverSslConnectDomainCountVoList){
            DomainListVo domainListVo = dataMap.get(domainCountVo.getDomainAddr());
            int num = domainCountVo.getCount();
            if (num > 0) {
                DomainListVo.Location location = domainListVo.getLocation();
                location.setSsl(true);
            }
        }

        List<DomainCountVo> serverHttpConnectDomainCountVoList = domainService.countServerHttpConnectDomainByDomains(domains);
        for(DomainCountVo domainCountVo : serverHttpConnectDomainCountVoList){
            DomainListVo domainListVo = dataMap.get(domainCountVo.getDomainAddr());
            int num = domainCountVo.getCount();
            if (num > 0) {
                DomainListVo.Location location = domainListVo.getLocation();
                location.setHttp(true);
            }
        }

        List<DomainCountVo> sniBindCountVoList = domainService.countSniBindByDomains(domains);
        for(DomainCountVo domainCountVo : sniBindCountVoList){
            DomainListVo domainListVo = dataMap.get(domainCountVo.getDomainAddr());
            int num = domainCountVo.getCount();
            if (num > 0) {
                DomainListVo.Location location = domainListVo.getLocation();
                location.setCert(true);
            }
        }

        // 关联告警
        for (Map.Entry<String, DomainListVo> entry : dataMap.entrySet()) {
            String domainAddr = entry.getKey();
            SearchSourceBuilder alarmSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("targets.name.keyword", domainAddr));
            boolQueryBuilder.must(QueryBuilders.termQuery("targets.type.keyword", "domain"));

            alarmSourceBuilder.query(boolQueryBuilder);
            CountRequest countRequest = new CountRequest(new String[]{"alarm*"}, alarmSourceBuilder);
            CountResponse countResponse = esearchService.esSearchForCount(countRequest);
            DomainListVo vo = entry.getValue();
            vo.setAlarmNum(countResponse.getCount());
        }

        //组装个别字段
        for (DomainListVo domainListVo : voList) {
            String domain = domainListVo.getDomain();
            Set<String> ips = ipsMap.get(domain);
            if (ips != null && ips.size() > 0) {
                domainListVo.setIpHeart(IpUtils.ipHotCrc(new ArrayList<>(ips)));
            }
        }

        result.setRecords(voList);
        return result;
    }

    @Override
    public List<CertListVo> getCertListNebulaData(List<CertListVo> allCertList, List<String> certStrList) {

        Map<String, CertListVo> certMaps = new HashMap<>();
        Map<String, Set<String>> clientIpsMap = new HashMap<>();
        for (CertListVo certListVo : allCertList) {
            String cert = certListVo.getCert();
            certMaps.put(cert, certListVo);
            //初始化集合字段
            Set<Integer> labels = new HashSet<>();
            certListVo.setLabels(labels);
            //客户端热度所需ip集合
            Set<String> clientIps = new HashSet<>();
            clientIpsMap.put(cert, clientIps);
            //关联叶子证书
            delUnionSunCert(cert, certListVo);
            // 关联证书额外信息（使用者 颁发者 告警日期等）
            delCertInfo(cert, certListVo);
        }

        // 关联告警统一查询
        delUnionAlarm2(allCertList, certStrList, certMaps);

        //查证书点
        List<CertVo> certVoList = certService.listByCerts(certStrList);
        for(CertVo certVo : certVoList){
            CertListVo certListVo = certMaps.get(certVo.getCertId());
            certListVo.setFirstTime(certVo.getFirstTime());
            certListVo.setLastTime(certVo.getLastTime());
        }

        //客户端热度
        List<CertRelatedIpsVo> certRelatedIpsVoList = certService.listRelatedIpsByCerts(certStrList);
        for(CertRelatedIpsVo certRelatedIpsVo : certRelatedIpsVoList){
            String certId = certRelatedIpsVo.getCertId();
            CertListVo certListVo = certMaps.get(certId);
            Set<String> clientIpsByCert = clientIpsMap.get(certId);
            Set<String> ipVWList = new HashSet<>(certRelatedIpsVo.getIpList());
            clientIpsByCert.addAll(ipVWList);
            if (CollectionUtil.isNotEmpty(clientIpsByCert)) {
                certListVo.setClientHeat(IpUtils.ipHotCrc(new ArrayList<>(clientIpsByCert)));
            }
        }
        //关联域名
        List<CertCountVo> domainNumCountVoList = certService.countDomainNumByCerts(certStrList);
        for(CertCountVo certCountVo : domainNumCountVoList){
            CertListVo certListVo = certMaps.get(certCountVo.getCertId());
            certListVo.setDomainNum(certCountVo.getCount());
        }

        //关联服务器IP个数
        List<CertCountVo> serverIpNumCountVoList = certService.countServerIpNumByCerts(certStrList);
        for(CertCountVo certCountVo : serverIpNumCountVoList){
            CertListVo certListVo = certMaps.get(certCountVo.getCertId());
            certListVo.setServerIpNum(certCountVo.getCount());
        }

        //关联TLS指纹 实际查ssl指纹
        List<CertCountVo> sslIpNumCountVoList = certService.countSslIpNumByCerts(certStrList);
        for(CertCountVo certCountVo : sslIpNumCountVoList){
            CertListVo certListVo = certMaps.get(certCountVo.getCertId());
            certListVo.setSslIpNum(certCountVo.getCount());
        }

        return allCertList;
    }

    @Override
    public List<FingerprintVo> getFingerListNebulaData
            (List<String> indexNames, List<FingerprintVo> fingerList, AnalysisBaseCondition condition){

        List<String> fingerAllList = fingerList.stream().distinct().map(FingerprintVo::getFingerprint).collect(Collectors.toList());

        // 指纹描述 Nebula, 若已存在，则不再做查询
        Map<String, Map<String, String>> describeMap = fingerprintDescribe(fingerAllList);

        // 标签相关
        Map<String, Map<String, List<FingerprintVo.LabelDetail>>> labelMap = fingerprintLabel(fingerAllList);
        Map<String, List<FingerprintVo.LabelDetail>> dLabelMap = labelMap.getOrDefault("client", new HashMap<>());
        Map<String, List<FingerprintVo.LabelDetail>> sLabelMap = labelMap.getOrDefault("server", new HashMap<>());

        // 客户端IP热度
        Map<String, Integer> clientIpCountMap = ipHeat(fingerAllList, true);

        // 服务端IP热度
        Map<String, Integer> serverIpCountMap = ipHeat(fingerAllList, false);

        // 关联叶子证书数量
        Map<String, Integer> certCountMap = certCount(fingerAllList);

        // 补充ES数据 from cert_system&cert_user
        for (FingerprintVo fingerprintVo : fingerList) {
            String fingerprint = fingerprintVo.getFingerprint();
            String type = fingerprintVo.getFingerprintType();

            // 会话数量
            countSessionNumber(fingerprintVo, indexNames, condition);

            // 告警数量
            fingerAlarmCount(fingerprintVo);

            // 首次出现时间
            getFirstOrLastTime(fingerprintVo, true, indexNames);

            // 末次出现时间
            getFirstOrLastTime(fingerprintVo, false, indexNames);

            if ("dSSLFinger".equals(type)) {
                // 指纹描述
                if (ObjectUtils.isNotEmpty(describeMap) && describeMap.containsKey("client")) {
                    Map<String, String> dDescribeMap = describeMap.get("client");
                    fingerprintVo.setFingerprintDescribe(dDescribeMap.getOrDefault(fingerprint, ""));
                }

                // 标签
                fingerprintVo.setTag(dLabelMap.getOrDefault(fingerprint, new ArrayList<>()));

                // 客户端IP热度
                fingerprintVo.setDIpNum(clientIpCountMap.getOrDefault(fingerprint, 0));

                // 服务端IP热度
                fingerprintVo.setSIpNum(serverIpCountMap.getOrDefault(fingerprint, 0));

                // 叶子证书数量
                fingerprintVo.setCertNum(certCountMap.getOrDefault(fingerprint, 0));
            } else if ("sSSLFinger".equals(type)) {
                // 指纹描述
                if (ObjectUtils.isNotEmpty(describeMap) && describeMap.containsKey("server")) {
                    Map<String, String> sDescribeMap = describeMap.get("server");
                    fingerprintVo.setFingerprintDescribe(sDescribeMap.getOrDefault(fingerprint, ""));
                }

                // 标签
                fingerprintVo.setTag(sLabelMap.getOrDefault(fingerprint, new ArrayList<>()));

                // 客户端IP热度
                fingerprintVo.setDIpNum(clientIpCountMap.getOrDefault(fingerprint, 0));

                // 服务端IP热度
                fingerprintVo.setSIpNum(serverIpCountMap.getOrDefault(fingerprint, 0));

                // 叶子证书数量
                fingerprintVo.setCertNum(certCountMap.getOrDefault(fingerprint, 0));
            }
        }

        return fingerList;
    }

    private Map<String, Map<String, String>> fingerprintDescribe(List<String> fingerList) {
        Map<String, Map<String, String>> resultMap = new HashMap<>(2);
        List<SslFingerDescVo> sslFingerDescVoList = sslFingerService.listFingerDescribeByFingers(fingerList);
        for(SslFingerDescVo sslFingerDescVo : sslFingerDescVoList){
            String finger = sslFingerDescVo.getFingerId();
            String type = sslFingerDescVo.getFingerType();
            String fingerDesc = sslFingerDescVo.getFingerDesc();
            if ("client".equals(type)) {
                Map<String, String> clientMap = resultMap.getOrDefault("client", new HashMap<>());
                clientMap.put(finger, fingerDesc);
                resultMap.put("client", clientMap);
            } else if ("server".equals(type)) {
                Map<String, String> clientMap = resultMap.getOrDefault("server", new HashMap<>());
                clientMap.put(finger, fingerDesc);
                resultMap.put("server", clientMap);
            }
        }
        return resultMap;
    }

    private Map<String, Map<String, List<FingerprintVo.LabelDetail>>> fingerprintLabel(List<String> fingerList) {
        Map<String, Map<String, List<FingerprintVo.LabelDetail>>> resultMap = new HashMap<>(2);
        List<SslFingerLabelVo> sslFingerLabelVoList = sslFingerService.listFingerLabelByFingers(fingerList);
        for(SslFingerLabelVo sslFingerLabelVo : sslFingerLabelVoList){
            String finger = sslFingerLabelVo.getFingerId();
            String type = sslFingerLabelVo.getFingerType();
            Integer labelId = sslFingerLabelVo.getLabelId();
            String labelName = sslFingerLabelVo.getLabelName();
            FingerprintVo.LabelDetail labelDetail = new FingerprintVo.LabelDetail(labelId, labelName);

            if ("client".equals(type)) {
                Map<String, List<FingerprintVo.LabelDetail>> client = resultMap.getOrDefault("client", new HashMap<>());
                List<FingerprintVo.LabelDetail> labelList = client.getOrDefault(finger, new ArrayList<FingerprintVo.LabelDetail>());
                labelList.add(labelDetail);
                if (!client.containsKey(finger)) {
                    client.put(finger, labelList);
                }
                if (!resultMap.containsKey("client")) {
                    resultMap.put("client", client);
                }
            } else if ("server".equals(type)) {
                Map<String, List<FingerprintVo.LabelDetail>> server = resultMap.getOrDefault("server", new HashMap<>());
                List<FingerprintVo.LabelDetail> labelList = server.getOrDefault(finger, new ArrayList<FingerprintVo.LabelDetail>());
                labelList.add(labelDetail);
                if (!server.containsKey(finger)) {
                    server.put(finger, labelList);
                }
                if (!resultMap.containsKey("server")) {
                    resultMap.put("server", server);
                }
            }
        }
        return resultMap;
    }


    private Map<String, Integer> ipHeat(List<String> fingerList, boolean isClient){
        Map<String, Integer> resultMap = new HashMap<>(fingerList.size());
        Map<String, List<String>> tmpMap = new HashMap<>(fingerList.size());
        // put 每个finger 对应的ip list.
        List<SslFingerRelatedIpsVo> sslFingerRelatedIpsVoList;
        // 是否走客户端ip的线
        if (!isClient) {
            sslFingerRelatedIpsVoList = sslFingerService.listServerRelatedIpsByFingers(fingerList);
        }else{
            sslFingerRelatedIpsVoList = sslFingerService.listClientRelatedIpsByFingers(fingerList);
        }
        // put 每个finger 对应的ip list.
        for(SslFingerRelatedIpsVo sslFingerRelatedIpsVo : sslFingerRelatedIpsVoList){
            Set<String> relatedIps = new HashSet<>(sslFingerRelatedIpsVo.getIpList());
            tmpMap.put(sslFingerRelatedIpsVo.getFingerId(), new ArrayList<>(relatedIps));
        }
        // 计算 ip 热度
        Set<Map.Entry<String, List<String>>> tmpEntry = tmpMap.entrySet();
        for (Map.Entry<String, List<String>> entry : tmpEntry) {
            String finger = entry.getKey();
            Integer ipHeat = IpUtils.ipHotCrc(entry.getValue());
            resultMap.put(finger, ipHeat);
        }
        return resultMap;
    }

    private Map<String, Integer> certCount(List<String> fingerList) {
        Map<String, Integer> resultMap = new HashMap<>(fingerList.size());
        List<SslFingerCountVo> sslFingerCountVoList = sslFingerService.countCertByFingers(fingerList);
        for(SslFingerCountVo sslFingerCountVo : sslFingerCountVoList){
            String finger = sslFingerCountVo.getFingerId();
            Integer number = sslFingerCountVo.getCount();
            resultMap.put(finger, number);
        }
        return resultMap;
    }


    private void getFirstOrLastTime(FingerprintVo fingerprintVo, boolean isFirst, List<String> indexNames) {
        TermQueryBuilder queryBuilder = QueryBuilders.termQuery(fingerprintVo.getFingerprintType(), fingerprintVo.getFingerprint());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 是否查首次
        if (isFirst) {
            sourceBuilder.sort("CreateTime", SortOrder.ASC);
        } else {
            sourceBuilder.sort("CreateTime", SortOrder.DESC);
        }
        // 添加查询条件
        sourceBuilder.query(queryBuilder);
        // 查询第一条
        sourceBuilder.size(1);
        // 查询字段仅创建时间
        sourceBuilder.fetchSource("CreateTime", "");

        SearchResponse response = esearchService.esSearch(new SearchRequest(indexNames.toArray(new String[indexNames.size()])).source(sourceBuilder));
        SearchHit[] hits = response.getHits().getHits();
        for (SearchHit hit : hits) {
            Map<String, Object> sourceMap = hit.getSourceAsMap();
            if (isFirst) {
                fingerprintVo.setFirstTime(Long.valueOf(sourceMap.get("CreateTime").toString()));
            } else {
                fingerprintVo.setLastTime(Long.valueOf(sourceMap.get("CreateTime").toString()));
            }
        }
    }

    private void fingerAlarmCount(FingerprintVo fingerprintVo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("targets.name.keyword", fingerprintVo.getFingerprint()));
        boolQueryBuilder.must(QueryBuilders.termQuery("targets.type.keyword", "finger"));

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        CountRequest countRequest = new CountRequest(new String[]{"alarm*"}, sourceBuilder.query(boolQueryBuilder));
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        fingerprintVo.setAlarmNum(countResponse.getCount());
    }

    private void countSessionNumber(FingerprintVo fingerprintVo, List<String> indexNames, AnalysisBaseCondition
            condition) {
        String fingerPrint = fingerprintVo.getFingerprint();

        BoolQueryBuilder boolQueryBuilder = esAssemblySession(condition);

        BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
        shouldBuilder.should(QueryBuilders.termsQuery("sSSLFinger", fingerPrint));
        shouldBuilder.should(QueryBuilders.termsQuery("dSSLFinger", fingerPrint));
        shouldBuilder.should(QueryBuilders.termsQuery("sHTTPFinger", fingerPrint));
        shouldBuilder.should(QueryBuilders.termsQuery("dHTTPFinger", fingerPrint));

        boolQueryBuilder.must(shouldBuilder);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(boolQueryBuilder);

        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]), sourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        fingerprintVo.setSessionNum(countResponse.getCount());
    }

    private void delUnionAlarm(List<IpSortVo> allIpList, List<String> ipStrList, Map<String, IPListVo> map) {
        // 关联告警数量
        SearchRequest searchRequest = new SearchRequest("alarm*");
        TermsAggregationBuilder attakerIpAggBuilder = AggregationBuilders.terms("attacker_ip").field("attacker.ip").size(ipStrList.size() * 2);
        TermsAggregationBuilder victimIpAggBuilder = AggregationBuilders.terms("victim_ip").field("victim.ip").size(ipStrList.size() * 2);
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        aggrSourceBuilder.aggregation(attakerIpAggBuilder).aggregation(victimIpAggBuilder);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.should(QueryBuilders.termsQuery("attacker.ip", ipStrList));
        boolQueryBuilder.should(QueryBuilders.termsQuery("victim.ip", ipStrList));
        aggrSourceBuilder.query(boolQueryBuilder);
        aggrSourceBuilder.size(0);

        searchRequest.source(aggrSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        Aggregations aggregations = searchResponse.getAggregations();
        if (aggregations == null) {
            return;
        }
        ParsedStringTerms attackerIpAgg = aggregations.get("attacker_ip");
        ParsedStringTerms victimIpApp = aggregations.get("victim_ip");
        List<? extends Terms.Bucket> attackerIpAggBuckets = attackerIpAgg.getBuckets();
        List<? extends Terms.Bucket> victimIpAppBuckets = victimIpApp.getBuckets();
        attackerIpAggBuckets.parallelStream().forEach(attBucket -> {
            String ip = attBucket.getKeyAsString();
            if (!map.containsKey(ip)) {
                return;
            }
            long alarmCount = attBucket.getDocCount();
            IPListVo ipListVo = map.get(ip);
            ipListVo.setAlarmCount(alarmCount);
        });

        victimIpAppBuckets.parallelStream().forEach(vicBucket -> {
            String ip = vicBucket.getKeyAsString();
            if (!map.containsKey(ip)) {
                return;
            }
            long alarmCount = vicBucket.getDocCount();
            IPListVo ipListVo = map.get(ip);
            Long existCount = ipListVo.getAlarmCount();
            if (existCount != 0L) {
                alarmCount = alarmCount + existCount;
            }
            ipListVo.setAlarmCount(alarmCount);
        });

    }

    /**
     * 告警对象是该证书警个数  到告警索引查   证书
     */
    private void delUnionAlarm(String cert, CertListVo certListVo) {
        CountRequest countRequest = new CountRequest("alarm*");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        //targets可能存在有证书
        boolQueryBuilder.must(QueryBuilders.termQuery("targets.name.keyword", cert));
        boolQueryBuilder.must(QueryBuilders.termQuery("targets.type.keyword", "cert"));
        searchSourceBuilder.query(boolQueryBuilder);
        countRequest.source(searchSourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        certListVo.setAlarmCount(count);
    }

    private void delUnionAlarm2(List<CertListVo> allCertList, List<String> certStrList, Map<String, CertListVo> certMaps) {
        SearchRequest searchRequest = new SearchRequest("alarm*");
        TermsAggregationBuilder certAggBuilder = AggregationBuilders.terms("cert").field("targets.name.keyword").size(certStrList.size());
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        // 在type.keyword的告警类型中一定为cert
        boolQueryBuilder.must(QueryBuilders.termsQuery("targets.type.keyword", "cert"));
        boolQueryBuilder.must(QueryBuilders.termsQuery("targets.name.keyword", certStrList));
        aggrSourceBuilder.query(boolQueryBuilder).aggregation(certAggBuilder).size(0);

        searchRequest.source(aggrSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        Aggregations aggregations = searchResponse.getAggregations();
        if (aggregations == null) {
            return;
        }
        ParsedStringTerms certAgg = aggregations.get("cert");
        List<? extends Terms.Bucket> certAggBuckets = certAgg.getBuckets();
        certAggBuckets.parallelStream().forEach(certBucket -> {
            String certSHA1 = certBucket.getKeyAsString();
            if (!certMaps.containsKey(certSHA1)) {
                return;
            }
            long alarmCount = certBucket.getDocCount();
            CertListVo certListVo = certMaps.get(certSHA1);
            certListVo.setAlarmCount(alarmCount);
        });

    }

    //关联叶子证书个数
    private void delUnionSunCert(String cert, CertListVo certListVo) {
        CountRequest countRequest = new CountRequest("cert_user");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("FatherCertID.keyword", cert));
        searchSourceBuilder.query(boolQueryBuilder);
        countRequest.source(searchSourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        certListVo.setSonCertNum(count);
    }

    private void delCertInfo(String cert, CertListVo certListVo) {
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        SearchRequest searchRequest = new SearchRequest("cert_user");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(2);
        searchSourceBuilder.from(0);
        queryBuilder.must(QueryBuilders.termsQuery("ASN1SHA1", cert));
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long totalHits = searchResponse.getHits().totalHits;
        if (totalHits > 0) {
            SearchHit[] hits = searchResponse.getHits().getHits();
            Map<String, Object> certDataMap = new ConcurrentHashMap<>();
            Boolean isCertUser = false;
            for (SearchHit hit : hits) {
                Map<String, Object> esMap = hit.getSourceAsMap();
                if (hit.getIndex().equals("cert_user")) {
                    // 当前证书为用户证书，携带其解析后的标签
                    isCertUser = true;
                    certDataMap = esMap;
                    continue;
                }
                certDataMap = esMap;
            }
            // 父证书相关信息
            String fatherCert = certDataMap.getOrDefault("FatherCertID", "").toString();
            certListVo.setFatherCert(fatherCert);

            // 签发机构
            Map<String, Object> issuerMap = (Map<String, Object>) certDataMap.get("Issuer");
            if (MapUtils.isNotEmpty(issuerMap)) {
                certListVo.setIssuerO((String) issuerMap.get("CN"));
            }
            // 使用机构
            Map<String, Object> subjectMap = (Map<String, Object>) certDataMap.get("Subject");
            if (MapUtils.isNotEmpty(subjectMap)) {
                certListVo.setSubjectO((String) subjectMap.get("CN"));
            }

            // 证书有效期 or 失效时间
            String notBefore = (String) certDataMap.get("NotBefore");
            if (StrUtil.isNotEmpty(notBefore)) {
                // 这里是一个字符串，格式实为"20191230120925"，转义为"2019-12-30 12:09:25"
                notBefore = notBefore.substring(0, 4) + "-" + notBefore.substring(4, 6) + "-" + notBefore.substring(6, 8) + " " + notBefore.substring(8, 10) + ":" + notBefore.substring(10, 12) + ":" + notBefore.substring(12, 14);
                certListVo.setNotBefore(notBefore);
            }
            String notAfter = (String) certDataMap.get("NotAfter");
            if (StrUtil.isNotEmpty(notAfter)) {
                // 同起始时间一样进行字符串格式转换
                notAfter = notAfter.substring(0, 4) + "-" + notAfter.substring(4, 6) + "-" + notAfter.substring(6, 8) + " " + notAfter.substring(8, 10) + ":" + notAfter.substring(10, 12) + ":" + notAfter.substring(12, 14);
                certListVo.setNotAfter(notAfter);
            }

            // 证书标签
            Set<Integer> labels = new HashSet<>();
            List<String> certLabels = (List<String>) certDataMap.get("Labels");
            for (String label : certLabels) {
                labels.add(Integer.valueOf(label));
            }
            certListVo.setLabels(labels);
        }
    }


    public BoolQueryBuilder esAssemblySession(AnalysisBaseCondition sessionCondition) {

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();

        // 设置时间范围
        AnalysisBaseCondition.TimeRange timeRange = sessionCondition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            sessionCondition.setTimeRange(timeRange);
        }
        Long left = timeRange.getLeft();
        if (left == null || left < 1) {
            left = -1L;
            timeRange.setLeft(left);
        }
        Long right = timeRange.getRight();
        if (right == null || right < 1) {
            right = -1L;
            timeRange.setRight(right);
        }
        // JSON query字段对应条件拼装
        RangeQueryBuilder timeRangeQuery = QueryBuilders.rangeQuery("StartTime");
        if (left > 0) {
            // gt 大于
            timeRangeQuery.gte(left);
        }
        if (right > 0) {
            timeRangeQuery.lte(right);
        }

        if (left > 0 || right > 0) {
            boolQueryBuilder.must(timeRangeQuery);
        }

        return boolQueryBuilder;
    }

}

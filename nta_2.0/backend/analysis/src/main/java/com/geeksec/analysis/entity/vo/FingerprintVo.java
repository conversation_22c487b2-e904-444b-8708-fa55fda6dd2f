package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: GuanHao
 * @Date: 2022/6/27 17:22
 * @Description： <Functions List>
 */
@Data
public class FingerprintVo{

    /**
     * 指纹
     */
    @JsonProperty("fingerprint")
    private String fingerprint;

    /**
     * 指纹类型
     */
    @JsonProperty("fingerprint_type")
    private String fingerprintType;

    /**
     * 指纹描述
     */
    @JsonProperty("fingerprint_describe")
    private String fingerprintDescribe;

    /**
     * tag
     */
    @JsonProperty("tag")
    private List<LabelDetail> tag;

    /**
     * 客户端IP热度
     */
    @JsonProperty("dIp_num")
    private Integer dIpNum;

    /**
     * 服务端Ip热度
     */
    @JsonProperty("sIp_num")
    private Integer sIpNum;

    /**
     * 关联证书数量
     */
    @JsonProperty("cert_num")
    private Integer certNum;

    /**
     * 会话数量
     */
    @JsonProperty("session_num")
    private Long sessionNum;

    /**
     * 告警数量
     */
    @JsonProperty("alarm_num")
    private Long alarmNum;

    /**
     * 首次出现时间
     */
    @JsonProperty("first_time")
    private Long firstTime;

    /**
     * 末次出现时间
     */
    @JsonProperty("last_time")
    private Long lastTime;

    @Data
    public static class LabelDetail{

        @JsonProperty("tag_id")
        Integer labelId;

        @JsonProperty("tag_name")
        String tagName;

        public LabelDetail(Integer labelId, String labelName) {
            this.labelId = labelId;
            this.tagName= labelName;
        }
    }
}

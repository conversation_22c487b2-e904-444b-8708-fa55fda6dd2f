package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/7/26 17:51
 * @Description： <Functions List>
 */
@Data
public class MacVo {

    @JsonProperty("mac")
    private String mac;

    @JsonProperty("totalPacketNum")
    private Double totalPacketNum = 0.0;


    public MacVo(String mac) {
        this.mac = mac;
    }
}

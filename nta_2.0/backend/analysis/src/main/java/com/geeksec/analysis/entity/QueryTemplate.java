package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description：证书检索查询模板信息
 */
@Getter
@Setter
@TableName("tb_query_template")
public class QueryTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonProperty("id")
    private Integer id;

    /**
     * 创建模板用户ID
     */
    @TableField("user_id")
    @JsonProperty("user_id")
    private Integer userId;

    @TableField("user_name")
    @JsonProperty("user_name")
    private String userName;

    /**
     * 查询条件内容
     */
    @TableField("template_text")
    @JsonProperty("template_text")
    private String templateText;

    /**
     * 查询条件（前端用于回写）
     */
    @TableField(value = "query_cn")
    @JsonProperty("query_cn")
    private String queryCn;

    /**
     * 模板备注
     */
    @TableField("remark")
    @JsonProperty("remark")
    private String remark;

    /**
     * 记录创建时间
     */
    @TableField("created_time")
    @JsonProperty("created_time")
    private Date createdTime;

    /**
     * 模板末次时间
     */
    @TableField("last_used_time")
    @JsonProperty("last_used_time")
    private Date lastUsedTime;

    /**
     * 前后查询次数
     */
    @TableField(value = "query_count")
    @JsonProperty("query_count")
    private Long queryCount;
}


package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.entity.common.BaseCondition;
import com.geeksec.entity.common.ResultVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description： 查询历史Service
 */
public interface QueryHistoryService {

    /**
     * 创建多目标查询历史信息
     *
     * @param condition  查询条件
     * @param sourceList
     * @param costTime   查询耗时
     * @param count      查询结果数量
     */
    void createQueryHistory(AnalysisBaseCondition condition, List<Integer> sourceList, Long costTime, long count);

    /**
     * 获取查询历史记录
     *
     * @param condition
     * @return
     */
    ResultVo searchQueryHistoryList(BaseCondition condition);

    /**
     * 根据历史记录ID删除检索历史
     *
     * @param ids
     * @param clear
     * @return
     */
    ResultVo deleteQueryHistory(List<Integer> ids, Boolean clear);
}

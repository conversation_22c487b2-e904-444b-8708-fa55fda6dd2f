package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class NetworkFlowVo {
    /**
     * //自增ID ---任务信息表---
     */
    private Integer id;

    /**
     * //任务ID
     */
    @JsonProperty("pcie_id")
    private String pcieId;

    /**
     * //任务名称
     */
    @JsonProperty("flow_name")
    private String flowName;

    /**
     * //任务备注
     */
    @JsonProperty("network_type")
    private String networkType;

    /**
     * //0代表历史任务，1代表当前任务
     */
    @JsonProperty("state")
    private Integer state;
}

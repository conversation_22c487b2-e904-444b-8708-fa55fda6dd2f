package com.geeksec.analysis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.condition.SessionDetailCondition;
import com.geeksec.analysis.dao.FeatureRuleDao;
import com.geeksec.analysis.dao.ThAnalysisDao;
import com.geeksec.analysis.entity.session.SessionIdEntity;
import com.geeksec.analysis.entity.session.SessionTagEntity;
import com.geeksec.analysis.entity.session.TargetRemarkInfo;
import com.geeksec.analysis.handler.LogDataHandler;
import com.geeksec.analysis.service.TargetDetailService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.lmdb.LmdbService;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.MultiSearchRequest;
import org.elasticsearch.action.search.MultiSearchResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class TargetDetailServiceImpl implements TargetDetailService {

    private static Logger logger = LoggerFactory.getLogger(TargetDetailServiceImpl.class);

    // 706新增特殊应用协议类型
    private static final Set<String> APP_NAME_METADATA_SET = new HashSet<>(Arrays.asList("APP_PPTP", "APP_OPENVPN", "APP_IPSec_ISAKMP", "APP_RDP", "APP_SSH"));

    // 常规元数据协议类型
    private static final Set<String> STANDARD_APP_NAME_SET = new HashSet<>(Arrays.asList("APP_DNS", "APP_HTTP", "APP_SSL", "APP_SSH", "APP_Telnet", "APP_Rlogin", "APP_VNC", "APP_RDP", "APP_XDMCP"));

    @Autowired
    private EsearchService esearchService;

    @Autowired
    private ThAnalysisDao thAnalysisDao;

    @Autowired
    private FeatureRuleDao featureRuleDao;

    @Autowired
    private LmdbService lmdbService;

    private static String esConnectIndex = "connect*";

    @Value("${enabled.atlas}")
    private Boolean hasAtlas;

    private static Map<String, String> targetInfoTableMap = new HashMap<>();

    static {
        targetInfoTableMap.put("session", "tb_session_id");
        targetInfoTableMap.put("port", "tb_port_info");
        targetInfoTableMap.put("app", "tb_app_info");
        targetInfoTableMap.put("domain", "tb_domain_info");
        targetInfoTableMap.put("cert", "tb_cert_info");
        targetInfoTableMap.put("mac", "tb_mac");
        targetInfoTableMap.put("ip", "tb_ip_info");
        targetInfoTableMap.put("session", "tb_session_id");
        targetInfoTableMap.put("finger", "tb_finger_infor");

    }

    @Override
    public ResultVo getSessionDetail(SessionDetailCondition condition) {
        String subType = condition.getSubType();
        Map<String, Object> resultMap;
        switch (subType) {
            case "basic": // 基础五元组信息
                resultMap = getSessionBasicInfo(condition);
                break;
            case "session_log": // 会话日志 对应HKey查
                resultMap = getSessionLogInfo(condition);
                break;
            case "session_meta_data": // 协议元数据 (会话标签页)
                resultMap = getSessionMetadata(condition);
                break;
            case "protocol_meta_data": // 协议元数据（元数据标签页）
                resultMap = getMetadataByHkey(condition);
                break;
            case "packet_histogram": // 包分析
                resultMap = getPacketHistogram(condition);
                break;
            default:
                return ResultVo.success("数据准备中，请稍后查看");
        }

        if (ObjectUtils.isEmpty(resultMap)) {
            String message = "";
            switch (subType) {
                case "basic":
                    throw new GkException(GkErrorEnum.SESSION_BASIC_QUERY_ERROR);
                case "session_log":
                    message = "会话日志数据准备中，请稍后查看";
                    break;
                case "session_meta_data":
                case "protocol_meta_data":
                    message = "协议元数据准备中，请稍后查看";
                    break;
                case "packet_histogram":
                    message = "包分析数据准备中，请稍后查看";
                    break;
            }
            return ResultVo.success(message);
        }
        return ResultVo.success(resultMap);

    }

    @Override
    public ResultVo addTargetRemark(String targetType, String targetKey, String remark) {
        try {
            thAnalysisDao.addTargetRemark(targetType, targetKey, remark);
        } catch (Exception e) {
            logger.error("添加目标详情备注失败,error--->", e.getMessage());
            return ResultVo.fail("添加目标详情备注失败");
        }
        return ResultVo.success();
    }

    @Override
    public ResultVo deleteTargetRemark(String remarkId) {
        try {
            thAnalysisDao.deleteTargetRemarkById(remarkId);
        } catch (Exception e) {
            logger.error("删除单条目标详情备注失败,error--->", e.getMessage());
            return ResultVo.fail("删除目标详情备注失败");
        }
        return ResultVo.success();
    }

    @Override
    public ResultVo editTargetTag(String esIndex, String esId, List<Long> lables) throws InterruptedException {

        if (lables.contains(100L)) {
            logger.info("修改目标标签状态:包括确认黑名单,doc id--->{}", esId);
        } else {
            logger.info("修改目标标签,doc id--->{},lables--->{}", esId, lables);
        }

        // 以ID为条件，查询出当前需要修改目标ES元数据信息
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("_id", esId));
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest(new String[]{esIndex}, searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long totalHits = searchResponse.getHits().totalHits;
        if (totalHits < 1) {
            logger.error("修改目标标签失败！查询对应doc失败,esIndex--->{},esId--->{}", esIndex, esId);
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        SearchHit[] hits = searchResponse.getHits().getHits();
        Map<String, Object> esDataMap = hits[0].getSourceAsMap();
        esDataMap.put("Labels", lables);
        // 开始执行更新操作，立刻执行，不放入更新队列中
        UpdateRequest updateRequest = new UpdateRequest(esIndex, "_doc", esId)
                .fetchSource(true).doc(esDataMap).setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        UpdateResponse updateResponse = esearchService.updateDoc(updateRequest);
        RestStatus status = updateResponse.status();
        if (status.getStatus() != 200) {
            logger.error("修改目标标签失败！更新ES数据失败,esIndex--->{},esId--->{}", esIndex, esId);
            throw new GkException(GkErrorEnum.ES_TARGET_MODIFY_LABEL_ERROR);
        }
        // 在此处等待ES数据请求处理，执行完毕后查询ES数据中的当前ID里的Labels是否已经更新
        Boolean isUpdated = false;
        searchSourceBuilder.fetchSource(new String[]{"Labels"}, null);
        while (!isUpdated) {
            // 修改searchSourceBuilder 只查询labels字段是否相同
            SearchRequest searchRequest1 = new SearchRequest(new String[]{esIndex}, searchSourceBuilder);
            searchResponse = esearchService.esSearch(searchRequest1);
            SearchHit[] newHits = searchResponse.getHits().getHits();
            Map<String, Object> newEsDataMap = newHits[0].getSourceAsMap();
            List<Integer> newLabels = (List<Integer>) newEsDataMap.get("Labels");
            // 判断newLabels和 lables是否相同
            if (newLabels.equals(lables)) {
                isUpdated = true;
            }
        }

        return ResultVo.success("更新目标标签成功！");
    }

    @Override
    public ResultVo confirmBlack(String targetKey, String targetType) {
        // 1.生成黑名单前有可能Mysql里没有，先查找是否存在
        if (targetType.equals("session")) {
            SessionIdEntity sessionIdInfo = thAnalysisDao.getSessionIdInfo(targetKey);
            if (sessionIdInfo == null) {
                thAnalysisDao.addSessionIdInfo(targetKey, StringUtils.EMPTY);
            }
        }

        return null;
    }

    /**
     * 会话基础信息
     *
     * @param condition
     */
    private Map<String, Object> getSessionBasicInfo(SessionDetailCondition condition) {

        HashMap<String, Object> resultMap = new HashMap<>();
        String sessionId = condition.getSearch();
        String esIndex = condition.getEsIndex();
        if (sessionId == null) {
            return new HashMap<>();
        }
        Map<String, Object> ssMap = getSingleSessionInfo(sessionId, esIndex);
        if (MapUtil.isEmpty(ssMap)) {
            return new HashMap<>();
        }
        // 获取会话标签
        List<Integer> lables = (List<Integer>) ssMap.get("Labels");
        List<SessionTagEntity> sessionTags = thAnalysisDao.getSessionTag(lables);
        List<SessionTagEntity> listForLabel2 = featureRuleDao.getListForLabel2(lables);
        if (!listForLabel2.isEmpty()) {
            sessionTags.addAll(listForLabel2);
        }
        // 处理标签等级
        handleTagLevel(sessionTags);
        String hKey = (String) ssMap.get("Hkey");
        // Hkey :connectinfo_0_100001_2022052518342336277049810185 & 7889988955014386369_connectinfo_1_100002_20220701_143228_0055两种Hkey方式此处判断
        String taskId = StringUtils.EMPTY;
        if (hKey.startsWith("connectinfo")) {
            taskId = hKey.split("_")[1];
        } else {
            taskId = hKey.split("_")[2];
        }
        String taskName = thAnalysisDao.getTaskNameByTaskId(Integer.valueOf(taskId));
        resultMap.put("session_id", ssMap.get("SessionId"));
        resultMap.put("task_name", taskName);
        resultMap.put("tags", sessionTags);
        resultMap.put("src_ip", ssMap.get("sIp"));
        resultMap.put("src_port", ssMap.get("sPort"));
        resultMap.put("dst_ip", ssMap.get("dIp"));
        resultMap.put("dst_port", ssMap.get("dPort"));
        resultMap.put("ippro", ssMap.get("IPPro"));
        resultMap.put("app_name", ssMap.get("AppName"));
        resultMap.put("start_time", ssMap.get("StartTime"));
        resultMap.put("end_time", ssMap.get("EndTime"));
        resultMap.put("app_name", ssMap.get("AppName"));
        // 存放es_index和id
        resultMap.put("es_index", ssMap.get("es_index"));
        resultMap.put("id", ssMap.get("id"));
        resultMap.put("hkey", ssMap.get("Hkey"));

        SessionIdEntity sessionInfo = thAnalysisDao.getSessionIdInfo(sessionId);
        if (sessionInfo != null) {
            resultMap.put("black_list", sessionInfo.getBlackList());
        } else {
            resultMap.put("black_list", 0);
        }
        // 备注集合
        List<TargetRemarkInfo> remarkList = thAnalysisDao.getTargetRemarkByKey(sessionId, "session");
        resultMap.put("remark", remarkList);
        return resultMap;
    }

    /**
     * 会话日志信息JSON String
     *
     * @param condition
     * @return
     */
    private HashMap<String, Object> getSessionLogInfo(SessionDetailCondition condition) {
        String sessionId = condition.getSearch();
        String esIndex = condition.getEsIndex();
        if (sessionId == null) {
            return new HashMap<>();
        }
        Map<String, Object> esMap = getSingleSessionInfo(sessionId, esIndex);
        String hKey = esMap.get("Hkey").toString();
        Map<String, Object> pbMap = getLmdbValue(esMap, hKey);
        if (ObjectUtils.isEmpty(pbMap)) {
            logger.info("当前Hkey查询lmdb文件数据为空，Hkey ---{}", hKey);
            return new HashMap<>();
        }

        // 开始进行一段长长的数据处理~
        List<Map<String, Object>> connectMapList = LogDataHandler.extractConnectMap(pbMap);

        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("connect_info", connectMapList);
        pbMap.remove("SSL");
        pbMap.remove("HTTP");
        pbMap.remove("DNS");
        resultMap.put("common", new JSONObject(pbMap));

        return resultMap;
    }

    /**
     * 通过Hkey去ES里获取单体会话信息
     *
     * @param sessionId
     * @param esIndex
     * @return
     */
    public Map<String, Object> getSingleSessionInfo(String sessionId, String esIndex) {
        if (sessionId == null) {
            return new HashMap<>();
        }
        HashMap<String, Object> hitMap = new HashMap<>();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("SessionId", sessionId));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);

        // 固定查询的ES索引，查询更快
        if (esIndex.startsWith("http") || esIndex.startsWith("dns") || esIndex.startsWith("ssl") || esIndex.startsWith("ssh") || esIndex.startsWith("telnet")
                || esIndex.startsWith("rlogin") || esIndex.startsWith("vnc") || esIndex.startsWith("rdp") || esIndex.startsWith("xdmcp")) {
            // 获取第一个"_"后面的内容
            esIndex = "connectinfo_" + esIndex.substring(esIndex.indexOf("_") + 1);
        }
        SearchRequest searchRequest = new SearchRequest(esIndex);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse response = esearchService.esSearch(searchRequest);
            long count = response.getHits().getTotalHits();
            if (count == 0) {
                return new HashMap<>();
            }
            SearchHit[] hits = response.getHits().getHits();
            // 命中对应的ES会话文档
            hitMap = (HashMap<String, Object>) hits[0].getSourceAsMap();
            hitMap.put("es_index", hits[0].getIndex());
            hitMap.put("id", hits[0].getId());
        } catch (Exception e) {
            logger.error("查询单体会话信息失败--->{}", e.getMessage());
        }
        return hitMap;
    }


    private HashMap<String, Object> getSessionMetadata(SessionDetailCondition condition) {

        // 先拿到当前会话所使用的协议名称
        String appName = condition.getAppName();

        // 返回元数据类型 dns http modbus s7 ssh ssl
        HashMap<String, Object> metadataMap = new HashMap<String, Object>();

        // 通过协议名称查询对应的元数据（特殊协议类型，不进行lmdb查询）
        if (APP_NAME_METADATA_SET.contains(appName)) {
            Object specialMetaData = getSpecialMetadata(condition, appName);
            if (!ObjectUtils.isEmpty(specialMetaData)) {
                metadataMap.put("metadata", specialMetaData);
            }
        }
        List<Map<String, Object>> metadataList = new ArrayList<>();
        if (STANDARD_APP_NAME_SET.contains(appName)) {
            // 通过精确匹配进行会话元数据的查询
            appName = appName.split("_")[1].toLowerCase();
            // ES统一中间标识符 connectinfo_0_100001_20240109_100000001
            int index = condition.getEsIndex().indexOf("_");
            String esIndexPrefix = StrUtil.EMPTY;
            if (index >= 0) {
                esIndexPrefix = condition.getEsIndex().substring(index + 1);
            }
            esIndexPrefix = appName + "_" + esIndexPrefix;
            metadataList = getSingleMetadata(esIndexPrefix, condition);
            logger.info("通过会话获取的元数据数据字段为:{}", metadataList);
            if (CollectionUtil.isNotEmpty(metadataList)) {
                metadataMap.put("metadata", metadataList);
            }
        } else {
            return new HashMap<>();
        }

//        List<Map<String, Object>> dnsMetadataList = new ArrayList<>();
//        dnsMetadataList = getSingleMetadata("dns_*", condition);
//        if (!ObjectUtils.isEmpty(dnsMetadataList)) {
//            metadataMap.put("metadata", dnsMetadataList);
//        }
//
//        List<Map<String, Object>> httpMetadataList = new ArrayList<>();
//        httpMetadataList = getSingleMetadata("http_*", condition);
//        if (!ObjectUtils.isEmpty(httpMetadataList)) {
//            metadataMap.put("metadata", httpMetadataList);
//        }
//
//        List<Map<String, Object>> sslMetadataList = new ArrayList<>();
//        sslMetadataList = getSingleMetadata("ssl_*", condition);
//        if (!ObjectUtils.isEmpty(sslMetadataList)) {
//            metadataMap.put("metadata", sslMetadataList);
//        }
//
//        // 图谱平台添加SSH协议元数据
//        if (hasAtlas) {
//            List<Map<String, Object>> sshMetadataList = new ArrayList<>();
//            sshMetadataList = getSingleMetadata("ssh_*", condition);
//            if (!ObjectUtils.isEmpty(sshMetadataList)) {
//                metadataMap.put("metadata", sshMetadataList);
//            }
//        }

        return metadataMap;
    }

    /**
     * 处理只在ES获取的协议元数据内容
     *
     * @param condition
     * @param appName
     * @return
     */
    private Object getSpecialMetadata(SessionDetailCondition condition, String appName) {
        String sessionId = condition.getSearch();
        // 获取查询批次,唯一的日期ID
        String Hkey = condition.getHKey();
        int lastUnderscoreIndex = Hkey.lastIndexOf('_');
        int firstUnderscoreIndex = Hkey.indexOf('_');
        String sign = Hkey.substring(firstUnderscoreIndex + 1, lastUnderscoreIndex);

        String indexName = StringUtils.EMPTY;
        // 会话元数据的索引名称 元数据类型 +sign
        // 因为有可能会有多个元数据类型的情况，使用MultiSearchRequest

        if (appName.equals("APP_PPTP")) {
            MultiSearchRequest multiSearchRequest = new MultiSearchRequest();

            SearchRequest searchRequest1 = new SearchRequest();
            SearchSourceBuilder searchSourceBuilder1 = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder1 = QueryBuilders.boolQuery();
            boolQueryBuilder1.must(QueryBuilders.termQuery("SessionId", sessionId));
            searchSourceBuilder1.query(boolQueryBuilder1).size(1).from(0);
            multiSearchRequest.add(searchRequest1.source(searchSourceBuilder1).indices("gre_" + sign + "_*"));

            SearchRequest searchRequest2 = new SearchRequest();
            SearchSourceBuilder searchSourceBuilder2 = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder2 = QueryBuilders.boolQuery();
            boolQueryBuilder2.must(QueryBuilders.termQuery("SessionId", sessionId));
            searchSourceBuilder2.query(boolQueryBuilder2).size(1).from(0);
            multiSearchRequest.add(searchRequest2.source(searchSourceBuilder2).indices("pptp_" + sign + "_*"));
            MultiSearchResponse response = esearchService.esMultiSearch(multiSearchRequest);

            List<Map<String, Object>> multiSearchResult = new ArrayList<>();
            for (MultiSearchResponse.Item item : response.getResponses()) {
                SearchResponse searchResponse = item.getResponse();
                long count = searchResponse.getHits().getTotalHits();
                if (count == 0) {
                    continue;
                }
                SearchHit[] hits = searchResponse.getHits().getHits();
                Map<String, Object> hitMap = hits[0].getSourceAsMap();
                multiSearchResult.add(hitMap);
            }
            return multiSearchResult;
        } else {
            if (appName.equals("APP_OPENVPN")) {
                indexName = "openvpn_" + sign + "_*";
            } else if (appName.equals("APP_IPSec_ISAKMP")) {
                indexName = "ipsec_" + sign + "_*";
            } else if (appName.equals("APP_RDP")) {
                indexName = "ssl_" + sign + "_*";
            } else if (appName.equals("APP_SSH")) {
                indexName = "ssh_" + sign + "_*";
            }
            SearchRequest searchRequest = new SearchRequest();
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery("SessionId", sessionId));
            searchSourceBuilder.query(boolQueryBuilder).size(1).from(0);
            searchRequest.source(searchSourceBuilder).indices(indexName);
            try {
                SearchResponse response = esearchService.esSearch(searchRequest);
                SearchHit[] hits = response.getHits().getHits();
                if (hits.length > 0) {
                    Map<String, Object> hitMap = (HashMap<String, Object>) hits[0].getSourceAsMap();
                    return hitMap;
                } else {
                    return new HashMap<>();
                }
            } catch (Exception e) {
                logger.error("查询单体会话信息失败--->{}", e.getMessage());
                return null;
            }
        }
    }

    /**
     * 通过Hkey获取会话元数据 (元数据标签)
     * 先判断Hkey
     *
     * @param condition
     * @return
     */
    private Map<String, Object> getMetadataByHkey(SessionDetailCondition condition) {
        String hKey = condition.getHKey();
        String sessionId = condition.getSearch();
        HashMap<String, Object> esMap = new HashMap<>();
        String esIndexType = hKey.split("_")[0];

        List<Map<String, Object>> metaDataList = new ArrayList<>();
        switch (esIndexType) {
            case "http":
                metaDataList = getSingleMetadata("http_*", condition);
                break;
            case "dns":
                metaDataList = getSingleMetadata("dns_*", condition);
                break;
            case "ssl":
                metaDataList = getSingleMetadata("ssl_*", condition);
                break;
            case "ssh":
                metaDataList = getSingleMetadata("ssh_*", condition);
                break;
            case "telnet":
                metaDataList = getSingleMetadata("telnet_*", condition);
                break;
            case "rlogin":
                metaDataList = getSingleMetadata("rlogin_*", condition);
                break;
            case "vnc":
                metaDataList = getSingleMetadata("vnc_*", condition);
            case "rdp":
                metaDataList = getSingleMetadata("rdp_*", condition);
                break;
            case "xdmcp":
                metaDataList = getSingleMetadata("xdmcp_*", condition);
                break;
            default:
                break;
        }

        if (CollectionUtils.isEmpty(metaDataList)) {
            return new HashMap<>();
        }
        return metaDataList.get(0);
    }

    /**
     * 获取协议元数据Table & json
     *
     * @param esIndexType
     * @param condition
     * @return
     */
    private List<Map<String, Object>> getSingleMetadata(String esIndexType, SessionDetailCondition condition) {
        List<Map<String, Object>> metadataList = new ArrayList<>();

        // 获取单体会话SessionId
        String sessionId = condition.getSearch();
        SearchRequest searchRequest = new SearchRequest();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("SessionId", sessionId));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder).size(condition.getLimit());
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(esIndexType);
        try {
            SearchResponse response = esearchService.esSearch(searchRequest);
            SearchHit[] hits = response.getHits().getHits();
            if (response.getHits().getTotalHits() != 0) {
                for (SearchHit hit : hits) {
                    // ES Map
                    Map<String, Object> hitMap = hit.getSourceAsMap();
                    String hKey = (String) hitMap.get("Hkey");
                    // 根据Hkey获取lmdb中对应Row值
                    Map<String, Object> pbMap = getLmdbValue(hitMap, hKey);
                    if (ObjectUtils.isEmpty(pbMap)) {
                        return metadataList;
                    }
                    metadataList.add(pbMap);
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("查询单个会话元数据信息失败，error--->", e);
        }
        return metadataList;
    }

    /**
     * @param condition
     * @return 获取会话包时序分布
     */
    private HashMap<String, Object> getPacketHistogram(SessionDetailCondition condition) {

        // 直接使用condition中的Hkey
        String sessionId = condition.getSearch();
        String hKey = condition.getHKey();
        String esIndex = hKey.split("_")[0];

        // 元数据的会话详情查询，将Hkey前缀的元数据前缀换成connecinfo
        if (esIndex.startsWith("http") || esIndex.startsWith("dns") || esIndex.startsWith("ssl") || esIndex.startsWith("ssh") || esIndex.startsWith("telnet")
                || esIndex.startsWith("rlogin") || esIndex.startsWith("vnc") || esIndex.startsWith("rdp") || esIndex.startsWith("xdmcp")) {
            hKey = hKey.replace(esIndex, "connectinfo");
        }

        Map<String, Object> pbMap = getLmdbValue(new HashMap<>(), hKey);
        if (ObjectUtils.isEmpty(pbMap)) {
            return new HashMap<>();
        }
        // 获取pkt信息
        Map<String, Object> pktMap = (Map<String, Object>) pbMap.get("pkt");
        // 获取Infor信息
        List<Map<String, Object>> inforList = (List<Map<String, Object>>) pktMap.get("Infor");
        List<Map<String, Object>> histogram = new ArrayList<>();
        for (Map<String, Object> inforMap : inforList) {
            Map<String, Object> infor = new HashMap<>();
            infor.put("sec", inforMap.get("Sec"));
            infor.put("count", inforMap.get("Count"));
            infor.put("time", inforMap.get("nSec"));
            infor.put("len", inforMap.get("Len"));
            histogram.add(infor);
        }

        HashMap<String, Object> result = new HashMap<>();
        result.put("src_ip", pbMap.get("sIp"));
        result.put("dst_ip", pbMap.get("dIp"));
        result.put("histogram", histogram);

        return result;
    }

    private List<SessionTagEntity> handleTagLevel(List<SessionTagEntity> tagList) {
        for (SessionTagEntity info : tagList) {
            int blackList = info.getBlackList();
            int whiteList = info.getWhiteList();
            if (blackList >= 1 && blackList <= 100) {
                if (whiteList != 100) {
                    if (blackList >= 80) {
                        info.setTagLevel("danger");
                    } else {
                        info.setTagLevel("warning");
                    }
                } else {
                    info.setTagLevel("positive");
                }
            } else if (whiteList >= 1 && whiteList <= 100) {
                if (whiteList == 100) {
                    info.setTagLevel("success");
                } else {
                    info.setTagLevel("positive");
                }
            } else {
                info.setTagLevel("info");
            }
        }
        return tagList;
    }

    /**
     * 通过Hkey作为参数 调用
     *
     * @param esMap
     * @param hKey
     * @return
     */
    public Map<String, Object> getLmdbValue(Map<String, Object> esMap, String hKey) {
        try {
            esMap.remove("Hkey");
            Map<String, Object> pbMap = lmdbService.hLmdbHandle(esMap, hKey);
            return (pbMap != null) ? pbMap : esMap;
        } catch (Exception e) {
            logger.error("获取lmdb文件数据失败,error:", e);
        }
        return esMap;
    }
}

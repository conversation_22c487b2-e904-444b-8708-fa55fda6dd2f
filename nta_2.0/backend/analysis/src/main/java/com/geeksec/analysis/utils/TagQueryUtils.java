package com.geeksec.analysis.utils;

import cn.hutool.core.collection.CollUtil;
import com.geeksec.analysis.entity.vo.TagLibraryVo;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * <AUTHOR>
 * @Description：标签比对工具
 */
public class TagQueryUtils {

    private final static Logger logger = LoggerFactory.getLogger(TagQueryUtils.class);

    /**
     * 此处做标签条件分离，目标实体标签 & 其他常规信息标签
     * 同时做and & not 双条件分离
     * <p>
     * tagList 实例：{"and":[[10001,222,4112],[182,195,112]],"not":[]}
     *
     * @param tagDict
     * @param tagList
     * @return
     */
    public static Map<String, List<List<String>>> filterTagByType(Map<Integer, Object> tagDict, Map<String, List<List<String>>> tagList) {
        Map<String, List<List<String>>> result = new HashMap<>();
        // 初始化结果集
        result.put("target", new ArrayList<>());
        result.put("targetNot", new ArrayList<>());
        result.put("stan", new ArrayList<>());
        result.put("stanNot", new ArrayList<>());
        result.put("cert", new ArrayList<>());
        result.put("certNot", new ArrayList<>());

        // 目标类型集合
        Set<Integer> targetTypes = new HashSet<>(Arrays.asList(0, 3, 7));

        // 处理标签列表
        processTags(tagDict, tagList.get("and"), result, targetTypes, false);
        processTags(tagDict, tagList.get("not"), result, targetTypes, true);

        return result;
    }

    private static void processTags(Map<Integer, Object> tagDict, List<List<String>> tags,
                                    Map<String, List<List<String>>> result,
                                    Set<Integer> targetTypes, boolean isNot) {
        if (tags == null) {
            return;
        }

        String targetKey = isNot ? "targetNot" : "target";
        String stanKey = isNot ? "stanNot" : "stan";
        String certKey = isNot ? "certNot" : "cert";

        for (List<String> tagGroup : tags) {
            List<String> targetTags = new ArrayList<>();
            List<String> stanTags = new ArrayList<>();
            List<String> certTags = new ArrayList<>();

            for (String tagIdStr : tagGroup) {
                Integer tagId = Integer.valueOf(tagIdStr);
                Object tagObj = tagDict.get(tagId);

                if (tagObj instanceof TagLibraryVo) {
                    TagLibraryVo tagVo = (TagLibraryVo) tagObj;
                    Integer tagTargetType = tagVo.getTagTargetType();

                    if (targetTypes.contains(tagTargetType)) {
                        targetTags.add(tagIdStr);
                    } else if (tagTargetType == 4) {
                        certTags.add(tagIdStr);
                    } else {
                        stanTags.add(tagIdStr);
                    }
                } else {
                    stanTags.add(tagIdStr);
                }
            }

            // 使用CollUtil和CollectionUtils保持一致性
            if (CollUtil.isNotEmpty(targetTags)) {
                result.get(targetKey).add(targetTags);
            }
            if (CollUtil.isNotEmpty(stanTags)) {
                result.get(stanKey).add(stanTags);
            }
            if (CollUtil.isNotEmpty(certTags)) {
                result.get(certKey).add(certTags);
            }
        }
    }

    /**
     * 判断有无常规会话标签查询条件，拼装入queryBuilder中
     *
     * @param tagFiltertMap
     * @param queryBuilder
     * @return
     */
    public static BoolQueryBuilder handleStanTagQuery(Map<String, List<List<String>>> tagFiltertMap, BoolQueryBuilder queryBuilder) {
        // 常规标签
        List<List<String>> stanTagQueryList = tagFiltertMap.get("stan");
        List<List<String>> stanTagNotQueryList = tagFiltertMap.get("stanNot");

        if (CollectionUtils.isEmpty(stanTagQueryList) && CollectionUtils.isEmpty(stanTagNotQueryList)) {
            // 没有相关标签需要查询，返回queryBuilder
            return queryBuilder;
        }

        if (CollectionUtils.isNotEmpty(stanTagQueryList)) {
            for (List<String> stanTags : stanTagQueryList) {
                BoolQueryBuilder stanQueryBuilder = new BoolQueryBuilder();
                for (String tag : stanTags) {
                    stanQueryBuilder.should(QueryBuilders.termQuery("Labels", tag));
                }
                queryBuilder.must(stanQueryBuilder);
            }
        }

        if (CollectionUtils.isNotEmpty(stanTagNotQueryList)) {
            for (List<String> stanNotTags : stanTagNotQueryList) {
                BoolQueryBuilder stanQueryBuilder = new BoolQueryBuilder();

                for (String tag : stanNotTags) {
                    stanQueryBuilder.mustNot(QueryBuilders.termQuery("Labels", tag));
                }
                queryBuilder.must(stanQueryBuilder);
            }
        }

        return queryBuilder;
    }

}

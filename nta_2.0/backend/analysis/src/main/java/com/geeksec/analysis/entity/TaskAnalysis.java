package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_task_analysis")
public class TaskAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * //自增ID ---任务信息表---
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * //任务ID
     */
    @TableField("task_id")
    private Integer taskId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * //任务类型（1-在线任务；2-离线任务；）
     */
    @TableField("task_type")
    private Integer taskType;

    /**
     * //任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * //任务备注
     */
    @TableField("task_remark")
    private String taskRemark;

    /**
     * //任务管理的网卡 ，json 数组格式  , 为 tb_network_flow 的 flow_name 
     */
    @TableField("netflow")
    private String netflow;

    /**
     * //0代表历史任务，1代表当前任务 2 代表挂起的任务
     */
    @TableField("task_state")
    private Integer taskState;

    /**
     * //最后挂起时间
     */
    @TableField("last_suspend_time")
    private Date lastSuspendTime;

    /**
     * //挂起次数
     */
    @TableField("suspend_times")
    private Integer suspendTimes;


}

package com.geeksec.analysis.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.dao.ThSessionDao;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.CommunicationCondition;
import com.geeksec.analysis.entity.storage.*;
import com.geeksec.analysis.entity.vo.FingerprintVo;
import com.geeksec.analysis.entity.vo.TagLibraryVo;
import com.geeksec.analysis.service.AggrTargetService;
import com.geeksec.analysis.service.NebulaRelatedService;
import com.geeksec.analysis.utils.ESQueryUtil;
import com.geeksec.analysis.utils.TagQueryUtils;
import com.geeksec.entity.common.PageResultVo;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.entity.communication.vo.CommunicationVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.ngbatis.service.CertService;
import com.geeksec.ngbatis.service.DomainService;
import com.geeksec.ngbatis.service.IpService;
import com.geeksec.ngbatis.vo.ExistCertPropertiesVo;
import com.geeksec.ngbatis.vo.ExistDomainPropertiesVo;
import com.geeksec.ngbatis.vo.ExistIpPropertiesVo;
import com.geeksec.util.IpUtils;
import com.geeksec.util.RedisUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.IncludeExclude;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sun.net.util.IPAddressUtil;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class AggrTargetServiceImpl implements AggrTargetService {

    private static final Logger logger = LoggerFactory.getLogger(AggrTargetServiceImpl.class);

    private static final Map<Integer, Object> TAG_INFO_MAP = new HashMap<>();

    @Value("${elasticsearch.es_connect_index}")
    private String esConnectIndex;

    @Value("${query.es_limit}")
    private Integer esLimit;

    @Autowired
    private ThSessionDao thSessionDao;

    @Autowired
    private NebulaRelatedService nebulaRelatedService;

    @Autowired
    private EsearchService esearchService;

    @Autowired
    private IpService ipService;

    @Autowired
    private DomainService domainService;

    @Autowired
    private CertService certService;

    /**
     * 初始化标签字典
     */
    @PostConstruct
    private void initMap() {
        List<TagLibraryVo> tagList = new ArrayList<>();
        tagList = thSessionDao.listAllTagAttribute();
        for (TagLibraryVo vo : tagList) {
            TAG_INFO_MAP.put(vo.getTagId(), vo);
        }
    }

    @Override
    public BoolQueryBuilder getTargetTagCondition(AnalysisBaseCondition condition, String targetType, BoolQueryBuilder queryBuilder) {
        // 1.根据不同的标签类型去处理分隔查询条件`
        Map<String, List<List<String>>> tagFiltertMap = TagQueryUtils.filterTagByType(TAG_INFO_MAP, condition.getTagQuery());

        // 2.若存在目标标签查询条件
        Map<String, Object> filterResult = nebulaRelatedService.handleTargetTagQuery(tagFiltertMap, queryBuilder, targetType);
        queryBuilder = (BoolQueryBuilder) filterResult.get("queryBuilder");

        boolean hasTarget = filterResult.get("has_target") != null && (boolean) filterResult.get("has_target");
        // 判断是否存在目标标签查询结果，若不存在则直接返回
        if (!hasTarget) {
            return null;
        }

        // 3.符合会话&其他类型的标签查询
        queryBuilder = TagQueryUtils.handleStanTagQuery(tagFiltertMap, queryBuilder);

        // 4.如果有证书相关的抽取证书相关的标签进行sha1预查询
        if (CollUtil.isNotEmpty(tagFiltertMap.get("cert")) || CollUtil.isNotEmpty(tagFiltertMap.get("certNot"))) {
            queryBuilder = collectCertSHA1QueryCondition(tagFiltertMap, queryBuilder);
        }

        return queryBuilder;
    }

    /**
     * 单独抽取出符合标签条件的证书SHA1
     * @param tagFiltertMap
     * @return
     */
    private List<String> collectCertSHA1ListWithTag(Map<String, List<List<String>>> tagFiltertMap){
        // 证书标签正反选集合
        List<List<String>> certTagList = tagFiltertMap.get("cert");
        List<List<String>> certNotTagList = tagFiltertMap.get("certNot");

        // 如果没有任何过滤条件，直接返回原查询构建器
        if (CollUtil.isEmpty(certTagList) && CollUtil.isEmpty(certNotTagList)) {
            return new ArrayList<>();
        }

        BoolQueryBuilder certSHA1QueryBuilder = new BoolQueryBuilder();

        // 处理正选条件
        if (CollUtil.isNotEmpty(certTagList)) {
            for (List<String> tagList : certTagList) {
                certSHA1QueryBuilder.must(QueryBuilders.termsQuery("Labels", tagList));
            }
        }

        // 处理反选条件
        if (CollUtil.isNotEmpty(certNotTagList)) {
            for (List<String> tagList : certNotTagList) {
                certSHA1QueryBuilder.mustNot(QueryBuilders.termsQuery("Labels", tagList));
            }
        }

        try {
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest("cert_user");
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(certSHA1QueryBuilder)
                    .fetchSource("ASN1SHA1", null)
                    .size(5000)
                    .from(0);
            searchRequest.source(searchSourceBuilder);

            // 执行搜索
            SearchResponse searchResponse = esearchService.esSearch(searchRequest);
            SearchHits searchHits = searchResponse.getHits();

            // 提取SHA1列表
            List<String> sha1List = new ArrayList<>();
            for (SearchHit hit : searchHits.getHits()) {
                String sha1 = MapUtil.getStr(hit.getSourceAsMap(), "ASN1SHA1");
                if (sha1 != null) {
                    sha1List.add(sha1);
                }
            }

            // 如果找到了SHA1，根据查询类型添加到主查询中
            if (CollUtil.isNotEmpty(sha1List)) {
                return sha1List;
            }
            return new ArrayList<>();

        } catch (Exception e) {
            // 根据你的需求处理异常
            logger.error("Error while searching certificates: ", e);
            throw new GkException(GkErrorEnum.CERT_TAG_QUERY_ERROR);
        }
    }

    /**
     * 抽取符合证书标签的sha1查询条件到ES查询条件中（FROM ES）
     *
     * @param tagFiltertMap
     * @param queryBuilder
     * @return
     */
    private BoolQueryBuilder collectCertSHA1QueryCondition(Map<String, List<List<String>>> tagFiltertMap, BoolQueryBuilder queryBuilder) {
        if (tagFiltertMap == null || queryBuilder == null) {
            return queryBuilder;
        }

        // 证书标签正反选集合
        List<List<String>> certTagList = tagFiltertMap.get("cert");
        List<List<String>> certNotTagList = tagFiltertMap.get("certNot");

        // 如果没有任何过滤条件，直接返回原查询构建器
        if (CollUtil.isEmpty(certTagList) && CollUtil.isEmpty(certNotTagList)) {
            return queryBuilder;
        }

        BoolQueryBuilder certSHA1QueryBuilder = new BoolQueryBuilder();

        // 处理正选条件
        if (CollUtil.isNotEmpty(certTagList)) {
            for (List<String> tagList : certTagList) {
                certSHA1QueryBuilder.must(QueryBuilders.termsQuery("Labels", tagList));
            }
        }

        // 处理反选条件
        if (CollUtil.isNotEmpty(certNotTagList)) {
            for (List<String> tagList : certNotTagList) {
                certSHA1QueryBuilder.mustNot(QueryBuilders.termsQuery("Labels", tagList));
            }
        }

        try {
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest("cert_user");
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(certSHA1QueryBuilder)
                    .fetchSource("ASN1SHA1", null)
                    .size(5000)
                    .from(0);
            searchRequest.source(searchSourceBuilder);

            // 执行搜索
            SearchResponse searchResponse = esearchService.esSearch(searchRequest);
            SearchHits searchHits = searchResponse.getHits();

            // 提取SHA1列表
            List<String> sha1List = new ArrayList<>();
            for (SearchHit hit : searchHits.getHits()) {
                String sha1 = MapUtil.getStr(hit.getSourceAsMap(), "ASN1SHA1");
                if (sha1 != null) {
                    sha1List.add(sha1);
                }
            }

            // 如果找到了SHA1，根据查询类型添加到主查询中
            if (!sha1List.isEmpty()) {
                BoolQueryBuilder shouldBuilder = new BoolQueryBuilder();
                shouldBuilder.should(QueryBuilders.termsQuery("SSL.dCertHash",sha1List));
                shouldBuilder.should(QueryBuilders.termsQuery("SSL.sCertHash",sha1List));
                queryBuilder.must(shouldBuilder);
            }

        } catch (Exception e) {
            // 根据你的需求处理异常
            logger.error("Error while searching certificates: ", e);
        }

        return queryBuilder;
    }

    @Override
    public ResultVo getAggrIpList(AnalysisBaseCondition condition) {

        // 存放在redis和查询出来的IP信息键值MAP
        Map<String, Long> ipCountMap = null;

        // 用于排序的
        List<IpSortVo> allIpList = null;
        Set<String> filterData = new HashSet<>();

        logger.info("IP列表聚合查询，condition->{}", condition);
        ResultVo errorVo = ESQueryUtil.checkParam(condition);

        PageResultVo<IPListVo> result = new PageResultVo<>();

        if (errorVo != null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        // redis检验缓存中是否存在列表数据
        String redisKey = "IP_LIST:" + ESQueryUtil.getRedisKey(condition);
        try {
            String redisJson = RedisUtil.get(redisKey);
            if (StringUtils.isNotEmpty(redisJson)) {
                // 将redisJson获取到的数据转换为Map<String,Long>
                ipCountMap = JSONObject.parseObject(redisJson, Map.class);
                logger.info("IP列表缓存数据查询成功,键值对数-->{}", ipCountMap.size());
            }

            String redisFilterData = RedisUtil.get(redisKey + "_filterData");
            if (StringUtils.isNotEmpty(redisFilterData)) {
                redisFilterData = redisFilterData.replace("[", "").replace("]", "");
                Collections.addAll(filterData, redisFilterData.split(", "));
            }
        } catch (Exception e) {
            logger.error("IP列表redis获取异常,error:", e);
            throw new GkException(GkErrorEnum.REDIS_QUERY_ERROR);
        }

        if (MapUtil.isNotEmpty(ipCountMap)) {
            // 若存在缓存数据，刷新缓存时间
            RedisUtil.expire(redisKey, 30);
            RedisUtil.expire(redisKey + "_filterData", 10);
        } else {
            // 根据条件开始聚合查询数据，返回没有进行排序和初筛的部分
            Map<String, Object> resultMap = aggrQueryIpList(condition);
            if (MapUtil.isEmpty(resultMap)) {
                result.setTotal(0);
                result.setRecords(new ArrayList<>());
                return ResultVo.success(result);
            }
            ipCountMap = (Map<String, Long>) resultMap.get("dataMap");
            filterData = (Set<String>) resultMap.get("filterIpSet");
            // 聚合查询完毕之后，刷新Redis缓存
            RedisUtil.setEx(redisKey, JSON.toJSONString(ipCountMap), 30);
            RedisUtil.setEx(redisKey + "_filterData", filterData.toString(), 30);
        }

        // 从这里获取到已存的Map之后进行排序和分页 所有IP，这里只循环分页所需的ip具体查询
        assert ipCountMap != null;
        allIpList = Lists.newArrayListWithCapacity(ipCountMap.size());

        //6.分开V4和V6的IP排序
        List<IpSortVo> ipv4SortVoList = new ArrayList<>();
        List<IpSortVo> ipv6SortVoList = new ArrayList<>();

        for (String ip : ipCountMap.keySet()) {
            IpSortVo ipSortVo = new IpSortVo();
            ipSortVo.setIp(ip);
            // 在这里对返回的值进行判定，如果是Integer转换成Long
            Object count = ipCountMap.get(ip);
            if (count instanceof Integer) {
                ipSortVo.setCount(((Integer) count).longValue());
            } else if (count instanceof Long) {
                ipSortVo.setCount((Long) count);
            } else {
                // 处理 count 不是 Integer 或 Long 的情况
                ipSortVo.setCount(0L); // 或者其他默认值
            }
            if (IpUtils.isIpv4Str(ip)) {
                ipSortVo.setIpv4Num(IpUtils.ipv4ToNumeric(ip));
                ipv4SortVoList.add(ipSortVo);
            } else if (IpUtils.isIpv6Str(ip)) {
                ipSortVo.setIpv6Num(IpUtils.ipv6ToNumeric(ip));
                ipv6SortVoList.add(ipSortVo);
            }
        }

        // 进行排序
        String orderField = condition.getOrderField();
        Boolean asc = condition.getAsc();

        if ("IP".equals(orderField)) {
            if (asc) {
                ipv4SortVoList = ipv4SortVoList.stream().sorted(Comparator.comparingLong(IpSortVo::getIpv4Num)).collect(Collectors.toList());
                ipv6SortVoList = ipv6SortVoList.stream().sorted(Comparator.comparing(IpSortVo::getIpv6Num)).collect(Collectors.toList());
            } else {
                ipv4SortVoList = ipv4SortVoList.stream().sorted(Comparator.comparingLong(IpSortVo::getIpv4Num).reversed()).collect(Collectors.toList());
                ipv6SortVoList = ipv6SortVoList.stream().sorted(Comparator.comparing(IpSortVo::getIpv6Num).reversed()).collect(Collectors.toList());
            }
        }

        allIpList.addAll(ipv4SortVoList);
        allIpList.addAll(ipv6SortVoList);

        filterIpVolistByCondition(allIpList, filterData, condition);

        // 查询总量赋值 & 分页赋值
        result.setTotal(allIpList.size());

        Integer currentPage = condition.getCurrentPage();
        Integer pageSize = condition.getPageSize();
        allIpList = PageResultVo.pageList(allIpList, currentPage, pageSize);
        // 抽取出allIpList中每一个实体的IP具体值生成新的List<String>
        List<String> ipStrList = allIpList.stream()
                .map(IpSortVo::getIp)
                .collect(Collectors.toList());

        // 开始对聚合出的IP进行批量赋值，查询Nebula的补充内容
        List<IPListVo> resultList = nebulaRelatedService.getIpListNebulaData(allIpList, ipStrList);

        result.setRecords(resultList);
        return ResultVo.success(result);
    }

    @Override
    public ResultVo getAggrDomainList(AnalysisBaseCondition condition) throws UnsupportedEncodingException {

        List<DomainSortVo> allDomainList = null;
        Set<String> filterData = new HashSet<>();

        logger.info("域名列表聚合查询，condition->{}", condition);
        ResultVo errorVo = ESQueryUtil.checkParam(condition);

        PageResultVo<DomainListVo> result = new PageResultVo<>();

        if (errorVo != null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        // redis检验缓存中是否存在列表数据
        String redisKey = "DOMAIN_LIST:" + ESQueryUtil.getRedisKey(condition);
        try {
            String redisJson = RedisUtil.get(redisKey);
            if (StringUtils.isNotEmpty(redisJson)) {
                allDomainList = JSONObject.parseArray(redisJson, DomainSortVo.class);
                logger.info("域名列表缓存数据查询成功,数组长度-->{}", allDomainList.size());
            }

            String redisFilterData = RedisUtil.get(redisKey + "_filterData");
            if (StringUtils.isNotEmpty(redisFilterData)) {
                redisFilterData = redisFilterData.replace("[", "").replace("]", "");
                Collections.addAll(filterData, redisFilterData.split(", "));
            }
        } catch (Exception e) {
            logger.error("DOMAIN列表redis获取异常,error->{}", e);
            throw new GkException(GkErrorEnum.REDIS_QUERY_ERROR);
        }

        if (CollectionUtils.isNotEmpty(allDomainList)) {
            // 若存在缓存数据，刷新缓存时间
            RedisUtil.expire(redisKey, 30);
            RedisUtil.expire(redisKey + "_filterData", 10);
        } else {
            // 根据条件开始聚合查询数据
            allDomainList = aggrQueryDomainList(condition);
            if (CollectionUtils.isEmpty(allDomainList)) {
                result.setRecords(new ArrayList<>());
                result.setTotal(0);
                return ResultVo.success(result);
            }
            // 聚合查询完毕之后，刷新Redis缓存
            RedisUtil.setEx(redisKey, JSON.toJSONString(allDomainList), 30);
            RedisUtil.setEx(redisKey + "_filterData", filterData.toString(), 30);
        }

        String orderField = condition.getOrderField();
        Boolean asc = condition.getAsc();

        result.setTotal(allDomainList.size());
        if ("DOMAIN".equals(orderField)) {
            if (asc) {
                allDomainList = allDomainList.stream().sorted(Comparator.comparing(DomainSortVo::getDomain)).collect(Collectors.toList());
            } else {
                allDomainList = allDomainList.stream().sorted(Comparator.comparing(DomainSortVo::getDomain).reversed()).collect(Collectors.toList());
            }
        }

        Integer currentPage = condition.getCurrentPage();
        Integer pageSize = condition.getPageSize();
        allDomainList = PageResultVo.pageList(allDomainList, currentPage, pageSize);

        result = nebulaRelatedService.getDomainListNebulaData(allDomainList, result);

        return ResultVo.success(result);
    }

    /**
     * es_index下获取查询索引集
     *
     * @param condition
     * @return
     */
    private List<String> queryFirstIndexName(AnalysisBaseCondition condition) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest("es_index");
        boolQueryBuilder.must(QueryBuilders.wildcardQuery("index.keyword", esConnectIndex));
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }
        //es_index的时间范围特殊
        ESQueryUtil.delEsIndexTime(boolQueryBuilder, timeRange.getLeft(), timeRange.getRight());
        List<Integer> taskId = condition.getTaskId();
        if (taskId != null && taskId.size() > 0) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("task", taskId));
        }
        searchSourceBuilder.size(10000);
        searchRequest.source(searchSourceBuilder);
        List<Map<String, Object>> esResultMapList = esearchService.normalSearch(searchRequest);

        //这里判断返回值 esResultMapList
        Map<String, String> map = new HashMap<>();
        List<String> indexNames = new ArrayList<>();
        if (esResultMapList.size() > 1000) {
            for (Map<String, Object> esResultMap : esResultMapList) {
                String index = esResultMap.get("index").toString();
                String[] sliV = index.split("_");
                map.put(sliV[0] + "_" + sliV[1] + "_*", "");
            }
            Set<String> keySet = map.keySet();
            indexNames = new ArrayList<>(keySet);
            return indexNames;
        }
        for (Map<String, Object> esResultMap : esResultMapList) {
            String index = esResultMap.get("index").toString();
            indexNames.add(index);
        }
        return indexNames;
    }

    //查询满足查询条件的es的id
    private ResultVo<List<String>> getEsIds(BoolQueryBuilder queryBuilder, List<String> indexNames) {
        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        aggrSourceBuilder.query(queryBuilder);
        countRequest.source(aggrSourceBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();

        if (count == 0) {
            return ResultVo.success();
        }

        Integer limit = esLimit;
        if (count < esLimit) {
            //设置循环参数
            // 如果当前count 超过最大esLimit 那最大为10w条
            limit = (int) count;
        }

        ResultVo<List<String>> esIds = esearchService.getEsIds(10000, limit, queryBuilder, indexNames, "CreateTime", "_id");

        return esIds;
    }

    /**
     * 处理域名聚合bucket数据
     *
     * @param buckets
     * @param allDomain
     * @param domains
     */
    private void delList(List<? extends Terms.Bucket> buckets, Map<String, Long> allDomain) {
        buckets.parallelStream().forEach(bucket -> {
            String domain = bucket.getKeyAsString();
            long docCount = bucket.getDocCount();
            if (allDomain.containsKey(domain)) {
                docCount = docCount + allDomain.get(domain);
            }
            allDomain.put(domain, docCount);
        });
    }

    /**
     * 聚合查询IP列表
     *
     * @param condition
     * @return
     */
    private Map<String, Object> aggrQueryIpList(AnalysisBaseCondition condition) {

        // 前置查询聚合索引
        List<String> indexNames = queryFirstIndexName(condition);

        // 初始化分页数据
        PageResultVo pageResultVo = new PageResultVo();
        pageResultVo.setRecords(new ArrayList<>());
        pageResultVo.setTotal(0);

        if (CollectionUtils.isEmpty(indexNames)) {
            return new HashMap<>();
        }

        // 1.判断元数据字段条件查询->返回对应的sessionId
        Map<String, BoolQueryBuilder> metaDataQueryMap = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(metaDataQueryMap);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.isEmpty()) {
            return new HashMap<>();
        }

        // 2.初始化ES查询条件
        BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && !sessionIdsByMateDataQuery.isEmpty()) {
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        // 3.处理查询条件中的标签字段，返回Nebula中存在标签关系的vid集合
        Map<String, List<List<String>>> tagFiltertMap = TagQueryUtils.filterTagByType(TAG_INFO_MAP, condition.getTagQuery());

        // 若存在目标标签查询条件
        Map<String, Object> filterResult = nebulaRelatedService.handleTargetTagQuery(tagFiltertMap, queryBuilder, "ALL");
        boolean hasTarget = filterResult.get("has_target") != null && (boolean) filterResult.get("has_target");
        // 判断是否存在目标标签查询结果，若不存在则直接返回
        if (!hasTarget) {
            return new HashMap<>();
        }
        queryBuilder = (BoolQueryBuilder) filterResult.get("queryBuilder");

        // 对证书标签进行处理筛选过滤
        queryBuilder = collectCertSHA1QueryCondition(tagFiltertMap,queryBuilder);

        // 目标集合赋值
        Map<String, Set<String>> targetFilterMap = (Map<String, Set<String>>) filterResult.get("targetFilterMap");

        // 4.组装对connectInfo ID集合的查询
        //符合会话&其他类型的标签查询
        TagQueryUtils.handleStanTagQuery(tagFiltertMap, queryBuilder);

        //connectinfo索引 的排序字段
        ResultVo<List<String>> voEsIds = getEsIds(queryBuilder, indexNames);

        Integer err = voEsIds.getErr();
        List<String> esIdList = voEsIds.getData();
        if (CollectionUtils.isEmpty(esIdList)) {
            return new HashMap<>();
        }

        //重新生成查询条件，将预聚合IDS作为查询条件,生成
        queryBuilder = new BoolQueryBuilder();
        queryBuilder.filter(QueryBuilders.termsQuery("_id", esIdList));
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        aggrSourceBuilder.query(queryBuilder);
        aggrSourceBuilder.size(0);

        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        TermsAggregationBuilder sipAggBuilder = AggregationBuilders.terms("sIps").field("sIp").size(esLimit);
        TermsAggregationBuilder dipAggBuilder = AggregationBuilders.terms("dIps").field("dIp").size(esLimit);
        aggrSourceBuilder.aggregation(sipAggBuilder);
        aggrSourceBuilder.aggregation(dipAggBuilder);
        searchRequest.source(aggrSourceBuilder);

        long t1 = System.currentTimeMillis();
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long t2 = System.currentTimeMillis();
        logger.info("ip聚合列表查询耗时t={}", (t2 - t1));

        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStringTerms sipAgg = aggregations.get("sIps");
        ParsedStringTerms dipAgg = aggregations.get("dIps");
        List<? extends Terms.Bucket> sipBuckets = sipAgg.getBuckets();
        List<? extends Terms.Bucket> dipBuckets = dipAgg.getBuckets();

        // 判断当前数据是否为空
        if (sipBuckets.isEmpty() && dipBuckets.isEmpty()) {
            return new HashMap<>();
        }

        //5.分离所有ip集合以及分属 sip/dip 形成对应的IP->count 的键值对Map
        Map<String, Long> ipCountMap = new ConcurrentHashMap<>(dipBuckets.size() + sipBuckets.size());
        sipBuckets.parallelStream().forEach(sipBucket -> {
            String ip = sipBucket.getKeyAsString();
            long docCount = sipBucket.getDocCount();
            ipCountMap.put(ip, docCount);
        });

        dipBuckets.parallelStream().forEach(dipBucket -> {
            String ip = dipBucket.getKeyAsString();
            long docCount = dipBucket.getDocCount();
            if (ipCountMap.containsKey(ip)) {
                //同一会话里，dip和sip是一定不相同的。那么，这个sip+dip的会话分析数，就等于ip自身存在的会话分析数量
                docCount = docCount + ipCountMap.get(ip);
            }
            ipCountMap.put(ip, docCount);
        });

        // 遍历ipCountMap，抽取其中的ip生成一个List<String>，用于后续的Nebula查询
        List<String> ipList = new ArrayList<>(ipCountMap.keySet());

        Map<String, Long> existIpCountMap = null;
        try {
            List<ExistIpPropertiesVo> existIpList = ipService.listExistIpProperties(ipList);
            existIpCountMap = new HashMap<>(existIpList.size());
            for (ExistIpPropertiesVo existIpPropertiesVo : existIpList) {
                String existIp = existIpPropertiesVo.getIpAddr();
                existIpCountMap.put(existIp, ipCountMap.get(existIp));
            }

        } catch (Exception e) {
            logger.info("Nebula查询IP列表异常，error->", e);
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("dataMap", existIpCountMap);
        resultMap.put("filterIpSet", targetFilterMap.get("IP") == null ? new HashSet<>() : targetFilterMap.get("IP"));

        return resultMap;
    }

    /**
     * 过滤符合目标标签查询的IP字符串数据
     *
     * @param allIpList
     * @param filterData
     * @param condition
     * @return
     */
    public static void filterIpVolistByCondition(List<IpSortVo> allIpList, Set<String> filterData, AnalysisBaseCondition condition) {
        //正向查询IP列表过滤
        List<String> searchIpList = new ArrayList<>();
        List<String> reverseIpList = new ArrayList<>();

        // 标签过滤后的IP
        if (!filterData.isEmpty()) {
            if (filterData.size() == 1 && filterData.contains("")) {
                return;
            }
            searchIpList.addAll(filterData);
        }

        if (!CollectionUtils.isEmpty(searchIpList)) {
            Iterator iterator = allIpList.iterator();
            while (iterator.hasNext()) {
                IpSortVo vo = (IpSortVo) iterator.next();
                String ip = vo.getIp();
                if (!searchIpList.contains(ip)) {
                    iterator.remove();
                    continue;
                }
            }
        }

        if (!CollectionUtils.isEmpty(reverseIpList)) {
            Iterator iterator = allIpList.iterator();
            while (iterator.hasNext()) {
                IpSortVo vo = (IpSortVo) iterator.next();
                String ip = vo.getIp();
                if (reverseIpList.contains(ip)) {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 聚合查询域名列表
     *
     * @param condition
     * @return
     */
    private List<DomainSortVo> aggrQueryDomainList(AnalysisBaseCondition condition) {
        List<DomainSortVo> allDomainList = null;
        Map<String, Set<String>> targetFilterMap = new HashMap<>();

        // 前置查询聚合索引
        List<String> indexNames = queryFirstIndexName(condition);

        if (CollectionUtils.isEmpty(indexNames)) {
            return new ArrayList<>();
        }

        // 1.判断元数据字段条件查询->返回对应的sessionId
        Map<String, BoolQueryBuilder> metaDataQueryMap = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(metaDataQueryMap);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() < 1) {
            return new ArrayList<>();
        }

        // 2.初始化ES查询条件
        BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() > 0) {
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        // 3.处理查询条件中的标签字段，返回Nebula中存在标签关系的vid集合
        Map<String, List<List<String>>> tagFiltertMap = TagQueryUtils.filterTagByType(TAG_INFO_MAP, condition.getTagQuery());

        // 若存在目标标签查询条件
        Map<String, Object> filterResult = nebulaRelatedService.handleTargetTagQuery(tagFiltertMap, queryBuilder, "ALL");
        boolean hasTarget = filterResult.get("has_target") != null && (boolean) filterResult.get("has_target");
        // 判断是否存在目标标签查询结果，若不存在则直接返回
        if (!hasTarget) {
            return new ArrayList<>();
        }
        queryBuilder = (BoolQueryBuilder) filterResult.get("queryBuilder");

        // 对证书标签进行处理筛选过滤
        queryBuilder = collectCertSHA1QueryCondition(tagFiltertMap,queryBuilder);

        // 目标集合赋值
        targetFilterMap = (Map<String, Set<String>>) filterResult.get("targetFilterMap");

        // 4.组装对connectInfo ID集合的查询
        //符合会话&其他类型的标签查询
        queryBuilder = TagQueryUtils.handleStanTagQuery(tagFiltertMap, queryBuilder);

        // 5.直接过滤存在域名信息的会话SessionId
        BoolQueryBuilder existBuilder = new BoolQueryBuilder();
        existBuilder.should(QueryBuilders.existsQuery("DNS.Domain"));
        existBuilder.should(QueryBuilders.existsQuery("HTTP.Host"));
        existBuilder.should(QueryBuilders.existsQuery("SSL.CH_ServerName"));
        queryBuilder.must(existBuilder);

        //connectinfo索引 的排序字段
        ResultVo<List<String>> voEsIds = getEsIds(queryBuilder, indexNames);

        Integer err = voEsIds.getErr();
        List<String> esIdList = voEsIds.getData();
        if (CollectionUtils.isEmpty(esIdList)) {
            return new ArrayList<>();
        }

        //重新生成查询条件，将预聚合IDS作为查询条件,生成
        queryBuilder = new BoolQueryBuilder();
        queryBuilder.filter(QueryBuilders.termsQuery("_id", esIdList));
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        aggrSourceBuilder.query(queryBuilder);
        aggrSourceBuilder.size(0);

        //A:CH_ServerName   B:HTTP.Host   C:DNS.Domain
        String[] exclude = {""};
        IncludeExclude includeExclude = new IncludeExclude(null, exclude);
        aggrSourceBuilder.aggregation(AggregationBuilders.terms("SSL").field("SSL.CH_ServerName.keyword").includeExclude(includeExclude).size(esLimit));
        aggrSourceBuilder.aggregation(AggregationBuilders.terms("HTTP").field("HTTP.Host.keyword").includeExclude(includeExclude).size(esLimit));
        aggrSourceBuilder.aggregation(AggregationBuilders.terms("DNS").field("DNS.Domain.keyword").includeExclude(includeExclude).size(esLimit));
        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        aggrSourceBuilder.size(0);
        searchRequest.source(aggrSourceBuilder);
        long t1 = System.currentTimeMillis();
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long t2 = System.currentTimeMillis();
        logger.info("域名列表，域名集合查询耗时t={}", (t2 - t1));
        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStringTerms sslAgg = aggregations.get("SSL");
        ParsedStringTerms httpAgg = aggregations.get("HTTP");
        ParsedStringTerms dnsAgg = aggregations.get("DNS");
        List<? extends Terms.Bucket> sslBuckets = sslAgg.getBuckets();
        List<? extends Terms.Bucket> httpBuckets = httpAgg.getBuckets();
        List<? extends Terms.Bucket> dnsBuckets = dnsAgg.getBuckets();
        Map<String, Long> allDomain = new ConcurrentHashMap<>(sslBuckets.size() + httpBuckets.size() + dnsBuckets.size());
        delList(sslBuckets, allDomain);
        delList(httpBuckets, allDomain);
        delList(dnsBuckets, allDomain);

        if (MapUtil.isEmpty(allDomain)) {
            return new ArrayList<>();
        }

        // 遍历allDomain，抽取其中的domain_addr 生成对应的List<String>，用于后续的Nebula查询
        List<String> domainList = new ArrayList<>();
        for (String domain : allDomain.keySet()) {
            if (domain.length() > 200) {
                domain = SecureUtil.md5(domain);
            }
            domainList.add(domain);
        }
        Map<String, Long> existDomainCountMap = null;
        try {
            List<ExistDomainPropertiesVo> existDomainList = domainService.listExistDomainProperties(domainList);
            existDomainCountMap = new HashMap<>(existDomainList.size());
            for (ExistDomainPropertiesVo existDomainPropertiesVo : existDomainList) {
                String existDomain = existDomainPropertiesVo.getDomainAddr();
                existDomainCountMap.put(existDomain, allDomain.get(existDomain));
            }
        } catch (Exception e) {
            logger.info("Nebula查询域名列表异常，error->", e);
        }

        allDomainList = Lists.newArrayListWithCapacity(existDomainCountMap.size());
        Set<String> filterDomainSet = targetFilterMap.get("DOMAIN") == null ? new HashSet<>() : targetFilterMap.get("DOMAIN");
        allDomainList = filterDomainListByCondition(allDomainList, existDomainCountMap, filterDomainSet, condition);

        return allDomainList;
    }

    private List<DomainSortVo> filterDomainListByCondition(List<DomainSortVo> allDomainList, Map<String, Long> allDomain, Set<String> filterData, AnalysisBaseCondition condition) {
        // 查询域名列表过滤
        List<AnalysisBaseCondition.QueryOb> queryObList = condition.getQuery();
        List<String> searchDomainLst = new ArrayList<>();
        List<String> reverseDomainList = new ArrayList<>();

        // 域名查询条件存在模糊查询的情况
        List<String> wildcardDomainList = new ArrayList<>();
        List<String> notWildcardDomainList = new ArrayList<>();

        // 标签过滤后的domain集合 符合标签的域名列表
        if (filterData.size() != 0) {
            searchDomainLst.addAll(filterData);
        }

        // 若没有过滤条件，全量查询
        if (!CollectionUtils.isEmpty(queryObList)) {
            for (AnalysisBaseCondition.QueryOb queryOb : queryObList) {
                List<AnalysisBaseCondition.SearchInfo> searchInfoList = queryOb.getSearch();
                // 正向检索
                if ("and".equals(queryOb.getBoolSearch())) {
                    for (AnalysisBaseCondition.SearchInfo searchInfo : searchInfoList) {
                        if ("AllDomain".equals(searchInfo.getTarget())) {
                            List<String> vals = searchInfo.getVal();
                            for (String domain : vals) {
                                // 判断是否为模糊匹配
                                if (domain.startsWith("*.")) {
                                    domain = domain.substring(2, domain.length());
                                    wildcardDomainList.add(domain);
                                } else if (domain.endsWith(".*")) {
                                    domain = domain.substring(0, domain.lastIndexOf("\\."));
                                    wildcardDomainList.add(domain);
                                } else {
                                    // 精确匹配
                                    searchDomainLst.add(domain);
                                }
                            }
                        }
                    }
                }
                if ("not".equals(queryOb.getBoolSearch())) {
                    for (AnalysisBaseCondition.SearchInfo searchInfo : searchInfoList) {
                        if ("AllDomain".equals(searchInfo.getTarget())) {
                            List<String> vals = searchInfo.getVal();
                            for (String domain : vals) {
                                if (domain.startsWith("*.")) {
                                    domain = domain.substring(2, domain.length());
                                    notWildcardDomainList.add(domain);
                                } else if (domain.endsWith(".*")) {
                                    domain = domain.substring(0, domain.lastIndexOf("\\."));
                                } else {
                                    // 精确匹配
                                }
                                reverseDomainList.add(domain);
                            }
                        }
                    }
                }
            }
        }

        Map<String, Long> resultMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(wildcardDomainList)) {
            Iterator iterator = allDomain.keySet().iterator();
            while (iterator.hasNext()) {
                String domain = (String) iterator.next();
                for (String wildcardDomain : wildcardDomainList) {
                    if (domain.contains(wildcardDomain)) {
                        resultMap.put(domain, allDomain.get(domain));
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(notWildcardDomainList)) {
            Iterator iterator = allDomain.keySet().iterator();
            while (iterator.hasNext()) {
                String domain = (String) iterator.next();
                for (String wildcardDomain : notWildcardDomainList) {
                    if (domain.contains(wildcardDomain)) {
                        resultMap.remove(domain);
                    }
                }
            }
        }

        if (!CollectionUtils.isEmpty(searchDomainLst)) {
            Iterator iterator = resultMap.keySet().iterator();
            while (iterator.hasNext()) {
                String domain = (String) iterator.next();
                if (!searchDomainLst.contains(domain)) {
                    iterator.remove();
                    continue;
                }
            }
        }

        if (!CollectionUtils.isEmpty(reverseDomainList)) {
            Iterator iterator = resultMap.keySet().iterator();
            while (iterator.hasNext()) {
                String domain = (String) iterator.next();
                if (reverseDomainList.contains(domain)) {
                    iterator.remove();
                    continue;
                }
            }
        }

        if (MapUtils.isNotEmpty(resultMap)) {
            return handleDomainVoList(searchDomainLst, resultMap);
        } else {
            return handleDomainVoList(searchDomainLst, allDomain);
        }

    }

    private List<DomainSortVo> handleDomainVoList(List<String> searchDomainLst, Map<String, Long> domainMap) {

        List<DomainSortVo> allDomainList = Lists.newArrayListWithCapacity(domainMap.size());

        for (String s : domainMap.keySet()) {
            if (IPAddressUtil.isIPv4LiteralAddress(s) || IPAddressUtil.isIPv6LiteralAddress(s)) {
                continue;
            }

            // 域名判断（过滤IP和尾缀为.arpa）
            String lowerDomainAddr = s.toLowerCase();
            if (lowerDomainAddr.endsWith("arpa") || s.startsWith("*.") || s.contains(":")) {
                continue;
            }

            if (CollectionUtils.isEmpty(searchDomainLst)) {
                DomainSortVo domainSortVo = new DomainSortVo();
                domainSortVo.setDomain(s);
                domainSortVo.setCount(domainMap.get(s));
                allDomainList.add(domainSortVo);
                continue;
            } else {
                for (String domain : searchDomainLst) {
                    if (s.contains(domain) || s.equals(domain)) {
                        DomainSortVo domainSortVo = new DomainSortVo();
                        domainSortVo.setDomain(s);
                        domainSortVo.setCount(domainMap.get(s));
                        allDomainList.add(domainSortVo);
                    }
                }
            }
        }

        return allDomainList;
    }

    @Override
    public ResultVo getAggrCertList(AnalysisBaseCondition condition) {
        List<CertListVo> allCertList = null;
        Set<String> filterData = new HashSet<>();

        logger.info("证书列表聚合查询，condition->{}", condition);
        ResultVo errorVo = ESQueryUtil.checkParam(condition);

        PageResultVo<CertListVo> result = new PageResultVo<>();

        if (errorVo != null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        // redis检验缓存中是否存在列表数据
        String redisKey = "CERT_LIST:" + ESQueryUtil.getRedisKey(condition);
        try {
            String redisJson = RedisUtil.get(redisKey);
            if (StringUtils.isNotEmpty(redisJson)) {
                allCertList = JSONObject.parseArray(redisJson, CertListVo.class);
                logger.info("证书列表缓存数据查询成功,数组长度-->{}", allCertList.size());
            }

            String redisFilterData = RedisUtil.get(redisKey + "_filterData");
            if (StringUtils.isNotEmpty(redisFilterData)) {
                redisFilterData = redisFilterData.replace("[", "").replace("]", "");
                Collections.addAll(filterData, redisFilterData.split(", "));
            }
        } catch (Exception e) {
            logger.error("证书列表redis获取异常,error->", e);
            throw new GkException(GkErrorEnum.REDIS_QUERY_ERROR);
        }

        if (CollectionUtils.isNotEmpty(allCertList)) {
            // 若存在缓存数据，刷新缓存时间
            RedisUtil.expire(redisKey, 30);
            RedisUtil.expire(redisKey + "_filterData", 10);
        } else {
            // 根据条件开始聚合查询数据
            allCertList = aggrQueryCertList(condition);
            if (CollectionUtils.isEmpty(allCertList)) {
                result.setTotal(0);
                result.setRecords(new ArrayList<>());
                return ResultVo.success(result);
            }
            // 聚合查询完毕之后，刷新Redis缓存
            RedisUtil.setEx(redisKey, JSON.toJSONString(allCertList), 30);
            RedisUtil.setEx(redisKey + "_filterData", filterData.toString(), 30);
        }

        result.setTotal(allCertList.size());

        String orderField = condition.getOrderField();
        Boolean asc = condition.getAsc();
        if ("CERT".equals(orderField)) {
            if (asc) {
                allCertList = allCertList.stream().sorted(Comparator.comparing(CertListVo::getCert)).collect(Collectors.toList());
            } else {
                allCertList = allCertList.stream().sorted(Comparator.comparing(CertListVo::getCert).reversed()).collect(Collectors.toList());
            }
        }

        Integer currentPage = condition.getCurrentPage();
        Integer pageSize = condition.getPageSize();
        allCertList = PageResultVo.pageList(allCertList, currentPage, pageSize);
        // 抽取出allCertList中的证书集合
        List<String> certStrList = allCertList.stream()
                .map(CertListVo::getCert)
                .collect(Collectors.toList());

        // 开始对聚合出的CERT进行批量赋值，查询Nebula的补充内容
        allCertList = nebulaRelatedService.getCertListNebulaData(allCertList, certStrList);

        result.setRecords(allCertList);
        return ResultVo.success(result);
    }

    private List<CertListVo> aggrQueryCertList(AnalysisBaseCondition condition) {
        List<CertListVo> allCertList;
        Map<String, Set<String>> targetFilterMap;

        // 前置查询聚合索引
        List<String> indexNames = queryFirstIndexName(condition);

        if (CollectionUtils.isEmpty(indexNames)) {
            return new ArrayList<>();
        }

        // 1.判断元数据字段条件查询->返回对应的sessionId
        Map<String, BoolQueryBuilder> metaDataQueryMap = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(metaDataQueryMap);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.isEmpty()) {
            return new ArrayList<>();
        }

        // 2.初始化ES查询条件
        BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && !sessionIdsByMateDataQuery.isEmpty()) {
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        // 3.处理查询条件中的标签字段，返回Nebula中存在标签关系的vid集合
        Map<String, List<List<String>>> tagFiltertMap = TagQueryUtils.filterTagByType(TAG_INFO_MAP, condition.getTagQuery());

        // 若存在目标标签查询条件
        Map<String, Object> filterResult = nebulaRelatedService.handleTargetTagQuery(tagFiltertMap, queryBuilder, "ALL");
        boolean hasTarget = filterResult.get("has_target") != null && (boolean) filterResult.get("has_target");
        // 判断是否存在目标标签查询结果，若不存在则直接返回
        if (!hasTarget) {
            return new ArrayList<>();
        }
        queryBuilder = MapUtil.get(filterResult, "queryBuilder", BoolQueryBuilder.class);

        // 目标集合赋值
        targetFilterMap = (Map<String, Set<String>>) filterResult.get("targetFilterMap");

        // 对证书标签进行处理筛选过滤
        queryBuilder = collectCertSHA1QueryCondition(tagFiltertMap,queryBuilder);

        // 4.组装对connectInfo ID集合的查询
        //符合会话&其他类型的标签查询
        queryBuilder = TagQueryUtils.handleStanTagQuery(tagFiltertMap, queryBuilder);

        // 5.直接过滤存在证书信息的会话数据
        BoolQueryBuilder existBuilder = new BoolQueryBuilder();
        existBuilder.should(QueryBuilders.existsQuery("SSL.sCertHash"));
        existBuilder.should(QueryBuilders.existsQuery("SSL.dCertHash"));
        queryBuilder.must(existBuilder);

        //connectinfo索引 的排序字段
        ResultVo<List<String>> voEsIds = getEsIds(queryBuilder, indexNames);

        List<String> esIdList = voEsIds.getData();
        if (CollectionUtils.isEmpty(esIdList)) {
            return new ArrayList<>();
        }

        //重新生成查询条件，将预聚合IDS作为查询条件
        queryBuilder = new BoolQueryBuilder();
        queryBuilder.filter(QueryBuilders.termsQuery("_id", esIdList));
        // 在APP_NAME为APP_SSL的会话中获取证书数据
        queryBuilder.must(QueryBuilders.termQuery("AppName", "APP_SSL"));
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        aggrSourceBuilder.query(queryBuilder);
        aggrSourceBuilder.size(0);
        aggrSourceBuilder.aggregation(AggregationBuilders.terms("dCert").field("SSL.dCertHash").size(esLimit));
        aggrSourceBuilder.aggregation(AggregationBuilders.terms("sCert").field("SSL.sCertHash").size(esLimit));
        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        searchRequest.source(aggrSourceBuilder);

        long t1 = System.currentTimeMillis();
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long t2 = System.currentTimeMillis();
        logger.info("证书列表，证书集合查询耗时t={}", (t2 - t1));

        Aggregations aggregations = searchResponse.getAggregations();
        ParsedStringTerms sCertAgg = aggregations.get("sCert");
        ParsedStringTerms dCertAgg = aggregations.get("dCert");
        List<? extends Terms.Bucket> sBuckets = sCertAgg.getBuckets();
        List<? extends Terms.Bucket> dBuckets = dCertAgg.getBuckets();
        Map<String, Long> allCert = new ConcurrentHashMap<>(dBuckets.size() + sBuckets.size());
        //关联会话数量
        dBuckets.parallelStream().forEach(dBucket -> {
            String key = dBucket.getKeyAsString();
            long docCount = dBucket.getDocCount();
            allCert.put(key, docCount);
        });
        sBuckets.parallelStream().forEach(sBucket -> {
            String key = sBucket.getKeyAsString();
            long docCount = sBucket.getDocCount();
            if (allCert.containsKey(key)) {
                allCert.put(key, allCert.get(key) + docCount);
            } else {
                allCert.put(key, docCount);
            }
        });
        if (MapUtil.isEmpty(allCert)) {
            return new ArrayList<>();
        }

        // 遍历allCert，抽取其中的cert_sha1，生成对应的List<String>
        List<String> certList = new ArrayList<>();
        for (String certSha1 : allCert.keySet()) {
            certList.add(certSha1);
        }

        List<String> convertSHA1List = collectCertSHA1ListWithTag(tagFiltertMap);
        // 如果存在标签匹配的证书sha1,取已经抽取出的SHA1列表与查询到的交集
        if(CollUtil.isNotEmpty(convertSHA1List)){
            certList = certList.stream().filter(convertSHA1List::contains).collect(Collectors.toList());
        }
        Map<String, Long> existCertCountMap = null;
        try {
            List<ExistCertPropertiesVo> existCertList = certService.listExistCertProperties(certList);
            existCertCountMap = new HashMap<>(existCertList.size());
            for (ExistCertPropertiesVo existCertPropertiesVo : existCertList) {
                String existCert = existCertPropertiesVo.getCertId();
                existCertCountMap.put(existCert, allCert.get(existCert));
            }
        } catch (Exception e) {
            logger.info("Nebula查询IP列表异常，error->", e);
        }

        allCertList = existCertCountMap.keySet().stream()
                .map(s -> {
                    CertListVo vo = new CertListVo();
                    vo.setCert(s);
                    vo.setCount(allCert.get(s));
                    return vo;
                })
                .collect(Collectors.toList());
        Set<String> filterCertSet = targetFilterMap.get("CERT") == null ? new HashSet<>() : targetFilterMap.get("CERT");
        allCertList = filterCertVoListByCondition(allCertList, filterCertSet, condition);

        return allCertList;

    }


    @Override
    public ResultVo getAggrFingerList(AnalysisBaseCondition condition) throws UnsupportedEncodingException {
        logger.info("指纹列表查询，condition={}", condition);
        long startTime = System.currentTimeMillis();
        ResultVo errorVo = ESQueryUtil.checkParam(condition);

        PageResultVo<FingerprintVo> result = new PageResultVo<>();

        if (errorVo != null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        List<FingerprintVo> fingerprintVoList = new ArrayList<>();
        Set<String> filterData = new HashSet<>();

        String redisKey = "FINGER_LIST:" + ESQueryUtil.getRedisKey(condition);
        try {
            String redisJson = RedisUtil.get(redisKey);
            if (StringUtils.isNotEmpty(redisJson)) {
                fingerprintVoList = JSONObject.parseArray(redisJson, FingerprintVo.class);
                logger.info("指纹列表缓存数据查询成功,数组长度-->{}", fingerprintVoList.size());
            }
            String redisFilterData = RedisUtil.get(redisKey + "_filterData");
            if (StringUtils.isNotEmpty(redisFilterData)) {
                redisFilterData = redisFilterData.replace("[", "").replace("]", "");
                Collections.addAll(filterData, redisFilterData.split(", "));
            }
        } catch (Exception e) {
            logger.error("指纹列表redis获取异常,error->{}", e);
            throw new GkException(GkErrorEnum.REDIS_QUERY_ERROR);
        }

        if (CollectionUtils.isNotEmpty(fingerprintVoList)) {
            RedisUtil.expire(redisKey, 30);
            RedisUtil.expire(redisKey + "_filterData", 10);
        } else {
            fingerprintVoList = aggrQueryFingerList(condition);
            if (CollectionUtils.isEmpty(fingerprintVoList)) {
                result.setRecords(new ArrayList<>());
                result.setTotal(0);
                return ResultVo.success(result);
            }
            // 聚合查询完毕之后，刷新Redis缓存
            RedisUtil.setEx(redisKey, JSON.toJSONString(fingerprintVoList), 30);
            RedisUtil.setEx(redisKey + "_filterData", filterData.toString(), 30);
        }


        Boolean asc = condition.getAsc();
        if (asc) {
            fingerprintVoList = fingerprintVoList.stream().sorted(Comparator.comparing(FingerprintVo::getFingerprint)).collect(Collectors.toList());
        } else {
            fingerprintVoList = fingerprintVoList.stream().sorted(Comparator.comparing(FingerprintVo::getFingerprint).reversed()).collect(Collectors.toList());
        }
        Integer currentPage = condition.getCurrentPage();
        Integer pageSize = condition.getPageSize();
        List<FingerprintVo> fingerList = PageResultVo.pageList(fingerprintVoList, currentPage, pageSize);

        // 前置查询聚合索引
        List<String> indexNames = queryFirstIndexName(condition);

        // 补充剩余数据
        long l1 = System.currentTimeMillis();
        fingerList = nebulaRelatedService.getFingerListNebulaData(indexNames, fingerList, condition);
        long l2 = System.currentTimeMillis();
        logger.info("指纹列表补充数据耗时t={}", (l2 - l1));

        // result data.
        result.setRecords(fingerList);
        result.setTotal(fingerprintVoList.size());
        logger.info("指纹列表查询结束，用时: {}秒", (System.currentTimeMillis() - startTime) / 1000);

        return ResultVo.success(result);
    }

    @Override
    public ResultVo getCommunicationSankey(CommunicationCondition condition) {

        logger.info("查询会话信息中通信信息桑基图，通过条件,condion:{}", condition);
        Integer limit = condition.getPageSize();
        if (limit == null) {
            limit = 10;
        }
        //获取索引集合
        AnalysisBaseCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AnalysisBaseCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }

        List<String> indexNames = queryFirstIndexName(condition);
        if (indexNames == null || indexNames.size() < 1) {
            return ResultVo.success(new ArrayList<>());
        }

        //先判断元数据查询
        Map<String, BoolQueryBuilder> map1 = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(map1);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() < 1) {
            return ResultVo.success(new ArrayList<>());
        }

        //配置基本的查询条件
        BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() > 0) {
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        Map<String, List<List<String>>> tagFiltertMap = TagQueryUtils.filterTagByType(TAG_INFO_MAP, condition.getTagQuery());

        // 若存在目标标签查询条件
        Map<String, Object> filterResult = nebulaRelatedService.handleTargetTagQuery(tagFiltertMap, queryBuilder, "ALL");
        boolean hasTarget = filterResult.get("has_target") != null && (boolean) filterResult.get("has_target");
        // 判断是否存在目标标签查询结果，若不存在则直接返回
        if (!hasTarget) {
            return ResultVo.success(new ArrayList<>());
        }
        queryBuilder = (BoolQueryBuilder) filterResult.get("queryBuilder");


        // 4.组装对connectInfo ID集合的查询
        //符合会话&其他类型的标签查询
        queryBuilder = TagQueryUtils.handleStanTagQuery(tagFiltertMap, queryBuilder);
        CountRequest countRequest = new CountRequest(indexNames.toArray(new String[indexNames.size()]));
        SearchSourceBuilder countSearchBuilder = new SearchSourceBuilder();
        countSearchBuilder.query(queryBuilder);
        countRequest.source(countSearchBuilder);
        CountResponse countResponse = esearchService.esSearchForCount(countRequest);
        long count = countResponse.getCount();
        if (count == 0) {
            return ResultVo.success(new ArrayList<>());
        }

        try {
            ResultVo<List<String>> voEsIds = getEsIds(queryBuilder, indexNames);
            if (voEsIds.getErr() != 0) {
                throw new GkException(GkErrorEnum.ES_SEARCH_ERROR);
            }

            List<String> esIds = voEsIds.getData();
            SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
            queryBuilder = new BoolQueryBuilder();
            queryBuilder.filter(QueryBuilders.termsQuery("_id", esIds));
            aggrSourceBuilder.query(queryBuilder);

            TermsAggregationBuilder termsAggregationBuilder = AggregationBuilders.terms("communication")
                    .size(limit)
                    .field("sip_appid_dport_dip");
            aggrSourceBuilder.size(0);
            aggrSourceBuilder.aggregation(termsAggregationBuilder);

            SearchRequest aggrRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
            aggrRequest.source(aggrSourceBuilder);
            // 直接返回聚合处理结果
            Aggregations aggregations = esearchService.aggrSearch(aggrRequest);
            ParsedStringTerms commuData = aggregations.get("communication");
            List<String> aggrResult = new ArrayList<>();

            // 开始处理结果数据
            List<CommunicationVo> voList = new ArrayList<>();
            Map<String, Object> resultMap = new HashMap<>();

            // metadata 结构 166.111.5.203_10071_53_8.8.8.8 sip_appid_dport_dip
            List<? extends Terms.Bucket> buckets = commuData.getBuckets();
            for (Terms.Bucket bucket : buckets) {
                String metadata = bucket.getKeyAsString();
                long docCount = bucket.getDocCount();
                CommunicationVo vo = new CommunicationVo();
                vo.setSIp(metadata.split("_")[0]);
                vo.setAppId(metadata.split("_")[1]);
                vo.setDstPort(metadata.split("_")[2]);
                vo.setDIp(metadata.split("_")[3]);
                vo.setCount(docCount);
                voList.add(vo);
                aggrResult.add(metadata);
            }

            resultMap.put("records", voList);
            resultMap.put("total", buckets.size());
            return ResultVo.success(resultMap);
        } catch (Exception e) {
            logger.error("查询会话信息中通信信息桑基图异常", e);
            throw new GkException(GkErrorEnum.ES_OPERATE_ERROR);
        }
    }

    private List<FingerprintVo> aggrQueryFingerList(AnalysisBaseCondition condition) {
        Map<String, Set<String>> targetFilterMap = new HashMap<>();

        // 前置查询聚合索引
        List<String> indexNames = queryFirstIndexName(condition);

        if (CollectionUtils.isEmpty(indexNames)) {
            return new ArrayList<>();
        }

        // 1.判断元数据字段条件查询->返回对应的sessionId
        Map<String, BoolQueryBuilder> metaDataQueryMap = ESQueryUtil.mateDataQuery(condition);
        List<String> sessionIdsByMateDataQuery = esearchService.getSessionIdsByMateDataQuery(metaDataQueryMap);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() < 1) {
            return new ArrayList<>();
        }

        // 2.初始化ES查询条件
        BoolQueryBuilder queryBuilder = ESQueryUtil.query(condition);
        if (sessionIdsByMateDataQuery != null && sessionIdsByMateDataQuery.size() > 0) {
            queryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIdsByMateDataQuery));
        }

        // 3.处理查询条件中的标签字段，返回Nebula中存在标签关系的vid集合
        Map<String, List<List<String>>> tagFiltertMap = TagQueryUtils.filterTagByType(TAG_INFO_MAP, condition.getTagQuery());

        // 若存在目标标签查询条件
        Map<String, Object> filterResult = nebulaRelatedService.handleTargetTagQuery(tagFiltertMap, queryBuilder, "ALL");
        boolean hasTarget = filterResult.get("has_target") != null && (boolean) filterResult.get("has_target");
        // 判断是否存在目标标签查询结果，若不存在则直接返回
        if (!hasTarget) {
            return new ArrayList<>();
        }
        queryBuilder = (BoolQueryBuilder) filterResult.get("queryBuilder");

        // 对证书标签进行处理筛选过滤
        queryBuilder = collectCertSHA1QueryCondition(tagFiltertMap,queryBuilder);

        // 目标集合赋值
        targetFilterMap = (Map<String, Set<String>>) filterResult.get("targetFilterMap");

        // 4.组装对connectInfo ID集合的查询
        //符合会话&其他类型的标签查询
        queryBuilder = TagQueryUtils.handleStanTagQuery(tagFiltertMap, queryBuilder);

        // 5.直接过滤存在指纹信息的会话SessionId
        BoolQueryBuilder existBuilder = new BoolQueryBuilder();
        existBuilder.mustNot(QueryBuilders.termQuery("sSSLFinger", "0"));
        existBuilder.mustNot(QueryBuilders.termQuery("dSSLFinger", "0"));
        queryBuilder.must(existBuilder);

        ResultVo<List<String>> voEsIds = getEsIds(queryBuilder, indexNames);

        Integer err = voEsIds.getErr();
        List<String> esIdList = voEsIds.getData();
        if (CollectionUtils.isEmpty(esIdList)) {
            return new ArrayList<>();
        }

        //重新生成查询条件，将预聚合IDS作为查询条件,生成
        queryBuilder = new BoolQueryBuilder();
        queryBuilder.filter(QueryBuilders.termsQuery("_id", esIdList));

        // 聚合获取指纹相关数据
        SearchRequest searchRequest = new SearchRequest(indexNames.toArray(new String[indexNames.size()])).preference("_only_nodes:box_type:hot");
        SearchSourceBuilder aggrSourceBuilder = new SearchSourceBuilder();
        aggrSourceBuilder.query(queryBuilder);
        TermsAggregationBuilder sSSLFinger = AggregationBuilders.terms("sSSLFingers").field("sSSLFinger").size(esLimit);
        TermsAggregationBuilder dSSLFinger = AggregationBuilders.terms("dSSLFingers").field("dSSLFinger").size(esLimit);
        aggrSourceBuilder.aggregation(sSSLFinger);
        aggrSourceBuilder.aggregation(dSSLFinger);

        // search hits size is 0
        aggrSourceBuilder.size(0);

        // source aggregation.
        searchRequest.source(aggrSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);

        // init FingerprintVo
        Aggregations aggregations = searchResponse.getAggregations();
        List<FingerprintVo> fingerprintVoList = new ArrayList<>();
        ParsedStringTerms sSSLFingersAgg = aggregations.get("sSSLFingers");
        List<? extends Terms.Bucket> sSSLFingersAggBuckets = sSSLFingersAgg.getBuckets();
        for (Terms.Bucket sSSLFingersAggBucket : sSSLFingersAggBuckets) {
            String key = sSSLFingersAggBucket.getKeyAsString();
            if ("0".equals(key)) {
                continue;
            }

            FingerprintVo finger = new FingerprintVo();
            finger.setFingerprint(key);
            finger.setFingerprintType("sSSLFinger");
            fingerprintVoList.add(finger);
        }

        ParsedStringTerms dSSLFingersAgg = aggregations.get("dSSLFingers");
        List<? extends Terms.Bucket> dSSLFingersAggBuckets = dSSLFingersAgg.getBuckets();
        for (Terms.Bucket dSSLFingersAggBucket : dSSLFingersAggBuckets) {
            String key = dSSLFingersAggBucket.getKeyAsString();
            if ("0".equals(key)) {
                continue;
            }

            FingerprintVo finger = new FingerprintVo();
            finger.setFingerprint(key);
            finger.setFingerprintType("dSSLFinger");
            fingerprintVoList.add(finger);
        }

        Set<String> filterFingerSet = targetFilterMap.get("FINGER") == null ? new HashSet<>() : targetFilterMap.get("FINGER");
        fingerprintVoList = filterFingerVoLstByCondition(fingerprintVoList, filterFingerSet, condition);

        return fingerprintVoList;

    }

    private List<FingerprintVo> filterFingerVoLstByCondition(List<FingerprintVo> fingerprintVoList, Set<String> filterData, AnalysisBaseCondition condition) {
        // 过滤条件，只展示符合条件的
        List<AnalysisBaseCondition.QueryOb> queryObList = condition.getQuery();

        // 正向查询指纹列表
        List<String> searchFingerList = new ArrayList<>();
        List<String> reverseFingerList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(filterData)) {
            searchFingerList.addAll(filterData);
        }

        // 留下正选项目
        if (!CollectionUtils.isEmpty(searchFingerList)) {
            Iterator iterator = fingerprintVoList.iterator();
            while (iterator.hasNext()) {
                FingerprintVo vo = (FingerprintVo) iterator.next();
                String sslFinger = vo.getFingerprint();
                if (!searchFingerList.contains(sslFinger)) {
                    iterator.remove();
                }
            }
        }

        // 去掉反选项目
        if (!CollectionUtils.isEmpty(reverseFingerList)) {
            Iterator iterator = fingerprintVoList.iterator();
            while (iterator.hasNext()) {
                FingerprintVo vo = (FingerprintVo) iterator.next();
                String sslFinger = vo.getFingerprint();
                if (reverseFingerList.contains(sslFinger)) {
                    iterator.remove();
                }
            }
        }

        return fingerprintVoList;
    }

    private List<CertListVo> filterCertVoListByCondition(List<CertListVo> list, Set<String> filterData, AnalysisBaseCondition condition) {
        // 正向查询证书列表过滤
        List<AnalysisBaseCondition.QueryOb> queryObList = condition.getQuery();
        List<String> searchCertList = new ArrayList<>();
        List<String> reverseCertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(filterData)) {
            searchCertList.addAll(filterData);
        }

        // 先去掉反选项目
        if (!CollectionUtils.isEmpty(reverseCertList)) {
            Iterator iterator = list.iterator();
            while (iterator.hasNext()) {
                CertListVo vo = (CertListVo) iterator.next();
                String certStr = vo.getCert();
                if (reverseCertList.contains(certStr)) {
                    iterator.remove();
                }
            }
        }

        if (!CollectionUtils.isEmpty(searchCertList)) {
            Iterator iterator = list.iterator();
            while (iterator.hasNext()) {
                CertListVo vo = (CertListVo) iterator.next();
                String certStr = vo.getCert();
                if (!searchCertList.contains(certStr)) {
                    iterator.remove();
                }
            }
        }
        return list;
    }

}

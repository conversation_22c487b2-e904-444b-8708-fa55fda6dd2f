package com.geeksec.analysis.entity.storage;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：指纹展示实体VO
 */
@Data
public class SSLFingerInfoVo {
    /**
     * 指纹ID
     */
    @JsonProperty(value = "finger_id")
    private String fingerId;

    /**
     * Ja3指纹Hash
     */
    @JsonProperty(value = "ja3_hash")
    private String ja3Hash;

    /**
     * 指纹说明
     */
    @JsonProperty(value = "desc")
    private String desc= CharSequenceUtil.EMPTY;

    /**
     * 客户端or服务端指纹
     */
    @JsonProperty(value = "type")
    private String type;

    /**
     * 标签集合
     */
    private List<Integer> labels;
}

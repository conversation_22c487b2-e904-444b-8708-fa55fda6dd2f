package com.geeksec.analysis.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：目标备注修改
 */
@Data
public class TargetRemarkCondition {
    /**
     * 目标识别值(sessionId ip ...)
     */
    @JsonProperty("target_key")
    private String targetKey;

    /**
     * 目标类型
     */
    @JsonProperty("target_type")
    private String targetType;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;
}

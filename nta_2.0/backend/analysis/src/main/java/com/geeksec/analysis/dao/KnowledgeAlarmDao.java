package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.KnowledgeAlarm;
import com.geeksec.analysis.entity.ModelAttackInfo;
import com.geeksec.analysis.entity.vo.KnowledgeAlarmVo;
import com.geeksec.analysis.entity.vo.KnowledgeTypeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-26
 */
@Mapper
public interface KnowledgeAlarmDao extends BaseMapper<KnowledgeAlarm> {


    /**
     * 获取完整的规则/知识库 列表
     *
     * @return
     */
    List<KnowledgeAlarmVo> getKnowledgeAlarmList(@Param("userId")Integer userId);

    /**
     * 获取告警的攻击类型    过滤规则默认的attackType为-1  attackTypeName=规则
     *
     * @return
     */
    List<KnowledgeTypeVo> getKnowledgeType();

    /**
     * 计算当前攻击链路的组类是否存在
     *
     * @param attackerIp
     * @param victimIp
     * @param labelStr
     * @return
     */
    long countAttackChain(@Param("attackerIp") String attackerIp, @Param("victimIp") String victimIp, @Param("labelStr") String labelStr);

    /**
     * 插入攻击链数据
     *
     * @param attackerIp
     * @param victimIp
     * @param labelStr
     */
    void insertAttackChain(@Param("attackerIp") String attackerIp, @Param("victimIp") String victimIp, @Param("labelStr") String labelStr);

    /**
     * 获取全量模型告警攻击链字典
     *
     * @return
     */
    List<ModelAttackInfo> getAttackChainMap();
}

package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: <PERSON>uanHao
 * @Date: 2022/5/27 10:20
 * @Description： <Functions List>
 */
@Data
public class SystemInfoVo {

    /**
     * 主机名称
     */
    @JsonProperty("hostname")
    private String hostname;

    /**
     * 操作系统
     */
    @JsonProperty("os_info")
    private String osinfo;

    /**
     * 系统时间
     */
    @JsonProperty("timeS")
    private String timeS;

    /**
     * 运行时间
     */
    @JsonProperty("time")
    private Long time;

    /**
     * 启动时间
     */
    @JsonProperty("start_time")
    private LocalDateTime startTime;
}

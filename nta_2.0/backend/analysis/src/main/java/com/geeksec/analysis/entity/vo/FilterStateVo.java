package com.geeksec.analysis.entity.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class FilterStateVo {

    @ApiModelProperty("任务id")
    @JsonProperty("task_id")
    private Integer taskId;
    @ApiModelProperty("0表示保留，1表示丢弃")
    private Integer state;
}

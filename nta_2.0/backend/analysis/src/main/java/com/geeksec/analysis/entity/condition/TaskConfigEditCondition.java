package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class TaskConfigEditCondition {

    /**
     * 系统类型
     */
    @JsonProperty("system")
    private String system;

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 批次ID
     */
    @JsonProperty("batch_id")
    private Integer batchId;

    /**
     * 描述
     */
    @JsonProperty("batch_remark")
    private String batchRemark;

    /**
     * 全流量留存 ON  OFF
     */
    @JsonProperty("fullflow_state")
    private String fullflowState;

    /**
     * 会话元数据保留 0否 1是
     */
    @JsonProperty("full_flow_should_log_def")
    private Integer fullFlowShouldLogDef;

    /**
     * 协议元数据保留 0否 1是
     */
    @JsonProperty("parse_proto_should_log_def")
    private Integer parseProtoShouldLogDef;

    /**
     * 协议元数据 集合
     */
    @JsonProperty("parse_proto_should_log_def_list")
    private List<Integer> parseProtoShouldLogDefList;

    /**
     * DDos数据留存 0否 1是
     */
    @JsonProperty("ddos_state")
    private Integer dDosState;
}

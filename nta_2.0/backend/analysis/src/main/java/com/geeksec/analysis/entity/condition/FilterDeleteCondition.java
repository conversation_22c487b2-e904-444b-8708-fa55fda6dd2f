package com.geeksec.analysis.entity.condition;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class FilterDeleteCondition {

    @ApiModelProperty("规则id")
    private List<Long> ids;
    @ApiModelProperty("任务id")
    @JsonProperty("task_id")
    private Integer taskId;
    @ApiModelProperty("批次ID")
    @JsonProperty("batch_id")
    private Integer batchId;
}

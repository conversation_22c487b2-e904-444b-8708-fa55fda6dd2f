package com.geeksec.analysis.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Date;

/**
 * @Author: GuanHao
 * @Date: 2022/8/18 17:26
 * @Description： <Functions List>
 */
public class ThreatInfoVo {

    /**
     * 唯一主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 威胁目标
     */
    @JsonProperty("target")
    private String target;

    /**
     * 目标类型(domaincertip)
     */
    @JsonProperty("target_type")
    private String targetType;

    /**
     * 目标标签
     */
    @JsonProperty("tag_name")
    private String tagName;

    /**
     * 来源
     */
    @JsonProperty("source")
    private String source;

    /**
     * 有效开始时间
     */
    @TableField("valid_from")
    private Date validFrom;
}

package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 离线任务批次列表
 */
@Data
public class OfflineTaskBatchPageVo {

    /**
     * 批次ID
     */
    @JsonProperty("batch_id")
    private Integer batchId;

    /**
     * 文件来源
     */
    @JsonProperty("file_path")
    private String filePath;

    /**
     * pcap包数量
     */
    @JsonProperty("pcap_num")
    private Integer pcapNum;

    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    private String startTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    private String endTime;

    /**
     * 批次状态（1-等待导入；2-正在导入；3-导入完成；4-已取消；）
     */
    @JsonProperty("batch_status")
    private Integer batchStatus;

    /**
     * 导入类型（1-服务器数据；2-数据上传；）
     */
    @JsonProperty("batch_type")
    private Integer batchType;

}

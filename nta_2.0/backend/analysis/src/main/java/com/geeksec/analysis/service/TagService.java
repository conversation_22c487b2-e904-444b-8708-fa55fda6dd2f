package com.geeksec.analysis.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.entity.condition.TagSearchCondition;
import com.geeksec.entity.common.ResultVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
public interface TagService {

    /**
     * 标签搜索（全量 or 列表形式）
     * @param condition
     * @return
     */
    ResultVo tagSearch(TagSearchCondition condition);

    /**
     * 添加标签
     * @param params
     * @return
     */
    ResultVo addTag(JSONObject params);

    /**
     * 查询标签分类
     */
    ResultVo listTagAttribute();

    /**
     * 根据查询条件，存储标签推荐数据
     * @param query
     */
    void updateTagRecommend(List<AnalysisBaseCondition.QueryOb> query);

    /**
     * 获取标签推荐数据
     * @return
     */
    ResultVo<Map<String, Object>> getRecommendTagList();


}

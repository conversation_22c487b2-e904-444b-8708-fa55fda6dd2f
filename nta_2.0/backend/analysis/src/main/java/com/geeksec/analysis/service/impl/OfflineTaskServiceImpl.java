package com.geeksec.analysis.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.geeksec.analysis.dao.FilterStateDao;
import com.geeksec.analysis.dao.OfflineTaskBatchFileDao;
import com.geeksec.analysis.dao.TaskAnalysisDao;
import com.geeksec.analysis.dao.TaskBatchDao;
import com.geeksec.analysis.entity.FilterState;
import com.geeksec.analysis.entity.OfflineTaskBatchFile;
import com.geeksec.analysis.entity.TaskAnalysis;
import com.geeksec.analysis.entity.TaskBatch;
import com.geeksec.analysis.entity.condition.OfflineTaskQueryCondition;
import com.geeksec.analysis.entity.dto.OfflineTaskDeleteDto;
import com.geeksec.analysis.entity.dto.OfflineTaskDto;
import com.geeksec.analysis.entity.vo.OfflineTaskBatchProgressVo;
import com.geeksec.analysis.entity.vo.OfflineTaskPageVo;
import com.geeksec.analysis.entity.vo.OfflineTaskVo;
import com.geeksec.analysis.entity.vo.PageVo;
import com.geeksec.analysis.service.OfflineTaskService;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.util.HttpUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 离线任务服务impl
 */
@Service
@DS("nta-db")
public class OfflineTaskServiceImpl implements OfflineTaskService {

    private static final Logger logger = LoggerFactory.getLogger(OfflineTaskServiceImpl.class);

    @Autowired
    private TaskAnalysisDao taskAnalysisDao;
    @Autowired
    private TaskBatchDao taskBatchDao;
    @Autowired
    private OfflineTaskBatchFileDao offlineTaskBatchFileDao;
    @Autowired
    private EsearchService esearchService;
    @Autowired
    private FilterStateDao filterStateDao;
    @Autowired
    private TokenService tokenService;
    @Value("${file.delete-batch-pcaps}")
    private String DELETE_BATCH_PCAPS_URL;

    @Override
    public OfflineTaskVo getLastTask() {
        Integer userId = tokenService.getUserInfoByToken();
        OfflineTaskVo offlineTaskVo = taskAnalysisDao.getLastTask(userId);
        if(offlineTaskVo!=null) {
            List<OfflineTaskBatchProgressVo> offlineTaskBatchProgressVoList = taskBatchDao.listBatchByTaskId(offlineTaskVo.getTaskId());
            offlineTaskVo.setLastImportTime(this.caclLastImportTime(offlineTaskBatchProgressVoList.stream().map(OfflineTaskBatchProgressVo::getEndTime).collect(Collectors.toList())));
            offlineTaskVo.setBatchNum(offlineTaskBatchProgressVoList.size());
            List<OfflineTaskBatchProgressVo> newList = offlineTaskBatchProgressVoList.stream()
                    .filter(vo -> vo.getBatchStatus() != null && vo.getBatchStatus() != 3 && vo.getBatchStatus() != 4)
                    .collect(Collectors.toList());
            offlineTaskVo.setBatchProgressList(newList);
        }
        return offlineTaskVo;
    }

    @Override
    public OfflineTaskVo getTask(Integer taskId) {
        OfflineTaskVo offlineTaskVo =  taskAnalysisDao.getTask(taskId);
        if(offlineTaskVo!=null){
            List<OfflineTaskBatchProgressVo> offlineTaskBatchProgressVoList = taskBatchDao.listBatchByTaskId(offlineTaskVo.getTaskId());
            offlineTaskVo.setLastImportTime(this.caclLastImportTime(offlineTaskBatchProgressVoList.stream().map(OfflineTaskBatchProgressVo::getEndTime).collect(Collectors.toList())));
            offlineTaskVo.setBatchNum(offlineTaskBatchProgressVoList.size());
            List<OfflineTaskBatchProgressVo> newList = offlineTaskBatchProgressVoList.stream()
                    .filter(vo -> vo.getBatchStatus() != null && vo.getBatchStatus() != 3 && vo.getBatchStatus() != 4)
                    .collect(Collectors.toList());
            offlineTaskVo.setBatchProgressList(newList);
        }
        return offlineTaskVo;
    }

    @Override
    public PageVo<OfflineTaskPageVo> pageTask(OfflineTaskQueryCondition condition) {
        Integer userId = tokenService.getUserInfoByToken();
        condition.setUserId(userId);
        PageVo<OfflineTaskPageVo> pageVo = new PageVo<>();
        PageHelper.startPage(condition.getCurrentPage(), condition.getPageSize());
        List<OfflineTaskPageVo> offlineTaskPageVoList = taskAnalysisDao.pageTask(condition);
        List<Integer> taskIdList = offlineTaskPageVoList.stream().map(OfflineTaskPageVo::getTaskId).collect(Collectors.toList());
        List<TaskBatch> taskBatchList = taskBatchDao.listTaskBatchItem(taskIdList);
        Map<Integer, List<TaskBatch>> taskBatchListMap =  taskBatchList.stream().collect(Collectors.groupingBy(TaskBatch::getTaskId));
        offlineTaskPageVoList.forEach(offlineTaskPageVo -> {
            Integer taskId = offlineTaskPageVo.getTaskId();
            for (Integer batchTaskId : taskBatchListMap.keySet()) {
                if (taskId.equals(batchTaskId)) {
                    List<TaskBatch> taskBatches = taskBatchListMap.get(batchTaskId);
                    offlineTaskPageVo.setTaskStatus(this.caclTaskStatus(taskBatches));
                    offlineTaskPageVo.setLastImportTime(this.caclLastImportTime(taskBatches.stream().map(TaskBatch::getEndTime).collect(Collectors.toList())));
                }
            }
        });
        PageInfo pageInfo = new PageInfo<>(offlineTaskPageVoList);
        pageVo.setTotal(pageInfo.getTotal());
        pageVo.setData(pageInfo.getList());
        return pageVo;
    }

    public String caclLastImportTime(List<Integer> endTimeList){
        int lastImportTime = -1; // 默认值为-1，表示未确定最后导入时间

        for (Integer endTime : endTimeList) {
            if (endTime != null) {
                if (lastImportTime == -1 || endTime > lastImportTime) {
                    lastImportTime = endTime;
                }
            }
        }
        if(lastImportTime==-1){
            return "";
        }
        Instant instant = Instant.ofEpochSecond(lastImportTime);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }

    public Integer caclTaskStatus(List<TaskBatch> taskBatches){
        for (TaskBatch taskBatch : taskBatches) {
            Integer taskStatus = taskBatch.getBatchStatus();
            if(taskStatus!=null){
                if (taskStatus == 1 || taskStatus == 2) {
                    return 1;
                }
            }
        }
        return 2;
    }


    @Override
    public ResultVo addTask(OfflineTaskDto dto) {
        Integer userId = tokenService.getUserInfoByToken();
        QueryWrapper<TaskAnalysis> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_name", dto.getTaskName());
        Long count = taskAnalysisDao.selectCount(queryWrapper);
        if(count>0L){
            return ResultVo.fail("任务名称已存在，请重新输入");
        }
        TaskAnalysis taskAnalysis = new TaskAnalysis();
        taskAnalysis.setUserId(userId);
        taskAnalysis.setTaskType(2);
        taskAnalysis.setTaskName(dto.getTaskName());
        taskAnalysis.setTaskRemark(dto.getTaskDescription());
        taskAnalysisDao.insert(taskAnalysis);
        taskAnalysis.setTaskId(taskAnalysis.getId());
        taskAnalysisDao.updateTaskId(taskAnalysis.getId());
        filterStateDao.createFilterState(taskAnalysis.getTaskId(), 1);
        return ResultVo.success(taskAnalysis.getTaskId());
    }

    @Override
    public ResultVo updateTask(OfflineTaskDto dto) {
        return ResultVo.success(taskAnalysisDao.updateTask(dto));
    }

    @Override
    public ResultVo deleteTask(OfflineTaskDeleteDto dto) {
        Integer count = taskBatchDao.countIncompleteTask(dto.getTaskId());
        if(count>0){
            return ResultVo.fail("该任务下有"+count+"条批次未导入完成，不能删除");
        }
        QueryWrapper<TaskAnalysis> taskWrapper = Wrappers.query();
        taskWrapper.eq("task_id", dto.getTaskId());
        taskAnalysisDao.delete(taskWrapper);

        QueryWrapper<TaskBatch> taskBatchWrapper = Wrappers.query();
        taskBatchWrapper.eq("task_id", dto.getTaskId());
        taskBatchDao.delete(taskBatchWrapper);

        QueryWrapper<OfflineTaskBatchFile> taskBatchFileWrapper = Wrappers.query();
        taskBatchFileWrapper.eq("task_id", dto.getTaskId());
        offlineTaskBatchFileDao.delete(taskBatchFileWrapper);

        QueryWrapper<FilterState> filterStateWrapper = Wrappers.query();
        filterStateWrapper.eq("task_id", dto.getTaskId());
        filterStateDao.delete(filterStateWrapper);

        JSONObject paramJson = new JSONObject();
        paramJson.put("task_id",dto.getTaskId());
        HttpUtils.sendPost(DELETE_BATCH_PCAPS_URL,paramJson.toJSONString());

        DeleteIndexRequest deleteIndexRequest = new DeleteIndexRequest("connectinfo_"+dto.getTaskId()+"*");
        AcknowledgedResponse response = esearchService.deleteDoc(deleteIndexRequest);
        if (response.isAcknowledged()) {
            return ResultVo.success("删除任务成功");
        } else {
            throw new GkException(GkErrorEnum.ES_DELETE_DOC_ERROR);
        }
    }

}

package com.geeksec.analysis.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.TagInfoDao;
import com.geeksec.analysis.dao.TaskAnalysisDao;
import com.geeksec.analysis.entity.TaskAnalysis;
import com.geeksec.analysis.entity.TbTagInfo;
import com.geeksec.analysis.entity.condition.NbLabelUpCondition;
import com.geeksec.analysis.entity.condition.NbRemarkUpCondition;
import com.geeksec.analysis.entity.storage.*;
import com.geeksec.analysis.entity.vo.CertTagVo;
import com.geeksec.analysis.service.Metadata2Service;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.EsearchService;
import com.geeksec.ngbatis.pojo.vertex.AppVertex;
import com.geeksec.ngbatis.pojo.vertex.SslFingerVertex;
import com.geeksec.ngbatis.repository.*;
import com.geeksec.ngbatis.service.CertService;
import com.geeksec.ngbatis.service.DomainService;
import com.geeksec.ngbatis.service.EdgeTypeService;
import com.geeksec.ngbatis.service.IpService;
import com.geeksec.ngbatis.vo.*;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.IpUtils;
import com.geeksec.util.RedisUtil;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Log4j2
@DS("nta-db")
public class Metadata2ServiceImpl implements Metadata2Service {

    @Autowired
    private EsearchService esearchService;

    @Autowired
    private TaskAnalysisDao taskAnalysisDao;

    @Autowired
    private TagInfoDao tagInfoDao;

    @Autowired
    private AllAtlasDao allAtlasDao;

    @Autowired
    private IpService ipService;

    @Autowired
    private CertService certService;

    @Autowired
    private SslFingerDao sslFingerDao;

    @Autowired
    private DomainService domainService;

    @Autowired
    private EdgeTypeService edgeTypeService;

    @Autowired
    private AppServiceDao appServiceDao;

    @Autowired
    private AppDao appDao;

    @Autowired
    private OrgDao orgDao;

    @Override
    public ResultVo getIpInfo(String ip) {
        IpInfoVo ipInfoVo = new IpInfoVo();
        ipInfoVo.setIp(ip);
        Map<String, Object> ipTagMap = ipService.getIpInfo(ip);
        if (MapUtil.isEmpty(ipTagMap)) {
            return ResultVo.success("IP详情正在准备中...");
        }
        delIpTag(ipInfoVo, ipTagMap);

        //开放服务：  服务--IP-->服务归属
        IpCountVo openPortIpCountVo = ipService.countAppServerByIp(ip);
        if (openPortIpCountVo != null) {
            ipInfoVo.setOpenPortNum(CommonUtil.convertLongToInt(openPortIpCountVo.getCount()));
        }
        //访问端口数：  IP--服务-->访问服务
        IpCountVo accessPortIpCountVo = ipService.countClientAppByIp(ip);
        if (accessPortIpCountVo != null) {
            ipInfoVo.setAccessPortNum(CommonUtil.convertLongToInt(accessPortIpCountVo.getCount()));
        }
        //关联锚域名
        //先查IP->关联域名
        Set<String> relatedDomainList = new HashSet<>();

        // parse_to 最终解析
        IpRelatedDomainsVo parseToRelatedDomain = ipService.listParseToRelatedDomainsByIp(ip);
        if (parseToRelatedDomain != null) {
            relatedDomainList.addAll(parseToRelatedDomain.getDomainList());
        }

        IpRelatedDomainsVo serverRelatedDomain = ipService.listServerRelatedDomainsByIp(ip);
        if (serverRelatedDomain != null) {
            relatedDomainList.addAll(serverRelatedDomain.getDomainList());
        }

        if (!CollectionUtils.isEmpty(relatedDomainList)) {
            Integer count = ipService.countFDomainNumByDomains(relatedDomainList);
            ipInfoVo.setFDomainNum(count);
        } else {
            // 不存在所属锚域名数
            ipInfoVo.setFDomainNum(0);
        }

        //关联标签IP--标签-->（只有一个边）
        List<Long> labelList = ipService.listHasLabelByIp(ip);
        List<Integer> labels = new ArrayList<>();
        for (Long lable : labelList) {
            labels.add(CommonUtil.convertLongToInt(lable));
        }
        ipInfoVo.setLabels(labels);
        String ipKey = ipInfoVo.getIpKey();
        // 内网IP情况 展示任务名称
        if (ipKey.contains("-")) {
            TaskAnalysis task = taskAnalysisDao.getTaskAnalysis(Integer.valueOf(ipKey.split("-")[0]));
            ipInfoVo.setTaskNames(Lists.newArrayList(task.getTaskName()));
        } else {
            ipInfoVo.setTaskNames(new ArrayList<>());
        }
        return ResultVo.success(ipInfoVo);
    }

    private void delIpTag(IpInfoVo ipInfoVo, Map<String, Object> ipTagMap) {
        ipInfoVo.setIpAddr(ipTagMap.get("ipAddr").toString());
        ipInfoVo.setIpKey(ipTagMap.get("ipKey").toString());
        Object city = ipTagMap.get("city");
        if (city != null) {
            ipInfoVo.setCity(city.toString());

        }
        Object country = ipTagMap.get("country");
        if (city != null) {
            ipInfoVo.setCountry(country.toString());
        }
        ipInfoVo.setAverageBps(Math.abs(Long.parseLong(ipTagMap.get("averageBps").toString())));
        ipInfoVo.setRecvBytes(Math.abs(Long.parseLong(ipTagMap.get("recvBytes").toString())));
        ipInfoVo.setSendBytes(Math.abs(Long.parseLong(ipTagMap.get("sendBytes").toString())));
        ipInfoVo.setFirstTime(Long.parseLong(ipTagMap.get("firstTime").toString()));
        ipInfoVo.setLastTime(Long.parseLong(ipTagMap.get("lastTime").toString()));
        ipInfoVo.setBlackList(Integer.parseInt(ipTagMap.get("blackList").toString()));
        ipInfoVo.setWhiteList(Integer.parseInt(ipTagMap.get("whiteList").toString()));

        Object remark = ipTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            ipInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                ipInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                ipInfoVo.setRemarks(list);
            }
        } else {
            ipInfoVo.setRemarks(new ArrayList<>());
        }
    }

    @Override
    public ResultVo<OrgInfoVo> getOrgInfo(String org) {
        OrgInfoVo orgInfoVo = new OrgInfoVo();
        OrgVo orgVo = orgDao.getOrg(org);
        BeanUtil.copyProperties(orgVo,orgInfoVo);
        if (orgInfoVo == null) {
            log.info("企业详情，nebula中无返回，org={}", org);
            throw new GkException(GkErrorEnum.GRAPHDB_QUERY_EMPTY);
        }
        Object remark = orgVo.getRemark();
        if (remark != null) {
            String s = remark.toString();
            orgInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                orgInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                orgInfoVo.setRemarks(list);
            }
        } else {
            orgInfoVo.setRemarks(new ArrayList<>());
        }
        return ResultVo.success(orgInfoVo);
    }

    @Override
    public ResultVo<SSLFingerInfoVo> getSSLFingerInfo(String str) {
        SSLFingerInfoVo sslFingerInfoVo = new SSLFingerInfoVo();

        SslFingerVertex sslFingerVertex = sslFingerDao.selectById(str);
        if (sslFingerVertex == null) {
            log.info("指纹详情，nebula中无返回，str={}", str);
            throw new GkException(GkErrorEnum.GRAPHDB_QUERY_EMPTY);
        }
        // 通过BeanUtils.copyProperties()将sslFingerVertex的属性拷贝到sslFingerInfoVo
        BeanUtil.copyProperties(sslFingerVertex, sslFingerInfoVo);

        List<List<String>> labelList = allAtlasDao.getTagLabels(str);
        List<Integer> labelIds = Lists.newArrayListWithCapacity(labelList.size());

        if (CollectionUtils.isNotEmpty(labelList)) {
            for (List<String> labels : labelList) {
                for (String label : labels) {
                    labelIds.add(Integer.parseInt(label));
                }
            }
        }

        sslFingerInfoVo.setLabels(labelIds);
        return ResultVo.success(sslFingerInfoVo);
    }

    @Override
    public ResultVo<AppServiceInfoVo> getAppService(String str) {
        AppServiceInfoVo appServiceInfoVo = new AppServiceInfoVo();
        AppServiceVo appServiceVertex = appServiceDao.getAppService(str);
        if (appServiceVertex == null) {
            log.info("应用服务详情，nebula中无返回，str={}", str);
            throw new GkException(GkErrorEnum.GRAPHDB_QUERY_EMPTY);
        }
        BeanUtil.copyProperties(appServiceVertex, appServiceInfoVo);
        appServiceInfoVo.setAppName(appServiceVertex.getAppName());
        appServiceInfoVo.setDPort(appServiceVertex.getDPort());
        appServiceInfoVo.setIpPro(appServiceVertex.getIPPro());
        return ResultVo.success(appServiceInfoVo);
    }

    @Override
    public ResultVo<AppInfoVo> getApp(String str) {
        AppInfoVo appInfoVo = new AppInfoVo();
        AppVertex appVertex = appDao.selectById(str);
        if (appVertex == null) {
            log.info("应用详情，nebula中无返回，str={}", str);
            throw new GkException(GkErrorEnum.GRAPHDB_QUERY_EMPTY);
        }
        appInfoVo.setAppName(appVertex.getAppName());
        appInfoVo.setAppVersion(appVertex.getAppVersion());
        List<List<String>> labelList = allAtlasDao.getTagLabels(str);
        List<Integer> labelIds = Lists.newArrayListWithCapacity(labelList.size());

        if (CollectionUtils.isNotEmpty(labelList)) {
            for (List<String> labels : labelList) {
                for (String label : labels) {
                    labelIds.add(Integer.parseInt(label));
                }
            }
        }

        appInfoVo.setLabels(labelIds);
        return ResultVo.success(appInfoVo);
    }

    @Override
    public ResultVo<CertInfoVo> getCertInfo(String certId) {

        CertInfoVo certInfoVo = new CertInfoVo();
        certInfoVo.setCert(certId);
        Map<String, Object> certTagMap = certService.getCertInfo(certId);
        delCertTag(certTagMap, certInfoVo);

        //关联标签证书--标签-->（只有一个边）
        List<Long> labelList = certService.listHasLabelByCert(certId);
        List<Integer> labels = new ArrayList<>();
        for (Long label : labelList) {
            labels.add(CommonUtil.convertLongToInt(label));
        }
        certInfoVo.setLabels(labels);
        //服务器热度 IP——证书——》服务端使用证书server_use_cert
        List<String> serverIps = certService.listRelatedServerIpsByCert(certId);
        certInfoVo.setServerHeat(IpUtils.ipHotCrc(serverIps));
        //客户端热度 IP——证书——》客户端使用证书server_use_cert
        List<String> clientIps = certService.listRelatedClientIpsByCert(certId);
        certInfoVo.setClientHeat(IpUtils.ipHotCrc(clientIps));
        //es字段
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(1);
        BoolQueryBuilder queryBuilder = new BoolQueryBuilder();
        queryBuilder.must(QueryBuilders.termsQuery("ASN1SHA1", certId));
        searchSourceBuilder.query(queryBuilder);
        SearchRequest searchRequest = new SearchRequest("cert_user");
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long totalHits = searchResponse.getHits().totalHits;
        if (totalHits > 0) {
            SearchHit[] hits = searchResponse.getHits().getHits();
            Map<String, Object> result = hits[0].getSourceAsMap();

            Map<String, Object> issuerMap = (Map<String, Object>) result.get("Issuer");
            if (MapUtils.isNotEmpty(issuerMap)) {
                certInfoVo.setIssuerO((String) issuerMap.get("O"));
            }

            Map<String, Object> subjectMap = (Map<String, Object>) result.get("Subject");
            if (MapUtils.isNotEmpty(subjectMap)) {
                certInfoVo.setSubjectO((String) subjectMap.get("O"));
            }

            Object o3 = result.get("NotBefore");
            if (o3 != null) {
                certInfoVo.setNotBefore(o3.toString());
            }
            Object o4 = result.get("NotAfter");
            if (o4 != null) {
                certInfoVo.setNotAfter(o4.toString());
            }
        }
        return ResultVo.success(certInfoVo);
    }

    /**
     * 证书详情分为四个部分 基础信息basic_data、证书日志log_data、签发链sign_chain、图探索atlas_data
     * 本接口只返回其前三个
     *
     * @param certId
     * @return
     */
    @Override
    public ResultVo getCertDetail(String certId) {
        Map<String, Object> resultMap = new HashMap<>();

        // 首次出现时间和末次出现时间在Nebula里面
        Map<String, Object> certTagMap = certService.getCertInfo(certId);

        // 一次性将CERT在ES中的数据查询出
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(1).from(0);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("ASN1SHA1", certId));
        searchSourceBuilder.query(boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest("cert_user").source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long totalHits = searchResponse.getHits().totalHits;
        if (totalHits == 0) {
            log.warn("查询证书详情信息失败,证书SHA1:{}不存在", certId);
            return ResultVo.success("证书详情信息正在准备中，请稍后...");
        }
        SearchHit[] hits = searchResponse.getHits().getHits();
        Map<String, Object> sourceAsMap = new HashMap<>();
        String index = "";
        // 证书信息以cert_user的为准
        for (SearchHit hit : hits) {
            String hitIndex = hit.getIndex();
            if ("cert_user".equals(hitIndex)) {
                sourceAsMap = hit.getSourceAsMap();
                index = hitIndex;
                break;
            }
            if ("cert_system".equals(hitIndex)) {
                sourceAsMap = hit.getSourceAsMap();
                index = hitIndex;
            }
        }

        // 获取标签实体的信息
        List<String> labels = (List<String>) sourceAsMap.get("Labels");
        if (CollectionUtils.isEmpty(labels) && "cert_system".equals(index)) {
            labels.add("228");
        }

        // 基础信息组装
        CertInfoVo certInfoVo = assembleCertInfoVo(certId, certTagMap, sourceAsMap, labels);
        resultMap.put("basic_data", certInfoVo);

        // 证书日志(纯ES日志)
        sourceAsMap.put("es_id", hits[0].getId());
        resultMap.put("log_data", sourceAsMap);

        // 签发链数据
        // 遍历循环查询证书签发链信息 ["父亲证书"，"爷爷证书"，"太爷爷证书"]，依次按顺序查询
        List<Map<String, Object>> signCertList = new ArrayList<>();
        List<String> fatherCertIds = new ArrayList<>();
        Map<String, Object> certChainMap = new HashMap<>();

        certChainMap.put("cert_sha1", certId);
        certChainMap.put("Labels", transCertTagInfoVoList(labels));
        signCertList.add(certChainMap);

        fatherCertIds.addAll((Collection<? extends String>) sourceAsMap.getOrDefault("FatherCertIDList", new ArrayList<>()));
        if (CollectionUtil.isNotEmpty(fatherCertIds)) {
            for (String fatherCertId : fatherCertIds) {
                Map<String, Object> signCertInfo = getSignCertInfo(fatherCertId);
                signCertList.add(signCertInfo);
            }
        }
        resultMap.put("sign_chains", signCertList);

        return ResultVo.success(resultMap);
    }

    /**
     * 通过信息组装CertInfoVo详情基础信息
     *
     * @param certId
     * @param certTagMap
     * @param sourceAsMap
     * @param labels
     * @return
     */
    private CertInfoVo assembleCertInfoVo(String certId, Map<String, Object> certTagMap, Map<String, Object> sourceAsMap, List<String> labels) {
        CertInfoVo certInfoVo = new CertInfoVo();
        certInfoVo.setCert(certId);
        certInfoVo.setLabels(labels.stream().map(Integer::parseInt).collect(Collectors.toList()));
        certInfoVo.setCertTagList(transCertTagInfoVoList(labels));
        // 获取父证书
        certInfoVo.setFatherIdList((List<String>) sourceAsMap.getOrDefault("FatherCertIDList", new ArrayList<>()));

        //服务器热度 IP——证书——》服务端使用证书server_use_cert
        List<String> serverIps = certService.listRelatedServerIpsByCert(certId);
        certInfoVo.setServerHeat(IpUtils.ipHotCrc(serverIps));
        //客户端热度 IP——证书——》客户端使用证书server_use_cert
        List<String> clientIps = certService.listRelatedClientIpsByCert(certId);
        certInfoVo.setClientHeat(IpUtils.ipHotCrc(clientIps));
        Map<String, Object> issuerMap = (Map<String, Object>) sourceAsMap.get("Issuer");
        if (MapUtils.isNotEmpty(issuerMap)) {
            // 签发机构
            certInfoVo.setIssuerO((String) issuerMap.get("CN"));
        } else {
            certInfoVo.setIssuerO("");
        }
        Map<String, Object> subjectMap = (Map<String, Object>) sourceAsMap.get("Subject");
        if (MapUtils.isNotEmpty(subjectMap)) {
            // 证书所有者
            certInfoVo.setSubjectO((String) subjectMap.get("CN"));
        } else {
            certInfoVo.setSubjectO("");
        }

        //  "NotBefore": "20161207121734",转换为2016-12-07 12:17:34
        String notBefore = (String) sourceAsMap.get("NotBefore");
        if (StringUtils.isNotEmpty(notBefore)) {
            certInfoVo.setNotBefore(notBefore.substring(0, 4) + "-" + notBefore.substring(4, 6) + "-" + notBefore.substring(6, 8) + " " + notBefore.substring(8, 10) + ":" + notBefore.substring(10, 12) + ":" + notBefore.substring(12, 14));
        } else {
            certInfoVo.setNotBefore("");
        }

        // "NotAfter": "20201207121734",转换为2020-12-07 12:17:34
        String notAfter = (String) sourceAsMap.get("NotAfter");
        if (StringUtils.isNotEmpty(notAfter)) {
            certInfoVo.setNotAfter(notAfter.substring(0, 4) + "-" + notAfter.substring(4, 6) + "-" + notAfter.substring(6, 8) + " " + notAfter.substring(8, 10) + ":" + notAfter.substring(10, 12) + ":" + notAfter.substring(12, 14));
        } else {
            certInfoVo.setNotAfter("");
        }

        // 首次&末次出现时间
        certInfoVo.setFirstTime(Integer.parseInt(certTagMap.get("firstTime").toString()));
        certInfoVo.setLastTime(Integer.parseInt(certTagMap.get("lastTime").toString()));
        Object remark = certTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            certInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                certInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                certInfoVo.setRemarks(list);
            }
        } else {
            certInfoVo.setRemarks(new ArrayList<>());
        }

        return certInfoVo;
    }

    /**
     * 获取签发链证书信息
     *
     * @param fatherCertId
     * @return
     */
    private Map<String, Object> getSignCertInfo(String fatherCertId) {
        Map<String, Object> signCertMap = new HashMap<>();

        SearchRequest searchRequest = new SearchRequest();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("ASN1SHA1", fatherCertId));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(1).from(0);

        // 证书签发链顺序，只查询系统证书
        String[] indexArray = new String[]{"cert_system","cert_user"};
        searchRequest.indices(indexArray);
        searchRequest.source(searchSourceBuilder);
        SearchResponse searchResponse = esearchService.esSearch(searchRequest);
        long totalHits = searchResponse.getHits().getTotalHits();
        if (totalHits > 0) {
            SearchHit[] hits = searchResponse.getHits().getHits();
            Map<String, Object> sourceAsMap = hits[0].getSourceAsMap();
            // 查询标签实体信息
            List<String> labels = (List<String>) sourceAsMap.get("Labels");
            signCertMap.put("cert_sha1", fatherCertId);
            if (CollectionUtils.isEmpty(labels)) {
                labels = new ArrayList<>();
                labels.add("228");
            }
            List<CertTagVo> certTagVos = transCertTagInfoVoList(labels);
            signCertMap.put("Labels", certTagVos);

        }

        return signCertMap;
    }

    /**
     * 通过获取到的标签集合转化为标签实体VO信息并赋予证书标签的等级
     *
     * @param labels
     * @return
     */
    private List<CertTagVo> transCertTagInfoVoList(List<String> labels) {
        List<CertTagVo> certTagVos = new ArrayList<>(labels.size());

        QueryWrapper<TbTagInfo> queryWrapper = new QueryWrapper<>();
        List<Integer> tagIdList = labels.stream().map(Integer::parseInt).collect(Collectors.toList());
        queryWrapper.in("tag_id", tagIdList).eq("tag_target_type", 4);
        List<TbTagInfo> tagInfoList = tagInfoDao.selectList(queryWrapper);

        for (TbTagInfo tagInfo : tagInfoList) {
            CertTagVo certTagVo = new CertTagVo();
            certTagVo.setTagId(tagInfo.getTagId());
            certTagVo.setTagText(tagInfo.getTagText());
            certTagVo.setTagRemark(tagInfo.getTagRemark());
            certTagVo.setBlackList(tagInfo.getBlackList());
            certTagVo.setWhiteList(tagInfo.getWhiteList());
            int blackList = tagInfo.getBlackList();
            int whiteList = tagInfo.getWhiteList();
            // 判断标签等级
            if (blackList >= 1 && blackList <= 100 && whiteList != 100) {
                certTagVo.setTagLevel(blackList >= 80 ? "danger" : "warning");
            } else if (whiteList >= 1 && whiteList <= 100 && blackList == 0) {
                certTagVo.setTagLevel(whiteList == 100 ? "success" : "positive");
            } else {
                certTagVo.setTagLevel("info");
            }
            certTagVos.add(certTagVo);
        }
        return certTagVos;
    }

    private void delCertTag(Map<String, Object> certTagMap, CertInfoVo certInfoVo) {
        certInfoVo.setFirstTime(Integer.parseInt(certTagMap.get("firstTime").toString()));
        certInfoVo.setLastTime(Integer.parseInt(certTagMap.get("lastTime").toString()));
        certInfoVo.setBlackList(Integer.parseInt(certTagMap.get("blackList").toString()));
        certInfoVo.setWhiteList(Integer.parseInt(certTagMap.get("whiteList").toString()));
        Object remark = certTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            certInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                certInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                certInfoVo.setRemarks(list);
            }
        } else {
            certInfoVo.setRemarks(new ArrayList<>());
        }

    }


    @Override
    public ResultVo getDomainInfo(String domain) {
        DomainInfoVo domainInfoVo = new DomainInfoVo();
        domainInfoVo.setDomain(domain);
        Map<String, Object> domainTagMap = domainService.getDomainInfo(domain);
        delDomainInfo(domainTagMap, domainInfoVo);
        //自身标签
        List<Long> labelList = domainService.listHasLabelByDomain(domain);
        List<Integer> labels = new ArrayList<>();
        for (Long lable : labelList) {
            labels.add(CommonUtil.convertLongToInt(lable));
        }
        domainInfoVo.setLabels(labels);
        //锚域名
        String fDomain = domainService.getFDomainByDomain(domain);
        if (StringUtils.isNotEmpty(fDomain)) {
            domainInfoVo.setFDomain(fDomain);
            //域名--标签-->（只有一个边）
            List<Long> fLabelList = domainService.listHasLabelByFDomain(fDomain);
            List<Integer> fLabels = new ArrayList<>();
            for (Long fLable : fLabelList) {
                fLabels.add(CommonUtil.convertLongToInt(fLable));
            }
            domainInfoVo.setFLabels(fLabels);
            //兄弟域名 锚域名 反查 域名个数  -1
            DomainCountVo domainCountVo = domainService.countBrotherNumByDomain(fDomain);
            if (domainCountVo != null && domainCountVo.getCount() != 0) {
                domainInfoVo.setBrotherNum(domainCountVo.getCount() - 1);
            }
        }
        //域名指向IP数量  域名--IP--》解析(cname_result)  最终解析（parse_to）这两个边相加
        Integer cnameResultCount = domainService.countCnameResultByDomain(domain);
        Integer parseToCount = domainService.countParseToByDomain(domain);
        domainInfoVo.setToIpNum(cnameResultCount + parseToCount);
        // 客户端热度，IP--域名-->HTTP访问和SSL访问
        DomainRelatedIpsVo domainRelatedIpsVo = domainService.listRelatedClientIpsByDomain(domain);
        if (domainRelatedIpsVo != null && domainRelatedIpsVo.getIpList().size() > 0) {
            Set<String> ips = new HashSet<>(domainRelatedIpsVo.getIpList());
            //ip请求热度的算法
            domainInfoVo.setClientHeat(IpUtils.ipHotCrc(new ArrayList<>(ips)));
        }
        //关联证书 DOMAIN - CERT --》sni_bind
        Integer certNum = domainService.countCertNumByDomain(domain);
        domainInfoVo.setCertNum(certNum);
        return ResultVo.success(domainInfoVo);
    }

    private void delDomainInfo(Map<String, Object> domainTagMap, DomainInfoVo domainInfoVo) {
        domainInfoVo.setFirstTime(Integer.parseInt(domainTagMap.get("firstTime").toString()));
        domainInfoVo.setLastTime(Integer.parseInt(domainTagMap.get("lastTime").toString()));
        domainInfoVo.setBlackList(Integer.parseInt(domainTagMap.get("blackList").toString()));
        domainInfoVo.setWhiteList(Integer.parseInt(domainTagMap.get("whiteList").toString()));
        Object remark = domainTagMap.get("remark");
        if (remark != null) {
            String s = remark.toString();
            domainInfoVo.setRemark(s);
            try {
                List<String> list = JSON.parseArray(s).toJavaList(String.class);
                domainInfoVo.setRemarks(list);
            } catch (Exception e) {
                //转换不成功就只有一个对象
                List<String> list = new ArrayList<>();
                if (!StringUtils.isEmpty(s)) {
                    list.add(s);
                }
                domainInfoVo.setRemarks(list);
            }
        } else {
            domainInfoVo.setRemarks(new ArrayList<>());
        }

        Object alexaRankObj = domainTagMap.get("alexaRank");
        if (alexaRankObj != null) {
            domainInfoVo.setAlexaRank(Integer.parseInt(alexaRankObj.toString()));
        }
        domainInfoVo.setWhoIs(domainTagMap.get("whoIs").toString());
    }

    @Override
    public ResultVo updateLabels(NbLabelUpCondition condition) {
        log.info("修改标签，condition={}", condition);

        String type = condition.getType();
        if (StringUtils.isEmpty(type) || !validType(type)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }

        String str = condition.getStr();
        List<String> labels = condition.getLabels();

        // 如果为证书的标签修改，走一遍ES的修改
        if ("CERT".equals(type)) {
            if (labels.contains("228") && labels.contains("182")) {
                log.error("证书标签中不能同时含有黑名单和白名单标签");
                throw new GkException(GkErrorEnum.CERT_BLACK_WHITE_REPEAT);
            }
            // 以ID为条件，查询出当前需要修改目标ES元数据信息
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            boolQueryBuilder.must(QueryBuilders.termQuery("ASN1SHA1", str));
            searchSourceBuilder.query(boolQueryBuilder);
            // 全量的索引中进行查找更新
            SearchRequest searchRequest = new SearchRequest(new String[]{"cert_user"}, searchSourceBuilder);
            SearchResponse searchResponse = esearchService.esSearch(searchRequest);
            long totalHits = searchResponse.getHits().totalHits;
            if (totalHits == 0) {
                log.error("修改目标标签失败！查询对应doc失败");
                throw new GkException(GkErrorEnum.CERT_TAG_MODIFY_EMPTY);
            }
            SearchHit[] hits = searchResponse.getHits().getHits();
            Map<String, Object> esDataMap = hits[0].getSourceAsMap();
            String esId = hits[0].getId();

            esDataMap.put("Labels", labels);
            try {
                // 开始执行更新操作
                UpdateRequest updateRequest = new UpdateRequest("cert_user", "_doc", esId);
                updateRequest.fetchSource(true);
                updateRequest.doc(esDataMap);
                UpdateResponse updateResponse = esearchService.updateDoc(updateRequest);
                // 在此处等待ES数据请求处理
                Thread.sleep(500);
                RestStatus status = updateResponse.status();
                if (status.getStatus() == 200) {
                    return ResultVo.success("更新目标标签成功！");
                } else {
                    throw new GkException(GkErrorEnum.CERT_TAG_MODIFY_ERROR);
                }
            } catch (Exception e) {
                log.error("更新目标标签失败！", e);
                throw new GkException(GkErrorEnum.CERT_TAG_MODIFY_ERROR);
            }
        }
        // 全量删除后，进行全量新增
        //1.删除Label边
        edgeTypeService.deleteHasLabel(str);

        if (CollectionUtil.isNotEmpty(labels)) {
            //2.重新插入
            edgeTypeService.insertHasLabel(str, labels.stream().map(Object::toString).collect(Collectors.toList()));
        }

        return ResultVo.success();
    }

    private boolean validType(String type) {
        List<String> validTypes = Arrays.asList("IP", "CERT", "DOMAIN", "ORG", "BLOCKCHAIN", "SSLFINGER","APP");
        return validTypes.contains(type);
    }

    @Override
    public ResultVo updateRemark(NbRemarkUpCondition condition) {
        log.info("修改备注，condition={}", JSON.toJSONString(condition));
        String type = condition.getType();
        if (StringUtils.isEmpty(type)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        if (!("IP".equals(type) || "CERT".equals(type) || "DOMAIN".equals(type) || "ORG".equals(type))) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        List<String> remarks = condition.getRemarks();
        String remark = "[]";
        if (remarks != null && !remarks.isEmpty()) {
            remark = JSON.toJSONString(remarks);
        }
        ipService.updateRemark(condition.getType(), condition.getStr(), remark);
        return ResultVo.success();
    }

    private void attackersToVictims(Set<String> attackers, Set<String> victims, List<Map<String, Object>> edgeListNe) {
        for (String attacker : attackers) {
            for (String victim : victims) {
                Map<String, Object> edgeMap = new HashMap<>();
                edgeMap.put("from", attacker);
                edgeMap.put("to", victim);
                //此type为 攻击
                edgeMap.put("type", "rule_attacker");
                edgeListNe.add(edgeMap);
            }
        }
    }

    private void querySession(List<String> sessionIds, Map<String, JSONObject> verMap, List<Map<String, Object>> edgeListNe, List<JSONObject> tagList) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.fetchSource(new String[]{"sIp", "dIp", "Labels"}, null);
        boolQueryBuilder.filter(QueryBuilders.termsQuery("SessionId", sessionIds));
        SearchRequest searchRequest = new SearchRequest("conn*");
        searchRequest.source(searchSourceBuilder);
        List<Map<String, Object>> list = esearchService.normalSearch(searchRequest);
        if (list == null || list.size() < 1) {
            return;
        }
        Map<String, JSONObject> sessionAttackerMap = new HashMap<>();
        Map<String, Object> sessionVerMap = new HashMap<>();
        for (Map<String, Object> map : list) {
            //要sip和dip这一个对象  在点边关系里 才行。
            String sIp = map.getOrDefault("sIp", "").toString();
            String dIp = map.getOrDefault("dIp", "").toString();
            if (!"".equals(sIp)) {
                sessionVerMap.put(sIp, null);
            }
            if (!"".equals(dIp)) {
                sessionVerMap.put(dIp, null);
            }
            String key = sIp + "_" + dIp;
            Object labels = map.get("Labels");
            JSONArray objects = JSON.parseArray(JSON.toJSONString(labels));
            if (sessionAttackerMap.containsKey(key)) {
                JSONObject oldJson = sessionAttackerMap.get(key);
                JSONArray oldLabels = oldJson.getJSONArray("labels");
                if (oldLabels != null && oldLabels.size() > 0) {
                    //这里先汇集所有标签  后续去重
                } else {
                    oldLabels = new JSONArray();
                }
                oldLabels.addAll(objects);
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("from", sIp);
                jsonObject.put("to", dIp);
                jsonObject.put("type", "rule_attacker");
                if (labels != null) {
                    jsonObject.put("labels", objects);
                }
                sessionAttackerMap.put(key, jsonObject);
            }
        }
        if (edgeListNe == null) {
            edgeListNe = new ArrayList<>();
        }
        for (String key : sessionAttackerMap.keySet()) {
            JSONObject jsonObject = sessionAttackerMap.get(key);
            JSONArray labels = jsonObject.getJSONArray("labels");
            HashSet<Object> objects = new HashSet<>(labels);
            jsonObject.put("labels", objects);
            edgeListNe.add(jsonObject);
        }
        //需要向点集合 添加session出来的点，需判断是否存在点集合里
        Map<String, Object> versMap = new HashMap<>();
        for (JSONObject jsonObject : tagList) {
            String id = jsonObject.getString("id");
            versMap.put("id", id);
        }
        for (String ip : sessionVerMap.keySet()) {
            //session的ip不在点集合
            if (!versMap.containsKey(ip)) {
                //session出来的ip  点里没有
                JSONObject sessionVer = new JSONObject();
                sessionVer.put("type", "IP");
                sessionVer.put("id", ip);
                sessionVer.put("label", ip);
                tagList.add(sessionVer);
            }
        }

    }


    private Map<String, JSONObject> delEsDataToNebula(Map<String, Object> alarmMap, List<String> sessionIds, Set<String> attackers, Set<String> victims) {
        //id去重
        Map<String, JSONObject> map = new HashMap<>();
        if (alarmMap.containsKey("attacker")) {
            JSONArray attacker = JSON.parseArray(JSON.toJSONString(alarmMap.get("attacker")));
            for (Object o : attacker) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(o));
                delJSONKey(jsonObject);
                map.put(jsonObject.getString("id"), jsonObject);
                attackers.add(jsonObject.getString("id"));
            }
        }
        if (alarmMap.containsKey("victim")) {
            JSONArray attacker = JSON.parseArray(JSON.toJSONString(alarmMap.get("victim")));
            for (Object o : attacker) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(o));
                delJSONKey(jsonObject);
                map.put(jsonObject.getString("id"), jsonObject);
                victims.add(jsonObject.getString("id"));
            }
        }
        if (alarmMap.containsKey("targets")) {
            JSONArray attacker = JSON.parseArray(JSON.toJSONString(alarmMap.get("targets")));
            for (Object o : attacker) {
                JSONObject targetJSON = JSON.parseObject(JSON.toJSONString(o));
                JSONObject jsonObject = new JSONObject();
                String name = targetJSON.get("name").toString();
                String type = targetJSON.get("type").toString();
                if ("session".equals(type)) {
                    //session类型记录
                    sessionIds.add(name);
                } else {
                    jsonObject.put("type", type.toUpperCase());
                    jsonObject.put("id", name);
                    jsonObject.put("label", name);
                    JSONArray labels = targetJSON.getJSONArray("labels");
                    if (map.containsKey(name)) {
                        JSONObject oldJson = map.get(name);
                        JSONArray oldLabels = oldJson.getJSONArray("labels");
                        if (oldLabels != null) {
                            oldLabels.addAll(labels);
                        } else {
                            oldJson.put("labels", labels);
                        }
                        map.put(name, jsonObject);
                    } else {
                        jsonObject.put("labels", labels);
                        map.put(jsonObject.getString("id"), jsonObject);
                    }
                }

            }
        }
        //set到list  并去重 标签
        for (String id : map.keySet()) {
            JSONObject jsonObject = map.get(id);
            JSONArray labels = jsonObject.getJSONArray("labels");
            if (labels != null) {
                HashSet<Object> objects = new HashSet<>(labels);
                jsonObject.put("labels", objects);
            }
        }
        return map;
    }

    //转换key为大写
    private void delJSONKey(JSONObject jsonObject) {
        jsonObject.forEach((k, v) -> {
            String s = k.toUpperCase();
            jsonObject.remove(k);
            jsonObject.put("id", v);
            jsonObject.put("label", v);
            jsonObject.put("type", s);
        });
    }

    @Override
    public ResultVo getRedisTest(String key, String value) {
        RedisUtil.setEx(key, value, 5);
        String s = RedisUtil.get(key);
        return ResultVo.success(s);
    }

}

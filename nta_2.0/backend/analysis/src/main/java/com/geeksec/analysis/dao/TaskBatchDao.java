package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.TaskAnalysis;
import com.geeksec.analysis.entity.TaskBatch;
import com.geeksec.analysis.entity.condition.OfflineTaskBatchQueryCondition;
import com.geeksec.analysis.entity.vo.OfflineTaskBatchPageVo;
import com.geeksec.analysis.entity.vo.OfflineTaskBatchProgressVo;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
public interface TaskBatchDao extends BaseMapper<TaskBatch> {

    /**
     * 通过task
     * @param taskId
     * @return
     */
    TaskBatch getTaskBatchInfoByTaskId(@Param("taskId") Integer taskId);

    /**
     * 查看当前任务DDos配置
     * @param taskId
     * @return
     */
    HashMap<String, Object> getTaskDDosState(@Param("taskId") Integer taskId);

    /**
     * 查询任务信息表
     * @param taskId
     * @return
     */
    TaskAnalysis getTaskAnalysis(@Param("taskId") Integer taskId);

    /**
     * 修改ddos的开启状态
     * @param taskId
     * @param state
     * @return
     */
    Integer updateTaskDDosState(@Param("taskId") Integer taskId, @Param("state") Integer state);

    List<TaskBatch> listTaskBatchItem(@Param("taskIdList")List<Integer> taskIdList);

    List<OfflineTaskBatchProgressVo> listBatchByTaskId(@Param("taskId")Integer taskId);

    List<OfflineTaskBatchPageVo> pageBatch(OfflineTaskBatchQueryCondition condition);

    Integer countIncompleteTask(@Param("taskId") Integer taskId);

    Integer cancelBatch(@Param("batchId") Integer batchId);

    String getServiceNameByBatchId(@Param("batchId") Integer batchId);

}

package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class FilterConfigVo implements Serializable {
    @ApiModelProperty("规则id")
    private Integer id;
    @ApiModelProperty("任务id")
    @JsonProperty("task_id")
    private Integer taskId;
    @ApiModelProperty("ip")
    private String ip;
    @ApiModelProperty("规则json")
    @JsonProperty("filter_json")
    private String filterJson;
    @JsonProperty("filter_info")
    @ApiModelProperty("规则json转对象")
    private FilterInfoVo filterInfo;

    @ApiModelProperty("创建时间")
    @JsonProperty("created_time")
    private Integer createdTime;
    @ApiModelProperty("更新时间")
    @JsonProperty("updated_time")
    private Integer updatedTime;
    @ApiModelProperty("0  端口  1 ippro 2 网段")
    private Integer type;

    private String hash;
}

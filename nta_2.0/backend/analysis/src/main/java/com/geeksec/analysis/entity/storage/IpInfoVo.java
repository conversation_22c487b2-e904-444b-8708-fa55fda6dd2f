package com.geeksec.analysis.entity.storage;

import lombok.Data;

import java.util.List;

/**
 * @author: qiuwen
 * @date: 2022/7/21
 * @Description: IP详情侧啦
 **/
@Data
public class IpInfoVo {
    // ***********   IP点  具有的属性
    private String ip;

    private String ipKey;

    private String ipAddr;

    private String remark;

    private List<String> remarks;

    /**
     * 平均流量
     */
    private long averageBps;

    /**
     * 接受字节
     */
    private long recvBytes;

    /**
     * 发送字节
     */
    private long sendBytes;

    private long firstTime;

    private long lastTime;

    private int blackList;

    private int whiteList;

    private String city;

    private String country;

    //点-点 查询的数据
    //开放端口数
    private int openPortNum = 0;
    //访问端口数
    private int accessPortNum = 0;
    //所属锚域名数
    private int fDomainNum = 0;
    //标签ids
    private List<Integer> labels;

    private List<String> taskNames;
}

package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.FeatureRule;
import com.geeksec.analysis.entity.condition.FeatureRuleSearchCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.session.SessionTagEntity;
import com.geeksec.analysis.entity.vo.TagInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-12
 */
public interface FeatureRuleDao extends BaseMapper<FeatureRule> {

    /**
     * 当前数据库最大ruleId
     * @return
     */
    Integer getMaxRuleId();

    /**
     * 不等于当前id,id可以为Null  的hash
     * @param id
     * @param hash
     * @return
     */
    FeatureRule selectOneByHash(@Param("id") Long id, @Param("hash") String hash, @Param("taskId") Integer taskId);

    /**
     * 批量删除特征规则
     * @param condition
     * @return
     */
    Integer deleteFeatureRules(FilterDeleteCondition condition);

    List<FeatureRule> getList(FeatureRuleSearchCondition condition);

    /**
     * es查询的标签id 查规则   用于会话分析列表的标签(开发人不一样 导致同样需求查询不一样)
     * @param ruleIds
     * @return
     */
    List<TagInfoVo> getListForLabel(@Param("ruleIds") List<Integer> ruleIds);

    /**
     * es查询的标签id 查规则   用于会话分析详情的标签(开发人不一样 导致同样需求查询不一样)
     * @param ruleIds
     * @return
     */
    List<SessionTagEntity> getListForLabel2(@Param("ruleIds") List<Integer> ruleIds);

    Long selectCountByUserId(@Param("ruleName") String ruleName, @Param("userId") Integer userId);

    List<FeatureRule> queryList(@Param("ruleName") String ruleName, @Param("userId") Integer userId);

}

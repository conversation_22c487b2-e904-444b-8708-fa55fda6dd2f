package com.geeksec.analysis.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.analysis.dao.ThAnalysisDao;
import com.geeksec.analysis.entity.condition.AlarmRoleJudgeCondition;
import com.geeksec.analysis.entity.dict.AnalysisTagInfoEntity;
import com.geeksec.analysis.service.AlarmJudgeService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.general.service.EsearchService;
import com.geeksec.nebula.AlarmJudgeEdgeEntity;
import com.geeksec.nebula.AlarmJudgeVertexEntity;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class AlarmJudgeServiceImpl implements AlarmJudgeService {

    private static final Logger logger = LoggerFactory.getLogger(AlarmJudgeServiceImpl.class);

    @Autowired
    private EsearchService esearchService;

    @Autowired
    private ThAnalysisDao thAnalysisDao;


    @Override
    public ResultVo createAlarmJudgeGraph(Map<String, Object> alarmMap) {

        List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
        List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();

        // 开始创建连线,创建相关节点信息
        List<String> sessionList = (List<String>) alarmMap.get("alarm_session_list");

        Object alarmKnowledgeId = alarmMap.get("alarm_knowledge_id");
        Integer alarmKnowId = 0;
        if (alarmKnowledgeId instanceof String) {
            alarmKnowId = Integer.parseInt((String) alarmKnowledgeId);
        } else {
            alarmKnowId = (Integer) alarmKnowledgeId;
        }

        Map<String, Object> resultMap = new HashMap<>();

        // 如果是规则告警的模式，则需要通过sessionID进行探针告警索引查询
        Object alarmType = alarmMap.get("alarm_type");
        if (alarmType instanceof String) {
            resultMap = handleGraphByKnowledgeType(alarmMap, alarmKnowId);
        } else {
            resultMap = handleGraphBySessionId(alarmMap);
        }

        // 对已经生成的edge进行去重
        resultMap = handleJudgeGraphResult(resultMap);

        return ResultVo.success(resultMap);
    }

    /**
     * 使用ipAddr以及对应所角色，查询出对应的告警信息，根据时间进行排序，取出最新的告警信息
     *
     * @param condition
     * @return
     */
    @Override
    public ResultVo<Map<String, Object>> createAlarmJudgeGraphByRole(AlarmRoleJudgeCondition condition) {

        Map<String, Object> resultMap = new HashMap<>();
        List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
        List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();
        List<Map<String, Object>> sessionLabels = new ArrayList<>();

        logger.info("通过角色和对应IP进行告警研判延伸查询, condition: {}", condition);

        String ipAddr = condition.getIpAddr();
        List<String> roles = condition.getRole();

        AlarmRoleJudgeCondition.TimeRange timeRange = condition.getTimeRange();
        if (timeRange == null) {
            timeRange = new AlarmRoleJudgeCondition.TimeRange();
            condition.setTimeRange(timeRange);
        }

        // 总查询条件
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        BoolQueryBuilder roleQueryBuilder = new BoolQueryBuilder();
        if (roles.contains("attacker")) {
            roleQueryBuilder.should(QueryBuilders.termsQuery("attacker.ip", ipAddr));
        }
        if (roles.contains("victim")) {
            roleQueryBuilder.should(QueryBuilders.termsQuery("victim.ip", ipAddr));
        }
        boolQueryBuilder.must(roleQueryBuilder);
        boolQueryBuilder.must(QueryBuilders.rangeQuery("time").gte(timeRange.getLeft()).lte(timeRange.getRight()));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(condition.getQueryNum());
        searchSourceBuilder.sort("time", SortOrder.DESC);
        SearchRequest searchRequest = new SearchRequest("alarm_*");
        searchRequest.source(searchSourceBuilder);
        SearchResponse response = esearchService.esSearch(searchRequest);
        SearchHit[] hits = response.getHits().getHits();
        if (hits.length == 0) {
            return ResultVo.fail("未查询到相关告警信息");
        } else {
            logger.info("查询到{}条告警信息，进行图像绘制", hits.length);
            for (SearchHit hit : hits) {
                Map<String, Object> alarmMap = hit.getSourceAsMap();
                List<String> sessionList = (List<String>) alarmMap.get("alarm_session_list");
                Object alarmKnowledgeId = alarmMap.get("alarm_knowledge_id");
                Integer alarmKnowId = 0;
                if (alarmKnowledgeId instanceof String) {
                    alarmKnowId = Integer.parseInt((String) alarmKnowledgeId);
                } else {
                    alarmKnowId = (Integer) alarmKnowledgeId;
                }
                Map<String, Object> singleGraphMap = handleGraphByKnowledgeType(alarmMap, alarmKnowId);
                List<AlarmJudgeVertexEntity> singleVertexList = (List<AlarmJudgeVertexEntity>) singleGraphMap.get("vertex");
                List<AlarmJudgeEdgeEntity> singleEdgeList = (List<AlarmJudgeEdgeEntity>) singleGraphMap.get("edge");
                List<Map<String, Object>> singleLabelList = (List<Map<String, Object>>) singleGraphMap.get("label_vertex");

                // 将查出来的每一个告警信息的图像进行合并
                vertexList.addAll(singleVertexList);
                edgeList.addAll(singleEdgeList);
                sessionLabels.addAll(singleLabelList);
            }

            resultMap.put("vertex", vertexList);
            resultMap.put("edge", edgeList);
            resultMap.put("label_vertex", sessionLabels);
            return ResultVo.success(resultMap);
        }
    }

    /**
     * 根据knowledgeId的不同进行绘图处理
     *
     * @param alarmMap
     * @param alarmKnowledgeId
     */
    private Map<String, Object> handleGraphByKnowledgeType(Map<String, Object> alarmMap, Integer alarmKnowledgeId) {

        HashMap<String, Object> resultMap = new HashMap<>();

        List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
        List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();
        List<Map<String, Object>> sessionLabels = new ArrayList<>();

        switch (alarmKnowledgeId) {
            case 100002:
                // 扫描行为
                createScanBehavior(alarmMap, vertexList, edgeList, sessionLabels);
                break;
            case 100017:
                // 尝试挖矿连接
                createMingingConn(alarmMap, vertexList, edgeList, sessionLabels);
                break;
            case 100005:
                // 远控木马
                createRemoteTrojan(alarmMap, vertexList, edgeList, sessionLabels);
                break;
            case 100006:
                // 挖矿病毒
                createMiningVirus(alarmMap, vertexList, edgeList, sessionLabels);
                break;
            case 100009:
                // 违规外联
                createIllegalExtraConn(alarmMap, vertexList, edgeList, sessionLabels);
                break;
            case 100010:
                // 隐蔽隧道
                craeteConvertTunnel(alarmMap, vertexList, edgeList, sessionLabels);
                break;
            case 120044:
                // 随机化指纹访问
                createRandomFinger(alarmMap, vertexList, edgeList, sessionLabels);
                break;
            default:
                break;
        }

        resultMap.put("vertex", vertexList);
        resultMap.put("edge", edgeList);
        resultMap.put("label_vertex", sessionLabels);

        return resultMap;
    }

    // 规则告警告警研判图生成
    private Map<String, Object> handleGraphBySessionId(Map<String, Object> alarmMap) {

        Map<String, Object> resultMap = new HashMap<>();

        List<Map<String, Object>> targets = alarmMap.get("targets") == null ? null : (List<Map<String, Object>>) alarmMap.get("targets");
        String sessionId = (String) targets.get(0).get("name");

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery("SessionId", sessionId));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(1);
        searchSourceBuilder.from(0);
        SearchRequest searchRequest = new SearchRequest("connect*");
        searchRequest.source(searchSourceBuilder);
        SearchResponse response = esearchService.esSearch(searchRequest);

        Set<String> ipSet = new HashSet<>();

        if (response.getHits().getHits().length == 0) {
            // 当前会话信息已回滚 or 未录入到ES中
            resultMap.put("vertex", new ArrayList<>());
            resultMap.put("edge", new ArrayList<>());
            resultMap.put("label_vertex", new ArrayList<>());
            return resultMap;
        } else {
            // 存在当前会话信息
            SearchHit[] hits = response.getHits().getHits();
            Map<String, Object> dataMap = hits[0].getSourceAsMap();
            List<AlarmJudgeVertexEntity> vertexList = new ArrayList<>();
            List<AlarmJudgeEdgeEntity> edgeList = new ArrayList<>();
            List<Map<String, Object>> sessionLabels = new ArrayList<>();

            String sIp = dataMap.get("sIp") == null ? null : (String) dataMap.get("sIp");
            String dIp = dataMap.get("dIp") == null ? null : (String) dataMap.get("dIp");
            ipSet.add(sIp);
            ipSet.add(dIp);
            String key = sIp + "_" + dIp;

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sIp);
            alarmConnEdge.setTo(dIp);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            // 生成会话相关标签，悬挂形式
            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sIp);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dIp);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            for (String ipAddr : ipSet) {
                AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
                ipVertex.setType("IP");
                ipVertex.setLabel(ipAddr);
                ipVertex.setVid(ipAddr);
                ipVertex.setLevel(0);
                ipVertex.setStatus(StringUtils.EMPTY);
                ipVertex.setNum(StringUtils.EMPTY);
                ipVertex.setIdentity("other");
                vertexList.add(ipVertex);
            }

            resultMap.put("vertex", vertexList);
            resultMap.put("edge", edgeList);
            resultMap.put("label_vertex", sessionLabels);
        }
        return resultMap;
    }

    /**
     * 获取攻击者 受害者的IP集合
     *
     * @param alarmMap
     * @param character
     * @return
     */
    private List<String> getIpListByCharacter(Map<String, Object> alarmMap, String character) {
        List<String> ipList = new ArrayList<>();
        List<Map<String, Object>> mapList = (List<Map<String, Object>>) alarmMap.get(character);
        for (Map<String, Object> ipMap : mapList) {
            String ipAddr = (String) ipMap.get("ip");
            ipList.add(ipAddr);
        }
        return ipList;
    }

    /**
     * 获取攻击方式（域名/指纹/证书）节点
     *
     * @param alarmMap
     * @return
     */
    private List<AlarmJudgeVertexEntity> getAlarmRouteList(Map<String, Object> alarmMap) {
        List<AlarmJudgeVertexEntity> routeVertexList = new ArrayList<>();
        List<Map<String, Object>> attackRouteList = (List<Map<String, Object>>) alarmMap.get("attack_route");
        if (CollectionUtils.isNotEmpty(attackRouteList)) {
            for (Map<String, Object> routeMap : attackRouteList) {
                String routeType = (String) routeMap.get("type");
                String routeName = (String) routeMap.get("name");
                List<String> tagList = (List<String>) routeMap.get("label");
                AlarmJudgeVertexEntity routeVertex = new AlarmJudgeVertexEntity();
                // 如果说routeType为finger，则转换为SSLFINGER
                if (StringUtils.equals(routeType, "finger")) {
                    routeType = "SSLFINGER";
                }
                routeVertex.setType(routeType.toUpperCase());
                routeVertex.setLabel(routeName);
                routeVertex.setVid(routeName);
                routeVertex.setTagList(CollectionUtils.isNotEmpty(tagList) ? tagList : new ArrayList<>());
                routeVertex.setLevel(0);
                routeVertex.setIdentity("route");
                routeVertex.setStatus(StringUtils.EMPTY);
                routeVertex.setNum(StringUtils.EMPTY);
                routeVertexList.add(routeVertex);
            }
        }
        return routeVertexList;
    }

    private SearchHit[] getAlarmConnectionDataList(Map<String, Object> alarmMap) {

        // 获取告警知识库ID
        Object alarmKnowledgeId = alarmMap.get("alarm_knowledge_id");
        Integer alarmKnowId = 0;
        if (alarmKnowledgeId instanceof String) {
            alarmKnowId = Integer.parseInt((String) alarmKnowledgeId);
        } else {
            alarmKnowId = (Integer) alarmKnowledgeId;
        }

        // 获取session列表
        List<String> sessionList = (List<String>) alarmMap.get("alarm_session_list");

        // 1.首先查询会话信息中是否含有相关信息
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if (alarmKnowId == 120044) {
            // 如果告警为随机化指纹访问，则查询某一条的会话信息
            boolQueryBuilder.must(QueryBuilders.termsQuery("SessionId", sessionList.get(0)));
        } else {
            boolQueryBuilder.must(QueryBuilders.termsQuery("SessionId", sessionList));
        }

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);

        // 如果告警为随机化指纹访问，则查询某一条的会话信息
        if (alarmKnowId == 120044) {
            searchSourceBuilder.size(1);
        } else {
            searchSourceBuilder.size(sessionList.size());
        }

        searchSourceBuilder.from(0);
        SearchRequest searchRequest = new SearchRequest("connect*");
        searchRequest.source(searchSourceBuilder);
        SearchResponse response = esearchService.esSearch(searchRequest);

        if (response.getHits().getHits().length == 0) {
            // 2.如果会话信息中没有相关信息，则查询SSL信息中是否含有相关信息
            searchRequest = new SearchRequest("ssl*");
            searchRequest.source(searchSourceBuilder);
            response = esearchService.esSearch(searchRequest);
        }

        return response.getHits().getHits();

    }

    private SearchHit[] getAlarmSSLDataList(Map<String, Object> alarmMap) {
        List<String> sessionList = (List<String>) alarmMap.get("alarm_session_list");

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termsQuery("SessionId", sessionList));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(sessionList.size());
        searchSourceBuilder.from(0);
        SearchRequest searchRequest = new SearchRequest("ssl*");
        searchRequest.source(searchSourceBuilder);
        SearchResponse response = esearchService.esSearch(searchRequest);
        return response.getHits().getHits();
    }

    /**
     * 扫描行为告警研判图
     *
     * @param alarmMap
     * @param vertexList
     * @param edgeList
     * @param sessionLabels
     */
    private void createScanBehavior(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {

        try {
            List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
            List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
            List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
            // 使用路径.方式生成点
            vertexList.addAll(routeVertexList);

            // 已存的IP列表集合
            Set<String> ipSet = new HashSet<>();


            SearchHit[] hits = getAlarmConnectionDataList(alarmMap);
            for (SearchHit hit : hits) {
                Map<String, Object> dataMap = hit.getSourceAsMap();
                String sip = (String) dataMap.get("sIp");
                String dip = (String) dataMap.get("dIp");
                String key = sip + "_" + dip;
                ipSet.add(sip);
                ipSet.add(dip);

                AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
                alarmConnEdge.setFrom(sip);
                alarmConnEdge.setTo(dip);
                alarmConnEdge.setLabel((String) dataMap.get("AppName"));
                alarmConnEdge.setRankId(key);
                edgeList.add(alarmConnEdge);

                // 生成会话相关标签，悬挂形式
                List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
                List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
                Map<String, Object> labelMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(labelList)) {
                    labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
                }
                labelMap.put("id", key + "_label");
                labelMap.put("type", "LABELS");
                labelMap.put("labels", labelsInfo);
                sessionLabels.add(labelMap);

                AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
                sipConnLabelEdge.setFrom(sip);
                sipConnLabelEdge.setTo(key + "_label");
                sipConnLabelEdge.setLabel(StringUtils.EMPTY);
                sipConnLabelEdge.setRankId(key);
                edgeList.add(sipConnLabelEdge);

                AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
                connDipLabelEdge.setFrom(key + "_label");
                connDipLabelEdge.setTo(dip);
                connDipLabelEdge.setLabel(StringUtils.EMPTY);
                connDipLabelEdge.setRankId(key);
                edgeList.add(connDipLabelEdge);

                // 若为渗透工具,生成判断指纹关联边
                if (CollectionUtils.isNotEmpty(routeVertexList)) {
                    for (AlarmJudgeVertexEntity sslFingerVertex : routeVertexList) {
                        AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                        sIpFingerEdge.setFrom(sip);
                        sIpFingerEdge.setTo(sslFingerVertex.getVid());
                        sIpFingerEdge.setLabel(StringUtils.EMPTY);
                        sIpFingerEdge.setRankId(StringUtils.EMPTY);
                        edgeList.add(sIpFingerEdge);

                        AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                        fingerDipEdge.setFrom(sslFingerVertex.getVid());
                        fingerDipEdge.setTo(dip);
                        fingerDipEdge.setLabel(StringUtils.EMPTY);
                        fingerDipEdge.setRankId(StringUtils.EMPTY);
                        edgeList.add(fingerDipEdge);
                    }
                }
            }

            for (String ipAddr : ipSet) {
                AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
                ipVertex.setType("IP");
                ipVertex.setLabel(ipAddr);
                ipVertex.setVid(ipAddr);
                ipVertex.setLevel(0);
                ipVertex.setStatus(StringUtils.EMPTY);
                ipVertex.setNum(StringUtils.EMPTY);
                if (attackerIpList.contains(ipAddr)) {
                    ipVertex.setIdentity("attacker");
                } else if (victimIpList.contains(ipAddr)) {
                    ipVertex.setIdentity("victim");
                } else {
                    ipVertex.setIdentity("other");
                }
                vertexList.add(ipVertex);
            }
        } catch (Exception e) {
            logger.error("绘制扫描行为告警研判关联图失败,error->", e);
            return;
        }
    }

    /**
     * 尝试挖矿连接研判图绘制
     *
     * @param alarmMap
     * @param vertexList
     * @param edgeList
     * @param sessionLabels
     */
    private void createMingingConn(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        // 使用路径.方式生成点
        vertexList.addAll(routeVertexList);
        // 已存的IP列表集合
        Set<String> ipSet = new HashSet<>();

        SearchHit[] hits = getAlarmConnectionDataList(alarmMap);
        for (SearchHit hit : hits) {
            // 正常情况只会存在一个会话信息
            Map<String, Object> dataMap = hit.getSourceAsMap();
            String sip = (String) dataMap.get("sIp");
            // 此处的dip为DNS服务器地址
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            HashMap<String, Object> domainIpMap = ((List<HashMap<String, Object>>) dataMap.get("DNS")).get(0);
            List<String> domainIpList = (List<String>) domainIpMap.get("DomainIp");

            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity domainVertex : routeVertexList) {
                    // 绘制SIP与矿池域名 & 矿池域名与DNS解析服务器的连线
                    AlarmJudgeEdgeEntity sIpDomainEdge = new AlarmJudgeEdgeEntity();
                    sIpDomainEdge.setFrom(sip);
                    sIpDomainEdge.setTo(domainVertex.getVid());
                    sIpDomainEdge.setLabel(StringUtils.EMPTY);
                    sIpDomainEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpDomainEdge);

                    AlarmJudgeEdgeEntity domainDipEdge = new AlarmJudgeEdgeEntity();
                    domainDipEdge.setFrom(domainVertex.getVid());
                    domainDipEdge.setTo(dip);
                    domainDipEdge.setLabel(StringUtils.EMPTY);
                    domainDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(domainDipEdge);

                    AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
                    alarmConnEdge.setFrom(sip);
                    alarmConnEdge.setTo(dip);
                    alarmConnEdge.setLabel((String) dataMap.get("AppName"));
                    alarmConnEdge.setRankId(key);
                    edgeList.add(alarmConnEdge);

                    // 生成会话相关标签，悬挂形式
                    List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
                    List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
                    Map<String, Object> labelMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(labelList)) {
                        labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
                    }
                    labelMap.put("id", key + "_label");
                    labelMap.put("type", "LABELS");
                    labelMap.put("labels", labelsInfo);
                    sessionLabels.add(labelMap);

                    AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
                    sipConnLabelEdge.setFrom(sip);
                    sipConnLabelEdge.setTo(key + "_label");
                    sipConnLabelEdge.setLabel(StringUtils.EMPTY);
                    sipConnLabelEdge.setRankId(key);
                    edgeList.add(sipConnLabelEdge);

                    AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
                    connDipLabelEdge.setFrom(key + "_label");
                    connDipLabelEdge.setTo(dip);
                    connDipLabelEdge.setLabel(StringUtils.EMPTY);
                    connDipLabelEdge.setRankId(key);
                    edgeList.add(connDipLabelEdge);

                    // 绘制DNS解析服务器与域名最终解析IP的连线
                    for (String domainIp : domainIpList) {
                        AlarmJudgeEdgeEntity dnsIpDomainIpEdge = new AlarmJudgeEdgeEntity();
                        dnsIpDomainIpEdge.setFrom(dip);
                        dnsIpDomainIpEdge.setTo(domainIp);
                        dnsIpDomainIpEdge.setLabel(StringUtils.EMPTY);
                        dnsIpDomainIpEdge.setRankId(StringUtils.EMPTY);
                        edgeList.add(dnsIpDomainIpEdge);
                        ipSet.add(domainIp);
                    }
                }
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }

    }

    /**
     * 远程木马类型告警研判图绘制
     *
     * @param alarmMap
     * @param vertexList
     * @param edgeList
     * @param sessionLabels
     */
    private void createRemoteTrojan(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        // 使用路径.方式生成点
        vertexList.addAll(routeVertexList);
        // 已存的IP列表集合
        Set<String> ipSet = new HashSet<>();

        SearchHit[] hits = getAlarmConnectionDataList(alarmMap);
        for (SearchHit hit : hits) {
            Map<String, Object> dataMap = hit.getSourceAsMap();
            String sip = (String) dataMap.get("sIp");
            // 此处的dip为DNS服务器地址
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sip);
            alarmConnEdge.setTo(dip);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            // 生成会话相关标签，悬挂形式
            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sip);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dip);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity sslFingerVertex : routeVertexList) {
                    AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                    sIpFingerEdge.setFrom(sip);
                    sIpFingerEdge.setTo(sslFingerVertex.getVid());
                    sIpFingerEdge.setLabel(StringUtils.EMPTY);
                    sIpFingerEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpFingerEdge);

                    AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                    fingerDipEdge.setFrom(sslFingerVertex.getVid());
                    fingerDipEdge.setTo(dip);
                    fingerDipEdge.setLabel(StringUtils.EMPTY);
                    fingerDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(fingerDipEdge);
                }
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    /**
     * 违规外联类型告警研判图绘制
     *
     * @param alarmMap
     * @param vertexList
     * @param edgeList
     * @param sessionLabels
     */
    private void createIllegalExtraConn(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        // 使用路径.方式生成点
        vertexList.addAll(routeVertexList);
        // 已存的IP列表集合
        Set<String> ipSet = new HashSet<>();

        SearchHit[] hits = getAlarmConnectionDataList(alarmMap);
        for (SearchHit hit : hits) {
            Map<String, Object> dataMap = hit.getSourceAsMap();
            String sip = (String) dataMap.get("sIp");
            // 此处的dip为DNS服务器地址
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sip);
            alarmConnEdge.setTo(dip);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            // 生成会话相关标签，悬挂形式
            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sip);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dip);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity sslFingerVertex : routeVertexList) {
                    AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                    sIpFingerEdge.setFrom(sip);
                    sIpFingerEdge.setTo(sslFingerVertex.getVid());
                    sIpFingerEdge.setLabel(StringUtils.EMPTY);
                    sIpFingerEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpFingerEdge);

                    AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                    fingerDipEdge.setFrom(sslFingerVertex.getVid());
                    fingerDipEdge.setTo(dip);
                    fingerDipEdge.setLabel(StringUtils.EMPTY);
                    fingerDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(fingerDipEdge);
                }
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    /**
     * 隐蔽隧道类型告警研判图绘制
     *
     * @param alarmMap
     * @param vertexList
     * @param edgeList
     * @param sessionLabels
     */
    private void craeteConvertTunnel(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        // 使用路径.方式生成点
        vertexList.addAll(routeVertexList);
        // 已存的IP列表集合
        Set<String> ipSet = new HashSet<>();
        Set<String> keySet = new HashSet<>();

        SearchHit[] hits = getAlarmConnectionDataList(alarmMap);
        for (SearchHit hit : hits) {
            Map<String, Object> dataMap = hit.getSourceAsMap();
            String sip = (String) dataMap.get("sIp");
            // 此处的dip为DNS服务器地址
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            if (!keySet.contains(key)) {
                AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
                alarmConnEdge.setFrom(sip);
                alarmConnEdge.setTo(dip);
                alarmConnEdge.setLabel((String) dataMap.get("AppName"));
                alarmConnEdge.setRankId(key);
                edgeList.add(alarmConnEdge);
                keySet.add(key);

                // 生成会话相关标签，悬挂形式
                List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
                List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
                Map<String, Object> labelMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(labelList)) {
                    labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
                }
                labelMap.put("id", key + "_label");
                labelMap.put("type", "LABELS");
                labelMap.put("labels", labelsInfo);
                sessionLabels.add(labelMap);

                AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
                sipConnLabelEdge.setFrom(sip);
                sipConnLabelEdge.setTo(key + "_label");
                sipConnLabelEdge.setLabel(StringUtils.EMPTY);
                sipConnLabelEdge.setRankId(key);
                edgeList.add(sipConnLabelEdge);

                AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
                connDipLabelEdge.setFrom(key + "_label");
                connDipLabelEdge.setTo(dip);
                connDipLabelEdge.setLabel(StringUtils.EMPTY);
                connDipLabelEdge.setRankId(key);
                edgeList.add(connDipLabelEdge);
            }

        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    /**
     * 挖矿病毒类型告警研判图绘制
     *
     * @param alarmMap
     * @param vertexList
     * @param edgeList
     * @param sessionLabels
     */
    private void createMiningVirus(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        // 使用路径.方式生成点
        vertexList.addAll(routeVertexList);
        // 已存的IP列表集合
        Set<String> ipSet = new HashSet<>();
        Set<String> keySet = new HashSet<>();

        SearchHit[] hits = getAlarmConnectionDataList(alarmMap);
        for (SearchHit hit : hits) {
            Map<String, Object> dataMap = hit.getSourceAsMap();
            String sip = (String) dataMap.get("sIp");
            // 此处的dip为DNS服务器地址
            String dip = (String) dataMap.get("dIp");
            String key = sip + "_" + dip;
            ipSet.add(sip);
            ipSet.add(dip);

            AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
            alarmConnEdge.setFrom(sip);
            alarmConnEdge.setTo(dip);
            alarmConnEdge.setLabel((String) dataMap.get("AppName"));
            alarmConnEdge.setRankId(key);
            edgeList.add(alarmConnEdge);

            // 生成会话相关标签，悬挂形式
            List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
            List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
            Map<String, Object> labelMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(labelList)) {
                labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
            }
            labelMap.put("id", key + "_label");
            labelMap.put("type", "LABELS");
            labelMap.put("labels", labelsInfo);
            sessionLabels.add(labelMap);

            AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
            sipConnLabelEdge.setFrom(sip);
            sipConnLabelEdge.setTo(key + "_label");
            sipConnLabelEdge.setLabel(StringUtils.EMPTY);
            sipConnLabelEdge.setRankId(key);
            edgeList.add(sipConnLabelEdge);

            AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
            connDipLabelEdge.setFrom(key + "_label");
            connDipLabelEdge.setTo(dip);
            connDipLabelEdge.setLabel(StringUtils.EMPTY);
            connDipLabelEdge.setRankId(key);
            edgeList.add(connDipLabelEdge);

            // 对于挖矿病毒来说，存在指纹、域名、也可能同时存在
            if (CollectionUtils.isNotEmpty(routeVertexList)) {
                for (AlarmJudgeVertexEntity routeVertex : routeVertexList) {
                    AlarmJudgeEdgeEntity sIpRouteEdge = new AlarmJudgeEdgeEntity();
                    sIpRouteEdge.setFrom(sip);
                    sIpRouteEdge.setTo(routeVertex.getVid());
                    sIpRouteEdge.setLabel(StringUtils.EMPTY);
                    sIpRouteEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(sIpRouteEdge);

                    AlarmJudgeEdgeEntity routeDipEdge = new AlarmJudgeEdgeEntity();
                    routeDipEdge.setFrom(routeVertex.getVid());
                    routeDipEdge.setTo(dip);
                    routeDipEdge.setLabel(StringUtils.EMPTY);
                    routeDipEdge.setRankId(StringUtils.EMPTY);
                    edgeList.add(routeDipEdge);
                }
            }
        }
        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    /**
     * 指纹随机化访问类型告警研判图绘制
     *
     * @param alarmMap
     * @param vertexList
     * @param edgeList
     * @param sessionLabels
     */
    private void createRandomFinger(Map<String, Object> alarmMap, List<AlarmJudgeVertexEntity> vertexList, List<AlarmJudgeEdgeEntity> edgeList, List<Map<String, Object>> sessionLabels) {
        List<String> attackerIpList = getIpListByCharacter(alarmMap, "attacker");
        List<String> victimIpList = getIpListByCharacter(alarmMap, "victim");
        List<AlarmJudgeVertexEntity> routeVertexList = getAlarmRouteList(alarmMap);
        // 使用路径.方式生成点
        vertexList.addAll(routeVertexList);
        // 已存的IP列表集合
        Set<String> ipSet = new HashSet<>();
        Set<String> keySet = new HashSet<>();

        SearchHit[] hits = getAlarmConnectionDataList(alarmMap);
        SearchHit singleHit = hits[0];
        Map<String, Object> dataMap = singleHit.getSourceAsMap();
        String sip = (String) dataMap.get("sIp");
        // 此处的dip为DNS服务器地址
        String dip = (String) dataMap.get("dIp");
        String key = sip + "_" + dip;
        ipSet.add(sip);
        ipSet.add(dip);

        AlarmJudgeEdgeEntity alarmConnEdge = new AlarmJudgeEdgeEntity();
        alarmConnEdge.setFrom(sip);
        alarmConnEdge.setTo(dip);
        alarmConnEdge.setLabel((String) dataMap.get("AppName"));
        alarmConnEdge.setRankId(key);
        edgeList.add(alarmConnEdge);

        // 生成会话相关标签，悬挂形式
        List<Integer> labelList = (List<Integer>) dataMap.getOrDefault("Labels", new ArrayList<>());
        List<AnalysisTagInfoEntity> labelsInfo = new ArrayList<>();
        Map<String, Object> labelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(labelList)) {
            labelsInfo = thAnalysisDao.getTagInfoByIds(labelList);
        }
        labelMap.put("id", key + "_label");
        labelMap.put("type", "LABELS");
        labelMap.put("labels", labelsInfo);
        sessionLabels.add(labelMap);

        AlarmJudgeEdgeEntity sipConnLabelEdge = new AlarmJudgeEdgeEntity();
        sipConnLabelEdge.setFrom(sip);
        sipConnLabelEdge.setTo(key + "_label");
        sipConnLabelEdge.setLabel(StringUtils.EMPTY);
        sipConnLabelEdge.setRankId(key);
        edgeList.add(sipConnLabelEdge);

        AlarmJudgeEdgeEntity connDipLabelEdge = new AlarmJudgeEdgeEntity();
        connDipLabelEdge.setFrom(key + "_label");
        connDipLabelEdge.setTo(dip);
        connDipLabelEdge.setLabel(StringUtils.EMPTY);
        connDipLabelEdge.setRankId(key);
        edgeList.add(connDipLabelEdge);

        // 对于指纹随机化访问服务端类告警，同时存在多个指纹，进行关联
        if (CollectionUtils.isNotEmpty(routeVertexList)) {
            for (AlarmJudgeVertexEntity sllFingerVertex : routeVertexList) {
                AlarmJudgeEdgeEntity sIpFingerEdge = new AlarmJudgeEdgeEntity();
                sIpFingerEdge.setFrom(sip);
                sIpFingerEdge.setTo(sllFingerVertex.getVid());
                sIpFingerEdge.setLabel(StringUtils.EMPTY);
                sIpFingerEdge.setRankId(StringUtils.EMPTY);
                edgeList.add(sIpFingerEdge);

                AlarmJudgeEdgeEntity fingerDipEdge = new AlarmJudgeEdgeEntity();
                fingerDipEdge.setFrom(sllFingerVertex.getVid());
                fingerDipEdge.setTo(dip);
                fingerDipEdge.setLabel(StringUtils.EMPTY);
                fingerDipEdge.setRankId(StringUtils.EMPTY);
                edgeList.add(fingerDipEdge);
            }
        }

        for (String ipAddr : ipSet) {
            AlarmJudgeVertexEntity ipVertex = new AlarmJudgeVertexEntity();
            ipVertex.setType("IP");
            ipVertex.setLabel(ipAddr);
            ipVertex.setVid(ipAddr);
            ipVertex.setLevel(0);
            ipVertex.setStatus(StringUtils.EMPTY);
            ipVertex.setNum(StringUtils.EMPTY);
            if (attackerIpList.contains(ipAddr)) {
                ipVertex.setIdentity("attacker");
            } else if (victimIpList.contains(ipAddr)) {
                ipVertex.setIdentity("victim");
            } else {
                ipVertex.setIdentity("other");
            }
            vertexList.add(ipVertex);
        }
    }

    /**
     * 对结果map里的edgeList和sessionLabel进行去重
     *
     * @param resuleMap
     * @return
     */
    private Map<String, Object> handleJudgeGraphResult(Map<String, Object> resuleMap) {
        // 处理重复的边
        List<AlarmJudgeEdgeEntity> edgeList = (List<AlarmJudgeEdgeEntity>) resuleMap.get("edge");
        List<AlarmJudgeEdgeEntity> newEdgeList = new ArrayList<>();
        Set<String> edgeSet = new HashSet<>();
        for (AlarmJudgeEdgeEntity edge : edgeList) {
            String edgeStr = edge.getFrom() + "_" + edge.getTo();
            if (!edgeSet.contains(edgeStr)) {
                newEdgeList.add(edge);
                edgeSet.add(edgeStr);
            }
        }
        resuleMap.put("edge", newEdgeList);

        // 处理重复的sessionLabel
        List<Map<String, Object>> sessionLabelList = (List<Map<String, Object>>) resuleMap.get("label_vertex");
        List<Map<String, Object>> newSessionLabelList = new ArrayList<>();
        Set<String> sessionLabelSet = new HashSet<>();
        for (Map<String, Object> sessionLabel : sessionLabelList) {
            String sessionLabelStr = (String) sessionLabel.get("id");
            if (!sessionLabelSet.contains(sessionLabelStr)) {
                newSessionLabelList.add(sessionLabel);
                sessionLabelSet.add(sessionLabelStr);
            }
        }
        resuleMap.put("label_vertex", newSessionLabelList);
        return resuleMap;
    }
}



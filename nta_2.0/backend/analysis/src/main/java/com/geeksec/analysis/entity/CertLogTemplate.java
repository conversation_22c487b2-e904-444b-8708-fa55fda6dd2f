package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: jerryzhou
 * @date: 2024/1/29 18:12
 * @Description:
 **/
@TableName(value ="tb_cert_log_template")
@Data
public class CertLogTemplate implements Serializable {
    /**
     * 使用模板用户ID
     */
    @TableId
    private Integer userId;

    /**
     * 模板JSON文件内容
     */

    private String templateJson;

    /**
     * 记录创建时间
     */
    private Date createdTime;

    /**
     * 最后修改时间
     */
    private Date updatedTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

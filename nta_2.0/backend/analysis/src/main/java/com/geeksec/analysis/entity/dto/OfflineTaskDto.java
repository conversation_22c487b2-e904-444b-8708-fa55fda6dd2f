package com.geeksec.analysis.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class OfflineTaskDto {

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 任务名
     */
    @JsonProperty("task_name")
    private String taskName;

    /**
     * 任务描述
     */
    @JsonProperty("task_description")
    private String taskDescription;

}

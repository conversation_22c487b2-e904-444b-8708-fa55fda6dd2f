package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.FilterConfig;
import com.geeksec.analysis.entity.condition.FilterConfigInCondition;
import com.geeksec.analysis.entity.condition.FilterDeleteCondition;
import com.geeksec.analysis.entity.condition.FilterRuleCondition;
import com.geeksec.analysis.entity.vo.FilterConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
public interface FilterConfigDao extends BaseMapper<FilterConfig> {

    Integer getCount(Integer taskId);

    List<FilterConfigVo> getList(FilterRuleCondition filterRuleCondition);

    /**
     * 查询是否有重复的hash的sql
     * @param filterConfig
     * @return
     */
    FilterConfig getOneByHash(FilterConfigInCondition filterConfig);

    Integer deleteBySome(FilterDeleteCondition condition);

    Integer addByList(@Param("list") List<FilterConfig> list);

}

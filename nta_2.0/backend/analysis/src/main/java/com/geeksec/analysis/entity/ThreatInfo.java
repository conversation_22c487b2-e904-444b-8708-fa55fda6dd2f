package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_threat_info")
public class ThreatInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 威胁目标
     */
    @TableField("target")
    private String target;

    /**
     * 目标类型(domaincertip)
     */
    @TableField("target_type")
    private String targetType;

    /**
     * 目标标签
     */
    @TableField("tag_name")
    private String tagName;

    /**
     * 来源
     */
    @TableField("source")
    private String source;

    /**
     * 版本
     */
    @TableField("version")
    private String version;

    /**
     * hash
     */
    @TableField("shash")
    private String shash;

    /**
     * 入库时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 有效开始时间
     */
    @TableField("valid_from")
    private Date validFrom;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


}

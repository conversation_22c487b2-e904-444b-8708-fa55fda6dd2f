package com.geeksec.analysis.entity.storage;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class DomainInfoVo {

    private String domain;

    /** 锚域名 */
    private String fDomain;

    /** 备注 */
    private String remark;

    private List<String> remarks;

    private Integer blackList;

    private Integer whiteList;

    private long firstTime;

    private long lastTime;

    private int alexaRank;

    /** 标签ids */
    private List<Integer> labels;

    @JsonProperty("fLabels")
    private List<Integer> fLabels;

    /** 兄弟域名数量 */
    private int brotherNum;

    /** 指向IP数量 */
    private int toIpNum;

    /** 客户端热度 */
    private Integer clientHeat;

    private List<String> taskNames;

    /** 关联证书数量 */
    private int certNum;

    private String whoIs;

}

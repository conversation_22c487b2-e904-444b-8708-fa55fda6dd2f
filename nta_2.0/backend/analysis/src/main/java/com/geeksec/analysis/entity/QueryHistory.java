package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 查询历史记录表
 * @TableName tb_query_history
 */
@TableName(value ="tb_query_history")
@Data
public class QueryHistory implements Serializable {
    /**
     * 唯一识别ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 查询用户名称
     */
    @TableField(value = "user_name")
    @JsonProperty("user_name")
    private String userName;

    /**
     * 查询类型 存放的是来源的数组 [0,1,2] 0 用户导入 1推送 2扫描
     */
    @TableField(value = "source")
    private String source;

    /**
     * 查询条件
     */
    @TableField(value = "condition_text")
    @JsonProperty("condition_text")
    private String conditionText;

    /**
     * 查询条件（前端用于回写）
     */
    @TableField(value = "query_cn")
    @JsonProperty("query_cn")
    private String queryCn;

    /**
     * 命中记录数量
     */
    @TableField(value = "hit_count")
    @JsonProperty("hit_count")
    private Long hitCount;

    /**
     * 查询耗时（毫秒）
     */
    @TableField(value = "cost_time")
    @JsonProperty("cost_time")
    private Long costTime;

    /**
     * 前后查询次数
     */
    @TableField(value = "query_count")
    @JsonProperty("query_count")
    private Long queryCount;

    /**
     * 查询时间
     */
    @TableField(value = "query_time")
    @JsonProperty("query_time")
    private Date queryTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}

package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_filter_config")
public class FilterConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID ---fifter 筛选配置---
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Integer taskId;

    /**
     * ip
     */
    @TableField("ip")
    private String ip;

    /**
     * json
     */
    @TableField("filter_json")
    private String filterJson;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Integer createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private Integer updatedTime;

    /**
     * filter_json的hash，用于校验重复
     */
    @TableField("hash")
    private String hash;

    /**
     * 0  端口  1 ippro
     */
    @TableField("type")
    private Integer type;


}

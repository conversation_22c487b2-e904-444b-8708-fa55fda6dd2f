package com.geeksec.analysis.entity.storage;

import lombok.Data;

import java.util.Set;

@Data
public class CertListVo {

    private String cert;

    private long count;

    private Long firstTime;

    private Long lastTime;

    /** 关联告警个数 */
    private long alarmCount;

    /** 父证书ID */
    private String fatherCert;

    /** 叶子证书个数 */
    private long sonCertNum;

    /** 签发机构  ES:Issuer.O */
    private String issuerO;

    /** 所有者机构  ES:Subject.O*/
    private String subjectO;

    /** 颁发时间  ES：有效期开始时间 NotBefore */
    private String notBefore;

    /** 有效时间  ES：有效期结束时间 NotAfter */
    private String notAfter;

    private Set<Integer> labels;

    /** 客户端热度（默认为0） */
    private Integer clientHeat = 0;

    /** 关联域名个数 */
    private int domainNum;

    /** 关联服务器ip个数 */
    private int serverIpNum;

    /** 关联SSL个数 */
    private int sslIpNum;

}

package com.geeksec.analysis.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.geeksec.analysis.entity.DownloadTask;
import com.geeksec.analysis.entity.condition.DownloadListSearchCondition;
import com.geeksec.analysis.entity.vo.DownloadPcapAdminVo;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
public interface DownloadTaskDao extends BaseMapper<DownloadTask> {

    /**
     * 数据是否准备完成
     *
     * @param taskId 下载任务的ID
     * @return 返回结果 > 0 表示数据准备完成
     */
    String getPcapPath(Integer taskId);

    /**
     *
     * @return
     */
    List<DownloadPcapAdminVo> listdownloadPcapList(DownloadListSearchCondition condition);
}

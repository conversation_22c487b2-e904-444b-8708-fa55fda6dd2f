package com.geeksec.analysis.condition;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON>ao
 * @Date: 2022/5/19 10:48
 * @Description： 通用的任务注册condition
 */
@Data
public class TaskRegisterCondition {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("user_id")
    private Integer userId;

    /**
     * 当前查询条件
     */
    @JsonProperty("condition")
    private JSONObject condition;

    /**
     * 下载任务类型 (1.会话列表下载)
     */
    @JsonProperty("task_type")
    private Integer taskType;

    /**
     * 当前页条件展示数量
     */
    @JsonProperty("limit")
    private int limit = 10;

    /**
     * 当前页码
     */
    @JsonProperty("page")
    private int page = 1;

    /**
     * 查询下载列表时传入
     */
    @JsonProperty("search_list")
    private List<Integer> searchList;
}

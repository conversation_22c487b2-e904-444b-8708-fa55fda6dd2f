package com.geeksec.analysis.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.BatchPluginDao;
import com.geeksec.analysis.dao.NetworkFlowDao;
import com.geeksec.analysis.dao.TaskAnalysisDao;
import com.geeksec.analysis.dao.TaskBatchDao;
import com.geeksec.analysis.entity.BatchPlugin;
import com.geeksec.analysis.entity.NetworkFlow;
import com.geeksec.analysis.entity.TaskAnalysis;
import com.geeksec.analysis.entity.TaskBatch;
import com.geeksec.analysis.entity.condition.FlowUpCondition;
import com.geeksec.analysis.entity.condition.TaskAnalysisCondition;
import com.geeksec.analysis.entity.condition.TaskConfigEditCondition;
import com.geeksec.analysis.entity.vo.NetworkFlowVo;
import com.geeksec.analysis.entity.vo.TaskEditVo;
import com.geeksec.analysis.service.TaskConfigService;
import com.geeksec.constants.Constants;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.ObjectUtil;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("nta-db")
public class TaskConfigServiceImpl implements TaskConfigService {

    private static final Logger logger = LoggerFactory.getLogger(TaskConfigServiceImpl.class);

    @Autowired
    private TaskBatchDao taskBatchDao;
    @Autowired
    private BatchPluginDao batchPluginDao;
    @Autowired
    private TaskAnalysisDao taskAnalysisDao;
    @Autowired
    private NetworkFlowDao networkFlowDao;

    @Override
    @Transactional
    public ResultVo configTaskEdit(TaskConfigEditCondition condition) {
        logger.info("修改任务配置，condition -> {}", condition);
        String system = condition.getSystem();


        if ("probe".equals(system)) {
            // 探针产品任务配置修改
            checkParam(condition);
            TaskBatch updateOb = new TaskBatch();
            Integer batchId = condition.getBatchId();
            Integer taskId = condition.getTaskId();
            updateOb.setBatchId(batchId);
            updateOb.setBatchRemark(condition.getBatchRemark());
            updateOb.setFullflowState(condition.getFullflowState());
            updateOb.setFullFlowShouldLogDef(condition.getFullFlowShouldLogDef());
            delPluginUpdate(batchId, null, 1, condition.getFullFlowShouldLogDef());
            //协议元数据是否开启
            Integer parseProtoShouldLogDef = condition.getParseProtoShouldLogDef();
            updateOb.setParseProtoShouldLogDef(parseProtoShouldLogDef);
            //协议需要开启的集合
            List<Integer> defList = condition.getParseProtoShouldLogDefList();
            delPluginUpdate(batchId, defList, 2, parseProtoShouldLogDef);

            //ddos状态更新
            taskBatchDao.updateTaskDDosState(taskId, condition.getDDosState());
            //tb_task_batch更新
            taskBatchDao.updateById(updateOb);

        } else if ("analysis".equals(system)) {
            // TODO 存储分析任务配置修改

        }
        return ResultVo.success();
    }

    /**
     * 修改plugin
     *
     * @param batchId
     * @param defList
     * @param type    1:会话元数据   2：协议
     */
    private void delPluginUpdate(Integer batchId, List<Integer> defList, Integer type, Integer shouldLogDef) {
        if (type == 1) {
            //只有一个对应id，直接操作
            batchPluginDao.updateList(batchId, null, shouldLogDef, type);
        } else {
            //先关闭所有
            batchPluginDao.updateList(batchId, null, Constants.OFF, type);
            if (Constants.ON.equals(shouldLogDef)) {
                //开启选中
                batchPluginDao.updateList(batchId, defList, Constants.ON, type);
            }
        }

    }

    private ResultVo checkParam(TaskConfigEditCondition condition) {
        String system = condition.getSystem();
        if (StringUtil.isEmpty(system) || (!"probe".equals(system) && !"analysis".equals(system))) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        Integer taskId = condition.getTaskId();
        if (taskId == null || taskId < 0 || taskId > 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        String fullflowState = condition.getFullflowState();
        if (StringUtil.isEmpty(fullflowState) || !(Constants.SYS_OFF.equals(fullflowState) || Constants.SYS_ON.equals(fullflowState)))
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);

        Integer fullFlowShouldLogDef = condition.getFullFlowShouldLogDef();
        if (fullFlowShouldLogDef != null && !(fullFlowShouldLogDef == 0 || fullFlowShouldLogDef == 1)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        Integer parseProtoShouldLogDef = condition.getParseProtoShouldLogDef();
        if (parseProtoShouldLogDef != null && !(parseProtoShouldLogDef == 0 || parseProtoShouldLogDef == 1)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        Integer dDosState = condition.getDDosState();
        if (dDosState != null && !(dDosState == 0 || dDosState == 1)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        List<Integer> list = condition.getParseProtoShouldLogDefList();
        if (parseProtoShouldLogDef == 1 && (list == null || list.isEmpty())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        return null;
    }

    @Override
    public Map<String, Object> configTaskInfo(Integer taskId) {

        logger.info("开始查询taskId为{}的任务配置信息", taskId);
        try {
            // 查询任务详情
            TaskBatch tbInfo = taskBatchDao.getTaskBatchInfoByTaskId(taskId);
            QueryWrapper<TaskBatch> wrapper = new QueryWrapper<>();
            wrapper.eq("task_id", taskId);
            TaskBatch info = taskBatchDao.selectOne(wrapper);

            // 查看当前任务DDos策略
            HashMap<String, Object> ddosMap = taskBatchDao.getTaskDDosState(taskId);
            Integer ddosState = null;
            if (!ddosMap.isEmpty()) {
                ddosState = (Integer) ddosMap.get("state");
            }

            // 查看存在的分析任务名称
            TaskAnalysis taInfo = taskBatchDao.getTaskAnalysis(taskId);

            String jsonStr = JSONObject.toJSONString(tbInfo);
            Map<String, Object> result = ObjectUtil.objectToMap(tbInfo);
            result.put("ddos_state", ddosState);
            result.put("task_name", taInfo.getTaskName());

            return result;
        } catch (Exception e) {
            logger.error("查询任务配置状态失败,error ->", e);
            throw new GkException(GkErrorEnum.QUERY_TASK_CONFIG_ERROR);
        }
    }

    @Override
    public List<TaskEditVo> taskEditVoList() {
        QueryWrapper<TaskBatch> batchQueryWrapper = new QueryWrapper<>();
        batchQueryWrapper.eq("state", Constants.ON);
        batchQueryWrapper.eq("task_type", 1);
        List<TaskBatch> taskBatches = taskBatchDao.selectList(batchQueryWrapper);
        List<TaskEditVo> taskEditVoList = new ArrayList<>();
        for (TaskBatch taskBatch : taskBatches) {
            TaskEditVo taskEditVo = new TaskEditVo();
            Integer batchId = taskBatch.getBatchId();
            taskEditVo.setBatchId(batchId);
            Integer taskId = taskBatch.getTaskId();
            taskEditVo.setTaskId(taskId);
            taskEditVo.setBatchRemark(taskBatch.getBatchRemark());
            taskEditVo.setFullflowState(taskBatch.getFullflowState());
            taskEditVo.setFullFlowShouldLogDef(taskBatch.getFullFlowShouldLogDef());
            Integer parseProtoShouldLogDef = taskBatch.getParseProtoShouldLogDef();
            taskEditVo.setParseProtoShouldLogDef(parseProtoShouldLogDef);
            //协议集合
            if (parseProtoShouldLogDef == 1) {
                List<BatchPlugin> batchPlugins = batchPluginDao.selectByBatchId(batchId);
                List<Integer> parseProtoShouldLogDefList = new ArrayList<>();
                for (BatchPlugin batchPlugin : batchPlugins) {
                    if (Constants.ON.equals(batchPlugin.getShouldLogDef())) {
                        parseProtoShouldLogDefList.add(batchPlugin.getPluginId());
                    }
                }
                taskEditVo.setParseProtoShouldLogDefList(parseProtoShouldLogDefList);
            }
            //ddos
            HashMap<String, Object> taskDDosState = taskBatchDao.getTaskDDosState(taskId);
            taskEditVo.setDDosState(Integer.valueOf(taskDDosState.get("state").toString()));
            //任务挂起  及  网口
            QueryWrapper<TaskAnalysis> taskAnalysisQueryWrapper = new QueryWrapper<>();
            taskAnalysisQueryWrapper.eq("task_id", taskId);
            List<TaskAnalysis> taskAnalyses = taskAnalysisDao.selectList(taskAnalysisQueryWrapper);
            if (taskAnalyses != null && !taskAnalyses.isEmpty()) {
                TaskAnalysis taskAnalysis = taskAnalyses.get(0);
                taskEditVo.setTaskState(taskAnalysis.getTaskState());
                String netflow = taskAnalysis.getNetflow();
                List<Integer> flowIds = JSONArray.parseArray(netflow, Integer.class);
                List<NetworkFlowVo> networkFlowVos = new ArrayList<>();
                if (flowIds != null && !flowIds.isEmpty()) {
                    List<NetworkFlow> networkFlows = networkFlowDao.selectBatchIds(flowIds);
                    for (NetworkFlow networkFlow : networkFlows) {
                        NetworkFlowVo networkFlowVo = new NetworkFlowVo();
                        BeanUtils.copyProperties(networkFlow, networkFlowVo);
                        networkFlowVos.add(networkFlowVo);
                    }
                }
                taskEditVo.setNetflowVos(networkFlowVos);
            }
            taskEditVoList.add(taskEditVo);
        }
        return taskEditVoList;
    }

    @Override
    @Transactional
    public ResultVo updateAnalysis(List<TaskAnalysisCondition> conditions) {
        TaskAnalysisCondition condition0 = null;
        TaskAnalysisCondition condition1 = null;
        for (TaskAnalysisCondition condition : conditions) {
            if (condition.getTaskId() == 0) {
                condition0 = condition;
            }
            if (condition.getTaskId() == 1) {
                condition1 = condition;
            }
        }
        //需要处理两个任务
        if (condition0 == null || condition1 == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        //Collections.disjoint(list1 , list2)  true表示 没有交集
        List<Integer> nets0 = condition0.getNetflows();
        List<Integer> nets1 = condition1.getNetflows();
        if (nets0.isEmpty() && nets1.isEmpty()) {
            return ResultVo.fail("端口集合不能全为空");
        }
        if (!Collections.disjoint(nets0, nets1)) {
            //有交集
            return ResultVo.fail("端口集合有交集");
        }
        List<Integer> nets = new ArrayList<>();
        nets.addAll(nets0);
        nets.addAll(nets1);
        List<Integer> ids = taskAnalysisDao.checkFlowId(nets);
        if (ids == null || ids.size() < nets.size()) {
            return ResultVo.fail("端口集合有不合法的端口名");
        }
        Integer state0 = condition0.getTaskState();
        Integer state1 = condition1.getTaskState();
        if (state0 == null || state0 < 0 || state0 > 2) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        if (state1 == null || state1 < 0 || state1 > 2) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        TaskAnalysis up0 = new TaskAnalysis();
        up0.setTaskId(condition0.getTaskId());
        up0.setTaskState(state0);
        up0.setNetflow(JSON.toJSONString(nets0));
        if (state0 == 2) {
            //这里数据库直接+1
            up0.setSuspendTimes(-1);
        }
        taskAnalysisDao.updateFlow(up0);
        TaskAnalysis up1 = new TaskAnalysis();
        up1.setTaskId(condition1.getTaskId());
        up1.setTaskState(state1);
        up1.setNetflow(JSON.toJSONString(nets1));
        if (state1 == 2) {
            //这里数据库直接+1
            up1.setSuspendTimes(-1);
        }
        taskAnalysisDao.updateFlow(up1);

        return ResultVo.success();
    }

    @Override
    @Transactional
    public ResultVo updateFlowName(List<FlowUpCondition> conditions) {
        List<Integer> ids = new ArrayList<>();
        List<NetworkFlow> flows = new ArrayList<>();
        for (FlowUpCondition condition : conditions) {
            NetworkFlow networkFlow = new NetworkFlow();
            ids.add(condition.getId());
            BeanUtils.copyProperties(condition, networkFlow);
            flows.add(networkFlow);
        }
        List<Integer> checkIds = taskAnalysisDao.checkFlowId(ids);
        if (checkIds == null || checkIds.size() < ids.size()) {
            return ResultVo.fail("端口集合有不合法的端口名");
        }
        for (NetworkFlow flow : flows) {
            int i = networkFlowDao.updateById(flow);
        }
        return ResultVo.success();
    }
}

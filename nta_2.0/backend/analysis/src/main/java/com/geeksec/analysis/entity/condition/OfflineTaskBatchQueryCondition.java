package com.geeksec.analysis.entity.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class OfflineTaskBatchQueryCondition extends BaseCondition {


    @ApiModelProperty("任务ID")
    @JsonProperty("task_id")
    private String taskId;

}

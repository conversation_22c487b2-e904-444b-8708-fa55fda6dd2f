package com.geeksec.analysis.entity.dict;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("app_pro_value")
public class AppProValue implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 协议ID ---应用协议知识库---
     */
    @TableId("pro_id")
    private Integer proId;

    /**
     * 协议名称1
     */
    @TableField("pro_value")
    private String proValue;

    /**
     * 协议名称2
     */
    @TableField("pro_name")
    private String proName;

    /**
     * 协议类型
     */
    @TableField("pro_type")
    private String proType;

    /**
     * 协议说明
     */
    @TableField("pro_exp")
    private String proExp;

    /**
     * 协议类型 1 为连接 2 为单包 3 tcp/udp负载
     */
    @TableField("type")
    private Integer type;

}

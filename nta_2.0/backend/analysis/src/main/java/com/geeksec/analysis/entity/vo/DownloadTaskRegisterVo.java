package com.geeksec.analysis.entity.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: GuanHao
 * @Date: 2022/5/19 18:07
 * @Description： <Functions List>
 */
@Data
public class DownloadTaskRegisterVo {
    /**
     * 主键
     */
    @JsonProperty(value = "id")
    private Integer id;

    /**
     * 用户id
     */
    @JsonProperty("user_id")
    private Integer userId;

    /**
     * 用户名
     */
    @JsonProperty("user_name")
    private String userName;

    /**
     * 日志保存路径
     */
    @JsonProperty("path")
    private String path;

    /**
     * 查询条件
     */
    @JsonProperty("query")
    private String query;

    /**
     * 前端展示字段
     */
    @TableField("show_query")
    private String showQuery;

    /**
     * 日志状态，1 待执行，2 准备数据，3 待下载，4 已删除，-1错误
     */
    @JsonProperty("type")
    private Integer type;

    /**
     * 下载次数
     */
    @JsonProperty("download_count")
    private Integer downloadCount;

    /**
     * 删除时间
     */
    @JsonProperty("delete_time")
    private Long deleteTime;

    /**
     * 修改时间
     */
    @JsonProperty("update_time")
    private Long updateTime;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private Long createTime;

    /**
     * 任务类型：1 日志下载 3 元数据下载
     */
    @JsonProperty("task_type")
    private Integer taskType;

    /**
     * 如果为元数据下载，则拥有其元数据类型
     */
    @JsonProperty("metadata_type")
    private String metadataType;

    /**
     * 排名属性，该字段不在数据库内。
     */
    @JsonProperty("front_num")
    private Integer frontNum;

}

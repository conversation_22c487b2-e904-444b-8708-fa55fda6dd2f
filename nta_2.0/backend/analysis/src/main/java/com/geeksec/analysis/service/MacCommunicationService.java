package com.geeksec.analysis.service;

import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.entity.common.ResultVo;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/7/25 17:12
 * @Description： <Functions List>
 */
public interface MacCommunicationService {

    /**
     * Mac 通讯查询接口
     *
     * @return
     */
    ResultVo macList(AnalysisBaseCondition condition);
}

package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Author: GuanHao
 * @Date: 2022/5/11 10:10
 * @Description： <Functions List>
 */
@Data
@TableName("tb_tag_attribute")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TbTagAttribute {

    @TableField("attribute_id")
    private Integer attributeId;

    @TableField("attribute_name")
    private String attributeName;

    @TableField("target_id")
    private Integer targetId;


}

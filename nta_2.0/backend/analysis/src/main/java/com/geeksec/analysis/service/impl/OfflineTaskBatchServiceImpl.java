package com.geeksec.analysis.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.analysis.dao.TaskBatchDao;
import com.geeksec.analysis.entity.TaskAnalysis;
import com.geeksec.analysis.entity.TaskBatch;
import com.geeksec.analysis.entity.condition.OfflineTaskBatchQueryCondition;
import com.geeksec.analysis.entity.dto.OfflineFilePathDto;
import com.geeksec.analysis.entity.dto.OfflineTaskBatchCancelDto;
import com.geeksec.analysis.entity.dto.OfflineTaskBatchDto;
import com.geeksec.analysis.entity.vo.FileTreeNodeVo;
import com.geeksec.analysis.entity.vo.OfflineTaskBatchPageVo;
import com.geeksec.analysis.entity.vo.PageVo;
import com.geeksec.analysis.service.OfflineTaskBatchFileService;
import com.geeksec.analysis.service.OfflineTaskBatchService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.HttpUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 离线任务批次服务impl
 */
@Service
@DS("nta-db")
public class OfflineTaskBatchServiceImpl implements OfflineTaskBatchService {

    private static final Logger logger = LoggerFactory.getLogger(OfflineTaskBatchServiceImpl.class);

    @Autowired
    private TaskBatchDao taskBatchDao;
    @Autowired
    private OfflineTaskBatchFileService offlineTaskBatchFileService;
    @Value("${file.list-files-url}")
    private String LIST_FILES_URL;
    @Value("${file.check-files-url}")
    private String CKECK_FILES_URL;
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;
    private static final String OFFLINE_THD = "offline_thd";
    @Value("${file.stop-batch-offline_thd}")
    private String STOP_BATCH_OFFLINE_THD;
    @Value("${file.server-pcap-path}")
    private String SERVER_PCAP_PATH;

    @Override
    public PageVo<OfflineTaskBatchPageVo> pageBatch(OfflineTaskBatchQueryCondition condition) {
        PageVo<OfflineTaskBatchPageVo> pageVo = new PageVo<>();
        PageHelper.startPage(condition.getCurrentPage(), condition.getPageSize());
        List<OfflineTaskBatchPageVo> offlineTaskBatchPageVoList = taskBatchDao.pageBatch(condition);
        PageInfo pageInfo = new PageInfo<>(offlineTaskBatchPageVoList);
        pageVo.setTotal(pageInfo.getTotal());
        pageVo.setData(pageInfo.getList());
        return pageVo;
    }

    @Override
    public ResultVo addBatch(OfflineTaskBatchDto dto) {
        TaskAnalysis taskAnalysis = taskBatchDao.getTaskAnalysis(dto.getTaskId());
        if(taskAnalysis==null){
            return ResultVo.fail("任务id不存在");
        }
        List<String> duplicateFiles = new ArrayList<>();
        Set<String> uniqueFiles = new HashSet<>();
        for (OfflineFilePathDto filePath : dto.getFilePathList()) {
            if (!uniqueFiles.add(filePath.getLocalPath())) {
                duplicateFiles.add(filePath.getLocalPath());
            }
        }
        if(duplicateFiles!=null && duplicateFiles.size()>0){
            return ResultVo.fail("以下文件重复："+duplicateFiles);
        }
        if(dto.getBatchType()==1){
            for (OfflineFilePathDto filePath : dto.getFilePathList()) {
                filePath.setServerPath(SERVER_PCAP_PATH+filePath.getServerPath());
            }
        }
        JSONObject paramJson = new JSONObject();
        paramJson.put("file_path_list",dto.getFilePathList().stream().map(OfflineFilePathDto::getServerPath).collect(Collectors.toList()));
        JSONObject responseJson = HttpUtils.sendPost(CKECK_FILES_URL,paramJson.toJSONString());
        JSONArray jsonArray = responseJson.getJSONArray("non_exist_files");
        if(jsonArray!=null && jsonArray.size()>0){
            return ResultVo.fail("以下文件不存在："+JSONObject.parseArray(jsonArray.toJSONString(),String.class));
        }
        TaskBatch offlineTaskBatch = new TaskBatch();
        offlineTaskBatch.setTaskId(dto.getTaskId());
        offlineTaskBatch.setTaskType(taskAnalysis.getTaskType());
        offlineTaskBatch.setBatchRemark(dto.getBatchDescription());
        offlineTaskBatch.setBatchType(dto.getBatchType());
        offlineTaskBatch.setFullflowState(dto.getFullflowState());
        offlineTaskBatch.setFlowlogState(dto.getFlowlogState());
        taskBatchDao.insert(offlineTaskBatch);
        offlineTaskBatchFileService.add(offlineTaskBatch,dto.getFilePathList());
        // 给Kafka那边传送一条message
        JSONObject offlineTaskBatchJson = new JSONObject();
        offlineTaskBatchJson.put("task_id", offlineTaskBatch.getTaskId());
        offlineTaskBatchJson.put("batch_id", offlineTaskBatch.getBatchId());
        offlineTaskBatchJson.put("input_dirs", dto.getFilePathList().stream().map(OfflineFilePathDto::getServerPath).collect(Collectors.toList()));
        String jsonStr = JSONObject.toJSONString(offlineTaskBatchJson);
        // 发送消息
        ListenableFuture<SendResult<String, Object>> future = kafkaTemplate.send(OFFLINE_THD, "offline_task_batch", jsonStr);
        future.addCallback(new ListenableFutureCallback<SendResult<String, Object>>() {
            @Override
            public void onFailure(Throwable throwable) {
                logger.info("离线导入数据导入消息推送失败，task_id--->{},batch_id--->{},input_dirs--->{}", offlineTaskBatch.getTaskId(),offlineTaskBatch.getBatchId(),dto.getFilePathList());
                throw new GkException(GkErrorEnum.OFFLINE_TASK_BATCH_ERROR);
            }
            @Override
            public void onSuccess(SendResult<String, Object> stringObjectSendResult) {
                logger.info("离线导入数据导入消息推送成功，task_id--->{},batch_id--->{},input_dirs--->{}", offlineTaskBatch.getTaskId(),offlineTaskBatch.getBatchId(),dto.getFilePathList());
            }
        });
        return ResultVo.success();
    }

    @Override
    public ResultVo cancelBatch(OfflineTaskBatchCancelDto dto) {
        //修改批次状态
        taskBatchDao.cancelBatch(dto.getBatchId());
        //停止探针服务
        String serviceName = taskBatchDao.getServiceNameByBatchId(dto.getBatchId());
        JSONObject paramJson = new JSONObject();
        paramJson.put("service_name",serviceName);
        HttpUtils.sendPost(STOP_BATCH_OFFLINE_THD,paramJson.toJSONString());
        return ResultVo.success();
    }

    @Override
    public List<FileTreeNodeVo> listServerPath(String directoryPath) {
        List<FileTreeNodeVo> list = new ArrayList<>();
        try {
            String url = LIST_FILES_URL;
            if(!StringUtils.isEmpty(directoryPath)){
                url = LIST_FILES_URL+"?dir="+directoryPath;
            }
            String response = HttpUtils.setGet(url);
            // 使用 Jackson 解析 JSON 响应
            ObjectMapper objectMapper = new ObjectMapper();
            list =  objectMapper.readValue(
                    response, new TypeReference<List<FileTreeNodeVo>>() {});
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }

}

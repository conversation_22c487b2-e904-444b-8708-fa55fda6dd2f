package com.geeksec.analysis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tb_filter_state")
public class FilterState implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID ---筛选 fifter 配置---
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Integer taskId;

    /**
     * 0表示保留，1表示丢弃
     */
    @TableField("state")
    private Integer state;


}

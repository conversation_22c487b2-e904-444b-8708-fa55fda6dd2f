package com.geeksec.analysis.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/5/11 11:34
 * @Description： <Functions List>
 */
@Data
public class TagInfoVo {

    @JsonProperty("tag_id")
    private Integer tagId;

    @JsonProperty("tag_explain")
    private String tagExplain;

    @JsonProperty("tag_text")
    private String tagText;

    @JsonProperty("black_list")
    private Integer blackList;

    @JsonProperty("white_list")
    private Integer whiteList;
}

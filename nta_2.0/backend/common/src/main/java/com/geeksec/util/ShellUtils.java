package com.geeksec.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.util.List;

/**
 * <AUTHOR>
 * @Description： shell脚本工具类 一定执行最后的destroy关流
 */
public class ShellUtils {
    private static ProcessBuilder processBuilder = new ProcessBuilder();

    /**
     * 执行脚本命令
     * <AUTHOR>
     * @param commend
     * @return  java.lang.Process
     * @date    2019/9/26
     * @throws
     */
    public static Process exec(List<String> commend) {
        Process process = null;
        try {
            String[] commends = new String[commend.size()];
            commend.toArray(commends);
            processBuilder.command(commends);
            process = processBuilder.start();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return process;
    }

    /**
     * 获取输出的信息
     */
    public static String getOutput(Process process) {
        String output = null;
        BufferedReader reader = null;
        try {
            if (process != null) {
                StringBuffer stringBuffer = new StringBuffer();
                reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                while (reader.read() != -1){
                    stringBuffer.append("\n" + reader.readLine());
                }
                output = stringBuffer.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeQuietly(reader);
        return output;
    }

    /**
     * 获取错误信息
     * @throws
     */
    public static String getError(Process process) {
        String errput = null;
        BufferedReader reader = null;
        try {
            if (process != null) {
                StringBuffer stringBuffer = new StringBuffer();
                reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                while (reader.read() != -1){
                    stringBuffer.append("\n" + reader.readLine());
                }
                errput = stringBuffer.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeQuietly(reader);
        return errput;
    }

    /**
     * 关流
     * <AUTHOR>
     * @throws
     */
    public static void closeQuietly(Reader reader) {
        try {
            if (reader != null) {
                reader.close();
            }
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }

    /**
     * 终止进程
     * @throws
     */
    public static void destroy(Process process) {
        if (process != null) {
            process.destroyForcibly();
        }
    }
}

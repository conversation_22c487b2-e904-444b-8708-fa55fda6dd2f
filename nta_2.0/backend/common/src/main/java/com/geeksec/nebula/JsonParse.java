package com.geeksec.nebula;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;
import java.util.List;

public class JsonParse {
    private static JacksonHandle instance=null;
    public JsonNode hJsonParse(String str)
    {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(str);
            return root;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
    public JsonNode getObjValue(JsonNode obj, String path)
    {
        try {
            List<String> result = Arrays.asList(path.split("/"));
            JsonNode objL = obj;
            for (int i = 0; i < result.size(); i++) {
                // 判断有没有符号
                String filed = result.get(i);
                int nt1 = filed.indexOf("[");
                int nt2 = filed.indexOf("]");
                if(nt1 != -1 && nt2 != -1)
                {
                    String sArrFiled = filed.substring(0,nt1-1);
                    int num = Integer.parseInt(filed.substring(nt1+1,nt2-1));
                    objL = objL.get(filed).get(num);
                }
                else {
                    if (objL == null) {
                        return null;
                    }
                    objL = objL.get(filed);
                }
            }
            return objL;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null ;
    }
}

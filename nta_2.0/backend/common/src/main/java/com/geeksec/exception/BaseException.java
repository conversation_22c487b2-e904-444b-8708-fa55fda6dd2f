package com.geeksec.exception;

/**
 * <AUTHOR>
 * @Description：
 */
public class BaseException extends Exception{

    /**
     *
     */
    private static final long serialVersionUID = 1L;

    protected int code = -1;

    public final static int NOT_FOUND = 404;

    public BaseException() {
        super();
    }

    public BaseException(int code) {
        super();
        this.code = code;
    }
    public BaseException(String message) {
        super(message);
    }

    public BaseException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public BaseException(Throwable cause) {
        super(cause);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}

package com.geeksec.constants;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/3/14 15:54
 * @Description： <Functions List>
 */
public class LoggerString {

    /**
     * 开始执行
     */
    public static final String LOG_EXECUTOR = "Execute the command to: {}.";

    /**
     * 执行修改密码请求
     */
    public static final String LOG_CHANGE_PASSWORD = "User [ {} ] requested to change the password.";

    /**
     * 密码修改成功
     */
    public final static String LOG_PASSWORD_SUCCESS = "Password change successfully of user [ {} ], the new password: {}.";

    /**
     * 获取主机信息
     */
    public static final String LOG_GET_SERVER_INFO = "host information is successfully obtained,json: {}";

    /**
     * 执行过程中出现异常
     */
    public final static String LOG_EXCEPTION = "Failed to execute {} command, return message:{}";

    /**
     * 密码修改异常
     */
    public final static String LOG_PASSWORD_EXCEPTION = "Password change exception of user [ {} ].";

}

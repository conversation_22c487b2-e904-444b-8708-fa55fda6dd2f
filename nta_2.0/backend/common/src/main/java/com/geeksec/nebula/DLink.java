package com.geeksec.nebula;

public class DLink extends  VLBase  {
    DLink(String lName  ) {
        name = lName;
        type = "L";
    }
    public  SqlParse parse(SqlParse sqlParse) {
        if (sqlParse.lastVType.equals("V")) {
            if (name.equals("D") ) {
                sqlParse.sql += "-";
            } else if  (name.equals("R") ) {
                sqlParse.sql += "->";
            } else if  (name.equals("L") ) {
                sqlParse.sql += "<-";
            }
        }
        return sqlParse;
    }
}

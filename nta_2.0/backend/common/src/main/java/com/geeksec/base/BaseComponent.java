package com.geeksec.base;

import com.geeksec.util.AssertUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 *
 *
 */
@Component
public class BaseComponent {
    @Autowired
    protected MessageSource messageSource;

    private final Locale locale = Locale.SIMPLIFIED_CHINESE;

    protected String getMessage(String code) {
        AssertUtil.hasTextString(code);
        return messageSource.getMessage(code, null, locale);
    }

    protected String getMessage(String code, @Nullable Object[] args) {
        AssertUtil.hasTextString(code);
        return messageSource.getMessage(code, args, locale);
    }

    protected String getMessage(String code, String... argsCodes) {
        AssertUtil.hasTextString(code);
        if (argsCodes != null && argsCodes.length > 0) {
            Object[] args = new Object[argsCodes.length];
            for (int i = 0; i < argsCodes.length; i++) {
                try {
                    args[i] = getMessage(argsCodes[i]);
                }catch(Exception e){
                    args[i] =argsCodes[i];
                }
            }
            return getMessage(code, args);
        } else {
            return getMessage(code);
        }
    }
}

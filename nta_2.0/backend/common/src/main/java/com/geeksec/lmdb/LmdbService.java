package com.geeksec.lmdb;

import com.PB2Msg.test.Pb2MsgPB;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.entity.common.ZMPNMsg;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.lmdbjava.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static com.geeksec.lmdb.LmdbHandle.bb;
import static com.geeksec.lmdb.LmdbHandle.bytebuffer2ByteArray;
import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;
import static org.lmdbjava.DbiFlags.MDB_CREATE;
import static org.lmdbjava.Env.create;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
public class LmdbService {

    private static final Logger logger = LoggerFactory.getLogger(LmdbService.class);

    @Value("${external-system-url.pb-file}")
    private String lmdbUrl;

    @Value("${lmdb.path}")
    private String lmdbPath;

    @Value("${lmdb.max-size}")
    private long LMDB_MAXSIZE;
    /**
     * 通过调用外部接口去获取lmdb的数据
     *
     * @param esMap
     * @param hKey
     * @return
     */
    /**
     * 处理LMDB数据
     *
     * @param esMap 原始数据Map
     * @param hKey 查询键
     * @return 处理后的数据Map
     * @throws GkException 当数据处理发生错误时抛出
     */
    public Map<String, Object> hLmdbHandle(Map<String, Object> esMap, String hKey) {
        // 参数校验
        if (esMap == null) {
            throw new IllegalArgumentException("esMap cannot be null");
        }

        try {
            // 获取key
            String key = getKey(esMap, hKey);

            // 获取LMDB数据
            byte[] byteArray = fetchLmdbData(key);

            if (byteArray == null) {
                logger.info("No data found in LMDB for Hkey: {}", key);
                return esMap;
            }

            // 解析Protocol Buffer数据
            return parsePbData(byteArray, esMap);

        } catch (GkException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Failed to process LMDB data for Hkey: {}", hKey, e);
            throw new GkException(GkErrorEnum.SESSION_DETAIL_PARSE_ERROR);
        }
    }

    /**
     * 获取查询键
     */
    private String getKey(Map<String, Object> esMap, String hKey) {
        if (StringUtils.isNotEmpty(hKey)) {
            return hKey;
        }
        Object hKeyObj = esMap.get("Hkey");
        if (hKeyObj == null) {
            throw new IllegalArgumentException("Hkey not found in esMap");
        }
        return hKeyObj.toString();
    }

    /**
     * 从LMDB获取数据
     */
    private byte[] fetchLmdbData(String key) {
        String[] keys = key.split("_");
        Path dbPath = Paths.get(lmdbPath,keys[0]);
        File dbFile = new File(dbPath.toString());
        if (!dbFile.exists()){
            logger.info("dbFile not found");
            return null;
        }
        List<String> dbs = new ArrayList<>();
        Arrays.stream(Objects.requireNonNull(dbFile.list())).forEach(file -> {
            if (file.startsWith(keys[1] + "_" + keys[2] + "_" + keys[3])){
                Path path = Paths.get(String.valueOf(dbPath), file);
                dbs.add(String.valueOf(path));
            }
        });
        if (dbs.isEmpty()){
            logger.info("dbFile not found");
            return null;
        }
        return getValueByKey(dbs,key);
    }

    public byte[] getValueByKey(List<String> dbs, String key) {
        for (String dbName : dbs) {
            File lmdbDir = new File(dbName);
            logger.info("Current Path {}", lmdbDir);
            if (!lmdbDir.exists()) {
                logger.info("Current Path not exists  {}", lmdbDir);
            }
            // Create LMDB environment
            try (Env<ByteBuffer> env = // 创建或打开LMDB环境
                         create(PROXY_OPTIMAL)
                                 .setMapSize(LMDB_MAXSIZE)
                                 .open(lmdbDir, EnvFlags.MDB_NOTLS)) {
                Dbi<ByteBuffer> dbi = env.openDbi((byte[]) null, DbiFlags.MDB_CREATE);
                try (Txn<ByteBuffer> txn = env.txnWrite()) {
                    Cursor<ByteBuffer> c = dbi.openCursor(txn);
                    if (c.get(bb(key), GetOp.MDB_SET_KEY)) {
                        ByteBuffer byteBuffer = c.val();
                        c.close();
                        logger.info("查询  ===  " + key + "结果：  " + byteBuffer.toString());
                        return bytebuffer2ByteArray(byteBuffer);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.err.println("Failed to read LMDB data");
            }
        }
        return null;
    }

    /**
     * 解码Base64数据
     */
    private byte[] decodeBase64(String base64Str) {
        try {
            return Base64.getDecoder().decode(base64Str);
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid Base64 string", e);
            return null;
        }
    }

    /**
     * 解析Protocol Buffer数据并合并esMap
     */
    private Map<String, Object> parsePbData(byte[] byteArray, Map<String, Object> esMap) throws Exception {
        ZMPNMsg.JKNmsg jkNmsg = ZMPNMsg.JKNmsg.parseFrom(byteArray);
        Map<String, Object> pbMap = new Pb2MsgPB().handle(jkNmsg);

        // 合并数据
        esMap.forEach((key, value) -> {
            if ("AppName".equals(key) || !pbMap.containsKey(key)) {
                pbMap.put(key, value);
            }
        });

        return pbMap;
    }

    private String post(String url, List<String> respBodyMap) {

        // 创建httpClient实例对象
        try {

            PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
            cm.setMaxTotal(100);
            cm.setDefaultMaxPerRoute(10);
            CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();//设置数据传输的最长时间
            HttpPost httpPost = new HttpPost(url);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(1000)//设置创建连接的最长时间
                    .setConnectionRequestTimeout(500)//设置获取连接的最长时间
                    .setSocketTimeout(10 * 1000)//设置数据传输的最长时间
                    .build();
            httpPost.setConfig(requestConfig);
            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
            StringEntity requestEntity = new StringEntity(JSONObject.toJSONString(respBodyMap), "utf-8");
            httpPost.setEntity(requestEntity);
            CloseableHttpResponse response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }

            String resultStr = EntityUtils.toString(entity);

            logger.info("LMDB解析读取信息:{}" , resultStr);
            response.close();

            return resultStr;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}

package com.geeksec.nebula;

import cn.hutool.core.util.ObjectUtil;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

//  对nanubal 查询的数据 整理成图的结构
// V(id , type , status  ,  value )
// V_Vname_id
// E( from: to:   value: )
//
//  解析配置
public class VEMap {
    private Set<String> vertexSet = new HashSet<>();
    private Set<String> edgeSet = new HashSet<>();
    private HashMap<String, Set<String>> labelMap = new HashMap<String, Set<String>>();

    private void print(Set<String> T) {
        //System.out.ut.println("********* Print ******************");
        for (String L : T) {
            //System.out.ut.println(L);
        }
        //System.out.ut.println("********* End ******************");
    }

    public boolean addVertex(Map<String, Object> vertexMap) {
        if (ObjectUtil.isEmpty(vertexMap.get("id"))) {
            vertexMap.put("id", "-");
        }
        String key = vertexMap.get("id").toString() + "_" + vertexMap.get("type").toString();
        //System.out.ut.println("AddVertice  key ===== "+Key);
        // Print(VSet);
        if (vertexSet.contains(key)) {
            print(vertexSet);
            return false;
        } else {
            vertexSet.add(key);
            return true;
        }

    }

    public boolean addEdge(Map<String, Object> edgeMap) {

        if (ObjectUtil.isEmpty(edgeMap.get("from"))) {
            edgeMap.put("from", "-");
        }
        if (ObjectUtil.isEmpty(edgeMap.get("to"))) {
            edgeMap.put("to", "-");
        }
        // 如果存在两个点相同的情况，但边类型不一样时，做特殊判断
        String key = edgeMap.get("from").toString() + "_" + edgeMap.get("to").toString();
        String label = edgeMap.get("label").toString();

        // 判断当前边Key-边类型是否存在
        if (!labelMap.containsKey(key)) {
            Set<String> labelSet = new HashSet<>();
            labelSet.add(label);
            labelMap.put(key, labelSet);
            return true;
        } else {
            Set<String> labelSet = labelMap.get(key);
            if (labelSet.contains(label)) {
                return false;
            } else {
                return true;
            }
        }

        //if (ESet.contains(Key) ) {
        //    return false;
        //} else {
        //    ESet.add(Key);
        //    return true;
        //}
    }
}


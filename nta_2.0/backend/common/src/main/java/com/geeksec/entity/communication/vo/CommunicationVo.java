package com.geeksec.entity.communication.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：通信信息（IP态势图）展示VO
 */
@Data
public class CommunicationVo {
    /**
     * 协议ID
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 数量
     */
    @JsonProperty("cnt")
    private long count;

    /**
     * 目的IP
     */
    @JsonProperty("d_ip")
    private String dIp;

    /**
     * 源端口
     */
    @JsonProperty("dst_port")
    private String dstPort;

    /**
     * 源IP
     */
    @JsonProperty("s_ip")
    private String sIp;
}

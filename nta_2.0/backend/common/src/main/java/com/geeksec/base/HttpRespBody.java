package com.geeksec.base;
//import com.bigdata.utils.ConstantUtils;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 *
 */
public class HttpRespBody<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final int OK = 200;
    private static final int ERROR = 500;

    @ApiModelProperty(value = "返回CODE:1为正常，0位异常")
    private int code;
    @ApiModelProperty(value = "返回错误信息")
    private String msg;
    @ApiModelProperty(value = "返回数据")
    private T data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public HttpRespBody(int code, T data, String msg) {
        super();
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    public static HttpRespBody<String> okBody() {
        return okBody("ok");
    }

    public static <T> HttpRespBody<T> okBody(T data) {
        HttpRespBody<T> httpRespBody = new HttpRespBody<T>(OK, data,"");
        return httpRespBody;
    }

    public static <T> HttpRespBody<T> errorBody(int code,String errorMsg) {
        HttpRespBody<T> httpRespBody = new HttpRespBody<T>(code,null,errorMsg);
        return httpRespBody;
    }
}

package com.geeksec.nebula;

public class Edge extends  VLBase  {
    public Edge(String eName) {
        name = eName;
        type = "E";
    }
    public  SqlParse parse(SqlParse sqlParse) {
        if (sqlParse.lastVType.equals("V")) {
            sqlParse.sql += "-";
        }else if (sqlParse.lastVType.equals("E")) {
            sqlParse.sql += "-";
        }
        //String VAliseName = PSql.GetEdgeAlias(Name);
        sqlParse.sql += "[" + name +"]";
        return sqlParse;

    }
}

package com.geeksec.lmdb;

import com.PB2Msg.test.Pb2MsgPB;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.entity.common.ZMPNMsg;
import com.geeksec.pb2Msg.HbaseOper;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.lmdbjava.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static java.lang.Integer.BYTES;
import static java.nio.ByteBuffer.allocateDirect;
import static org.lmdbjava.ByteBufferProxy.PROXY_OPTIMAL;
import static org.lmdbjava.DbiFlags.MDB_CREATE;
import static org.lmdbjava.Env.create;

class LmdbHandle {
    private final static Logger logger = LoggerFactory.getLogger(LmdbHandle.class);
    private Env<ByteBuffer> env = null;
    private Long lmdbMaxSize = 1099511627776L;
    private String dbPath = "/data/lmdb/";

    public List<String> scanPath(String filePath, String db) {
        //读取目录
        System.out.println("PATH ：" + filePath + "     =====  db === " + db);
        File file = new File(filePath);
        File[] tempList = file.listFiles();
        List<String> filenamelist = new ArrayList<>();

        for (int i = 0; i < tempList.length; i++) {

            System.out.println("tempList[i].toString() === [" + tempList[i].getName() + "]    db ==== [" + db + "]");

            if (tempList[i].getName().startsWith(db)) {
                filenamelist.add(tempList[i].getName());
            }
        }
        System.out.println("该目录下对象个数：" + filenamelist.size());
        return filenamelist;
    }

    public byte[] pbbyteHandle(String db, String key) {
        String lmdbPath = dbPath + db;
        // #########  ###########
        try {
            final File path = new File(lmdbPath);

            if (!path.exists()) {
                path.mkdirs();
            }
            env = create(PROXY_OPTIMAL)
                    .setMapSize(lmdbMaxSize)
                    .open(path);
            logger.info("初始化Lmdb " + lmdbPath + "成功......");
            // }


            //return env;
        } catch (Exception e) {
            logger.error("lmdbPath ===== " + lmdbPath);
            return null;
        }
        byte[] b = getValueByKey(db, key);
        env.close();
        return b;

    }

    public byte[] handle(Map<String, Object> esMap) {
        String key = esMap.get("Hkey").toString();
        String[] arr = key.split("_");
        String db = arr[1] + "_" + arr[2] + "_" + arr[3];
        List<String> dbList = scanPath(dbPath, db);
        for (String dbItem : dbList) {
            byte[] b = pbbyteHandle(dbItem, key);
            if (b != null) {
                return b;
            }
        }
        return null;

    }

    static byte[] bytebuffer2ByteArray(ByteBuffer buffer) {

        //获取buffer中有效大小

        int len = buffer.limit() - buffer.position();
        logger.info("    结果 长度  :  " + String.valueOf(buffer.limit()) + " ====  " + String.valueOf(buffer.position()));
        byte[] bytes = new byte[len];
        logger.info("    结果 长度  :  " + String.valueOf(len));
        for (int i = 0; i < len; i++) {
            bytes[i] = buffer.get();

        }

        return bytes;
    }

    public byte[] getValueByKey(String dbName, String key) {

        final Dbi<ByteBuffer> db = env.openDbi(dbName, MDB_CREATE);
        final Cursor<ByteBuffer> c;
        byte[] value = null;
        try (Txn<ByteBuffer> txn = env.txnRead()) {
            c = db.openCursor(txn);
            if (c.get(bb(key), GetOp.MDB_SET_KEY)) {
                ByteBuffer byteBuffer = c.val();
                c.close();
                logger.info("查询  ===  " + key + "          结果：  " + byteBuffer.toString());

                value = bytebuffer2ByteArray(byteBuffer);
                return value;
            }

        }
        return null;

    }

    static ByteBuffer bb(final int value) {
        final ByteBuffer bb = allocateDirect(BYTES);
        bb.putInt(value).flip();
        return bb;
    }

    static ByteBuffer bb(final String value) {
        byte[] val = value.getBytes(StandardCharsets.UTF_8);
        final ByteBuffer bb = allocateDirect(val.length);
        bb.put(val).flip();
        return bb;
    }

    static ByteBuffer bb(final byte[] val) {
        //byte[] val = value.getBytes(StandardCharsets.UTF_8);
        final ByteBuffer bb = allocateDirect(val.length);
        bb.put(val).flip();
        return bb;
    }
}

@Component
public class PbMsgHandle {

    @Value("${send-url.lmdb_file}")
    private static String lmdbUrl ;

    public Map<String, Object> handle(Map<String, Object> esMap) {
        String hKey = esMap.get("Hkey").toString();
        HbaseOper pHbase = new HbaseOper();
        try {
            pHbase.setUp();
            Map<String, byte[]> hKeyMap = pHbase.queryTableByRowKey("PbData", hKey);

            ZMPNMsg.JKNmsg jkNmsg = ZMPNMsg.JKNmsg.parseFrom(hKeyMap.get("row"));
            Pb2MsgPB pb = new Pb2MsgPB();
            Map<String, Object> pbMap = pb.handle(jkNmsg);
            for (String key : esMap.keySet()) {
                pbMap.put(key, esMap.get(key));
            }
            pHbase.close();
            return pbMap;
        } catch (IOException e) {
            e.printStackTrace();
        }
        pHbase.close();
        return null;
    }

    public Map<String, Object> lmdbHandle(Map<String, Object> esMap) {

        LmdbHandle pLmdb = new LmdbHandle();
        try {
            byte[] b = pLmdb.handle(esMap);
            if (b == null) {
                return null;
            }
            ZMPNMsg.JKNmsg jkNmsg = ZMPNMsg.JKNmsg.parseFrom(b);
            Pb2MsgPB pb = new Pb2MsgPB();
            Map<String, Object> pbMap = pb.handle(jkNmsg);
            for (String key : esMap.keySet()) {
                pbMap.put(key, esMap.get(key));
            }
            //pLmdb.close();
            return pbMap;
        } catch (IOException e) {
            e.printStackTrace();
        }
        //pLmdb.close();
        return null;

    }

    String post(String url, Map<String, Object> paramMap, Map<String, Object> resBodyMap) {

        // 创建httpClient实例对象
        try {

            PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
            cm.setMaxTotal(100);
            cm.setDefaultMaxPerRoute(10);
            // org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
            CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();//设置数据传输的最长时间
            HttpPost httpPost = new HttpPost(url);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(1000)//设置创建连接的最长时间
                    .setConnectionRequestTimeout(500)//设置获取连接的最长时间
                    .setSocketTimeout(10 * 1000)//设置数据传输的最长时间
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置httpClient连接主机服务器超时时间：15000毫秒
            // CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();
            //httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(15000);
            // 创建post请求方法实例对象
            //PostMethod postMethod = new PostMethod(url);
            //postMethod.setRequestBody();
            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
            //httpPost.getMethod(new StringEntity(JSONObject.toJSONString(map)));
            StringEntity requestEntity = new StringEntity(JSONObject.toJSONString(resBodyMap), "utf-8");
            httpPost.setEntity(requestEntity);
            CloseableHttpResponse response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }

            String responseStr = EntityUtils.toString(entity);

            //System.out.println("接收到的响应信息:--------"+ EntityUtils.toString(entity,"UTF-8"));
            response.close();

            return responseStr;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    String post(String url, Map<String, Object> paramMap, List<String> resBodyMap) {

        // 创建httpClient实例对象
        try {

            PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
            cm.setMaxTotal(100);
            cm.setDefaultMaxPerRoute(10);
            // org.apache.commons.httpclient.HttpClient httpClient = new org.apache.commons.httpclient.HttpClient();
            CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();//设置数据传输的最长时间
            HttpPost httpPost = new HttpPost(url);

            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(1000)//设置创建连接的最长时间
                    .setConnectionRequestTimeout(500)//设置获取连接的最长时间
                    .setSocketTimeout(10 * 1000)//设置数据传输的最长时间
                    .build();
            httpPost.setConfig(requestConfig);

            // 设置httpClient连接主机服务器超时时间：15000毫秒
            // CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();
            //httpClient.getHttpConnectionManager().getParams().setConnectionTimeout(15000);
            // 创建post请求方法实例对象
            //PostMethod postMethod = new PostMethod(url);
            //postMethod.setRequestBody();
            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
            //httpPost.getMethod(new StringEntity(JSONObject.toJSONString(map)));
            StringEntity requestEntity = new StringEntity(JSONObject.toJSONString(resBodyMap), "utf-8");
            httpPost.setEntity(requestEntity);
            CloseableHttpResponse response = httpClient.execute(httpPost);

            HttpEntity entity = response.getEntity();
            if (entity == null) {
                return null;
            }

            String responseStr = EntityUtils.toString(entity);

            System.out.println("接收到的响应信息:--------" + responseStr);
            response.close();

            return responseStr;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public Map<String, Object> hLmdbHandle(Map<String, Object> esMap) {

        try {
            String hKey = esMap.get("Hkey").toString();
            List<String> klist = new ArrayList<>();
            klist.add(hKey);
            String postResult = post(lmdbUrl, null, klist);

            JSONObject m = JSONObject.parseObject(postResult);
            JSONObject mess = m.getJSONObject("message");
            String messageStr = mess.getString(hKey);
            if (StringUtils.isEmpty(messageStr)){
                return null;
            }
            Map<String, Object> userMap = new HashMap<>();
            for (Map.Entry<String, Object> entry : mess.entrySet()) {
                userMap.put(entry.getKey(), entry.getValue());
            }

            String pbbs64 = userMap.get(hKey).toString();

            byte[] b = Base64.getDecoder().decode(pbbs64);
            if (b == null) {
                return null;
            }
            ZMPNMsg.JKNmsg jkNmsg = ZMPNMsg.JKNmsg.parseFrom(b);
            Pb2MsgPB pb = new Pb2MsgPB();
            Map<String, Object> pbMap = pb.handle(jkNmsg);
            for (String key : esMap.keySet()) {
                pbMap.put(key, esMap.get(key));
            }
            return pbMap;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;


    }

    public void close() {

    }
}






package com.geeksec.util;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

public class TimeUtil {

    /**
     * 获取近几日的时间节点，从今天的24:00:00开始  单位秒
     * @param days
     * @param isIncludeToday 是否包含今日
     * @return
     */
    public static List<Integer> getZeroNode(Integer days,Boolean isIncludeToday){
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.HOUR_OF_DAY,0);
        if(isIncludeToday){
            //这里+1
            calendar.add(Calendar.DATE,1);
        }
        List<Integer> list = new ArrayList<>();
        list.add((int)(calendar.getTime().getTime()/1000));
        for (int i = 1; i < days; i++) {
            calendar.add(Calendar.DATE,-1);
            list.add((int)(calendar.getTime().getTime()/1000));
        }
        return list;
    }


    /**
     * 时间戳转字符串时间
     * @param value
     * @return
     */
    public static String getNormalTime(long value) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss") ;
        String time = format.format(new Date(value)) ;
        return time;
    }
}

package com.geeksec.entity.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel
@Data
public class PageResultVo<T> implements Serializable {

    @ApiModelProperty("数据集合")
    private List<T> records;
    @ApiModelProperty("总记录数")
    private long total = 0;

    @JsonProperty("es_limit_total")
    private long esLimitTotal;

    public long getEsLimitTotal() {
        if(esLimitTotal > 0)
            return esLimitTotal;
        if(total > 10000){
            //前端es分页列表仅 1W
            return 10000;
        }else{
            return total;
        }
    }

    public void setEsLimitTotal(long esLimitTotal) {
        this.esLimitTotal = esLimitTotal;
    }

    /**
     * @param list     分页前的集合
     * @param pageNum  页码
     * @param pageSize 页数
     * @param <T>
     * @return 分页后的集合
     */
    public static <T> List<T> pageList(List<T> list, int pageNum, int pageSize) {
        if(list == null || list.size() < 1){
            return list;
        }
        //计算总页数
        int page = list.size() % pageSize == 0 ? list.size() / pageSize : list.size() / pageSize + 1;
        //兼容性分页参数错误
        pageNum = pageNum <= 0 ? 1 : pageNum;
        pageNum = pageNum >= page ? page : pageNum;
        // 开始索引
        int begin = 0;
        // 结束索引
        int end = 0;
        if (pageNum != page) {
            begin = (pageNum - 1) * pageSize;
            end = begin + pageSize;
        } else {
            begin = (pageNum - 1) * pageSize;
            end = list.size();
        }
        return list.subList(begin, end);
    }

}

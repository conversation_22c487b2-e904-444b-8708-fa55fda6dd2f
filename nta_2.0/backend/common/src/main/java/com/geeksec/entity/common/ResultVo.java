package com.geeksec.entity.common;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.enumeration.GkErrorEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel
@Data
public class ResultVo<T> implements Serializable {

    @ApiModelProperty(value = "返回对象")
    private T data;

    @ApiModelProperty(value = "返回码值")
    private Integer err;

    @ApiModelProperty(value = "返回信息")
    private String msg;

    public ResultVo(){

    }

    public ResultVo(T data, Integer err, String msg){
        this.data = data;
        this.err = err;
        this.msg = msg;
    }

    public ResultVo(T data){
        this.data = data;
        this.err = 0;
        this.msg = "成功";
    }

    public ResultVo(Integer err, String msg){
        this.err = err;
        this.msg = msg;
    }

    /**
     * 返回空对象的suc
     * @param <T>
     * @return
     */
    public static <T> ResultVo<T> success(){
        return new ResultVo<>(0,"成功");
    }

    /**
     * 返回对象的suc
     * @param <T>
     * @return
     */
    public static <T> ResultVo<T> success(T data){
        return new ResultVo<>(data);
    }

    public static  ResultVo successEnum(GkErrorEnum errorEnum){
        return new ResultVo<>(errorEnum.getErr(),0,errorEnum.getMsg());
    }

    /**
     * 返回对象的suc
     * @param <T>
     * @return
     */
    public static <T> ResultVo<T> successMsg(String msg){
        return new ResultVo<>(0,msg);
    }

    public static <T> ResultVo<T> fail(GkErrorEnum errorEnum){
        return new ResultVo<>(Integer.valueOf(errorEnum.getErr()),errorEnum.getMsg());
    }

    /**
     * 失败信息
     * @param <T>
     * @return
     */
    public static <T> ResultVo<T> fail(Integer err, String msg){
        return (ResultVo<T>) new ResultVo<JSONObject>(new JSONObject(),err,msg);
    }

    /**
     * 失败信息
     * @param <T>
     * @return
     */
    public static <T> ResultVo<T> fail(String msg){
        return new ResultVo<>(40000,msg);
    }
}

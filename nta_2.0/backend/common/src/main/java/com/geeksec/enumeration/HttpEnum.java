package com.geeksec.enumeration;

/**
 * <AUTHOR>
 */

public enum HttpEnum {
    CONTINUE(100,"继续"),
    SUCCESS(200,"请求成功"),
    UNAUTHORIZED(401,"未授权"),
    FORBIDDEN(403,"禁止访问"),
    NOT_FOUND(404,"无法找到网页"),
    METHOD_NOT_ALLOWED(405,"方法不被允许"),
    CONFLICT(409,"请求冲突"),
    URL_TOO_LONG(414,"请求URL过长"),
    ERROR(500,"内部服务器错误");

    private int statusCode;
    private String statusMessage;

    private HttpEnum(int statusCode, String statusMessage) {
        this.statusCode = statusCode;
        this.statusMessage = statusMessage;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getStatusMessage() {
        return statusMessage;
    }

    public void setStatusMessage(String statusMessage) {
        this.statusMessage = statusMessage;
    }
}

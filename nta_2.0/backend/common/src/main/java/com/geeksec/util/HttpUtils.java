package com.geeksec.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;


/**
 * 创建HTTP连接，调用外部服务
 *
 * @author: GuanHao
 * @createTime: 2022/3/9 11:23
 * <p>
 * <Functions List>
 */
@Log4j2
public class HttpUtils {

    /**
     * 发送http请求，调用宿主服务，主要调用服务有：
     * 1.关机
     * 2.重启
     * 3.获取磁盘信息
     * 4.获取主机状态信息
     *
     * @param urlParam    请求地址
     * @param requestType 返回信息格式
     * @return data Json字符串
     * @throws IOException 获取数据异常时创建
     */
    public static String sendRequest(String urlParam, String requestType) throws IOException {
        //初始化
        HttpURLConnection con = null;
        BufferedReader buffer = null;
        StringBuffer resultBuffer = null;
        URL url = new URL(urlParam);

        //得到连接对象
        con = (HttpURLConnection) url.openConnection();
        //设置请求类型
        con.setRequestMethod(requestType);
        //设置请求需要返回的数据类型和字符集类型
        con.setRequestProperty("Content-Type", "application/json;charset=GBK");
        //允许写出
        con.setDoOutput(true);
        //允许读入
        con.setDoInput(true);
        //不使用缓存
        con.setUseCaches(false);
        //得到响应码
        int responseCode = con.getResponseCode();

        if (responseCode == HttpURLConnection.HTTP_OK) {
            //得到响应流
            InputStream inputStream = con.getInputStream();
            //将响应流转换成字符串
            resultBuffer = new StringBuffer();
            String line;
            buffer = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            while ((line = buffer.readLine()) != null) {
                resultBuffer.append(line);
            }
            return resultBuffer.toString();
        }

        return "";
    }

    public static String setGet(String urlParam) throws IOException {
        // 初始化
        HttpURLConnection con = null;
        BufferedReader buffer = null;
        StringBuffer resultBuffer = null;
        URL url = new URL(urlParam);

        // 得到连接对象
        con = (HttpURLConnection) url.openConnection();
        // 设置请求类型
        con.setRequestMethod("GET");
        // 设置请求需要返回的数据类型和字符集类型
        con.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        // 允许读入
        con.setDoInput(true);
        // 不使用缓存
        con.setUseCaches(false);
        // 得到响应码
        int responseCode = con.getResponseCode();

        if (responseCode == HttpURLConnection.HTTP_OK) {
            // 得到响应流
            InputStream inputStream = con.getInputStream();
            // 将响应流转换成字符串
            resultBuffer = new StringBuffer();
            String line;
            buffer = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            while ((line = buffer.readLine()) != null) {
                resultBuffer.append(line);
            }
            return resultBuffer.toString();
        }

        return "";
    }

    public static JSONObject sendPost(String url, String param) {
        log.info("http，POST开始，url={},param={}", url, param);
        long t1 = System.currentTimeMillis();
        RestTemplate template = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> request = new HttpEntity<>(param, headers);

        final ResponseEntity<String> responseEntity;
        try {
            responseEntity = template.postForEntity(url, request, String.class);
            final String body = responseEntity.getBody();
            final JSONObject responseBody = JSON.parseObject(body);
            long t2 = System.currentTimeMillis();
            //log.info("http，POST请求返回responseBody={},耗时毫秒time={}", responseBody, t2 - t1);
            final String statue = responseBody.get("status").toString();
            if ("true".equals(statue)) {
                return responseBody;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("http，POST请求异常，e={},url={},param={}", e.getMessage(), url, param);
        }
        return null;
    }

    public static String sendCurl(String url, String jsonStr) {

        String[] cmds = {"curl",
                "-H", "Content-Type:application/json",
                "-X", "POST", "-k", "-i", "" + url + "",
                "-d", "" + jsonStr + ""};   //超级注意：这个拼接json串的时候不要拼接双引号或者单引号，这个是踩坑提示，因为这个改了好久
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < cmds.length; i++) {
            sb.append(cmds[i] + " ");
        }
        String result = execCurl(cmds);
        return result;
    }

    private static String execCurl(String[] cmds) {
        ProcessBuilder process = new ProcessBuilder(cmds);
        Process p;
        try {
            p = process.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
                builder.append(System.getProperty("line.separator"));
            }
            return builder.toString();

        } catch (IOException e) {
            log.error("获取线程异常" + e);
        }
        return null;
    }

}

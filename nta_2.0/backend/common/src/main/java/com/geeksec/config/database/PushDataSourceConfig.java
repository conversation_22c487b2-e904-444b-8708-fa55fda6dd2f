package com.geeksec.config.database;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：websocket推送所用数据源配置(push_datasource)
 */
//@Configuration
//@MapperScan(basePackages = {"com.geeksec.push.dao.normal"},sqlSessionFactoryRef = "PushSqlSessionFactory")
public class PushDataSourceConfig {

    @Bean(name = "PushDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.push-db")
    public DataSource getPushDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "PushSqlSessionFactory")
    public SqlSessionFactory PushSqlSessionFactory(@Qualifier("PushDataSource") DataSource datasource)
            throws Exception {
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setJdbcTypeForNull(JdbcType.NULL);
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCacheEnabled(false);
        // 配置打印SQL语句
//        configuration.setLogImpl(StdOutImpl.class);

        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(datasource);
        bean.setConfiguration(configuration);

        List<Resource> resourceList = new ArrayList<Resource>();
        resourceList.addAll(Arrays.asList(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/pushNormal/*.xml")));

        Resource[] resources = resourceList.toArray(new Resource[]{});
        bean.setMapperLocations(resources);
        return bean.getObject();// 设置mybatis的xml所在位置
    }

    @Bean("PushSqlSessionTemplate")
    public SqlSessionTemplate PushSqlSessionTemplate(@Qualifier("PushSqlSessionFactory") SqlSessionFactory sqlSessionFactory){
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}

package com.geeksec.util;

import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Date;

@Slf4j
public class FileUtil {

    /**
     * 文件上传
     *
     * @param file
     * @return
     * @throws Exception
     */
    public static ResultVo fileUpload(String filePath, MultipartFile file) {
        if (StringUtils.isEmpty(filePath)) {
            throw new GkException(GkErrorEnum.FILE_UPLOAD_PATH_EMPTY);
        }
        try {
            if (file.isEmpty()) {
                throw new GkException(GkErrorEnum.UPLOAD_FILE_EMPTY);
            }
            log.info(file.getOriginalFilename());

            // 判断上传文件大小
//            if (file.getSize() >fileSize) {
//                log.error("上传文件规定小于50MB");
//                throw new Exception("上传文件大于50MB");
//            }
            // 获取文件名
            String fileName = file.getOriginalFilename();
            // 获取文件的后缀名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));

            String newName = new Date().getTime() + suffixName;

            // 设置文件存储路径
            String path = filePath + newName;
            File dest = new File(path);
            // 检测是否存在目录,不存在则创建
            if (!dest.getParentFile().exists()) {
                dest.getParentFile().mkdirs();
            }
            // 文件写入
            file.transferTo(dest);
            return ResultVo.success(newName);
        } catch (Exception e) {
            log.error("上传失败： <{}>", e.getMessage(), e);
            throw new GkException(GkErrorEnum.UPLOAD_FILE_FAIL);
        }
    }

    /**
     * csv文件下载
     *
     * @param response
     * @param file
     * @throws Exception
     */
    public static void csvDownloadFile(HttpServletResponse response, File file) {
        if (file.exists()) {
            OutputStream toClient = null;
            try {
                // 取得文件名。
                String filename = file.getName();
                // 以流的形式下载文件。
                InputStream fis = new BufferedInputStream(new FileInputStream(file));
                // 清空response
                response.reset();
                // 设置response的Header,给文件名进行utf-8编码,不然下载的时候文件名乱码不对
                response.addHeader("Content-Disposition", URLEncoder.encode(filename, "UTF-8"));
                response.setCharacterEncoding("UTF-8");
                response.addHeader("Content-Length", "" + file.length());
                //response.setContentType("blob");
                //response.setContentType("application/x-download;charset=UTF-8");
                response.setHeader("Access-Control-Allow-Origin", "*");
                response.setHeader("Access-Control-Expose-Headers", "*");
                toClient = new BufferedOutputStream(
                        response.getOutputStream());
                byte[] b = new byte[1024];
                int len;
                while ((len = fis.read(b)) != -1) {
                    toClient.write(b, 0, len);
                }
                log.info("文件下载成功，filename={}", filename);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("下载文件失败,e={}", e);
            } finally {
                if (toClient != null) {
                    try {
                        toClient.flush();
                        toClient.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                        log.error("下载文件关闭流失败,e={}", e);
                    }
                }
            }
        }
    }

    /**
     * 文件删除
     *
     * @param path
     * @return
     * @throws Exception
     */
    public static void fileDelete(String path) {
        log.info("删除文件开始,path={}", path);
        if (StringUtils.isEmpty(path)) {
            return;
        }
        File file = new File(path);
        if (!file.exists()) {
            log.error("文件不存在");
        }
        try {
            if (file.delete()) {
                log.info("删除文件成功,fileName={}", path);
                return;
            }
        } catch (Exception e) {
            log.error("文件删除异常:e={} ", e.getMessage());
        }
        log.error("文件删除失败,path={}", path);
    }

}


package com.geeksec.nebula;

public class Order extends VLBase {
    private String sField = null;
    private boolean desc = true;

    Order(String tableName, String field, Boolean dDesc) {
        name = tableName;
        sField = field;
        type = "O";
        desc = dDesc;
    }

    public Order(String field, Boolean dDesc) {
        name = null;
        sField = field;
        type = "O";
        desc = dDesc;
    }

    public SqlParse parse(SqlParse sqlParse) {
        String sql = "";

        if (name != null) {
            sqlParse.sql += " " + name + "." + sField + " ";
        } else {
            sqlParse.sql += " " + sField + " ";
        }
        if (desc) {
            sqlParse.sql += "DESC ";
        } else {
            sqlParse.sql += "ASC ";
        }
        return sqlParse;
    }
}
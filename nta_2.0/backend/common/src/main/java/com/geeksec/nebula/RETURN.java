package com.geeksec.nebula;

public class RETURN extends VLBase {
    public RETURN(String returnSQL) {
        name = returnSQL;
    }

    public SqlParse parse(SqlParse sqlParse) {
        //String alname = PSql.GetAlias(Name);
        //System.out.ut.println("RETURN  ========= Parse");
        String alname = name;
        if (alname == null) {
            return null;
        } else {
            sqlParse.sql += " " + alname;
        }
        return sqlParse;
    }


}

package com.geeksec.exception;

/**
 * <AUTHOR>
 * @Description：
 */
public class SzwRunTimeException extends RuntimeException{

    /** **/
    private static final long serialVersionUID = 8924997566380919394L;

    public SzwRunTimeException() {
        super();
    }

    public SzwRunTimeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public SzwRunTimeException(String message, Throwable cause) {
        super(message, cause);
    }

    public SzwRunTimeException(String message) {
        super(message);
    }

    public SzwRunTimeException(Throwable cause) {
        super(cause);
    }

}

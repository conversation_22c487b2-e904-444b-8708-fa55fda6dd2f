package com.geeksec.enumeration;

/**
 * <AUTHOR>
 * @Description：
 * 标签目标类型(0 ip目标，1 端口目标，2 应用目标，3 域名目标，4 证书目标，5 MAC目标，6 连接目标(会话) , 7 指纹 9999 所有)
 */

public enum TagTargetEnum {

    IP(0,"IP"),
    PORT(1,"端口"),
    APP(2,"应用服务"),
    DOMAIN(3,"域名"),
    CERT(4,"证书"),
    MAC(5,"MAC"),
    SESSION(6,"会话"),
    FINGERPRINT(7,"指纹"),
    ALL(9999,"全部");


    TagTargetEnum(Integer tagTargetNum, String tarTargetName) {
        this.tagTargetNum = tagTargetNum;
        this.tarTargetName = tarTargetName;
    }

    private Integer tagTargetNum;

    private String tarTargetName;

    // 判断tagTargetNum,如果相同返回其对应的tarTargetName
    public static String getTarTargetNameByTagTargetNum(Integer tagTargetNum){
        for(TagTargetEnum tagTargetEnum : TagTargetEnum.values()){
            if(tagTargetEnum.getTagTargetNum().equals(tagTargetNum)){
                return tagTargetEnum.getTarTargetName();
            }
        }
        return null;
    }
    public static Integer getTarTargetNumByTagTargetName(String tagTargetName){
        for(TagTargetEnum tagTargetEnum : TagTargetEnum.values()){
            if(tagTargetEnum.getTarTargetName().equals(tagTargetName)){
                return tagTargetEnum.getTagTargetNum();
            }
        }
        return null;
    }

    public Integer getTagTargetNum() {
        return tagTargetNum;
    }

    public void setTagTargetNum(Integer tagTargetNum) {
        this.tagTargetNum = tagTargetNum;
    }

    public String getTarTargetName() {
        return tarTargetName;
    }

    public void setTarTargetName(String tarTargetName) {
        this.tarTargetName = tarTargetName;
    }

}

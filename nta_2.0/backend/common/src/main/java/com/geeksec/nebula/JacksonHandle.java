package com.geeksec.nebula;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Arrays;
import java.util.List;

// JacksonH 处理封装
public class JacksonHandle {
    private static JacksonHandle instance = null;

    private JacksonHandle() {
    }

    public static synchronized JacksonHandle getInstance() {
        if (instance == null) {
            instance = new JacksonHandle();
        }
        return instance;
    }

    public JsonNode newEmtpyJson() {
        try {
            String str = "{}";
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readTree(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 字符串 转 json 对象
    public JsonNode jsonParse(String str) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(str);
            return root;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public JsonNode getObjValue(JsonNode obj, String path) {
        try {
            List<String> result = Arrays.asList(path.split("/"));
            JsonNode objL = obj;
            for (int i = 0; i < result.size(); i++) {
                // 判断有没有符号
                String filed = result.get(i);
                int nt1 = filed.indexOf("[");
                int nt2 = filed.indexOf("]");
                if (nt1 != -1 && nt2 != -1) {
                    String sArrFiled = filed.substring(0, nt1 - 1);
                    int num = Integer.parseInt(filed.substring(nt1 + 1, nt2 - 1));
                    objL = objL.get(filed).get(num);
                } else {
                    if (objL == null) {
                        return null;
                    }
                    objL = objL.get(filed);
                }
            }
            return objL;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 设置值 JsonNode
    public void setValue(JsonNode obj, String path, String key, JsonNode value) {
        ObjectNode objL = (ObjectNode) getObjValue(obj, path);
        objL.put(key, objL.toString());
    }

    public void setValue(JsonNode obj, String key, JsonNode value) {
        ObjectNode objL = (ObjectNode) obj;
        objL.put(key, value);
    }

    // 定稿
    public void setValue(JsonNode obj, String key, String value) {
        ObjectNode objL = (ObjectNode) obj;
        objL.put(key, value);
    }

    // 定稿
    public void setValue(JsonNode obj, String path, String key, String value) {
        ObjectNode objL = (ObjectNode) getObjValue(obj, path);
        objL.put(key, value);
    }

    // 定稿
    public void setValue(JsonNode obj, String key, boolean value) {
        ObjectNode objL = (ObjectNode) obj;
        objL.put(key, value);
    }

    public void setValue(JsonNode obj, String key, int value) {
        ObjectNode objL = (ObjectNode) obj;
        objL.put(key, value);
    }

    public void setValue(JsonNode obj, String key, float value) {
        ObjectNode objL = (ObjectNode) obj;
        objL.put(key, value);
    }

    public String GetValue(JsonNode obj, String path) {
        JsonNode objL = JacksonHandle.getInstance().getObjValue(obj, path);
        if (objL == null) {
            //System.out.ut.println("obj ==== " + obj.toString());
            //System.out.ut.println("Path ==== " + Path);
            //System.out.ut.println(Path +"配置错误 !");
            //System.out.ut.println("错误定位  : "+obj_L.toString());
            System.exit(1);
        }
        if (objL.isTextual()) {
            //System.out.ut.println("STRING  取值  : "+obj_L.toString());
            return objL.textValue();
        }
        if (objL.isInt()) {
            return String.valueOf(objL.asInt());
        } else {
            //System.out.ut.println("取值  : "+obj_L.toString());
            return objL.toString();
        }
    }
}

/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 * <p>
 * https://www.renren.io
 * <p>
 * 版权所有，侵权必究！
 */

package com.geeksec.exception;

import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 *@description: 异常处理器
 *@author: shiwenxu
 *@createtime: 2023/8/30 10:01
 **/
@RestControllerAdvice
@ResponseBody
public class GkExceptionHandler {
    private final Logger logger = LoggerFactory.getLogger(GkExceptionHandler.class);

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(GkException.class)
    public ResultVo<Object> handleGkException(GkException e) {
        logger.error("发生异常:", e);
        return ResultVo.fail(e.getErr(), e.getMessage());
    }

    /**
     * 处理通用异常
     *
     * @param e
     */
    @ExceptionHandler(Exception.class)
    public ResultVo<Object> handleException(Exception e) {
        logger.error("系统异常:", e);
        return ResultVo.fail(GkErrorEnum.FAIL.getErr(), GkErrorEnum.FAIL.getMsg());
    }
}

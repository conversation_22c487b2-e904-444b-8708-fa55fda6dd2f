package com.PB2Msg.test;


import com.geeksec.entity.common.ZMPNMsg;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;

public class Pb2MsgPB {
    private Map<String, String> clientMap = new HashMap<String, String>();
    private Map<String, String> serverMap = new HashMap<String, String>();
    public Map<String, Object> JKNmsgMap = new HashMap<>();
    public Map<Integer, List<String>> filterMap = new HashedMap();

    public List<String> domainIPFen(String domainIP) {
        String[] domainT = StringUtils.split(domainIP, "|");
        //DomainIP.split("|");
        return Arrays.asList(domainT);
    }

    public Long int2Uint32(Integer num) {

        Long result = Integer.toUnsignedLong(num);
        return result;

    }

    public Long long2Ulong(Long num) {
        if (num < 0) {
            String numStr = Long.toUnsignedString(num);
            Long reusltNum = Long.valueOf(numStr);
            return reusltNum;
        } else return num;
    }


    public Map<String, Object> handle(ZMPNMsg.JKNmsg msg) {
        switch (msg.getType()) {
            case 30:
                return handleAddMsg(msg, msg3009Handle(msg), 3009);
            case 4:
                return handleAddMsg(msg, msg148Handle(msg), 148);
            case 80:
                return handleAddMsg(msg, msg3008Handle(msg), 3008);
            case 29:
                return handleAddMsg(msg, msg145Handle(msg), 145);
            case 28:
                return handleAddMsg(msg, msg157Handle(msg), 157);

        }
        return null;
    }

    public Pb2MsgPB() {
        List<String> connFilterList = new ArrayList<>();
        connFilterList.add("HandleBeginTime");
        connFilterList.add("HandleEndTime");
        connFilterList.add("Hkey");
        filterMap.put(3009, connFilterList);

        List<String> dnsFilterList = new ArrayList<>();
        dnsFilterList.add("Hkey");
        filterMap.put(145, dnsFilterList);

        List<String> httpFilterList = new ArrayList<>();
        httpFilterList.add("Hkey");
        filterMap.put(3008, httpFilterList);

        List<String> sslFilterList = new ArrayList<>();
        sslFilterList.add("Hkey");
        filterMap.put(145, sslFilterList);

        List<String> sshFilterList = new ArrayList<>();
        sshFilterList.add("Hkey");
        filterMap.put(157, sshFilterList);
    }

    public Map<String, Object> filterMapHandle(Map<String, Object> msgMap, int type) {

        List<String> filterList = filterMap.get(type);
        if (filterList != null) {
            for (String t : filterList) {
                msgMap.remove(t);
            }
        }
        return msgMap;
    }

    public Map<String, Object> handleAddMsg(ZMPNMsg.JKNmsg pbMsg, Map<String, Object> msgMap, int type) {

        switch (type) {
            case 3009:

                break;
            case 148:

                break;
            case 3008:

            case 145:

                break;
            default:

                //Msg.put("es_key",String.valueOf(Msg.get("TaskId")) + "_"+ String.valueOf(Msg.get("BatchNum")));
                break;

        }

        return filterMapHandle(msgMap, type);
    }

    public Map<String, Object> jsonToMap(String sValue) {
        System.out.println(sValue);
        if (sValue == null || sValue.contains("")) {
            JSONObject jo = new JSONObject("[]");
            return jo.toMap();
        } else {
            JSONObject jo = new JSONObject(sValue);
            return jo.toMap();
        }
    }

    public List<Object> jsonArrayToMap(String sValue) {
        System.out.println(sValue);

        if (sValue == null || sValue.length() == 0) {
            JSONArray jo = new JSONArray("[]");
            return jo.toList();

        } else {


            JSONArray jo = new JSONArray(sValue);
            return jo.toList();
        }
    }

    public String intToString(Integer num) {
        return String.valueOf(num);
    }

    public String longToString(Long num) {
        return String.valueOf(num);
    }


    public Map<String, Object> msg3009Handle(ZMPNMsg.JKNmsg msg) {
        List<Integer> ruleLabelsList = msg.getSingleSession().getRuleLabelsList();
        List<Object> ruleLabelsListValues = new ArrayList<>();
        for (Integer tmp : ruleLabelsList) {

            ruleLabelsListValues.add(tmp);
        }
        List<Integer> portListList = msg.getSingleSession().getPortListList();
        List<Object> portListListValues = new ArrayList<>();
        for (Integer tmp : portListList) {

            portListListValues.add(tmp);
        }
        List<Integer> blockCipherList = msg.getSingleSession().getSsStats().getBlockCipherList();
        List<Object> blockCipherListValues = new ArrayList<>();
        for (Integer tmp : blockCipherList) {

            blockCipherListValues.add(tmp);
        }
        List<ZMPNMsg.packet_info_msg> pktInforList = msg.getSingleSession().getSsPkt().getPktInforList();
        List<Object> pktInforListValues = new ArrayList<>();
        for (ZMPNMsg.packet_info_msg tmp : pktInforList) {
            Map<String, Object> pkt_inforMap = new HashMap<>();
            pkt_inforMap.put("Count", int2Uint32(tmp.getCount()));
            pkt_inforMap.put("Len", tmp.getLen());
            pkt_inforMap.put("Sec", int2Uint32(tmp.getSec()));
            pkt_inforMap.put("nSec", int2Uint32(tmp.getNsec()));

            pktInforListValues.add(pkt_inforMap);
        }
        List<ZMPNMsg.md_tcp_msg> statsTcpInfoList = msg.getSingleSession().getSsStats().getStatsTcpInfoList();
        List<Object> statsTcpInfoListValues = new ArrayList<>();
        for (ZMPNMsg.md_tcp_msg tmp : statsTcpInfoList) {
            Map<String, Object> stats_tcp_infoMap = new HashMap<>();
            stats_tcp_infoMap.put("Bytes", tmp.getBytes());
            stats_tcp_infoMap.put("PacketNum", int2Uint32(tmp.getPacketNum()));
            stats_tcp_infoMap.put("PSHNum", int2Uint32(tmp.getPshNum()));
            stats_tcp_infoMap.put("ACK", int2Uint32(tmp.getAcknowledgement()));
            stats_tcp_infoMap.put("MinSeq", int2Uint32(tmp.getMinSequence()));
            stats_tcp_infoMap.put("MaxSeq", int2Uint32(tmp.getMaxSequence()));

            statsTcpInfoListValues.add(stats_tcp_infoMap);
        }
        List<Integer> statsDistdurList = msg.getSingleSession().getSsStats().getStatsDistdurList();
        List<Object> statsDistdurListValues = new ArrayList<>();
        for (Integer tmp : statsDistdurList) {

            statsDistdurListValues.add(tmp);
        }
        List<Integer> synSeqList = msg.getSingleSession().getSsStats().getSynSeqList();
        List<Object> synSeqListValues = new ArrayList<>();
        for (Integer tmp : synSeqList) {

            synSeqListValues.add(tmp);
        }
        List<Integer> statsSipidOffsetList = msg.getSingleSession().getSsStats().getStatsSipidOffsetList();
        List<Object> statsSipidOffsetListValues = new ArrayList<>();
        for (Integer tmp : statsSipidOffsetList) {

            statsSipidOffsetListValues.add(tmp);
        }
        List<Integer> statsDipidOffsetList = msg.getSingleSession().getSsStats().getStatsDipidOffsetList();
        List<Object> statsDipidOffsetListValues = new ArrayList<>();
        for (Integer tmp : statsDipidOffsetList) {

            statsDipidOffsetListValues.add(tmp);
        }
        List<Integer> statsSdistlenList = msg.getSingleSession().getSsStats().getStatsSdistlenList();
        List<Object> statsSdistlenListValues = new ArrayList<>();
        for (Integer tmp : statsSdistlenList) {

            statsSdistlenListValues.add(tmp);
        }
        List<Integer> statsDdistlenList = msg.getSingleSession().getSsStats().getStatsDdistlenList();
        List<Object> statsDdistlenListValues = new ArrayList<>();
        for (Integer tmp : statsDdistlenList) {

            statsDdistlenListValues.add(tmp);
        }
        List<ZMPNMsg.single_http> httpList = msg.getSingleSession().getHttpList();
        List<Object> httpListValues = new ArrayList<>();
        for (ZMPNMsg.single_http tmp : httpList) {
            Map<String, Object> httpMap = new HashMap<>();
            httpMap.put("Url", tmp.getUrl());
            httpMap.put("Act", tmp.getAct());
            httpMap.put("Host", tmp.getHost());
            httpMap.put("User-Agent", tmp.getUserAgent());
            httpMap.put("Response", tmp.getResponse());

            httpListValues.add(httpMap);
        }
        List<ZMPNMsg.single_dns> dnsList = msg.getSingleSession().getDnsList();
        List<Object> dnsListValues = new ArrayList<>();
        for (ZMPNMsg.single_dns tmp : dnsList) {
            Map<String, Object> dnsMap = new HashMap<>();
            dnsMap.put("Domain", tmp.getDomain());
            dnsMap.put("DomainIp", domainIPFen(tmp.getDomainIp()));

            dnsListValues.add(dnsMap);
        }
        List<ZMPNMsg.single_ssl> sslList = msg.getSingleSession().getSslList();
        List<Object> sslListValues = new ArrayList<>();
        for (ZMPNMsg.single_ssl tmp : sslList) {
            Map<String, Object> sslMap = new HashMap<>();
            sslMap.put("CH_Ciphersuit", tmp.getChCiphersuit());
            sslMap.put("CH_CiphersuitNum", tmp.getChCiphersuitNum());
            sslMap.put("CH_ServerName", tmp.getChServerName());
            sslMap.put("CH_ALPN", jsonArrayToMap(tmp.getChAlpn()));
            sslMap.put("sCertHash", jsonArrayToMap(tmp.getCCert()));
            sslMap.put("dCertHash", jsonArrayToMap(tmp.getSCert()));

            sslListValues.add(sslMap);
        }
        JKNmsgMap.put("HandleBeginTime", int2Uint32(msg.getSingleSession().getHandleBeginTime()));
        JKNmsgMap.put("HandleEndTime", int2Uint32(msg.getSingleSession().getHandleEndTime()));
        JKNmsgMap.put("sIp", msg.getSingleSession().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", int2Uint32(msg.getSingleSession().getCommMsg().getSrcPort()));
        JKNmsgMap.put("dIp", msg.getSingleSession().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", int2Uint32(msg.getSingleSession().getCommMsg().getDstPort()));
        JKNmsgMap.put("IPPro", int2Uint32(msg.getSingleSession().getCommMsg().getIppro()));
        JKNmsgMap.put("Labels", ruleLabelsListValues);
        JKNmsgMap.put("ProxyIPInfor", int2Uint32(msg.getSingleSession().getProxyType()));
        JKNmsgMap.put("FirstSender", msg.getSingleSession().getFirstSender());
        JKNmsgMap.put("SessionId", msg.getSingleSession().getCommMsg().getSessionId());
        JKNmsgMap.put("AppId", int2Uint32(msg.getSingleSession().getCommMsg().getAppId()));
        JKNmsgMap.put("AppName", msg.getSingleSession().getCommMsg().getAppName());
        JKNmsgMap.put("DeviceID", int2Uint32(msg.getSingleSession().getDeviceId()));
        JKNmsgMap.put("EthPortID", portListListValues);
        JKNmsgMap.put("TaskId", int2Uint32(msg.getSingleSession().getCommMsg().getTaskId()));
        JKNmsgMap.put("BatchNum", int2Uint32(msg.getSingleSession().getCommMsg().getBatchId()));
        JKNmsgMap.put("ThreadId", int2Uint32(msg.getSingleSession().getCommMsg().getThreadId()));
        JKNmsgMap.put("Duration", int2Uint32(msg.getSingleSession().getDuration()));
        JKNmsgMap.put("StartTime", int2Uint32(msg.getSingleSession().getCommMsg().getBeginTime()));
        JKNmsgMap.put("StartNSec", int2Uint32(msg.getSingleSession().getCommMsg().getBeginNsec()));
        JKNmsgMap.put("EndTime", int2Uint32(msg.getSingleSession().getEndTime()));
        JKNmsgMap.put("EndNSec", int2Uint32(msg.getSingleSession().getEndNsec()));
        JKNmsgMap.put("FirstProto", int2Uint32(msg.getSingleSession().getFirstProto()));
        JKNmsgMap.put("PktLenDist", blockCipherListValues);
        Map<String, Object> ssPktMap = new HashMap<>();

        ssPktMap.put("ProNum", msg.getSingleSession().getSsPkt().getPktPronum());
        ssPktMap.put("UnkProNum", msg.getSingleSession().getSsPkt().getPktUnkonwPronum());
        ssPktMap.put("sPayload", msg.getSingleSession().getSsPkt().getPktSpayloadList());
        ssPktMap.put("dPayload", msg.getSingleSession().getSsPkt().getPktDpayloadList());
        ssPktMap.put("Infor", pktInforListValues);
        ssPktMap.put("APPCount", msg.getSingleSession().getSsPkt().getAppPktId());
        ssPktMap.put("sNum", msg.getSingleSession().getSsPkt().getPktSnum());
        ssPktMap.put("sPayloadNum", msg.getSingleSession().getSsPkt().getPktSpayloadnum());
        ssPktMap.put("sBytes", msg.getSingleSession().getSsPkt().getPktSbytes());
        ssPktMap.put("sPayloadBytes", msg.getSingleSession().getSsPkt().getPktSpayloadbytes());
        ssPktMap.put("dNum", msg.getSingleSession().getSsPkt().getPktDnum());
        ssPktMap.put("dPayloadNum", msg.getSingleSession().getSsPkt().getPktDpayloadnum());
        ssPktMap.put("dBytes", msg.getSingleSession().getSsPkt().getPktDbytes());
        ssPktMap.put("dPayloadBytes", msg.getSingleSession().getSsPkt().getPktDpayloadbytes());
        ssPktMap.put("sMaxLen", msg.getSingleSession().getSsPkt().getPktSmaxlen());
        ssPktMap.put("sPSHNum", msg.getSingleSession().getSsPkt().getPktSpshnum());
        ssPktMap.put("sFINNum", msg.getSingleSession().getSsPkt().getPktSfinnum());
        ssPktMap.put("sRSTNum", msg.getSingleSession().getSsPkt().getPktSrstnum());
        ssPktMap.put("sSYNNum", msg.getSingleSession().getSsPkt().getPktSsynnum());
        ssPktMap.put("sSYNBytes", msg.getSingleSession().getSsPkt().getPktSsynbytes());
        ssPktMap.put("sTTLMax", msg.getSingleSession().getSsPkt().getPktSttlmax());
        ssPktMap.put("sTTLMin", msg.getSingleSession().getSsPkt().getPktSttlmin());
        ssPktMap.put("dMaxLen", msg.getSingleSession().getSsPkt().getPktDmaxlen());
        ssPktMap.put("dFinNum", msg.getSingleSession().getSsPkt().getPktDfinnum());
        ssPktMap.put("dRSTNum", msg.getSingleSession().getSsPkt().getPktDrstnum());
        ssPktMap.put("dSYNNum", msg.getSingleSession().getSsPkt().getPktDsynnum());
        ssPktMap.put("dSYNBytes", msg.getSingleSession().getSsPkt().getPktDsynbytes());
        ssPktMap.put("dPSHNum", msg.getSingleSession().getSsPkt().getPktDpshnum());


        // 添加ProList字段到全量会话数据中
        JKNmsgMap.put("ProListNum", msg.getSingleSession().getSsStats().getStatsProlistNum());
        String proListString = msg.getSingleSession().getSsStats().getStatsProlist();
        com.alibaba.fastjson.JSONObject ProStackJson = com.alibaba.fastjson.JSONObject.parseObject(proListString);
        JKNmsgMap.put("ProList", ProStackJson.getInnerMap());

        JKNmsgMap.put("pkt", ssPktMap);
        JKNmsgMap.put("TCPGather", statsTcpInfoListValues);
        JKNmsgMap.put("SbytesDiviDbytes", int2Uint32(msg.getSingleSession().getSsPkt().getPktDnum()));
        JKNmsgMap.put("PktIntervalTimeDist", statsDistdurListValues);
        JKNmsgMap.put("SynSeqList", synSeqListValues);
        JKNmsgMap.put("SynNum", int2Uint32(msg.getSingleSession().getSsStats().getSynSeqNum()));
        JKNmsgMap.put("sMac", msg.getSingleSession().getSsBasic().getSmac());
        JKNmsgMap.put("sSSLFinger", longToString(msg.getSingleSession().getSslCFinger()));
        JKNmsgMap.put("sIpIdOffset", statsSipidOffsetListValues);
        JKNmsgMap.put("dMac", msg.getSingleSession().getSsBasic().getDmac());
        JKNmsgMap.put("dSSLFinger", longToString(msg.getSingleSession().getSslSFinger()));
        JKNmsgMap.put("dIpIdOffset", statsDipidOffsetListValues);
        JKNmsgMap.put("sDistLen", statsSdistlenListValues);
        JKNmsgMap.put("dDistLen", statsDdistlenListValues);
        JKNmsgMap.put("HTTP", httpListValues);
        JKNmsgMap.put("DNS", dnsListValues);
        JKNmsgMap.put("SSL", sslListValues);

        return JKNmsgMap;
    }

    public Map<String, Object> msg148Handle(ZMPNMsg.JKNmsg msg) {


        JKNmsgMap.put("sIp", msg.getDns().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", int2Uint32(msg.getDns().getCommMsg().getSrcPort()));
        JKNmsgMap.put("dIp", msg.getDns().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", int2Uint32(msg.getDns().getCommMsg().getDstPort()));
        JKNmsgMap.put("SessionId", msg.getDns().getCommMsg().getSessionId());
        JKNmsgMap.put("AppName", msg.getDns().getCommMsg().getAppName());
        JKNmsgMap.put("TaskId", int2Uint32(msg.getDns().getCommMsg().getTaskId()));
        JKNmsgMap.put("StartTime", int2Uint32(msg.getDns().getCommMsg().getBeginTime()));
        JKNmsgMap.put("StartNSec", int2Uint32(msg.getDns().getCommMsg().getBeginNsec()));
        JKNmsgMap.put("Flags", int2Uint32(msg.getDns().getDnsFlags()));
        JKNmsgMap.put("Que", int2Uint32(msg.getDns().getDnsQue()));
        JKNmsgMap.put("Ans", int2Uint32(msg.getDns().getDnsAns()));
        JKNmsgMap.put("Auth", int2Uint32(msg.getDns().getDnsAuth()));
        JKNmsgMap.put("Add", int2Uint32(msg.getDns().getDnsAdd()));
        JKNmsgMap.put("Query", jsonArrayToMap(msg.getDns().getDnsQuery()));
        JKNmsgMap.put("Answer", jsonArrayToMap(msg.getDns().getDnsAnswer()));
        JKNmsgMap.put("Domain", msg.getDns().getDnsDomain());
        JKNmsgMap.put("DomainIp", domainIPFen(msg.getDns().getDnsDomainIp()));


        return JKNmsgMap;
    }

    public Map<String, Object> msg3008Handle(ZMPNMsg.JKNmsg msg) {


        JKNmsgMap.put("sIp", msg.getHttpHeader().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", int2Uint32(msg.getHttpHeader().getCommMsg().getSrcPort()));
        JKNmsgMap.put("dIp", msg.getHttpHeader().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", int2Uint32(msg.getHttpHeader().getCommMsg().getDstPort()));
        JKNmsgMap.put("ServerIP", msg.getHttpHeader().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getHttpHeader().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", int2Uint32(msg.getHttpHeader().getCommMsg().getTaskId()));
        JKNmsgMap.put("BatchNum", int2Uint32(msg.getHttpHeader().getCommMsg().getBatchId()));
        JKNmsgMap.put("StartTime", int2Uint32(msg.getHttpHeader().getCommMsg().getBeginTime()));
        JKNmsgMap.put("StartNSec", int2Uint32(msg.getHttpHeader().getCommMsg().getBeginNsec()));
        JKNmsgMap.put("Url", msg.getHttpHeader().getUrl());
        JKNmsgMap.put("Act", msg.getHttpHeader().getAct());
        JKNmsgMap.put("Host", msg.getHttpHeader().getHost());
        JKNmsgMap.put("Response", msg.getHttpHeader().getResponse());


        return JKNmsgMap;
    }

    public Map<String, Object> msg145Handle(ZMPNMsg.JKNmsg msg) {


        JKNmsgMap.put("sIp", msg.getSsl().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", int2Uint32(msg.getSsl().getCommMsg().getSrcPort()));
        JKNmsgMap.put("dIp", msg.getSsl().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", int2Uint32(msg.getSsl().getCommMsg().getDstPort()));
        JKNmsgMap.put("SessionId", msg.getSsl().getCommMsg().getSessionId());
        JKNmsgMap.put("AppName", msg.getSsl().getCommMsg().getAppName());
        JKNmsgMap.put("TaskId", int2Uint32(msg.getSsl().getCommMsg().getTaskId()));
        JKNmsgMap.put("BatchNum", int2Uint32(msg.getSsl().getCommMsg().getBatchId()));
        JKNmsgMap.put("StartTime", int2Uint32(msg.getSsl().getCommMsg().getBeginTime()));
        JKNmsgMap.put("StartNSec", int2Uint32(msg.getSsl().getCommMsg().getBeginNsec()));
        JKNmsgMap.put("cSSLVersion", int2Uint32(msg.getSsl().getSslCVersion()));
        JKNmsgMap.put("CH_Version", int2Uint32(msg.getSsl().getSslHelloCVersion()));
        JKNmsgMap.put("CH_Time", int2Uint32(msg.getSsl().getSslHelloCTime()));
        JKNmsgMap.put("CH_Random", msg.getSsl().getSslHelloCRandom());
        JKNmsgMap.put("CH_SessionID", msg.getSsl().getSslHelloCSessionid());
        JKNmsgMap.put("CH_SessionIDLen", int2Uint32(msg.getSsl().getSslHelloCSessionidlen()));
        JKNmsgMap.put("CH_Ciphersuit", msg.getSsl().getSslHelloCCiphersuit());
        JKNmsgMap.put("CH_CiphersuitNum", int2Uint32(msg.getSsl().getSslHelloCCiphersuitnum()));
        JKNmsgMap.put("CH_CompressionMethod", msg.getSsl().getSslHelloCCompressionmethod());
        JKNmsgMap.put("CH_CompressionMethodLen", int2Uint32(msg.getSsl().getSslHelloCCompressionmethodlen()));
        JKNmsgMap.put("CH_ExtentionNum", int2Uint32(msg.getSsl().getSslHelloCExtentionnum()));
        JKNmsgMap.put("CH_Extention", jsonArrayToMap(msg.getSsl().getSslHelloCExtention()));
        JKNmsgMap.put("CH_ServerName", msg.getSsl().getSslHelloCServername());
        JKNmsgMap.put("CH_ServerNameType", int2Uint32(msg.getSsl().getSslHelloCServernametype()));
        JKNmsgMap.put("CH_SessionTicket", msg.getSsl().getSslHelloCSessionticket());
        JKNmsgMap.put("CH_ALPN", jsonArrayToMap(msg.getSsl().getSslHelloCAlpn()));
        JKNmsgMap.put("sCertHash", jsonArrayToMap(msg.getSsl().getSslCertCHash()));
        JKNmsgMap.put("sCertNum", int2Uint32(msg.getSsl().getSslCertCNum()));
        JKNmsgMap.put("sKeyExchange", msg.getSsl().getSslCKeyexchange());
        JKNmsgMap.put("sKeyExchangeLen", int2Uint32(msg.getSsl().getSslCKeyexchangelen()));
        JKNmsgMap.put("sSSLFinger", longToString(msg.getSsl().getSslCFinger()));
        JKNmsgMap.put("sSSLVersion", int2Uint32(msg.getSsl().getSslVersion()));
        JKNmsgMap.put("SH_Version", int2Uint32(msg.getSsl().getSslHelloSVersion()));
        JKNmsgMap.put("SH_Time", int2Uint32(msg.getSsl().getSslHelloSTime()));
        JKNmsgMap.put("SH_Random", msg.getSsl().getSslHelloSRandom());
        JKNmsgMap.put("SH_SessionId", msg.getSsl().getSslHelloSSessionid());
        JKNmsgMap.put("SH_SessionIdLen", int2Uint32(msg.getSsl().getSslHelloSSessionidlen()));
        JKNmsgMap.put("SH_Cipersuite", msg.getSsl().getSslHelloSCipersuite());
        JKNmsgMap.put("SH_CompressionMethod", msg.getSsl().getSslHelloSCompressionmethod());
        JKNmsgMap.put("SH_SessionTicket", msg.getSsl().getSslHelloSSessionticket());
        JKNmsgMap.put("SH_ALPN", msg.getSsl().getSslHelloSAlpn());
        JKNmsgMap.put("SH_ExtentionNum", int2Uint32(msg.getSsl().getSslHelloSExtentionnum()));
        JKNmsgMap.put("SH_Extention", jsonArrayToMap(msg.getSsl().getSslHelloSExtention()));
        JKNmsgMap.put("dCertHash", jsonArrayToMap(msg.getSsl().getSslCertSHash()));
        JKNmsgMap.put("dCertHashStr", msg.getSsl().getSslCertSHash());
        JKNmsgMap.put("dCertNum", int2Uint32(msg.getSsl().getSslCertSNum()));
        JKNmsgMap.put("dKeyExchange", msg.getSsl().getSslSKeyexchange());
        JKNmsgMap.put("dKeyExchangelen", int2Uint32(msg.getSsl().getSslSKeyexchangelen()));
        JKNmsgMap.put("dNewSessionTicket_LifeTime", int2Uint32(msg.getSsl().getSslSNewsessionticketLifetime()));
        JKNmsgMap.put("dNewSessionTicket_Ticket", msg.getSsl().getSslSNewsessionticketTicket());
        JKNmsgMap.put("dNewSessionTicket_TicketLen", int2Uint32(msg.getSsl().getSslSNewsessionticketTicketlen()));
        JKNmsgMap.put("dSSLFinger", longToString(msg.getSsl().getSslSFinger()));


        return JKNmsgMap;
    }

    public Map<String, Object> msg157Handle(ZMPNMsg.JKNmsg msg) {


        List<String> kexAlgorithmsList = msg.getSsh().getClient().getKexAlgorithmsList();
        List<Object> kexAlgorithmsListValues = new ArrayList<>();
        for (String tmp : kexAlgorithmsList) {

            kexAlgorithmsListValues.add(tmp);
        }
        List<String> serverHostKeyAlgorithmsList = msg.getSsh().getClient().getServerHostKeyAlgorithmsList();
        List<Object> serverHostKeyAlgorithmsListValues = new ArrayList<>();
        for (String tmp : serverHostKeyAlgorithmsList) {

            serverHostKeyAlgorithmsListValues.add(tmp);
        }
        List<String> encryptionAlgorithmsClientToServerList = msg.getSsh().getClient().getEncryptionAlgorithmsClientToServerList();
        List<Object> encryptionAlgorithmsClientToServerListValues = new ArrayList<>();
        for (String tmp : encryptionAlgorithmsClientToServerList) {

            encryptionAlgorithmsClientToServerListValues.add(tmp);
        }
        List<String> encryptionAlgorithmsServerToClientList = msg.getSsh().getClient().getEncryptionAlgorithmsServerToClientList();
        List<Object> encryptionAlgorithmsServerToClientListValues = new ArrayList<>();
        for (String tmp : encryptionAlgorithmsServerToClientList) {

            encryptionAlgorithmsServerToClientListValues.add(tmp);
        }
        List<String> macAlgorithmsClientToServerList = msg.getSsh().getClient().getMacAlgorithmsClientToServerList();
        List<Object> macAlgorithmsClientToServerListValues = new ArrayList<>();
        for (String tmp : macAlgorithmsClientToServerList) {

            macAlgorithmsClientToServerListValues.add(tmp);
        }
        List<String> macAlgorithmsServerToClientList = msg.getSsh().getClient().getMacAlgorithmsServerToClientList();
        List<Object> macAlgorithmsServerToClientListValues = new ArrayList<>();
        for (String tmp : macAlgorithmsServerToClientList) {

            macAlgorithmsServerToClientListValues.add(tmp);
        }
        List<String> compressionAlgorithmsClientToServerList = msg.getSsh().getClient().getCompressionAlgorithmsClientToServerList();
        List<Object> compressionAlgorithmsClientToServerListValues = new ArrayList<>();
        for (String tmp : compressionAlgorithmsClientToServerList) {

            compressionAlgorithmsClientToServerListValues.add(tmp);
        }
        List<String> compressionAlgorithmsServerToClientList = msg.getSsh().getClient().getCompressionAlgorithmsServerToClientList();
        List<Object> compressionAlgorithmsServerToClientListValues = new ArrayList<>();
        for (String tmp : compressionAlgorithmsServerToClientList) {

            compressionAlgorithmsServerToClientListValues.add(tmp);
        }
        List<String> kexAlgorithmsList1 = msg.getSsh().getServer().getKexAlgorithmsList();
        List<Object> kexAlgorithmsListValues1 = new ArrayList<>();
        for (String tmp : kexAlgorithmsList1) {

            kexAlgorithmsListValues1.add(tmp);
        }
        List<String> serverHostKeyAlgorithmsList1 = msg.getSsh().getServer().getServerHostKeyAlgorithmsList();
        List<Object> serverHostKeyAlgorithmsListValues1 = new ArrayList<>();
        for (String tmp : serverHostKeyAlgorithmsList1) {

            serverHostKeyAlgorithmsListValues1.add(tmp);
        }
        List<String> encryptionAlgorithmsClientToServerList1 = msg.getSsh().getServer().getEncryptionAlgorithmsClientToServerList();
        List<Object> encryptionAlgorithmsClientToServerListValues1 = new ArrayList<>();
        for (String tmp : encryptionAlgorithmsClientToServerList1) {

            encryptionAlgorithmsClientToServerListValues1.add(tmp);
        }
        List<String> encryptionAlgorithmsServerToClientList1 = msg.getSsh().getServer().getEncryptionAlgorithmsServerToClientList();
        List<Object> encryptionAlgorithmsServerToClientListValues1 = new ArrayList<>();
        for (String tmp : encryptionAlgorithmsServerToClientList1) {

            encryptionAlgorithmsServerToClientListValues1.add(tmp);
        }
        List<String> macAlgorithmsClientToServerList1 = msg.getSsh().getServer().getMacAlgorithmsClientToServerList();
        List<Object> macAlgorithmsClientToServerListValues1 = new ArrayList<>();
        for (String tmp : macAlgorithmsClientToServerList1) {

            macAlgorithmsClientToServerListValues1.add(tmp);
        }
        List<String> macAlgorithmsServerToClientList1 = msg.getSsh().getServer().getMacAlgorithmsServerToClientList();
        List<Object> macAlgorithmsServerToClientListValues1 = new ArrayList<>();
        for (String tmp : macAlgorithmsServerToClientList1) {

            macAlgorithmsServerToClientListValues1.add(tmp);
        }
        List<String> compressionAlgorithmsClientToServerList1 = msg.getSsh().getServer().getCompressionAlgorithmsClientToServerList();
        List<Object> compressionAlgorithmsClientToServerListValues1 = new ArrayList<>();
        for (String tmp : compressionAlgorithmsClientToServerList1) {

            compressionAlgorithmsClientToServerListValues1.add(tmp);
        }
        List<String> compressionAlgorithmsServerToClientList1 = msg.getSsh().getServer().getCompressionAlgorithmsServerToClientList();
        List<Object> compressionAlgorithmsServerToClientListValues1 = new ArrayList<>();
        for (String tmp : compressionAlgorithmsServerToClientList1) {

            compressionAlgorithmsServerToClientListValues1.add(tmp);
        }
        JKNmsgMap.put("sIp", msg.getSsh().getCommMsg().getSrcIp());
        JKNmsgMap.put("sPort", int2Uint32(msg.getSsh().getCommMsg().getSrcPort()));
        JKNmsgMap.put("dIp", msg.getSsh().getCommMsg().getDstIp());
        JKNmsgMap.put("dPort", int2Uint32(msg.getSsh().getCommMsg().getDstPort()));
        JKNmsgMap.put("ServerIP", msg.getSsh().getCommMsg().getServerIp());
        JKNmsgMap.put("SessionId", msg.getSsh().getCommMsg().getSessionId());
        JKNmsgMap.put("TaskId", int2Uint32(msg.getSsh().getCommMsg().getTaskId()));
        JKNmsgMap.put("BatchNum", int2Uint32(msg.getSsh().getCommMsg().getBatchId()));
        JKNmsgMap.put("StartTime", int2Uint32(msg.getSsh().getCommMsg().getBeginTime()));
        JKNmsgMap.put("StartNSec", int2Uint32(msg.getSsh().getCommMsg().getBeginNsec()));
        Map<String, Object> clientMap = new HashMap<>();

        clientMap.put("Protocol", msg.getSsh().getClient().getProtocol());
        clientMap.put("Cookie", msg.getSsh().getClient().getCookie());
        clientMap.put("KexAlgorithms", kexAlgorithmsListValues);
        clientMap.put("ServerHostKeyAlgorithms", serverHostKeyAlgorithmsListValues);
        clientMap.put("EncryptionAlgorithmsC2S", encryptionAlgorithmsClientToServerListValues);
        clientMap.put("EncryptionAlgorithmsS2C", encryptionAlgorithmsServerToClientListValues);
        clientMap.put("MacAlgorithmsC2S", macAlgorithmsClientToServerListValues);
        clientMap.put("MacAlgorithmsS2C", macAlgorithmsServerToClientListValues);
        clientMap.put("CompressionAlgorithmsC2S", compressionAlgorithmsClientToServerListValues);
        clientMap.put("CompressionAlgorithmsS2C", compressionAlgorithmsServerToClientListValues);
        JKNmsgMap.put("Client", clientMap);
        Map<String, Object> serverMap = new HashMap<>();

        serverMap.put("Protocol", msg.getSsh().getServer().getProtocol());
        serverMap.put("Cookie", msg.getSsh().getServer().getCookie());
        serverMap.put("KexAlgorithms", kexAlgorithmsListValues1);
        serverMap.put("ServerHostKeyAlgorithms", serverHostKeyAlgorithmsListValues1);
        serverMap.put("EncryptionAlgorithmsC2S", encryptionAlgorithmsClientToServerListValues1);
        serverMap.put("EncryptionAlgorithmsS2C", encryptionAlgorithmsServerToClientListValues1);
        serverMap.put("MacAlgorithmsC2S", macAlgorithmsClientToServerListValues1);
        serverMap.put("MacAlgorithmsS2C", macAlgorithmsServerToClientListValues1);
        serverMap.put("CompressionAlgorithmsC2S", compressionAlgorithmsClientToServerListValues1);
        serverMap.put("CompressionAlgorithmsS2C", compressionAlgorithmsServerToClientListValues1);
        JKNmsgMap.put("Server", serverMap);
        JKNmsgMap.put("DH_e", msg.getSsh().getDhE());
        JKNmsgMap.put("DH_f", msg.getSsh().getDhF());
        JKNmsgMap.put("DHGEX_Min", msg.getSsh().getDhGexMin());
        JKNmsgMap.put("DHGEX_NBits", msg.getSsh().getDhGexNbits());
        JKNmsgMap.put("DHGEX_Max", msg.getSsh().getDhGexMax());
        JKNmsgMap.put("DHGEX_P", msg.getSsh().getDhGexP());
        JKNmsgMap.put("DHGEX_G", msg.getSsh().getDhGexG());
        JKNmsgMap.put("ECDH_Q_C", msg.getSsh().getEcdhQC());
        JKNmsgMap.put("ECDH_Q_S", msg.getSsh().getEcdhQS());
        JKNmsgMap.put("HostKeyType", msg.getSsh().getHostKeyType());
        JKNmsgMap.put("HostKey_RSA_e", msg.getSsh().getHostKeyRsaE());
        JKNmsgMap.put("HostKey_RSA_N", msg.getSsh().getHostKeyRsaN());
        JKNmsgMap.put("HostKey_ECDSA_ID", msg.getSsh().getHostKeyEcdsaId());
        JKNmsgMap.put("HostKey_ECDSA_Q", msg.getSsh().getHostKeyEcdsaQ());
        JKNmsgMap.put("HostKey_DSA_p", msg.getSsh().getHostKeyDsaP());
        JKNmsgMap.put("HostKey_DSA_q", msg.getSsh().getHostKeyDsaQ());
        JKNmsgMap.put("HostKey_DSA_g", msg.getSsh().getHostKeyDsaG());
        JKNmsgMap.put("HostKey_DSA_y", msg.getSsh().getHostKeyDsaY());
        JKNmsgMap.put("HostKey_EdDSA_Key", msg.getSsh().getHostKeyEddsaKey());
        JKNmsgMap.put("KexHSig", msg.getSsh().getKexHSig());


        return JKNmsgMap;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.geeksec.authentication.dao.UserDao">

    <select id="countUser" resultType="Integer">
        SELECT count(0)
        FROM tb_user
        WHERE  status = 1
    </select>

    <resultMap id="userMap" type="com.geeksec.authentication.entity.vo.UserInfoVo">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="show_username" property="showUsername"/>
        <result column="password" property="password"/>
        <result column="phone" property="phone"/>
        <result column="email" property="email"/>
        <result column="remarks" property="remarks"/>
        <result column="created_time" property="createdTime"/>
        <result column="last_modified_time" property="lastModifiedTime"/>
        <collection property="roleNames" ofType="java.lang.String">
            <constructor>
                <arg column="roleNames"/>
            </constructor>
        </collection>
    </resultMap>

    <select id="listUser" resultMap="userMap">
        SELECT tu.id,
        tu.username,
        tu.show_username,
        tu.password,
        tu.phone,
        tu.email,
        tu.remarks,
        tu.created_time,
        tu.last_modified_time,
        tg.role_name roleNames
        FROM auth_db.tb_user tu
        LEFT JOIN auth_db.tb_user_role tur on tu.id = tur.user_id
        LEFT JOIN auth_db.tb_role tg on tur.role_id = tg.role_id
        <where>
            tu.status = 1
            <choose>
                <when test="condition.query != '' and condition.query !=null">
                    AND (
                    tu.username LIKE CONCAT('%',#{condition.query,jdbcType=VARCHAR},'%')
                    OR
                    tu.show_username LIKE CONCAT('%',#{condition.query,jdbcType=VARCHAR},'%')
                    OR
                    tu.email LIKE CONCAT('%',#{condition.query,jdbcType=VARCHAR},'%')
                    OR
                    tg.role_name LIKE CONCAT('%',#{condition.query,jdbcType=VARCHAR},'%')
                    )
                </when>
            </choose>
        </where>
        <choose>
            <when test="condition.sortName != null and !&quot;!&quot;.equals(condition.sortName.trim())">
                <choose>
                    <when test="condition.sortName == 'username'">
                        ORDER BY CONVERT (username USING gbk) ${condition.sortOrder}
                    </when>
                    <when test="condition.sortName == 'show_username'">
                        ORDER BY CONVERT (show_username USING gbk) ${condition.sortOrder}
                    </when>
                    <otherwise>
                        ORDER BY created_time ${condition.sortOrder}
                    </otherwise>
                </choose>
            </when>
        </choose>

    </select>

    <select id="getExistUserName" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(1) from auth_db.tb_user where username = #{username}
    </select>

    <insert id="addUser" useGeneratedKeys="true" keyProperty="userId">
        insert into
            auth_db.tb_user
        (id,username,show_username,password,phone,email,remarks,web_show,status,created_time,last_modified_time)
        values
        (#{user.userId},#{user.username},#{user.showUsername},#{user.password},#{user.phone},#{user.email},#{user.remarks},1,1,now(),now())
    </insert>

    <update id="updateUser" parameterType="com.geeksec.authentication.condition.user.UserCondition">
        update tb_user
        <trim prefix="set">
            <if test="user.showUsername != null and user.showUsername != ''">show_username = #{user.showUsername},</if>
            <if test="user.username != null and user.username != ''">username = #{user.username},</if>
            <if test="user.password != null and user.password != ''">password = #{user.password},</if>
            <if test="user.phone != null and user.phone != ''">phone = #{user.phone},</if>
            <if test="user.email != null and user.email != ''">email =#{user.email},</if>
            <if test="user.remarks != null and user.remarks != ''">remarks = #{user.remarks},</if>
        </trim>
        last_modified_time = now()
        where id = #{user.userId}
    </update>

    <select id="getAllRoles" resultType="com.alibaba.fastjson.JSONObject">
        SELECT id        roleId,
               role_name roleName
        FROM sys_role
        WHERE delete_status = '1'
    </select>

    <delete id="batchDeleteUser">
        delete from auth_db.tb_user
        where id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="removeRole">
        UPDATE sys_role
        SET delete_status = '2'
        WHERE id = #{roleId}
          and id != 1
    </update>

    <update id="removeRoleAllPermission">
        UPDATE sys_role_permission
        SET delete_status = '2'
        WHERE role_id = #{roleId}
    </update>

    <update id="removeOldPermission">
        UPDATE sys_role_permission
        SET
        delete_status = '2'
        WHERE role_id = #{roleId}
        AND permission_id in (
        <foreach collection="permissions" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </update>
    <update id="updateRoleName">
        UPDATE sys_role
        SET role_name = #{roleName}
        WHERE id = #{roleId}
    </update>
    <select id="queryExistUsername" resultType="int">
        select count(0)
        from sys_user
        WHERE username = #{username}
          AND delete_status = '1'
    </select>

    <select id="getAllUsername" resultType="java.lang.String">
        select username from auth_db.tb_user
    </select>

    <select id="getUserById" resultMap="userMap">
        select * from auth_db.tb_user where id = ${userId}
    </select>

    <select id="getUserNameById" resultType="java.lang.String">
        select username from auth_db.tb_user where id = ${userId}
    </select>

    <update id="updatePassword">
        update auth_db.tb_user set password = #{password} where username = #{userName} and status = '1'
    </update>

    <select id="existIsUser" resultType="java.lang.Integer">
        select count(*)
        from auth_db.tb_user
        where username = #{username} and status = '1'
    </select>

    <select id="queryUserData" resultType="com.geeksec.authentication.entity.vo.UserInfoVo">
        select * from auth_db.tb_user where username = #{username} and status = '1'
    </select>
    <select id="getUserInfo" resultType="com.geeksec.authentication.entity.vo.UserInfoVo">
        select * from auth_db.tb_user where id = #{userId} and status = '1'
    </select>
    <select id="listAll" resultType="com.geeksec.authentication.entity.vo.UserInfoVo">
        select * from auth_db.tb_user where status = '1'
    </select>

    <select id="getUserInfoById" resultType="com.geeksec.authentication.entity.vo.UserInfoVo">
        SELECT tu.id,
               tu.username,
               tu.password,
               tu.show_username,
               tu.phone,
               tu.email,
               tu.created,
               tu.updated
        FROM tb_user tu
        WHERE tu.id = #{id,jdbcType=INTEGER}
    </select>
</mapper>

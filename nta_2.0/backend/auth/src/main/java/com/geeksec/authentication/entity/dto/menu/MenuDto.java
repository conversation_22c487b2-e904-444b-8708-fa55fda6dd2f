package com.geeksec.authentication.entity.dto.menu;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class MenuDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("menu_code")
    private String menuCode;

    @JsonProperty("menu_name")
    private String menuName;

    @JsonProperty("router_path")
    private String routerPath;

    @JsonProperty("url")
    private String url;

    @JsonProperty("permission_code")
    private String permissionCode;

    @JsonProperty("permission_name")
    private String permissionName;

    @JsonProperty("level")
    private Integer level;

    @JsonProperty("parent_menu")
    private String parentMenu;

    @JsonProperty("required_permission")
    private String requiredPermission;

    @JsonProperty("status")
    private Integer status;


}

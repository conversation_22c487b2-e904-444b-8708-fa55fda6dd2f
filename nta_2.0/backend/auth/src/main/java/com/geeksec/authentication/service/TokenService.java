package com.geeksec.authentication.service;

import cn.dev33.satoken.stp.StpUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.authentication.config.exception.CommonJsonException;
import com.geeksec.authentication.dao.LoginDao;
import com.geeksec.authentication.entity.dto.session.SessionUserInfo;
import com.geeksec.authentication.util.StringTools;
import com.geeksec.authentication.util.constants.ErrorEnum;
import com.github.benmanes.caffeine.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.Date;

@Service
@Slf4j
@DS("auth-db")
public class TokenService {

    @Value("${static.token}")
    private String standarToken;

    @Autowired
    private Cache<String, SessionUserInfo> cacheMap;

    @Autowired
    LoginDao loginDao;

    @PostConstruct
    public void initStandarToken(){
        SessionUserInfo standarUser = new SessionUserInfo();
        standarUser.setUsername("root");
        cacheMap.put(standarToken,standarUser);
    }

    /**
     * 用户登录验证通过后(sso/帐密),生成token,记录用户已登录的状态
     */
    public String generateToken(String username,String password) {
        MDC.put("username", username);
        String token = JWT.create()
                    .withJWTId(username)
                    .withIssuer("geeksec")
                    .withNotBefore(new Date())
                    .sign(Algorithm.HMAC256(password));
        if(username.equals("root")){
            // 若是权限全开放用户，返回指定token，永不过期
            return standarToken;
        }
        //设置用户信息缓存
        setCache(token, username);
        return token;
    }

    /**
     * 远程跳转登陆token生成
     */
    public String generateRemoteToken(String username, String dateStr){
        MDC.put("username",username);
        String token = JWT.create()
                .withJWTId(username)
                .withNotBefore(new Date())
                .sign(Algorithm.HMAC256(dateStr));
        // 设置远程用户信息缓存
        setCache(token,username);
        return token;
    }

    public SessionUserInfo getUserInfo() {
        String token = MDC.get("token");
        return getUserInfoFromCache(token);
    }

    /**
     * 根据token查询用户信息
     * 如果token无效,会抛未登录的异常
     */
    public SessionUserInfo getUserInfoFromCache(String token) {
        if (StringTools.isNullOrEmpty(token)) {
            throw new CommonJsonException(ErrorEnum.E_20011);
        }
        log.debug("根据token从缓存中查询用户信息,{}", token);
        SessionUserInfo sessionUserInfo = cacheMap.getIfPresent(token);

        if (ObjectUtils.isEmpty(sessionUserInfo)) {
            log.info("没拿到缓存 token={}", token);
            throw new CommonJsonException(ErrorEnum.E_20011);
        }
        return sessionUserInfo;
    }

    public Integer getUserInfoByToken() {
        String token = MDC.get("token");
        Integer userId = Integer.valueOf((String) StpUtil.getLoginIdByToken(token));
        return userId;
    }


    private void setCache(String token, String username) {
//        SessionUserInfo info = getUserInfoByUsername(username);
        log.info("设置用户信息缓存:token={} , username={}", token, username );
        SessionUserInfo sessionUserInfo = new SessionUserInfo();
        sessionUserInfo.setUsername(username);
        cacheMap.put(token,sessionUserInfo);
    }

    /**
     * 退出登录时,将token置为无效
     */
    public void invalidateToken() {
        String token = MDC.get("token");
        if (!StringTools.isNullOrEmpty(token)) {
            cacheMap.invalidate(token);
        }
        log.debug("退出登录,清除缓存:token={}", token);
        initStandarToken();
    }

    private SessionUserInfo getUserInfoByUsername(String username) {
        SessionUserInfo userInfo = loginDao.getUserInfo(username);
        if (userInfo.getRoleIds().contains(101)) {
            //管理员,查出全部按钮和权限码
            userInfo.setMenuList(loginDao.getAllMenu());
            userInfo.setPermissionList(loginDao.getAllPermissionCode());
        }
        return userInfo;
    }
}

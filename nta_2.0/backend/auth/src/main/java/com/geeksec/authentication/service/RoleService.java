package com.geeksec.authentication.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.authentication.condition.role.RoleCondition;
import com.geeksec.authentication.condition.role.RoleQueryCondition;
import com.geeksec.authentication.entity.vo.RoleInfoVo;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
public interface RoleService {

    /**
     * 根据条件查询角色列表
     * @param condition
     * @return
     */
    PageInfo<RoleInfoVo> listRole(RoleQueryCondition condition);

    /**
     * 获取所有角色信息
     * @return
     */
    JSONObject getAllRoles();

    /**
     * 查询所有权限
     * @return
     */
    JSONObject getAllPermissions();

    /**
     * 根据角色ID查询当前角色所拥有权限
     * @param roleId
     * @return
     */
    JSONObject getPermissionById(Long roleId);


    /**
     * 添加角色
     * @param requestBody
     * @return
     */
    JSONObject addRole(JSONObject requestBody);

    /**
     * 修改角色信息
     * @param condition
     * @return
     */
    JSONObject updateRole(RoleCondition condition);

    /**
     * 批量删除角色
     * @param roleIds
     * @return
     */
    JSONObject batchDeleteRoles(List<Long> roleIds);


}

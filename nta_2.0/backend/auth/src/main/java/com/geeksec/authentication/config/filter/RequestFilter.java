package com.geeksec.authentication.config.filter;

import cn.dev33.satoken.stp.StpUtil;
import com.geeksec.authentication.dao.UserDao;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.authentication.util.StringTools;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RequestFilter extends OncePerRequestFilter implements Filter {

    @Value("${static.token}")
    private String standarToken;

    @Autowired
    TokenService tokenService;

    @Autowired
    UserDao userDao;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        //每个请求记录一个traceId,可以根据traceId搜索出本次请求的全部相关日志
        MDC.put("traceId", UUID.randomUUID().toString().replace("-", "").substring(0, 12));
        setProductId(request);
        // 检测请求是否为大文件上传
        boolean isLargeUpload = request.getContentType() != null && request.getContentType().startsWith("multipart/form-data");
        if (!isLargeUpload) {
            // 使 request 中的 body 可以重复读取
            request = new ContentCachingRequestWrapper(request);
        }
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
        response.setHeader("Access-Control-Allow-Headers", "*");
        String uri = request.getRequestURI();
        // 登录接口不需要做username校验
        if (!uri.equals("/login") && !uri.equals("/dict")) {
            setUsername(request);
        }
        // 判断完所有设置之后执行剩余的拦截器校验
        filterChain.doFilter(request, response);
        //清理ThreadLocal
        MDC.clear();
    }

    /**
     * 将url参数中的productId放入ThreadLocal
     */
    private void setProductId(HttpServletRequest request) {
        String productIdStr = request.getParameter("productId");
        if (!StringTools.isNullOrEmpty(productIdStr)) {
            log.debug("url中productId = {}", productIdStr);
            MDC.put("productId", productIdStr);
        }
    }

    private void setUsername(HttpServletRequest request) {

        // 通过token解析出username
        String token = request.getHeader("token");
        if (token == null) {
            return;
        }
        MDC.put("token", token);
        if (token.equals(standarToken)) {
            MDC.put("username", "超级管理员");
            return;
        }

        Object loginId = StpUtil.getLoginIdByToken(token);
        if (loginId == null) {
            MDC.put("username", "admin");
            log.warn("token已过期，请重新登录");
        } else {
            Integer userId = Integer.parseInt(loginId.toString());
            UserInfoVo userInfoVo = userDao.getUserInfo(userId);
            if (userInfoVo == null) {
                throw new GkException(GkErrorEnum.USER_NOT_EXIST);
            }
            MDC.put("username", userInfoVo.getUsername());
        }
    }

}

package com.geeksec.authentication.dao;

import com.geeksec.authentication.entity.dto.session.SessionUserInfo;
import com.geeksec.authentication.entity.vo.LoginUserInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: heeexy
 * @description: 登录相关dao
 * @date: 2017/10/24 11:02
 */
public interface LoginDao {

    /**
     * 根据用户名和密码查询对应的用户
     */
    LoginUserInfoVo checkUser(@Param("username") String username, @Param("password") String password);

    /**
     * 判断用户是否存在
     *
     * @param username
     * @return
     */
    Boolean checkUserExist(String username);

    SessionUserInfo getUserInfo(@Param("username") String username);

    Set<String> getAllMenu();

    Set<String> getAllPermissionCode();

    List<String> getAllRemoteKey();

    void insertRemoteKey(@Param("remoteKey") String remoteKey);
}



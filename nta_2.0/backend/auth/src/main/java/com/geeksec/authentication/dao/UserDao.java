package com.geeksec.authentication.dao;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.authentication.condition.user.UserCondition;
import com.geeksec.authentication.condition.user.UserQueryCondition;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: heeexy
 * @description: 用户/角色/权限
 * @date: 2017-11-14 15:08:45
 */
@Mapper
public interface UserDao  {
    /**
     * 查询用户数量
     */
    int countUser();

    /*** 根据条件查询用户列表
     */
    List<UserInfoVo> listUser(@Param("condition") UserQueryCondition condition);

    /**
     * 查询所有的角色
     * 在添加/修改用户的时候要使用此方法
     */
    List<JSONObject> getAllRoles();

    /**
     * 校验是否用同名用户名
     *
     * @param username
     * @return
     */
    int getExistUserName(@Param("username") String username);

    /**
     * 当前用户是否存在
     *
     * @param username 用户名
     * @return 效验转态，返回值 < 0 表示效验成功
     */
    int existIsUser(@Param("username") String username);

    /**
     * 查询指定用户数据
     *
     * @param username 用户名
     * @return 数据库中的数据
     */
    UserInfoVo queryUserData(@Param("username") String username);

    /**
     * 修改用户密码
     *
     * @param password 密码
     * @return
     */
    int updatePassword(@Param("userName") String userName, @Param("password") String password);

    /**
     * 新增用户
     */
    int addUser(@Param("user") UserCondition condition);

    /**
     * 查询所有用户
     * @return
     */
    List<UserInfoVo> listAll();

    /**
     * 修改用户
     */
    int updateUser(@Param("user") UserCondition condition);

    /**
     * 批量删除用户
     * @param userIds
     */
    void batchDeleteUser(@Param("userIds") List<Long> userIds);

    /**
     * 将角色曾经拥有而修改为不再拥有的权限 delete_status改为'2'
     */
    int removeOldPermission(@Param("roleId") String roleId, @Param("permissions") List<Integer> permissions);

    /**
     * 修改角色名称
     */
    int updateRoleName(JSONObject jsonObject);

    /**
     * 删除角色
     */
    int removeRole(JSONObject jsonObject);

    /**
     * 删除本角色全部权限
     */
    int removeRoleAllPermission(JSONObject jsonObject);

    /**
     * 查询所有的用户名列表
     * @return
     */
    List<String> getAllUsername();

    /**
     * 根据userid查找用户名
     * @param userId
     * @return
     */
    String getUserNameById(@Param("userId") Long userId);

    /**
     * 更具用户Id查询用户信息
     * @param userId
     * @return
     */
    UserInfoVo getUserInfo(Integer userId);

    /**
     * 查询当前用户的信息
     * @param loginId
     * @return
     */
    UserInfoVo getUserInfoById(Integer loginId);
}

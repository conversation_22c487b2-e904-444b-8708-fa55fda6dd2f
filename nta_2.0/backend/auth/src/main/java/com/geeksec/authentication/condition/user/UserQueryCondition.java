package com.geeksec.authentication.condition.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.authentication.condition.BaseCondition;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class UserQueryCondition extends BaseCondition {

    /**
     * 查询条件（单条框查询全部）
     */
    @JsonProperty(value = "query")
    private String query;

    /**
     * 排序选项(字段)
     */
    @JsonProperty(value = "sort_name")
    private String sortName;

}

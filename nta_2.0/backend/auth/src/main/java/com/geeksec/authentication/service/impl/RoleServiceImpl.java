package com.geeksec.authentication.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.authentication.condition.role.RoleCondition;
import com.geeksec.authentication.condition.role.RoleQueryCondition;
import com.geeksec.authentication.dao.RoleDao;
import com.geeksec.authentication.entity.dto.permission.PermissionDto;
import com.geeksec.authentication.entity.vo.RoleInfoVo;
import com.geeksec.authentication.service.RoleService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.authentication.util.constants.ErrorEnum;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Service
@DS("auth-db")

public class RoleServiceImpl implements RoleService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    @Autowired
    private RoleDao roleDao;

    @Override
    public PageInfo<RoleInfoVo> listRole(RoleQueryCondition condition) {

        logger.info("根据条件查询角色列表,condition--->{}", condition);
        int page = condition.getCurrentPage();
        int pageSize = condition.getPageSize();
        List<RoleInfoVo> result = new ArrayList<>();
        try {
            PageHelper.startPage(page, pageSize);
            result = roleDao.listRole(condition);
        } catch (Exception e) {
            logger.error("根据条件查询角色列表失败,error-->{}", e.getMessage());
        }

        return new PageInfo<>(result);
    }

    @Override
    public JSONObject getAllRoles() {
        List<HashMap<String, Object>> list = new ArrayList<>();
        try {
            List<RoleInfoVo> result = roleDao.listAllRole();
            for (RoleInfoVo vo : result) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("role_id", vo.getRoleId());
                map.put("role_name", vo.getRoleName());
                map.put("remarks", vo.getRemarks());
                list.add(map);
            }
        } catch (Exception e) {
            logger.info("查询所有角色信息有误,error-->{}", e.getMessage());
            throw new GkException(GkErrorEnum.FAIL);
        }
        return CommonUtil.successJson(list);
    }

    @Override
    public JSONObject getPermissionById(Long roleId) {

        try {
            List<Integer> permissionList = roleDao.getPermissionById(roleId);
            HashMap<String, Object> map = new HashMap<>();
            map.put("permission_ids", permissionList);
            return CommonUtil.successJson(map);
        } catch (Exception e) {
            logger.error("查询当前角色所拥有权限失败,error--->{}", e.getMessage());
            return CommonUtil.errorJson(ErrorEnum.E_10004);
        }
    }

    @Override
    public JSONObject addRole(JSONObject requestBody) {
        String roleName = requestBody.getString("role_name");
        String remarks = requestBody.getString("remarks");
        // 该用户所拥有权限，可以为空
        List<Integer> permissionList = (List<Integer>) requestBody.get("permission_list");

        // 判断是否有同名角色
        int exist = roleDao.getExistRoleName(roleName);
        if (exist > 0) {
            return CommonUtil.errorJson(ErrorEnum.E_10011);
        }
        try {
            // 新增角色信息
            Long roleId = roleDao.addRole(roleName, remarks);
            // 挂靠新增角色权限表
            if (roleId > 0 && !CollectionUtils.isEmpty(permissionList)) {
                roleDao.addRolePermissionRelated(roleId, permissionList);
            }
        } catch (Exception e) {
            logger.error("新增角色权限失败,error--->{}", e.getMessage());
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson("添加角色成功");
    }

    @Override
    public JSONObject updateRole(RoleCondition condition) {
        RoleInfoVo existRole = roleDao.getRoleById(condition.getRoleId());

        // 查询是否存在此角色
        Long roleId = existRole.getRoleId();
        if (ObjectUtils.isEmpty(existRole)) {
            return CommonUtil.errorJson(ErrorEnum.E_10005);
        }

        // 查询现存角色是否重名
        String roleName = condition.getRoleName();
        if (StringUtils.hasText(roleName) && condition.getRoleName().equals(existRole.getRoleName())) {
            return CommonUtil.errorJson(ErrorEnum.E_10016);
        }

        try {
            // 修改用户信息
            roleDao.updateRoleById(condition);
            // 若有修改的用户权限表,删除原生权限对照关系表，生成新的
            if (condition.getPermissionList().size() > 0 && condition.getPermissionList() != null) {
                roleDao.removeRoleAllRelated(roleId);
                roleDao.addRolePermissionRelated(roleId, condition.getPermissionList());
            }

        } catch (Exception e) {
            logger.error("update roles info failed ,error --->{}", e.getMessage());
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson("角色修改成功");
    }

    @Override
    public JSONObject batchDeleteRoles(List<Long> roleIds) {
        logger.info("删除roleIds为{}的角色", roleIds);

        // 无法删除管理员角色
        if (roleIds.contains(101)) {
            return CommonUtil.errorJson(ErrorEnum.E_10013);
        }
        if (roleIds.size() > 0) {
            try {
                // 1.判断当前角色是否有用户正在使用
                Integer count = roleDao.getRoleUserCount(roleIds);
                if (count > 0) {
                    return CommonUtil.errorJson(ErrorEnum.E_10014);
                }
                // 2.删除所有角色信息
                roleDao.deleteRole(roleIds);
                // 3.删除上述删除角色所有对应权限关系
                roleDao.batchRemoveRoleAllRelated(roleIds);
            } catch (Exception e) {
                logger.error("删除角色失败!erorr--->{}", e.getMessage());
                CommonUtil.errorJson(ErrorEnum.E_400);
            }
        }
        return CommonUtil.successJson("删除角色成功");
    }

    @Override
    public JSONObject getAllPermissions() {

        List<HashMap<String, Object>> result = new ArrayList<>();
        try {
            // 1.查出所有的父级菜单和路由信息
            List<HashMap<String, String>> parentMenus = roleDao.getMenuInfoByLevelAndParent(1, null);
            for (HashMap<String, String> parentMenu : parentMenus) {
                HashMap<String, Object> resultMap = new HashMap<>();
                //2. 组装单个父级菜单下的接口和子菜单
                resultMap.put("id", parentMenu.get("id"));
                resultMap.put("menu_code", parentMenu.get("menu_code"));
                resultMap.put("menu_name", parentMenu.get("menu_name"));
                resultMap.put("router_path", parentMenu.get("router_path"));

                //3.接口权限赋予并组装
                List<PermissionDto> permissionList = new ArrayList<>();
                permissionList = roleDao.getPermissionByParent(parentMenu.get("menu_code"));
                resultMap.put("permission_list", permissionList);

                //4.组件子菜单的路径与权限
                List<HashMap<String, Object>> childMaps = new ArrayList<>();
                List<HashMap<String, String>> childMenus = roleDao.getMenuInfoByLevelAndParent(2, parentMenu.get("menu_code"));
                if (CollectionUtils.isEmpty(childMenus)) {
                    resultMap.put("children", null);
                } else {
                    for (HashMap<String, String> childMenu : childMenus) {
                        HashMap<String, Object> childMap = new HashMap<>();
                        childMap.put("id", childMenu.get("id"));
                        childMap.put("menu_code", childMenu.get("menu_code"));
                        childMap.put("menu_name", childMenu.get("menu_name"));
                        childMap.put("parent_menu", childMenu.get("parent_menu"));
                        childMap.put("router_path", childMenu.get("router_path"));
                        List<PermissionDto> childPermissions = roleDao.getPermissionByParent(childMenu.get("menu_code"));
                        childMap.put("permission_list", childPermissions);
                        childMaps.add(childMap);
                    }
                    resultMap.put("children", childMaps);
                }
                result.add(resultMap);
            }
        } catch (Exception e) {
            logger.error("查询所有权限失败,error -->{}", e.getMessage());
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(result);
    }

}

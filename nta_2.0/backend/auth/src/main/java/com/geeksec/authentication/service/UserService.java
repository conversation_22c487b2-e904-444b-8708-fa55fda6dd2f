package com.geeksec.authentication.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.authentication.condition.user.UserCondition;
import com.geeksec.authentication.condition.user.UserQueryCondition;
import com.geeksec.authentication.entity.vo.UserInfoVo;
import com.geeksec.entity.common.ResultVo;

import java.util.HashMap;
import java.util.List;

/**
 * @author: heeexy
 * @description: 用户/角色/权限
 * @date: 2017/11/2 10:18
 */
public interface UserService {

    /**
     * 根据条件获取用户列表
     * @param condition
     * @return
     */
    HashMap<String,Object> listUser(UserQueryCondition condition);

    /**
     * 新增用户
     * @param user
     * @return
     */
    JSONObject addUser(UserCondition user);

    /**
     * 修改用户信息
     * @param condition
     * @return
     */
    JSONObject updateUser(UserCondition condition);

    /**
     * 删除用户
     * @param userIds
     * @return
     */
    JSONObject deleteUser(List<Long> userIds);

    /**
     * 查询用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    UserInfoVo queryUserData(String username);

    /**
     * 根据Id查询用户信息
     *
     * @param userId userId
     * @return 用户信息
     */
    UserInfoVo queryUserData(Integer userId);

    /**
     * 通过token查询当前用户的信息
     * @param token
     * @return
     */
    ResultVo getLoginUserInfo(String token);

    String getUserNameById(Long userId);


//    /**
//     * 角色列表
//     */
//    public JSONObject listRole() {
//        List<JSONObject> roles = userDao.listRole();
//        return CommonUtil.successPage(roles);
//    }
//
//    /**
//     * 查询所有权限, 给角色分配权限时调用
//     */
//
//    public JSONObject listAllPermission() {
//        List<JSONObject> permissions = userDao.listAllPermission();
//        return CommonUtil.successPage(permissions);
//    }
//
//    /**
//     * 添加角色
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @SuppressWarnings("unchecked")
//    public JSONObject addRole(JSONObject jsonObject) {
//        userDao.insertRole(jsonObject);
//        userDao.insertRolePermission(jsonObject.getString("roleId"), (List<Integer>) jsonObject.get("permissions"));
//        return CommonUtil.successJson();
//    }
//
//    /**
//     * 修改角色
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @SuppressWarnings("unchecked")
//    public JSONObject updateRole(JSONObject jsonObject) {
//        String roleId = jsonObject.getString("roleId");
//        List<Integer> newPerms = (List<Integer>) jsonObject.get("permissions");
//        JSONObject roleInfo = userDao.getRoleAllInfo(jsonObject);
//        Set<Integer> oldPerms = (Set<Integer>) roleInfo.get("permissionIds");
//        //修改角色名称
//        dealRoleName(jsonObject, roleInfo);
//        //添加新权限
//        saveNewPermission(roleId, newPerms, oldPerms);
//        //移除旧的不再拥有的权限
//        removeOldPermission(roleId, newPerms, oldPerms);
//        return CommonUtil.successJson();
//    }
//
//    /**
//     * 修改角色名称
//     */
//    private void dealRoleName(JSONObject paramJson, JSONObject roleInfo) {
//        String roleName = paramJson.getString("roleName");
//        if (!roleName.equals(roleInfo.getString("roleName"))) {
//            userDao.updateRoleName(paramJson);
//        }
//    }
//
//    /**
//     * 为角色添加新权限
//     */
//    private void saveNewPermission(String roleId, Collection<Integer> newPerms, Collection<Integer> oldPerms) {
//        List<Integer> waitInsert = new ArrayList<>();
//        for (Integer newPerm : newPerms) {
//            if (!oldPerms.contains(newPerm)) {
//                waitInsert.add(newPerm);
//            }
//        }
//        if (waitInsert.size() > 0) {
//            userDao.insertRolePermission(roleId, waitInsert);
//        }
//    }
//
//    /**
//     * 删除角色 旧的 不再拥有的权限
//     */
//    private void removeOldPermission(String roleId, Collection<Integer> newPerms, Collection<Integer> oldPerms) {
//        List<Integer> waitRemove = new ArrayList<>();
//        for (Integer oldPerm : oldPerms) {
//            if (!newPerms.contains(oldPerm)) {
//                waitRemove.add(oldPerm);
//            }
//        }
//        if (waitRemove.size() > 0) {
//            userDao.removeOldPermission(roleId, waitRemove);
//        }
//    }
//
//    /**
//     * 删除角色
//     */
//    @Transactional(rollbackFor = Exception.class)
//    @SuppressWarnings("unchecked")
//    public JSONObject deleteRole(JSONObject jsonObject) {
//        JSONObject roleInfo = userDao.getRoleAllInfo(jsonObject);
//        List<JSONObject> users = (List<JSONObject>) roleInfo.get("users");
//        if (users != null && users.size() > 0) {
//            return CommonUtil.errorJson(ErrorEnum.E_10008);
//        }
//        userDao.removeRole(jsonObject);
//        userDao.removeRoleAllPermission(jsonObject);
//        return CommonUtil.successJson();
//    }
}

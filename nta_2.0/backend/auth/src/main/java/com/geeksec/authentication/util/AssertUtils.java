package com.geeksec.authentication.util;

import com.geeksec.exception.SzwRunTimeException;

import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
public class AssertUtils {

    /**
     * 判断是否为NULL
     * @param obj
     * @param label
     */
    public static void assertNotNull(Object obj, String label) {
        if(null == obj){
            throw new SzwRunTimeException(label + "不能为空");
        }
    }

    /**
     * 判断是否为空
     * @param obj
     * @param label
     */
    public static void assertNotEmpty(Object obj, String label) {
        if(null != obj){
            if(obj instanceof String){
                String string = (String) obj;
                if("".equals(string.trim())){
                    throw new SzwRunTimeException(label + "不能为空");
                }
            }else if(obj.getClass().isArray()){
                if(((Object[])obj).length<1){
                    throw new SzwRunTimeException(label + "不能为空");
                }
            }else if(obj instanceof Collection){
                if(((Collection<?>)obj).size()<1){
                    throw new SzwRunTimeException(label + "不能为空");
                }
            }else if(obj instanceof Map){
                if(((Map<?, ?>)obj).size()<1){
                    throw new SzwRunTimeException(label + "不能为空");
                }
            }
        }else{
            throw new SzwRunTimeException(label + "不能为NULL");
        }
    }



    /**
     * 判断数组不能为空
     * @param array
     * @param label
     */
    public static <T> void assertArrayNotEmpty(T[] array, String label){
        if(null!=array){
            if(array.length<1){
                throw new SzwRunTimeException(label + "不能为空");
            }
        }else{
            throw new SzwRunTimeException(label + "不能为NULL");
        }
    }

    /**
     * 判断集合不能为空
     * @param label
     */
    public static <T> void assertCollectionNotEmpty(Collection<T> collection, String label){
        if(null!=collection){
            if(collection.size()<1){
                throw new SzwRunTimeException(label + "不能为空");
            }
        }else{
            throw new SzwRunTimeException(label + "不能为NULL");
        }
    }

    /**
     * 判断Map不能为空
     * @param label
     */
    public static <E, T> void assertMapNotEmpty(Map<E, T> map, String label) {
        if(null!=map){
            if(map.size()<1){
                throw new SzwRunTimeException(label + "不能为空");
            }
        }else{
            throw new SzwRunTimeException(label + "不能为NULL");
        }
    }
}

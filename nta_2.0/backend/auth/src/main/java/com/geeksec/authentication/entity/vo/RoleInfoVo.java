package com.geeksec.authentication.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class RoleInfoVo {

    @JsonProperty(value = "role_id")
    private Long roleId;

    @JsonProperty(value = "role_name")
    private String roleName;

    @JsonProperty(value = "remarks")
    private String remarks;

    @JsonProperty(value = "status")
    private Integer status;

    @JsonProperty("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    @JsonProperty("last_modified_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastModifiedTime;
}

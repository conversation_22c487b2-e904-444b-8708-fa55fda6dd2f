package com.geeksec.authentication.service;

import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.geeksec.authentication.dao.LoginDao;
import com.geeksec.authentication.entity.vo.LoginUserInfoVo;
import com.geeksec.authentication.util.Md5Util;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: heeexy
 * @description: 登录service实现类
 * @date: 2017/10/24 11:53
 */
@DS("auth-db")
@Service
@Slf4j
public class LoginService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    @Autowired
    private LoginDao loginDao;
    @Autowired
    private TokenService tokenService;

    /**
     * 登录表单提交
     */
    public ResultVo authLogin(JSONObject jsonObject) {
        String username = jsonObject.getString("username");
        String passwordMd5Str = Md5Util.encodeMd5(jsonObject.getString("password"));

        // 先判断用户是否存在
        Boolean userExist = loginDao.checkUserExist(username);
        if (!userExist) {
            throw new GkException(GkErrorEnum.USER_NOT_EXIST);
        }

        // 判断用户用户密码是否正确
        LoginUserInfoVo loginUserInfoVo = loginDao.checkUser(username, passwordMd5Str);
        if (loginUserInfoVo == null) {
            throw new GkException(GkErrorEnum.LOGIN_PASSWORD_FAILED);
        }

        // 使用so-token方式进行单点登录
        StpUtil.login(loginUserInfoVo.getUserId());
        SaTokenInfo tokenInfo = StpUtil.getTokenInfo();
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("token", tokenInfo.getTokenValue());
        resultMap.put("username", loginUserInfoVo.getUsername());
        resultMap.put("show_username", loginUserInfoVo.getShowUsername());
        resultMap.put("groub_id", loginUserInfoVo.getGroubId());
        return ResultVo.success(resultMap);
    }

    /**
     * 退出登录
     */
    public ResultVo logout() {
        StpUtil.logout();
        return ResultVo.success("用户登出成功");
    }

    /**
     * 远程登录表单提交
     *
     * @param params
     * @return
     */
    public String remoteAuthLogin(JSONObject params) {
        // 远程登录用户名
        String username = params.getString("username");

        // 验证口令 : username + 当前年月日 md5形式 () e1c7bb737ad014f62e4dda5bf4f7a875
        String identification = params.getString("identification");

        SimpleDateFormat dateformat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateformat.format(System.currentTimeMillis());

        try {
            List<String> keyList = loginDao.getAllRemoteKey();
            for (String remoteKey : keyList) {
                String currentIdentification = username + "_" + dateStr + "_" + remoteKey;
                if (identification.equals(Md5Util.encodeMd5(currentIdentification))) {
                    return tokenService.generateRemoteToken(username, dateStr);
                }
            }
        } catch (Exception e) {
            logger.error("远程登录获取token失败！");
            return StringUtils.EMPTY;
        }

        return StringUtils.EMPTY;
    }

}

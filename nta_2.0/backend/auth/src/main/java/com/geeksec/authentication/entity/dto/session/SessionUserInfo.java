package com.geeksec.authentication.entity.dto.session;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * 保存在session中的用户信息
 * <AUTHOR>
 */
@Data
public class SessionUserInfo {

    /**
     * 用户ID1
     */
    @JsonProperty("id")
    private Long userId;

    /**
     * 用户名
     */
    @JsonProperty("username")
    private String username;

    /**
     * 用户展示名
     */
    @JsonProperty("show_username")
    private String showUsername;

    /**
     * 用户组（全）
     */
    @JsonProperty("role_ids")
    private List<Integer> roleIds;

    /**
     * 可访问菜单权限
     */
    @JsonProperty("menu_list")
    private Set<String> menuList;

    /**
     * 可访问菜单路由
     */
    @JsonProperty("router_list")
    private Set<String> routerList;

    /**
     * 拥有权限集合
     */
    @JsonProperty("permission_list")
    private Set<String> permissionList;
}

package com.geeksec.authentication.entity.dto.permission;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class PermissionVo {

    /**
     * 权限ID
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 父级菜单
     */
    @JsonProperty("parent_menu")
    private String parentMenu;

    /**
     * 路由信息
     */
    @JsonProperty("router_path")
    private String routerPath;

    /**
     * 权限
     */
    @JsonProperty("permission_code")
    private String permissionCode;

    /**
     * 权限名称
     */
    @JsonProperty("permission_name")
    private String permissionName;

    /**
     * 是否为基础权限
     */
    @JsonProperty("required_permission")
    private int requiredPermission;
}

package com.geeksec.authentication.dao;

import com.geeksec.authentication.condition.role.RoleCondition;
import com.geeksec.authentication.condition.role.RoleQueryCondition;
import com.geeksec.authentication.entity.dto.menu.MenuDto;
import com.geeksec.authentication.entity.dto.permission.PermissionDto;
import com.geeksec.authentication.entity.vo.RoleInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@Mapper
public interface RoleDao {
    /**
     * 新建用户时添加权限
     */
    int addUserRole(@Param("userId") Long userId, @Param("roleIds") List<Integer> roleIds);

    /**
     * 删除当前用户所有角色关联
     * @param userId
     * @return
     */
    int removeUserAllRoleRelated(@Param("userIds") List<Long> userId);

    /**
     * 批量添加用户与角色关系
     * @param roleIds
     * @param userId
     * @return
     */
    int batchAddUserRoleRelated(@Param("roleIds") List<Integer> roleIds, @Param("userId") Long userId);

    /**
     * 根据条件查询角色列表
     * @param condition
     * @return
     */
    List<RoleInfoVo> listRole(@Param("condition") RoleQueryCondition condition);

    List<RoleInfoVo> listAllRole();

    /**
     * 根据角色ID查询权限列表
     * @param roleId
     * @return
     */
    List<Integer> getPermissionById(@Param("roleId") Long roleId);

    /**
     * 获取所有权限信息
     * @return
     */
    List<PermissionDto> getAllPermission();

    /**
     * 获取路由菜单
     * @return
     */
    List<HashMap<String ,String>> getMenuInfoByLevelAndParent(@Param("level") int level,@Param("parentMenu") String parentMenu);

    /**
     * 新增角色信息
     * @param roleName
     * @param remarks
     * @return
     */
    Long addRole(@Param("roleName") String roleName,@Param("remarks") String remarks);

    /**
     * 挂靠角色与权限关系
     * @param roleId
     * @param permissionList
     */
    void addRolePermissionRelated(long roleId, List<Integer> permissionList);

    /**
     * 根据角色ID查询角色信息
     * @param roleId
     * @return
     */
    RoleInfoVo getRoleById(@Param("roleId") Long roleId);

    /**
     * 更新角色信息
     * @param condition
     */
    void updateRoleById(@Param("condition") RoleCondition condition);

    /**
     * 删除角色权限所有关联
     * @param roleId
     */
    void removeRoleAllRelated(@Param("roleId") Long roleId);

    /**
     * 批量删除角色所对应所有关联
     */
    void batchRemoveRoleAllRelated(@Param("roleIds") List<Long> roleIds);

    /**
     * 删除角色
     * @param roleIds
     */
    void deleteRole(@Param("roleIds") List<Long> roleIds);

    /**
     * 查询是否有相同名称角色
     * @param roleName
     * @return
     */
    int getExistRoleName(@Param("roleName") String roleName);

    /**
     * 查询父级菜单下的所有接口权限
     * @param menuCode
     * @return
     */
    List<PermissionDto> getPermissionByParent(@Param("menuName") String menuCode);

    /**
     * 批量插入菜单信息
     * @param menuList
     */
    void batchInsertMenuInfo(@Param("menuList") List<MenuDto> menuList);

    /**
     * 判断当前数组里有无正在使用的用户
     * @param roleIds
     * @return
     */
    Integer getRoleUserCount(@Param("roleIds") List<Long> roleIds);
}




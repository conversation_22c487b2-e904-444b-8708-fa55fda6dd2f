package com.geeksec.authentication.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
@JsonIgnoreProperties(value = "{password}")
public class UserInfoEntity {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("username")
    private String username;

    @JsonProperty("show_username")
    private String showUsername;

    @JsonProperty("password")
    private String password;

    @JsonProperty("phone")
    private String phone;

    @JsonProperty("email")
    private String email;

    @JsonProperty("group_id")
    private Integer groupId;

    @JsonProperty("remarks")
    private String remarks;

    @JsonProperty("web_show")
    private Integer webShow;

    @JsonProperty("status")
    private Integer status;

    @JsonProperty("created_time")
    private Date createdTime;

    @JsonProperty("last_modified_time")
    private Date lastModifiedTime;
}

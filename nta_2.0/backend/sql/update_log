-- 2022-03-24
-- tb_filter_config  过滤规则  新增 2 网段 描述
ALTER TABLE `th_analysis`.`tb_filter_config`
    MODIFY COLUMN `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '0  端口  1 ippro  2 网段';

-- tb_filter_config  过滤规则 新增字段 状态  0:正常  1:删除
ALTER TABLE `th_analysis`.`tb_filter_config`
ADD COLUMN `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态  1:正常  0:删除';

-- tb_filter_config  过滤规则 新增2个索引
ALTER TABLE `th_analysis`.`tb_filter_config`
ADD INDEX `idx_task_id`(`task_id`),
ADD INDEX `idx_hash`(`hash`);

-- push_database库 task_statistic表  新增索引字段
ALTER TABLE `push_database`.`task_statistic`
ADD INDEX `idx_create_time`(`create_time`) USING BTREE;


-- 2022-04-12  删除两张表，这两张表的字段，已经在tb_rule  有了
DROP TABLE `th_analysis`.`tb_rule_info` ;
DROP TABLE `th_analysis`.`tb_rule_lib_config` ;

-- 修改 标识字段为  status
ALTER TABLE `th_analysis`.`tb_rule`
ADD COLUMN `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '规则状态  1:正常 0:删除';

-- 修改 rule_state 使用状态的描述 和 varchar长度
ALTER TABLE `th_analysis`.`tb_rule`
MODIFY COLUMN `rule_state` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规则状态  失效 or 生效';

-- 新增字段 记录规则包含哪些。
ALTER TABLE `th_analysis`.`tb_rule`
ADD COLUMN `rule_type` varchar(20) NOT NULL COMMENT '包含的规则，逗号分割 1:ip规则 2:协议规则 3:特征规则 4:正则规则 5:域名规则';

ALTER TABLE `th_analysis`.`tb_rule`
ADD COLUMN `lib_data_so` text NULL COMMENT 'so文件base64字符串' ,
ADD COLUMN `lib_data_conf` text NULL COMMENT 'conf文件base64字符串' ;


-- 新增 唯一索引  rule_id
ALTER TABLE `th_analysis`.`tb_rule`
ADD UNIQUE INDEX `idx_rule_id`(`rule_id`) USING BTREE;
-- 新增 普通索引  rule_hash
ALTER TABLE `th_analysis`.`tb_rule`
ADD INDEX `idx`(`rule_hash`) USING BTREE;


-- spring 新增配置文件  临时文件地址
file:
  tmp-path: /usr/local/tmp
-- spring 配置 设置每个文件不超过100M   每次请求的文件不超过100M
spring.servlet.multipart.max-file-size = 100MB
spring.servlet.multipart.max-request-size=100MB



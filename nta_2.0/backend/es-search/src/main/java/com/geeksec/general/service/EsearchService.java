package com.geeksec.general.service;

import com.geeksec.entity.common.ResultVo;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.search.MultiSearchRequest;
import org.elasticsearch.action.search.MultiSearchResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.aggregations.Aggregations;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
public interface EsearchService {

    /**
     * 根据索引名称查询当前索引所含文档数量
     *
     * @param protocolName
     * @return
     */
    long indexCount(String protocolName);

    /**
     * Fuzzy错字查询
     *
     * @param key      匹配字段
     * @param domain   字段值
     * @param sub
     * @param page
     * @param pageSize
     */
    List<Map<String, Object>> fuzzySearch(String indexName, String key, String domain, String sub, int page, int pageSize);

    /**
     * 普通共用查询
     *
     * @param searchRequest
     * @return List<Map < String, Object>>
     */
    List<Map<String, Object>> normalSearch(SearchRequest searchRequest);

    /**
     * 聚合查询(返回Aggreagtion自行处理)
     */
    Aggregations aggrSearch(SearchRequest searchRequest);

    /**
     * 普通查询 返回es结构体
     * @param searchRequest
     * @return
     */
    SearchResponse esSearch(SearchRequest searchRequest);


    /**
     * scroll查询 返回es结构体
     */
    List<Map<String,Object>> scrollSearch(SearchRequest searchRequest);

    /**
     * 更新文档
     * @param updateRequest
     */
    UpdateResponse updateDoc(UpdateRequest updateRequest);

    /**
     * 普通查询 count
     * @param searchRequest
     * @return
     */
    CountResponse esSearchForCount(CountRequest searchRequest);

    /**
     * 删除文档
     * @param request
     * @return
     */
    BulkByScrollResponse esDelete(DeleteByQueryRequest request);

    /**
     * 多线程获取即将聚合的EsIds
     * @param size  每页数量  注意调用方传入  一般达到创建10个线程
     * @param esLimit  es聚合的限制数量，配置文件可配
     * @param boolQueryBuilder
     * @param indexNames
     * @param orderField 排序的字段 如 time
     * @return
     */
    ResultVo<List<String>> getEsIds(Integer size, Integer esLimit,
                                    BoolQueryBuilder boolQueryBuilder,
                                    List<String> indexNames, String orderField,
                                    String fieldName);

    /**
     * @param totalSize        当前所需查询到的ID数量 默认最大10W
     * @param esLimit          10000条，单词查询的最大限度
     * @param boolQueryBuilder 查询条件
     * @param indexNames       查询索引
     * @param orderField       排序字段
     * @param fieldName        所查询字段
     * @return
     */
    public ResultVo<List<String>> searchEsIds(Integer totalSize, Integer esLimit, BoolQueryBuilder boolQueryBuilder, List<String> indexNames, String orderField, String fieldName);

    /**
     * 通过scroll获取即将聚合的ES ids
     * @param boolQueryBuilder
     * @param indexName
     * @param orderField
     * @param fieldName
     * @return
     */
    ResultVo<List<String>> getEsIdsByScroll(Integer totalSize,BoolQueryBuilder boolQueryBuilder,List<String> indexName,String orderField,String fieldName);

    /**
     * 元数据查询 先查SessionIds
     * @param map
     * @return
     */
    List<String> getSessionIdsByMateDataQuery(Map<String, BoolQueryBuilder> map);

    /**
     * 删除指定索引下所有信息
     * @param deleteIndexRequest
     * @return
     */
    AcknowledgedResponse deleteDoc(DeleteIndexRequest deleteIndexRequest);

    /**
     * 多个request同时请求，返回多个response
     * @param multiSearchRequest
     * @return
     */
    MultiSearchResponse esMultiSearch(MultiSearchRequest multiSearchRequest);
}

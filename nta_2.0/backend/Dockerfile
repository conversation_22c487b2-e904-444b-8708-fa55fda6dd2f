FROM bitnami/spring-cloud-dataflow:2.10.0-debian-11-r16

USER root
ENV TZ=Asia/Shanghai
ENV DB_HOST=mysql
ENV ES_HOST=elasticsearch
ENV LANG en_US.UTF-8
WORKDIR /opt/local

ARG JAR_FILE=./GeeksecApiApplication.jar
COPY ${JAR_FILE} /opt/local/GeeksecApiApplication.jar

COPY docker/sources.list /etc/apt/sources.list
COPY docker/entrypoint.sh /opt/local/entrypoint.sh
# 具体的文件要copy完全路径和文件名称
COPY template/feature_template.csv /opt/rule/template/feature_template.csv
COPY template/filter_template.csv /opt/rule/template/filter_template.csv
COPY template/LibFolder.zip /opt/rule/template/LibFolder.zip

RUN apt-get update && apt-get upgrade -y --no-install-recommends && DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
      net-tools \
      curl \
      wget \
      netcat \
      vim \
      && rm -rf /var/lib/apt/lists/*

RUN chmod +x /opt/local/entrypoint.sh

ENTRYPOINT ["/bin/bash","/opt/local/entrypoint.sh"]
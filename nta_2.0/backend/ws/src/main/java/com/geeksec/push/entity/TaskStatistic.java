package com.geeksec.push.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskStatistic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前流量bps
     */
    @TableField("bps")
    private Long bps;

    /**
     * 当前流量bps(进：包的目的IP为内网)
     */
    @TableField("bps_in")
    private Integer bpsIn;

    /**
     * 当前流量bps(出：包的目的IP不是内网)
     */
    @TableField("bps_out")
    private Long bpsOut;

    /**
     * 当前流量pps
     */
    @TableField("pps")
    private Long pps;

    /**
     * 当前流量pps(进：包的目的IP为内网)
     */
    @TableField("pps_in")
    private Long ppsIn;

    /**
     * 当前流量pps(出：包的目的IP不是内网)
     */
    @TableField("pps_out")
    private Long ppsOut;

    /**
     * 当前并发连接数
     */
    @TableField("conn")
    private Integer conn;

    /**
     * 当前并发连接数(进：会话服务端IP为内网)
     */
    @TableField("conn_in")
    private Integer connIn;

    /**
     * 当前并发连接数(出：会话服务端IP不是内网)
     */
    @TableField("conn_out")
    private Integer connOut;

    /**
     * IPV4包pps
     */
    @TableField("pps_ipv4")
    private Integer ppsIpv4;

    /**
     * IPV6包pps
     */
    @TableField("pps_ipv6")
    private Integer ppsIpv6;

    /**
     * 其他包pps(非IP包)
     */
    @TableField("pps_notip")
    private Integer ppsNotip;

    /**
     * TCP包pps
     */
    @TableField("pps_tcp")
    private Integer ppsTcp;

    /**
     * UDP包pps
     */
    @TableField("pps_udp")
    private Integer ppsUdp;

    /**
     * 其他包pps(IP包,非TCP,非UDP)
     */
    @TableField("pps_ipother")
    private Integer ppsIpother;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Integer createTime;

    /**
     * //设备ID
     */
    @TableField("device_id")
    private Integer deviceId;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;


}

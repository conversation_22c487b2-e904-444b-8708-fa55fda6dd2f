<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.push.dao.normal.TaskStatisticDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.push.entity.TaskStatistic">
        <id column="id" property="id" />
        <result column="bps" property="bps" />
        <result column="bps_in" property="bpsIn" />
        <result column="bps_out" property="bpsOut" />
        <result column="pps" property="pps" />
        <result column="pps_in" property="ppsIn" />
        <result column="pps_out" property="ppsOut" />
        <result column="conn" property="conn" />
        <result column="conn_in" property="connIn" />
        <result column="conn_out" property="connOut" />
        <result column="pps_ipv4" property="ppsIpv4" />
        <result column="pps_ipv6" property="ppsIpv6" />
        <result column="pps_notip" property="ppsNotip" />
        <result column="pps_tcp" property="ppsTcp" />
        <result column="pps_udp" property="ppsUdp" />
        <result column="pps_ipother" property="ppsIpother" />
        <result column="create_time" property="createTime" />
        <result column="device_id" property="deviceId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        bps, bps_in, bps_out, pps, pps_in, pps_out, conn, conn_in, conn_out, pps_ipv4, pps_ipv6, pps_notip, pps_tcp, pps_udp, pps_ipother, create_time, device_id, id
    </sql>

    <select id="getTodayBps" resultType="java.lang.Long">
        select bps from push_database.task_statistic where create_time &lt; #{endTime} and create_time &gt;= #{startTime}
        and task_id = #{taskId}
        order by create_time desc limit 1
    </select>
</mapper>

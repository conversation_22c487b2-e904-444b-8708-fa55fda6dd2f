<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.push.dao.normal.DiskFieldDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.geeksec.push.entity.DiskField">
        <id column="id" property="id" />
        <result column="field" property="field" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, field
    </sql>

</mapper>

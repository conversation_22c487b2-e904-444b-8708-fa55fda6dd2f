package com.geeksec.general.controller.analysis;

import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.service.MacCommunicationService;
import com.geeksec.entity.common.ResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: GuanHao
 * @Date: 2022/7/25 17:10
 * @Description： <Functions List>
 */
@RestController
@Api(tags = "MAC通讯查询")
@RequestMapping("/mac")
public class MacCommunicationController {

    @Autowired
    MacCommunicationService macCommunicationService;

    /**
     * mac通讯列表查询
     * @return
     */
    @RequestMapping("/list")
    @ApiOperation("MAC 通讯列表查询")
    public ResultVo macList(@RequestBody AnalysisBaseCondition condition){

        return macCommunicationService.macList(condition);
    }
}

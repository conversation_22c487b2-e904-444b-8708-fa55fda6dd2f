package com.geeksec.general.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AddInternalNetCondition;
import com.geeksec.analysis.entity.condition.CommunicationCondition;
import com.geeksec.analysis.entity.condition.UpdateInternalNetCondition;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.general.condition.workbench.CommuListCondition;
import com.geeksec.general.condition.workbench.InternalNetCondition;
import com.geeksec.push.entity.vo.TaskStatisticBpsVo;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：工作台态势接口
 */
public interface WorkBenchService {

    /**
     * 根据条件查询通信信息（IP态势图）
     * @param condition
     * @return
     */
    HashMap<String, Object> getCommuList(CommuListCondition condition) ;

    /**
     * 根据条件查询通信信息（最近1小时流量版本）
     *
     */
    HashMap<String, Object> getCommuListNew(CommuListCondition condition);

    /**
     * 会话信息 查询IP态势图
     * @param condition
     * @return
     */
    ResultVo getCommuList2(CommunicationCondition condition);

    /**
     *
     * @param condition
     * @return
     */
    HashMap<String,Object> getCommuList3(CommuListCondition condition);

    /**
     * 查询内网IP列表
     */
    HashMap<String, Object> getInternalNetList(InternalNetCondition condition);

    /**
     * 添加单条内网IP网段信息
     * @param condition
     */
    JSONObject addInternalNetInfo(AddInternalNetCondition condition);

    /**
     * 获取近日  每天的bps
     * @param days 周期天数
     * @param taskId
     * @return
     */
    List<TaskStatisticBpsVo> getBpsForRecently(Integer days,Integer taskId);

    /**
     * 批量删除内网IP网段信息
     * @param taskIds
     * @return
     */
    boolean deleteInterInfoByIds(List<Integer> taskIds);

    /**
     * 更新内网IP网段信息
     * @param condition
     * @return
     */
    boolean updateInternalNetInfo(UpdateInternalNetCondition condition);

    /**
     * 查询矿池IP列表
     * @return
     */
    HashMap<String, Object> getPoolIpList();
}

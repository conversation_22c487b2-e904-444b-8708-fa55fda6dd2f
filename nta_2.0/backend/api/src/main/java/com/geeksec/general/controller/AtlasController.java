package com.geeksec.general.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.analysis.condition.AtlasCondition;
import com.geeksec.analysis.condition.FocusTag;
import com.geeksec.analysis.condition.VidsSearchCondition;
import com.geeksec.analysis.service.AtlasService;
import com.geeksec.constants.AtlasTypeConstants;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.ngbatis.condition.GraphPropertiesNextCondition;
import com.geeksec.ngbatis.condition.SubGraphNextCondition;
import com.geeksec.ngbatis.service.VertexService;
import com.geeksec.ngbatis.vo.VertexAssociationNextVo;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.HttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.CollectionUtils;
import org.apache.hbase.thirdparty.org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: qiuwen
 * @date: 2022/9/16
 * @Description: 网络流量智能图谱系统（VID查询）
 **/
@RestController
@RequestMapping("/atlas")
public class AtlasController {

    private static final Logger logger = LoggerFactory.getLogger(AtlasController.class);

    @Autowired
    private AtlasService atlasService;
    @Autowired
    private VertexService vertexService;

    /**
     * 根据VID查询对应的第一层数据(图关联查询入口)
     *
     * @return
     */
    @PostMapping("/search")
    public ResultVo vidsSearch(@RequestBody VidsSearchCondition condition) {
        // 不允许存在空字符串
        if (condition.getVidList().stream().anyMatch(StrUtil::isEmpty)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        ResultVo resultVo = atlasService.vidSearch(condition);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            atlasService.craeteAtlasRecord(AtlasTypeConstants.SEARCH,objectMapper.writeValueAsString(condition));
        } catch (JsonProcessingException e) {
            logger.error("保存探索历史失败！error->"+e.getMessage());
        }
        return resultVo;
    }

    /**
     * 根据点属性查询对应的第一层数据
     *
     * @return
     */
    @PostMapping("/properties/search")
    public ResultVo propertiesSearch(@RequestBody GraphPropertiesNextCondition condition) {
        if (StringUtils.isEmpty(condition.getTagName()) || StringUtils.isEmpty(condition.getPropertiesName()) || StringUtils.isEmpty(condition.getPropertiesValue())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        ResultVo resultVo = atlasService.propertiesSearch(condition);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            atlasService.craeteAtlasRecord(AtlasTypeConstants.PROPERTIES_SEARCH,objectMapper.writeValueAsString(condition));
        } catch (JsonProcessingException e) {
            logger.error("保存探索历史失败！error->"+e.getMessage());
        }
        return resultVo;
    }

    /**
     * 子图遍历
     * @param condition
     * @return
     */
    @PostMapping("/sub/search")
    public ResultVo subSearch(@RequestBody SubGraphNextCondition condition) {
        if (CollectionUtil.isEmpty(condition.getVidList()) || condition.getStepCount()==null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        VertexAssociationNextVo vertexAssociationNextVo = vertexService.getSubAssociationNext(condition);
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            atlasService.craeteAtlasRecord(AtlasTypeConstants.SUB_SEARCH,objectMapper.writeValueAsString(condition));
        } catch (JsonProcessingException e) {
            logger.error("保存探索历史失败！error->"+e.getMessage());
        }
        return ResultVo.success(vertexAssociationNextVo);
    }

    /**
     * 根据节点ID与类型 进行单体TAG 节点列表查询
     *
     * @return
     */
    @PostMapping("/relation/list")
    public ResultVo tagRelationList(@RequestBody AtlasCondition condition) {
        HashMap<String, Object> result = atlasService.relationList(condition);
        if (MapUtils.isEmpty(result)) {
            return ResultVo.success("当前查询关联节点无数据!");
        }
        return ResultVo.success(result);
    }

    /**
     * 查询到第一层节点后，进行标签节点查询
     */
    @PostMapping("/tagLabels")
    public ResultVo getTagLabels(@RequestBody List<Map<String, Object>> params) {
        if (CollectionUtils.isEmpty(params)) {
            return ResultVo.success("无关联节点数据，查询对应标签为空");
        }
        return atlasService.getTagLabels(params);
    }

    /**
     * 关联查询节点，过滤可以展示的边种类
     */
    @GetMapping("/visibleRelation")
    public ResultVo getVisbleRelation(@RequestParam("str") String str,
                                      @RequestParam("type") String type,
                                      @RequestParam("direct") String direct) throws IOException {
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        if (!"forward".equals(direct) && !"reverse".equals(direct) && !"bothway".equals(direct)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        return atlasService.getVisibleRelation(str, type, direct);
    }

    /**
     * 侧拉框点边关系列表，过滤可以展示的边种类
     * @param str 查询点VID
     * @param type 查询点类型
     * @return
     * @throws IOException
     */
    @GetMapping("/visibleSideRelation")
    public ResultVo getVisbleSideRelation(@RequestParam("str") String str,
                                          @RequestParam("type") String type){
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(type)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }

        return atlasService.getVisibleSideRelation(str, type);
    }

    /**
     * 查询多节点之间的关联关系
     *
     * @param condition
     * @return
     */
    @PostMapping("/focusTag")
    public ResultVo getFocusTagRelation(@RequestBody List<FocusTag> condition) {

        if (CollectionUtils.isEmpty(condition) || condition.size() < 2) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }
        return atlasService.getFocusTagRelation(condition);
    }

    /**
     * 查询图探索查询记录
     * @param params
     * @return
     */
    @PostMapping("/history/query")
    public ResultVo queryAtlasHistory(@RequestBody JSONObject params) {
        try {
            return atlasService.queryAtlasHistory(params);

        } catch (Exception e) {
            logger.error("查询探索历史失败！error->", e);
            return ResultVo.fail("查询探索历史失败!");
        }
    }

    /**
     * 删除图探索查询记录
     */
    @DeleteMapping("/history/delete")
    public ResultVo deleteAtlasHistory(@RequestBody JSONObject params){
        try {
            return atlasService.deleteAtlasHistory(params);
        } catch (Exception e) {
            logger.error("删除探索历史失败！error->", e);
            return ResultVo.fail("删除探索历史失败!");
        }
    }
}

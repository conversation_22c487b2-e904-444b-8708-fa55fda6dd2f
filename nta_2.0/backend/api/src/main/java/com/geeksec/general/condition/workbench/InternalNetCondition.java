package com.geeksec.general.condition.workbench;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.entity.common.BaseCondition;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description：
 */
@Data
public class InternalNetCondition extends BaseCondition {

    /**
     * 排序字段
     */
    @JsonProperty("order_filed")
    private String orderFiled = "created_time";

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;
}

package com.geeksec.general.controller.analysis;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.condition.SessionDetailCondition;
import com.geeksec.analysis.condition.TargetRemarkCondition;
import com.geeksec.analysis.service.TargetDetailService;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.CommonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description：会话分析各项详情信息获取
 */
@RestController
@Api(tags = "目标分析详情接口")
@RequestMapping("/target")
public class TargetDetailController {

    @Autowired
    private TargetDetailService targetDetailService;

    /**
     * 会话详情查询（1 + 3）
     *
     * @param condition
     * @return
     */
    @PostMapping("/session/detail")
    @ApiOperation("会话详情查询")
    public ResultVo sessionDetail(@RequestBody SessionDetailCondition condition) {
        if (ObjectUtils.isEmpty(condition)) {
            throw new GkException(GkErrorEnum.SESSION_DETAIL_QUERY_ERROR);
        }
        return targetDetailService.getSessionDetail(condition);
    }

    @PostMapping("/addRemark")
    @ApiOperation("目标详情新增备注")
    public ResultVo addTargetRemark(@RequestBody TargetRemarkCondition condition) {
        if (ObjectUtils.isEmpty(condition)) {
            throw new GkException(GkErrorEnum.UPDATE_REMARK_ERROR);
        }
        String targetType = condition.getTargetType();
        return targetDetailService.addTargetRemark(targetType, condition.getTargetKey(), condition.getRemark());
    }

    @PostMapping("/deleteRemark")
    @ApiOperation("目标详情删除单条备注")
    public ResultVo deleteTargetRemark(@RequestBody JSONObject jsonObject) {
        CommonUtil.hasAllRequired(jsonObject, "remark_id");
        String remarkId = jsonObject.getString("remark_id");
        return targetDetailService.deleteTargetRemark(remarkId);
    }

    @PostMapping("/editTargetTag")
    @ApiOperation("修改目标标签")
    public ResultVo editTargetTag(@RequestBody JSONObject jsonObject) throws InterruptedException {
        CommonUtil.hasAllRequired(jsonObject, "lables,es_index,id");
        String esId = jsonObject.getString("id");
        List<Long> lables = (List<Long>) jsonObject.get("lables");
        String esIndex = jsonObject.getString("es_index");

        return targetDetailService.editTargetTag(esIndex, esId, lables);
    }

    @PostMapping("/black/confirm")
    @ApiOperation("确认目标为黑名单成员")
    @Deprecated
    public ResultVo targetConfirmBlack(@RequestBody JSONObject jsonObject) {
        CommonUtil.hasAllRequired(jsonObject, "target_key");
        CommonUtil.hasAllRequired(jsonObject, "target_type");

        String targetKey = jsonObject.getString("target_key");
        String targetType = jsonObject.getString("target_type");

        return targetDetailService.confirmBlack(targetKey, targetType);
    }
}

package com.geeksec.general.controller.analysis;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import com.geeksec.analysis.service.QueryHistoryService;
import com.geeksec.analysis.service.QueryTemplateService;
import com.geeksec.entity.common.BaseCondition;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * 查询模板 & 查询历史相关接口
 */
@RestController
@RequestMapping("/query")
public class QueryOperationController {

    @Autowired
    private QueryTemplateService queryTemplateService;

    @Autowired
    private QueryHistoryService queryHistoryService;

    /**
     * 通过查询条件创建对应的查询模板（通过现成的查询条件）
     *
     * @param condition
     * @return
     */
    @PostMapping("/template/create")
    public ResultVo createQueryTemplate(@RequestBody AnalysisBaseCondition condition) {

        if (condition == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }

        Integer userId = condition.getUserId();
        if (userId == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }

        List<AnalysisBaseCondition.QueryOb> queryObList = condition.getQuery();
        if (CollectionUtil.isEmpty(queryObList)) {
            throw new GkException(GkErrorEnum.CREATE_QUERY_TEMPLATE_LEAK);
        }

        return queryTemplateService.createTemplate(condition);
    }

    /**
     * 查询当前用户的查询模板列表
     */
    @PostMapping("/template/list")
    public ResultVo queryTemplateList(@RequestBody JSONObject params) {
        if (params == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }
        Integer userId = params.getInteger("user_id");
        if (userId == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }

        return queryTemplateService.queryTemplateList(params);
    }

    /**
     * 使用当前模板进行查询时，进行记录更新，更新查询的使用时间
     */
    @PutMapping("/template/update")
    public ResultVo updateTemplateUseTime(@RequestBody JSONObject params) {
        Integer templateId = params.getInteger("id");
        if (templateId == null || templateId == 0) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }
        return queryTemplateService.updateTemplateUseTime(templateId);
    }

    /**
     * 批量删除模板记录
     *
     * @return
     */
    @DeleteMapping("/template/delete")
    public ResultVo batchDeleteTemplate(@RequestBody JSONObject params) {

        List<Integer> ids = (List<Integer>) params.get("ids");
        boolean clear = params.getBoolean("clear");

        if (ids == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }

        return queryTemplateService.deleteTemplateRecord(ids, clear);
    }

    /**
     * 查询检索历史记录
     */
    @PostMapping("/history/list")
    public ResultVo searchQueryHistoryList(@RequestBody BaseCondition condition) {
        if (condition == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }
        return queryHistoryService.searchQueryHistoryList(condition);
    }

    /**
     * 批量删除检索历史记录
     * @param params
     * @return
     */
    @DeleteMapping("/history/delete")
    public ResultVo batchDeleteHistory(@RequestBody JSONObject params) {
        List<Integer> ids = (List<Integer>) params.get("ids");
        boolean clear = params.getBoolean("clear");

        if (ids == null ){
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);

        }
        return queryHistoryService.deleteQueryHistory(ids, clear);
    }
}


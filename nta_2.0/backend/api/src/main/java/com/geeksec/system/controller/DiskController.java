package com.geeksec.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.system.entity.condition.UserCondition;
import com.geeksec.util.HttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/raid")
public class DiskController {

    @Value("${send-url.base}")
    private String base;
    @Value("${send-url.raid_info}")
    private String raidInfoUrl;
    @Value("${send-url.shutdown}")
    private String shutdownUrl;
    @Value("${send-url.reboot}")
    private String rebootUrl;


    @PostMapping("/system/shutdown")
    public ResultVo shutdown(@RequestBody UserCondition condition){
        return sendPost(JSON.toJSONString(condition),shutdownUrl);
    }

    @PostMapping("/system/reboot")
    public ResultVo reboot(@RequestBody UserCondition condition){
        return sendPost(JSON.toJSONString(condition),rebootUrl);
    }
    /**
     * raid卡磁盘信息
     * @return
     */
    @PostMapping("/info")
    public ResultVo getRaidInfo(){
        return sendPostForInfo("{}", raidInfoUrl);
    }

    //需要拿到探针接口返回的业务对象
    private ResultVo sendPostForInfo(String json,String url){
        if(StringUtils.isEmpty(url)){
            throw new GkException(GkErrorEnum.RULE_SYNC_URL_EMPTY);
        }
        JSONObject resp = HttpUtils.sendPost(base+url, json);
        if(resp==null){
            throw new GkException(GkErrorEnum.RULE_SYNC_REQUEST_ERROR);
        }
        return ResultVo.success(resp.get("message"));
    }


    private ResultVo sendPost(String json,String url){
        if(StringUtils.isEmpty(url)){
            throw new GkException(GkErrorEnum.RULE_SYNC_URL_EMPTY);
        }
        JSONObject resp = HttpUtils.sendPost(base+url, json);
        if(resp==null){
            throw new GkException(GkErrorEnum.RULE_SYNC_REQUEST_ERROR);
        }
        return ResultVo.successMsg("调用成功，执行中");
    }
}

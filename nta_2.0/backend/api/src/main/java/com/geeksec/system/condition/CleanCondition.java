package com.geeksec.system.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/5/30 14:23
 * @Description： 清理数据Condition
 */
@Data
public class CleanCondition {

    @JsonProperty("user_id")
    private Integer userId;

    @JsonProperty("password")
    private String password;

    @JsonProperty("task_id")
    private Integer taskId;

    @JsonProperty("clean_list")
    private List<String> cleanList;
}

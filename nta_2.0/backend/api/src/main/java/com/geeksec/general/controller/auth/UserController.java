package com.geeksec.general.controller.auth;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.authentication.condition.user.UserCondition;
import com.geeksec.authentication.condition.user.UserQueryCondition;
import com.geeksec.authentication.entity.common.ViewResponse;
import com.geeksec.authentication.service.UserService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.authentication.util.constants.ErrorEnum;
import com.geeksec.entity.common.ResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * @author: jerryzhou
 * @description: 用户/角色/权限相关controller
 * @date: 2017/11/2 10:19
 */
@RestController
@Api(tags = "用户相关接口")
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 根据token获取用户信息以及菜单访问权限
     *
     * @param token
     * @return
     */
    @GetMapping("/info")
    public ResultVo getLoginUserInfo(@RequestParam(value = "token") String token) {

        return userService.getLoginUserInfo(token);
    }


    /**
     * 查询用户列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "根据条件查询用户列表")
    public JSONObject listUser(@RequestBody UserQueryCondition condition) {

        HashMap<String,Object> resultMap= userService.listUser(condition);

        if (resultMap.get("list") == null) {
            ViewResponse.success("查询到的用户列表为空");
        }
        return CommonUtil.successJson(resultMap);
    }

    @PostMapping("/addUser")
    @ApiOperation(value = "添加用户")
    public JSONObject addUser(@RequestBody UserCondition condition) {

        if (ObjectUtils.isEmpty(condition)){
            return CommonUtil.errorJson(ErrorEnum.E_10001);
        }
        return userService.addUser(condition);

    }

    @PostMapping("/updateUser")
    @ApiOperation(value = "修改用户")
    public JSONObject updateUser(@RequestBody UserCondition condition) {

        if (ObjectUtils.isEmpty(condition)){
            return CommonUtil.errorJson(ErrorEnum.E_10002);
        }
        return userService.updateUser(condition);
    }

    @PostMapping("/deleteUser")
    @ApiOperation(value = "删除用户")
    public JSONObject deleteUser(@RequestBody JSONObject requestJson){
        CommonUtil.hasAllRequired(requestJson, "user_ids");
        List<Long> userIds = requestJson.getObject("user_ids",List.class);
        return userService.deleteUser(userIds);
    }

}

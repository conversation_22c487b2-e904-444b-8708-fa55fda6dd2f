package com.geeksec.general.controller.auth;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.authentication.condition.role.RoleCondition;
import com.geeksec.authentication.condition.role.RoleQueryCondition;
import com.geeksec.authentication.entity.vo.RoleInfoVo;
import com.geeksec.authentication.service.RoleService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.authentication.util.constants.ErrorEnum;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@RestController
@Api(tags = "角色管理")
@RequestMapping("/roleManagement")
public class RoleController {

    private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());

    @Autowired
    private RoleService roleService;

    @PostMapping(value = "/list")
    @ApiOperation(value = "根据条件查询角色列表")
    public JSONObject getRoleList(@RequestBody RoleQueryCondition condition) {

        if (ObjectUtils.isEmpty(condition)) {
            CommonUtil.errorJson(ErrorEnum.E_90003);
        }
        PageInfo<RoleInfoVo> pageResult = roleService.listRole(condition);
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("list", pageResult.getList());
        resultMap.put("total", pageResult.getTotal());

        return CommonUtil.successJson(resultMap);
    }

    @PostMapping(value = "/allPermission")
    @ApiOperation(value = "查询所有权限信息")
    public JSONObject getAllPermissions() {
        return roleService.getAllPermissions();
    }

    @PostMapping(value = "/allRole")
    @ApiOperation(value = "查询所有角色信息")
    public JSONObject getAllRoles() {
        return roleService.getAllRoles();
    }

    @PostMapping(value = "/detail")
    @ApiOperation(value = "根据角色ID查询所拥有权限")
    public JSONObject getRoleDetail(@RequestBody JSONObject requestBody) {

        CommonUtil.hasAllRequired(requestBody, "role_id");
        Long roleId = requestBody.getLong("role_id");
        return roleService.getPermissionById(roleId);
    }

    @PostMapping(value = "/add")
    @ApiOperation(value = "添加角色与其权限")
    public JSONObject addRole(@RequestBody JSONObject requestBody) {
        CommonUtil.hasAllRequired(requestBody, "role_name,permission_list");
        return roleService.addRole(requestBody);
    }


    @PostMapping(value = "/update")
    @ApiOperation(value = "更新角色")
    public JSONObject updateRole(@RequestBody RoleCondition condition) {
        if (ObjectUtils.isEmpty(condition)) {
            CommonUtil.errorJson(ErrorEnum.E_90003);
        }
        return roleService.updateRole(condition);
    }

    @PostMapping(value = "/delete")
    @ApiOperation(value = "批量删除角色")
    public JSONObject deleteRole(@RequestBody JSONObject requestJson) {
        CommonUtil.hasAllRequired(requestJson, "role_ids");
        List<Long> userIds = requestJson.getObject("role_ids", List.class);
        return roleService.batchDeleteRoles(userIds);
    }
}

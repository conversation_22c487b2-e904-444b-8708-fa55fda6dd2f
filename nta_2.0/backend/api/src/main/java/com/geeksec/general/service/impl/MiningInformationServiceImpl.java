package com.geeksec.general.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.ThreatInfoDao;
import com.geeksec.analysis.entity.ThreatInfo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.condition.workbench.InformationCondition;
import com.geeksec.general.service.MiningInformationService;
import com.geeksec.util.CommonUtil;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: GuanHao
 * @Date: 2022/8/16 15:47
 * @Description： <Functions List>
 */
@Service
@DS("nta-db")
public class MiningInformationServiceImpl implements MiningInformationService {
    private static final Logger LOG = LoggerFactory.getLogger(MiningInformationServiceImpl.class);

    @Autowired
    ThreatInfoDao threatInfoDao;

    @Override
    public JSONObject informationList(InformationCondition condition) {
        LOG.info("查询情报列表开始：==>{}", condition);
        HashMap<String, Object> resultMap = new HashMap<>();
        Long total = 0L;

        // page
        Integer from = condition.getCurrentPage();
        Integer size = condition.getPageSize();
        PageHelper.startPage(from, size);

        // wrapper
        QueryWrapper<ThreatInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderBy(true, condition.getAsc(), "valid_from");

        List<ThreatInfo> threatInfoList;
        try {
            threatInfoList = threatInfoDao.selectList(queryWrapper);
            total = threatInfoDao.selectCount(new QueryWrapper<ThreatInfo>());
        } catch (Exception e) {
            LOG.info("情报列表查询失败！==>", e);
            throw new GkException(GkErrorEnum.THREAT_INFO_QUERY_ERROR);
        }

        // result
        resultMap.put("page", from);
        resultMap.put("size", size);
        resultMap.put("total", total);
        resultMap.put("records", threatInfoList);
        return CommonUtil.successJson(resultMap);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public JSONObject informationImport(MultipartFile file) {
        LOG.info("开始接收上传文件 ==> {}", file.getOriginalFilename());
        List<ThreatInfo> lines = new ArrayList<>();
        Map<String, Integer> fieldMap = new HashMap<String, Integer>(7);
        Set<String> fields = null;
        // read file.
        try {
            String line;
            BufferedReader br = new BufferedReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8));
            boolean isTitle = true;
            while ((line = br.readLine()) != null) {
                String[] lineArray = line.split(",");
                // 表头标记
                if (isTitle) {
                    isTitle = false;
                    fieldMap = tabLineNumber(lineArray);
                    fields = fieldMap.keySet();
                    // target 为不可缺少的字段
                    if (!fieldMap.containsKey("target")) {
                        throw new IOException("文件缺少必有字段：[ target ]");
                    }
                    continue;
                }

                ThreatInfo threatInfo;
                try {
                    threatInfo = new ThreatInfo();
                    for (String field : fields) {
                        switch (field) {
                            // 类型
                            case "target_type":
                                threatInfo.setTargetType(lineArray[fieldMap.get(field)]);
                                break;
                            // target
                            case "target":
                                String target = lineArray[fieldMap.get(field)];
                                // target 的值不可为null
                                if (ObjectUtils.isEmpty(target)) {
                                    throw new NullPointerException("target 值为null");
                                }
                                // 类型为ip的target去除端口
                                if (threatInfo.getTargetType().contains("ip")) {
                                    threatInfo.setTarget(target.split(":")[0]);
                                } else {
                                    threatInfo.setTarget(target);
                                }
                                break;
                            //source
                            case "source":
                                threatInfo.setSource(lineArray[fieldMap.get(field)]);
                                break;
                            // 标签
                            case "tag_name":
                                threatInfo.setTagName(lineArray[fieldMap.get(field)]);
                                break;
                            // 有效时间
                            case "valid_from":
                                String validFrom = lineArray[fieldMap.get(field)];
                                Date date = strStrToDate(validFrom);
                                threatInfo.setValidFrom(date);
                                break;
                            // version
                            case "version":
                                threatInfo.setVersion(lineArray[fieldMap.get(field)]);
                                break;
                            // hash
                            case "shash":
                                threatInfo.setShash(lineArray[fieldMap.get(field)]);
                                break;
                            default:
                        }
                    }
                } catch (Exception e) {
                    LOG.warn("当行值解析失败，跳过！");
                    continue;
                }
                // 添加对象入列表
                lines.add(threatInfo);

                // 写入数据库
                if (lines.size() >= 50000) {
                    threatInfoDao.insertBatch(lines);
                    lines.clear();
                }
            }
        } catch (IOException ioe) {
            LOG.error("文件：[ {} ] 读取失败 ==>。", file.getOriginalFilename(), ioe);
            throw new GkException(GkErrorEnum.FILE_READ_FAILURE);
        }

        // 写入最后一批数据
        if (lines.size() > 0) {
            threatInfoDao.insertBatch(lines);
        }

        return CommonUtil.successJson();
    }


    @Override
    public JSONObject informationExport(HttpServletResponse response, InformationCondition condition) {
        LOG.info("开始执行导出操作：{}", condition);

        List<ThreatInfo> threatInfoList;
        try {
            if (condition.isWhole()) {
                QueryWrapper<ThreatInfo> queryWrapper = new QueryWrapper<>();
                queryWrapper.orderBy(true, false, "create_time");

                Integer from = 1;
                Integer size = 10000;
                PageHelper.startPage(from, size);
                threatInfoList = threatInfoDao.selectList(queryWrapper);
            } else {
                threatInfoList = threatInfoDao.selectBatchIds(condition.getInformationList());
            }

            if (threatInfoList.size() < 1) {
                throw new NullPointerException("查询数据为空。");
            }

            // to export object
            StringBuilder exportStrB = new StringBuilder("target,target_type,tag_name,source,version,shash,valid_from\n");
            for (ThreatInfo threatInfo : threatInfoList) {
                exportStrB.append(threatInfo.getTarget()).append(",");
                exportStrB.append(threatInfo.getTargetType()).append(",");
                exportStrB.append(threatInfo.getTagName()).append(",");
                exportStrB.append(threatInfo.getSource()).append(",");
                exportStrB.append(threatInfo.getVersion()).append(",");
                exportStrB.append(threatInfo.getShash()).append(",");
                exportStrB.append(dateFormat(threatInfo.getValidFrom())).append("\n");
            }
            // download csv。
            csvDownloadFile(response, exportStrB.toString());
        } catch (Exception e) {
            LOG.error("数据库查询失败 ==>", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson();
    }


    @Override
    public JSONObject informationDelete(InformationCondition condition) {
        LOG.info("开始删除情报: {}", condition);
        // 是否全量删除
        try {
            if (condition.isWhole()) {
                threatInfoDao.truncate();
            } else {
                threatInfoDao.deleteBatchIds(condition.getInformationList());
            }
        } catch (Exception e) {
            LOG.error("删除情报失败 ==>", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson();
    }

    @Override
    public JSONObject downloadTemplate(HttpServletResponse response) {
        StringBuilder template = new StringBuilder("target,target_type,tag_name,source,version,shash,valid_from\n");
        template.append("anon-wallet.api.luckypool.io,domain,MinePool,gk_self,20220721.160617,2412474801223485,2022-07-21 08:22:41");
        try {
            csvDownloadFile(response, template.toString());
        } catch (IOException e) {
            LOG.error("模板导出失败 ==>", e);
            throw new GkException(GkErrorEnum.FILE_CREATION_FAILED);
        }

        return CommonUtil.successJson();
    }


    /**
     * 时间转换
     *
     * @param str 时间字符串
     * @return date
     * @throws ParseException
     */
    private static Date strStrToDate(String str) throws ParseException {
        // is not null.
        if (StringUtils.isEmpty(str)) {
            return null;
        }

        SimpleDateFormat dateFormat;
        Date parse = null;
        dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            parse = dateFormat.parse(str);
        } catch (ParseException e) {
            dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            parse = dateFormat.parse(str);
        }

        return parse;
    }


    /**
     * date to yyyy-MM-dd HH:mm:ss of type str
     *
     * @param date
     * @return yyyy-MM-dd HH:mm:ss of type str
     */
    private static String dateFormat(Date date) {
        if (ObjectUtils.isEmpty(date)) {
            return null;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(date);
    }


    /**
     * 记录行号
     *
     * @param lines title
     * @return
     */
    private static Map<String, Integer> tabLineNumber(String[] lines) {
        HashMap<String, Integer> map = new HashMap<>(9);
        int number = 0;
        for (String line : lines) {
            map.put(line, number);
            number++;
        }

        return map;
    }

    /**
     * 情报CSV 下载
     *
     * @param response
     * @param contents
     * @throws IOException
     */
    public static void csvDownloadFile(HttpServletResponse response, String contents) throws IOException {
        LOG.info("开始下载文件");
        // 取得文件名。
        String filename = System.currentTimeMillis() / 1000 + ".csv";
        byte[] contentBytes = contents.getBytes(StandardCharsets.UTF_8);
        // 清空response
        response.reset();
        // 设置response的Header,给文件名进行utf-8编码,不然下载的时候文件名乱码不对
        response.addHeader("Content-Disposition", URLEncoder.encode(filename, "UTF-8"));
        response.setCharacterEncoding("UTF-8");
        try (OutputStream toClient = new BufferedOutputStream(response.getOutputStream(), 10240)){
            response.addHeader("Content-Length", "" + contentBytes.length);
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Expose-Headers", "*");
            toClient.write(contentBytes);
        } catch (Exception e) {
            throw new IOException("文件下载错误==> ", e);
        }

    }
}

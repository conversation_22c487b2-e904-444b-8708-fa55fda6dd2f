package com.geeksec.general.service;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.general.condition.workbench.InformationCondition;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/8/16 15:06
 * @Description： <Functions List>
 */
public interface MiningInformationService {

    /**
     * 情报列表查询
     *
     * @return 情报列表
     */
    JSONObject informationList(InformationCondition condition);

    /**
     * 情报导入
     *
     * @return 导入状态
     */
    JSONObject informationImport(MultipartFile file);

    /**
     * 情报导出
     *
     * @param condition 条件
     * @return 导出状态
     */
    JSONObject informationExport(HttpServletResponse response, InformationCondition condition);

    /**
     * 情报删除
     *
     * @param condition 条件
     * @return 删除状态
     */
    JSONObject informationDelete(InformationCondition condition);

    /**
     * download template
     *
     * @param response
     * @return
     */
    JSONObject downloadTemplate(HttpServletResponse response);
}

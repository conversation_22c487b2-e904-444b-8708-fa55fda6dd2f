package com.geeksec.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.geeksec.analysis.dao.SystemInfoDao;
import com.geeksec.analysis.entity.vo.ProductInfoVo;
import com.geeksec.analysis.entity.vo.SystemInfoVo;
import com.geeksec.authentication.dao.UserDao;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.authentication.util.Md5Util;
import com.geeksec.constants.LoggerString;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.push.dao.normal.DiskFieldDao;
import com.geeksec.push.dao.normal.DiskTypeDao;
import com.geeksec.push.entity.DiskField;
import com.geeksec.push.entity.DiskType;
import com.geeksec.system.condition.CleanCondition;
import com.geeksec.system.service.SystemService;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.HttpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 系统类接口实现类
 *
 * @author: GuanHao
 * @createTime: 2022/3/9 9:35
 */
@Service
@DS("push-db")
public class SystemServiceImpl implements SystemService {
    private static final Logger logger = LoggerFactory.getLogger(SystemServiceImpl.class);

    /**
     * 数据清理的Key
     */
    private static final List<String> cleanKey = Arrays.asList("conf", "filter", "rule", "pcap", "PbSession", "SSL", "HTTP", "DNS", "log", "cert");

    /**
     * 宿主请求方式
     */
    private static final String POST = "POST";

    /**
     * 调用外部服务，关机的URL
     */
    @Value("${external-system-url.shutdown}")
    private String urlShutdown = null;

    /**
     * 调用外部服务，重启的URL
     */
    @Value("${external-system-url.reboot}")
    private String urlReboot = null;

    /**
     * 调用外部服务，获取磁盘信息的URL
     */
    @Value("${external-system-url.disk-data}")
    private String urlDisk = null;

    /**
     * 调用外部服务，重启的URL
     */
    @Value("${external-system-url.clean-data}")
    private String urlClanData = null;

    /**
     * 调用外部服务，系统重置接口
     */
    @Value("${external-system-url.system-reset}")
    private String urlSystemReset;

    /**
     * 数据清理状态查询地址
     */
    @Value("${external-system-url.clean-data-status}")
    private String urlCleanDataStatus;

    /**
     * 更新磁盘
     */
    @Value("${external-system-url.disk-change}")
    private String urlDiskChange;

    /**
     * 重组磁盘
     */
    @Value("${external-system-url.disk-rebuild}")
    private String urlDiskRebuild;

    /**
     * 动态库文件检测
     */
    @Value("${external-system-url.check-so}")
    private String urlCheckSo;

    /**
     * Docker 动态库文件检测
     */
    @Value("${external-system-url.docker-check-so}")
    private String urlDockerCheckSo;

    /**
     * 磁盘重组状态检测
     */
    @Value("${external-system-url.disk-check}")
    private String urlDiskCheck;

    /**
     * 磁盘挂载准备
     */
    @Value("${external-system-url.disk-ready-mount}")
    private String urlMountReady;

    /**
     * 磁盘挂载
     */
    @Value("${external-system-url.disk-mount-data}")
    private String urlMountData;

    /**
     * database: td_auth table:tb_user
     */
    @Autowired(required = false)
    UserDao userDao;

    @Autowired
    SystemInfoDao systemInfoDao;

    @Autowired
    DiskTypeDao diskTypeDao;

    @Autowired
    DiskFieldDao diskFieldDao;

    @Autowired
    TokenService tokenService;

    @Override
    public JSONObject shutdown() {
        logger.info(LoggerString.LOG_EXECUTOR, "shutdown");

        String response;
        // 发送请求
        try {
            // 修改tb_valset值，保证其发送请求后为关机操作,关机为0，重启为1
            systemInfoDao.modifyShutdownValue(0);
            response = HttpUtils.sendRequest(urlShutdown, POST);
        } catch (IOException e) {
            logger.error(LoggerString.LOG_EXCEPTION, "shutdown", e.getMessage());

            throw new GkException(GkErrorEnum.FAIL);
        }

        // 处理返回结果
        JSONObject responseJson = JSONObject.parseObject(response);
        String status = String.valueOf(responseJson.get("status"));
        if ("false".equals(status) || "null".equals(status)) {
            String message = responseJson.getOrDefault("message", "null").toString();
            logger.error(LoggerString.LOG_EXCEPTION, "shutdown", message);

            throw new GkException(GkErrorEnum.FAIL);
        } else {
            return CommonUtil.successJson();
        }

    }


    @Override
    public JSONObject reboot() {
        logger.info(LoggerString.LOG_EXECUTOR, "reboot");

        String response;
        // 发送请求
        try {
            // 修改tb_valset值，保证其发送请求后为重启操作,关机为0，重启为1
            systemInfoDao.modifyShutdownValue(1);
            response = HttpUtils.sendRequest(urlReboot, POST);
        } catch (IOException e) {
            logger.error(LoggerString.LOG_EXCEPTION, "reboot", e.getMessage());

            throw new GkException(GkErrorEnum.FAIL);
        }

        // 处理返回结果
        JSONObject responseJson = JSONObject.parseObject(response);
        String status = String.valueOf(responseJson.get("status"));
        if ("false".equals(status) || "null".equals(status)) {
            String message = responseJson.getOrDefault("message", "null").toString();
            logger.error(LoggerString.LOG_EXCEPTION, "reboot", message);

            throw new GkException(GkErrorEnum.FAIL);
        } else {
            return CommonUtil.successJson();
        }

    }


    @Override
    public JSONObject changePassword(String userName, String password) {
        logger.info(LoggerString.LOG_CHANGE_PASSWORD, userName);

        String passwordMD5 = Md5Util.encodeMd5(password);
        int i = userDao.updatePassword(userName, passwordMD5);

        // 成功返回值为1，失败返回值为0
        if (i == 0) {
            logger.error(LoggerString.LOG_PASSWORD_EXCEPTION, userName);
            throw new GkException(GkErrorEnum.FAIL);
        }

        // 清理token
        tokenService.invalidateToken();

        logger.info(LoggerString.LOG_PASSWORD_SUCCESS, userName, password);
        return CommonUtil.successJson();
    }


    @Override
    public JSONObject getDiskInfoData() {
        logger.info(LoggerString.LOG_EXECUTOR, "get disk message");

        String response;
        // 发送请求
        try {
            response = HttpUtils.sendRequest(urlDisk, POST);
        } catch (IOException e) {
            logger.error(LoggerString.LOG_EXCEPTION, "get disk message", e.getMessage());

            throw new GkException(GkErrorEnum.FAIL);
        }
        JSONObject resultData = JSONObject.parseObject(response);

        // 返回状态判断
        String status = String.valueOf(resultData.get("status"));
        if ("false".equals(status) || "null".equals(status)) {
            logger.error(LoggerString.LOG_EXCEPTION, "get disk message", status);

            throw new GkException(GkErrorEnum.FAIL);
        }

        logger.info("Disk get message successfully.");
        return CommonUtil.successJson(resultData);
    }

    @Override
    public SystemInfoVo getSystemInfo() {
        SystemInfoVo systemInfo = null;
        try {
            systemInfo = systemInfoDao.getSystemInfo();
            if (systemInfo != null){
                systemInfo.setTime(getSystemUptimeSeconds());
                systemInfo.setStartTime(getSystemStartTime());
            }
        } catch (Exception e) {
            logger.error("系统信息查询失败:{}", e);
        }

        return systemInfo;
    }

    @Override
    public ProductInfoVo getProductInfo() {
        ProductInfoVo productInfo = null;
        try {
            productInfo = systemInfoDao.getProductInfo();
        } catch (Exception e) {
            logger.error("产品信息查询失败:{}", e);
        }

        return productInfo;
    }

    /**
     * 获取系统运行时长（秒）
     * @return 系统运行时长（秒）
     */
    public static long getSystemUptimeSeconds() {
        try (BufferedReader reader = new BufferedReader(new FileReader("/proc/uptime"))) {
            String line = reader.readLine();
            if (line != null) {
                String[] parts = line.split("\\s+");
                return Long.parseLong(parts[0].split("\\.")[0]); // 取整数部分
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return -1; // 获取失败
    }

    /**
     * 计算系统启动时间
     * @return 系统启动时间
     */
    public static LocalDateTime getSystemStartTime() {
        long uptimeSeconds = getSystemUptimeSeconds();
        if (uptimeSeconds == -1) {
            throw new RuntimeException("无法获取系统运行时长");
        }

        // 当前时间减去运行时长得到系统启动时间
        return LocalDateTime.ofInstant(Instant.now().minusSeconds(uptimeSeconds), ZoneId.systemDefault());
    }

    @Override
    public JSONObject cleanData(CleanCondition condition) {
        logger.info("即将清理数据：", condition);
        long startTime = System.currentTimeMillis();

        HashMap<String, Object> requestMap = new HashMap<>();

        // 请求参数准备
        Integer taskId = condition.getTaskId();
        requestMap.put("task_id", taskId);
        List<String> cleanList = condition.getCleanList();
        for (String clean : cleanKey) {
            if (cleanList.contains(clean)) {
                requestMap.put(clean, true);
            } else {
                requestMap.put(clean, false);
            }
        }

        // 发送请求
        JSONObject response;
        try {
            String param = JSON.toJSONString(requestMap);
            response = HttpUtils.sendPost(urlClanData, param);
        } catch (Exception e) {
            logger.error("调用外部服务删除数据失败: {}", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        //判断响应状态
        String status = response.getOrDefault("status", "null").toString();
        if (status.equals("null") || status.equals("false")) {
            logger.error("外部服务删除数据失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        logger.info("数据清理成功，用时：{}秒", (System.currentTimeMillis() - startTime) / 1000);
        return CommonUtil.successJson();
    }

    @Override
    public JSONObject systemReset(JSONObject json) {
        logger.info("尝试重置系统操作：{}", json);
        JSONObject responseJson;

        // 调用外部服务
        try {
            String response = HttpUtils.sendRequest(urlSystemReset, "POST");
            responseJson = JSONObject.parseObject(response);
        } catch (Exception e) {
            logger.error("试图调用外部服务重置系统失败：{}", e);
            throw new GkException(GkErrorEnum.FAIL);
        }
        // 获取返回值失败!
        String status = responseJson.getOrDefault("status", "null").toString();
        if (status.equals("null") || status.equals("false")) {
            logger.error("外部服务重置系统失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson();
    }

    @Override
    public JSONObject cleanDataSchedule() {
        JSONObject responseJson;
        // 调用外部服务
        try {
            String response = HttpUtils.sendRequest(urlCleanDataStatus, "POST");
            responseJson = JSONObject.parseObject(response);
        } catch (Exception e) {
            logger.error("试图调用外部服务查询数据清理状态：",e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        String status = responseJson.getOrDefault("status", "null").toString();
        if ("null".equals(status) || "false".equals(status)) {
            logger.error("外部服务查询清理状态失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(responseJson);
    }

    @Override
    public JSONObject diskChange() {
        logger.info("尝试进行磁盘更新操作。");
        // 在数据库中记录一条
        DiskType diskType = new DiskType();
        diskType.setStartTime(System.currentTimeMillis() / 1000);
        diskType.setState(0);
        diskType.setType(1);
        try {
            diskTypeDao.insert(diskType);
        } catch (Exception e) {
            logger.error("数据库中记录重组信息失败==> ", e);
            throw new GkException(GkErrorEnum.FAIL);
        }
        JSONObject responseJson;
        // 调用外部服务
        try {
            String response = HttpUtils.sendRequest(urlDiskChange, "POST");
            responseJson = JSONObject.parseObject(response);
        } catch (Exception e) {
            logger.error("试图调用外部服务更新磁盘：{}", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        String status = responseJson.getOrDefault("status", "null").toString();
        if (status.equals("null") || status.equals("false")) {
            logger.error("试图调用外部服务更新磁盘失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return shutdown();
    }

    @Override
    public JSONObject diskRebuild() {
        logger.info("试图执行重组磁盘操作。");

        // 检测接口
        JSONObject responseJson;
        try {
            String response = HttpUtils.sendRequest(urlDiskCheck, "POST");
            responseJson = JSONObject.parseObject(response);
        } catch (Exception e) {
            logger.error("试图调用外部服务检测磁盘重组失败：{}", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        // 调用外部服务
        String status;
        try {
            String response = HttpUtils.sendRequest(urlDiskRebuild, "POST");
            responseJson = JSONObject.parseObject(response);
        } catch (Exception e) {
            logger.error("试图调用外部服务重组磁盘：{}", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        status = responseJson.getOrDefault("status", "null").toString();
        if (status.equals("null") || status.equals("false")) {
            logger.error("试图调用外部服务重组磁盘失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(responseJson);
    }

    @Override
    public JSONObject diskMountReady() {
        logger.info("尝试进行磁盘挂载准备操作。");
        // 数据库记录
        DiskType diskType = new DiskType();
        diskType.setStartTime(System.currentTimeMillis() / 1000);
        diskType.setState(0);
        diskType.setType(2);
        try {
            diskTypeDao.insert(diskType);
        } catch (Exception e) {
            logger.error("数据库中记录磁盘挂载准备信息失败==> ", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        // 调用外部服务
        JSONObject responseJson;
        try {
            String responseStr = HttpUtils.sendRequest(urlMountReady, "POST");
            responseJson = JSONObject.parseObject(responseStr);
        } catch (Exception e) {
            logger.error("试图调用外部服务进行磁盘挂载准备失败==> ", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        String status = String.valueOf(responseJson.get("status"));
        if (status.equals("null") || status.equals("false")) {
            logger.error("外部服务进行磁盘挂载准备失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(responseJson);
    }

    @Override
    public JSONObject diskMountData() {
        logger.info("尝试进行磁盘挂载操作。");
        JSONObject responseJson;
        // 调用外部服务
        try {
            String responseStr = HttpUtils.sendRequest(urlMountData, "POST");
            responseJson = JSONObject.parseObject(responseStr);
        } catch (Exception e) {
            logger.error("试图调用外部服务进行磁盘挂载失败==> ", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        String status = String.valueOf(responseJson.get("status"));
        if (status.equals("null") || status.equals("false")) {
            logger.error("外部服务进行磁盘挂载失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(responseJson);
    }

    @Override
    public JSONObject checkSo(Integer ruleId) {
        logger.info("尝试进行动态库文件检测操作：{}", ruleId);
        JSONObject responseJson;
        // 调用外部服务
        try {
            responseJson = HttpUtils.sendPost(urlCheckSo, ruleId.toString());
        } catch (Exception e) {
            logger.error("试图调用外部服务进行动态库文件检测==> ", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        String status = String.valueOf(responseJson.get("status"));
        if (status.equals("null") || status.equals("false")) {
            logger.error("外部服务进行动态库文件检测失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(responseJson);
    }

    @Override
    public JSONObject dockerCheckSo(String path) {
        logger.info("尝试进行Docker动态库文件检测：{}", path);
        JSONObject responseJson;
        // 调用外部服务
        try {
            responseJson = HttpUtils.sendPost(urlDockerCheckSo, path);
        } catch (Exception e) {
            logger.error("试图调用外部服务进行Docker动态库文件检测==> ", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        String status = String.valueOf(responseJson.get("status"));
        if (status.equals("null") || status.equals("false")) {
            logger.error("外部服务进行Docker动态库文件检测失败！");
            throw new GkException(GkErrorEnum.FAIL);
        }

        return CommonUtil.successJson(responseJson);
    }

    @Override
    public JSONObject checkDiskStatus() {
        DiskType diskType = null;
        try {
            diskType = diskTypeDao.getOrderIdOne();
        } catch (Exception e) {
            logger.error("查询磁盘重组状态失败==> ", e);
            throw new GkException(GkErrorEnum.FAIL);
        }

        // false 表示已完成，true表示重组中
//        boolean isRun = !ObjectUtils.isEmpty(diskType) && diskType.getState() == 0;
        return CommonUtil.successJson(diskType);
    }

    @Override
    public JSONObject getDiskField() {
        try {
            DiskField diskField = diskFieldDao.selectOne(new QueryWrapper<DiskField>().eq("id", 1));

            String fieldName;
            if (diskField == null) {
                fieldName = "读盘模式";
            } else {
                int field = diskField.getField();
                if (field == 0) {
                    fieldName = "读盘模式";
                } else if (field == 1) {
                    fieldName = "换盘模式";
                } else {
                    logger.error("数据库磁盘模式字段错误！");
                    throw new GkException(GkErrorEnum.UPLOAD_FILE_FAIL);
                }
            }

            return CommonUtil.successJson(fieldName);
        } catch (Exception e) {
            logger.error("查询磁盘展示字段失败 ==>", e);
            throw new GkException(GkErrorEnum.UPLOAD_FILE_FAIL);
        }
    }
}

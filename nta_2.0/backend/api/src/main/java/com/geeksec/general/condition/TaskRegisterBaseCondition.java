package com.geeksec.general.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.analysis.entity.condition.AnalysisBaseCondition;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/5/24 18:59
 * @Description： <Functions List>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TaskRegisterBaseCondition extends AnalysisBaseCondition {

    @JsonProperty("data_key")
    private String dataKey;

    @JsonProperty("field_names")
    private List<String> fieldNames;
}

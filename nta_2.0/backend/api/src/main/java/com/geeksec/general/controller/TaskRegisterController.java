package com.geeksec.general.controller;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.condition.TaskRegisterCondition;
import com.geeksec.analysis.entity.DownloadTaskRegister;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.service.TaskRegisterService;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.ArrayList;

/**
 * @Author: GuanHao
 * @Date: 2022/5/19 14:52
 * @Description： <Functions List>
 */
@RestController
@Api(tags = "下载任务注册相关接口")
@RequestMapping("/task/register")
public class TaskRegisterController {
    private static final Logger LOG = LoggerFactory.getLogger(TaskRegisterController.class);

    @Autowired
    TaskRegisterService taskRegisterService;

    @PostMapping("/create")
    @ApiOperation("下载任务创建")
    public JSONObject createTask(@RequestBody TaskRegisterCondition condition) {
        Integer userId = condition.getUserId();
        Integer taskType = condition.getTaskType();
        JSONObject conditionQuery = condition.getCondition();
        ArrayList<Integer> taskId = (ArrayList<Integer>) conditionQuery.getOrDefault("task_id", null);

        // 这两项参数不允许为null
        if (userId == null || taskType == null || taskId == null || taskId.size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        // 当taskType为1时，query中不能存在data_key
        if (taskType == 1 && conditionQuery.containsKey("data_key")) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        // query字段参数检测
        CommonUtil.hasAllRequired(conditionQuery, "order_field");
        // 补充默认值
        conditionQuery.put("page_size", -1);
        conditionQuery.putIfAbsent("current_page", 1);
        conditionQuery.putIfAbsent("asc", true);

        return taskRegisterService.taskRegister(condition);
    }

    @PostMapping("/search/list")
    @ApiOperation("下载列表查询")
    public JSONObject searchList(@RequestBody TaskRegisterCondition condition) {
        return taskRegisterService.searchList(condition);
    }

    @PostMapping("/delete")
    @ApiOperation("任务删除接口")
    public JSONObject deleteRecord(@RequestBody JSONObject json) {
        Integer id = json.getInteger("id");
        if (id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        return taskRegisterService.deleteTask(id);
    }

    @PostMapping("/download")
    @ApiOperation("数据下载接口")
    public JSONObject downloadData(@RequestBody JSONObject json, HttpServletResponse response, HttpServletRequest request) {
        LOG.info("下载任务id为:[ {} ]的文件。", json);
        long startTime = System.currentTimeMillis();
        // 参数效验
        Integer id = json.getInteger("id");
        if (id == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        // 获取地址
        DownloadTaskRegister register = taskRegisterService.downloadData(id);

        // 数据库查询失败 || path == null
        if (ObjectUtils.isEmpty(register) || ObjectUtils.isEmpty(register.getPath()) || register.getType() != 3) {
            LOG.error("数据下载失败，数据库数据异常, id:[ {} ] ", id);
            throw new GkException(GkErrorEnum.FAIL);
        }

        // 获取文件
        String path = register.getPath();
        File file = new File(path);
        if (file.exists() && file.isFile()) {
            // 上传文件至下载列表
            try {
                FileUtil.csvDownloadFile(response, file);
            } catch (Exception e) {
                response.setContentType("application/json;charset-uft-8");
                throw new GkException(GkErrorEnum.FILE_DOWNLOAD_FAIL);
            }
        } else {
            LOG.error("文件路径不存在或不是一个文件，任务id为[ {} ]", id);
            throw new GkException(GkErrorEnum.FILE_DOWNLOAD_FAIL);
        }

        LOG.info("下载任务结束，用时:{}秒", (System.currentTimeMillis() - startTime) / 1000);
        return CommonUtil.successJson();
    }
}

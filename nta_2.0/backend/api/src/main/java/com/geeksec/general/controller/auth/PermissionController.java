package com.geeksec.general.controller.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.geeksec.authentication.dao.RoleDao;
import com.geeksec.authentication.entity.dto.menu.MenuDto;
import com.geeksec.authentication.service.LoginService;
import com.geeksec.authentication.service.TokenService;
import com.geeksec.authentication.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.FileNotFoundException;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：API转发接口权限进行权限判断
 */
@RestController
@RequestMapping("/permission")
public class PermissionController {

    @Autowired
    private LoginService loginService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RoleDao roleDao;

    @RequestMapping("/importPermission")
    public void importPer() throws FileNotFoundException {
        String jsonString = JsonUtils.readJsonFile("/Users/<USER>/GeekSec/geeksec-api-auth/src/main/resources/template.json");

        JSONArray jsonArray = JSON.parseArray(jsonString);
        List<MenuDto> list = jsonArray.toJavaList(MenuDto.class);

        roleDao.batchInsertMenuInfo(list);
    }
}

package com.geeksec.general.controller;

import com.geeksec.entity.common.ResultVo;
import com.geeksec.general.service.AppDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
@RestController
@Api(tags = "应用字典接口")
public class AppDictController {

    @Autowired
    private AppDictService appDictService;

    @GetMapping("/dict")
    @ApiOperation(value = "查询应用字典")
    public ResultVo<Map<String, Object>> getAppDict() throws IOException {
        Map<String,Object> result = appDictService.getAppDict();
        return ResultVo.success(result);
    }

    @GetMapping("/dict/alarm")
    @ApiOperation(value = "查询告警字典")
    public ResultVo<List<Map<String, Object>>> getAlarmDict(){
        List<Map<String, Object>> result = appDictService.getAlarmDict();
        return ResultVo.success(result);
    }
}

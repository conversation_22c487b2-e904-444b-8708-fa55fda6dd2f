package com.geeksec.general.controller.task;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.FlowUpCondition;
import com.geeksec.analysis.entity.condition.TaskAnalysisCondition;
import com.geeksec.analysis.entity.condition.TaskConfigEditCondition;
import com.geeksec.analysis.entity.vo.TaskEditVo;
import com.geeksec.analysis.service.TaskConfigService;
import com.geeksec.authentication.util.CommonUtil;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.util.HttpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@RestController
@Api(tags = "工作台-任务配置信息接口")
@RequestMapping("/task/config")
@Log4j2
public class TaskConfigController {

    @Autowired
    private TaskConfigService taskConfigService;
    @Value("${send-url.task}")
    private String taskUrl;
    @Value("${send-url.rules}")
    private String rulesUrl;
    @Value("${send-url.base}")
    private String base;

    @Autowired
    private ThreadPoolTaskExecutor myExecutor;

    @ApiOperation("查询任务详情信息")
    @PostMapping("/info")
    public JSONObject configTaskInfo(@RequestBody JSONObject jsonObject){

        CommonUtil.hasAllRequired(jsonObject,"task_id");
        Integer taskId = jsonObject.getInteger("task_id");
        return CommonUtil.successJson(taskConfigService.configTaskInfo(taskId));
    }

    @ApiOperation("工作台-任务配置")
    @PostMapping("/edit")
    public ResultVo configTaskEdit(@RequestBody TaskConfigEditCondition condition){
        if (!StringUtils.hasText(condition.getSystem())) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }
        taskConfigService.configTaskEdit(condition);
        List<Integer> endList = new ArrayList<>();
        endList.add(condition.getTaskId());
        endList.add(condition.getBatchId());
        sendPost(JSONObject.toJSONString(endList), rulesUrl);
        return ResultVo.successMsg("探针任务规则同步成功");
    }

    @GetMapping("/info")
    @ApiOperation("工作台-任务的基本情况")
    public ResultVo<List<TaskEditVo>> taskEditVoList(){
        return ResultVo.success(taskConfigService.taskEditVoList());
    }

    @ApiOperation("工作台-任务关联网口")
    @PutMapping("/analysis")
    public ResultVo updateAnalysis(@RequestBody List<TaskAnalysisCondition> conditions){
        if(errorCode == 1 || errorCode == 3){
            return ResultVo.success("探针配置正在同步中");
        }
        synchronized(this){
            //同步失败和静止状态 才可以继续
            if(errorCode == 1 || errorCode == 3){
                return ResultVo.success("探针配置正在同步中");
            }
            errorCode=3;
            try {
                ResultVo vo = taskConfigService.updateAnalysis(conditions);
                if(vo == null || vo.getErr() != 0){
                    errorCode=4;
                    return vo;
                }
            }catch (Exception e){
                errorCode=4;
                log.error("工作台-任务关联网口异常e={}",e);
                throw new GkException(GkErrorEnum.TASK_RELATED_NETFLOW_ERROR);
            }
            
            myExecutor.submit(()->{
                JSONObject resp = HttpUtils.sendPost(base + taskUrl, "{}");
                if(resp==null){
                    errorCode=2;
                }else{
                    errorCode=1;
                }
            });
            return ResultVo.successMsg("重启探针请求发送成功");
        }

    }
    //  1：同步成功  2:同步失败 3:同步中  4:静止状态
    private Integer errorCode = 4;

    @ApiOperation("工作台-任务关联网口-查询同步状态")
    @GetMapping("/analysis/code")
    //defaultValue 随便写了个值
    public ResultVo getAnalysisStatus(@RequestParam(defaultValue = "1") Integer suc){
        if(suc != null && 4 == suc){
            errorCode = 4;
            return ResultVo.successEnum(GkErrorEnum.E_60004);
        }
        if(errorCode == 2){
            //探针失败、这里修改到 4 禁止
            errorCode = 4;
            return ResultVo.successEnum(GkErrorEnum.E_60002);
        }
        if(errorCode == 1){
            return ResultVo.successEnum(GkErrorEnum.E_60001);
        }
        if(errorCode == 4){
            return ResultVo.successEnum(GkErrorEnum.E_60004);
        }
        return ResultVo.successEnum(GkErrorEnum.E_60003);
    }

    @ApiOperation("工作台-网口信息修改")
    @PutMapping("/flow/name")
    public ResultVo updateFlowName(@RequestBody List<FlowUpCondition> conditions){
        if (errorCode == 1 || errorCode == 3) {
            return ResultVo.success("探针配置正在同步中");
        }
        synchronized (this) {
            //同步失败和静止状态 才可以继续
            if (errorCode == 1 || errorCode == 3) {
                return ResultVo.success("探针配置正在同步中");
            }
            errorCode = 3;
            try {
                ResultVo vo = taskConfigService.updateFlowName(conditions);
                if(vo == null || vo.getErr() != 0){
                    errorCode=4;
                    return vo;
                }
            } catch (Exception e) {
                errorCode = 4;
                log.error("工作台-网口信息修改->", e);
                throw new GkException(GkErrorEnum.TASK_RELATED_NETFLOW_ERROR);
            }

            myExecutor.submit(() -> {
                JSONObject resp = HttpUtils.sendPost(base + taskUrl, "{}");
                if (resp == null) {
                    errorCode = 2;
                } else {
                    errorCode = 1;
                }
            });
            return ResultVo.successMsg("重启探针请求发送成功");
        }

    }

    private void sendPost(String json, String url) {
        if (StrUtil.isEmpty(url)) {
            throw new GkException(GkErrorEnum.RULE_SYNC_URL_EMPTY);
        }

        JSONObject resp = HttpUtils.sendPost(base + url, json);
        if (JSONUtil.isNull(resp)) {
            throw new GkException(GkErrorEnum.RULE_SYNC_REQUEST_ERROR);
        }
    }
}

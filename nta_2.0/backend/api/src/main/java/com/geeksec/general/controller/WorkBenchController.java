package com.geeksec.general.controller;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.analysis.entity.condition.AddInternalNetCondition;
import com.geeksec.analysis.entity.condition.CommunicationCondition;
import com.geeksec.analysis.entity.condition.UpdateInternalNetCondition;
import com.geeksec.entity.common.ResultVo;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.condition.workbench.CommuListCondition;
import com.geeksec.general.condition.workbench.InternalNetCondition;
import com.geeksec.general.service.WorkBenchService;
import com.geeksec.push.entity.vo.TaskStatisticBpsVo;
import com.geeksec.util.CommonUtil;
import com.geeksec.util.HttpUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description：
 */
@RestController
@Api(tags = "工作台态势接口")
@RequestMapping("/workbench")
public class WorkBenchController {

    private static Logger logger = LoggerFactory.getLogger(WorkBenchController.class);

    @Value("${send-url.rules}")
    private String url;

    @Value("${send-url.base}")
    private String base;

    @Autowired
    private WorkBenchService workBenchService;


    //此接口只用于首页  因为很多参数没有开发
    @ApiOperation("查询通信通信（IP态势图）")
    @PostMapping("/communication/list")
    public JSONObject communicationList(@RequestBody CommuListCondition condition) {

        if (ObjectUtils.isEmpty(condition) || condition.getTaskId() == null || condition.getTaskId().size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        //HashMap<String, Object> result = workBenchService.getCommuList(condition);
        HashMap<String, Object> result = workBenchService.getCommuListNew(condition);

        if (result.get("error") != null) {
            return CommonUtil.errorJson(Integer.valueOf(result.get("error").toString()), result.get("msg").toString());
        }
        return CommonUtil.successJson(result);
    }

    //复制上面接口的逻辑  把会话分析的公共逻辑板块继承进来
    @ApiOperation("查询通信通信（IP态势图）会话分析使用")
    @PostMapping("/communication/list2")
    public ResultVo communicationList2(@RequestBody CommunicationCondition condition) {
        if (ObjectUtils.isEmpty(condition) || condition.getTaskId() == null || condition.getTaskId().size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        ResultVo commuList2 = workBenchService.getCommuList2(condition);
        return commuList2;
    }

    // 挖矿项目单独聚合Dns解析IP版本
    @ApiOperation("查询通信信息 （IP态势图） 挖矿项目使用")
    @PostMapping("/communication/mine/list")
    public JSONObject communicationList3(@RequestBody CommuListCondition condition) {
        if (ObjectUtils.isEmpty(condition) || condition.getTaskId() == null || condition.getTaskId().size() < 1) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        HashMap<String, Object> result = workBenchService.getCommuList3(condition);
        if (result.get("error") != null) {
            return CommonUtil.errorJson(Integer.valueOf(result.get("error").toString()), result.get("msg").toString());
        }
        return CommonUtil.successJson(result);
    }

    @ApiOperation("矿池IP列表（挖矿分析平台使用）")
    @GetMapping("/pool/list")
    public ResultVo poolIpList() {
        HashMap<String, Object> result = workBenchService.getPoolIpList();
        if (CollectionUtils.isEmpty(result)) {
            return ResultVo.success("矿池IP列表为空");
        } else {
            return ResultVo.success(result);
        }
    }


    @ApiOperation("查询内网IP网段列表")
    @PostMapping("/internal/list")
    public ResultVo<HashMap<String, Object>> internalIpList(@RequestBody InternalNetCondition condition) {
        if (condition.getSortOrder() == null) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        HashMap<String, Object> result = workBenchService.getInternalNetList(condition);
        return ResultVo.success(result);
    }

    @ApiOperation("添加内网MAC_IP网段信息")
    @PostMapping("/internal/add")
    public ResultVo addIntraInfo(@RequestBody AddInternalNetCondition condition) {
        if (ObjectUtils.isEmpty(condition)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }

        workBenchService.addInternalNetInfo(condition);
        List<Integer> sendParams = new ArrayList<>();

        // 添加完成后调用同步脚本
        sendParams.add(condition.getTaskId());
        sendParams.add(condition.getBatchId());
        return sendPost(JSONObject.toJSONString(sendParams));
    }

    @ApiOperation("根据IDs删除内网IP网段信息")
    @PostMapping("/internal/delete")
    public ResultVo deleteInterInfo(@RequestBody JSONObject requestJson) {
        CommonUtil.hasAllRequired(requestJson, "ids");
        CommonUtil.hasAllRequired(requestJson, "task_id");
        CommonUtil.hasAllRequired(requestJson, "batch_id");

        List<Integer> ids = requestJson.getObject("ids", List.class);
        boolean success = workBenchService.deleteInterInfoByIds(ids);
        if (success) {
            logger.info("删除内网IP网段信息成功");
            List<Integer> sendParams = new ArrayList<>();

            // 添加完成后调用同步脚本
            sendParams.add(requestJson.getInteger("task_id"));
            sendParams.add(requestJson.getInteger("batch_id"));
            return sendPost(JSONObject.toJSONString(sendParams));
        } else {
            return ResultVo.fail("删除内网IP网段信息失败");
        }
    }

    @ApiOperation("根据ID更新内网IP网段信息")
    @PostMapping("/internal/update")
    public ResultVo updateInterInfo(@RequestBody UpdateInternalNetCondition condition) {
        if (ObjectUtils.isEmpty(condition) && condition.getId() <= 0) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        String ipAddr = condition.getInterIp();
        boolean success = workBenchService.updateInternalNetInfo(condition);
        if (success) {
            logger.info("更新内网IP网段信息成功");
            List<Integer> sendParams = new ArrayList<>();
            // 添加完成后调用同步脚本
            sendParams.add(condition.getTaskId());
            sendParams.add(condition.getBatchId());
            return sendPost(JSONObject.toJSONString(sendParams));
        } else {
            return ResultVo.fail("更新内网IP网段信息失败");
        }
    }


    @GetMapping("/recent/flow")
    @ApiOperation("工作台:近日流量情况")
    public ResultVo<List<TaskStatisticBpsVo>> getBpsForRecently(@RequestParam("days") Integer days,
                                                                @RequestParam("task_id") Integer taskId) {
        if (days == null || taskId == null)
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        List<TaskStatisticBpsVo> bpsForRecently = workBenchService.getBpsForRecently(days, taskId);
        return ResultVo.success(bpsForRecently);
    }

    private ResultVo sendPost(String json) {
        if (StringUtils.isEmpty(url)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_EMPTY);
        }
        JSONObject resp = HttpUtils.sendPost(base + url, json);
        if (resp == null) {
            throw new GkException(GkErrorEnum.RULE_SYNC_REQUEST_ERROR);
        }
        return ResultVo.successMsg("探针同步成功");
    }
}

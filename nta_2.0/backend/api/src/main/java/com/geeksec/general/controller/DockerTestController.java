package com.geeksec.general.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.geeksec.entity.common.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

@RestController
@Slf4j
public class DockerTestController {

    @Value("${user.name}")
    private String name;

    @GetMapping("/docker/test")
    public ResultVo<JSONObject> tt(){
        JSONArray list = new JSONArray();
        try {
            BufferedReader in = new BufferedReader(new FileReader("/opt/local/config/test.txt"));
            String str;
            while ((str = in.readLine()) != null) {
                log.info("读取每行,str={}",str);
                list.add(str);
            }
            if(StringUtils.isNotBlank(str)){
                list.add(str);
            }
        } catch (IOException e) {
        }
        JSONObject json = new JSONObject();
        json.put("list",list);
        json.put("name",name);
        return ResultVo.success(json);
    }
}

package com.geeksec.general.controller;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.enumeration.GkErrorEnum;
import com.geeksec.exception.GkException;
import com.geeksec.general.condition.workbench.InformationCondition;
import com.geeksec.general.service.MiningInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @Author: GuanHao
 * @Date: 2022/8/16 15:04
 * @Description： <Functions List>
 */
@RestController
@Api(tags = "应用字典接口")
@RequestMapping("/information")
public class MiningInformationController {

    @Autowired
    MiningInformationService informationService;


    @PostMapping("/list")
    @ApiOperation("情报列表查询")
    public JSONObject informationList(@RequestBody InformationCondition condition) {
        return informationService.informationList(condition);
    }

    @PostMapping("/upload")
    @ApiOperation("情报导入")
    public JSONObject informationImport(@RequestParam("file") MultipartFile file) {
        // 判断是否是CSV文件
        String[] fileNameArray = Objects.requireNonNull(file.getOriginalFilename()).split("\\.");
        if (!"csv".equals(fileNameArray[fileNameArray.length - 1])) {
            throw new GkException(GkErrorEnum.FILE_CONTENT_FORMAT_ILLEGAL);
        }

        try {
            // 过滤空文件
            byte[] bytes = file.getBytes();
            if (ObjectUtils.isEmpty(bytes) || bytes.length < 1) {
                throw new IOException("文件为空");
            }
        } catch (IOException e) {
            throw new GkException(GkErrorEnum.FILE_READ_FAILURE);
        }
        return informationService.informationImport(file);
    }

    @PostMapping("/export")
    @ApiOperation("情报导出")
    public JSONObject informationExport(HttpServletResponse response,@RequestBody InformationCondition condition) {
        List<Integer> list = condition.getInformationList();
        // 不是全量时，list 不允许为空或元素个数小于1
        if (!condition.isWhole() && (ObjectUtils.isEmpty(list) || list.size() < 1)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }

        return informationService.informationExport(response, condition);
    }


    @PostMapping("/delete")
    @ApiOperation("情报删除")
    public JSONObject informationDelete(@RequestBody InformationCondition condition) {
        List<Integer> list = condition.getInformationList();
        // 不是全量时，list 不允许为空或元素个数小于1
        if (!condition.isWhole() && (ObjectUtils.isEmpty(list) || list.size() < 1)) {
            throw new GkException(GkErrorEnum.REQUEST_PARAM_LEAK);
        }
        return informationService.informationDelete(condition);
    }

    @PostMapping("/template")
    @ApiOperation("模板下载")
    public JSONObject downloadTemplate(HttpServletResponse response) {
        return informationService.downloadTemplate(response);
    }

}

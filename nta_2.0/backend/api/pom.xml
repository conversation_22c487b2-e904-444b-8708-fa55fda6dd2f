<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>nta-backend</artifactId>
        <version>2.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>api</artifactId>

    <dependencies>
        <!-- 通用模块 -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>common</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <!-- 登陆鉴权模块 -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>auth</artifactId>
            <version>2.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- 分析系统模块 -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>analysis</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <!-- ES查询模块 -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>es-search</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

        <!-- 推送模块 -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>ws</artifactId>
            <version>2.0.3-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>ngbatis</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>

    </dependencies>

</project>

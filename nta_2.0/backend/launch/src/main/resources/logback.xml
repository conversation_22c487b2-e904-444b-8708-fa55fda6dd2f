<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds">

    <contextName>geeksec-be</contextName>

    <!--定义日志文件的存储地址 -->
    <property name="LOG_HOME" value="logs"/>

    <!--定义日志的输出格式 -->
    <property name="PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss} %-5level [%X{traceId}][%X{username}] [%thread] %logger{30}:%line - %msg%n"/>

    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss} %yellow([%X{traceId}][%X{username}]) %highlight(%-5level) %magenta(%logger{30}:%-3L) - %m%n"/>

    <!-- console日志格式 精简版 -->
    <property name="CONSOLE_LOG_PATTERN_SIMPLE"
              value="%d{HH:mm:ss} %highlight(%-5level) %magenta(%logger{20}:%-3L) - %m%n"/>

    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <!--encoder 默认配置为PatternLayoutEncoder-->
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="RollingFile"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/nta_backend_%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <maxHistory>90</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <appender name="AsyncRollingFile" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="RollingFile"/>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- 输出到控制台和文件，可定义更多的 Appender -->
    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="AsyncRollingFile"/>
    </root>

    <!-- 下面配置一些第三方包的日志过滤级别，用于避免刷屏 -->
    <logger name="org.mybatis" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="com.geeksec.*" level="WARN"/>
    <logger name="com.geeksec.*.dao" level="INFO"/>

</configuration>

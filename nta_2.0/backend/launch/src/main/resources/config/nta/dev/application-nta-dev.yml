elasticsearch:
  hosts: ***************:9200

spring:
  datasource:
    dynamic:
      primary: analysis-db  #设置默认的数据源或者数据源组,默认值即为auth-db
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        auth-db: #用户鉴权模块数据源
          url: ***************************************************************************************************************************************************
          username: root
          password: simpleuse23306p
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            max-lifetime: 28740000
        analysis-db: #探针业务数据源
          url: *******************************************************************************************************************************************************
          username: root
          password: simpleuse23306p
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            max-lifetime: 28740000
        push-db: #websocket推送数据源
          url: *********************************************************************************************************************************************************
          username: root
          password: simpleuse23306p
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            max-lifetime: 28740000
  redis:
    #数据库索引
    database: 0
    host: 127.0.0.1
    port: 6379
    password:
    jedis:
      pool:
        #最大连接数
        max-active: 8
        #最大阻塞等待时间(负数表示没限制)
        max-wait: -1
        #最大空闲
        max-idle: 8
        #最小空闲
        min-idle: 0
    timeout: 10000
  kafka:
    bootstrap-servers: ***************:9092
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      linger:
        ms: 0


# hbase 数据库配置
hbase:
  config:
    hbase:
      zookeeper:
        quorum: ***************
        property:
          clientPort: 2181


external-system-url:
  # 探针 关闭主机
  shutdown: http://***************:59000/system/shutdown
  # 探针 重启主机
  reboot: http://***************:59000/system/reboot
  #  探针 磁盘信息
  disk-data: http://***************:59000/system/disk_data
  # 探针 清理数据
  clean-data: http://***************:59000/system/clean_task_data
  # 探针 系统重置
  system-reset: http://***************:59000/thd/reset
  # 探针 清理进度查询
  clean-data-status: http://***************:59000/system/clean_task_status
  # 探针 更新磁盘
  disk-change: http://***************:59000/system/fdisk_change
  # 探针 重组磁盘
  disk-rebuild: http://***************:59000/system/fdisk_rebliud
  # 探针 动态库文件检测
  check-so: http://***************:59000/thd/check_so
  # 探针 docker 动态库文件检测
  docker-check-so: http://***************:59000/thd/docker_check_so
  # 磁盘重组检测
  disk-check: http://***************:59000/system/fdisk_check
  # 准备挂载磁盘
  disk-ready-mount: http://***************:59000/system/ready_mount
  # 挂载磁盘
  disk-mount-data: http://***************:59000/system/mount_data
  # PB文件查询
  pb-file: http://***************:59000/system/pb

file:
  #本地测试 需要添加相应的测试地址  文件临时地址
  tmp-path: /Users/<USER>/Desktop/screen/
  #本地测试 需要添加 会话分析  下载pcap 的生成目录
  pcap-download-path: /Users/<USER>/Desktop/screen/
  #本地测试 需要添加 会话分析  下载csv 的生成目录
  session-path: /Users/<USER>/Desktop/screen/
  #本地测试 目前是过滤规则  特征规则 有两个模板文件 存放地址
  template-path: /Users/<USER>/Desktop/screen/
  #pcap文件存放目录
  pcap-path: /Users/<USER>/Desktop/offline_thd/pcap_upload/
  #服务器导入pcap文件存放目录
  server-pcap-path: /Users/<USER>/Desktop/offline_thd/pcap_source/
  #数据导入-读取服务器文件接口
  list-files-url: http://127.0.0.1:5000/offline/listServerPath
  #数据导入-获取路径是否存在接口
  check-files-url: http://127.0.0.1:5000/offline/checkFilePaths
  #数据导入-删除批次下的pcap文件
  delete-batch-pcaps: http://127.0.0.1:5000/offline/deletePcapFiles
  #数据导入-停止批次对应的探针实例
  stop-batch-offline_thd: http://127.0.0.1:5000/offline/stopOfflineThd

send-url:
  #探针 地址前缀
  base: http://***************:59000
  #探针  同步任务的规则的url
  rules: /thd/rule_syn
  #探针  探针底层配置同步
  task: /thd/task_check

  ##########磁盘相关接口
  #raid卡磁盘信息
  raid_info: /system/raid_info
  #关机
  shutdown: /system/shutdown
  #重启
  reboot: /system/reboot
  #动态库文件检测
  check_so: /thd/docker_check_so
  #告警报告导出
  alarm_report_export: http://***************:37777/probe_pdf_result

query:
  #查询限制：聚合或者导出的前提数据量小于  100W  测试环境设置的10W
  es_limit: 100000

nebula:
  ngbatis:
    # ^v1.1.2
    # 连接使用 nebula-java 中的 SessionPool
    use-session-pool: true
  # 填入 graphd 的 ip 和端口号，下面仅供参考
  hosts: ***************:9669
  # 连接图数据库所用的用户名
  username: root
  # 连接图数据库所用的密码
  password: nebula
  # 所要连接的图数据库图空间名
  space: gs_analysis_graph
  # 连接池配置
  pool-config:
    # 连接池中最小空闲连接数
    min-conns-size: 0
    # 连接池中最大空闲连接数
    max-conns-size: 10
    # 客户端同服务端建立连接的超时时间设置，单位为 ms；超过设定时间未建立起连接，则报错
    timeout: 0
    # 连接空闲时间，为 0 表示连接永不删除，单位为 ms
    idle-time: 0
    # 连接池检测空闲连接的时间间隔，为 -1 表示不进行检测
    interval-idle: -1
    # 连接等候时间，超过则不再等候连接
    wait-time: 120
    # 集群允许最小的服务可用率，1.0 表示为所有机器 graphd 可用，0.25 表示集群中 1/4 机器可用即可
    min-cluster-health-rate: 1.0
    # 是否允许 SSL 连接，目前暂不支持
    enable-ssl: false
    max_sessions_per_ip_per_user: 2000
# 防止与mybatis冲突，单独配置mapper扫描路径
cql:
  parser:
    mapperLocations: classpath*:repository/*.xml

# 在控制台打印nGQL
logging:
  level:
    org.nebula.contrib: DEBUG

static:
  disk-field: 更换磁盘
  token: geeksec-static-token

#挖矿需求，首页关系图的Label id集合
mining:
  labels: 20036,20048

mybatis-plus:
  mapper-locations: classpath*:mapper/*/*.xml
  global-config:
    banner: false
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl #关闭sql日志
lmdb:
  path: /data/lmdb/
  max-size: 1099511627776
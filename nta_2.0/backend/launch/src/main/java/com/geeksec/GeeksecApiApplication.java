package com.geeksec;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description：
 */

@RestController
@EnableScheduling
@EnableAutoConfiguration(exclude = {MultipartAutoConfiguration.class})
@SpringBootApplication(scanBasePackages = { "com.geeksec","org.nebula.contrib"})
@MapperScan("com.geeksec.*.dao")
public class GeeksecApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(GeeksecApiApplication.class, args);
    }

}

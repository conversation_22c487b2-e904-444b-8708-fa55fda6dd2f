#!/bin/bash

# 批量更新flink-jobs项目中的import语句
echo "开始更新import语句..."

# 更新所有Java文件中的import语句
find /home/<USER>/nta3.0/flink-jobs -name "*.java" -type f -exec sed -i 's/import com\.geeksec\.common/import com.geeksec.flink.common/g' {} \;

echo "import语句更新完成！"

# 显示更新后的import语句数量
echo "更新后的import语句数量："
find /home/<USER>/nta3.0/flink-jobs -name "*.java" -type f -exec grep -l "import com.geeksec.flink.common" {} \; | wc -l

echo "检查是否还有遗漏的旧import语句："
find /home/<USER>/nta3.0/flink-jobs -name "*.java" -type f -exec grep -l "import com.geeksec.common" {} \; | wc -l
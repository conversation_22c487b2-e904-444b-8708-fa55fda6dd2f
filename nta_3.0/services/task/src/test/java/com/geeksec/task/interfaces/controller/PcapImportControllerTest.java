package com.geeksec.task.interfaces.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.task.application.service.ImportTaskScheduler;
import com.geeksec.task.application.service.PcapImportManagerService;
import com.geeksec.task.application.service.ServiceStatusManager;
import com.geeksec.task.application.service.TaskBatchManagerService;
import com.geeksec.task.model.dto.BatchOperationResultDto;
import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import com.geeksec.task.model.vo.ImportServiceStatusVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * PCAP 导入控制器测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@WebMvcTest(PcapImportController.class)
class PcapImportControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private PcapImportManagerService pcapImportManagerService;

    @MockBean
    private ImportTaskScheduler importTaskScheduler;

    @MockBean
    private ServiceStatusManager serviceStatusManager;

    @MockBean
    private TaskBatchManagerService taskBatchManagerService;

    private PcapImportRequestDto testRequest;
    private PcapImportResultDto testResult;

    @BeforeEach
    void setUp() {
        testRequest = new PcapImportRequestDto();
        testRequest.setTaskId(1001);
        testRequest.setBatchId(2001);
        testRequest.setInputPaths(Arrays.asList("/test/file1.pcap", "/test/file2.pcap"));
        testRequest.setBatchDescription("测试批次");
        testRequest.setFullflowState("OFF");
        testRequest.setFlowlogState("ON");

        testResult = PcapImportResultDto.success(1, "test-service", 1001, 2001, 
            Arrays.asList("/test/file1.pcap", "/test/file2.pcap"));
    }

    @Test
    void testSubmitImportTask_Success() throws Exception {
        // 模拟服务返回成功结果
        when(importTaskScheduler.submitImportTask(any(PcapImportRequestDto.class)))
            .thenReturn(testResult);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.taskId").value(1001))
                .andExpect(jsonPath("$.data.batchId").value(2001));
    }

    @Test
    void testSubmitImportTask_Failure() throws Exception {
        // 模拟服务返回失败结果
        PcapImportResultDto failureResult = PcapImportResultDto.failure("队列已满", "任务队列容量不足");
        when(importTaskScheduler.submitImportTask(any(PcapImportRequestDto.class)))
            .thenReturn(failureResult);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("队列已满"));
    }

    @Test
    void testSubmitImportTask_ValidationError() throws Exception {
        // 创建无效请求（缺少必需字段）
        PcapImportRequestDto invalidRequest = new PcapImportRequestDto();
        // 不设置 taskId 和 batchId

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/submit")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testProcessImportTask_Success() throws Exception {
        // 模拟服务返回成功结果
        when(pcapImportManagerService.processImportRequest(any(PcapImportRequestDto.class)))
            .thenReturn(testResult);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/process")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true));
    }

    @Test
    void testGetAllServiceStatus() throws Exception {
        // 准备测试数据
        List<ImportServiceStatusVo> statusList = Arrays.asList(
            ImportServiceStatusVo.createIdleService(1, "service-1"),
            ImportServiceStatusVo.createActiveService(2, "service-2", 1001, 2001)
        );

        when(serviceStatusManager.getAllServiceStatus()).thenReturn(statusList);

        // 执行请求
        mockMvc.perform(get("/api/v1/pcap-import/services/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].serviceId").value(1))
                .andExpect(jsonPath("$.data[0].status").value("IDLE"))
                .andExpect(jsonPath("$.data[1].serviceId").value(2))
                .andExpect(jsonPath("$.data[1].status").value("ACTIVE"));
    }

    @Test
    void testGetServiceStatus() throws Exception {
        // 准备测试数据
        ImportServiceStatusVo status = ImportServiceStatusVo.createIdleService(1, "service-1");
        when(serviceStatusManager.getServiceStatus(1)).thenReturn(status);

        // 执行请求
        mockMvc.perform(get("/api/v1/pcap-import/services/1/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.serviceId").value(1))
                .andExpect(jsonPath("$.data.status").value("IDLE"));
    }

    @Test
    void testGetServiceStatus_NotFound() throws Exception {
        // 模拟服务不存在
        when(serviceStatusManager.getServiceStatus(999)).thenReturn(null);

        // 执行请求
        mockMvc.perform(get("/api/v1/pcap-import/services/999/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("服务实例不存在：999"));
    }

    @Test
    void testStopService() throws Exception {
        // 模拟停止服务成功
        when(serviceStatusManager.stopService(1)).thenReturn(true);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/services/1/stop"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("服务实例停止成功"));
    }

    @Test
    void testRestartService() throws Exception {
        // 模拟重启服务成功
        when(serviceStatusManager.createOrRestartService(1)).thenReturn(true);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/services/1/restart"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("服务实例重启成功"));
    }

    @Test
    void testGetSchedulerStatus() throws Exception {
        // 准备测试数据
        ImportTaskScheduler.SchedulerStatus status = new ImportTaskScheduler.SchedulerStatus(
            true, 5, 2, 10, 1, 100L);
        when(importTaskScheduler.getSchedulerStatus()).thenReturn(status);

        // 执行请求
        mockMvc.perform(get("/api/v1/pcap-import/scheduler/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.running").value(true))
                .andExpect(jsonPath("$.data.pendingTaskCount").value(5))
                .andExpect(jsonPath("$.data.processingTaskCount").value(2));
    }

    @Test
    void testStartScheduler() throws Exception {
        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/scheduler/start"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务调度器启动成功"));
    }

    @Test
    void testStopScheduler() throws Exception {
        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/scheduler/stop"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务调度器停止成功"));
    }

    @Test
    void testCancelImportTask() throws Exception {
        // 模拟取消任务成功
        when(importTaskScheduler.cancelImportTask(1001, 2001)).thenReturn(true);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/tasks/1001/batches/2001/cancel"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务取消成功"));
    }

    @Test
    void testGetPendingTasks() throws Exception {
        // 准备测试数据
        List<PcapImportRequestDto> pendingTasks = Arrays.asList(testRequest);
        when(importTaskScheduler.getPendingTasks()).thenReturn(pendingTasks);

        // 执行请求
        mockMvc.perform(get("/api/v1/pcap-import/tasks/pending"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].taskId").value(1001));
    }

    @Test
    void testClearTaskQueue() throws Exception {
        // 模拟清空队列
        when(importTaskScheduler.clearTaskQueue()).thenReturn(5);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/tasks/clear"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务队列清空成功"))
                .andExpect(jsonPath("$.data").value(5));
    }

    @Test
    void testStopBatch() throws Exception {
        // 准备测试数据
        BatchOperationResultDto result = BatchOperationResultDto.success(2001, 1001, "STOP", "批次停止成功");
        when(taskBatchManagerService.stopBatch(2001)).thenReturn(result);

        // 执行请求
        mockMvc.perform(post("/api/v1/pcap-import/batches/2001/stop"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.success").value(true))
                .andExpect(jsonPath("$.data.batchId").value(2001));
    }

    @Test
    void testGetBatchProgress() throws Exception {
        // 模拟批次进度
        when(taskBatchManagerService.getBatchProgress(2001)).thenReturn(0.75);

        // 执行请求
        mockMvc.perform(get("/api/v1/pcap-import/batches/2001/progress"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(0.75));
    }

    @Test
    void testIsBatchCompleted() throws Exception {
        // 模拟批次完成状态
        when(taskBatchManagerService.isBatchCompleted(2001)).thenReturn(true);

        // 执行请求
        mockMvc.perform(get("/api/v1/pcap-import/batches/2001/completed"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }
}

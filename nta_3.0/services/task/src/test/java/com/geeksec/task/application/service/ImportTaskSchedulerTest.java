package com.geeksec.task.application.service;

import com.geeksec.task.application.service.impl.ImportTaskSchedulerImpl;
import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 导入任务调度器测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class ImportTaskSchedulerTest {

    @Mock
    private PcapImportManagerService pcapImportManagerService;

    @InjectMocks
    private ImportTaskSchedulerImpl importTaskScheduler;

    private PcapImportRequestDto testRequest;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(importTaskScheduler, "maxQueueSize", 10);
        ReflectionTestUtils.setField(importTaskScheduler, "schedulerEnabled", true);
        
        // 初始化调度器
        importTaskScheduler.init();
        
        // 创建测试请求
        testRequest = new PcapImportRequestDto();
        testRequest.setTaskId(1001);
        testRequest.setBatchId(2001);
        testRequest.setInputPaths(Arrays.asList("/test/file1.pcap", "/test/file2.pcap"));
        testRequest.setBatchDescription("测试批次");
    }

    @Test
    void testSubmitImportTask_Success() {
        // 执行测试
        PcapImportResultDto result = importTaskScheduler.submitImportTask(testRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals("任务已提交到调度队列", result.getMessage());
        assertEquals(testRequest.getTaskId(), result.getTaskId());
        assertEquals(testRequest.getBatchId(), result.getBatchId());
        
        // 验证队列中有任务
        assertEquals(1, importTaskScheduler.getPendingTaskCount());
    }

    @Test
    void testSubmitImportTask_SchedulerNotRunning() {
        // 停止调度器
        importTaskScheduler.stopScheduler();

        // 执行测试
        PcapImportResultDto result = importTaskScheduler.submitImportTask(testRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertEquals("任务调度器未运行", result.getMessage());
    }

    @Test
    void testSubmitImportTask_QueueFull() {
        // 填满队列
        for (int i = 0; i < 10; i++) {
            PcapImportRequestDto request = new PcapImportRequestDto();
            request.setTaskId(1000 + i);
            request.setBatchId(2000 + i);
            request.setInputPaths(Arrays.asList("/test/file" + i + ".pcap"));
            importTaskScheduler.submitImportTask(request);
        }

        // 尝试提交额外的任务
        PcapImportResultDto result = importTaskScheduler.submitImportTask(testRequest);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.getSuccess());
        assertTrue(result.getMessage().contains("任务队列已满"));
    }

    @Test
    void testGetPendingTaskCount() {
        // 提交几个任务
        importTaskScheduler.submitImportTask(testRequest);
        
        PcapImportRequestDto request2 = new PcapImportRequestDto();
        request2.setTaskId(1002);
        request2.setBatchId(2002);
        request2.setInputPaths(Arrays.asList("/test/file3.pcap"));
        importTaskScheduler.submitImportTask(request2);

        // 验证队列大小
        assertEquals(2, importTaskScheduler.getPendingTaskCount());
    }

    @Test
    void testGetProcessingTaskCount() {
        // 初始状态应该没有正在处理的任务
        assertEquals(0, importTaskScheduler.getProcessingTaskCount());
    }

    @Test
    void testStartStopScheduler() {
        // 测试停止调度器
        importTaskScheduler.stopScheduler();
        assertFalse(importTaskScheduler.isRunning());

        // 测试启动调度器
        importTaskScheduler.startScheduler();
        assertTrue(importTaskScheduler.isRunning());
    }

    @Test
    void testGetSchedulerStatus() {
        // 提交一个任务
        importTaskScheduler.submitImportTask(testRequest);

        // 获取调度器状态
        ImportTaskScheduler.SchedulerStatus status = importTaskScheduler.getSchedulerStatus();

        // 验证状态
        assertNotNull(status);
        assertTrue(status.isRunning());
        assertEquals(1, status.getPendingTaskCount());
        assertEquals(0, status.getProcessingTaskCount());
        assertEquals(0, status.getCompletedTaskCount());
        assertEquals(0, status.getFailedTaskCount());
    }

    @Test
    void testCancelImportTask() {
        // 提交任务
        importTaskScheduler.submitImportTask(testRequest);
        assertEquals(1, importTaskScheduler.getPendingTaskCount());

        // 取消任务
        boolean cancelled = importTaskScheduler.cancelImportTask(testRequest.getTaskId(), testRequest.getBatchId());

        // 验证结果
        assertTrue(cancelled);
        assertEquals(0, importTaskScheduler.getPendingTaskCount());
    }

    @Test
    void testCancelImportTask_TaskNotFound() {
        // 尝试取消不存在的任务
        boolean cancelled = importTaskScheduler.cancelImportTask(9999, 8888);

        // 验证结果
        assertFalse(cancelled);
    }

    @Test
    void testGetPendingTasks() {
        // 提交几个任务
        importTaskScheduler.submitImportTask(testRequest);
        
        PcapImportRequestDto request2 = new PcapImportRequestDto();
        request2.setTaskId(1002);
        request2.setBatchId(2002);
        request2.setInputPaths(Arrays.asList("/test/file3.pcap"));
        importTaskScheduler.submitImportTask(request2);

        // 获取待处理任务
        List<PcapImportRequestDto> pendingTasks = importTaskScheduler.getPendingTasks();

        // 验证结果
        assertNotNull(pendingTasks);
        assertEquals(2, pendingTasks.size());
        
        // 验证任务内容
        assertTrue(pendingTasks.stream().anyMatch(task -> 
            task.getTaskId().equals(testRequest.getTaskId()) && 
            task.getBatchId().equals(testRequest.getBatchId())));
        assertTrue(pendingTasks.stream().anyMatch(task -> 
            task.getTaskId().equals(1002) && 
            task.getBatchId().equals(2002)));
    }

    @Test
    void testClearTaskQueue() {
        // 提交几个任务
        for (int i = 0; i < 5; i++) {
            PcapImportRequestDto request = new PcapImportRequestDto();
            request.setTaskId(1000 + i);
            request.setBatchId(2000 + i);
            request.setInputPaths(Arrays.asList("/test/file" + i + ".pcap"));
            importTaskScheduler.submitImportTask(request);
        }

        assertEquals(5, importTaskScheduler.getPendingTaskCount());

        // 清空队列
        int clearedCount = importTaskScheduler.clearTaskQueue();

        // 验证结果
        assertEquals(5, clearedCount);
        assertEquals(0, importTaskScheduler.getPendingTaskCount());
    }

    @Test
    void testProcessTaskQueue() {
        // 模拟有可用的服务实例
        when(pcapImportManagerService.getRunningTaskCount()).thenReturn(0);
        when(pcapImportManagerService.getMaxConcurrentTasks()).thenReturn(3);
        when(pcapImportManagerService.processImportRequest(any(PcapImportRequestDto.class)))
            .thenReturn(PcapImportResultDto.success(1, "test-service", 1001, 2001, Arrays.asList("/test/file.pcap")));

        // 提交任务
        importTaskScheduler.submitImportTask(testRequest);
        assertEquals(1, importTaskScheduler.getPendingTaskCount());

        // 手动触发任务处理
        importTaskScheduler.processTaskQueue();

        // 验证任务被处理
        verify(pcapImportManagerService, times(1)).processImportRequest(any(PcapImportRequestDto.class));
    }
}

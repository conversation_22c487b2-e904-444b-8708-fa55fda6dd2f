# Task Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8086}
  servlet:
    context-path: /task

# 任务服务特定配置
task:
  # 测试环境启用定时任务调度
  enabled:
    task_scheduled: true
  
  # 测试环境文件路径配置
  file:
    tmp-path: ${TASK_TMP_PATH:/data/nta-test/tmp/}
    pcap-download-path: ${TASK_PCAP_DOWNLOAD_PATH:/data/nta-test/download/pcap/}
    session-path: ${TASK_SESSION_PATH:/data/nta-test/download/logs/}
    template-path: ${TASK_TEMPLATE_PATH:/data/nta-test/template/}
    pcap-path: ${TASK_PCAP_PATH:/data/nta-test/import/pcap/}
    server-pcap-path: ${TASK_SERVER_PCAP_PATH:/data/nta-test/pcapfiles/}
  
  # 测试环境外部服务URL配置
  external:
    list-files-url: ${IMPORT_SERVICE_URL:http://test-import.nta.local:5000}/offline/listServerPath
    check-files-url: ${IMPORT_SERVICE_URL:http://test-import.nta.local:5000}/offline/checkFilePaths
    delete-batch-pcaps: ${IMPORT_SERVICE_URL:http://test-import.nta.local:5000}/offline/deletePcapFiles
    stop-batch-offline-thd: ${IMPORT_SERVICE_URL:http://test-import.nta.local:5000}/offline/stopOfflineThd
  
  # 测试环境调度配置
  scheduler:
    pool-size: 10
    thread-name-prefix: task-test-
    await-termination-seconds: 60
    wait-for-tasks-to-complete-on-shutdown: true
  
  # 测试环境任务配置
  jobs:
    # 数据清理任务
    cleanup:
      enabled: true
      cron: "0 0 3 * * ?"  # 每天凌晨3点执行
      retention-days: 30  # 测试环境保留30天
    # 数据同步任务
    sync:
      enabled: true
      cron: "0 */5 * * * ?"  # 每5分钟执行一次
      batch-size: 500

# 日志配置
logging:
  level:
    com.geeksec.task: INFO
    org.springframework.scheduling: INFO

# Task Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8086}
  servlet:
    context-path: /task

# 任务服务特定配置
task:
  # 生产环境启用定时任务调度
  enabled:
    task_scheduled: true
  
  # 生产环境文件路径配置
  file:
    tmp-path: ${TASK_TMP_PATH:/data/nta-prod/tmp/}
    pcap-download-path: ${TASK_PCAP_DOWNLOAD_PATH:/data/nta-prod/download/pcap/}
    session-path: ${TASK_SESSION_PATH:/data/nta-prod/download/logs/}
    template-path: ${TASK_TEMPLATE_PATH:/data/nta-prod/template/}
    pcap-path: ${TASK_PCAP_PATH:/data/nta-prod/import/pcap/}
    server-pcap-path: ${TASK_SERVER_PCAP_PATH:/data/nta-prod/pcapfiles/}
  
  # 生产环境外部服务URL配置
  external:
    list-files-url: ${IMPORT_SERVICE_URL:http://prod-import.nta.local:5000}/offline/listServerPath
    check-files-url: ${IMPORT_SERVICE_URL:http://prod-import.nta.local:5000}/offline/checkFilePaths
    delete-batch-pcaps: ${IMPORT_SERVICE_URL:http://prod-import.nta.local:5000}/offline/deletePcapFiles
    stop-batch-offline-thd: ${IMPORT_SERVICE_URL:http://prod-import.nta.local:5000}/offline/stopOfflineThd
  
  # 生产环境调度配置
  scheduler:
    pool-size: 20
    thread-name-prefix: task-prod-
    await-termination-seconds: 120
    wait-for-tasks-to-complete-on-shutdown: true
  
  # 生产环境任务配置
  jobs:
    # 数据清理任务
    cleanup:
      enabled: true
      cron: "0 0 1 * * ?"  # 每天凌晨1点执行
      retention-days: 90  # 生产环境保留90天
    # 数据同步任务
    sync:
      enabled: true
      cron: "0 */2 * * * ?"  # 每2分钟执行一次
      batch-size: 2000
  
  # 生产环境性能监控
  monitoring:
    enable-task-metrics: true
    enable-execution-time-tracking: true
    slow-task-threshold: 300000  # 慢任务阈值5分钟
  
  # 生产环境故障恢复
  recovery:
    enable-auto-retry: true
    max-retry-attempts: 3
    retry-delay: 60000  # 重试延迟1分钟

# 日志配置
logging:
  level:
    com.geeksec.task: INFO
    org.springframework.scheduling: WARN

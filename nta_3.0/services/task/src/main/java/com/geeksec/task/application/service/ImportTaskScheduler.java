package com.geeksec.task.application.service;

import com.geeksec.task.model.dto.PcapImportRequestDto;
import com.geeksec.task.model.dto.PcapImportResultDto;

import java.util.List;

/**
 * 导入任务调度器接口
 * 替代原来的 Kafka 消息监听，实现任务的调度和分发
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface ImportTaskScheduler {

    /**
     * 提交导入任务
     * 
     * @param request 导入请求
     * @return 提交结果
     */
    PcapImportResultDto submitImportTask(PcapImportRequestDto request);

    /**
     * 获取待处理的任务队列大小
     * 
     * @return 队列大小
     */
    int getPendingTaskCount();

    /**
     * 获取正在处理的任务数量
     * 
     * @return 处理中的任务数量
     */
    int getProcessingTaskCount();

    /**
     * 启动任务调度器
     */
    void startScheduler();

    /**
     * 停止任务调度器
     */
    void stopScheduler();

    /**
     * 检查调度器是否正在运行
     * 
     * @return 是否正在运行
     */
    boolean isRunning();

    /**
     * 获取调度器状态信息
     * 
     * @return 状态信息
     */
    SchedulerStatus getSchedulerStatus();

    /**
     * 取消指定的导入任务
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @return 取消是否成功
     */
    boolean cancelImportTask(Integer taskId, Integer batchId);

    /**
     * 获取所有待处理的任务
     * 
     * @return 待处理任务列表
     */
    List<PcapImportRequestDto> getPendingTasks();

    /**
     * 清空任务队列
     * 
     * @return 清空的任务数量
     */
    int clearTaskQueue();

    /**
     * 调度器状态信息
     */
    class SchedulerStatus {
        private boolean running;
        private int pendingTaskCount;
        private int processingTaskCount;
        private int completedTaskCount;
        private int failedTaskCount;
        private long totalProcessedTasks;
        private String lastProcessTime;

        // 构造函数
        public SchedulerStatus() {}

        public SchedulerStatus(boolean running, int pendingTaskCount, int processingTaskCount,
                             int completedTaskCount, int failedTaskCount, long totalProcessedTasks) {
            this.running = running;
            this.pendingTaskCount = pendingTaskCount;
            this.processingTaskCount = processingTaskCount;
            this.completedTaskCount = completedTaskCount;
            this.failedTaskCount = failedTaskCount;
            this.totalProcessedTasks = totalProcessedTasks;
        }

        // Getters and Setters
        public boolean isRunning() { return running; }
        public void setRunning(boolean running) { this.running = running; }

        public int getPendingTaskCount() { return pendingTaskCount; }
        public void setPendingTaskCount(int pendingTaskCount) { this.pendingTaskCount = pendingTaskCount; }

        public int getProcessingTaskCount() { return processingTaskCount; }
        public void setProcessingTaskCount(int processingTaskCount) { this.processingTaskCount = processingTaskCount; }

        public int getCompletedTaskCount() { return completedTaskCount; }
        public void setCompletedTaskCount(int completedTaskCount) { this.completedTaskCount = completedTaskCount; }

        public int getFailedTaskCount() { return failedTaskCount; }
        public void setFailedTaskCount(int failedTaskCount) { this.failedTaskCount = failedTaskCount; }

        public long getTotalProcessedTasks() { return totalProcessedTasks; }
        public void setTotalProcessedTasks(long totalProcessedTasks) { this.totalProcessedTasks = totalProcessedTasks; }

        public String getLastProcessTime() { return lastProcessTime; }
        public void setLastProcessTime(String lastProcessTime) { this.lastProcessTime = lastProcessTime; }
    }
}

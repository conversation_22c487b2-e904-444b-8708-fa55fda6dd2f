package com.geeksec.task.interfaces.controller;

import com.geeksec.task.application.service.MinioFileService;
import com.geeksec.task.model.vo.FileTreeNodeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PCAP 文件管理控制器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/pcap-files")
@RequiredArgsConstructor
@Validated
@Tag(name = "PCAP文件管理", description = "PCAP文件的浏览、检查、删除等操作")
public class PcapFileController {

    private final MinioFileService minioFileService;

    /**
     * 查询服务器文件路径
     */
    @GetMapping("/listServerPath")
    @Operation(summary = "查询服务器文件路径", description = "浏览MinIO存储桶中的PCAP文件")
    public ResponseEntity<List<FileTreeNodeVo>> listServerPath(
            @Parameter(description = "目录路径", required = false)
            @RequestParam(value = "dir", required = false, defaultValue = "") String directoryPath) {
        try {
            List<FileTreeNodeVo> result = minioFileService.listServerPath(directoryPath);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("查询服务器文件路径失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 检查文件路径是否存在
     */
    @PostMapping("/checkFilePaths")
    @Operation(summary = "检查文件路径", description = "检查MinIO中指定文件路径是否存在")
    public ResponseEntity<Map<String, Object>> checkFilePaths(
            @Parameter(description = "文件路径检查请求", required = true)
            @RequestBody CheckFilePathsRequest request) {
        try {
            MinioFileService.CheckFilePathsResult result = minioFileService.checkFilePaths(request.getFilePathList());
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", result.isStatus());
            response.put("non_exist_files", result.getNonExistFiles());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("检查文件路径失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 删除批次下的PCAP文件
     */
    @PostMapping("/deletePcapFiles")
    @Operation(summary = "删除PCAP文件", description = "删除MinIO中指定任务的PCAP文件")
    public ResponseEntity<Map<String, Object>> deletePcapFiles(
            @Parameter(description = "删除文件请求", required = true)
            @RequestBody DeletePcapFilesRequest request) {
        try {
            MinioFileService.DeleteFilesResult result = minioFileService.deletePcapFiles(request.getTaskId());
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", result.isStatus());
            response.put("message", result.getMessage());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除PCAP文件失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 上传PCAP文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传PCAP文件", description = "上传PCAP文件到MinIO存储")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @Parameter(description = "上传文件请求", required = true)
            @RequestBody UploadFileRequest request) {
        try {
            MinioFileService.UploadResult result = minioFileService.uploadFile(
                request.getFilePath(), 
                request.getContent()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("file_path", result.getFilePath());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    /**
     * 下载PCAP文件
     */
    @GetMapping("/download")
    @Operation(summary = "下载PCAP文件", description = "从MinIO存储下载PCAP文件")
    public ResponseEntity<byte[]> downloadFile(
            @Parameter(description = "文件路径", required = true)
            @RequestParam("file_path") String filePath) {
        try {
            byte[] content = minioFileService.downloadFile(filePath);
            
            return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=\"" + extractFileName(filePath) + "\"")
                .header("Content-Type", "application/octet-stream")
                .body(content);
        } catch (Exception e) {
            log.error("下载文件失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 提取文件名
     */
    private String extractFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "download.pcap";
        }
        
        int lastSlashIndex = filePath.lastIndexOf('/');
        return lastSlashIndex >= 0 ? filePath.substring(lastSlashIndex + 1) : filePath;
    }

    /**
     * 检查文件路径请求
     */
    public static class CheckFilePathsRequest {
        private List<String> filePathList;

        public List<String> getFilePathList() {
            return filePathList;
        }

        public void setFilePathList(List<String> filePathList) {
            this.filePathList = filePathList;
        }
    }

    /**
     * 删除PCAP文件请求
     */
    public static class DeletePcapFilesRequest {
        private String taskId;

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }
    }

    /**
     * 上传文件请求
     */
    public static class UploadFileRequest {
        private String filePath;
        private byte[] content;

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public byte[] getContent() {
            return content;
        }

        public void setContent(byte[] content) {
            this.content = content;
        }
    }
}

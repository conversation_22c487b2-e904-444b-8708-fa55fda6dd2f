package com.geeksec.task.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 导入服务状态 VO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ImportServiceStatusVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 服务实例ID
     */
    private Integer serviceId;

    /**
     * 服务实例名称
     */
    private String serviceName;

    /**
     * 服务状态
     */
    private ServiceStatus status;

    /**
     * 当前处理的任务ID
     */
    private Integer currentTaskId;

    /**
     * 当前处理的批次ID
     */
    private Integer currentBatchId;

    /**
     * 任务开始时间
     */
    private LocalDateTime taskStartTime;

    /**
     * 处理进度 (0.0 - 1.0)
     */
    private Double progress;

    /**
     * 已处理的文件数量
     */
    private Integer processedFileCount;

    /**
     * 总文件数量
     */
    private Integer totalFileCount;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        /**
         * 活跃状态 - 正在处理任务
         */
        ACTIVE("活跃"),
        
        /**
         * 空闲状态 - 可以接受新任务
         */
        IDLE("空闲"),
        
        /**
         * 错误状态 - 服务出现异常
         */
        ERROR("错误"),
        
        /**
         * 停止状态 - 服务已停止
         */
        STOPPED("停止");

        private final String description;

        ServiceStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建空闲状态的服务
     */
    public static ImportServiceStatusVo createIdleService(Integer serviceId, String serviceName) {
        ImportServiceStatusVo vo = new ImportServiceStatusVo();
        vo.setServiceId(serviceId);
        vo.setServiceName(serviceName);
        vo.setStatus(ServiceStatus.IDLE);
        vo.setProgress(0.0);
        vo.setProcessedFileCount(0);
        vo.setTotalFileCount(0);
        vo.setLastUpdateTime(LocalDateTime.now());
        return vo;
    }

    /**
     * 创建活跃状态的服务
     */
    public static ImportServiceStatusVo createActiveService(Integer serviceId, String serviceName,
                                                          Integer taskId, Integer batchId) {
        ImportServiceStatusVo vo = new ImportServiceStatusVo();
        vo.setServiceId(serviceId);
        vo.setServiceName(serviceName);
        vo.setStatus(ServiceStatus.ACTIVE);
        vo.setCurrentTaskId(taskId);
        vo.setCurrentBatchId(batchId);
        vo.setTaskStartTime(LocalDateTime.now());
        vo.setProgress(0.0);
        vo.setProcessedFileCount(0);
        vo.setTotalFileCount(0);
        vo.setLastUpdateTime(LocalDateTime.now());
        return vo;
    }
}

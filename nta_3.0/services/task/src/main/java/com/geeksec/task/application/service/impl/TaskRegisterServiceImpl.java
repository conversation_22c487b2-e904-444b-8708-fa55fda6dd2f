package com.geeksec.task.application.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.task.application.service.TaskRegisterService;
import com.geeksec.task.model.dto.TaskRegisterCondition;
import com.geeksec.task.model.entity.DownloadTaskRegister;
import com.geeksec.task.repository.DownloadTaskRegisterRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.ThreadPoolExecutor;
import com.geeksec.common.concurrent.ThreadPoolUtils;

/**
 * 任务注册应用服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskRegisterServiceImpl implements TaskRegisterService {

    private final DownloadTaskRegisterRepository downloadTaskRegisterRepository;

    // 下一条预执行的任务ID
    private Integer nextId;

    @Value("${enabled.task_scheduled:true}")
    private Boolean enabledTaskScheduled;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject taskRegister(TaskRegisterCondition condition) {
        log.info("创建下载任务：{}", condition);
        
        Integer userId = getCurrentUserId();
        condition.setUserId(userId);
        
        long startTime = System.currentTimeMillis();
        DownloadTaskRegister taskRegister = new DownloadTaskRegister();
        
        // 设置基本信息
        taskRegister.setUserId(condition.getUserId());
        taskRegister.setQuery(condition.getCondition().toJSONString());
        taskRegister.setShowQuery(condition.getShowQuery());
        taskRegister.setTaskType(condition.getTaskType());
        taskRegister.setType(condition.getType());
        // 设置为待处理状态
        taskRegister.setStatus(1);
        taskRegister.setCreateTime(LocalDateTime.now());
        
        // 保存任务注册信息
        downloadTaskRegisterRepository.save(taskRegister);
        
        JSONObject result = new JSONObject();
        result.put("success", true);
        result.put("taskId", taskRegister.getId());
        result.put("message", "任务注册成功");
        result.put("cost", System.currentTimeMillis() - startTime);
        
        return result;
    }

    @Override
    public JSONObject searchList(TaskRegisterCondition condition) {
        // TODO: 实现搜索列表逻辑
        JSONObject result = new JSONObject();
        result.put("success", true);
        result.put("data", "搜索列表功能待实现");
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject deleteTask(Integer id) {
        try {
            DownloadTaskRegister taskRegister = downloadTaskRegisterRepository.getById(id);
            if (taskRegister == null) {
                return createErrorResult("任务不存在");
            }

            if (!Integer.valueOf(1).equals(taskRegister.getStatus())) {
                return createErrorResult("只能删除待处理状态的任务");
            }

            downloadTaskRegisterRepository.removeById(id);
            
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "删除任务成功");
            return result;
        } catch (Exception e) {
            log.error("删除任务失败，任务ID：{}", id, e);
            return createErrorResult("删除任务失败：" + e.getMessage());
        }
    }

    @Override
    public DownloadTaskRegister downloadData(Integer id) {
        return downloadTaskRegisterRepository.getById(id);
    }

    /**
     * 数据准备任务（定时任务）
     */
    @Scheduled(fixedDelay = 5000)
    public void dataPrepareJob() {
        if (enabledTaskScheduled) {
            ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtils.createTaskProcessingPool("data-prepare");
            threadPoolExecutor.execute(this::dataPrepare);
            threadPoolExecutor.shutdown();
        }
    }

    @Override
    public DownloadTaskRegister getNextTask() {
        return downloadTaskRegisterRepository.getNextPendingTask();
    }

    @Override
    public void dataPrepare() {
        DownloadTaskRegister register = getNextTask();
        if (register == null) {
            return;
        }
        
        nextId = register.getId();
        log.info("会话分析任务扫描，本次执行任务id={}, 任务type={}", register.getId(), register.getType());
        
        try {
            // 更新状态为处理中
            DownloadTaskRegister registerUpdate = new DownloadTaskRegister(register.getId(), 2);
            downloadTaskRegisterRepository.updateById(registerUpdate);

            // 根据任务类型处理不同的数据准备逻辑
            Integer taskType = register.getTaskType();
            boolean success = processTaskByType(register, taskType);

            if (success) {
                // 更新状态为已完成
                registerUpdate.setStatus(3);
                downloadTaskRegisterRepository.updateById(registerUpdate);
                log.info("任务处理成功，任务ID：{}", register.getId());
            } else {
                // 更新状态为失败
                registerUpdate.setStatus(-1);
                registerUpdate.setErrorMsg("数据准备失败");
                downloadTaskRegisterRepository.updateById(registerUpdate);
                log.error("任务处理失败，任务ID：{}", register.getId());
            }
        } catch (Exception e) {
            log.error("数据准备出错，任务ID：{}", register.getId(), e);
            try {
                DownloadTaskRegister registerUpdate = new DownloadTaskRegister(register.getId(), -1);
                registerUpdate.setErrorMsg(e.getMessage());
                downloadTaskRegisterRepository.updateById(registerUpdate);
            } catch (Exception updateException) {
                log.error("更新任务状态失败，任务ID：{}", register.getId(), updateException);
            }
        } finally {
            nextId = null;
        }
    }

    /**
     * 根据任务类型处理数据准备
     */
    private boolean processTaskByType(DownloadTaskRegister register, Integer taskType) {
        try {
            switch (taskType) {
                case 1:
                    // 会话列表
                    return processSessionListPreparation(register);
                case 2:
                    // 会话聚合列表
                    return processAggregationListPreparation(register);
                case 3:
                    // 元数据列表导出
                    return processMetadataListPreparation(register);
                case 4:
                case 5:
                    // 元数据聚合导出
                    return processMetadataAggListPreparation(register);
                case 6:
                    // IP列表导出
                    return processTargetAggrPreparation(register, "IP");
                case 7:
                    // 域名列表导出
                    return processTargetAggrPreparation(register, "DOMAIN");
                default:
                    log.warn("未知的任务类型：{}", taskType);
                    return false;
            }
        } catch (Exception e) {
            log.error("处理任务类型{}失败", taskType, e);
            return false;
        }
    }

    /**
     * 处理会话列表准备
     */
    private boolean processSessionListPreparation(DownloadTaskRegister register) {
        // TODO: 实现会话列表数据准备逻辑
        log.info("处理会话列表准备，任务ID：{}", register.getId());
        return true;
    }

    /**
     * 处理聚合列表准备
     */
    private boolean processAggregationListPreparation(DownloadTaskRegister register) {
        // TODO: 实现聚合列表数据准备逻辑
        log.info("处理聚合列表准备，任务ID：{}", register.getId());
        return true;
    }

    /**
     * 处理元数据列表准备
     */
    private boolean processMetadataListPreparation(DownloadTaskRegister register) {
        // TODO: 实现元数据列表数据准备逻辑
        log.info("处理元数据列表准备，任务ID：{}", register.getId());
        return true;
    }

    /**
     * 处理元数据聚合列表准备
     */
    private boolean processMetadataAggListPreparation(DownloadTaskRegister register) {
        // TODO: 实现元数据聚合列表数据准备逻辑
        log.info("处理元数据聚合列表准备，任务ID：{}", register.getId());
        return true;
    }

    /**
     * 处理目标聚合准备
     */
    private boolean processTargetAggrPreparation(DownloadTaskRegister register, String targetType) {
        // TODO: 实现目标聚合数据准备逻辑
        log.info("处理目标聚合准备，任务ID：{}，目标类型：{}", register.getId(), targetType);
        return true;
    }

    /**
     * 创建错误结果
     */
    private JSONObject createErrorResult(String message) {
        JSONObject result = new JSONObject();
        result.put("success", false);
        result.put("message", message);
        return result;
    }

    /**
     * 获取当前用户ID
     */
    private Integer getCurrentUserId() {
        // TODO: 从安全上下文获取用户ID
        return 1;
    }
}

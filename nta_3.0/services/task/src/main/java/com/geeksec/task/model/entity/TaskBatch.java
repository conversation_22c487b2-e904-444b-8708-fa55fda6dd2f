package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * 任务批次实体类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_task_batch")
public class TaskBatch implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增批次ID
     */
    @Id(keyType = KeyType.Auto)
    @Column("batch_id")
    private Integer batchId;

    /**
     * 任务ID
     */
    @Column("task_id")
    private Integer taskId;

    /**
     * 任务类型 (1: 在线任务, 2: 离线任务)
     */
    @Column("task_type")
    private Integer taskType;

    /**
     * 批次类型 (1: 服务器数据, 2: 数据上传)
     */
    @Column("batch_type")
    private Integer batchType;

    /**
     * 批次描述
     */
    @Column("batch_remark")
    private String batchRemark;

    /**
     * 全流量留存状态 (ON: 启用, OFF: 停用)
     */
    @Column("fullflow_state")
    private String fullflowState;

    /**
     * 流量日志留存状态 (ON: 启用, OFF: 停用)
     */
    @Column("flowlog_state")
    private String flowlogState;

    /**
     * 导入的数据类型 (1: pcap, 2: pb, 3: 探针数据)
     */
    @Column("data_type")
    private Integer dataType;

    /**
     * MAC日志留存状态 (ON: 启用, OFF: 停用)
     */
    @Column("topology_state")
    private String topologyState;

    /**
     * 导入开始时间
     */
    @Column("begin_time")
    private Integer beginTime;

    /**
     * 导入结束时间
     */
    @Column("end_time")
    private Integer endTime;

    /**
     * 数据开始时间
     */
    @Column("data_begin_time")
    private Integer dataBeginTime;

    /**
     * 数据结束时间
     */
    @Column("data_end_time")
    private Integer dataEndTime;

    /**
     * 导入数据量（字节）
     */
    @Column("batch_bytes")
    private Long batchBytes;

    /**
     * 导入会话量
     */
    @Column("batch_session")
    private Integer batchSession;

    /**
     * 批次的告警量
     */
    @Column("batch_alarm")
    private Integer batchAlarm;

    /**
     * 高危目标数量
     */
    @Column("importrarnt_target")
    private Integer importantTarget;

    /**
     * 过滤数据量
     */
    @Column("filter_data_total")
    private Long filterDataTotal;

    /**
     * 采集规则命中数据量
     */
    @Column("rule_hits_data_total")
    private Long ruleHitsDataTotal;

    /**
     * 白名单过滤量
     */
    @Column("whitelist_filter_total")
    private Long whitelistFilterTotal;

    /**
     * 任务状态 (1: 等待导入, 2: 正在导入, 3: 导入完成, 4: 导入失败)
     */
    @Column("batch_status")
    private Integer batchStatus;

    /**
     * 导入进度
     */
    @Column("batch_progress")
    private Double batchProgress;

    /**
     * 当前任务批次数据路径
     */
    @Column("batch_dir")
    private String batchDir;

    /**
     * 批次数据报告生成路径
     */
    @Column("report_path")
    private String reportPath;

    /**
     * 筛选条件
     */
    @Column("screening_conditions")
    private String screeningConditions;

    /**
     * 每线程每秒钟平均可写pcap字节数
     */
    @Column("avg_byte_pt_ps")
    private Integer avgBytePtPs;

    /**
     * 每线程每秒钟最多可写pcap字节数
     */
    @Column("max_byte_pt_ps")
    private Integer maxBytePtPs;

    /**
     * 批次地址
     */
    @Column("addr")
    private String addr;

    /**
     * 更新标识 (0: 初始化状态, 1: 非初始化状态)
     */
    @Column("task_update")
    private Integer taskUpdate;

    /**
     * 全流量插件默认开启状态 (1: 开启, 0: 关闭)
     */
    @Column("full_flow_should_log_def")
    private Integer fullFlowShouldLogDef;

    /**
     * 协议解析插件默认开启状态 (1: 开启, 0: 关闭)
     */
    @Column("parse_proto_should_log_def")
    private Integer parseProtoShouldLogDef;

    /**
     * 批次运行状态 (1: 正在运行, 0: 关闭)
     */
    @Column("state")
    private Integer state;
}

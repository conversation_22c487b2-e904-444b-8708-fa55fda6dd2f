package com.geeksec.task.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务查询请求DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "任务查询请求")
public class TaskQueryRequest {
    
    @Schema(description = "页码", example = "1")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "20")
    private Integer size = 20;
    
    @Schema(description = "任务名称（模糊查询）", example = "分析")
    private String taskName;
    
    @Schema(description = "任务状态列表", example = "[\"RUNNING\", \"PENDING\"]")
    private List<String> taskStatuses;
    
    @Schema(description = "任务类型列表", example = "[\"ANALYSIS\", \"DOWNLOAD\"]")
    private List<String> taskTypes;
    
    @Schema(description = "创建用户ID", example = "1")
    private Integer userId;
    
    @Schema(description = "创建用户名", example = "admin")
    private String userName;
    
    @Schema(description = "创建时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime createTimeStart;
    
    @Schema(description = "创建时间结束", example = "2024-01-31T23:59:59")
    private LocalDateTime createTimeEnd;
    
    @Schema(description = "开始时间开始", example = "2024-01-01T00:00:00")
    private LocalDateTime startTimeStart;
    
    @Schema(description = "开始时间结束", example = "2024-01-31T23:59:59")
    private LocalDateTime startTimeEnd;
    
    @Schema(description = "任务优先级", example = "HIGH")
    private String priority;
    
    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;
    
    @Schema(description = "排序字段", example = "createTime")
    private String sortBy = "createTime";
    
    @Schema(description = "排序方向", example = "DESC")
    private String sortDirection = "DESC";
}

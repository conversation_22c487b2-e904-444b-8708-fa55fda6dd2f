package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.OfflineTaskBatchService;
import com.geeksec.task.application.service.MinioFileService;
import com.geeksec.task.model.vo.FileTreeNodeVo;
import com.geeksec.task.repository.TaskBatchRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 离线任务批次服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflineTaskBatchServiceImpl implements OfflineTaskBatchService {

    private final MinioFileService minioFileService;
    private final TaskBatchRepository taskBatchRepository;

    @Override
    public BatchPageResult pageBatch(OfflineTaskBatchQueryCondition condition) {
        try {
            // TODO: 实现分页查询批次信息
            // 这里需要根据实际的数据库表结构来实现
            log.info("查询批次信息，任务ID: {}", condition.getTaskId());

            // 暂时返回空结果，实际实现需要查询数据库
            return new BatchPageResult(List.of(), 0L, condition.getCurrentPage(), condition.getPageSize());
        } catch (Exception e) {
            log.error("查询批次信息失败", e);
            throw new RuntimeException("查询批次信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchOperationResult addBatch(OfflineTaskBatchDto dto) {
        try {
            log.info("开始添加批次，任务ID: {}, 文件数量: {}", dto.getTaskId(),
                    dto.getFilePathList() != null ? dto.getFilePathList().size() : 0);

            // 1. 检查文件是否存在
            if (dto.getFilePathList() != null && !dto.getFilePathList().isEmpty()) {
                CheckFilePathsResult checkResult = checkFilePaths(dto.getFilePathList());
                if (!checkResult.getNonExistFiles().isEmpty()) {
                    return new BatchOperationResult(false, "以下文件不存在: " + String.join(", ", checkResult.getNonExistFiles()));
                }
            }

            // 2. 创建批次记录
            // TODO: 实现批次创建逻辑
            // TaskBatch taskBatch = new TaskBatch();
            // taskBatch.setTaskId(dto.getTaskId());
            // taskBatch.setBatchRemark(dto.getBatchDescription());
            // taskBatch.setBatchType(dto.getBatchType());
            // taskBatch.setFullflowState(dto.getFullflowState());
            // taskBatch.setFlowlogState(dto.getFlowlogState());
            // taskBatchRepository.save(taskBatch);

            // 3. 发送Kafka消息通知处理
            // TODO: 发送Kafka消息

            log.info("批次添加成功，任务ID: {}", dto.getTaskId());
            return new BatchOperationResult(true, "批次添加成功");

        } catch (Exception e) {
            log.error("添加批次失败，任务ID: {}", dto.getTaskId(), e);
            return new BatchOperationResult(false, "添加批次失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchOperationResult cancelBatch(OfflineTaskBatchCancelDto dto) {
        try {
            log.info("开始取消批次，批次ID: {}", dto.getBatchId());

            // 1. 更新批次状态
            // TODO: 实现批次状态更新逻辑
            // taskBatchRepository.updateBatchStatus(dto.getBatchId(), BatchStatus.CANCELLED);

            // 2. 停止相关的处理服务
            // TODO: 调用停止服务的接口

            log.info("批次取消成功，批次ID: {}", dto.getBatchId());
            return new BatchOperationResult(true, "批次取消成功");

        } catch (Exception e) {
            log.error("取消批次失败，批次ID: {}", dto.getBatchId(), e);
            return new BatchOperationResult(false, "取消批次失败: " + e.getMessage());
        }
    }

    @Override
    public List<FileTreeNodeVo> listServerPath(String directoryPath) {
        try {
            log.info("查询服务器文件路径，目录: {}", directoryPath);
            
            // 使用MinIO文件服务查询文件路径
            List<FileTreeNodeVo> result = minioFileService.listServerPath(directoryPath);
            
            log.info("查询服务器文件路径成功，目录: {}, 文件数量: {}", directoryPath, result.size());
            return result;

        } catch (Exception e) {
            log.error("查询服务器文件路径失败，目录: {}", directoryPath, e);
            throw new RuntimeException("查询服务器文件路径失败: " + e.getMessage(), e);
        }
    }

    @Override
    public CheckFilePathsResult checkFilePaths(List<String> filePathList) {
        try {
            log.info("检查文件路径，文件数量: {}", filePathList.size());
            
            // 使用MinIO文件服务检查文件路径
            MinioFileService.CheckFilePathsResult minioResult = minioFileService.checkFilePaths(filePathList);
            
            CheckFilePathsResult result = new CheckFilePathsResult(
                minioResult.isStatus(), 
                minioResult.getNonExistFiles()
            );
            
            log.info("检查文件路径完成，总数: {}, 不存在: {}", 
                    filePathList.size(), result.getNonExistFiles().size());
            return result;

        } catch (Exception e) {
            log.error("检查文件路径失败", e);
            throw new RuntimeException("检查文件路径失败: " + e.getMessage(), e);
        }
    }

    @Override
    public DeleteFilesResult deletePcapFiles(String taskId) {
        try {
            log.info("删除任务PCAP文件，任务ID: {}", taskId);
            
            // 使用MinIO文件服务删除文件
            MinioFileService.DeleteFilesResult minioResult = minioFileService.deletePcapFiles(taskId);
            
            DeleteFilesResult result = new DeleteFilesResult(
                minioResult.isStatus(), 
                minioResult.getMessage()
            );
            
            log.info("删除任务PCAP文件完成，任务ID: {}, 结果: {}", taskId, result.getMessage());
            return result;

        } catch (Exception e) {
            log.error("删除任务PCAP文件失败，任务ID: {}", taskId, e);
            return new DeleteFilesResult(false, "删除失败: " + e.getMessage());
        }
    }
}

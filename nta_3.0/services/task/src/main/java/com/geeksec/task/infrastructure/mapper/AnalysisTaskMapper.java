package com.geeksec.task.infrastructure.mapper;

import com.geeksec.task.model.entity.AnalysisTask;
import com.geeksec.task.model.condition.OfflineTaskQueryCondition;
import com.geeksec.task.model.vo.OfflineTaskVo;
import com.geeksec.task.model.vo.OfflineTaskPageVo;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 分析任务数据访问映射器
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface AnalysisTaskMapper extends BaseMapper<AnalysisTask> {

    /**
     * 获取用户最新的任务
     * 
     * @param userId 用户ID
     * @return 最新任务
     */
    @Select("SELECT task_id, task_name, task_remark, task_state, task_type, netflow, " +
            "user_id, DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time, " +
            "DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time " +
            "FROM tb_task_analysis WHERE user_id = #{userId} AND deleted = 0 " +
            "ORDER BY create_time DESC LIMIT 1")
    OfflineTaskVo getLastTask(@Param("userId") Integer userId);

    /**
     * 根据任务ID获取任务详情
     * 
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Select("SELECT task_id, task_name, task_remark, task_state, task_type, netflow, " +
            "user_id, DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time, " +
            "DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time " +
            "FROM tb_task_analysis WHERE task_id = #{taskId} AND deleted = 0")
    OfflineTaskVo getTask(@Param("taskId") Integer taskId);

    /**
     * 分页查询任务列表
     *
     * @param condition 查询条件
     * @return 任务分页列表
     */
    @Select("""
            <script>
            SELECT
                task_id,
                task_name,
                task_remark as task_description,
                task_remark,
                task_type,
                netflow as net_flow,
                user_id,
                DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
                DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
            FROM tb_task_analysis
            WHERE deleted = 0
            <if test="condition.userId != null">
                AND user_id = #{condition.userId}
            </if>
            <if test="condition.taskName != null and condition.taskName != ''">
                AND task_name LIKE CONCAT('%', #{condition.taskName}, '%')
            </if>
            <if test="condition.taskType != null">
                AND task_type = #{condition.taskType}
            </if>
            <if test="condition.startTime != null and condition.startTime != ''">
                AND create_time >= #{condition.startTime}
            </if>
            <if test="condition.endTime != null and condition.endTime != ''">
                AND create_time &lt;= #{condition.endTime}
            </if>
            ORDER BY
            <choose>
                <when test="condition.orderBy != null and condition.orderBy != ''">
                    ${condition.orderBy}
                </when>
                <otherwise>
                    create_time
                </otherwise>
            </choose>
            <choose>
                <when test="condition.orderDirection != null and condition.orderDirection != ''">
                    ${condition.orderDirection}
                </when>
                <otherwise>
                    DESC
                </otherwise>
            </choose>
            </script>
            """)
    List<OfflineTaskPageVo> pageTask(@Param("condition") OfflineTaskQueryCondition condition);

    /**
     * 更新任务ID
     * 
     * @param id 主键ID
     * @return 更新结果
     */
    @Update("UPDATE tb_task_analysis SET task_id = #{id} WHERE id = #{id}")
    boolean updateTaskId(@Param("id") Integer id);
}

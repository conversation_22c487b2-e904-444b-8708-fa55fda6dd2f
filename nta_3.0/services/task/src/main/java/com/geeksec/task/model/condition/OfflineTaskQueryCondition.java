package com.geeksec.task.model.condition;

import lombok.Data;

/**
 * 离线任务查询条件
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class OfflineTaskQueryCondition {

    /**
     * 当前页码
     */
    private Integer currentPage = 1;

    /**
     * 页面大小
     */
    private Integer pageSize = 10;

    /**
     * 任务名称（模糊查询）
     */
    private String taskName;

    /**
     * 任务状态
     */
    private Integer taskStatus;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 排序字段
     */
    private String orderBy = "create_time";

    /**
     * 排序方向 (ASC, DESC)
     */
    private String orderDirection = "DESC";
}

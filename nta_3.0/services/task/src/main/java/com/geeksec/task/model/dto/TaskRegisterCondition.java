package com.geeksec.task.model.dto;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * 任务注册条件数据传输对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class TaskRegisterCondition {

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 查询条件
     */
    private JSONObject condition;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 下载类型
     */
    private Integer type;

    /**
     * 前端展示查询条件
     */
    private String showQuery;

    /**
     * 会话ID列表
     */
    private String sessionIds;

    /**
     * 当前页码
     */
    private Integer currentPage = 1;

    /**
     * 页面大小
     */
    private Integer pageSize = 10;

    /**
     * 搜索列表
     */
    private java.util.List<Integer> searchList;
}

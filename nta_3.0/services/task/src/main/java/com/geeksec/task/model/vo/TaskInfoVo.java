package com.geeksec.task.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 任务信息视图对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class TaskInfoVo {

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private Integer taskId;

    /**
     * 任务名称
     */
    @JsonProperty("task_name")
    private String taskName;

    /**
     * 任务描述
     */
    @JsonProperty("task_description")
    private String taskDescription;

    /**
     * 任务状态
     */
    @JsonProperty("task_status")
    private String taskStatus;

    /**
     * 任务类型
     */
    @JsonProperty("task_type")
    private String taskType;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 更新时间
     */
    @JsonProperty("update_time")
    private String updateTime;
}

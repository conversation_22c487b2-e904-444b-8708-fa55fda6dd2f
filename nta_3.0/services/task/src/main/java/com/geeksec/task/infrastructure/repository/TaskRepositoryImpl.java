package com.geeksec.task.infrastructure.repository;

import com.geeksec.task.infrastructure.mapper.TaskMapper;
import com.geeksec.task.model.entity.Task;
import com.geeksec.task.repository.TaskRepository;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务仓储实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
public class TaskRepositoryImpl extends ServiceImpl<TaskMapper, Task> implements TaskRepository {

    @Override
    public List<Task> findByUserId(Integer userId) {
        return list(QueryWrapper.create()
                .where("user_id = ?", userId)
                .and("deleted = ?", 0)
                .orderBy("create_time DESC"));
    }

    @Override
    public List<Task> findByTaskStatus(String taskStatus) {
        return list(QueryWrapper.create()
                .where("task_status = ?", taskStatus)
                .and("deleted = ?", 0)
                .orderBy("create_time DESC"));
    }

    @Override
    public List<Task> findByTaskType(String taskType) {
        return list(QueryWrapper.create()
                .where("task_type = ?", taskType)
                .and("deleted = ?", 0)
                .orderBy("create_time DESC"));
    }

    @Override
    public Task findLatestByUserId(Integer userId) {
        return getOne(QueryWrapper.create()
                .where("user_id = ?", userId)
                .and("deleted = ?", 0)
                .orderBy("create_time DESC")
                .limit(1));
    }

    @Override
    public Long countByUserId(Integer userId) {
        return count(QueryWrapper.create()
                .where("user_id = ?", userId)
                .and("deleted = ?", 0));
    }

    @Override
    public Task findByTaskName(String taskName) {
        return getOne(QueryWrapper.create()
                .where("task_name = ?", taskName)
                .and("deleted = ?", 0));
    }

    @Override
    public boolean existsByTaskName(String taskName, Integer excludeId) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where("task_name = ?", taskName)
                .and("deleted = ?", 0);

        if (excludeId != null) {
            queryWrapper.and("task_id != ?", excludeId);
        }

        return count(queryWrapper) > 0;
    }
}

package com.geeksec.task.repository;

import static com.geeksec.task.model.entity.table.OfflineTaskBatchFileTableDef.OFFLINE_TASK_BATCH_FILE;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.geeksec.task.model.entity.OfflineTaskBatchFile;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * 离线任务批次文件数据访问接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface OfflineTaskBatchFileRepository extends BaseMapper<OfflineTaskBatchFile> {

    /**
     * 根据批次ID查询文件列表
     * 
     * @param batchId 批次ID
     * @return 文件列表
     */
    default List<OfflineTaskBatchFile> findByBatchId(Integer batchId) {
        return selectListByQuery(QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.BATCH_ID.eq(batchId))
                .orderBy(OFFLINE_TASK_BATCH_FILE.CREATED_AT.asc()));
    }

    /**
     * 根据任务ID查询文件列表
     * 
     * @param taskId 任务ID
     * @return 文件列表
     */
    default List<OfflineTaskBatchFile> findByTaskId(Integer taskId) {
        return selectListByQuery(QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.TASK_ID.eq(taskId))
                .orderBy(OFFLINE_TASK_BATCH_FILE.CREATED_AT.asc()));
    }

    /**
     * 根据批次ID删除文件记录
     * 
     * @param batchId 批次ID
     * @return 删除的记录数
     */
    default int deleteByBatchId(Integer batchId) {
        return deleteByQuery(QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.BATCH_ID.eq(batchId)));
    }

    /**
     * 根据任务ID删除文件记录
     * 
     * @param taskId 任务ID
     * @return 删除的记录数
     */
    default int deleteByTaskId(Integer taskId) {
        return deleteByQuery(QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.TASK_ID.eq(taskId)));
    }

    /**
     * 根据状态查询文件列表
     * 
     * @param status 文件状态
     * @return 文件列表
     */
    default List<OfflineTaskBatchFile> findByStatus(Integer status) {
        return selectListByQuery(QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.STATUS.eq(status))
                .orderBy(OFFLINE_TASK_BATCH_FILE.CREATED_AT.asc()));
    }

    /**
     * 更新文件状态
     * 
     * @param id 文件ID
     * @param status 新状态
     * @return 更新的记录数
     */
    default int updateStatus(Integer id, Integer status) {
        OfflineTaskBatchFile updateEntity = new OfflineTaskBatchFile();
        updateEntity.setId(id);
        updateEntity.setStatus(status);
        return update(updateEntity);
    }

    /**
     * 批量更新批次文件状态
     * 
     * @param batchId 批次ID
     * @param status 新状态
     * @return 更新的记录数
     */
    default int updateStatusByBatchId(Integer batchId, Integer status) {
        OfflineTaskBatchFile updateEntity = new OfflineTaskBatchFile();
        updateEntity.setStatus(status);
        return updateByQuery(updateEntity, QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.BATCH_ID.eq(batchId)));
    }

    /**
     * 统计批次文件数量
     * 
     * @param batchId 批次ID
     * @return 文件数量
     */
    default long countByBatchId(Integer batchId) {
        return selectCountByQuery(QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.BATCH_ID.eq(batchId)));
    }

    /**
     * 统计指定状态的文件数量
     *
     * @param batchId 批次ID
     * @param status 文件状态
     * @return 文件数量
     */
    default long countByBatchIdAndStatus(Integer batchId, Integer status) {
        return selectCountByQuery(QueryWrapper.create()
                .where(OFFLINE_TASK_BATCH_FILE.BATCH_ID.eq(batchId))
                .and(OFFLINE_TASK_BATCH_FILE.STATUS.eq(status)));
    }

    /**
     * 根据ID获取文件记录
     *
     * @param id 文件ID
     * @return 文件记录
     */
    default OfflineTaskBatchFile getById(Integer id) {
        return selectOneById(id);
    }
}

package com.geeksec.task.infrastructure.repository;

import com.geeksec.task.infrastructure.mapper.DownloadTaskMapper;
import com.geeksec.task.model.entity.DownloadTask;
import com.geeksec.task.repository.DownloadTaskRepository;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 下载任务仓储实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
public class DownloadTaskRepositoryImpl extends ServiceImpl<DownloadTaskMapper, DownloadTask> implements DownloadTaskRepository {

    @Override
    public List<DownloadTask> findByUserId(Integer userId) {
        return list(QueryWrapper.create()
                .where("user_id = ?", userId)
                .and("status = ?", 1)
                .orderBy("created_time DESC"));
    }

    @Override
    public List<DownloadTask> findByState(Integer state) {
        return list(QueryWrapper.create()
                .where("state = ?", state)
                .and("status = ?", 1)
                .orderBy("created_time DESC"));
    }

    @Override
    public String getPcapPath(Integer taskId) {
        return getMapper().getPcapPath(taskId);
    }

    @Override
    public List<DownloadTask> findByUserIdAndState(Integer userId, Integer state) {
        return list(QueryWrapper.create()
                .where("user_id = ?", userId)
                .and("state = ?", state)
                .and("status = ?", 1)
                .orderBy("created_time DESC"));
    }

    @Override
    public Long countByUserId(Integer userId) {
        return count(QueryWrapper.create()
                .where("user_id = ?", userId)
                .and("status = ?", 1));
    }

    @Override
    public List<DownloadTask> findByTaskType(Integer taskType) {
        return list(QueryWrapper.create()
                .where("task_type = ?", taskType)
                .and("status = ?", 1)
                .orderBy("created_time DESC"));
    }
}

package com.geeksec.task.repository;

import com.geeksec.task.model.entity.TaskBatch;
import com.geeksec.task.model.vo.OfflineTaskBatchProgressVo;
import com.mybatisflex.core.service.IService;

import java.util.List;

/**
 * 任务批次仓储接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface TaskBatchRepository extends IService<TaskBatch> {

    /**
     * 根据任务ID查询批次列表
     * 
     * @param taskId 任务ID
     * @return 批次列表
     */
    List<TaskBatch> findByTaskId(Integer taskId);

    /**
     * 根据任务ID查询批次进度信息
     * 
     * @param taskId 任务ID
     * @return 批次进度列表
     */
    List<OfflineTaskBatchProgressVo> listBatchByTaskId(Integer taskId);

    /**
     * 根据任务ID列表查询批次信息
     * 
     * @param taskIdList 任务ID列表
     * @return 批次列表
     */
    List<TaskBatch> listTaskBatchItem(List<Integer> taskIdList);

    /**
     * 统计任务下未完成的批次数量
     * 
     * @param taskId 任务ID
     * @return 未完成批次数量
     */
    Integer countIncompleteTask(Integer taskId);

    /**
     * 根据批次状态查询批次列表
     * 
     * @param batchStatus 批次状态
     * @return 批次列表
     */
    List<TaskBatch> findByBatchStatus(Integer batchStatus);

    /**
     * 根据任务类型查询批次列表
     * 
     * @param taskType 任务类型
     * @return 批次列表
     */
    List<TaskBatch> findByTaskType(Integer taskType);

    /**
     * 更新批次进度
     * 
     * @param batchId 批次ID
     * @param progress 进度
     * @return 更新结果
     */
    boolean updateProgress(Integer batchId, Double progress);

    /**
     * 更新批次状态
     * 
     * @param batchId 批次ID
     * @param status 状态
     * @return 更新结果
     */
    boolean updateStatus(Integer batchId, Integer status);
}

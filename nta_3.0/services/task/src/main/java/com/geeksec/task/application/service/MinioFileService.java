package com.geeksec.task.application.service;

import com.geeksec.task.model.vo.FileTreeNodeVo;

import java.util.List;

/**
 * MinIO 文件管理服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface MinioFileService {

    /**
     * 列出服务器文件路径
     * 
     * @param directoryPath 目录路径（相对路径）
     * @return 文件树节点列表
     */
    List<FileTreeNodeVo> listServerPath(String directoryPath);

    /**
     * 检查文件路径是否存在
     * 
     * @param filePathList 文件路径列表
     * @return 检查结果，包含不存在的文件路径列表
     */
    CheckFilePathsResult checkFilePaths(List<String> filePathList);

    /**
     * 删除批次下的 PCAP 文件
     * 
     * @param taskId 任务ID
     * @return 删除结果
     */
    DeleteFilesResult deletePcapFiles(String taskId);

    /**
     * 上传文件到 MinIO
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     * @return 上传结果
     */
    UploadResult uploadFile(String filePath, byte[] content);

    /**
     * 下载文件从 MinIO
     *
     * @param filePath 文件路径
     * @return 文件内容
     */
    byte[] downloadFile(String filePath);

    /**
     * 获取文件大小
     *
     * @param filePath 文件路径
     * @return 文件大小（字节），如果文件不存在返回 -1
     */
    long getFileSize(String filePath);

    /**
     * 检查文件路径结果
     */
    class CheckFilePathsResult {
        private boolean status;
        private List<String> nonExistFiles;

        public CheckFilePathsResult(boolean status, List<String> nonExistFiles) {
            this.status = status;
            this.nonExistFiles = nonExistFiles;
        }

        public boolean isStatus() {
            return status;
        }

        public List<String> getNonExistFiles() {
            return nonExistFiles;
        }
    }

    /**
     * 删除文件结果
     */
    class DeleteFilesResult {
        private boolean status;
        private String message;

        public DeleteFilesResult(boolean status, String message) {
            this.status = status;
            this.message = message;
        }

        public boolean isStatus() {
            return status;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * 上传文件结果
     */
    class UploadResult {
        private boolean success;
        private String message;
        private String filePath;

        public UploadResult(boolean success, String message, String filePath) {
            this.success = success;
            this.message = message;
            this.filePath = filePath;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public String getFilePath() {
            return filePath;
        }
    }
}

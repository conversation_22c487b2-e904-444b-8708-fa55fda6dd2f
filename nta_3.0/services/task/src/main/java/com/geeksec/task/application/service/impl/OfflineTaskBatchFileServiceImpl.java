package com.geeksec.task.application.service.impl;

import com.geeksec.task.application.service.OfflineTaskBatchFileService;
import com.geeksec.task.model.dto.OfflineFilePathDto;
import com.geeksec.task.model.entity.OfflineTaskBatchFile;
import com.geeksec.task.model.entity.TaskBatch;
import com.geeksec.task.repository.OfflineTaskBatchFileRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 离线任务批次文件服务实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OfflineTaskBatchFileServiceImpl implements OfflineTaskBatchFileService {

    private final OfflineTaskBatchFileRepository offlineTaskBatchFileRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBatchFiles(TaskBatch taskBatch, List<OfflineFilePathDto> filePathList) {
        if (filePathList == null || filePathList.isEmpty()) {
            log.warn("文件路径列表为空，跳过批次文件关联创建");
            return;
        }

        try {
            for (OfflineFilePathDto filePathDto : filePathList) {
                OfflineTaskBatchFile batchFile = new OfflineTaskBatchFile();
                batchFile.setTaskId(taskBatch.getTaskId());
                batchFile.setBatchId(taskBatch.getBatchId());
                batchFile.setLocalPath(filePathDto.getLocalPath());
                batchFile.setServerPath(filePathDto.getServerPath());
                batchFile.setFileSize(filePathDto.getFileSize());
                batchFile.setFileType(filePathDto.getFileType() != null ? filePathDto.getFileType() : 1);
                batchFile.setStatus(0); // 待处理
                batchFile.setCreatedAt(LocalDateTime.now());
                batchFile.setUpdatedAt(LocalDateTime.now());

                offlineTaskBatchFileRepository.insert(batchFile);
            }

            log.info("批次文件关联创建成功，批次ID: {}, 文件数量: {}", 
                    taskBatch.getBatchId(), filePathList.size());

        } catch (Exception e) {
            log.error("批次文件关联创建失败，批次ID: {}", taskBatch.getBatchId(), e);
            throw new RuntimeException("批次文件关联创建失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<OfflineTaskBatchFile> getBatchFiles(Integer batchId) {
        try {
            List<OfflineTaskBatchFile> files = offlineTaskBatchFileRepository.findByBatchId(batchId);
            log.debug("查询批次文件成功，批次ID: {}, 文件数量: {}", batchId, files.size());
            return files;
        } catch (Exception e) {
            log.error("查询批次文件失败，批次ID: {}", batchId, e);
            throw new RuntimeException("查询批次文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<OfflineTaskBatchFile> getTaskFiles(Integer taskId) {
        try {
            List<OfflineTaskBatchFile> files = offlineTaskBatchFileRepository.findByTaskId(taskId);
            log.debug("查询任务文件成功，任务ID: {}, 文件数量: {}", taskId, files.size());
            return files;
        } catch (Exception e) {
            log.error("查询任务文件失败，任务ID: {}", taskId, e);
            throw new RuntimeException("查询任务文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatchFiles(Integer batchId) {
        try {
            int deletedCount = offlineTaskBatchFileRepository.deleteByBatchId(batchId);
            log.info("删除批次文件关联成功，批次ID: {}, 删除数量: {}", batchId, deletedCount);
        } catch (Exception e) {
            log.error("删除批次文件关联失败，批次ID: {}", batchId, e);
            throw new RuntimeException("删除批次文件关联失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskFiles(Integer taskId) {
        try {
            int deletedCount = offlineTaskBatchFileRepository.deleteByTaskId(taskId);
            log.info("删除任务文件关联成功，任务ID: {}, 删除数量: {}", taskId, deletedCount);
        } catch (Exception e) {
            log.error("删除任务文件关联失败，任务ID: {}", taskId, e);
            throw new RuntimeException("删除任务文件关联失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFileStatus(Integer fileId, Integer status) {
        try {
            int updatedCount = offlineTaskBatchFileRepository.updateStatus(fileId, status);
            if (updatedCount > 0) {
                log.debug("更新文件状态成功，文件ID: {}, 新状态: {}", fileId, status);
            } else {
                log.warn("更新文件状态失败，文件ID: {} 不存在", fileId);
            }
        } catch (Exception e) {
            log.error("更新文件状态失败，文件ID: {}, 状态: {}", fileId, status, e);
            throw new RuntimeException("更新文件状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchFileStatus(Integer batchId, Integer status) {
        try {
            int updatedCount = offlineTaskBatchFileRepository.updateStatusByBatchId(batchId, status);
            log.info("批量更新批次文件状态成功，批次ID: {}, 新状态: {}, 更新数量: {}", 
                    batchId, status, updatedCount);
        } catch (Exception e) {
            log.error("批量更新批次文件状态失败，批次ID: {}, 状态: {}", batchId, status, e);
            throw new RuntimeException("批量更新批次文件状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public BatchFileStatistics getBatchFileStatistics(Integer batchId) {
        try {
            long totalFiles = offlineTaskBatchFileRepository.countByBatchId(batchId);
            long pendingFiles = offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 0);
            long processingFiles = offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 1);
            long completedFiles = offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 2);
            long failedFiles = offlineTaskBatchFileRepository.countByBatchIdAndStatus(batchId, 3);

            BatchFileStatistics statistics = new BatchFileStatistics(
                    totalFiles, pendingFiles, processingFiles, completedFiles, failedFiles);

            log.debug("获取批次文件统计信息成功，批次ID: {}, 总数: {}, 完成: {}, 失败: {}", 
                    batchId, totalFiles, completedFiles, failedFiles);

            return statistics;
        } catch (Exception e) {
            log.error("获取批次文件统计信息失败，批次ID: {}", batchId, e);
            throw new RuntimeException("获取批次文件统计信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<OfflineTaskBatchFile> getFilesByStatus(Integer status) {
        try {
            List<OfflineTaskBatchFile> files = offlineTaskBatchFileRepository.findByStatus(status);
            log.debug("根据状态查询文件成功，状态: {}, 文件数量: {}", status, files.size());
            return files;
        } catch (Exception e) {
            log.error("根据状态查询文件失败，状态: {}", status, e);
            throw new RuntimeException("根据状态查询文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startProcessFile(Integer fileId) {
        try {
            OfflineTaskBatchFile file = offlineTaskBatchFileRepository.getById(fileId);
            if (file != null) {
                file.setStatus(1); // 处理中
                file.setProcessStartTime(LocalDateTime.now());
                file.setUpdatedAt(LocalDateTime.now());
                offlineTaskBatchFileRepository.update(file);
                log.debug("开始处理文件，文件ID: {}", fileId);
            } else {
                log.warn("开始处理文件失败，文件ID: {} 不存在", fileId);
            }
        } catch (Exception e) {
            log.error("开始处理文件失败，文件ID: {}", fileId, e);
            throw new RuntimeException("开始处理文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishProcessFile(Integer fileId, boolean success, String errorMessage) {
        try {
            OfflineTaskBatchFile file = offlineTaskBatchFileRepository.getById(fileId);
            if (file != null) {
                file.setStatus(success ? 2 : 3); // 2: 已完成, 3: 失败
                file.setProcessEndTime(LocalDateTime.now());
                file.setErrorMessage(errorMessage);
                file.setUpdatedAt(LocalDateTime.now());
                offlineTaskBatchFileRepository.update(file);
                log.debug("完成文件处理，文件ID: {}, 成功: {}", fileId, success);
            } else {
                log.warn("完成文件处理失败，文件ID: {} 不存在", fileId);
            }
        } catch (Exception e) {
            log.error("完成文件处理失败，文件ID: {}", fileId, e);
            throw new RuntimeException("完成文件处理失败: " + e.getMessage(), e);
        }
    }
}

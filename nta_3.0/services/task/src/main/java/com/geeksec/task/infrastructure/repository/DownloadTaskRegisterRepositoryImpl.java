package com.geeksec.task.infrastructure.repository;

import com.geeksec.task.infrastructure.mapper.DownloadTaskRegisterMapper;
import com.geeksec.task.model.entity.DownloadTaskRegister;
import com.geeksec.task.repository.DownloadTaskRegisterRepository;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 下载任务注册仓储实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
public class DownloadTaskRegisterRepositoryImpl extends ServiceImpl<DownloadTaskRegisterMapper, DownloadTaskRegister> implements DownloadTaskRegisterRepository {

    @Override
    public List<DownloadTaskRegister> findByUserId(Integer userId) {
        return list(QueryWrapper.create()
                .where("user_id = ?", userId)
                .orderBy("create_time DESC"));
    }

    @Override
    public List<DownloadTaskRegister> findByStatus(Integer status) {
        return list(QueryWrapper.create()
                .where("status = ?", status)
                .orderBy("create_time DESC"));
    }

    @Override
    public List<DownloadTaskRegister> findByTaskType(Integer taskType) {
        return list(QueryWrapper.create()
                .where("task_type = ?", taskType)
                .orderBy("create_time DESC"));
    }

    @Override
    public DownloadTaskRegister getNextPendingTask() {
        return getMapper().getNextPendingTask();
    }

    @Override
    public Long countByUserId(Integer userId) {
        return count(QueryWrapper.create()
                .where("user_id = ?", userId));
    }

    @Override
    public List<DownloadTaskRegister> findByStatusAndType(Integer status, Integer type) {
        return list(QueryWrapper.create()
                .where("status = ?", status)
                .and("type = ?", type)
                .orderBy("id ASC"));
    }
}

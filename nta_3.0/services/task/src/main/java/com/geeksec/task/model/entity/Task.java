package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务实体类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_task")
public class Task implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @Id(keyType = KeyType.Auto)
    @Column("task_id")
    private Integer taskId;

    /**
     * 任务名称
     */
    @Column("task_name")
    private String taskName;

    /**
     * 任务描述
     */
    @Column("task_description")
    private String taskDescription;

    /**
     * 任务类型 (ANALYSIS, DOWNLOAD, OFFLINE, MAINTENANCE, etc.)
     */
    @Column("task_type")
    private String taskType;

    /**
     * 任务状态 (PENDING, RUNNING, COMPLETED, FAILED, CANCELLED)
     */
    @Column("task_status")
    private String taskStatus;

    /**
     * 任务优先级 (LOW, NORMAL, HIGH, URGENT)
     */
    @Column("priority")
    private String priority;

    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;

    /**
     * 计划开始时间
     */
    @Column("scheduled_start_time")
    private LocalDateTime scheduledStartTime;

    /**
     * 实际开始时间
     */
    @Column("actual_start_time")
    private LocalDateTime actualStartTime;

    /**
     * 计划结束时间
     */
    @Column("scheduled_end_time")
    private LocalDateTime scheduledEndTime;

    /**
     * 实际结束时间
     */
    @Column("actual_end_time")
    private LocalDateTime actualEndTime;

    /**
     * 进度百分比 (0-100)
     */
    @Column("progress_percent")
    private Integer progressPercent;

    /**
     * 会话数量
     */
    @Column("session_count")
    private Long sessionCount;

    /**
     * 数据大小（字节）
     */
    @Column("data_size")
    private Long dataSize;

    /**
     * 任务配置参数（JSON格式）
     */
    @Column("task_config")
    private String taskConfig;

    /**
     * Cron表达式（定时任务）
     */
    @Column("cron_expression")
    private String cronExpression;

    /**
     * 重试次数
     */
    @Column("retry_count")
    private Integer retryCount;

    /**
     * 当前重试次数
     */
    @Column("current_retry_count")
    private Integer currentRetryCount;

    /**
     * 超时时间（秒）
     */
    @Column("timeout_seconds")
    private Integer timeoutSeconds;

    /**
     * 错误信息
     */
    @Column("error_message")
    private String errorMessage;

    /**
     * 执行节点
     */
    @Column("execution_node")
    private String executionNode;

    /**
     * 是否启用
     */
    @Column("enabled")
    private Boolean enabled;

    /**
     * 创建用户ID
     */
    @Column("user_id")
    private Integer userId;

    /**
     * 创建用户名
     */
    @Column("user_name")
    private String userName;

    /**
     * 更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否删除 (0: 未删除, 1: 已删除)
     */
    @Column("deleted")
    private Integer deleted;
}

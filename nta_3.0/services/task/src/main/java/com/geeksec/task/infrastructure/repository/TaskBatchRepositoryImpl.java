package com.geeksec.task.infrastructure.repository;

import com.geeksec.task.infrastructure.mapper.TaskBatchMapper;
import com.geeksec.task.model.entity.TaskBatch;
import com.geeksec.task.model.vo.OfflineTaskBatchProgressVo;
import com.geeksec.task.repository.TaskBatchRepository;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 任务批次仓储实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Repository
public class TaskBatchRepositoryImpl extends ServiceImpl<TaskBatchMapper, TaskBatch> implements TaskBatchRepository {

    @Override
    public List<TaskBatch> findByTaskId(Integer taskId) {
        return list(QueryWrapper.create()
                .where("task_id = ?", taskId)
                .orderBy("batch_id DESC"));
    }

    @Override
    public List<OfflineTaskBatchProgressVo> listBatchByTaskId(Integer taskId) {
        return getMapper().listBatchByTaskId(taskId);
    }

    @Override
    public List<TaskBatch> listTaskBatchItem(List<Integer> taskIdList) {
        return getMapper().listTaskBatchItem(taskIdList);
    }

    @Override
    public Integer countIncompleteTask(Integer taskId) {
        return getMapper().countIncompleteTask(taskId);
    }

    @Override
    public List<TaskBatch> findByBatchStatus(Integer batchStatus) {
        return list(QueryWrapper.create()
                .where("batch_status = ?", batchStatus)
                .orderBy("batch_id DESC"));
    }

    @Override
    public List<TaskBatch> findByTaskType(Integer taskType) {
        return list(QueryWrapper.create()
                .where("task_type = ?", taskType)
                .orderBy("batch_id DESC"));
    }

    @Override
    public boolean updateProgress(Integer batchId, Double progress) {
        TaskBatch taskBatch = new TaskBatch();
        taskBatch.setBatchId(batchId);
        taskBatch.setBatchProgress(progress);
        return updateById(taskBatch);
    }

    @Override
    public boolean updateStatus(Integer batchId, Integer status) {
        TaskBatch taskBatch = new TaskBatch();
        taskBatch.setBatchId(batchId);
        taskBatch.setBatchStatus(status);
        return updateById(taskBatch);
    }
}

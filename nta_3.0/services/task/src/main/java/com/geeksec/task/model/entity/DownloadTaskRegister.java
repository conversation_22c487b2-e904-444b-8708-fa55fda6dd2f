package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 下载任务注册实体类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_download_task_register")
public class DownloadTaskRegister implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Integer userId;

    /**
     * 文件路径
     */
    @Column("path")
    private String path;

    /**
     * 查询条件
     */
    @Column("query")
    private String query;

    /**
     * 任务类型 (1: 数据准备任务)
     */
    @Column("type")
    private Integer type;

    /**
     * 下载次数
     */
    @Column("download_count")
    private Integer downloadCount;

    /**
     * 删除时间
     */
    @Column("delete_time")
    private LocalDateTime deleteTime;

    /**
     * 更新时间
     */
    @Column("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @Column("create_time")
    private LocalDateTime createTime;

    /**
     * 任务类型
     */
    @Column("task_type")
    private Integer taskType;

    /**
     * 错误信息
     */
    @Column("error_msg")
    private String errorMsg;

    /**
     * 任务状态 (1: 待处理, 2: 处理中, 3: 已完成, -1: 失败)
     */
    @Column("status")
    private Integer status;

    /**
     * 前端展示查询条件
     */
    @Column("show_query")
    private String showQuery;

    /**
     * 构造函数
     */
    public DownloadTaskRegister() {}

    /**
     * 构造函数
     * @param id 任务ID
     * @param status 任务状态
     */
    public DownloadTaskRegister(Integer id, Integer status) {
        this.id = id;
        this.status = status;
    }
}

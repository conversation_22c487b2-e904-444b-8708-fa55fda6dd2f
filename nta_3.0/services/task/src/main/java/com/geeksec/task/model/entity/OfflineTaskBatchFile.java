package com.geeksec.task.model.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 离线任务批次文件实体类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_offline_task_batch_file")
public class OfflineTaskBatchFile implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 任务ID
     */
    @Column("task_id")
    private Integer taskId;

    /**
     * 批次ID
     */
    @Column("batch_id")
    private Integer batchId;

    /**
     * 本地文件路径
     */
    @Column("local_path")
    private String localPath;

    /**
     * 服务器文件路径
     */
    @Column("server_path")
    private String serverPath;

    /**
     * 文件大小（字节）
     */
    @Column("file_size")
    private Long fileSize;

    /**
     * 文件状态 (0: 待处理, 1: 处理中, 2: 已完成, 3: 失败)
     */
    @Column("status")
    private Integer status;

    /**
     * 文件类型 (1: PCAP文件, 2: 其他文件)
     */
    @Column("file_type")
    private Integer fileType;

    /**
     * 处理开始时间
     */
    @Column("process_start_time")
    private LocalDateTime processStartTime;

    /**
     * 处理结束时间
     */
    @Column("process_end_time")
    private LocalDateTime processEndTime;

    /**
     * 错误信息
     */
    @Column("error_message")
    private String errorMessage;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 创建者用户ID
     */
    @Column("created_by")
    private Integer createdBy;

    /**
     * 更新者用户ID
     */
    @Column("updated_by")
    private Integer updatedBy;
}

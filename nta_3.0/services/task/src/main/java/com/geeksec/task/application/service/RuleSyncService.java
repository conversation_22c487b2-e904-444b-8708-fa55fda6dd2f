package com.geeksec.task.application.service;

import com.geeksec.task.model.dto.RuleSyncResultDto;

/**
 * 规则同步服务接口
 * 用于同步过滤规则、特征规则、全流量留存和元数据留存配置
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface RuleSyncService {

    /**
     * 同步所有规则和配置
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param serviceId 服务实例ID
     * @return 同步结果
     */
    RuleSyncResultDto syncAllRules(Integer taskId, Integer batchId, Integer serviceId);

    /**
     * 同步特征规则
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param serviceId 服务实例ID
     * @return 同步结果
     */
    RuleSyncResultDto syncFeatureRules(Integer taskId, Integer batchId, Integer serviceId);

    /**
     * 同步过滤规则
     * 
     * @param taskId 任务ID
     * @param serviceId 服务实例ID
     * @return 同步结果
     */
    RuleSyncResultDto syncFilterRules(Integer taskId, Integer serviceId);

    /**
     * 同步全流量留存和流日志配置
     * 
     * @param batchId 批次ID
     * @param serviceId 服务实例ID
     * @return 同步结果
     */
    RuleSyncResultDto syncFlowConfigs(Integer batchId, Integer serviceId);

    /**
     * 生成规则配置文件
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @param serviceId 服务实例ID
     * @return 配置文件路径
     */
    String generateRuleConfigFile(Integer taskId, Integer batchId, Integer serviceId);

    /**
     * 生成过滤配置文件
     * 
     * @param taskId 任务ID
     * @param serviceId 服务实例ID
     * @return 配置文件路径
     */
    String generateFilterConfigFile(Integer taskId, Integer serviceId);

    /**
     * 验证规则配置的有效性
     * 
     * @param taskId 任务ID
     * @param batchId 批次ID
     * @return 验证是否通过
     */
    boolean validateRuleConfig(Integer taskId, Integer batchId);
}

spring:
  application:
    name: auth-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common

  # 数据源配置 - 统一使用PostgreSQL + Druid
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_auth}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}

    # Druid 连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false

  # Redis 配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:1}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# MyBatis-Flex 配置
mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
  global-config:
    logic-delete-column: deleted
    version-column: version
    key-generator-type: snowflake
    print-sql: ${DEBUG_SQL:true}

# Knife4j 配置
knife4j:
  enable: true
  openapi:
    title: NTA 认证服务 API 文档
    description: NTA 3.0 网络流量分析平台 - 认证服务接口文档
    version: 3.0.0
    concat: NTA 开发团队
    email: <EMAIL>
    url: https://www.geeksec.com
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表

# Sa-Token 配置
sa-token:
  token-name: Authorization
  timeout: 7200
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: false

server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: ${CONTEXT_PATH:}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  prometheus:
    metrics:
      export:
        enabled: true

logging:
  level:
    '[com.geeksec.auth]': ${LOG_LEVEL:DEBUG}
    '[com.mybatisflex]': ${LOG_LEVEL:DEBUG}
    '[cn.dev33.satoken]': ${LOG_LEVEL:INFO}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
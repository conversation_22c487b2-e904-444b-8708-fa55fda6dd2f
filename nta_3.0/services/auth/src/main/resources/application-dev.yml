# Auth Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

  # Auth服务开发环境数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_auth_dev}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}

  # Auth服务开发环境Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:1}  # Auth服务使用数据库1
      timeout: 5000ms

# Auth服务特定配置
auth:
  # Sa-Token 开发环境配置
  token:
    # 开发环境token配置
    timeout: 3600  # 开发环境1小时过期
    active-timeout: -1  # 永不冻结
    is-concurrent: true  # 允许并发登录
    is-share: true  # 共享token
    token-style: uuid  # token风格
  
  # 密码策略配置
  password:
    min-length: 6  # 开发环境密码最小长度
    require-uppercase: false
    require-lowercase: false
    require-numbers: false
    require-special-chars: false
    max-attempts: 10  # 开发环境允许更多尝试次数
    lock-duration: 300  # 锁定5分钟
  
  # 开发环境默认用户配置
  default-users:
    - username: admin
      password: admin123
      roles: [ADMIN, USER]
    - username: dev
      password: dev123
      roles: [USER]

# Sa-Token 开发环境配置
sa-token:
  token-name: Authorization
  timeout: 3600  # 开发环境1小时
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  is-log: true  # 开发环境启用日志

# 服务器配置
server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: ${CONTEXT_PATH:/auth}

# 日志配置
logging:
  level:
    '[com.geeksec.auth]': DEBUG
    '[com.mybatisflex]': DEBUG
    '[cn.dev33.satoken]': DEBUG

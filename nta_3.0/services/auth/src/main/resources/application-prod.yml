# Auth Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

  # Auth服务生产环境数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:prod-db.nta.local}:${DB_PORT:5432}/${DB_NAME:nta_auth_prod}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&useSSL=true&sslmode=require
    username: ${DB_USERNAME:nta_auth_prod}
    password: ${DB_PASSWORD}

  # Auth服务生产环境Redis配置
  data:
    redis:
      host: ${REDIS_HOST:prod-redis.nta.local}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD}
      database: ${REDIS_DATABASE:1}  # Auth服务使用数据库1
      timeout: 5000ms
      ssl: ${REDIS_SSL:true}

# Auth服务特定配置
auth:
  # Sa-Token 生产环境配置
  token:
    # 生产环境token配置
    timeout: 28800  # 生产环境8小时过期
    active-timeout: -1  # 永不冻结
    is-concurrent: false  # 生产环境不允许并发登录
    is-share: false  # 生产环境不共享token
    token-style: uuid  # token风格
  
  # 密码策略配置
  password:
    min-length: 12  # 生产环境密码最小长度
    require-uppercase: true
    require-lowercase: true
    require-numbers: true
    require-special-chars: true
    max-attempts: 3  # 生产环境严格限制尝试次数
    lock-duration: 1800  # 锁定30分钟
  
  # 生产环境安全配置
  security:
    enable-rate-limit: true
    rate-limit-requests: 100  # 每分钟最多100次请求
    enable-ip-whitelist: ${AUTH_IP_WHITELIST_ENABLED:false}
    ip-whitelist: ${AUTH_IP_WHITELIST:}
    enable-audit-log: true
    session-timeout: 28800  # 8小时会话超时

# Sa-Token 生产环境配置
sa-token:
  token-name: Authorization
  timeout: 28800  # 生产环境8小时
  active-timeout: -1
  is-concurrent: false  # 生产环境不允许并发登录
  is-share: false
  token-style: uuid
  is-log: false  # 生产环境关闭详细日志

# 服务器配置
server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: ${CONTEXT_PATH:/auth}
  # 生产环境启用HTTPS
  ssl:
    enabled: ${SSL_ENABLED:false}
    key-store: ${SSL_KEY_STORE:}
    key-store-password: ${SSL_KEY_STORE_PASSWORD:}

# 日志配置
logging:
  level:
    '[com.geeksec.auth]': INFO
    '[com.mybatisflex]': WARN
    '[cn.dev33.satoken]': WARN

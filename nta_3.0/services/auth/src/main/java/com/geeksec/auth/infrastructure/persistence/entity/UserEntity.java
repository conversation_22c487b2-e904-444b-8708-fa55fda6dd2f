package com.geeksec.auth.infrastructure.persistence.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户实体
 *
 * 使用 MyBatis-Flex 1.10.9 注解进行映射
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Table("sys_user")
public class UserEntity {

    /**
     * 用户ID - 主键，自增
     */
    @Id(keyType = KeyType.Auto)
    @Column("user_id")
    private Long userId;

    /**
     * 用户名 - 唯一
     */
    @Column("username")
    private String username;

    /**
     * 密码 - 加密存储
     */
    @Column("password")
    private String password;

    /**
     * 显示名称
     */
    @Column("display_name")
    private String displayName;

    /**
     * 用户组ID
     */
    @Column("group_id")
    private Integer groupId;

    /**
     * 用户状态：0-禁用，1-启用
     */
    @Column("status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    @Column("deleted")
    private Integer deleted;

    /**
     * 版本号 - 乐观锁
     */
    @Column("version")
    private Integer version;

    /**
     * 角色关联 - 这个字段不会映射到数据库，需要手动处理
     */
    private transient Set<RoleEntity> roles = new HashSet<>();

    /**
     * 获取ID，兼容原有代码
     * @return 用户ID
     */
    public Long getId() {
        return userId;
    }
}

package com.geeksec.auth.domain.repository;

import com.geeksec.auth.domain.model.Role;
import java.util.List;
import java.util.Optional;

/**
 * 角色仓储接口
 *
 * <AUTHOR>
 */
public interface RoleRepository {
    /**
     * 保存角色
     */
    Role save(Role role);
    
    /**
     * 根据ID查找角色
     */
    Optional<Role> findById(Role.RoleId id);
    
    /**
     * 根据名称查找角色
     */
    Optional<Role> findByName(String name);
    
    /**
     * 查找所有角色
     */
    List<Role> findAll();
    
    /**
     * 删除角色
     */
    void delete(Role role);
    
    /**
     * 检查角色名是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 检查角色是否被用户使用
     */
    boolean isRoleUsedByUsers(List<Long> roleIds);
}

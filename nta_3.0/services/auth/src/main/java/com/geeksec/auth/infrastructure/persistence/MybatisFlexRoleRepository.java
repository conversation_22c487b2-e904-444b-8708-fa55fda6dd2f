package com.geeksec.auth.infrastructure.persistence;

import com.geeksec.auth.domain.model.Role;
import com.geeksec.auth.domain.repository.RoleRepository;
import com.geeksec.auth.infrastructure.persistence.entity.PermissionEntity;
import com.geeksec.auth.infrastructure.persistence.entity.RoleEntity;
import com.geeksec.auth.infrastructure.persistence.mapper.RoleEntityMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.PermissionMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.RoleMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.RolePermissionMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MyBatis Flex角色仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class MybatisFlexRoleRepository implements RoleRepository {
    private final RoleMapper flexRoleMapper;
    private final RoleEntityMapper roleEntityMapper;
    private final JdbcTemplate jdbcTemplate;
    private final RolePermissionMapper rolePermissionMapper;
    private final PermissionMapper permissionMapper;

    public MybatisFlexRoleRepository(RoleMapper flexRoleMapper,
                                    RoleEntityMapper roleEntityMapper,
                                    JdbcTemplate jdbcTemplate,
                                    RolePermissionMapper rolePermissionMapper,
                                    PermissionMapper permissionMapper) {
        this.flexRoleMapper = flexRoleMapper;
        this.roleEntityMapper = roleEntityMapper;
        this.jdbcTemplate = jdbcTemplate;
        this.rolePermissionMapper = rolePermissionMapper;
        this.permissionMapper = permissionMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Role save(Role role) {
        RoleEntity roleEntity = roleEntityMapper.toEntity(role);

        // 如果ID为空，则插入，否则更新
        if (roleEntity.getId() == null) {
            flexRoleMapper.insert(roleEntity);
        } else {
            flexRoleMapper.update(roleEntity);
            // 删除角色所有权限关联
            rolePermissionMapper.deleteRoleAllPermissions(roleEntity.getId());
        }

        // 添加角色权限关联
        if (roleEntity.getPermissions() != null && !roleEntity.getPermissions().isEmpty()) {
            Set<Long> permissionIds = roleEntity.getPermissions().stream()
                    .map(PermissionEntity::getId)
                    .collect(Collectors.toSet());
            rolePermissionMapper.batchAddRolePermission(roleEntity.getId(), permissionIds);
        }

        return findById(new Role.RoleId(roleEntity.getId()))
                .orElseThrow(() -> new IllegalStateException("Role not found after save"));
    }

    @Override
    public Optional<Role> findById(Role.RoleId id) {
        RoleEntity roleEntity = flexRoleMapper.selectOneById(id.getValue());
        if (roleEntity != null) {
            // 查询角色权限
            List<Long> permissionIds = rolePermissionMapper.findPermissionIdsByRoleId(roleEntity.getId());
            if (!permissionIds.isEmpty()) {
                Set<PermissionEntity> permissions = new HashSet<>();
                for (Long permissionId : permissionIds) {
                    PermissionEntity permissionEntity = permissionMapper.selectOneById(permissionId);
                    if (permissionEntity != null) {
                        permissions.add(permissionEntity);
                    }
                }
                roleEntity.setPermissions(permissions);
            }
            return Optional.of(roleEntityMapper.toDomain(roleEntity));
        }
        return Optional.empty();
    }

    @Override
    public Optional<Role> findByName(String name) {
        RoleEntity roleEntity = flexRoleMapper.findByName(name);
        if (roleEntity != null) {
            // 查询角色权限
            List<Long> permissionIds = rolePermissionMapper.findPermissionIdsByRoleId(roleEntity.getId());
            if (!permissionIds.isEmpty()) {
                Set<PermissionEntity> permissions = new HashSet<>();
                for (Long permissionId : permissionIds) {
                    PermissionEntity permissionEntity = permissionMapper.selectOneById(permissionId);
                    if (permissionEntity != null) {
                        permissions.add(permissionEntity);
                    }
                }
                roleEntity.setPermissions(permissions);
            }
            return Optional.of(roleEntityMapper.toDomain(roleEntity));
        }
        return Optional.empty();
    }

    @Override
    public List<Role> findAll() {
        List<RoleEntity> roleEntities = flexRoleMapper.selectAll();
        for (RoleEntity roleEntity : roleEntities) {
            // 查询角色权限
            List<Long> permissionIds = rolePermissionMapper.findPermissionIdsByRoleId(roleEntity.getId());
            if (!permissionIds.isEmpty()) {
                Set<PermissionEntity> permissions = new HashSet<>();
                for (Long permissionId : permissionIds) {
                    PermissionEntity permissionEntity = permissionMapper.selectOneById(permissionId);
                    if (permissionEntity != null) {
                        permissions.add(permissionEntity);
                    }
                }
                roleEntity.setPermissions(permissions);
            }
        }
        return roleEntities.stream()
                .map(roleEntityMapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Role role) {
        Long roleId = role.getId().getValue();
        // 删除角色权限关联
        rolePermissionMapper.deleteRoleAllPermissions(roleId);
        // 删除角色
        flexRoleMapper.deleteById(roleId);
    }

    @Override
    public boolean existsByName(String name) {
        return flexRoleMapper.existsByName(name);
    }

    @Override
    public boolean isRoleUsedByUsers(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return false;
        }

        String sql = "SELECT COUNT(1) > 0 FROM user_role WHERE role_id IN (" +
                     String.join(",", roleIds.stream().map(String::valueOf).collect(Collectors.toList())) +
                     ")";

        return Boolean.TRUE.equals(jdbcTemplate.queryForObject(sql, Boolean.class));
    }
}

package com.geeksec.auth.application.service;

import java.util.Arrays;

import org.springframework.stereotype.Service;

import cn.dev33.satoken.stp.StpUtil;

/**
 * 权限检查服务实现
 * 使用 SA-Token 进行权限验证
 *
 * <AUTHOR>
 */
@Service
public class PermissionCheckServiceImpl implements PermissionCheckService {

    public PermissionCheckServiceImpl() {
        // 使用 SA-Token，无需注入其他服务
    }

    @Override
    public boolean hasAllPermissions(String token, String... permissions) {
        if (token == null || permissions == null || permissions.length == 0) {
            return false;
        }

        try {
            // 使用 SA-Token 验证令牌并检查权限
            Object loginId = StpUtil.getLoginIdByToken(token);
            if (loginId == null) {
                return false;
            }

            // 检查是否拥有所有权限
            return Arrays.stream(permissions)
                    .allMatch(permission -> StpUtil.hasPermission(loginId, permission));
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean hasAnyPermission(String token, String... permissions) {
        if (token == null || permissions == null || permissions.length == 0) {
            return false;
        }

        try {
            // 使用 SA-Token 验证令牌并检查权限
            Object loginId = StpUtil.getLoginIdByToken(token);
            if (loginId == null) {
                return false;
            }

            // 检查是否拥有任一权限
            return Arrays.stream(permissions)
                    .anyMatch(permission -> StpUtil.hasPermission(loginId, permission));
        } catch (Exception e) {
            return false;
        }
    }
}

package com.geeksec.auth.domain.repository;

import com.geeksec.auth.domain.model.Permission;
import java.util.List;
import java.util.Optional;

/**
 * 权限仓储接口
 *
 * <AUTHOR>
 */
public interface PermissionRepository {
    /**
     * 保存权限
     */
    Permission save(Permission permission);
    
    /**
     * 根据ID查找权限
     */
    Optional<Permission> findById(Permission.PermissionId id);
    
    /**
     * 根据键查找权限
     */
    Optional<Permission> findByKey(String key);
    
    /**
     * 查找所有权限
     */
    List<Permission> findAll();
    
    /**
     * 删除权限
     */
    void delete(Permission permission);
    
    /**
     * 检查权限键是否存在
     */
    boolean existsByKey(String key);
}

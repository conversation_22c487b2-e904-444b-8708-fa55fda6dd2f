package com.geeksec.auth.infrastructure.persistence.mapper.flex;

import com.geeksec.auth.infrastructure.persistence.entity.RoleEntity;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import static com.geeksec.auth.infrastructure.persistence.entity.table.RoleEntityTableDef.ROLE_ENTITY;

/**
 * 角色Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper + 表定义类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface RoleMapper extends BaseMapper<RoleEntity> {

    /**
     * 根据角色名查询角色
     *
     * @param name 角色名
     * @return 角色实体
     */
    default RoleEntity findByName(String name) {
        return selectOneByQuery(QueryWrapper.create()
                .where(ROLE_ENTITY.NAME.eq(name))
                .and(ROLE_ENTITY.DELETED.eq(0)));
    }

    /**
     * 检查角色名是否存在
     *
     * @param name 角色名
     * @return 是否存在
     */
    default boolean existsByName(String name) {
        return selectCountByQuery(QueryWrapper.create()
                .where(ROLE_ENTITY.NAME.eq(name))
                .and(ROLE_ENTITY.DELETED.eq(0))) > 0;
    }
}

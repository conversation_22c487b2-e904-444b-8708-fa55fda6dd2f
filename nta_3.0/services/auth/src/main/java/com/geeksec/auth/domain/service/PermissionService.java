package com.geeksec.auth.domain.service;

import com.geeksec.auth.domain.model.Permission;
import com.geeksec.auth.domain.model.User;
import com.geeksec.auth.domain.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限领域服务
 *
 * <AUTHOR>
 */
@Service
public class PermissionService {
    private final UserRepository userRepository;

    @Autowired
    public PermissionService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * 检查用户是否拥有指定权限（AND逻辑）
     *
     * @param userId 用户ID
     * @param permissions 权限列表
     * @return 是否拥有所有权限
     */
    public boolean hasAllPermissions(Long userId, String... permissions) {
        return userRepository.findById(new User.UserId(userId))
                .map(user -> {
                    Set<String> userPermissionKeys = getUserPermissionKeys(user);
                    return Arrays.stream(permissions)
                            .allMatch(userPermissionKeys::contains);
                })
                .orElse(false);
    }

    /**
     * 检查用户是否拥有指定权限（OR逻辑）
     *
     * @param userId 用户ID
     * @param permissions 权限列表
     * @return 是否拥有任一权限
     */
    public boolean hasAnyPermission(Long userId, String... permissions) {
        return userRepository.findById(new User.UserId(userId))
                .map(user -> {
                    Set<String> userPermissionKeys = getUserPermissionKeys(user);
                    return Arrays.stream(permissions)
                            .anyMatch(userPermissionKeys::contains);
                })
                .orElse(false);
    }

    /**
     * 获取用户权限键集合
     *
     * @param user 用户
     * @return 权限键集合
     */
    private Set<String> getUserPermissionKeys(User user) {
        return user.getRoles().stream()
                .flatMap(role -> role.getPermissions().stream())
                .map(Permission::getKey)
                .collect(Collectors.toSet());
    }
}

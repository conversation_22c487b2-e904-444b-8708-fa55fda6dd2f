package com.geeksec.auth.application.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.geeksec.auth.application.dto.command.CreateRoleCommand;
import com.geeksec.auth.application.dto.command.UpdateRoleCommand;
import com.geeksec.auth.application.dto.response.RoleResponseDto;
import com.geeksec.auth.domain.model.Permission;
import com.geeksec.auth.domain.model.Role;
import com.geeksec.auth.domain.repository.PermissionRepository;
import com.geeksec.auth.domain.repository.RoleRepository;

/**
 * 角色应用服务
 *
 * <AUTHOR>
 */
@Service
public class RoleApplicationService {
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;

    public RoleApplicationService(RoleRepository roleRepository, PermissionRepository permissionRepository) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
    }

    /**
     * 创建角色
     */
    @Transactional(rollbackFor = Exception.class)
    public RoleResponseDto createRole(CreateRoleCommand command) {
        // 检查角色名是否存在
        if (roleRepository.existsByName(command.name())) {
            throw new RuntimeException("角色名已存在");
        }

        // 创建角色
        Role role = new Role.Builder()
                .withName(command.name())
                .withDescription(command.description())
                .build();

        // 保存角色
        Role savedRole = roleRepository.save(role);

        // 分配权限
        if (command.permissionIds() != null && !command.permissionIds().isEmpty()) {
            assignPermissions(savedRole.getId().getValue(), command.permissionIds());
        }

        // 返回DTO
        return toResponseDto(savedRole);
    }

    /**
     * 获取角色
     */
    public Optional<RoleResponseDto> getRole(Long roleId) {
        return roleRepository.findById(new Role.RoleId(roleId))
                .map(this::toResponseDto);
    }

    /**
     * 获取所有角色
     */
    public List<RoleResponseDto> getAllRoles() {
        return roleRepository.findAll().stream()
                .map(this::toResponseDto)
                .collect(Collectors.toList());
    }

    /**
     * 更新角色
     */
    @Transactional(rollbackFor = Exception.class)
    public Optional<RoleResponseDto> updateRole(Long roleId, UpdateRoleCommand command) {
        return roleRepository.findById(new Role.RoleId(roleId))
                .map(role -> {
                    // 检查角色名是否存在
                    if (!role.getName().equals(command.name()) && roleRepository.existsByName(command.name())) {
                        throw new RuntimeException("角色名已存在");
                    }

                    // 更新角色信息
                    Role updatedRole = new Role.Builder()
                            .withId(role.getId())
                            .withName(command.name())
                            .withDescription(command.description())
                            .withPermissions(role.getPermissions())
                            .build();

                    // 保存角色
                    Role savedRole = roleRepository.save(updatedRole);

                    // 更新权限
                    if (command.permissionIds() != null) {
                        assignPermissions(savedRole.getId().getValue(), command.permissionIds());
                    }

                    return savedRole;
                })
                .map(this::toResponseDto);
    }

    /**
     * 删除角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long roleId) {
        return roleRepository.findById(new Role.RoleId(roleId))
                .map(role -> {
                    roleRepository.delete(role);
                    return true;
                })
                .orElse(false);
    }

    /**
     * 批量删除角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteRoles(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return false;
        }

        // 检查角色是否被用户使用
        if (roleRepository.isRoleUsedByUsers(roleIds)) {
            throw new RuntimeException("角色正在被用户使用，无法删除");
        }

        // 删除角色
        roleIds.forEach(this::deleteRole);

        return true;
    }

    /**
     * 分配权限
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(Long roleId, List<Long> permissionIds) {
        return roleRepository.findById(new Role.RoleId(roleId))
                .map(role -> {
                    // 获取权限
                    List<Permission> permissions = permissionIds.stream()
                            .map(id -> permissionRepository.findById(new Permission.PermissionId(id))
                                    .orElseThrow(() -> new RuntimeException("权限不存在: " + id)))
                            .collect(Collectors.toList());

                    // 更新角色
                    Role updatedRole = new Role.Builder()
                            .withId(role.getId())
                            .withName(role.getName())
                            .withDescription(role.getDescription())
                            .withPermissions(new java.util.HashSet<>(permissions))
                            .build();

                    // 保存角色
                    roleRepository.save(updatedRole);

                    return true;
                })
                .orElse(false);
    }

    /**
     * 将领域模型转换为响应DTO
     */
    private RoleResponseDto toResponseDto(Role role) {
        List<Long> permissionIds = role.getPermissions().stream()
                .map(permission -> permission.getId().getValue())
                .collect(Collectors.toList());

        return new RoleResponseDto(
                role.getId().getValue(),
                role.getName(),
                role.getDescription(),
                permissionIds
        );
    }
}

package com.geeksec.auth.infrastructure.persistence.mapper.flex;

import com.geeksec.auth.infrastructure.persistence.entity.UserEntity;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import static com.geeksec.auth.infrastructure.persistence.entity.table.UserEntityTableDef.USER_ENTITY;

/**
 * 用户Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper + 表定义类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<UserEntity> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户实体
     */
    default UserEntity findByUsername(String username) {
        return selectOneByQuery(QueryWrapper.create()
                .where(USER_ENTITY.USERNAME.eq(username))
                .and(USER_ENTITY.DELETED.eq(0)));
    }

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    default boolean existsByUsername(String username) {
        return selectCountByQuery(QueryWrapper.create()
                .where(USER_ENTITY.USERNAME.eq(username))
                .and(USER_ENTITY.DELETED.eq(0))) > 0;
    }
}

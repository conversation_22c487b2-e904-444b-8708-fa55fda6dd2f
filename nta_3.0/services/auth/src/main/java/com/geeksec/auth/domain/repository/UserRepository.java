package com.geeksec.auth.domain.repository;

import com.geeksec.auth.domain.model.User;
import java.util.Optional;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 用户仓储接口
 */
public interface UserRepository {
    /**
     * 保存用户
     */
    User save(User user);
    
    /**
     * 根据ID查找用户
     */
    Optional<User> findById(User.UserId id);
    
    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(User.Username username);
    
    /**
     * 查找所有用户
     */
    List<User> findAll();
    
    /**
     * 分页查找所有用户
     */
    Page<User> findAll(Pageable pageable);
    
    /**
     * 删除用户
     */
    void delete(User user);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(User.Username username);
}

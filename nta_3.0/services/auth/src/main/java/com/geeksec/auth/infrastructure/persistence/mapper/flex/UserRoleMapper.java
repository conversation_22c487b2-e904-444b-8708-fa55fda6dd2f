package com.geeksec.auth.infrastructure.persistence.mapper.flex;

import com.geeksec.auth.infrastructure.persistence.entity.UserRoleEntity;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 用户角色关联Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRoleEntity> {

    /**
     * 根据用户ID查询角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    default List<Long> findRoleIdsByUserId(Long userId) {
        return selectListByQuery(QueryWrapper.create()
                .where(UserRoleEntity::getUserId).eq(userId))
                .stream()
                .map(UserRoleEntity::getRoleId)
                .toList();
    }

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    default List<Long> findUserIdsByRoleId(Long roleId) {
        return selectListByQuery(QueryWrapper.create()
                .where(UserRoleEntity::getRoleId).eq(roleId))
                .stream()
                .map(UserRoleEntity::getUserId)
                .toList();
    }

    /**
     * 添加用户角色关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 影响行数
     */
    default int addUserRole(Long userId, Long roleId) {
        UserRoleEntity entity = new UserRoleEntity();
        entity.setUserId(userId);
        entity.setRoleId(roleId);
        return insert(entity);
    }

    /**
     * 批量添加用户角色关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 影响行数
     */
    default int batchAddUserRole(Long userId, Set<Long> roleIds) {
        List<UserRoleEntity> entities = roleIds.stream()
                .map(roleId -> {
                    UserRoleEntity entity = new UserRoleEntity();
                    entity.setUserId(userId);
                    entity.setRoleId(roleId);
                    return entity;
                })
                .toList();
        return insertBatch(entities);
    }

    /**
     * 删除用户角色关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 影响行数
     */
    default int deleteUserRole(Long userId, Long roleId) {
        return deleteByQuery(QueryWrapper.create()
                .where(UserRoleEntity::getUserId).eq(userId)
                .and(UserRoleEntity::getRoleId).eq(roleId));
    }

    /**
     * 删除用户所有角色关联
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    default int deleteUserAllRoles(Long userId) {
        return deleteByQuery(QueryWrapper.create()
                .where(UserRoleEntity::getUserId).eq(userId));
    }

    /**
     * 删除角色所有用户关联
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    default int deleteRoleAllUsers(Long roleId) {
        return deleteByQuery(QueryWrapper.create()
                .where(UserRoleEntity::getRoleId).eq(roleId));
    }
}

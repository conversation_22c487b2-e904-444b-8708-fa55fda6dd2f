package com.geeksec.auth.application.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.geeksec.auth.application.dto.command.CreatePermissionCommand;
import com.geeksec.auth.application.dto.command.UpdatePermissionCommand;
import com.geeksec.auth.application.dto.response.PermissionResponseDto;
import com.geeksec.auth.domain.model.Permission;
import com.geeksec.auth.domain.repository.PermissionRepository;

/**
 * 权限应用服务
 *
 * <AUTHOR>
 */
@Service
public class PermissionApplicationService {
    private final PermissionRepository permissionRepository;

    public PermissionApplicationService(PermissionRepository permissionRepository) {
        this.permissionRepository = permissionRepository;
    }

    /**
     * 获取所有权限
     */
    public List<PermissionResponseDto> getAllPermissions() {
        return permissionRepository.findAll().stream()
                .map(this::toResponseDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取权限
     */
    public Optional<PermissionResponseDto> getPermission(Long permissionId) {
        return permissionRepository.findById(new Permission.PermissionId(permissionId))
                .map(this::toResponseDto);
    }

    /**
     * 创建权限
     */
    @Transactional(rollbackFor = Exception.class)
    public PermissionResponseDto createPermission(CreatePermissionCommand command) {
        // 检查权限键是否存在
        if (permissionRepository.existsByKey(command.key())) {
            throw new RuntimeException("权限键已存在");
        }

        // 创建权限
        Permission permission = new Permission(
                null,
                command.key(),
                command.name(),
                "MENU".equals(command.type()) ? Permission.PermissionType.MENU : Permission.PermissionType.OPERATION
        );

        // 保存权限
        Permission savedPermission = permissionRepository.save(permission);

        // 返回DTO
        return toResponseDto(savedPermission);
    }

    /**
     * 更新权限
     */
    @Transactional(rollbackFor = Exception.class)
    public Optional<PermissionResponseDto> updatePermission(Long permissionId, UpdatePermissionCommand command) {
        return permissionRepository.findById(new Permission.PermissionId(permissionId))
                .map(permission -> {
                    // 检查权限键是否存在
                    if (!permission.getKey().equals(command.key()) && permissionRepository.existsByKey(command.key())) {
                        throw new RuntimeException("权限键已存在");
                    }

                    // 更新权限
                    Permission updatedPermission = new Permission(
                            permission.getId(),
                            command.key(),
                            command.name(),
                            "MENU".equals(command.type()) ? Permission.PermissionType.MENU : Permission.PermissionType.OPERATION
                    );

                    // 保存权限
                    return permissionRepository.save(updatedPermission);
                })
                .map(this::toResponseDto);
    }

    /**
     * 删除权限
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePermission(Long permissionId) {
        return permissionRepository.findById(new Permission.PermissionId(permissionId))
                .map(permission -> {
                    permissionRepository.delete(permission);
                    return true;
                })
                .orElse(false);
    }

    /**
     * 将领域模型转换为响应DTO
     */
    private PermissionResponseDto toResponseDto(Permission permission) {
        return new PermissionResponseDto(
                permission.getId().getValue(),
                permission.getKey(),
                permission.getName(),
                permission.getType() == Permission.PermissionType.MENU ? "MENU" : "OPERATION"
        );
    }
}

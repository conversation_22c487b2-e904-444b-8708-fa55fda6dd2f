package com.geeksec.auth.infrastructure.persistence.entity;

import com.geeksec.auth.domain.model.Permission;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 权限实体
 *
 * 使用 MyBatis-Flex 1.10.9 注解进行映射
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Table("sys_permission")
public class PermissionEntity {

    /**
     * 权限ID - 主键，自增
     */
    @Id(keyType = KeyType.Auto)
    @Column("permission_id")
    private Long id;

    /**
     * 权限编码
     */
    @Column("permission_code")
    private String code;

    /**
     * 权限名称
     */
    @Column("permission_name")
    private String name;

    /**
     * 权限描述
     */
    @Column("description")
    private String description;

    /**
     * 创建时间
     */
    @Column(value = "created_at", onInsertValue = "now()")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(value = "updated_at", onInsertValue = "now()", onUpdateValue = "now()")
    private LocalDateTime updatedAt;

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    @Column("deleted")
    private Integer deleted;

    /**
     * 角色关联 - 这个字段不会映射到数据库，需要手动处理
     */
    private transient Set<RoleEntity> roles = new HashSet<>();

    public Permission toDomain() {
        // 根据权限编码确定权限类型，默认为OPERATION类型
        Permission.PermissionType type = code.contains(":") ?
            Permission.PermissionType.OPERATION : Permission.PermissionType.MENU;

        return new Permission(
            new Permission.PermissionId(id),
            code,
            name,
            type
        );
    }

    public static PermissionEntity fromDomain(Permission permission) {
        PermissionEntity entity = new PermissionEntity();
        entity.setId(permission.getId().getValue());
        entity.setCode(permission.getKey());
        entity.setName(permission.getName());
        // 领域模型中没有description属性，设置为null
        entity.setDescription(null);
        return entity;
    }
}

package com.geeksec.auth.infrastructure.persistence;

import com.geeksec.auth.domain.repository.RemoteKeyRepository;
import com.geeksec.auth.infrastructure.persistence.entity.RemoteKeyEntity;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.RemoteKeyMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * MyBatis Flex远程密钥仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class MybatisFlexRemoteKeyRepository implements RemoteKeyRepository {
    private final RemoteKeyMapper remoteKeyMapper;

    public MybatisFlexRemoteKeyRepository(RemoteKeyMapper remoteKeyMapper) {
        this.remoteKeyMapper = remoteKeyMapper;
    }

    @Override
    public List<String> getAllRemoteKeys() {
        return remoteKeyMapper.getAllApiKeys();
    }

    @Override
    public void addRemoteKey(String remoteKey) {
        RemoteKeyEntity entity = new RemoteKeyEntity();
        entity.setApiKey(remoteKey);
        // createdAt 会通过 BaseAuditableEntity 的 @Column(onInsertValue = "now()") 自动设置
        remoteKeyMapper.insert(entity);
    }
}

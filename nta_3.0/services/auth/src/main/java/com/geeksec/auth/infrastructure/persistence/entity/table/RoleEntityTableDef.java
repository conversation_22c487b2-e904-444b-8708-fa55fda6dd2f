package com.geeksec.auth.infrastructure.persistence.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 角色实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class RoleEntityTableDef extends TableDef {

    /**
     * 角色实体表定义实例
     */
    public static final RoleEntityTableDef ROLE_ENTITY = new RoleEntityTableDef();

    /**
     * 角色ID - 主键，自增
     */
    public final QueryColumn ID = new QueryColumn(this, "role_id");

    /**
     * 角色名称
     */
    public final QueryColumn NAME = new QueryColumn(this, "role_name");

    /**
     * 角色描述
     */
    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    public final QueryColumn DELETED = new QueryColumn(this, "deleted");

    /**
     * 构造函数
     */
    public RoleEntityTableDef() {
        super("", "sys_role");
    }

    /**
     * 带别名的构造函数
     */
    public RoleEntityTableDef(String alias) {
        super("", "sys_role", alias);
    }
}

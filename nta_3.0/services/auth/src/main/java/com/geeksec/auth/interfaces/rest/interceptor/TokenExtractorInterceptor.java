package com.geeksec.auth.interfaces.rest.interceptor;

import com.geeksec.auth.application.service.PermissionCheckService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 令牌提取拦截器
 * 用于从请求中提取令牌并设置到 PermissionCheckServiceImpl 中
 *
 * <AUTHOR>
 */
@Component
public class TokenExtractorInterceptor implements HandlerInterceptor {
    private final PermissionCheckService permissionCheckService;

    public TokenExtractorInterceptor(PermissionCheckService permissionCheckService) {
        this.permissionCheckService = permissionCheckService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String token = extractToken(request);
        if (token != null) {
            request.setAttribute("token", token);
        }
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        request.removeAttribute("token");
    }
    
    /**
     * 从请求头中提取令牌
     *
     * @param request 请求
     * @return 令牌
     */
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}

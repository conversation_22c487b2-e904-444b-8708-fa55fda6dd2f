package com.geeksec.auth.infrastructure.persistence.mapper.flex;

import com.geeksec.auth.infrastructure.persistence.entity.RolePermissionEntity;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 角色权限关联Mapper接口
 *
 * 使用 MyBatis-Flex 1.10.9 BaseMapper
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface RolePermissionMapper extends BaseMapper<RolePermissionEntity> {

    /**
     * 根据角色ID查询权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    default List<Long> findPermissionIdsByRoleId(Long roleId) {
        return selectListByQuery(QueryWrapper.create()
                .where(RolePermissionEntity::getRoleId).eq(roleId))
                .stream()
                .map(RolePermissionEntity::getPermissionId)
                .toList();
    }

    /**
     * 根据权限ID查询角色ID列表
     *
     * @param permissionId 权限ID
     * @return 角色ID列表
     */
    default List<Long> findRoleIdsByPermissionId(Long permissionId) {
        return selectListByQuery(QueryWrapper.create()
                .where(RolePermissionEntity::getPermissionId).eq(permissionId))
                .stream()
                .map(RolePermissionEntity::getRoleId)
                .toList();
    }

    /**
     * 添加角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 影响行数
     */
    default int addRolePermission(Long roleId, Long permissionId) {
        RolePermissionEntity entity = new RolePermissionEntity();
        entity.setRoleId(roleId);
        entity.setPermissionId(permissionId);
        return insert(entity);
    }

    /**
     * 批量添加角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 影响行数
     */
    default int batchAddRolePermission(Long roleId, Set<Long> permissionIds) {
        List<RolePermissionEntity> entities = permissionIds.stream()
                .map(permissionId -> {
                    RolePermissionEntity entity = new RolePermissionEntity();
                    entity.setRoleId(roleId);
                    entity.setPermissionId(permissionId);
                    return entity;
                })
                .toList();
        return insertBatch(entities);
    }

    /**
     * 删除角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 影响行数
     */
    default int deleteRolePermission(Long roleId, Long permissionId) {
        return deleteByQuery(QueryWrapper.create()
                .where(RolePermissionEntity::getRoleId).eq(roleId)
                .and(RolePermissionEntity::getPermissionId).eq(permissionId));
    }

    /**
     * 删除角色所有权限关联
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    default int deleteRoleAllPermissions(Long roleId) {
        return deleteByQuery(QueryWrapper.create()
                .where(RolePermissionEntity::getRoleId).eq(roleId));
    }

    /**
     * 删除权限所有角色关联
     *
     * @param permissionId 权限ID
     * @return 影响行数
     */
    default int deletePermissionAllRoles(Long permissionId) {
        return deleteByQuery(QueryWrapper.create()
                .where(RolePermissionEntity::getPermissionId).eq(permissionId));
    }
}

package com.geeksec.auth.infrastructure.persistence.mapper;

import com.geeksec.auth.domain.model.Role;
import com.geeksec.auth.infrastructure.persistence.entity.RoleEntity;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * 角色映射器
 *
 * <AUTHOR>
 */
@Component
public class RoleEntityMapper {

    private final PermissionEntityMapper permissionEntityMapper;

    public RoleEntityMapper(PermissionEntityMapper permissionEntityMapper) {
        this.permissionEntityMapper = permissionEntityMapper;
    }

    /**
     * 将领域模型转换为实体
     *
     * @param role 角色领域模型
     * @return 角色实体
     */
    public RoleEntity toEntity(Role role) {
        RoleEntity entity = new RoleEntity();
        entity.setId(role.getId().getValue());
        entity.setName(role.getName());
        entity.setDescription(role.getDescription());
        
        // 转换权限
        entity.setPermissions(role.getPermissions().stream()
                .map(permissionEntityMapper::toEntity)
                .collect(Collectors.toSet()));
        
        return entity;
    }

    /**
     * 将实体转换为领域模型
     *
     * @param entity 角色实体
     * @return 角色领域模型
     */
    public Role toDomain(RoleEntity entity) {
        return new Role.Builder()
                .withId(new Role.RoleId(entity.getId()))
                .withName(entity.getName())
                .withDescription(entity.getDescription())
                .withPermissions(entity.getPermissions().stream()
                        .map(permissionEntityMapper::toDomain)
                        .collect(Collectors.toSet()))
                .build();
    }
}

package com.geeksec.auth.infrastructure.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import cn.hutool.crypto.digest.BCrypt;

import jakarta.annotation.PostConstruct;
import java.security.SecureRandom;

/**
 * 密码配置类
 * 使用 Hutool 的 BCrypt 替代 Spring Security 的 PasswordEncoder
 *
 * <AUTHOR>
 */
@Configuration
public class PasswordConfig {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new PasswordEncoder() {
            @Override
            public String encode(CharSequence rawPassword) {
                return BCrypt.hashpw(rawPassword.toString(), BCrypt.gensalt());
            }

            @Override
            public boolean matches(CharSequence rawPassword, String encodedPassword) {
                return BCrypt.checkpw(rawPassword.toString(), encodedPassword);
            }
        };
    }

    @PostConstruct
    public void init() {
        // 初始化安全随机数生成器
        SECURE_RANDOM.nextBytes(new byte[16]);
    }

    /**
     * 简单的密码编码器接口
     */
    public interface PasswordEncoder {
        /**
         * 编码密码
         */
        String encode(CharSequence rawPassword);

        /**
         * 验证密码
         */
        boolean matches(CharSequence rawPassword, String encodedPassword);
    }
}

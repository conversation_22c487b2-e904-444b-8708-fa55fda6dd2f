package com.geeksec.auth.application.service;

/**
 * 权限检查服务接口
 *
 * <AUTHOR>
 */
public interface PermissionCheckService {
    /**
     * 检查是否拥有所有权限
     *
     * @param token 令牌
     * @param permissions 权限列表
     * @return 是否拥有所有权限
     */
    boolean hasAllPermissions(String token, String... permissions);

    /**
     * 检查是否拥有任一权限
     *
     * @param token 令牌
     * @param permissions 权限列表
     * @return 是否拥有任一权限
     */
    boolean hasAnyPermission(String token, String... permissions);
} 
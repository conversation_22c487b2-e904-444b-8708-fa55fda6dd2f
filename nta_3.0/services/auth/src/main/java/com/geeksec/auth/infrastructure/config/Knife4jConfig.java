package com.geeksec.auth.infrastructure.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Knife4j API 文档配置类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Configuration
public class Knife4jConfig {

    @Value("${server.port:8081}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * 配置 OpenAPI 文档信息
     */
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(buildApiInfo())
                .servers(buildServers())
                .addSecurityItem(new SecurityRequirement().addList("saToken"))
                .components(new Components()
                        .addSecuritySchemes("saToken", new SecurityScheme()
                                .name("saToken")
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("Sa-Token")));
    }

    /**
     * 构建 API 信息
     */
    private Info buildApiInfo() {
        return new Info()
                .title("NTA 认证服务 API 文档")
                .description("NTA 3.0 网络流量分析平台 - 认证服务接口文档\n\n" +
                           "技术栈：\n" +
                           "- MyBatis-Flex 1.10.9 (ORM框架)\n" +
                           "- Knife4j 4.4.0 (API文档)\n" +
                           "- Sa-Token 1.44.0 (权限认证)")
                .version("3.0.0")
                .contact(buildContact())
                .license(buildLicense());
    }

    /**
     * 构建联系人信息
     */
    private Contact buildContact() {
        return new Contact()
                .name("NTA 开发团队")
                .email("<EMAIL>")
                .url("https://www.geeksec.com");
    }

    /**
     * 构建许可证信息
     */
    private License buildLicense() {
        return new License()
                .name("Apache 2.0")
                .url("https://www.apache.org/licenses/LICENSE-2.0.html");
    }

    /**
     * 构建服务器列表
     */
    private List<Server> buildServers() {
        String serverUrl = "http://localhost:" + serverPort;
        if (contextPath != null && !contextPath.isEmpty()) {
            serverUrl += contextPath;
        }

        Server server = new Server()
                .url(serverUrl)
                .description("认证服务开发环境");

        return List.of(server);
    }
}

package com.geeksec.auth.infrastructure.config;

import com.geeksec.auth.interfaces.rest.interceptor.TokenExtractorInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Bean配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class BeanConfig implements WebMvcConfigurer {

    private final TokenExtractorInterceptor tokenExtractorInterceptor;

    public BeanConfig(TokenExtractorInterceptor tokenExtractorInterceptor) {
        this.tokenExtractorInterceptor = tokenExtractorInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenExtractorInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns("/api/v1/auth/**");
    }
}

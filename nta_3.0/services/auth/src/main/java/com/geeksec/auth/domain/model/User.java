package com.geeksec.auth.domain.model;

import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

/**
 * 用户领域模型
 */
public class User {
    private UserId id;
    private Username username;
    private Password password;
    private String displayName;
    private UserStatus status;
    private Set<Role> roles;
    private Instant createdAt;
    private Instant updatedAt;

    // 私有构造函数，通过Builder创建
    private User(UserId id, Username username, Password password, String displayName,
                UserStatus status, Set<Role> roles, Instant createdAt, Instant updatedAt) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.displayName = displayName;
        this.status = status;
        this.roles = roles;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // 领域行为
    public boolean authenticate(String rawPassword) {
        return password.matches(rawPassword);
    }

    public boolean hasPermission(Permission permission) {
        return roles.stream()
                .anyMatch(role -> role.hasPermission(permission));
    }

    public boolean hasAllPermissions(Set<Permission> permissions) {
        return permissions.stream()
                .allMatch(this::hasPermission);
    }

    public boolean hasAnyPermission(Set<Permission> permissions) {
        return permissions.stream()
                .anyMatch(this::hasPermission);
    }

    public void assignRole(Role role) {
        roles.add(role);
        updatedAt = Instant.now();
    }

    public void removeRole(Role role) {
        roles.remove(role);
        updatedAt = Instant.now();
    }

    public void changePassword(Password newPassword) {
        this.password = newPassword;
        updatedAt = Instant.now();
    }

    public void activate() {
        this.status = UserStatus.ACTIVE;
        updatedAt = Instant.now();
    }

    public void deactivate() {
        this.status = UserStatus.INACTIVE;
        updatedAt = Instant.now();
    }

    // 值对象
    public static class UserId {
        private final Long value;

        public UserId(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public static class Username {
        private final String value;

        public Username(String value) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException("Username cannot be empty");
            }
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public static class Password {
        private final String hashedValue;

        private Password(String hashedValue) {
            this.hashedValue = hashedValue;
        }

        public static Password fromHashed(String hashedValue) {
            return new Password(hashedValue);
        }

        public static Password fromRaw(String rawPassword, PasswordEncoder encoder) {
            return new Password(encoder.encode(rawPassword));
        }

        private static PasswordEncoder passwordEncoder;

        public static void setPasswordEncoder(PasswordEncoder encoder) {
            passwordEncoder = encoder;
        }

        public boolean matches(String rawPassword) {
            if (passwordEncoder == null) {
                throw new IllegalStateException("PasswordEncoder not set");
            }
            return passwordEncoder.matches(rawPassword, hashedValue);
        }

        public String getHashedValue() {
            return hashedValue;
        }
    }

    public enum UserStatus {
        ACTIVE, INACTIVE
    }

    // Builder模式
    public static class Builder {
        private UserId id;
        private Username username;
        private Password password;
        private String displayName;
        private UserStatus status = UserStatus.ACTIVE;
        private Set<Role> roles = new HashSet<>();
        private Instant createdAt = Instant.now();
        private Instant updatedAt = Instant.now();

        public Builder withId(UserId id) {
            this.id = id;
            return this;
        }

        public Builder withUsername(Username username) {
            this.username = username;
            return this;
        }

        public Builder withPassword(Password password) {
            this.password = password;
            return this;
        }

        public Builder withDisplayName(String displayName) {
            this.displayName = displayName;
            return this;
        }

        public Builder withStatus(UserStatus status) {
            this.status = status;
            return this;
        }

        public Builder withRoles(Set<Role> roles) {
            this.roles = roles;
            return this;
        }

        public Builder withCreatedAt(Instant createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public Builder withUpdatedAt(Instant updatedAt) {
            this.updatedAt = updatedAt;
            return this;
        }

        public User build() {
            return new User(id, username, password, displayName, status, roles, createdAt, updatedAt);
        }
    }

    // Getters
    public UserId getId() {
        return id;
    }

    public Username getUsername() {
        return username;
    }

    public String getDisplayName() {
        return displayName;
    }

    public UserStatus getStatus() {
        return status;
    }

    public Set<Role> getRoles() {
        return new HashSet<>(roles);
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public Password getPassword() {
        return password;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    // 密码编码器接口
    public interface PasswordEncoder {
        String encode(String rawPassword);
        boolean matches(String rawPassword, String encodedPassword);
    }
}

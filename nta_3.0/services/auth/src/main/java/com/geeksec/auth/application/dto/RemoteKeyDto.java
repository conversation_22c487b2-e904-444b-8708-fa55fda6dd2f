package com.geeksec.auth.application.dto;

import java.time.Instant;

/**
 * 远程密钥DTO
 *
 * <AUTHOR>
 */
public record RemoteKeyDto(
    Long id,
    String apiKey,
    Instant createdAt
) {
    /**
     * 创建RemoteKeyDto的Builder
     *
     * @return Builder实例
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * RemoteKeyDto的Builder类
     */
    public static class Builder {
        private Long id;
        private String apiKey;
        private Instant createdAt;

        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        public Builder apiKey(String apiKey) {
            this.apiKey = apiKey;
            return this;
        }

        public Builder createdAt(Instant createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public RemoteKeyDto build() {
            return new RemoteKeyDto(id, apiKey, createdAt);
        }
    }
}

package com.geeksec.auth.interfaces.rest;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.geeksec.auth.application.dto.command.LoginCommand;
import com.geeksec.auth.application.dto.command.RefreshTokenCommand;
import com.geeksec.auth.application.dto.command.RemoteLoginCommand;
import com.geeksec.auth.application.dto.command.ValidateTokenCommand;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.auth.application.dto.response.LoginResponseDto;
import com.geeksec.auth.application.service.AuthApplicationService;
import com.geeksec.auth.domain.service.AuthenticationService.AuthenticationResult;
import com.geeksec.common.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.hateoas.Link;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * 认证控制器
 *
 * 使用 Knife4j 4.4.0 注解和 Sa-Token 1.44.0 权限认证
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {
    private final AuthApplicationService authApplicationService;

    public AuthController(AuthApplicationService authApplicationService) {
        this.authApplicationService = authApplicationService;
    }

    /**
     * 用户登录
     *
     * @param loginCommand 登录命令
     * @return 登录响应
     */
    @PostMapping("/login")
    @SaIgnore
    @Operation(summary = "用户登录", description = "用户登录系统，登录成功后返回 Sa-Token")
    @OperationLog(module = "认证", operation = "登录", description = "用户登录系统")
    public ResponseEntity<ApiResponse<LoginResponseDto>> login(@Valid @RequestBody LoginCommand loginCommand) {
        LoginResponseDto response = authApplicationService.login(loginCommand);
        List<Link> links = List.of(
            linkTo(methodOn(AuthController.class).login(loginCommand)).withSelfRel(),
            linkTo(methodOn(AuthController.class).logout("")).withRel("logout")
        );
        return ResponseEntity.ok()
            .cacheControl(CacheControl.maxAge(30, TimeUnit.MINUTES))
            .body(ApiResponse.success(response, links));
    }

    /**
     * 远程登录
     *
     * @param command 远程登录命令
     * @return 登录结果
     */
    @PostMapping("/remote-login")
    @SaIgnore
    @Operation(summary = "远程登录", description = "通过身份标识远程登录系统，返回 Sa-Token")
    @OperationLog(module = "认证", operation = "远程登录", description = "通过身份标识远程登录系统")
    public ResponseEntity<ApiResponse<String>> remoteLogin(@Valid @RequestBody RemoteLoginCommand command) {
        AuthenticationResult result = authApplicationService.remoteLogin(command);

        if (result.isSuccess()) {
            List<Link> links = List.of(
                linkTo(methodOn(AuthController.class).remoteLogin(command)).withSelfRel(),
                linkTo(methodOn(AuthController.class).logout("")).withRel("logout")
            );
            return ResponseEntity.ok(ApiResponse.<String>success(result.getToken(), links));
        } else {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(400, "远程登录失败，身份验证不通过"));
        }
    }

    /**
     * 用户登出
     *
     * @param token 令牌
     * @return 登出结果
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出系统")
    @OperationLog(module = "认证", operation = "登出", description = "用户登出系统")
    public ResponseEntity<ApiResponse<Void>> logout(@RequestHeader("Authorization") String token) {
        boolean result = authApplicationService.logout(extractToken(token));
        if (result) {
            return ResponseEntity.ok(ApiResponse.success(null));
        } else {
            return ResponseEntity.badRequest()
                .body(ApiResponse.error(400, "登出失败"));
        }
    }

    /**
     * 验证令牌
     *
     * @param command 验证令牌命令
     * @return 验证结果
     */
    @PostMapping("/validate")
    @SaIgnore
    @Operation(summary = "验证令牌", description = "验证 Sa-Token 的有效性")
    @OperationLog(module = "认证", operation = "验证", description = "验证令牌有效性")
    public ResponseEntity<ApiResponse<Boolean>> validateToken(@Valid @RequestBody ValidateTokenCommand command) {
        boolean valid = authApplicationService.validateToken(command.token());

        List<Link> links = List.of(
            linkTo(methodOn(AuthController.class).validateToken(command)).withSelfRel(),
            linkTo(methodOn(AuthController.class).refreshToken(new RefreshTokenCommand(command.token()))).withRel("refresh")
        );

        return ResponseEntity.ok()
            .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
            .body(ApiResponse.success(valid, links));
    }

    /**
     * 刷新令牌
     *
     * @param command 刷新令牌命令
     * @return 新令牌
     */
    @PostMapping("/refresh")
    @SaIgnore
    @Operation(summary = "刷新令牌", description = "刷新 Sa-Token 认证令牌")
    @OperationLog(module = "认证", operation = "刷新", description = "刷新认证令牌")
    public ResponseEntity<ApiResponse<String>> refreshToken(@Valid @RequestBody RefreshTokenCommand command) {
        String newToken = authApplicationService.refreshToken(command.token());

        List<Link> links = List.of(
            linkTo(methodOn(AuthController.class).refreshToken(command)).withSelfRel(),
            linkTo(methodOn(AuthController.class).validateToken(new ValidateTokenCommand(command.token()))).withRel("validate")
        );

        return ResponseEntity.ok(ApiResponse.<String>success(newToken, links));
    }

    /**
     * 从请求头中提取令牌
     *
     * @param authHeader 认证头
     * @return 令牌
     */
    private static final String TOKEN_PREFIX = "Bearer ";
    private static final int TOKEN_PREFIX_LENGTH = TOKEN_PREFIX.length();

    private String extractToken(String authHeader) {
        if (authHeader != null && authHeader.startsWith(TOKEN_PREFIX)) {
            return authHeader.substring(TOKEN_PREFIX_LENGTH);
        }
        return authHeader;
    }
}

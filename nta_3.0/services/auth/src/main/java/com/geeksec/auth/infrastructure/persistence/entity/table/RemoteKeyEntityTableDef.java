package com.geeksec.auth.infrastructure.persistence.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 远程密钥实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class RemoteKeyEntityTableDef extends TableDef {

    /**
     * 远程密钥实体表定义实例
     */
    public static final RemoteKeyEntityTableDef REMOTE_KEY_ENTITY = new RemoteKeyEntityTableDef();

    /**
     * 主键ID - 自增
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * API密钥
     */
    public final QueryColumn API_KEY = new QueryColumn(this, "api_key");

    /**
     * 密钥描述
     */
    public final QueryColumn DESCRIPTION = new QueryColumn(this, "description");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 逻辑删除标记：0-未删除，1-已删除
     */
    public final QueryColumn DELETED = new QueryColumn(this, "deleted");

    /**
     * 构造函数
     */
    public RemoteKeyEntityTableDef() {
        super("", "remote_key");
    }

    /**
     * 带别名的构造函数
     */
    public RemoteKeyEntityTableDef(String alias) {
        super("", "remote_key", alias);
    }
}

package com.geeksec.auth.infrastructure.config;

import com.geeksec.auth.application.service.PermissionCheckServiceImpl;
import com.geeksec.auth.interfaces.rest.interceptor.TokenExtractorInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 拦截器配置类
 *
 * <AUTHOR>
 */
@Configuration
public class InterceptorConfig {

    /**
     * 令牌提取拦截器
     */
    @Bean
    public TokenExtractorInterceptor tokenExtractorInterceptor(PermissionCheckServiceImpl permissionCheckService) {
        return new TokenExtractorInterceptor(permissionCheckService);
    }
}

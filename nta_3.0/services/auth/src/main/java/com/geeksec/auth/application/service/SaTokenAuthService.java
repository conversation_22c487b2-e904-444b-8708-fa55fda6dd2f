package com.geeksec.auth.application.service;

import org.springframework.stereotype.Service;

import com.geeksec.auth.application.dto.command.LoginCommand;
import com.geeksec.auth.application.dto.response.LoginResponseDto;
import com.geeksec.auth.domain.model.User;
import com.geeksec.auth.domain.model.User.PasswordEncoder;
import com.geeksec.auth.domain.repository.UserRepository;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Sa-Token 认证服务
 * 
 * 使用 Sa-Token 1.44.0 进行用户认证和权限管理
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SaTokenAuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    /**
     * 用户登录
     * 
     * @param loginCommand 登录命令
     * @return 登录响应
     */
    public LoginResponseDto login(LoginCommand loginCommand) {
        log.info("用户登录请求: {}", loginCommand.username());
        
        // 查找用户
        User user = userRepository.findByUsername(new User.Username(loginCommand.username()))
                .orElseThrow(() -> new RuntimeException("用户名或密码错误"));

        // 验证密码
        if (!passwordEncoder.matches(loginCommand.password(), user.getPassword().getHashedValue())) {
            log.warn("用户 {} 密码验证失败", loginCommand.username());
            throw new RuntimeException("用户名或密码错误");
        }

        // 检查用户状态
        if (user.getStatus() != User.UserStatus.ACTIVE) {
            log.warn("用户 {} 状态异常: {}", loginCommand.username(), user.getStatus());
            throw new RuntimeException("用户已被禁用");
        }
        
        // Sa-Token 登录
        StpUtil.login(user.getId().getValue());

        // 获取 Token
        String token = StpUtil.getTokenValue();

        log.info("用户 {} 登录成功，Token: {}", loginCommand.username(), token);

        return new LoginResponseDto(
                token,
                user.getUsername().getValue(),
                user.getDisplayName(),
                1  // 默认组ID，可以根据实际需求调整
        );
    }

    /**
     * 用户登出
     * 
     * @param token 令牌
     * @return 是否成功
     */
    public boolean logout(String token) {
        try {
            // 获取当前登录用户ID
            Object loginId = StpUtil.getLoginIdByToken(token);
            log.info("用户 {} 登出", loginId);
            
            // Sa-Token 登出
            StpUtil.logoutByTokenValue(token);
            
            return true;
        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证令牌
     * 
     * @param token 令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            // 验证 Token 有效性
            Object loginId = StpUtil.getLoginIdByToken(token);
            return loginId != null;
        } catch (Exception e) {
            log.debug("Token 验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 刷新令牌
     * 
     * @param token 旧令牌
     * @return 新令牌
     */
    public String refreshToken(String token) {
        try {
            // 获取登录用户ID
            Object loginId = StpUtil.getLoginIdByToken(token);
            
            // 重新登录生成新 Token
            StpUtil.login(loginId);
            String newToken = StpUtil.getTokenValue();
            
            // 注销旧 Token
            StpUtil.logoutByTokenValue(token);
            
            log.info("用户 {} Token 刷新成功", loginId);
            return newToken;
        } catch (Exception e) {
            log.error("Token 刷新失败: {}", e.getMessage());
            throw new RuntimeException("Token 刷新失败");
        }
    }

    /**
     * 获取当前登录用户信息
     * 
     * @return 用户信息
     */
    public User getCurrentUser() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            return userRepository.findById(new User.UserId(userId))
                    .orElseThrow(() -> new RuntimeException("用户不存在"));
        } catch (Exception e) {
            log.error("获取当前用户失败: {}", e.getMessage());
            throw new RuntimeException("获取用户信息失败");
        }
    }

    /**
     * 检查用户是否有指定权限
     * 
     * @param permission 权限标识
     * @return 是否有权限
     */
    public boolean hasPermission(String permission) {
        try {
            StpUtil.checkPermission(permission);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查用户是否有指定角色
     * 
     * @param role 角色标识
     * @return 是否有角色
     */
    public boolean hasRole(String role) {
        try {
            StpUtil.checkRole(role);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取用户权限列表
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    public java.util.List<String> getUserPermissions(Long userId) {
        // 这里应该从数据库查询用户的权限列表
        // 暂时返回空列表，实际项目中需要实现
        return java.util.Collections.emptyList();
    }

    /**
     * 获取用户角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    public java.util.List<String> getUserRoles(Long userId) {
        // 这里应该从数据库查询用户的角色列表
        // 暂时返回空列表，实际项目中需要实现
        return java.util.Collections.emptyList();
    }
}

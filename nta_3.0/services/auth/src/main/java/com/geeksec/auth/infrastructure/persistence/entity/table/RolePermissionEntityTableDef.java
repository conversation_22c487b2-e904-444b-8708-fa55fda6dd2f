package com.geeksec.auth.infrastructure.persistence.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 角色权限关联实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class RolePermissionEntityTableDef extends TableDef {

    /**
     * 角色权限关联实体表定义实例
     */
    public static final RolePermissionEntityTableDef ROLE_PERMISSION_ENTITY = new RolePermissionEntityTableDef();

    /**
     * 主键ID - 自增
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 角色ID
     */
    public final QueryColumn ROLE_ID = new QueryColumn(this, "role_id");

    /**
     * 权限ID
     */
    public final QueryColumn PERMISSION_ID = new QueryColumn(this, "permission_id");

    /**
     * 构造函数
     */
    public RolePermissionEntityTableDef() {
        super("", "role_permission");
    }

    /**
     * 带别名的构造函数
     */
    public RolePermissionEntityTableDef(String alias) {
        super("", "role_permission", alias);
    }
}

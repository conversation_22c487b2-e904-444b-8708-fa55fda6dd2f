package com.geeksec.auth.infrastructure.persistence.mapper;

import com.geeksec.auth.domain.model.Permission;
import com.geeksec.auth.infrastructure.persistence.entity.PermissionEntity;
import org.springframework.stereotype.Component;

/**
 * 权限实体映射器
 *
 * <AUTHOR>
 */
@Component
public class PermissionEntityMapper {

    /**
     * 将领域模型转换为实体
     *
     * @param permission 权限领域模型
     * @return 权限实体
     */
    public PermissionEntity toEntity(Permission permission) {
        PermissionEntity entity = new PermissionEntity();
        entity.setId(permission.getId().getValue());
        entity.setCode(permission.getKey());
        entity.setName(permission.getName());
        // 领域模型中没有description属性，设置为null
        entity.setDescription(null);
        return entity;
    }

    /**
     * 将实体转换为领域模型
     *
     * @param entity 权限实体
     * @return 权限领域模型
     */
    public Permission toDomain(PermissionEntity entity) {
        // 根据权限编码确定权限类型，默认为OPERATION类型
        Permission.PermissionType type = entity.getCode().contains(":") ?
                Permission.PermissionType.OPERATION : Permission.PermissionType.MENU;

        return new Permission(
                new Permission.PermissionId(entity.getId()),
                entity.getCode(),
                entity.getName(),
                type
        );
    }
}

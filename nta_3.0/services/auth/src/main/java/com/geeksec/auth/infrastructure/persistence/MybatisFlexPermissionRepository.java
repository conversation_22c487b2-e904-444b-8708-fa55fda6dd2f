package com.geeksec.auth.infrastructure.persistence;

import com.geeksec.auth.domain.model.Permission;
import com.geeksec.auth.domain.repository.PermissionRepository;
import com.geeksec.auth.infrastructure.persistence.entity.PermissionEntity;
import com.geeksec.auth.infrastructure.persistence.entity.RoleEntity;
import com.geeksec.auth.infrastructure.persistence.mapper.PermissionEntityMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.PermissionMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.RoleMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.RolePermissionMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MyBatis Flex权限仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class MybatisFlexPermissionRepository implements PermissionRepository {
    private final PermissionMapper flexPermissionMapper;
    private final PermissionEntityMapper permissionEntityMapper;
    private final RolePermissionMapper rolePermissionMapper;
    private final RoleMapper roleMapper;

    public MybatisFlexPermissionRepository(PermissionMapper flexPermissionMapper,
                                         PermissionEntityMapper permissionEntityMapper,
                                         RolePermissionMapper rolePermissionMapper,
                                         RoleMapper roleMapper) {
        this.flexPermissionMapper = flexPermissionMapper;
        this.permissionEntityMapper = permissionEntityMapper;
        this.rolePermissionMapper = rolePermissionMapper;
        this.roleMapper = roleMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Permission save(Permission permission) {
        PermissionEntity permissionEntity = permissionEntityMapper.toEntity(permission);

        // 如果ID为空，则插入，否则更新
        if (permissionEntity.getId() == null) {
            flexPermissionMapper.insert(permissionEntity);
        } else {
            flexPermissionMapper.update(permissionEntity);
        }

        return findById(new Permission.PermissionId(permissionEntity.getId()))
                .orElseThrow(() -> new IllegalStateException("Permission not found after save"));
    }

    @Override
    public Optional<Permission> findById(Permission.PermissionId id) {
        PermissionEntity permissionEntity = flexPermissionMapper.selectOneById(id.getValue());
        if (permissionEntity != null) {
            // 查询权限关联的角色
            List<Long> roleIds = rolePermissionMapper.findRoleIdsByPermissionId(permissionEntity.getId());
            if (!roleIds.isEmpty()) {
                Set<RoleEntity> roles = new HashSet<>();
                for (Long roleId : roleIds) {
                    RoleEntity roleEntity = roleMapper.selectOneById(roleId);
                    if (roleEntity != null) {
                        roles.add(roleEntity);
                    }
                }
                permissionEntity.setRoles(roles);
            }
            return Optional.of(permissionEntityMapper.toDomain(permissionEntity));
        }
        return Optional.empty();
    }

    @Override
    public Optional<Permission> findByKey(String key) {
        PermissionEntity permissionEntity = flexPermissionMapper.findByCode(key);
        if (permissionEntity != null) {
            // 查询权限关联的角色
            List<Long> roleIds = rolePermissionMapper.findRoleIdsByPermissionId(permissionEntity.getId());
            if (!roleIds.isEmpty()) {
                Set<RoleEntity> roles = new HashSet<>();
                for (Long roleId : roleIds) {
                    RoleEntity roleEntity = roleMapper.selectOneById(roleId);
                    if (roleEntity != null) {
                        roles.add(roleEntity);
                    }
                }
                permissionEntity.setRoles(roles);
            }
            return Optional.of(permissionEntityMapper.toDomain(permissionEntity));
        }
        return Optional.empty();
    }

    @Override
    public List<Permission> findAll() {
        List<PermissionEntity> permissionEntities = flexPermissionMapper.selectAll();
        for (PermissionEntity permissionEntity : permissionEntities) {
            // 查询权限关联的角色
            List<Long> roleIds = rolePermissionMapper.findRoleIdsByPermissionId(permissionEntity.getId());
            if (!roleIds.isEmpty()) {
                Set<RoleEntity> roles = new HashSet<>();
                for (Long roleId : roleIds) {
                    RoleEntity roleEntity = roleMapper.selectOneById(roleId);
                    if (roleEntity != null) {
                        roles.add(roleEntity);
                    }
                }
                permissionEntity.setRoles(roles);
            }
        }
        return permissionEntities.stream()
                .map(permissionEntityMapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Permission permission) {
        Long permissionId = permission.getId().getValue();
        // 删除权限角色关联
        rolePermissionMapper.deletePermissionAllRoles(permissionId);
        // 删除权限
        flexPermissionMapper.deleteById(permissionId);
    }

    @Override
    public boolean existsByKey(String key) {
        return flexPermissionMapper.existsByCode(key);
    }
}

package com.geeksec.auth.interfaces.rest;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.auth.application.service.PermissionCheckService;
import com.geeksec.common.annotation.OperationLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.hateoas.Link;
import org.springframework.http.CacheControl;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.linkTo;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * 权限检查控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/permission-check")
@Tag(name = "权限检查接口")
public class PermissionCheckController {
    private final PermissionCheckService permissionCheckService;
    private static final String TOKEN_PREFIX = "Bearer ";
    private static final int TOKEN_PREFIX_LENGTH = TOKEN_PREFIX.length();

    public PermissionCheckController(PermissionCheckService permissionCheckService) {
        this.permissionCheckService = permissionCheckService;
    }

    /**
     * 检查是否拥有所有权限
     */
    @PostMapping("/has-all")
    @Operation(summary = "检查是否拥有所有权限")
    @OperationLog(module = "权限", operation = "检查", description = "检查是否拥有所有指定权限")
    public ResponseEntity<ApiResponse<Boolean>> hasAllPermissions(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody List<String> permissions) {
        String token = extractToken(authHeader);
        boolean hasPermissions = permissionCheckService.hasAllPermissions(token, permissions.toArray(new String[0]));

        List<Link> links = List.of(
            linkTo(methodOn(PermissionCheckController.class).hasAllPermissions(authHeader, permissions)).withSelfRel(),
            linkTo(methodOn(PermissionCheckController.class).hasAnyPermission(authHeader, permissions)).withRel("has-any")
        );

        return ResponseEntity.ok()
            .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
            .body(ApiResponse.success(hasPermissions, links));
    }

    /**
     * 检查是否拥有任一权限
     */
    @PostMapping("/has-any")
    @Operation(summary = "检查是否拥有任一权限")
    @OperationLog(module = "权限", operation = "检查", description = "检查是否拥有任一指定权限")
    public ResponseEntity<ApiResponse<Boolean>> hasAnyPermission(
            @RequestHeader("Authorization") String authHeader,
            @RequestBody List<String> permissions) {
        String token = extractToken(authHeader);
        boolean hasPermissions = permissionCheckService.hasAnyPermission(token, permissions.toArray(new String[0]));

        List<Link> links = List.of(
            linkTo(methodOn(PermissionCheckController.class).hasAnyPermission(authHeader, permissions)).withSelfRel(),
            linkTo(methodOn(PermissionCheckController.class).hasAllPermissions(authHeader, permissions)).withRel("has-all")
        );

        return ResponseEntity.ok()
            .cacheControl(CacheControl.maxAge(5, TimeUnit.MINUTES))
            .body(ApiResponse.success(hasPermissions, links));
    }

    /**
     * 从请求头中提取令牌
     *
     * @param authHeader 认证头
     * @return 令牌
     */
    private String extractToken(String authHeader) {
        if (authHeader != null && authHeader.startsWith(TOKEN_PREFIX)) {
            return authHeader.substring(TOKEN_PREFIX_LENGTH);
        }
        return authHeader;
    }
}

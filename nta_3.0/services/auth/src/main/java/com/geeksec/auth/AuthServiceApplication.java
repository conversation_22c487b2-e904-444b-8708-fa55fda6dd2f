package com.geeksec.auth;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
 * 认证服务应用启动类
 *
 * 使用统一技术栈：
 * - MyBatis-Flex 1.10.9 作为 ORM 框架
 * - Knife4j 4.4.0 作为 API 文档框架
 * - Sa-Token 1.44.0 作为权限认证框架
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.geeksec")
@ComponentScan(basePackages = {"com.geeksec.auth", "com.geeksec.common"})
@MapperScan({"com.geeksec.auth.infrastructure.persistence.mapper", "com.geeksec.auth.infrastructure.persistence.mapper.flex"})
public class AuthServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(AuthServiceApplication.class, args);
    }
}

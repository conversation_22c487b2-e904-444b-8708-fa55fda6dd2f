package com.geeksec.auth.infrastructure.persistence;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.geeksec.auth.domain.model.User;
import com.geeksec.auth.domain.repository.UserRepository;
import com.geeksec.auth.infrastructure.persistence.entity.RoleEntity;
import com.geeksec.auth.infrastructure.persistence.entity.UserEntity;
import com.geeksec.auth.infrastructure.persistence.mapper.UserEntityMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.RoleMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.UserMapper;
import com.geeksec.auth.infrastructure.persistence.mapper.flex.UserRoleMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;

/**
 * MyBatis Flex用户仓储实现
 *
 * <AUTHOR>
 */
@Repository
public class MybatisFlexUserRepository implements UserRepository {
    private final UserMapper userMapper;
    private final UserEntityMapper userEntityMapper;
    private final UserRoleMapper userRoleMapper;
    private final RoleMapper roleMapper;

    public MybatisFlexUserRepository(UserMapper userMapper, UserEntityMapper userEntityMapper,
                                    UserRoleMapper userRoleMapper, RoleMapper roleMapper) {
        this.userMapper = userMapper;
        this.userEntityMapper = userEntityMapper;
        this.userRoleMapper = userRoleMapper;
        this.roleMapper = roleMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User save(User user) {
        UserEntity userEntity = userEntityMapper.toEntity(user);

        // 如果ID为空，则插入，否则更新
        if (userEntity.getUserId() == null) {
            userMapper.insert(userEntity);
        } else {
            userMapper.update(userEntity);
            // 删除用户所有角色关联
            userRoleMapper.deleteUserAllRoles(userEntity.getUserId());
        }

        // 添加用户角色关联
        if (userEntity.getRoles() != null && !userEntity.getRoles().isEmpty()) {
            Set<Long> roleIds = userEntity.getRoles().stream()
                    .map(RoleEntity::getId)
                    .collect(Collectors.toSet());
            userRoleMapper.batchAddUserRole(userEntity.getUserId(), roleIds);
        }

        return findById(new User.UserId(userEntity.getUserId()))
                .orElseThrow(() -> new IllegalStateException("User not found after save"));
    }

    @Override
    public Optional<User> findById(User.UserId id) {
        UserEntity userEntity = userMapper.selectOneById(id.getValue());
        if (userEntity != null) {
            // 查询用户角色
            List<Long> roleIds = userRoleMapper.findRoleIdsByUserId(userEntity.getUserId());
            if (!roleIds.isEmpty()) {
                Set<RoleEntity> roles = new HashSet<>();
                for (Long roleId : roleIds) {
                    RoleEntity roleEntity = roleMapper.selectOneById(roleId);
                    if (roleEntity != null) {
                        roles.add(roleEntity);
                    }
                }
                userEntity.setRoles(roles);
            }
            return Optional.of(userEntityMapper.toDomain(userEntity));
        }
        return Optional.empty();
    }

    @Override
    public Optional<User> findByUsername(User.Username username) {
        UserEntity userEntity = userMapper.findByUsername(username.getValue());
        if (userEntity != null) {
            // 查询用户角色
            List<Long> roleIds = userRoleMapper.findRoleIdsByUserId(userEntity.getUserId());
            if (!roleIds.isEmpty()) {
                Set<RoleEntity> roles = new HashSet<>();
                for (Long roleId : roleIds) {
                    RoleEntity roleEntity = roleMapper.selectOneById(roleId);
                    if (roleEntity != null) {
                        roles.add(roleEntity);
                    }
                }
                userEntity.setRoles(roles);
            }
            return Optional.of(userEntityMapper.toDomain(userEntity));
        }
        return Optional.empty();
    }

    @Override
    public List<User> findAll() {
        List<UserEntity> userEntities = userMapper.selectAll();
        for (UserEntity userEntity : userEntities) {
            // 查询用户角色
            List<Long> roleIds = userRoleMapper.findRoleIdsByUserId(userEntity.getUserId());
            if (!roleIds.isEmpty()) {
                Set<RoleEntity> roles = new HashSet<>();
                for (Long roleId : roleIds) {
                    RoleEntity roleEntity = roleMapper.selectOneById(roleId);
                    if (roleEntity != null) {
                        roles.add(roleEntity);
                    }
                }
                userEntity.setRoles(roles);
            }
        }
        return userEntities.stream()
                .map(userEntityMapper::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(User user) {
        Long userId = user.getId().getValue();
        // 删除用户角色关联
        userRoleMapper.deleteUserAllRoles(userId);
        // 删除用户
        userMapper.deleteById(userId);
    }

    @Override
    public boolean existsByUsername(User.Username username) {
        return userMapper.existsByUsername(username.getValue());
    }

    @Override
    public org.springframework.data.domain.Page<User> findAll(Pageable pageable) {
        // 使用MyBatis Flex的分页查询
        Page<UserEntity> page = userMapper.paginate(
                pageable.getPageNumber() + 1, // MyBatis Flex的页码从1开始
                pageable.getPageSize(),
                new QueryWrapper()
        );

        // 查询每个用户的角色
        for (UserEntity userEntity : page.getRecords()) {
            List<Long> roleIds = userRoleMapper.findRoleIdsByUserId(userEntity.getUserId());
            if (!roleIds.isEmpty()) {
                Set<RoleEntity> roles = new HashSet<>();
                for (Long roleId : roleIds) {
                    RoleEntity roleEntity = roleMapper.selectOneById(roleId);
                    if (roleEntity != null) {
                        roles.add(roleEntity);
                    }
                }
                userEntity.setRoles(roles);
            }
        }

        // 转换为Spring Data的Page
        List<User> users = page.getRecords().stream()
                .map(userEntityMapper::toDomain)
                .collect(Collectors.toList());

        return new org.springframework.data.domain.PageImpl<>(
                users,
                pageable,
                page.getTotalRow()
        );
    }
}

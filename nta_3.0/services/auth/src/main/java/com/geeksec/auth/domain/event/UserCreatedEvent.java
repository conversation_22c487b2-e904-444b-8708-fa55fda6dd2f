package com.geeksec.auth.domain.event;

import com.geeksec.auth.domain.model.User;
import com.geeksec.common.event.DomainEvent;
import java.time.Instant;

/**
 * 用户创建事件
 *
 * <AUTHOR>
 */
public class UserCreatedEvent implements DomainEvent {
    private final User.UserId userId;
    private final User.Username username;
    private final Instant occurredOn;

    public UserCreatedEvent(User.UserId userId, User.Username username) {
        this.userId = userId;
        this.username = username;
        this.occurredOn = Instant.now();
    }

    @Override
    public Instant occurredOn() {
        return occurredOn;
    }

    public User.UserId getUserId() {
        return userId;
    }

    public User.Username getUsername() {
        return username;
    }
}

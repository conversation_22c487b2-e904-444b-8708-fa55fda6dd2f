package com.geeksec.auth.infrastructure.persistence.entity;

import com.geeksec.common.entity.BaseAuditableEntity;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 远程密钥实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table(value = "user_remote_key")
public class RemoteKeyEntity extends BaseAuditableEntity {

    @Id(keyType = KeyType.Auto)
    private Long id;

    private String apiKey;
}

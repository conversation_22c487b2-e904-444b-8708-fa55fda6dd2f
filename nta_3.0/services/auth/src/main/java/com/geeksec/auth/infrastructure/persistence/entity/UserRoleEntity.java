package com.geeksec.auth.infrastructure.persistence.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

/**
 * 用户角色关联实体
 *
 * 使用 MyBatis-Flex 1.10.9 注解进行映射
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Table("user_role")
public class UserRoleEntity {

    /**
     * 主键ID - 自增
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 用户ID
     */
    @Column("user_id")
    private Long userId;

    /**
     * 角色ID
     */
    @Column("role_id")
    private Long roleId;
}

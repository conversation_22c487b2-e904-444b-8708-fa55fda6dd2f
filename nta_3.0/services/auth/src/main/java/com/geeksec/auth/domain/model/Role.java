package com.geeksec.auth.domain.model;

import java.util.HashSet;
import java.util.Set;

/**
 * 角色领域模型
 */
public class Role {
    private RoleId id;
    private String name;
    private String description;
    private Set<Permission> permissions;

    private Role(RoleId id, String name, String description, Set<Permission> permissions) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.permissions = permissions;
    }

    // 领域行为
    public boolean hasPermission(Permission permission) {
        return permissions.contains(permission);
    }

    public void grantPermission(Permission permission) {
        permissions.add(permission);
    }

    public void revokePermission(Permission permission) {
        permissions.remove(permission);
    }

    // 值对象
    public static class RoleId {
        private final Long value;

        public RoleId(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    // Builder模式
    public static class Builder {
        private RoleId id;
        private String name;
        private String description;
        private Set<Permission> permissions = new HashSet<>();

        public Builder withId(RoleId id) {
            this.id = id;
            return this;
        }

        public Builder withName(String name) {
            this.name = name;
            return this;
        }

        public Builder withDescription(String description) {
            this.description = description;
            return this;
        }

        public Builder withPermissions(Set<Permission> permissions) {
            this.permissions = permissions;
            return this;
        }

        public Role build() {
            return new Role(id, name, description, permissions);
        }
    }

    // Getters
    public RoleId getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Set<Permission> getPermissions() {
        return new HashSet<>(permissions);
    }
}

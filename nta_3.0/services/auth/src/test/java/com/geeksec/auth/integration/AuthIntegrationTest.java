package com.geeksec.auth.integration;

import com.geeksec.auth.TestBase;
import com.geeksec.auth.application.dto.LoginRequestDto;
import com.geeksec.auth.application.dto.RefreshTokenRequestDto;
import com.geeksec.auth.application.dto.ValidateTokenRequestDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class AuthIntegrationTest extends TestBase {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void loginAndValidateAndRefresh_ShouldWorkCorrectly() throws Exception {
        // 1. 登录
        LoginRequestDto loginRequest = new LoginRequestDto("admin", "admin123");
        MvcResult loginResult = mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value("200"))
            .andExpect(jsonPath("$.data.token").exists())
            .andReturn();
        
        String token = objectMapper.readTree(loginResult.getResponse().getContentAsString())
            .path("data").path("token").asText();
        
        // 2. 验证令牌
        ValidateTokenRequestDto validateRequest = new ValidateTokenRequestDto(token);
        mockMvc.perform(post("/api/v1/auth/validate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validateRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value("200"))
            .andExpect(jsonPath("$.data").value(true));
        
        // 3. 刷新令牌
        RefreshTokenRequestDto refreshRequest = new RefreshTokenRequestDto(token);
        mockMvc.perform(post("/api/v1/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(refreshRequest)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value("200"))
            .andExpect(jsonPath("$.data").exists());
    }
    
    @Test
    void login_ShouldFail_WhenCredentialsAreInvalid() throws Exception {
        LoginRequestDto request = new LoginRequestDto("invalid", "invalid");
        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isUnauthorized())
            .andExpect(jsonPath("$.code").value("401"));
    }
} 
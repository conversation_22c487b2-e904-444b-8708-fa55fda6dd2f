package com.geeksec.auth.interfaces.api;

import com.geeksec.auth.TestBase;
import com.geeksec.auth.application.dto.LoginRequestDto;
import com.geeksec.auth.application.dto.RefreshTokenRequestDto;
import com.geeksec.auth.application.dto.ValidateTokenRequestDto;
import com.geeksec.auth.domain.service.AuthenticationService;
import com.geeksec.auth.domain.service.TokenService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class AuthControllerTest extends TestBase {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AuthenticationService authenticationService;

    @MockBean
    private TokenService tokenService;

    @Test
    void login_ShouldReturnToken_WhenCredentialsAreValid() throws Exception {
        // Arrange
        LoginRequestDto request = new LoginRequestDto("test", "password");
        when(authenticationService.authenticate(any(), any()))
            .thenReturn(AuthenticationService.AuthenticationResult.success("valid-token", null));

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value("200"))
            .andExpect(jsonPath("$.data.token").exists());
    }

    @Test
    void validateToken_ShouldReturnTrue_WhenTokenIsValid() throws Exception {
        // Arrange
        ValidateTokenRequestDto request = new ValidateTokenRequestDto("valid-token");
        when(tokenService.validateToken(any()))
            .thenReturn(AuthenticationService.TokenValidationResult.valid(null));

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/validate")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value("200"))
            .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    void refreshToken_ShouldReturnNewToken_WhenTokenIsValid() throws Exception {
        // Arrange
        RefreshTokenRequestDto request = new RefreshTokenRequestDto("valid-token");
        when(tokenService.refreshToken(any()))
            .thenReturn(AuthenticationService.TokenRefreshResult.success("new-token"));

        // Act & Assert
        mockMvc.perform(post("/api/v1/auth/refresh")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value("200"))
            .andExpect(jsonPath("$.data").value("new-token"));
    }
}
# Model Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8085}
  servlet:
    context-path: /model

# 模型管理服务特定配置
model:
  # Kafka主题配置
  kafka:
    topics:
      model-state-changed: model_state_changed_dev
      model-config: model_config_dev

  # 开发环境模型文件存储配置
  storage:
    base-path: ${MODEL_STORAGE_PATH:./dev-data/models}
    max-file-size: ${MODEL_MAX_FILE_SIZE:50MB}  # 开发环境较小文件限制
    allowed-extensions: .model,.pkl,.pt,.onnx,.pb,.h5,.joblib
    backup-enabled: false  # 开发环境不启用备份

  # 开发环境模型验证配置
  validation:
    enable-config-validation: true
    enable-file-validation: true
    max-config-size: 10KB
    enable-signature-check: false  # 开发环境不检查签名

  # 开发环境模型执行配置
  execution:
    timeout: 30000  # 30秒超时
    max-memory: 1GB
    enable-gpu: false  # 开发环境不启用GPU
    thread-pool-size: 2

  # 开发环境模型缓存配置
  cache:
    enable: true
    max-size: 10
    ttl: 300  # 5分钟缓存

  # 开发环境默认模型配置
  defaults:
    - name: test-model
      type: classification
      version: 1.0.0
      enabled: true

# 日志配置
logging:
  level:
    com.geeksec.model: DEBUG
    org.springframework.kafka: DEBUG
    com.mybatisflex: DEBUG

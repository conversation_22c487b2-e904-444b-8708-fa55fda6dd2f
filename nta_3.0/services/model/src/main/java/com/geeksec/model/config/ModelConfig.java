package com.geeksec.model.config;

import com.mybatisflex.core.audit.AuditManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.KafkaListenerErrorHandler;
import org.springframework.kafka.listener.ListenerExecutionFailedException;
import org.springframework.messaging.Message;

/**
 * 模型管理服务配置类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Configuration
public class ModelConfig {

    /**
     * 配置MyBatis-Flex审计功能
     */
    @Bean
    public AuditManager auditManager() {
        AuditManager auditManager = new AuditManager();
        
        // 配置审计日志
        auditManager.setAuditEnable(true);
        auditManager.setMessageFactory((auditMessage) -> {
            log.debug("SQL审计: {}", auditMessage);
            return auditMessage.getFullMessage();
        });
        
        return auditManager;
    }

    /**
     * Kafka错误处理器
     */
    @Bean
    public KafkaListenerErrorHandler kafkaListenerErrorHandler() {
        return new KafkaListenerErrorHandler() {
            @Override
            public Object handleError(Message<?> message, ListenerExecutionFailedException exception) {
                log.error("Kafka消息处理失败: {}", message.getPayload(), exception);
                return null;
            }
        };
    }
}

package com.geeksec.model.service;

import com.geeksec.model.dto.*;
import com.geeksec.model.entity.ModelInfo;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

/**
 * 模型管理服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface ModelService {

    /**
     * 分页查询模型列表
     * 
     * @param condition 查询条件
     * @return 分页结果
     */
    Page<ModelResponse> getModelPage(ModelQueryCondition condition);

    /**
     * 根据条件查询模型列表
     * 
     * @param condition 查询条件
     * @return 模型列表
     */
    List<ModelResponse> getModelList(ModelQueryCondition condition);

    /**
     * 根据模型ID查询模型详情
     * 
     * @param modelId 模型ID
     * @return 模型详情
     */
    ModelResponse getModelById(Integer modelId);

    /**
     * 根据模型名称查询模型
     * 
     * @param modelName 模型名称
     * @return 模型信息
     */
    ModelResponse getModelByName(String modelName);

    /**
     * 创建模型
     * 
     * @param request 创建请求
     * @param operator 操作者
     * @return 模型ID
     */
    Integer createModel(ModelCreateRequest request, String operator);

    /**
     * 更新模型信息
     * 
     * @param request 更新请求
     * @param operator 操作者
     * @return 是否成功
     */
    Boolean updateModel(ModelUpdateRequest request, String operator);

    /**
     * 更新模型状态
     * 
     * @param request 状态更新请求
     * @param operator 操作者
     * @return 是否成功
     */
    Boolean updateModelState(ModelStateUpdateRequest request, String operator);

    /**
     * 删除模型（软删除）
     * 
     * @param modelId 模型ID
     * @param operator 操作者
     * @return 是否成功
     */
    Boolean deleteModel(Integer modelId, String operator);

    /**
     * 批量更新模型状态
     * 
     * @param modelIds 模型ID列表
     * @param state 目标状态
     * @param operator 操作者
     * @return 是否成功
     */
    Boolean batchUpdateModelState(List<Integer> modelIds, Integer state, String operator);

    /**
     * 根据状态查询模型列表
     * 
     * @param state 模型状态
     * @return 模型列表
     */
    List<ModelResponse> getModelsByState(Integer state);

    /**
     * 根据算法类型查询模型列表
     * 
     * @param algorithm 算法类型
     * @return 模型列表
     */
    List<ModelResponse> getModelsByAlgorithm(String algorithm);

    /**
     * 根据模型类型查询模型列表
     * 
     * @param modelType 模型类型
     * @return 模型列表
     */
    List<ModelResponse> getModelsByType(String modelType);

    /**
     * 根据标签查询模型列表
     * 
     * @param tag 标签
     * @return 模型列表
     */
    List<ModelResponse> getModelsByTag(String tag);

    /**
     * 获取模型统计信息
     * 
     * @return 统计信息
     */
    ModelStatistics getModelStatistics();

    /**
     * 检查模型名称是否存在
     * 
     * @param modelName 模型名称
     * @param excludeId 排除的模型ID（用于更新时检查）
     * @return 是否存在
     */
    Boolean isModelNameExists(String modelName, Integer excludeId);

    /**
     * 验证模型配置
     * 
     * @param modelConfig 模型配置JSON
     * @return 是否有效
     */
    Boolean validateModelConfig(String modelConfig);
}

package com.geeksec.model;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 模型管理服务启动类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@SpringBootApplication(scanBasePackages = {"com.geeksec.model", "com.geeksec.common"})
@MapperScan("com.geeksec.model.repository")
@EnableFeignClients
@EnableKafka
@EnableTransactionManagement
public class ModelApplication {

    public static void main(String[] args) {
        SpringApplication.run(ModelApplication.class, args);
    }
}

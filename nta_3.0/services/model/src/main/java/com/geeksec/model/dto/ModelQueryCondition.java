package com.geeksec.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型查询条件
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "模型查询条件")
public class ModelQueryCondition {

    /**
     * 当前页
     */
    @JsonProperty("current_page")
    @Schema(description = "当前页", example = "1")
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    @JsonProperty("page_size")
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    /**
     * 模型名称（模糊查询）
     */
    @JsonProperty("model_name")
    @Schema(description = "模型名称", example = "SSL指纹检测")
    private String modelName;

    /**
     * 模型算法类型
     */
    @JsonProperty("model_algorithm")
    @Schema(description = "模型算法类型", example = "特征识别")
    private String modelAlgorithm;

    /**
     * 模型类型
     */
    @JsonProperty("model_type")
    @Schema(description = "模型类型", example = "指纹检测")
    private String modelType;

    /**
     * 模型状态：0-失效，1-生效
     */
    @JsonProperty("state")
    @Schema(description = "模型状态", example = "1")
    private Integer state;

    /**
     * 模型标签
     */
    @JsonProperty("tags")
    @Schema(description = "模型标签", example = "SSL,指纹")
    private String tags;

    /**
     * 排序字段
     */
    @JsonProperty("order_field")
    @Schema(description = "排序字段", example = "updated_time")
    private String orderField = "updated_time";

    /**
     * 排序方式：asc-升序，desc-降序
     */
    @JsonProperty("sort_order")
    @Schema(description = "排序方式", example = "desc")
    private String sortOrder = "desc";

    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    private String startTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    private String endTime;
}

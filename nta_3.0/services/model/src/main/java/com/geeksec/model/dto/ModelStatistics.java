package com.geeksec.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模型统计信息
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "模型统计信息")
public class ModelStatistics {

    /**
     * 模型总数
     */
    @JsonProperty("total_count")
    @Schema(description = "模型总数", example = "100")
    private Long totalCount;

    /**
     * 生效模型数量
     */
    @JsonProperty("enabled_count")
    @Schema(description = "生效模型数量", example = "80")
    private Long enabledCount;

    /**
     * 失效模型数量
     */
    @JsonProperty("disabled_count")
    @Schema(description = "失效模型数量", example = "20")
    private Long disabledCount;

    /**
     * 生效率
     */
    @JsonProperty("enabled_rate")
    @Schema(description = "生效率", example = "0.8")
    private Double enabledRate;
}

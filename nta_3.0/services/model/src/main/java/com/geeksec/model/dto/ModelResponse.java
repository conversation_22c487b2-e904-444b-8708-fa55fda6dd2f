package com.geeksec.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 模型响应数据
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "模型响应数据")
public class ModelResponse {

    /**
     * 模型ID
     */
    @JsonProperty("model_id")
    @Schema(description = "模型ID", example = "99001")
    private Integer modelId;

    /**
     * 模型名称
     */
    @JsonProperty("model_name")
    @Schema(description = "模型名称", example = "SSL指纹检测模型")
    private String modelName;

    /**
     * 模型算法类型
     */
    @JsonProperty("model_algorithm")
    @Schema(description = "模型算法类型", example = "特征识别")
    private String modelAlgorithm;

    /**
     * 模型类型
     */
    @JsonProperty("model_type")
    @Schema(description = "模型类型", example = "指纹检测")
    private String modelType;

    /**
     * 模型版本
     */
    @JsonProperty("model_version")
    @Schema(description = "模型版本", example = "1.0.0")
    private String modelVersion;

    /**
     * 模型描述
     */
    @JsonProperty("remark")
    @Schema(description = "模型描述", example = "基于SSL指纹特征的检测模型")
    private String remark;

    /**
     * 模型状态：0-失效，1-生效
     */
    @JsonProperty("state")
    @Schema(description = "模型状态", example = "1")
    private Integer state;

    /**
     * 模型状态描述
     */
    @JsonProperty("state_desc")
    @Schema(description = "模型状态描述", example = "生效")
    private String stateDesc;

    /**
     * 模型配置参数（JSON格式）
     */
    @JsonProperty("model_config")
    @Schema(description = "模型配置参数", example = "{\"threshold\": 0.8, \"timeout\": 30}")
    private String modelConfig;

    /**
     * 模型文件路径
     */
    @JsonProperty("model_path")
    @Schema(description = "模型文件路径", example = "/models/ssl_fingerprint_v1.0.0.model")
    private String modelPath;

    /**
     * 模型文件哈希值
     */
    @JsonProperty("model_hash")
    @Schema(description = "模型文件哈希值", example = "sha256:abc123...")
    private String modelHash;

    /**
     * 模型优先级
     */
    @JsonProperty("priority")
    @Schema(description = "模型优先级", example = "100")
    private Integer priority;

    /**
     * 模型标签（多个标签用逗号分隔）
     */
    @JsonProperty("tags")
    @Schema(description = "模型标签", example = "SSL,指纹,检测")
    private String tags;

    /**
     * 创建时间
     */
    @JsonProperty("created_time")
    @Schema(description = "创建时间", example = "2024-01-01T10:00:00")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @JsonProperty("updated_time")
    @Schema(description = "更新时间", example = "2024-01-01T10:00:00")
    private LocalDateTime updatedTime;

    /**
     * 创建者
     */
    @JsonProperty("created_by")
    @Schema(description = "创建者", example = "admin")
    private String createdBy;

    /**
     * 更新者
     */
    @JsonProperty("updated_by")
    @Schema(description = "更新者", example = "admin")
    private String updatedBy;
}

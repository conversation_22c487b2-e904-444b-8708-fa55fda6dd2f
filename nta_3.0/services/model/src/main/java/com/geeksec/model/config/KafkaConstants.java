package com.geeksec.model.config;

/**
 * Kafka 相关常量定义
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public final class KafkaConstants {

    /**
     * 私有构造函数，防止实例化
     */
    private KafkaConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Kafka 主题名称
     */
    public static final class Topics {
        /**
         * 模型状态变更主题
         */
        public static final String MODEL_STATE_CHANGED = "model_state_changed";

        /**
         * 模型配置变更主题
         */
        public static final String MODEL_CONFIG_CHANGED = "model_config_changed";
    }

    /**
     * Kafka 消息 Key
     */
    public static final class MessageKeys {
        /**
         * 模型状态变更消息 Key
         */
        public static final String MODEL_STATE_CHANGE = "model_state_change";

        /**
         * 模型配置变更消息 Key
         */
        public static final String MODEL_CONFIG_CHANGE = "model_config_change";
    }
}

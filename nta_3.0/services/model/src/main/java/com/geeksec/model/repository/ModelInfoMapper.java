package com.geeksec.model.repository;

import com.geeksec.model.dto.ModelQueryCondition;
import com.geeksec.model.entity.ModelInfo;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.paginate.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模型信息数据访问接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Mapper
public interface ModelInfoMapper extends BaseMapper<ModelInfo> {

    /**
     * 分页查询模型列表
     * 
     * @param page 分页参数
     * @param condition 查询条件
     * @return 模型列表
     */
    Page<ModelInfo> selectModelPage(@Param("page") Page<ModelInfo> page, 
                                   @Param("condition") ModelQueryCondition condition);

    /**
     * 根据条件查询模型列表
     * 
     * @param condition 查询条件
     * @return 模型列表
     */
    List<ModelInfo> selectModelList(@Param("condition") ModelQueryCondition condition);

    /**
     * 根据模型名称查询模型
     * 
     * @param modelName 模型名称
     * @return 模型信息
     */
    ModelInfo selectByModelName(@Param("modelName") String modelName);

    /**
     * 根据模型ID更新模型状态
     * 
     * @param modelId 模型ID
     * @param state 模型状态
     * @param updatedBy 更新者
     * @return 更新行数
     */
    int updateModelState(@Param("modelId") Integer modelId, 
                        @Param("state") Integer state,
                        @Param("updatedBy") String updatedBy);

    /**
     * 根据状态查询模型列表
     * 
     * @param state 模型状态
     * @return 模型列表
     */
    List<ModelInfo> selectByState(@Param("state") Integer state);

    /**
     * 根据算法类型查询模型列表
     * 
     * @param algorithm 算法类型
     * @return 模型列表
     */
    List<ModelInfo> selectByAlgorithm(@Param("algorithm") String algorithm);

    /**
     * 根据模型类型查询模型列表
     * 
     * @param modelType 模型类型
     * @return 模型列表
     */
    List<ModelInfo> selectByModelType(@Param("modelType") String modelType);

    /**
     * 根据标签查询模型列表
     * 
     * @param tag 标签
     * @return 模型列表
     */
    List<ModelInfo> selectByTag(@Param("tag") String tag);

    /**
     * 统计模型总数
     * 
     * @param condition 查询条件
     * @return 模型总数
     */
    long countModels(@Param("condition") ModelQueryCondition condition);

    /**
     * 根据状态统计模型数量
     * 
     * @param state 模型状态
     * @return 模型数量
     */
    long countByState(@Param("state") Integer state);

    /**
     * 软删除模型
     * 
     * @param modelId 模型ID
     * @param updatedBy 更新者
     * @return 更新行数
     */
    int softDeleteModel(@Param("modelId") Integer modelId, 
                       @Param("updatedBy") String updatedBy);

    /**
     * 批量更新模型状态
     * 
     * @param modelIds 模型ID列表
     * @param state 模型状态
     * @param updatedBy 更新者
     * @return 更新行数
     */
    int batchUpdateModelState(@Param("modelIds") List<Integer> modelIds, 
                             @Param("state") Integer state,
                             @Param("updatedBy") String updatedBy);
}

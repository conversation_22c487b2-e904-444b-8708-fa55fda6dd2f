-- =============================================
-- NTA 3.0 模型管理服务数据库初始化脚本
-- =============================================

-- 创建模型信息表
DROP TABLE IF EXISTS tb_model_info;
CREATE TABLE tb_model_info (
    model_id SERIAL PRIMARY KEY,
    model_name VARCHAR(255) NOT NULL COMMENT '模型名称',
    model_algorithm VARCHAR(100) NOT NULL COMMENT '模型算法类型',
    model_type VARCHAR(100) COMMENT '模型类型',
    model_version VARCHAR(50) COMMENT '模型版本',
    remark TEXT COMMENT '模型描述',
    state INTEGER NOT NULL DEFAULT 1 COMMENT '模型状态：0-失效，1-生效',
    model_config TEXT COMMENT '模型配置参数（JSON格式）',
    model_path VARCHAR(500) COMMENT '模型文件路径',
    model_hash VARCHAR(128) COMMENT '模型文件哈希值',
    priority INTEGER DEFAULT 100 COMMENT '模型优先级',
    tags VARCHAR(500) COMMENT '模型标签（多个标签用逗号分隔）',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(100) COMMENT '创建者',
    updated_by VARCHAR(100) COMMENT '更新者',
    is_deleted INTEGER DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除'
);

-- 创建索引
CREATE INDEX idx_model_name ON tb_model_info(model_name);
CREATE INDEX idx_model_algorithm ON tb_model_info(model_algorithm);
CREATE INDEX idx_model_type ON tb_model_info(model_type);
CREATE INDEX idx_model_state ON tb_model_info(state);
CREATE INDEX idx_model_priority ON tb_model_info(priority);
CREATE INDEX idx_model_created_time ON tb_model_info(created_time);
CREATE INDEX idx_model_updated_time ON tb_model_info(updated_time);
CREATE INDEX idx_model_is_deleted ON tb_model_info(is_deleted);

-- 创建唯一索引（模型名称在未删除状态下唯一）
CREATE UNIQUE INDEX uk_model_name_not_deleted ON tb_model_info(model_name) WHERE is_deleted = 0;

-- 添加表注释
COMMENT ON TABLE tb_model_info IS '模型信息表';
COMMENT ON COLUMN tb_model_info.model_id IS '模型ID';
COMMENT ON COLUMN tb_model_info.model_name IS '模型名称';
COMMENT ON COLUMN tb_model_info.model_algorithm IS '模型算法类型';
COMMENT ON COLUMN tb_model_info.model_type IS '模型类型';
COMMENT ON COLUMN tb_model_info.model_version IS '模型版本';
COMMENT ON COLUMN tb_model_info.remark IS '模型描述';
COMMENT ON COLUMN tb_model_info.state IS '模型状态：0-失效，1-生效';
COMMENT ON COLUMN tb_model_info.model_config IS '模型配置参数（JSON格式）';
COMMENT ON COLUMN tb_model_info.model_path IS '模型文件路径';
COMMENT ON COLUMN tb_model_info.model_hash IS '模型文件哈希值';
COMMENT ON COLUMN tb_model_info.priority IS '模型优先级';
COMMENT ON COLUMN tb_model_info.tags IS '模型标签（多个标签用逗号分隔）';
COMMENT ON COLUMN tb_model_info.created_time IS '创建时间';
COMMENT ON COLUMN tb_model_info.updated_time IS '更新时间';
COMMENT ON COLUMN tb_model_info.created_by IS '创建者';
COMMENT ON COLUMN tb_model_info.updated_by IS '更新者';
COMMENT ON COLUMN tb_model_info.is_deleted IS '是否删除：0-未删除，1-已删除';

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tb_model_info_updated_time 
    BEFORE UPDATE ON tb_model_info 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_time_column();

-- 插入初始化数据（基于NTA 2.0的模型数据）
INSERT INTO tb_model_info (
    model_id, model_name, model_algorithm, model_type, model_version, remark, state, 
    priority, tags, created_by, updated_by
) VALUES 
-- 内网检测模型
(99001, '智能内网网段检测', '协议识别', '内网检测', '1.0.0', 
 '基于网络流量中的IP地址与MAC地址的对应关系构建IP、MAC的关联图谱，实现相关内网网段的检测', 
 1, 100, '内网,网段,检测', 'system', 'system'),

-- 证书检测模型
(99002, '检测异常证书', '特征识别', '证书检测', '1.0.0', 
 '基于网络流量中出现的海量证书，提取证书中的基础及扩展字段，基于专家知识对其中的异常字段进行挖掘，检测出异常证书', 
 1, 100, '证书,异常,检测', 'system', 'system'),

-- APT检测模型
(99003, '检测APT29组织发起的攻击', '行为识别', 'APT检测', '1.0.0', 
 '基于对已有APT29组织的攻击行为的分析，提取网络节点中相关的IP，指纹，证书等信息构建知识图谱，发掘流量中潜在的APT29组织的攻击', 
 1, 100, 'APT,APT29,攻击检测', 'system', 'system'),

(99004, '检测APT28组织发起的攻击', '行为识别', 'APT检测', '1.0.0', 
 '基于对已有APT28组织的攻击行为的分析，提取网络节点中相关的IP，指纹，证书等信息构建知识图谱，发掘流量中潜在的APT28组织的攻击', 
 1, 100, 'APT,APT28,攻击检测', 'system', 'system'),

-- 挖矿检测模型
(99005, '检测挖矿行为', '特征识别', '挖矿检测', '1.0.0', 
 '基于挖矿软件的网络通信特征，检测网络中的挖矿行为', 
 1, 100, '挖矿,检测', 'system', 'system'),

-- RAT检测模型
(99006, '检测远程访问木马', '行为识别', 'RAT检测', '1.0.0', 
 '基于远程访问木马的通信模式和行为特征，检测网络中的RAT活动', 
 1, 100, 'RAT,木马,检测', 'system', 'system'),

-- Tor检测模型
(99007, '检测Tor匿名网络', '特征识别', 'Tor检测', '1.0.0', 
 '基于Tor网络的通信特征，检测网络中的Tor流量', 
 1, 100, 'Tor,匿名,检测', 'system', 'system'),

-- 指纹检测模型
(99008, 'SSL指纹检测', '特征识别', '指纹检测', '1.0.0', 
 '基于SSL/TLS握手过程中的指纹特征，检测异常的SSL通信', 
 1, 100, 'SSL,指纹,检测', 'system', 'system'),

-- DNS检测模型
(99009, 'DNS隧道检测', '行为识别', 'DNS检测', '1.0.0', 
 '基于DNS查询的异常模式，检测DNS隧道通信', 
 1, 100, 'DNS,隧道,检测', 'system', 'system'),

-- 暴力破解检测模型
(99010, 'Web登录暴力破解检测', '行为识别', '暴力破解检测', '1.0.0', 
 '基于Web登录请求的频率和模式，检测暴力破解攻击', 
 1, 100, 'Web,暴力破解,检测', 'system', 'system');

-- 重置序列
SELECT setval('tb_model_info_model_id_seq', (SELECT MAX(model_id) FROM tb_model_info));

-- 创建视图：活跃模型视图
CREATE OR REPLACE VIEW v_active_models AS
SELECT 
    model_id,
    model_name,
    model_algorithm,
    model_type,
    model_version,
    remark,
    state,
    priority,
    tags,
    created_time,
    updated_time,
    created_by,
    updated_by
FROM tb_model_info 
WHERE is_deleted = 0 AND state = 1
ORDER BY priority DESC, updated_time DESC;

-- 创建视图：模型统计视图
CREATE OR REPLACE VIEW v_model_statistics AS
SELECT 
    COUNT(*) as total_count,
    COUNT(CASE WHEN state = 1 THEN 1 END) as enabled_count,
    COUNT(CASE WHEN state = 0 THEN 1 END) as disabled_count,
    ROUND(
        COUNT(CASE WHEN state = 1 THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(*), 0), 4
    ) as enabled_rate
FROM tb_model_info 
WHERE is_deleted = 0;

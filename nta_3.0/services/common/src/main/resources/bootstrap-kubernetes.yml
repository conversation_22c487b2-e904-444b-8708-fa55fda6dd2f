# Kubernetes集成通用bootstrap配置模板
# 各个微服务可以继承此配置

spring:
  profiles:
    # 激活Kubernetes配置
    include: kubernetes
  cloud:
    # 启用Kubernetes服务发现
    kubernetes:
      discovery:
        enabled: true
        # 命名空间
        namespace: ${KUBERNETES_NAMESPACE:nta}
        # 等待缓存就绪
        wait-cache-ready: true
        # 缓存加载超时时间
        cache-loading-timeout-seconds: 60
        # 服务标签过滤器
        service-labels:
          app.kubernetes.io/part-of: nta
        # 主要端口名称
        primary-port-name: http
      config:
        enabled: true
        # 从ConfigMap加载配置
        sources:
          # 服务特定配置
          - name: ${spring.application.name}-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
          # 通用配置
          - name: common-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
        # 配置重新加载
        reload:
          enabled: true
          mode: event
          strategy: refresh
    # 配置导入
    config:
      import:
        - "kubernetes:"
        - "classpath:application-kubernetes.yml"

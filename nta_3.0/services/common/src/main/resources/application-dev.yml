# NTA 3.0 Services 开发环境配置
# 继承通用配置，并针对开发环境进行优化

spring:
  profiles:
    active: dev
  
  # 开发环境数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_dev}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_dev}
    password: ${DB_PASSWORD:nta_dev123}
    
    # 开发环境 Druid 连接池配置（较小的连接池）
    druid:
      initial-size: 2
      min-idle: 2
      max-active: 10
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=2000
      
      # 开发环境启用监控
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: true
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}

  # 开发环境 Redis 配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # 开发环境 Kafka 配置
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    producer:
      retries: 1
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: 1
    consumer:
      bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
      group-id: ${spring.application.name}-dev
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true
      auto-commit-interval: 1000

# MyBatis-Flex 开发环境配置
mybatis-flex:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false
  global-config:
    logic-delete-column: deleted
    version-column: version
    key-generator-type: snowflake
    # 开发环境打印 SQL
    print-sql: true

# Knife4j 开发环境配置
knife4j:
  enable: true
  openapi:
    title: ${spring.application.name} API 文档 (开发环境)
    description: NTA 3.0 网络流量分析平台 - ${spring.application.name} 服务接口文档 (开发环境)
    version: 3.0.0-DEV
    concat: NTA 开发团队
    email: <EMAIL>
    url: https://dev.geeksec.com
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-version: true

# Sa-Token 开发环境配置
sa-token:
  token-name: Authorization
  # 开发环境较短的token有效期，便于测试
  timeout: 3600
  active-timeout: -1
  is-concurrent: true
  is-share: true
  token-style: uuid
  # 开发环境启用日志
  is-log: true

# 服务器开发环境配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# 开发环境日志配置
logging:
  level:
    com.geeksec: DEBUG
    com.mybatisflex: DEBUG
    cn.dev33.satoken: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.apache.kafka: INFO
    # 开发环境显示SQL日志
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:./logs}/${spring.application.name}-dev.log
    max-size: 100MB
    max-history: 30

# 开发环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 开发环境特定配置
dev:
  # 开发环境数据路径
  data:
    base-path: ${DEV_DATA_PATH:./dev-data}
    temp-path: ${DEV_TEMP_PATH:./dev-temp}
  # 开发环境外部服务地址
  external:
    services:
      import-service: ${IMPORT_SERVICE_URL:http://localhost:5000}
      analysis-service: ${ANALYSIS_SERVICE_URL:http://localhost:6000}
  # 开发环境调试选项
  debug:
    enable-sql-log: true
    enable-request-log: true
    enable-performance-monitor: true

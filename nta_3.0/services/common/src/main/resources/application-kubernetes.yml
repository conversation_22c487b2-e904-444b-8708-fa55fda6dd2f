# Spring Cloud Kubernetes 3.0.x 简化配置
# 该配置文件用于所有微服务的Kubernetes集成

spring:
  # 配置导入 - Spring Cloud Kubernetes 3.0.x 推荐方式
  config:
    import: "kubernetes:"

  cloud:
    # Kubernetes 服务发现配置
    kubernetes:
      discovery:
        # 启用服务发现
        enabled: true
        # 仅发现当前命名空间的服务
        all-namespaces: false
        # 是否包含外部名称服务
        include-external-name-services: false

# 日志配置
logging:
  level:
    "[org.springframework.cloud.kubernetes]": INFO
    "[org.springframework.cloud.loadbalancer]": INFO
    root: INFO

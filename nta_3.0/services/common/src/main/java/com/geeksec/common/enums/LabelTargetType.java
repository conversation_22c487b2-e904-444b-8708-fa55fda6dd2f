package com.geeksec.common.enums;

import lombok.Getter;

/**
 * 标签目标类型枚举
 * 统一定义标签可以应用的目标对象类型
 * 
 * 兼容原TagTargetEnum的功能，提供更完整的标签目标类型定义
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum LabelTargetType {
    
    /**
     * IP地址标签
     */
    IP(0, "IP", "IP地址", "IP地址相关标签"),
    
    /**
     * 端口标签
     */
    PORT(1, "端口", "端口", "端口相关标签"),
    
    /**
     * 应用标签
     */
    APPLICATION(2, "应用服务", "应用", "应用程序相关标签"),
    
    /**
     * 域名标签
     */
    DOMAIN(3, "域名", "域名", "域名相关标签"),
    
    /**
     * 证书标签
     */
    CERTIFICATE(4, "证书", "证书", "证书相关标签"),
    
    /**
     * MAC地址标签
     */
    MAC(5, "MAC", "MAC地址", "MAC地址相关标签"),
    
    /**
     * 会话标签
     */
    SESSION(6, "会话", "会话", "会话相关标签"),
    
    /**
     * 指纹标签
     */
    FINGERPRINT(7, "指纹", "指纹", "指纹相关标签"),
    
    /**
     * 全部类型（特殊值）
     */
    ALL(9999, "全部", "全部", "所有类型的标签");

    /**
     * 类型编号（兼容原TagTargetEnum）
     */
    private final Integer code;
    
    /**
     * 类型名称（兼容原TagTargetEnum）
     */
    private final String name;
    
    /**
     * 中文显示名称
     */
    private final String displayName;
    
    /**
     * 类型描述
     */
    private final String description;

    LabelTargetType(Integer code, String name, String displayName, String description) {
        this.code = code;
        this.name = name;
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 根据类型编号获取标签目标类型
     * 
     * @param code 类型编号
     * @return 标签目标类型，如果未找到返回null
     */
    public static LabelTargetType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (LabelTargetType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据类型名称获取标签目标类型
     * 
     * @param name 类型名称
     * @return 标签目标类型，如果未找到返回null
     */
    public static LabelTargetType getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        
        for (LabelTargetType type : values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据类型编号获取类型名称（兼容原TagTargetEnum方法）
     * 
     * @param code 类型编号
     * @return 类型名称，如果未找到返回null
     */
    public static String getNameByCode(Integer code) {
        LabelTargetType type = getByCode(code);
        return type != null ? type.getName() : null;
    }

    /**
     * 根据类型名称获取类型编号（兼容原TagTargetEnum方法）
     * 
     * @param name 类型名称
     * @return 类型编号，如果未找到返回null
     */
    public static Integer getCodeByName(String name) {
        LabelTargetType type = getByName(name);
        return type != null ? type.getCode() : null;
    }

    /**
     * 检查是否为有效的标签目标类型编号
     * 
     * @param code 类型编号
     * @return 是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 检查是否为有效的标签目标类型名称
     * 
     * @param name 类型名称
     * @return 是否有效
     */
    public static boolean isValidName(String name) {
        return getByName(name) != null;
    }

    /**
     * 获取所有非"全部"类型的标签目标类型
     * 
     * @return 标签目标类型数组
     */
    public static LabelTargetType[] getSpecificTypes() {
        return new LabelTargetType[]{
            IP, PORT, APPLICATION, DOMAIN, CERTIFICATE, MAC, SESSION, FINGERPRINT
        };
    }

    /**
     * 检查是否为特定类型（非"全部"类型）
     * 
     * @return 是否为特定类型
     */
    public boolean isSpecificType() {
        return this != ALL;
    }

    /**
     * 获取枚举值（兼容metadata模块的LabelTargetType）
     * 
     * @return 枚举值字符串
     */
    public String getValue() {
        return this.name().toUpperCase();
    }
}

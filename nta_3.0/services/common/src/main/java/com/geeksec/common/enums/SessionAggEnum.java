package com.geeksec.common.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum SessionAggEnum {

    //客户端=s、源   服务端=d、目标
    SIp("sIp","sIp"),
    DIp("dIp","dIp"),
    DPort("dPort","dPort"),
    AppName("appName","AppName"),
    SMac("sMac","sMac"),
    DMac("dMac","dMac"),
    SessionCount("sessionCount","sessionCount"),
    SpayloadByteSum("sPayloadByteSum","spayloadByteSum"),
    DpayloadByteSum("dPayloadByteSum","dpayloadByteSum"),
    SIpCountry("sIpCountry","sIpCountry"),
    DIpCountry("dIpCountry","dIpCountry");

    private final String field;
    //方便es查询字段
    private final String esUse;

    SessionAggEnum(String field,String esUse){
        this.field = field;
        this.esUse = esUse;
    }



    public static Boolean checkField(String field){
        for(SessionAggEnum aggEnum : SessionAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return true;
            }
        }
        return false;
    }

    public static String getEsField(String field){
        for(SessionAggEnum aggEnum : SessionAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return aggEnum.getEsUse();
            }
        }
        return null;
    }

    /**
     * 获取前端对应字段集合
     * @return
     */
    public static List<String> getAllField(){
        List<String> list = new ArrayList<>();
        for(SessionAggEnum aggEnum : SessionAggEnum.values()){
            String field = aggEnum.getField();
            if (field.equals("sessionCount") ||field.equals("sPayloadByteSum") || field.equals("dPayloadByteSum")){
                continue;
            }
            list.add(field);
        }
        return list;
    }

}

package com.geeksec.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态枚举
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    /**
     * 待处理
     */
    PENDING(1, "待处理"),

    /**
     * 处理中
     */
    PROCESSING(2, "处理中"),

    /**
     * 已完成
     */
    COMPLETED(3, "已完成"),

    /**
     * 失败
     */
    FAILED(-1, "失败"),

    /**
     * 已取消
     */
    CANCELLED(0, "已取消");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;



    /**
     * 根据状态码获取枚举
     * 
     * @param code 状态码
     * @return 任务状态枚举
     */
    public static TaskStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        
        for (TaskStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为完成状态
     * 
     * @param code 状态码
     * @return 是否完成
     */
    public static boolean isCompleted(Integer code) {
        return COMPLETED.getCode().equals(code);
    }

    /**
     * 判断是否为失败状态
     * 
     * @param code 状态码
     * @return 是否失败
     */
    public static boolean isFailed(Integer code) {
        return FAILED.getCode().equals(code);
    }

    /**
     * 判断是否为处理中状态
     * 
     * @param code 状态码
     * @return 是否处理中
     */
    public static boolean isProcessing(Integer code) {
        return PROCESSING.getCode().equals(code);
    }
}

package com.geeksec.common.enums;

import lombok.Getter;

/**
 * 威胁类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum ThreatType {
    
    /**
     * 恶意软件
     */
    MALWARE("恶意软件", "包括病毒、蠕虫、木马等恶意代码"),
    
    /**
     * APT攻击
     */
    APT("APT攻击", "高级持续性威胁攻击"),
    
    /**
     * 僵尸网络
     */
    BOTNET("僵尸网络", "被恶意控制的计算机网络"),
    
    /**
     * 钓鱼攻击
     */
    PHISHING("钓鱼攻击", "通过伪造网站或邮件获取敏感信息"),
    
    /**
     * C2通信
     */
    C2("C2通信", "命令与控制服务器通信"),
    
    /**
     * 挖矿攻击
     */
    MINING("挖矿攻击", "非法使用计算资源进行加密货币挖矿"),
    
    /**
     * 勒索软件
     */
    RANSOMWARE("勒索软件", "加密文件并要求赎金的恶意软件"),
    
    /**
     * 木马程序
     */
    TROJAN("木马程序", "伪装成正常软件的恶意程序"),
    
    /**
     * 后门程序
     */
    BACKDOOR("后门程序", "绕过安全控制的隐蔽访问方式"),
    
    /**
     * 漏洞利用
     */
    EXPLOIT("漏洞利用", "利用系统或应用程序漏洞的攻击"),
    
    /**
     * 其他威胁
     */
    OTHER("其他威胁", "未分类的其他安全威胁");

    private final String displayName;
    private final String description;

    ThreatType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }



    /**
     * 根据显示名称获取威胁类型
     */
    public static ThreatType fromDisplayName(String displayName) {
        for (ThreatType type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        return OTHER;
    }
}

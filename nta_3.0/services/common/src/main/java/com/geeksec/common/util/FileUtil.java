package com.geeksec.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.stream.Stream;

/**
 * 通用文件处理工具类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
@Slf4j
public class FileUtil {
    
    /**
     * 在目录中递归查找指定扩展名的文件
     * 
     * @param inputDirs 输入目录列表
     * @param extensions 文件扩展名列表（如 .pcap, .cap, .pcapng）
     * @return 匹配的文件路径列表
     */
    public List<String> findFilesByExtensions(List<String> inputDirs, List<String> extensions) {
        List<String> matchedFiles = new ArrayList<>();
        
        for (String inputPath : inputDirs) {
            Path path = Paths.get(inputPath);
            
            if (Files.isRegularFile(path)) {
                // 如果是文件，检查是否匹配扩展名
                if (hasMatchingExtension(path, extensions)) {
                    matchedFiles.add(inputPath);
                }
            } else if (Files.isDirectory(path)) {
                // 如果是目录，递归查找文件
                matchedFiles.addAll(findFilesInDirectory(path, extensions));
            } else {
                log.warn("路径不存在或不是文件/目录: {}", inputPath);
            }
        }
        
        log.info("找到匹配文件数量: {}", matchedFiles.size());
        return matchedFiles;
    }
    
    /**
     * 在目录中递归查找指定扩展名的文件
     * 
     * @param directory 目录路径
     * @param extensions 文件扩展名列表
     * @return 匹配的文件路径列表
     */
    private List<String> findFilesInDirectory(Path directory, List<String> extensions) {
        List<String> matchedFiles = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(directory)) {
            paths.filter(Files::isRegularFile)
                 .filter(path -> hasMatchingExtension(path, extensions))
                 .forEach(path -> matchedFiles.add(path.toString()));
        } catch (IOException e) {
            log.error("遍历目录失败: {}", directory, e);
        }
        
        return matchedFiles;
    }
    
    /**
     * 检查文件是否匹配指定的扩展名
     * 
     * @param file 文件路径
     * @param extensions 扩展名列表
     * @return 是否匹配
     */
    private boolean hasMatchingExtension(Path file, List<String> extensions) {
        String fileName = file.getFileName().toString().toLowerCase();
        return extensions.stream().anyMatch(fileName::endsWith);
    }
    
    /**
     * 写入文本文件
     * 
     * @param filePath 文件路径
     * @param content 文件内容
     */
    public void writeFile(Path filePath, String content) {
        try {
            Files.createDirectories(filePath.getParent());
            Files.writeString(filePath, content);
            log.debug("写入文件: {}", filePath);
        } catch (IOException e) {
            log.error("写入文件失败: filePath={}", filePath, e);
            throw new RuntimeException("写入文件失败", e);
        }
    }
    
    /**
     * 写入Base64编码的文件
     * 
     * @param filePath 文件路径
     * @param base64Content Base64编码的内容
     */
    public void writeBase64File(Path filePath, String base64Content) {
        try {
            Files.createDirectories(filePath.getParent());
            byte[] decodedBytes = Base64.getDecoder().decode(base64Content);
            Files.write(filePath, decodedBytes);
            log.debug("写入Base64文件: {}", filePath);
        } catch (Exception e) {
            log.error("写入Base64文件失败: filePath={}", filePath, e);
            throw new RuntimeException("写入Base64文件失败", e);
        }
    }
    
    /**
     * 读取文件内容
     * 
     * @param filePath 文件路径
     * @return 文件内容
     */
    public String readFile(Path filePath) {
        try {
            return Files.readString(filePath);
        } catch (IOException e) {
            log.error("读取文件失败: filePath={}", filePath, e);
            throw new RuntimeException("读取文件失败", e);
        }
    }
    
    /**
     * 复制文件
     * 
     * @param source 源文件路径
     * @param target 目标文件路径
     */
    public void copyFile(Path source, Path target) {
        try {
            Files.createDirectories(target.getParent());
            Files.copy(source, target);
            log.debug("复制文件: {} -> {}", source, target);
        } catch (IOException e) {
            log.error("复制文件失败: source={}, target={}", source, target, e);
            throw new RuntimeException("复制文件失败", e);
        }
    }
    
    /**
     * 创建目录
     * 
     * @param directory 目录路径
     */
    public void createDirectories(Path directory) {
        try {
            Files.createDirectories(directory);
            log.debug("创建目录: {}", directory);
        } catch (IOException e) {
            log.error("创建目录失败: directory={}", directory, e);
            throw new RuntimeException("创建目录失败", e);
        }
    }
    
    /**
     * 检查文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public boolean exists(Path filePath) {
        return Files.exists(filePath);
    }
    
    /**
     * 删除文件
     * 
     * @param filePath 文件路径
     */
    public void deleteFile(Path filePath) {
        try {
            Files.deleteIfExists(filePath);
            log.debug("删除文件: {}", filePath);
        } catch (IOException e) {
            log.error("删除文件失败: filePath={}", filePath, e);
            throw new RuntimeException("删除文件失败", e);
        }
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public long getFileSize(Path filePath) {
        try {
            return Files.size(filePath);
        } catch (IOException e) {
            log.error("获取文件大小失败: filePath={}", filePath, e);
            return 0;
        }
    }
    
    /**
     * 递归复制目录
     * 
     * @param sourceDir 源目录
     * @param targetDir 目标目录
     */
    public void copyDirectory(Path sourceDir, Path targetDir) {
        try {
            if (!Files.exists(sourceDir)) {
                log.warn("源目录不存在: {}", sourceDir);
                return;
            }
            
            Files.walk(sourceDir)
                .forEach(source -> {
                    try {
                        Path target = targetDir.resolve(sourceDir.relativize(source));
                        if (Files.isDirectory(source)) {
                            Files.createDirectories(target);
                        } else {
                            Files.copy(source, target);
                        }
                    } catch (IOException e) {
                        log.error("复制文件失败: source={}, target={}", source, targetDir, e);
                    }
                });
        } catch (IOException e) {
            log.error("复制目录失败: sourceDir={}, targetDir={}", sourceDir, targetDir, e);
            throw new RuntimeException("复制目录失败", e);
        }
    }
    
    /**
     * 检查路径是否为目录
     * 
     * @param path 路径
     * @return 是否为目录
     */
    public boolean isDirectory(Path path) {
        return Files.isDirectory(path);
    }
    
    /**
     * 检查路径是否为文件
     * 
     * @param path 路径
     * @return 是否为文件
     */
    public boolean isRegularFile(Path path) {
        return Files.isRegularFile(path);
    }
}

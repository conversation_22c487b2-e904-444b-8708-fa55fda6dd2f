package com.geeksec.common.interceptor;

import com.geeksec.common.annotation.RequiresPermission;
import com.geeksec.common.service.PermissionCheckService;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Arrays;

/**
 * 权限拦截器
 * 用于拦截带有 {@link RequiresPermission} 注解的方法，检查用户是否具有所需权限
 *
 * <AUTHOR>
 */
public class PermissionInterceptor implements HandlerInterceptor {

    private final PermissionCheckService permissionCheckService;

    public PermissionInterceptor(PermissionCheckService permissionCheckService) {
        this.permissionCheckService = permissionCheckService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        RequiresPermission requiresPermission = handlerMethod.getMethodAnnotation(RequiresPermission.class);

        if (requiresPermission == null) {
            requiresPermission = handlerMethod.getBeanType().getAnnotation(RequiresPermission.class);
        }

        if (requiresPermission != null) {
            String[] permissions = requiresPermission.value();
            RequiresPermission.Logical logical = requiresPermission.logical();

            if (logical == RequiresPermission.Logical.AND) {
                return Arrays.stream(permissions)
                    .allMatch(permission -> permissionCheckService.hasPermission(permission));
            } else {
                return Arrays.stream(permissions)
                    .anyMatch(permission -> permissionCheckService.hasPermission(permission));
            }
        }

        return true;
    }
}

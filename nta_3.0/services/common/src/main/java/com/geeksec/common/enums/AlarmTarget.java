package com.geeksec.common.enums;

import lombok.Getter;

/**
 * 告警目标枚举
 * 定义告警针对的目标对象类型
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum AlarmTarget {
    
    /**
     * IP告警
     */
    IP(0, "IP"),
    
    /**
     * 端口告警
     */
    PORT(1, "端口"),
    
    /**
     * 应用告警
     */
    APPLICATION(2, "应用"),
    
    /**
     * 域名告警
     */
    DOMAIN(3, "域名"),
    
    /**
     * 证书告警
     */
    CERTIFICATE(4, "证书"),
    
    /**
     * MAC告警
     */
    MAC(5, "MAC"),
    
    /**
     * 连接告警
     */
    CONNECTION(6, "连接");
    
    private final int code;
    private final String description;
    
    AlarmTarget(int code, String description) {
        this.code = code;
        this.description = description;
    }
    


    /**
     * 根据代码获取告警目标
     *
     * @param code 目标代码
     * @return 告警目标
     */
    public static AlarmTarget fromCode(int code) {
        for (AlarmTarget target : values()) {
            if (target.code == code) {
                return target;
            }
        }
        throw new IllegalArgumentException("未知的告警目标代码: " + code);
    }

    /**
     * 根据描述获取告警目标代码
     *
     * @param description 目标描述
     * @return 目标代码，如果未找到返回null
     */
    public static Integer getCodeByDescription(String description) {
        for (AlarmTarget target : values()) {
            if (target.description.equals(description)) {
                return target.code;
            }
        }
        return null;
    }
}

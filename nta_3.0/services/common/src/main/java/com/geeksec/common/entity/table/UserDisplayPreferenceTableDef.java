package com.geeksec.common.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 用户显示偏好配置表定义
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public class UserDisplayPreferenceTableDef extends TableDef {

    /**
     * 用户显示偏好配置表
     */
    public static final UserDisplayPreferenceTableDef USER_DISPLAY_PREFERENCE = new UserDisplayPreferenceTableDef();

    /**
     * 主键ID
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 用户ID
     */
    public final QueryColumn USER_ID = new QueryColumn(this, "user_id");

    /**
     * 模块名称
     */
    public final QueryColumn MODULE = new QueryColumn(this, "module");

    /**
     * 配置类型
     */
    public final QueryColumn CONFIG_TYPE = new QueryColumn(this, "config_type");

    /**
     * 配置内容JSON
     */
    public final QueryColumn CONFIG_JSON = new QueryColumn(this, "config_json");

    /**
     * 创建时间
     */
    public final QueryColumn CREATED_AT = new QueryColumn(this, "created_at");

    /**
     * 更新时间
     */
    public final QueryColumn UPDATED_AT = new QueryColumn(this, "updated_at");

    /**
     * 创建者用户ID
     */
    public final QueryColumn CREATED_BY = new QueryColumn(this, "created_by");

    /**
     * 更新者用户ID
     */
    public final QueryColumn UPDATED_BY = new QueryColumn(this, "updated_by");

    /**
     * 构造函数
     */
    public UserDisplayPreferenceTableDef() {
        super("", "user_display_preferences");
    }

    /**
     * 构造函数
     */
    public UserDisplayPreferenceTableDef(String schema) {
        super(schema, "user_display_preferences");
    }

    /**
     * 构造函数
     */
    public UserDisplayPreferenceTableDef(String schema, String tableName) {
        super(schema, tableName);
    }

    /**
     * 构造函数
     */
    public UserDisplayPreferenceTableDef(String schema, String tableName, String alias) {
        super(schema, tableName, alias);
    }
}

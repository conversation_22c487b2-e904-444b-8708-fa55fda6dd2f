package com.geeksec.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限要求注解
 * 用于标记需要特定权限才能访问的方法或类
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequiresPermission {
    /**
     * 所需权限列表
     */
    String[] value() default {};
    
    /**
     * 权限逻辑关系
     */
    Logical logical() default Logical.AND;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 需要满足所有权限
         */
        AND, 
        
        /**
         * 满足任一权限即可
         */
        OR
    }
}

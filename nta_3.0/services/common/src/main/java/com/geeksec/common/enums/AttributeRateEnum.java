package com.geeksec.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Description：
 */
@Getter
public enum AttributeRateEnum {

    THREAT(1, "威胁"),
    FUNCTION(2, "功能描述"),
    APT(3, "APT"),
    REMOTE_CONTROL(4, "远程控制"),
    PROXY(6, "代理"),
    FINGERPRINT(7, "指纹描述"),
    LEGALITY(8, "合法性"),
    BASIC(9, "基础属性"),
    ENCRYPT_MONITOR(10, "加密流量监测");

    private Integer attributeId;

    private String attributeName;

    // 判断attributeId,如果相同返回其对应的attributeName
    public static String getAttributeNameByAttributeId(Integer attributeId){
        for(AttributeRateEnum attributeRateEnum : AttributeRateEnum.values()){
            if(attributeRateEnum.getAttributeId().equals(attributeId)){
                return attributeRateEnum.getAttributeName();
            }
        }
        return null;
    }

    AttributeRateEnum(Integer attributeId, String attributeName) {
        this.attributeId = attributeId;
        this.attributeName = attributeName;
    }

    public void setAttributeId(Integer attributeId) {
        this.attributeId = attributeId;
    }

    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }
}

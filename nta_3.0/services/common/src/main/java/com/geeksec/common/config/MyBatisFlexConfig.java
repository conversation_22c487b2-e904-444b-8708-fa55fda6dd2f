package com.geeksec.common.config;

import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Flex 配置类
 *
 * 统一配置 MyBatis-Flex ORM 框架的相关设置：
 * - 全局配置
 * - 逻辑删除
 * - 乐观锁
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Configuration
public class MyBatisFlexConfig implements MyBatisFlexCustomizer {

    /**
     * 自定义 MyBatis-Flex 全局配置
     */
    @Override
    public void customize(FlexGlobalConfig globalConfig) {
        // 设置逻辑删除字段
        globalConfig.setLogicDeleteColumn("deleted");

        // 设置乐观锁版本字段
        globalConfig.setVersionColumn("version");

        // 开发环境下打印 SQL（通过配置文件控制更合适）
        // globalConfig.setSqlPrintEnable(true);
    }
}

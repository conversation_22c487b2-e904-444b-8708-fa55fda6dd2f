package com.geeksec.common.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum DNSAggEnum {
    //支持的聚合字段：客户端IP、服务端IP、服务端端口、域名、答复地址
    //客户端=s、源   服务端=d、目标
    SIP("sIp","sIp"),
    DIP("dIp","dIp"),
    DPort("dPort","dPort"),
    Domain("Domain","Domain.keyword"),
    DomainIp("DomainIp","DomainIp"),
    AppName("appName","AppName");


    private final String field;
    //方便es查询字段
    private final String esUse;

    DNSAggEnum(String field, String esUse){
        this.field = field;
        this.esUse = esUse;
    }



    public static Boolean checkField(String field){
        for(DNSAggEnum aggEnum : DNSAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return true;
            }
        }
        return false;
    }

    public static String getEsField(String field){
        for(DNSAggEnum aggEnum : DNSAggEnum.values()){
            if(aggEnum.getField().equals(field)){
                return aggEnum.getEsUse();
            }
        }
        return null;
    }

    /**
     * 获取前端对应字段集合
     * @return
     */
    public static List<String> getAllField(){
        List<String> list = new ArrayList<>();
        for(DNSAggEnum aggEnum : DNSAggEnum.values()){
            list.add(aggEnum.getField());
        }
        return list;
    }
}

package com.geeksec.common.metadata.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 国家信息数据传输对象
 * 跨模块共享的基础元数据
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CountryInfo {
    
    /**
     * 国家代码（ISO 3166-1 alpha-2）
     */
    private String code;
    
    /**
     * 国家名称
     */
    private String name;
    
    /**
     * 英文名称
     */
    private String englishName;
    
    /**
     * 创建国家信息
     * 
     * @param code 国家代码
     * @param name 国家名称
     * @return 国家信息
     */
    public static CountryInfo of(String code, String name) {
        return new CountryInfo(code, name, null);
    }
    
    /**
     * 创建完整的国家信息
     * 
     * @param code 国家代码
     * @param name 国家名称
     * @param englishName 英文名称
     * @return 国家信息
     */
    public static CountryInfo of(String code, String name, String englishName) {
        return new CountryInfo(code, name, englishName);
    }
}

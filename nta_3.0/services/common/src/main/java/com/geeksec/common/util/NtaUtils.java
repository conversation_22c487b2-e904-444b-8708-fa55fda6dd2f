package com.geeksec.common.util;

import java.util.List;
import java.util.regex.Pattern;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;

/**
 * NTA 3.0 Services 业务工具类
 *
 * 提供 Services 模块专用的业务工具方法：
 * - JSON 响应处理
 * - HTTP 请求参数转换
 * - 分页结果封装
 * - 业务参数验证
 *
 * 注意：通用工具方法已移动到系统级 common 模块中的对应工具类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class NtaUtils {

    private NtaUtils() {
        // 工具类，禁止实例化
    }



    // ==================== JSON 工具方法 ====================

    /**
     * 返回成功的JSON响应（无数据）
     */
    public static JSONObject successJson() {
        return successJson(new JSONObject());
    }

    /**
     * 返回成功的JSON响应（带数据）
     */
    public static JSONObject successJson(Object data) {
        JSONObject resultJson = new JSONObject();
        resultJson.put("err", 0);
        resultJson.put("msg", "成功");
        resultJson.put("data", data);
        return resultJson;
    }

    /**
     * 返回错误的JSON响应
     */
    public static JSONObject errorJson(Integer err, String msg) {
        JSONObject resultJson = new JSONObject();
        resultJson.put("err", err);
        resultJson.put("msg", msg);
        resultJson.put("data", new JSONObject());
        return resultJson;
    }

    /**
     * 返回错误的JSON响应（默认错误码）
     */
    public static JSONObject errorJson(String msg) {
        return errorJson(40000, msg);
    }

    /**
     * 安全的JSON解析
     */
    public static JSONObject parseJson(String jsonStr) {
        try {
            return JSON.parseObject(jsonStr);
        } catch (Exception e) {
            log.warn("JSON解析失败: {}", jsonStr, e);
            return new JSONObject();
        }
    }

    // ==================== 分页工具方法 ====================

    /**
     * 查询分页结果后的封装工具方法
     *
     * @param list        查询分页对象list
     * @param totalCount  查询出记录的总条数
     * @param pageRow     每页行数
     */
    public static JSONObject successPage(List<?> list, int totalCount, int pageRow) {
        int totalPage = getPageCounts(pageRow, totalCount);
        JSONObject result = successJson();
        JSONObject info = new JSONObject();
        info.put("list", list);
        info.put("totalCount", totalCount);
        info.put("totalPage", totalPage);
        result.put("info", info);
        return result;
    }

    /**
     * 查询分页结果后的封装工具方法（无分页信息）
     *
     * @param list 查询分页对象list
     */
    public static JSONObject successPage(List<?> list) {
        JSONObject result = successJson();
        JSONObject info = new JSONObject();
        info.put("list", list);
        result.put("info", info);
        return result;
    }

    /**
     * 获取总页数
     *
     * @param pageRow   每页行数
     * @param itemCount 结果的总条数
     */
    private static int getPageCounts(int pageRow, int itemCount) {
        if (itemCount == 0) {
            return 1;
        }
        return itemCount % pageRow > 0 ?
                itemCount / pageRow + 1 :
                itemCount / pageRow;
    }

    // ==================== 数据类型转换工具 ====================
   

    /**
     * String转int（安全转换）
     */
    public static int convertStringToInt(String value, int defaultValue) {
        try {
            return org.apache.commons.lang3.StringUtils.isNotEmpty(value) ? Integer.parseInt(value) : defaultValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }









    // ==================== 验证工具方法 ====================

    /**
     * 邮箱格式验证
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    public static boolean isValidEmail(String email) {
        return org.apache.commons.lang3.StringUtils.isNotEmpty(email) && EMAIL_PATTERN.matcher(email).matches();
    }





    // ==================== HTTP 请求工具方法 ====================

    /**
     * 将request参数值转为json
     */
    public static JSONObject request2Json(jakarta.servlet.http.HttpServletRequest request) {
        JSONObject requestJson = new JSONObject();
        java.util.Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] pv = request.getParameterValues(paramName);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < pv.length; i++) {
                if (pv[i].length() > 0) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    sb.append(pv[i]);
                }
            }
            requestJson.put(paramName, sb.toString());
        }
        return requestJson;
    }

    /**
     * 将request转JSON并且验证非空字段
     */
    public static JSONObject convert2JsonAndCheckRequiredColumns(
            jakarta.servlet.http.HttpServletRequest request, String requiredColumns) {
        JSONObject jsonObject = request2Json(request);
        hasAllRequired(jsonObject, requiredColumns);
        return jsonObject;
    }

    /**
     * 验证是否含有全部必填字段
     *
     * @param jsonObject      JSON对象
     * @param requiredColumns 必填的参数字段名称 逗号隔开 比如"userId,name,telephone"
     */
    public static void hasAllRequired(final JSONObject jsonObject, String requiredColumns) {
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(requiredColumns)) {
            //验证字段非空
            String[] columns = requiredColumns.split(",");
            StringBuilder missCol = new StringBuilder();
            for (String column : columns) {
                Object val = jsonObject.get(column.trim());
                if (val == null || org.apache.commons.lang3.StringUtils.isEmpty(val.toString())) {
                    missCol.append(column).append("  ");
                }
            }
            if (missCol.length() > 0) {
                throw new RuntimeException("缺少必填参数: " + missCol.toString());
            }
        }
    }

    // ==================== 分页参数处理 ====================

    /**
     * 在分页查询之前,为查询条件里加上分页参数
     *
     * @param paramObject    查询条件json
     * @param defaultPageRow 默认的每页条数,即前端不传pageRow参数时的每页条数
     */
    public static void fillPageParam(final JSONObject paramObject, int defaultPageRow) {
        int pageNum = paramObject.getIntValue("pageNum");
        pageNum = pageNum == 0 ? 1 : pageNum;
        int pageRow = paramObject.getIntValue("pageRow");
        pageRow = pageRow == 0 ? defaultPageRow : pageRow;
        paramObject.put("offSet", (pageNum - 1) * pageRow);
        paramObject.put("pageRow", pageRow);
        paramObject.put("pageNum", pageNum);
        //删除此参数,防止前端传了这个参数,pageHelper分页插件检测到之后,拦截导致SQL错误
        paramObject.remove("pageSize");
    }

    /**
     * 查询分页结果后的封装工具方法
     *
     * @param requestJson 请求参数json,此json在之前调用fillPageParam 方法时,已经将pageRow放入
     * @param list        查询分页对象list
     * @param totalCount  查询出记录的总条数
     */
    public static JSONObject successPage(final JSONObject requestJson, List<JSONObject> list, int totalCount) {
        int pageRow = requestJson.getIntValue("pageRow");
        int totalPage = getPageCounts(pageRow, totalCount);
        JSONObject result = successJson();
        JSONObject info = new JSONObject();
        info.put("list", list);
        info.put("totalCount", totalCount);
        info.put("totalPage", totalPage);
        result.put("info", info);
        return result;
    }







    /**
     * 验证数字是否在指定范围内
     *
     * @param value 待验证的数字
     * @param min 最小值
     * @param max 最大值
     * @return 是否在范围内
     */
    public static boolean isInRange(int value, int min, int max) {
        return value >= min && value <= max;
    }

    /**
     * 验证数字是否在指定范围内
     *
     * @param value 待验证的数字
     * @param min 最小值
     * @param max 最大值
     * @return 是否在范围内
     */
    public static boolean isInRange(long value, long min, long max) {
        return value >= min && value <= max;
    }

    /**
     * 检查字符串是否为空或null
     *
     * @param str 待检查的字符串
     * @return 如果字符串为null或空字符串返回true，否则返回false
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 检查字符串是否不为空且不为null
     *
     * @param str 待检查的字符串
     * @return 如果字符串不为null且不为空字符串返回true，否则返回false
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
}

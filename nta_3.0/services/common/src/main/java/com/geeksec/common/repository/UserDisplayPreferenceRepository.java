package com.geeksec.common.repository;

import com.geeksec.common.entity.UserDisplayPreference;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

import static com.geeksec.common.entity.table.UserDisplayPreferenceTableDef.USER_DISPLAY_PREFERENCE;

/**
 * 用户显示偏好配置数据访问接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Mapper
public interface UserDisplayPreferenceRepository extends BaseMapper<UserDisplayPreference> {

    /**
     * 根据用户ID和模块查询配置
     *
     * @param userId 用户ID
     * @param module 模块名称
     * @return 配置列表
     */
    default List<UserDisplayPreference> selectByUserIdAndModule(Integer userId, String module) {
        return selectListByQuery(QueryWrapper.create()
                .where(USER_DISPLAY_PREFERENCE.USER_ID.eq(userId))
                .and(USER_DISPLAY_PREFERENCE.MODULE.eq(module))
                .orderBy(USER_DISPLAY_PREFERENCE.CREATED_AT.desc()));
    }

    /**
     * 根据用户ID、模块和配置类型查询配置
     *
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @return 配置对象
     */
    default UserDisplayPreference selectByUserIdAndModuleAndType(Integer userId, String module, String configType) {
        return selectOneByQuery(QueryWrapper.create()
                .where(USER_DISPLAY_PREFERENCE.USER_ID.eq(userId))
                .and(USER_DISPLAY_PREFERENCE.MODULE.eq(module))
                .and(USER_DISPLAY_PREFERENCE.CONFIG_TYPE.eq(configType)));
    }

    /**
     * 根据用户ID查询所有配置
     *
     * @param userId 用户ID
     * @return 配置列表
     */
    default List<UserDisplayPreference> selectByUserId(Integer userId) {
        return selectListByQuery(QueryWrapper.create()
                .where(USER_DISPLAY_PREFERENCE.USER_ID.eq(userId))
                .orderBy(USER_DISPLAY_PREFERENCE.MODULE.asc(), USER_DISPLAY_PREFERENCE.CONFIG_TYPE.asc()));
    }

    /**
     * 根据模块查询所有用户的配置
     *
     * @param module 模块名称
     * @return 配置列表
     */
    default List<UserDisplayPreference> selectByModule(String module) {
        return selectListByQuery(QueryWrapper.create()
                .where(USER_DISPLAY_PREFERENCE.MODULE.eq(module))
                .orderBy(USER_DISPLAY_PREFERENCE.USER_ID.asc(), USER_DISPLAY_PREFERENCE.CONFIG_TYPE.asc()));
    }

    /**
     * 更新配置JSON内容
     *
     * @param id 配置ID
     * @param configJson 新的配置JSON
     * @param updatedBy 更新者用户ID
     * @return 更新行数
     */
    @Update("UPDATE user_display_preferences SET config_json = #{configJson}, updated_by = #{updatedBy}, updated_at = NOW() WHERE id = #{id}")
    int updateConfigJson(@Param("id") Integer id, @Param("configJson") String configJson, @Param("updatedBy") Integer updatedBy);

    /**
     * 删除用户的特定模块配置
     *
     * @param userId 用户ID
     * @param module 模块名称
     * @return 删除行数
     */
    default int deleteByUserIdAndModule(Integer userId, String module) {
        return deleteByQuery(QueryWrapper.create()
                .where(USER_DISPLAY_PREFERENCE.USER_ID.eq(userId))
                .and(USER_DISPLAY_PREFERENCE.MODULE.eq(module)));
    }

    /**
     * 删除用户的特定配置
     *
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @return 删除行数
     */
    default int deleteByUserIdAndModuleAndType(Integer userId, String module, String configType) {
        return deleteByQuery(QueryWrapper.create()
                .where(USER_DISPLAY_PREFERENCE.USER_ID.eq(userId))
                .and(USER_DISPLAY_PREFERENCE.MODULE.eq(module))
                .and(USER_DISPLAY_PREFERENCE.CONFIG_TYPE.eq(configType)));
    }

    /**
     * 检查用户是否有特定配置
     *
     * @param userId 用户ID
     * @param module 模块名称
     * @param configType 配置类型
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM user_display_preferences WHERE user_id = #{userId} AND module = #{module} AND config_type = #{configType}")
    boolean existsByUserIdAndModuleAndType(@Param("userId") Integer userId, @Param("module") String module, @Param("configType") String configType);

    /**
     * 统计用户配置数量
     *
     * @param userId 用户ID
     * @return 配置数量
     */
    @Select("SELECT COUNT(*) FROM user_display_preferences WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") Integer userId);

    /**
     * 统计模块配置数量
     *
     * @param module 模块名称
     * @return 配置数量
     */
    @Select("SELECT COUNT(*) FROM user_display_preferences WHERE module = #{module}")
    Long countByModule(@Param("module") String module);
}

package com.geeksec.common.service;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.dto.BasePageRequest;
import com.geeksec.common.entity.BaseEntity;
import com.geeksec.common.mapper.CommonBaseMapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 通用服务基类
 * 
 * 提供基础的 CRUD 操作，各服务可以继承此类
 * 
 * @param <M> Mapper 类型
 * @param <T> 实体类型
 * @param <ID> 主键类型
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public abstract class BaseService<M extends CommonBaseMapper<T>, T extends BaseEntity, ID extends Serializable> {

    @Autowired
    protected M mapper;

    /**
     * 根据ID查询
     * 
     * @param id 主键ID
     * @return 实体对象
     */
    public T getById(ID id) {
        return mapper.selectOneById(id);
    }

    /**
     * 查询所有
     * 
     * @return 实体列表
     */
    public List<T> list() {
        return mapper.selectAll();
    }

    /**
     * 根据条件查询
     * 
     * @param queryWrapper 查询条件
     * @return 实体列表
     */
    public List<T> list(QueryWrapper queryWrapper) {
        return mapper.selectListByQuery(queryWrapper);
    }

    /**
     * 分页查询
     * 
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    public Page<T> page(BasePageRequest pageRequest) {
        pageRequest.setDefaultPageParams();
        return mapper.paginate(pageRequest.getPageNum(), pageRequest.getPageSize(), QueryWrapper.create());
    }

    /**
     * 分页查询（带条件）
     * 
     * @param pageRequest 分页请求
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    public Page<T> page(BasePageRequest pageRequest, QueryWrapper queryWrapper) {
        pageRequest.setDefaultPageParams();
        return mapper.paginate(pageRequest.getPageNum(), pageRequest.getPageSize(), queryWrapper);
    }

    /**
     * 保存实体
     * 
     * @param entity 实体对象
     * @return 是否成功
     */
    public boolean save(T entity) {
        if (entity.getCreatedAt() == null) {
            entity.setCreatedAt(LocalDateTime.now());
        }
        if (entity.getUpdatedAt() == null) {
            entity.setUpdatedAt(LocalDateTime.now());
        }
        return mapper.insert(entity) > 0;
    }

    /**
     * 批量保存
     * 
     * @param entities 实体列表
     * @return 是否成功
     */
    public boolean saveBatch(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return true;
        }
        
        LocalDateTime now = LocalDateTime.now();
        entities.forEach(entity -> {
            if (entity.getCreatedAt() == null) {
                entity.setCreatedAt(now);
            }
            if (entity.getUpdatedAt() == null) {
                entity.setUpdatedAt(now);
            }
        });
        
        return mapper.insertBatch(entities) > 0;
    }

    /**
     * 更新实体
     * 
     * @param entity 实体对象
     * @return 是否成功
     */
    public boolean updateById(T entity) {
        entity.setUpdatedAt(LocalDateTime.now());
        return mapper.update(entity) > 0;
    }

    /**
     * 根据ID删除
     * 
     * @param id 主键ID
     * @return 是否成功
     */
    public boolean removeById(ID id) {
        return mapper.deleteById(id) > 0;
    }

    /**
     * 批量删除
     * 
     * @param ids 主键ID列表
     * @return 是否成功
     */
    public boolean removeByIds(List<ID> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }
        return mapper.deleteBatchByIds(ids) > 0;
    }

    /**
     * 根据条件删除
     * 
     * @param queryWrapper 查询条件
     * @return 是否成功
     */
    public boolean remove(QueryWrapper queryWrapper) {
        return mapper.deleteByQuery(queryWrapper) > 0;
    }

    /**
     * 统计数量
     *
     * @return 总数量
     */
    public long count() {
        return mapper.selectCountByQuery(QueryWrapper.create());
    }

    /**
     * 根据条件统计数量
     * 
     * @param queryWrapper 查询条件
     * @return 数量
     */
    public long count(QueryWrapper queryWrapper) {
        return mapper.selectCountByQuery(queryWrapper);
    }

    /**
     * 判断是否存在
     * 
     * @param id 主键ID
     * @return 是否存在
     */
    public boolean exists(ID id) {
        return getById(id) != null;
    }

    /**
     * 根据条件判断是否存在
     * 
     * @param queryWrapper 查询条件
     * @return 是否存在
     */
    public boolean exists(QueryWrapper queryWrapper) {
        return count(queryWrapper) > 0;
    }

    // ==================== 响应包装方法 ====================

    /**
     * 包装成功响应
     * 
     * @param data 数据
     * @param <R> 数据类型
     * @return API响应
     */
    protected <R> ApiResponse<R> success(R data) {
        return ApiResponse.success(data);
    }

    /**
     * 包装成功响应（无数据）
     * 
     * @return API响应
     */
    protected ApiResponse<Void> success() {
        return ApiResponse.success();
    }

    /**
     * 包装错误响应
     * 
     * @param message 错误信息
     * @return API响应
     */
    protected ApiResponse<Void> error(String message) {
        return ApiResponse.error(message);
    }

    /**
     * 包装错误响应
     * 
     * @param code 错误码
     * @param message 错误信息
     * @return API响应
     */
    protected ApiResponse<Void> error(Integer code, String message) {
        return ApiResponse.error(code, message);
    }
}

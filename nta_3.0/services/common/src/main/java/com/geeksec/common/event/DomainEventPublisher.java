package com.geeksec.common.event;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 领域事件发布器
 * 
 * <AUTHOR>
 */
@Component
public class DomainEventPublisher {
    private final ApplicationEventPublisher applicationEventPublisher;

    public DomainEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    /**
     * 发布领域事件
     * 
     * @param event 领域事件
     */
    public void publish(DomainEvent event) {
        applicationEventPublisher.publishEvent(event);
    }
}

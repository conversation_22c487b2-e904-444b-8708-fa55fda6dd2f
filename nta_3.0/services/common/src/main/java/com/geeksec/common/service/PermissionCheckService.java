package com.geeksec.common.service;

/**
 * 权限检查服务接口
 * 用于检查用户是否具有特定权限
 * 
 * <AUTHOR>
 */
public interface PermissionCheckService {
    
    /**
     * 检查当前用户是否具有指定权限
     * 
     * @param permission 权限标识
     * @return 是否具有权限
     */
    boolean hasPermission(String permission);
    
    /**
     * 检查当前用户是否具有所有指定权限
     * 
     * @param permissions 权限标识数组
     * @return 是否具有所有权限
     */
    boolean hasAllPermissions(String... permissions);
    
    /**
     * 检查当前用户是否具有任一指定权限
     * 
     * @param permissions 权限标识数组
     * @return 是否具有任一权限
     */
    boolean hasAnyPermission(String... permissions);
}

package com.geeksec.common.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * 基础领域实体类
 *
 * 用于DDD领域层的基础实体，不包含持久化相关注解
 * 继承 BaseBusinessEntity 获得通用业务逻辑
 * 添加领域特定的唯一标识
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "基础领域实体类")
public abstract class BaseDomainEntity extends BaseBusinessEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 实体唯一标识
     */
    @Schema(description = "实体唯一标识")
    protected String id;

}

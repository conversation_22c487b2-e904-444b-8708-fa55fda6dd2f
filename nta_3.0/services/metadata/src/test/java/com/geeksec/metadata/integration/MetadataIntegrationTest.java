package com.geeksec.metadata.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 元数据服务集成测试类
 * 测试完整的元数据服务功能流程
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@DisplayName("元数据服务集成测试")
class MetadataIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @DisplayName("系统配置接口完整流程测试")
    void testSystemConfigCompleteFlow() throws Exception {
        setUp();
        
        // 1. 测试获取所有系统元数据（兼容 NTA 2.0）
        mockMvc.perform(get("/api/v1/metadata/dict")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // 2. 测试获取攻击链模型（兼容 NTA 2.0）
        mockMvc.perform(get("/api/v1/metadata/dict/alarm")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());

        // 3. 测试获取证书类型
        mockMvc.perform(get("/api/v1/metadata/cert/types")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // 4. 测试获取域名类型
        mockMvc.perform(get("/api/v1/metadata/domain/types")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // 5. 测试获取国家
        mockMvc.perform(get("/api/v1/metadata/system/countries")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    @DisplayName("协议接口完整流程测试")
    void testProtocolCompleteFlow() throws Exception {
        setUp();
        
        // 1. 测试获取所有应用协议
        mockMvc.perform(get("/api/v1/metadata/protocols/application")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());

        // 2. 测试获取协议类型
        mockMvc.perform(get("/api/v1/metadata/protocols/metadata/types")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // 3. 测试获取应用协议信息
        mockMvc.perform(get("/api/v1/metadata/protocols/metadata/info")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    @DisplayName("标签接口完整流程测试")
    void testLabelCompleteFlow() throws Exception {
        setUp();
        
        // 1. 测试获取所有IP标签
        mockMvc.perform(get("/api/v1/metadata/labels/ip")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());

        // 2. 测试获取标签分类
        mockMvc.perform(get("/api/v1/metadata/labels/metadata/categories")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());

        // 3. 测试获取所有标签分类
        mockMvc.perform(get("/api/v1/metadata/labels/metadata/categories/all")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    @DisplayName("攻击阶段接口完整流程测试")
    void testAttackStageCompleteFlow() throws Exception {
        setUp();
        
        // 1. 测试获取所有攻击阶段详情
        mockMvc.perform(get("/api/v1/metadata/attack-stages")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());

        // 2. 测试获取攻击链模型
        mockMvc.perform(get("/api/v1/metadata/attack-stages/metadata/attack-chain")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());

        // 3. 测试获取告警类型
        mockMvc.perform(get("/api/v1/metadata/attack-stages/metadata/alarm-types")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    @DisplayName("缓存管理接口完整流程测试")
    void testCacheManagementCompleteFlow() throws Exception {
        setUp();
        
        // 1. 测试获取缓存统计信息
        mockMvc.perform(get("/api/v1/metadata/cache/statistics")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        // 2. 测试预热所有缓存
        mockMvc.perform(post("/api/v1/metadata/cache/warmup/all")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("所有缓存预热完成"));

        // 3. 测试清理所有缓存
        mockMvc.perform(delete("/api/v1/metadata/cache/all")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("所有缓存已清理"));
    }

    @Test
    @DisplayName("API 响应格式一致性测试")
    void testApiResponseConsistency() throws Exception {
        setUp();
        
        // 测试系统配置接口的响应格式一致性
        String[] configEndpoints = {
            "/api/v1/metadata/cert/types",
            "/api/v1/metadata/domain/types",
            "/api/v1/metadata/system/countries"
        };

        for (String endpoint : configEndpoints) {
            mockMvc.perform(get(endpoint)
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk())
                    .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                    .andExpect(jsonPath("$.dictType").exists())
                    .andExpect(jsonPath("$.dictDescription").exists())
                    .andExpect(jsonPath("$.items").isArray());
        }
    }
}

package com.geeksec.metadata.service;

import com.geeksec.metadata.service.impl.QueryFieldMetadataServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 查询字段元数据服务测试类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("查询字段元数据服务测试")
class QueryFieldMetadataServiceTest {

    @InjectMocks
    private QueryFieldMetadataServiceImpl queryFieldMetadataService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    // ==================== ES查询字段测试 ====================

    @Test
    @DisplayName("获取所有ES查询字段")
    void testGetAllEsQueryFields() {
        // When
        Map<String, Object> esQueryFields = queryFieldMetadataService.getAllEsQueryFields();

        // Then
        assertThat(esQueryFields).isNotEmpty();
        assertThat(esQueryFields).containsKey("ssl_subject");
        assertThat(esQueryFields.get("ssl_subject")).isEqualTo("SSL证书主题");
        assertThat(esQueryFields).containsKey("http_host");
        assertThat(esQueryFields.get("http_host")).isEqualTo("HTTP主机");
    }

    @Test
    @DisplayName("根据字段名获取ES查询字段配置")
    void testGetEsQueryFieldConfig() {
        // When
        Object config = queryFieldMetadataService.getEsQueryFieldConfig("ssl_subject");

        // Then
        assertThat(config).isNotNull();
        assertThat(config.toString()).isEqualTo("SSL证书主题");
    }

    @Test
    @DisplayName("获取ES查询字段显示名称")
    void testGetEsQueryFieldDisplayName() {
        // When
        String displayName = queryFieldMetadataService.getEsQueryFieldDisplayName("ssl_subject");

        // Then
        assertThat(displayName).isEqualTo("SSL证书主题");
    }

    @Test
    @DisplayName("获取不存在的ES查询字段显示名称应返回字段名")
    void testGetEsQueryFieldDisplayNameForNonExistentField() {
        // When
        String displayName = queryFieldMetadataService.getEsQueryFieldDisplayName("non_existent_field");

        // Then
        assertThat(displayName).isEqualTo("non_existent_field");
    }

    // ==================== 下载搜索字段测试 ====================

    @Test
    @DisplayName("获取所有下载搜索字段")
    void testGetAllDownloadSearchFields() {
        // When
        Map<String, Object> downloadSearchFields = queryFieldMetadataService.getAllDownloadSearchFields();

        // Then
        assertThat(downloadSearchFields).isNotEmpty();
        assertThat(downloadSearchFields).containsKey("timestamp");
        assertThat(downloadSearchFields.get("timestamp")).isEqualTo("时间戳");
        assertThat(downloadSearchFields).containsKey("src_ip");
        assertThat(downloadSearchFields.get("src_ip")).isEqualTo("源IP");
    }

    @Test
    @DisplayName("根据字段名获取下载搜索字段配置")
    void testGetDownloadSearchFieldConfig() {
        // When
        Object config = queryFieldMetadataService.getDownloadSearchFieldConfig("src_ip");

        // Then
        assertThat(config).isNotNull();
        assertThat(config.toString()).isEqualTo("源IP");
    }

    @Test
    @DisplayName("获取下载搜索字段显示名称")
    void testGetDownloadSearchFieldDisplayName() {
        // When
        String displayName = queryFieldMetadataService.getDownloadSearchFieldDisplayName("src_ip");

        // Then
        assertThat(displayName).isEqualTo("源IP");
    }

    // ==================== Nebula类型测试 ====================

    @Test
    @DisplayName("获取所有Nebula类型")
    void testGetAllNebulaTypes() {
        // When
        Map<String, Object> nebulaTypes = queryFieldMetadataService.getAllNebulaTypes();

        // Then
        assertThat(nebulaTypes).isNotEmpty();
        assertThat(nebulaTypes).containsKey("ip");
        assertThat(nebulaTypes.get("ip")).isEqualTo("IP地址");
        assertThat(nebulaTypes).containsKey("domain");
        assertThat(nebulaTypes.get("domain")).isEqualTo("域名");
    }

    @Test
    @DisplayName("根据类型获取Nebula类型名称")
    void testGetNebulaTypeName() {
        // When
        String typeName = queryFieldMetadataService.getNebulaTypeName("ip");

        // Then
        assertThat(typeName).isEqualTo("IP地址");
    }

    @Test
    @DisplayName("获取不存在的Nebula类型名称应返回类型本身")
    void testGetNebulaTypeNameForNonExistentType() {
        // When
        String typeName = queryFieldMetadataService.getNebulaTypeName("non_existent_type");

        // Then
        assertThat(typeName).isEqualTo("non_existent_type");
    }

    // ==================== Nebula属性测试 ====================

    @Test
    @DisplayName("获取所有Nebula属性")
    void testGetAllNebulaProperties() {
        // When
        Map<String, Object> nebulaProperties = queryFieldMetadataService.getAllNebulaProperties();

        // Then
        assertThat(nebulaProperties).isNotEmpty();
        assertThat(nebulaProperties).containsKey("ip.addr");
        assertThat(nebulaProperties.get("ip.addr")).isEqualTo("IP地址");
        assertThat(nebulaProperties).containsKey("domain.name");
        assertThat(nebulaProperties.get("domain.name")).isEqualTo("域名");
    }

    @Test
    @DisplayName("根据属性名获取Nebula属性配置")
    void testGetNebulaPropertyConfig() {
        // When
        Object config = queryFieldMetadataService.getNebulaPropertyConfig("ip.addr");

        // Then
        assertThat(config).isNotNull();
        assertThat(config.toString()).isEqualTo("IP地址");
    }

    // ==================== 字段验证测试 ====================

    @Test
    @DisplayName("验证有效的ES查询字段")
    void testIsValidEsQueryField() {
        // When
        boolean isValid = queryFieldMetadataService.isValidEsQueryField("ssl_subject");

        // Then
        assertThat(isValid).isTrue();
    }

    @Test
    @DisplayName("验证无效的ES查询字段")
    void testIsInvalidEsQueryField() {
        // When
        boolean isValid = queryFieldMetadataService.isValidEsQueryField("invalid_field");

        // Then
        assertThat(isValid).isFalse();
    }

    @Test
    @DisplayName("验证有效的下载搜索字段")
    void testIsValidDownloadSearchField() {
        // When
        boolean isValid = queryFieldMetadataService.isValidDownloadSearchField("src_ip");

        // Then
        assertThat(isValid).isTrue();
    }

    @Test
    @DisplayName("验证无效的下载搜索字段")
    void testIsInvalidDownloadSearchField() {
        // When
        boolean isValid = queryFieldMetadataService.isValidDownloadSearchField("invalid_field");

        // Then
        assertThat(isValid).isFalse();
    }

    @Test
    @DisplayName("验证有效的Nebula类型")
    void testIsValidNebulaType() {
        // When
        boolean isValid = queryFieldMetadataService.isValidNebulaType("ip");

        // Then
        assertThat(isValid).isTrue();
    }

    @Test
    @DisplayName("验证无效的Nebula类型")
    void testIsInvalidNebulaType() {
        // When
        boolean isValid = queryFieldMetadataService.isValidNebulaType("invalid_type");

        // Then
        assertThat(isValid).isFalse();
    }

    @Test
    @DisplayName("验证有效的Nebula属性")
    void testIsValidNebulaProperty() {
        // When
        boolean isValid = queryFieldMetadataService.isValidNebulaProperty("ip.addr");

        // Then
        assertThat(isValid).isTrue();
    }

    @Test
    @DisplayName("验证无效的Nebula属性")
    void testIsInvalidNebulaProperty() {
        // When
        boolean isValid = queryFieldMetadataService.isValidNebulaProperty("invalid.property");

        // Then
        assertThat(isValid).isFalse();
    }
}

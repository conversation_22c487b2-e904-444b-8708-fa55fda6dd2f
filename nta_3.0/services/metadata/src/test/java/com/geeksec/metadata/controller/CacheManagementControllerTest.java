package com.geeksec.metadata.controller;

import com.geeksec.metadata.service.CacheManagementService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 缓存管理控制器测试类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@WebMvcTest(CacheManagementController.class)
@DisplayName("缓存管理控制器测试")
class CacheManagementControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private CacheManagementService cacheManagementService;

    @Test
    @DisplayName("清理系统字典缓存")
    void testClearSystemDictCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).clearSystemDictCache();

        // When & Then
        mockMvc.perform(delete("/api/v1/metadata/cache/system-dict")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("系统字典缓存已清理"));

        verify(cacheManagementService, times(1)).clearSystemDictCache();
    }

    @Test
    @DisplayName("清理指定字典缓存")
    void testClearDictCache() throws Exception {
        // Given
        String dictType = "cert_type";
        doNothing().when(cacheManagementService).clearDictCache(anyString());

        // When & Then
        mockMvc.perform(delete("/api/v1/metadata/cache/system-dict/" + dictType)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("字典缓存已清理: " + dictType));

        verify(cacheManagementService, times(1)).clearDictCache(dictType);
    }

    @Test
    @DisplayName("清理所有标签缓存")
    void testClearAllLabelCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).clearAllLabelCache();

        // When & Then
        mockMvc.perform(delete("/api/v1/metadata/cache/labels")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("所有标签缓存已清理"));

        verify(cacheManagementService, times(1)).clearAllLabelCache();
    }

    @Test
    @DisplayName("清理指定标签缓存")
    void testClearLabelCache() throws Exception {
        // Given
        String labelType = "ip";
        doNothing().when(cacheManagementService).clearLabelCache(anyString());

        // When & Then
        mockMvc.perform(delete("/api/v1/metadata/cache/labels/" + labelType)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("标签缓存已清理: " + labelType));

        verify(cacheManagementService, times(1)).clearLabelCache(labelType);
    }

    @Test
    @DisplayName("清理所有协议缓存")
    void testClearAllProtocolCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).clearAllProtocolCache();

        // When & Then
        mockMvc.perform(delete("/api/v1/metadata/cache/protocols")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("所有协议缓存已清理"));

        verify(cacheManagementService, times(1)).clearAllProtocolCache();
    }

    @Test
    @DisplayName("清理应用协议缓存")
    void testClearAppProtocolCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).clearAppProtocolCache();

        // When & Then
        mockMvc.perform(delete("/api/v1/metadata/cache/app-protocols")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("应用协议缓存已清理"));

        verify(cacheManagementService, times(1)).clearAppProtocolCache();
    }

    @Test
    @DisplayName("清理所有缓存")
    void testClearAllCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).clearAllCache();

        // When & Then
        mockMvc.perform(delete("/api/v1/metadata/cache/all")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("所有缓存已清理"));

        verify(cacheManagementService, times(1)).clearAllCache();
    }

    @Test
    @DisplayName("预热系统字典缓存")
    void testWarmupSystemDictCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).warmupSystemDictCache();

        // When & Then
        mockMvc.perform(post("/api/v1/metadata/cache/warmup/system-dict")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("系统字典缓存预热完成"));

        verify(cacheManagementService, times(1)).warmupSystemDictCache();
    }

    @Test
    @DisplayName("预热标签缓存")
    void testWarmupLabelCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).warmupLabelCache();

        // When & Then
        mockMvc.perform(post("/api/v1/metadata/cache/warmup/labels")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("标签缓存预热完成"));

        verify(cacheManagementService, times(1)).warmupLabelCache();
    }

    @Test
    @DisplayName("预热协议缓存")
    void testWarmupProtocolCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).warmupProtocolCache();

        // When & Then
        mockMvc.perform(post("/api/v1/metadata/cache/warmup/protocols")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("协议缓存预热完成"));

        verify(cacheManagementService, times(1)).warmupProtocolCache();
    }

    @Test
    @DisplayName("预热所有缓存")
    void testWarmupAllCache() throws Exception {
        // Given
        doNothing().when(cacheManagementService).warmupAllCache();

        // When & Then
        mockMvc.perform(post("/api/v1/metadata/cache/warmup/all")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("所有缓存预热完成"));

        verify(cacheManagementService, times(1)).warmupAllCache();
    }

    @Test
    @DisplayName("获取缓存统计信息")
    void testGetCacheStatistics() throws Exception {
        // Given
        String mockStatistics = "缓存统计信息:\n- systemDict: ConcurrentMapCache\n- appProtocolInfo: ConcurrentMapCache\n";
        when(cacheManagementService.getCacheStatistics()).thenReturn(mockStatistics);

        // When & Then
        mockMvc.perform(get("/api/v1/metadata/cache/statistics")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(mockStatistics));

        verify(cacheManagementService, times(1)).getCacheStatistics();
    }
}

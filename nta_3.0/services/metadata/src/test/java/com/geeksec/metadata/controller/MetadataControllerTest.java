package com.geeksec.metadata.controller;

import com.geeksec.metadata.service.MetadataService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 元数据控制器测试类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@WebMvcTest(MetadataController.class)
class MetadataControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private MetadataService metadataService;

    @Test
    void testGetAllTags() throws Exception {
        // 准备测试数据
        when(metadataService.getAllTags()).thenReturn(new ArrayList<>());

        // 执行测试
        mockMvc.perform(get("/api/metadata/tags"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    void testGetAllIpProtocols() throws Exception {
        // 准备测试数据
        when(metadataService.getAllIpProtocols()).thenReturn(new ArrayList<>());

        // 执行测试
        mockMvc.perform(get("/api/metadata/protocols/ip"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    void testGetAllDevices() throws Exception {
        // 准备测试数据
        when(metadataService.getAllDevices()).thenReturn(new ArrayList<>());

        // 执行测试
        mockMvc.perform(get("/api/metadata/devices"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }

    @Test
    void testGetPopularTags() throws Exception {
        // 准备测试数据
        when(metadataService.getPopularTags(10)).thenReturn(new ArrayList<>());

        // 执行测试
        mockMvc.perform(get("/api/metadata/tags/popular?limit=10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());
    }
}

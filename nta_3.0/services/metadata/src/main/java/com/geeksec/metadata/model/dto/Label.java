package com.geeksec.metadata.model.dto;

import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.geeksec.metadata.model.enums.LabelTargetType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 标签数据传输对象
 * 对应统一的labels表
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "标签")
public class Label {

    /**
     * 标签ID
     */
    @Schema(description = "标签ID", example = "1001")
    private Integer id;

    /**
     * 标签名称（英文标识）
     */
    @Schema(description = "标签名称", example = "malicious_ip")
    private String name;

    /**
     * 显示名称（用户友好名称）
     */
    @Schema(description = "显示名称", example = "恶意IP")
    private String displayName;

    /**
     * 标签描述
     */
    @Schema(description = "标签描述", example = "已知的恶意IP地址")
    private String description;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "来源于威胁情报")
    private String remark;

    /**
     * 标签目标类型
     */
    @Schema(description = "标签目标类型", example = "IP")
    private LabelTargetType targetType;

    /**
     * 标签类别
     */
    @Schema(description = "标签类别", example = "THREAT")
    private LabelCategory category;

    /**
     * 标签来源
     */
    @Schema(description = "标签来源", example = "SYSTEM")
    private LabelSource source;

    /**
     * 威胁等级（0-100）
     */
    @Schema(description = "威胁等级", example = "80")
    private Integer threatLevel;

    /**
     * 信任等级（0-100）
     */
    @Schema(description = "信任等级", example = "20")
    private Integer trustLevel;

    /**
     * 默认威胁等级（0-100）
     */
    @Schema(description = "默认威胁等级", example = "80")
    private Integer defaultThreatLevel;

    /**
     * 默认信任等级（0-100）
     */
    @Schema(description = "默认信任等级", example = "20")
    private Integer defaultTrustLevel;

    /**
     * Cyber Kill Chain 阶段
     */
    @Schema(description = "Cyber Kill Chain 阶段", example = "RECONNAISSANCE")
    private CyberKillChain cyberKillChain;

    /**
     * 标签颜色（十六进制）
     */
    @Schema(description = "标签颜色", example = "#FF0000")
    private String color;

    /**
     * 排序顺序
     */
    @Schema(description = "排序顺序", example = "1")
    private Integer sortOrder;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean active;

    /**
     * 版本号（乐观锁）
     */
    @Schema(description = "版本号", example = "1")
    private Integer version;

    /**
     * 创建者用户ID
     */
    @Schema(description = "创建者用户ID", example = "1001")
    private Integer createdBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2023-01-01T00:00:00")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间", example = "2023-01-01T00:00:00")
    private LocalDateTime updatedAt;

    /**
     * 获取标签目标类型描述
     *
     * @return 标签目标类型描述
     */
    public String getTargetTypeDescription() {
        if (targetType == null) {
            return "未知";
        }
        return targetType.getChineseName();
    }

    /**
     * 获取标签类别描述
     *
     * @return 标签类别描述
     */
    public String getCategoryDescription() {
        if (category == null) {
            return "未知";
        }
        return category.getChineseName();
    }

    /**
     * 获取 Cyber Kill Chain 阶段描述
     *
     * @return Cyber Kill Chain 阶段描述
     */
    public String getCyberKillChainDescription() {
        if (cyberKillChain == null) {
            return "未知";
        }
        return cyberKillChain.getChineseName();
    }
}

package com.geeksec.metadata.controller;

import com.geeksec.metadata.model.dto.NetworkProtocolDto;
import com.geeksec.metadata.model.entity.NetworkProtocol;
import com.geeksec.metadata.model.entity.InternetProtocol;
import com.geeksec.metadata.service.ProtocolMetadataService;
import com.geeksec.metadata.service.ProtocolService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 协议控制器
 * 提供各种协议的查询接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/metadata/protocols")
@RequiredArgsConstructor
@Tag(name = "协议", description = "提供各种协议的查询和管理功能")
public class ProtocolController {

    private final ProtocolService protocolService;
    private final ProtocolMetadataService protocolMetadataService;

    // ==================== 网络协议接口 ====================

    @GetMapping("/network")
    @Operation(summary = "获取所有网络协议", description = "获取系统中所有的网络协议")
    public ResponseEntity<List<NetworkProtocol>> getAllNetworkProtocols() {
        log.info("获取所有网络协议");
        List<NetworkProtocol> protocols = protocolService.getAllNetworkProtocols();
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/{protocolId}")
    @Operation(summary = "根据协议ID获取网络协议", description = "根据协议ID获取具体的网络协议信息")
    public ResponseEntity<NetworkProtocol> getNetworkProtocolById(
            @Parameter(description = "协议ID") @PathVariable Integer protocolId) {
        log.info("根据协议ID获取网络协议: {}", protocolId);
        NetworkProtocol protocol = protocolService.getNetworkProtocolById(protocolId);
        if (protocol != null) {
            return ResponseEntity.ok(protocol);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/network/name/{protocolName}")
    @Operation(summary = "根据协议名称获取网络协议", description = "根据协议名称获取相关的网络协议")
    public ResponseEntity<List<NetworkProtocol>> getNetworkProtocolsByName(
            @Parameter(description = "协议名称") @PathVariable String protocolName) {
        log.info("根据协议名称获取网络协议: {}", protocolName);
        List<NetworkProtocol> protocols = protocolService.getNetworkProtocolsByName(protocolName);
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/display-name/{displayName}")
    @Operation(summary = "根据显示名称获取网络协议", description = "根据显示名称获取相关的网络协议")
    public ResponseEntity<List<NetworkProtocol>> getNetworkProtocolsByDisplayName(
            @Parameter(description = "显示名称") @PathVariable String displayName) {
        log.info("根据显示名称获取网络协议: {}", displayName);
        List<NetworkProtocol> protocols = protocolService.getNetworkProtocolsByDisplayName(displayName);
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/category/{category}")
    @Operation(summary = "根据协议分类获取网络协议", description = "根据协议分类获取网络协议")
    public ResponseEntity<List<NetworkProtocol>> getNetworkProtocolsByCategory(
            @Parameter(description = "协议分类") @PathVariable String category) {
        log.info("根据协议分类获取网络协议: {}", category);
        List<NetworkProtocol> protocols = protocolService.getNetworkProtocolsByCategory(category);
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/type/{protocolType}")
    @Operation(summary = "根据协议类型获取网络协议", description = "根据协议类型获取网络协议（1-连接, 2-单包, 3-tcp/udp负载）")
    public ResponseEntity<List<NetworkProtocol>> getNetworkProtocolsByProtocolType(
            @Parameter(description = "协议类型") @PathVariable Integer protocolType) {
        log.info("根据协议类型获取网络协议: {}", protocolType);
        List<NetworkProtocol> protocols = protocolService.getNetworkProtocolsByProtocolType(protocolType);
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/search")
    @Operation(summary = "搜索网络协议", description = "根据关键字搜索网络协议")
    public ResponseEntity<List<NetworkProtocol>> searchNetworkProtocols(
            @Parameter(description = "搜索关键字") @RequestParam String keyword) {
        log.info("搜索网络协议: {}", keyword);
        List<NetworkProtocol> protocols = protocolService.searchNetworkProtocols(keyword);
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/connection")
    @Operation(summary = "获取所有连接类型协议", description = "获取所有连接类型的网络协议")
    public ResponseEntity<List<NetworkProtocol>> getAllConnectionProtocols() {
        log.info("获取所有连接类型协议");
        List<NetworkProtocol> protocols = protocolService.getAllConnectionProtocols();
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/single-packet")
    @Operation(summary = "获取所有单包类型协议", description = "获取所有单包类型的网络协议")
    public ResponseEntity<List<NetworkProtocol>> getAllSinglePacketProtocols() {
        log.info("获取所有单包类型协议");
        List<NetworkProtocol> protocols = protocolService.getAllSinglePacketProtocols();
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/payload")
    @Operation(summary = "获取所有TCP/UDP负载类型协议", description = "获取所有TCP/UDP负载类型的网络协议")
    public ResponseEntity<List<NetworkProtocol>> getAllPayloadProtocols() {
        log.info("获取所有TCP/UDP负载类型协议");
        List<NetworkProtocol> protocols = protocolService.getAllPayloadProtocols();
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/network/common")
    @Operation(summary = "获取常用协议", description = "获取常用的网络协议列表")
    public ResponseEntity<List<NetworkProtocol>> getCommonProtocols() {
        log.info("获取常用协议");
        List<NetworkProtocol> protocols = protocolService.getCommonProtocols();
        return ResponseEntity.ok(protocols);
    }



    // ==================== 互联网协议接口 ====================

    @GetMapping("/internet-protocols")
    @Operation(summary = "获取所有互联网协议", description = "获取系统中所有的互联网协议")
    public ResponseEntity<List<InternetProtocol>> getAllInternetProtocols() {
        log.info("获取所有互联网协议");
        List<InternetProtocol> protocols = protocolService.getAllInternetProtocols();
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/internet-protocols/{proId}")
    @Operation(summary = "根据协议号获取互联网协议", description = "根据协议号获取具体的互联网协议信息")
    public ResponseEntity<InternetProtocol> getInternetProtocolById(
            @Parameter(description = "协议号") @PathVariable String proId) {
        log.info("根据协议号获取互联网协议: {}", proId);
        InternetProtocol protocol = protocolService.getInternetProtocolById(proId);
        if (protocol != null) {
            return ResponseEntity.ok(protocol);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/internet-protocols/name/{protocolName}")
    @Operation(summary = "根据协议名称获取互联网协议", description = "根据协议名称获取相关的互联网协议")
    public ResponseEntity<List<InternetProtocol>> getInternetProtocolsByName(
            @Parameter(description = "协议名称") @PathVariable String protocolName) {
        log.info("根据协议名称获取互联网协议: {}", protocolName);
        List<InternetProtocol> protocols = protocolService.getInternetProtocolsByName(protocolName);
        return ResponseEntity.ok(protocols);
    }

    @GetMapping("/internet-protocols/search")
    @Operation(summary = "搜索互联网协议", description = "根据关键字搜索互联网协议")
    public ResponseEntity<List<InternetProtocol>> searchInternetProtocols(
            @Parameter(description = "搜索关键字") @RequestParam String keyword) {
        log.info("搜索互联网协议: {}", keyword);
        List<InternetProtocol> protocols = protocolService.searchInternetProtocols(keyword);
        return ResponseEntity.ok(protocols);
    }

    // ==================== 通用协议接口 ====================

    @GetMapping("/types")
    @Operation(summary = "获取所有协议类型", description = "获取系统中所有的协议类型")
    public ResponseEntity<List<String>> getAllProtocolTypes() {
        log.info("获取所有协议类型");
        List<String> types = protocolService.getAllProtocolTypes();
        return ResponseEntity.ok(types);
    }

    @GetMapping("/names")
    @Operation(summary = "获取所有协议名称", description = "获取系统中所有的协议名称")
    public ResponseEntity<List<String>> getAllProtocolNames() {
        log.info("获取所有协议名称");
        List<String> names = protocolService.getAllProtocolNames();
        return ResponseEntity.ok(names);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取协议统计信息", description = "获取各类型协议的数量统计")
    public ResponseEntity<Map<String, Long>> getProtocolStatistics() {
        log.info("获取协议统计信息");
        Map<String, Long> statistics = protocolService.getProtocolStatistics();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索所有类型协议", description = "根据关键字搜索所有类型的协议")
    public ResponseEntity<Map<String, Object>> searchAllProtocols(
            @Parameter(description = "搜索关键字") @RequestParam String keyword) {
        log.info("搜索所有类型协议: {}", keyword);
        Map<String, Object> results = protocolService.searchAllProtocols(keyword);
        return ResponseEntity.ok(results);
    }

    // ==================== 缓存管理接口 ====================

    @PostMapping("/cache/init")
    @Operation(summary = "初始化协议缓存", description = "初始化所有类型协议的缓存")
    public ResponseEntity<String> initProtocolCache() {
        log.info("初始化协议缓存");
        protocolService.initProtocolCache();
        return ResponseEntity.ok("协议缓存初始化成功");
    }

    @DeleteMapping("/cache")
    @Operation(summary = "清除协议缓存", description = "清除所有类型协议的缓存")
    public ResponseEntity<String> clearProtocolCache() {
        log.info("清除协议缓存");
        protocolService.clearProtocolCache();
        return ResponseEntity.ok("协议缓存清除成功");
    }

    // ==================== 协议元数据接口 ====================

    @GetMapping("/metadata/types")
    @Operation(summary = "获取协议类型", description = "获取系统中所有协议类型")
    public ResponseEntity<Map<String, Object>> getProtocolTypes() {
        log.info("获取协议类型");
        Map<String, Object> result = protocolMetadataService.getAllProtocolTypes();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/info")
    @Operation(summary = "获取网络协议信息", description = "获取系统中所有网络协议信息")
    public ResponseEntity<List<NetworkProtocolDto>> getNetworkProtocolInfo() {
        log.info("获取网络协议信息");
        List<NetworkProtocolDto> result = protocolMetadataService.getAllNetworkProtocols();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/network-ids")
    @Operation(summary = "获取网络协议ID", description = "获取系统中所有网络协议ID")
    public ResponseEntity<Map<String, Object>> getNetworkProtocolIds() {
        log.info("获取网络协议ID");
        Map<String, Object> result = protocolMetadataService.getNetworkProtocolIdMapping();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/app-values")
    @Operation(summary = "获取应用值", description = "获取系统中所有应用值")
    public ResponseEntity<Map<String, Object>> getAppValues() {
        log.info("获取应用值");
        Map<String, Object> result = protocolMetadataService.getApplicationValueMapping();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/metadata/app-type-map")
    @Operation(summary = "获取应用类型映射", description = "获取系统中所有应用类型映射")
    public ResponseEntity<Map<String, Object>> getAppTypeMap() {
        log.info("获取应用类型映射");
        Map<String, Object> result = protocolMetadataService.getApplicationTypeMapping();
        return ResponseEntity.ok(result);
    }
}

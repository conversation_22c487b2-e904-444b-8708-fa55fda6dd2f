package com.geeksec.metadata.model.entity;

import com.mybatisflex.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 网络协议实体类
 * 对应 network_protocols 表
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("network_protocols")
public class NetworkProtocol implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 协议ID
     */
    @Id(keyType = KeyType.None)
    @Column("id")
    private Integer id;

    /**
     * 协议名称
     */
    @Column("protocol_name")
    private String protocolName;

    /**
     * 显示名称
     */
    @Column("display_name")
    private String displayName;

    /**
     * 协议分类
     */
    @Column("category")
    private String category;

    /**
     * 协议描述
     */
    @Column("description")
    private String description;

    /**
     * 协议类型 (1-连接, 2-单包, 3-tcp/udp负载)
     */
    @Column("protocol_type")
    private Integer protocolType;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;
}

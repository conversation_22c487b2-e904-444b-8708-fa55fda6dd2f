package com.geeksec.metadata.service.impl;

import com.geeksec.metadata.model.dto.Label;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.geeksec.metadata.model.enums.LabelTargetType;
import com.geeksec.metadata.service.LabelMetadataService;
import com.geeksec.metadata.service.LabelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签元数据服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LabelMetadataServiceImpl implements LabelMetadataService {

    private final LabelService labelService;

    // ==================== 标签分类相关实现 ====================

    @Override
    @Cacheable(value = "labelMetadata", key = "'labelCategories'")
    public List<LabelCategory> getAllLabelCategories() {
        log.debug("获取所有标签分类");
        return Arrays.asList(LabelCategory.values());
    }

    @Override
    @Cacheable(value = "labelMetadata", key = "'labelCategoryMapping'")
    public Map<String, Object> getLabelCategoryMapping() {
        log.debug("获取标签分类映射");
        Map<String, Object> categoryMapping = new LinkedHashMap<>();
        
        for (LabelCategory category : LabelCategory.values()) {
            Map<String, Object> categoryInfo = new HashMap<>();
            categoryInfo.put("code", category.getCode());
            categoryInfo.put("name", category.getName());
            categoryInfo.put("description", category.getDescription());
            categoryMapping.put(category.name(), categoryInfo);
        }
        
        return categoryMapping;
    }

    @Override
    @Cacheable(value = "labelMetadata", key = "'allLabelCategoryList'")
    public List<Label> getAllLabelCategoryList() {
        log.debug("获取所有标签分类列表（包括规则标签）");
        return labelService.getAllLabelCategoryList();
    }

    @Override
    @Cacheable(value = "labelMetadata", key = "'baseLabelCategoryList'")
    public List<Label> getBaseLabelCategoryList() {
        log.debug("获取基础标签分类列表");
        return labelService.getBaseLabelCategoryList();
    }

    // ==================== 标签目标类型相关实现 ====================

    @Override
    @Cacheable(value = "labelMetadata", key = "'labelTargetTypes'")
    public List<LabelTargetType> getAllLabelTargetTypes() {
        log.debug("获取所有标签目标类型");
        return Arrays.asList(LabelTargetType.values());
    }

    @Override
    @Cacheable(value = "labelMetadata", key = "'labelTargetTypeMapping'")
    public Map<Integer, String> getLabelTargetTypeMapping() {
        log.debug("获取标签目标类型映射");
        Map<Integer, String> targetTypeMapping = new LinkedHashMap<>();
        
        for (LabelTargetType targetType : LabelTargetType.values()) {
            targetTypeMapping.put(targetType.getCode(), targetType.getName());
        }
        
        return targetTypeMapping;
    }

    @Override
    public LabelTargetType getLabelTargetTypeByCode(Integer code) {
        log.debug("根据代码获取标签目标类型: {}", code);
        return LabelTargetType.fromCode(code);
    }

    // ==================== 标签来源相关实现 ====================

    @Override
    @Cacheable(value = "labelMetadata", key = "'labelSources'")
    public List<LabelSource> getAllLabelSources() {
        log.debug("获取所有标签来源");
        return Arrays.asList(LabelSource.values());
    }

    @Override
    public LabelSource getLabelSourceByValue(String value) {
        log.debug("根据值获取标签来源: {}", value);
        return LabelSource.fromValue(value);
    }

    // ==================== 分析标签相关实现 ====================

    @Override
    @Cacheable(value = "labelMetadata", key = "'analysisSignMapping'")
    public Map<String, Object> getAnalysisSignMapping() {
        log.debug("获取分析标签映射");
        Map<String, Object> analysisSignMapping = new LinkedHashMap<>();
        
        // 从现有的标签服务获取分析标签
        // 这里可以根据实际需求实现具体的分析标签逻辑
        
        return analysisSignMapping;
    }

    @Override
    @Cacheable(value = "labelMetadata", key = "'certificateSignMapping'")
    public Map<String, Object> getCertificateSignMapping() {
        log.debug("获取证书标签映射");
        Map<String, Object> certSignMapping = new LinkedHashMap<>();
        
        // 从现有的标签服务获取证书标签
        // 这里可以根据实际需求实现具体的证书标签逻辑
        
        return certSignMapping;
    }

    // ==================== 标签边关系相关实现 ====================

    @Override
    @Cacheable(value = "labelMetadata", key = "'tagEdgeRelationMapping'")
    public Map<String, Object> getTagEdgeRelationMapping() {
        log.debug("获取标签边关系映射");
        Map<String, Object> tagEdgeMapping = new LinkedHashMap<>();
        
        // 这里可以根据实际需求实现标签边关系逻辑
        // 暂时返回空映射
        
        return tagEdgeMapping;
    }

    // ==================== 标签查询相关实现 ====================

    @Override
    public List<com.geeksec.metadata.model.entity.Label> getLabelsByCategory(LabelCategory category) {
        log.debug("根据分类获取标签列表: {}", category);
        return labelService.getLabelsByCategory(category);
    }

    @Override
    public List<com.geeksec.metadata.model.entity.Label> getLabelsByTargetType(LabelTargetType targetType) {
        log.debug("根据目标类型获取标签列表: {}", targetType);
        return labelService.getLabelsByTargetType(targetType);
    }

    @Override
    public List<com.geeksec.metadata.model.entity.Label> getLabelsBySource(LabelSource source) {
        log.debug("根据来源获取标签列表: {}", source);
        return labelService.getLabelsBySource(source);
    }

    @Override
    public List<com.geeksec.metadata.model.entity.Label> searchLabels(String keyword) {
        log.debug("搜索标签: {}", keyword);
        return labelService.searchLabels(keyword);
    }

    // ==================== 缓存管理实现 ====================

    @Override
    @CacheEvict(value = "labelMetadata", allEntries = true)
    public void initLabelCache() {
        log.info("初始化标签缓存");
        // 预热缓存
        getAllLabelCategories();
        getLabelCategoryMapping();
        getAllLabelCategoryList();
        getBaseLabelCategoryList();
        getAllLabelTargetTypes();
        getLabelTargetTypeMapping();
        getAllLabelSources();
        getAnalysisSignMapping();
        getCertificateSignMapping();
        getTagEdgeRelationMapping();
    }

    @Override
    @CacheEvict(value = "labelMetadata", allEntries = true)
    public void clearLabelCache() {
        log.info("清除标签缓存");
    }

    @Override
    public void refreshLabelCache() {
        log.info("刷新标签缓存");
        clearLabelCache();
        initLabelCache();
    }
}

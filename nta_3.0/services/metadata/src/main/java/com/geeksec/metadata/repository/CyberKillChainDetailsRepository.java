package com.geeksec.metadata.repository;

import com.geeksec.metadata.model.entity.CyberKillChainDetails;
import com.geeksec.metadata.model.enums.CyberKillChain;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Cyber Kill Chain 详情 Repository 接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface CyberKillChainDetailsRepository extends BaseMapper<CyberKillChainDetails> {

    /**
     * 根据 Cyber Kill Chain 阶段查询详情
     */
    CyberKillChainDetails findByCyberKillChain(@Param("cyberKillChain") CyberKillChain cyberKillChain);

    /**
     * 根据严重程度等级查询
     */
    List<CyberKillChainDetails> findBySeverityLevel(@Param("severityLevel") Integer severityLevel);

    /**
     * 根据严重程度等级范围查询
     */
    List<CyberKillChainDetails> findBySeverityLevelRange(@Param("minLevel") Integer minLevel, @Param("maxLevel") Integer maxLevel);

    /**
     * 根据中文名称查询
     */
    CyberKillChainDetails findByChineseName(@Param("chineseName") String chineseName);

    /**
     * 根据英文名称查询
     */
    CyberKillChainDetails findByEnglishName(@Param("englishName") String englishName);

    /**
     * 查询高危 Cyber Kill Chain 阶段（严重程度>=7）
     */
    List<CyberKillChainDetails> findHighRiskStages();

    /**
     * 查询中危 Cyber Kill Chain 阶段（严重程度4-6）
     */
    List<CyberKillChainDetails> findMediumRiskStages();

    /**
     * 查询低危 Cyber Kill Chain 阶段（严重程度1-3）
     */
    List<CyberKillChainDetails> findLowRiskStages();

    /**
     * 获取所有 Cyber Kill Chain 阶段按严重程度排序
     */
    List<CyberKillChainDetails> findAllOrderBySeverityLevel();
}

package com.geeksec.metadata.controller;

import com.geeksec.metadata.service.CacheManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 缓存管理控制器
 * 提供缓存的清理、刷新和管理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/metadata/cache")
@RequiredArgsConstructor
@Tag(name = "缓存管理", description = "提供缓存的清理、刷新和管理功能")
public class CacheManagementController {

    private final CacheManagementService cacheManagementService;

    // ==================== 缓存清理接口 ====================

    @DeleteMapping("/system-dict")
    @Operation(summary = "清理系统字典缓存", description = "清理所有系统字典相关的缓存")
    public ResponseEntity<String> clearSystemDictCache() {
        log.info("清理系统字典缓存");
        cacheManagementService.clearSystemDictCache();
        return ResponseEntity.ok("系统字典缓存已清理");
    }

    @DeleteMapping("/system-dict/{dictType}")
    @Operation(summary = "清理指定字典缓存", description = "清理指定类型的字典缓存")
    public ResponseEntity<String> clearDictCache(
            @Parameter(description = "字典类型代码", example = "cert_type")
            @PathVariable String dictType) {
        log.info("清理字典缓存，类型: {}", dictType);
        cacheManagementService.clearDictCache(dictType);
        return ResponseEntity.ok("字典缓存已清理: " + dictType);
    }

    @DeleteMapping("/labels")
    @Operation(summary = "清理所有标签缓存", description = "清理所有类型的标签缓存")
    public ResponseEntity<String> clearAllLabelCache() {
        log.info("清理所有标签缓存");
        cacheManagementService.clearAllLabelCache();
        return ResponseEntity.ok("所有标签缓存已清理");
    }

    @DeleteMapping("/labels/{labelType}")
    @Operation(summary = "清理指定标签缓存", description = "清理指定类型的标签缓存")
    public ResponseEntity<String> clearLabelCache(
            @Parameter(description = "标签类型", example = "ip")
            @PathVariable String labelType) {
        log.info("清理标签缓存，类型: {}", labelType);
        cacheManagementService.clearLabelCache(labelType);
        return ResponseEntity.ok("标签缓存已清理: " + labelType);
    }

    @DeleteMapping("/protocols")
    @Operation(summary = "清理所有协议缓存", description = "清理所有协议相关的缓存")
    public ResponseEntity<String> clearAllProtocolCache() {
        log.info("清理所有协议缓存");
        cacheManagementService.clearAllProtocolCache();
        return ResponseEntity.ok("所有协议缓存已清理");
    }

    @DeleteMapping("/app-protocols")
    @Operation(summary = "清理应用协议缓存", description = "清理应用协议信息缓存")
    public ResponseEntity<String> clearAppProtocolCache() {
        log.info("清理应用协议缓存");
        cacheManagementService.clearAppProtocolCache();
        return ResponseEntity.ok("应用协议缓存已清理");
    }

    @DeleteMapping("/tag-categories")
    @Operation(summary = "清理标签分类缓存", description = "清理标签分类相关的缓存")
    public ResponseEntity<String> clearTagCategoryCache() {
        log.info("清理标签分类缓存");
        cacheManagementService.clearTagCategoryCache();
        return ResponseEntity.ok("标签分类缓存已清理");
    }

    @DeleteMapping("/attack-stages")
    @Operation(summary = "清理攻击阶段缓存", description = "清理攻击阶段相关的缓存")
    public ResponseEntity<String> clearAttackStageCache() {
        log.info("清理攻击阶段缓存");
        cacheManagementService.clearAttackStageCache();
        return ResponseEntity.ok("攻击阶段缓存已清理");
    }

    @DeleteMapping("/all")
    @Operation(summary = "清理所有缓存", description = "清理系统中所有的缓存")
    public ResponseEntity<String> clearAllCache() {
        log.info("清理所有缓存");
        cacheManagementService.clearAllCache();
        return ResponseEntity.ok("所有缓存已清理");
    }

    // ==================== 缓存预热接口 ====================

    @PostMapping("/warmup/system-dict")
    @Operation(summary = "预热系统字典缓存", description = "预热系统字典相关的缓存")
    public ResponseEntity<String> warmupSystemDictCache() {
        log.info("预热系统字典缓存");
        cacheManagementService.warmupSystemDictCache();
        return ResponseEntity.ok("系统字典缓存预热完成");
    }

    @PostMapping("/warmup/labels")
    @Operation(summary = "预热标签缓存", description = "预热所有标签相关的缓存")
    public ResponseEntity<String> warmupLabelCache() {
        log.info("预热标签缓存");
        cacheManagementService.warmupLabelCache();
        return ResponseEntity.ok("标签缓存预热完成");
    }

    @PostMapping("/warmup/protocols")
    @Operation(summary = "预热协议缓存", description = "预热所有协议相关的缓存")
    public ResponseEntity<String> warmupProtocolCache() {
        log.info("预热协议缓存");
        cacheManagementService.warmupProtocolCache();
        return ResponseEntity.ok("协议缓存预热完成");
    }

    @PostMapping("/warmup/all")
    @Operation(summary = "预热所有缓存", description = "预热系统中所有的缓存")
    public ResponseEntity<String> warmupAllCache() {
        log.info("预热所有缓存");
        cacheManagementService.warmupAllCache();
        return ResponseEntity.ok("所有缓存预热完成");
    }

    // ==================== 缓存统计接口 ====================

    @GetMapping("/statistics")
    @Operation(summary = "获取缓存统计信息", description = "获取当前缓存的统计信息")
    public ResponseEntity<String> getCacheStatistics() {
        log.info("获取缓存统计信息");
        String statistics = cacheManagementService.getCacheStatistics();
        return ResponseEntity.ok(statistics);
    }
}

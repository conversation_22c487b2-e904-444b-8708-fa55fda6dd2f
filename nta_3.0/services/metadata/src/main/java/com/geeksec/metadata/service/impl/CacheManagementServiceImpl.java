package com.geeksec.metadata.service.impl;

import com.geeksec.metadata.service.CacheManagementService;
import com.geeksec.metadata.service.LabelMetadataService;
import com.geeksec.metadata.service.LabelService;
import com.geeksec.metadata.service.ProtocolMetadataService;
import com.geeksec.metadata.service.ProtocolService;
import com.geeksec.metadata.service.QueryFieldMetadataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * 缓存管理服务实现类
 * 提供缓存的清理、刷新和管理功能
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheManagementServiceImpl implements CacheManagementService {

    private final CacheManager cacheManager;
    private final LabelMetadataService labelMetadataService;
    private final LabelService labelService;
    private final ProtocolMetadataService protocolMetadataService;
    private final ProtocolService protocolService;
    private final QueryFieldMetadataService queryFieldMetadataService;

    @Override
    @CacheEvict(value = {"protocolMetadata", "labelMetadata", "queryFieldMetadata"}, allEntries = true)
    public void clearSystemDictCache() {
        log.info("清理系统元数据缓存");
        protocolMetadataService.clearProtocolCache();
        labelMetadataService.clearLabelCache();
    }

    @Override
    public void clearDictCache(String dictType) {
        log.info("清理字典缓存，类型: {}", dictType);
        Cache cache = cacheManager.getCache("systemDict");
        if (cache != null) {
            cache.evict(dictType);
        }
    }

    @Override
    public void clearAllLabelCache() {
        log.info("清理所有标签缓存");
        String[] labelCaches = {"ipLabels", "applicationLabels", "domainLabels", 
                               "certificateLabels", "sessionLabels", "fingerprintLabels"};
        
        for (String cacheName : labelCaches) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
        }
        
        // 同时清理标签分类缓存
        clearTagCategoryCache();
    }

    @Override
    public void clearLabelCache(String labelType) {
        log.info("清理标签缓存，类型: {}", labelType);
        String cacheName = labelType + "Labels";
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        }
    }

    @Override
    public void clearAllProtocolCache() {
        log.info("清理所有协议缓存");
        String[] protocolCaches = {"applicationProtocols", "singleProtocols", "ipProtocols", 
                                  "protocolTypes", "protocolNames"};
        
        for (String cacheName : protocolCaches) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
        }
        
        // 同时清理应用协议信息缓存
        clearAppProtocolCache();
    }

    @Override
    @CacheEvict(value = "appProtocolInfo", allEntries = true)
    public void clearAppProtocolCache() {
        log.info("清理应用协议信息缓存");
    }

    @Override
    @CacheEvict(value = "labelCategory", allEntries = true)
    public void clearTagCategoryCache() {
        log.info("清理标签分类缓存");
    }

    @Override
    @CacheEvict(value = "attackStages", allEntries = true)
    public void clearAttackStageCache() {
        log.info("清理攻击阶段缓存");
    }

    @Override
    public void clearAllCache() {
        log.info("清理所有缓存");
        Collection<String> cacheNames = cacheManager.getCacheNames();
        for (String cacheName : cacheNames) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
        }
    }

    @Override
    public void warmupSystemDictCache() {
        log.info("预热系统元数据缓存");
        try {
            // 预热协议元数据缓存
            protocolMetadataService.initProtocolCache();

            // 预热标签元数据缓存
            labelMetadataService.initLabelCache();

            log.info("系统元数据缓存预热完成");
        } catch (Exception e) {
            log.error("系统元数据缓存预热失败", e);
        }
    }

    @Override
    public void warmupLabelCache() {
        log.info("预热标签缓存");
        try {
            // 预热标签元数据缓存
            labelMetadataService.initLabelCache();

            // 预热各类型标签数据
            labelService.getAllIpLabels();
            labelService.getAllDomainLabels();
            labelService.getAllCertificateLabels();
            labelService.getAllApplicationLabels();
            labelService.getAllSessionLabels();
            labelService.getAllFingerprintLabels();
            
            log.info("标签缓存预热完成");
        } catch (Exception e) {
            log.error("标签缓存预热失败", e);
        }
    }

    @Override
    public void warmupProtocolCache() {
        log.info("预热协议缓存");
        try {
            // 预热协议元数据缓存
            protocolMetadataService.initProtocolCache();

            // 预热各类型协议数据
            protocolService.getAllApplicationProtocols();
            protocolService.getAllInternetProtocols();

            log.info("协议缓存预热完成");
        } catch (Exception e) {
            log.error("协议缓存预热失败", e);
        }
    }

    @Override
    public void warmupAllCache() {
        log.info("预热所有缓存");
        warmupSystemDictCache();
        warmupLabelCache();
        warmupProtocolCache();
        log.info("所有缓存预热完成");
    }

    @Override
    public String getCacheStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("缓存统计信息:\n");
        
        Collection<String> cacheNames = cacheManager.getCacheNames();
        for (String cacheName : cacheNames) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                stats.append(String.format("- %s: %s\n", cacheName, cache.getNativeCache().getClass().getSimpleName()));
            }
        }
        
        return stats.toString();
    }
}

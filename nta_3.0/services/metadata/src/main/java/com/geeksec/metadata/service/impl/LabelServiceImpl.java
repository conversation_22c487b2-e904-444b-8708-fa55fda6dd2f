package com.geeksec.metadata.service.impl;

import com.geeksec.metadata.model.entity.*;
import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.geeksec.metadata.repository.*;
import com.geeksec.metadata.service.LabelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 标签服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LabelServiceImpl implements LabelService {

    /**
     * IP标签Repository
     */
    private final IpLabelRepository ipLabelRepository;
    

    
    /**
     * 应用标签Repository
     */
    private final ApplicationLabelRepository applicationLabelRepository;
    
    /**
     * 域名标签Repository
     */
    private final DomainLabelRepository domainLabelRepository;
    
    /**
     * 证书标签Repository
     */
    private final CertificateLabelRepository certificateLabelRepository;
    

    
    /**
     * 会话标签Repository
     */
    private final SessionLabelRepository sessionLabelRepository;
    
    /**
     * 指纹标签Repository
     */
    private final FingerprintLabelRepository fingerprintLabelRepository;

    /**
     * 标签缓存 - 按对象类型分别缓存
     */
    private final Map<String, Map<Integer, BaseLabel>> labelCacheByType = new ConcurrentHashMap<>();

    // ==================== IP标签相关实现 ====================

    @Override
    @Cacheable(value = "ipLabels", key = "'all'")
    public List<IpLabel> getAllIpLabels() {
        log.debug("获取所有IP标签");
        return ipLabelRepository.findAllActive();
    }

    @Override
    @Cacheable(value = "ipLabels", key = "'cyberKillChain:' + #cyberKillChain")
    public List<IpLabel> getIpLabelsByCyberKillChain(CyberKillChain cyberKillChain) {
        log.debug("根据Cyber Kill Chain阶段获取IP标签: {}", cyberKillChain);
        return ipLabelRepository.findByCyberKillChain(cyberKillChain);
    }

    @Override
    @Cacheable(value = "ipLabels", key = "'category:' + #category")
    public List<IpLabel> getIpLabelsByCategory(LabelCategory category) {
        log.debug("根据类别获取IP标签: {}", category);
        return ipLabelRepository.findByCategory(category);
    }

    @Override
    @Cacheable(value = "ipLabels", key = "'source:' + #source")
    public List<IpLabel> getIpLabelsBySource(LabelSource source) {
        log.debug("根据来源获取IP标签: {}", source);
        return ipLabelRepository.findBySource(source);
    }

    @Override
    public List<IpLabel> searchIpLabelsByName(String name) {
        log.debug("根据名称搜索IP标签: {}", name);
        return ipLabelRepository.findByNameContaining(name);
    }

    @Override
    public List<IpLabel> getIpLabelsByThreatScoreRange(Integer minScore, Integer maxScore) {
        log.debug("根据威胁评分范围获取IP标签: {} - {}", minScore, maxScore);
        return ipLabelRepository.findByThreatScoreRange(minScore, maxScore);
    }



    // ==================== 应用标签相关实现 ====================

    @Override
    @Cacheable(value = "applicationLabels", key = "'all'")
    public List<ApplicationLabel> getAllApplicationLabels() {
        log.debug("获取所有应用标签");
        return applicationLabelRepository.findAllActive();
    }

    @Override
    @Cacheable(value = "applicationLabels", key = "'cyberKillChain:' + #cyberKillChain")
    public List<ApplicationLabel> getApplicationLabelsByCyberKillChain(CyberKillChain cyberKillChain) {
        log.debug("根据Cyber Kill Chain阶段获取应用标签: {}", cyberKillChain);
        return applicationLabelRepository.findByCyberKillChain(cyberKillChain);
    }

    @Override
    @Cacheable(value = "applicationLabels", key = "'category:' + #category")
    public List<ApplicationLabel> getApplicationLabelsByCategory(LabelCategory category) {
        log.debug("根据类别获取应用标签: {}", category);
        return applicationLabelRepository.findByCategory(category);
    }

    // ==================== 域名标签相关实现 ====================

    @Override
    @Cacheable(value = "domainLabels", key = "'all'")
    public List<DomainLabel> getAllDomainLabels() {
        log.debug("获取所有域名标签");
        return domainLabelRepository.findAllActive();
    }

    @Override
    @Cacheable(value = "domainLabels", key = "'cyberKillChain:' + #cyberKillChain")
    public List<DomainLabel> getDomainLabelsByCyberKillChain(CyberKillChain cyberKillChain) {
        log.debug("根据Cyber Kill Chain阶段获取域名标签: {}", cyberKillChain);
        return domainLabelRepository.findByCyberKillChain(cyberKillChain);
    }

    @Override
    @Cacheable(value = "domainLabels", key = "'category:' + #category")
    public List<DomainLabel> getDomainLabelsByCategory(LabelCategory category) {
        log.debug("根据类别获取域名标签: {}", category);
        return domainLabelRepository.findByCategory(category);
    }

    // ==================== 证书标签相关实现 ====================

    @Override
    @Cacheable(value = "certificateLabels", key = "'all'")
    public List<CertificateLabel> getAllCertificateLabels() {
        log.debug("获取所有证书标签");
        return certificateLabelRepository.findAllActive();
    }

    @Override
    @Cacheable(value = "certificateLabels", key = "'cyberKillChain:' + #cyberKillChain")
    public List<CertificateLabel> getCertificateLabelsByCyberKillChain(CyberKillChain cyberKillChain) {
        log.debug("根据Cyber Kill Chain阶段获取证书标签: {}", cyberKillChain);
        return certificateLabelRepository.findByCyberKillChain(cyberKillChain);
    }

    @Override
    @Cacheable(value = "certificateLabels", key = "'category:' + #category")
    public List<CertificateLabel> getCertificateLabelsByCategory(LabelCategory category) {
        log.debug("根据类别获取证书标签: {}", category);
        return certificateLabelRepository.findByCategory(category);
    }



    // ==================== 会话标签相关实现 ====================

    @Override
    @Cacheable(value = "sessionLabels", key = "'all'")
    public List<SessionLabel> getAllSessionLabels() {
        log.debug("获取所有会话标签");
        return sessionLabelRepository.findAllActive();
    }

    @Override
    @Cacheable(value = "sessionLabels", key = "'cyberKillChain:' + #cyberKillChain")
    public List<SessionLabel> getSessionLabelsByCyberKillChain(CyberKillChain cyberKillChain) {
        log.debug("根据Cyber Kill Chain阶段获取会话标签: {}", cyberKillChain);
        return sessionLabelRepository.findByCyberKillChain(cyberKillChain);
    }

    // ==================== 指纹标签相关实现 ====================

    @Override
    @Cacheable(value = "fingerprintLabels", key = "'all'")
    public List<FingerprintLabel> getAllFingerprintLabels() {
        log.debug("获取所有指纹标签");
        return fingerprintLabelRepository.findAllActive();
    }

    @Override
    @Cacheable(value = "fingerprintLabels", key = "'cyberKillChain:' + #cyberKillChain")
    public List<FingerprintLabel> getFingerprintLabelsByCyberKillChain(CyberKillChain cyberKillChain) {
        log.debug("根据Cyber Kill Chain阶段获取指纹标签: {}", cyberKillChain);
        return fingerprintLabelRepository.findByCyberKillChain(cyberKillChain);
    }

    // ==================== 通用标签接口实现 ====================

    @Override
    public BaseLabel getLabelByIdAndType(Integer labelId, String labelType) {
        log.debug("根据标签ID和类型获取标签: {} - {}", labelId, labelType);
        
        Map<Integer, BaseLabel> typeCache = labelCacheByType.get(labelType);
        if (typeCache != null) {
            BaseLabel label = typeCache.get(labelId);
            if (label != null) {
                return label;
            }
        }
        
        // 从数据库查询
        BaseLabel label = queryLabelFromDatabase(labelId, labelType);
        if (label != null) {
            // 更新缓存
            labelCacheByType.computeIfAbsent(labelType, k -> new ConcurrentHashMap<>()).put(labelId, label);
        }
        
        return label;
    }

    @Override
    public Map<String, Long> getLabelCountByCyberKillChain(CyberKillChain cyberKillChain) {
        log.debug("根据Cyber Kill Chain阶段获取所有类型的标签统计: {}", cyberKillChain);

        Map<String, Long> countMap = new HashMap<>(16);
        countMap.put("ip", (long) ipLabelRepository.findByCyberKillChain(cyberKillChain).size());
        countMap.put("application", (long) applicationLabelRepository.findByCyberKillChain(cyberKillChain).size());
        countMap.put("domain", (long) domainLabelRepository.findByCyberKillChain(cyberKillChain).size());
        countMap.put("certificate", (long) certificateLabelRepository.findByCyberKillChain(cyberKillChain).size());
        countMap.put("session", (long) sessionLabelRepository.findByCyberKillChain(cyberKillChain).size());
        countMap.put("fingerprint", (long) fingerprintLabelRepository.findByCyberKillChain(cyberKillChain).size());

        return countMap;
    }

    @Override
    public Map<String, Long> getLabelCountByCategory(LabelCategory category) {
        log.debug("根据类别获取所有类型的标签统计: {}", category);
        
        Map<String, Long> countMap = new HashMap<>(16);
        countMap.put("ip", (long) ipLabelRepository.findByCategory(category).size());
        countMap.put("application", (long) applicationLabelRepository.findByCategory(category).size());
        countMap.put("domain", (long) domainLabelRepository.findByCategory(category).size());
        countMap.put("certificate", (long) certificateLabelRepository.findByCategory(category).size());
        countMap.put("session", (long) sessionLabelRepository.findByCategory(category).size());
        countMap.put("fingerprint", (long) fingerprintLabelRepository.findByCategory(category).size());
        
        return countMap;
    }

    @Override
    public void initLabelCache() {
        log.info("初始化标签缓存");
        
        labelCacheByType.clear();
        
        // 初始化各类型标签缓存
        initTypeLabelCache("ip", ipLabelRepository.findAllActive());
        initTypeLabelCache("application", applicationLabelRepository.findAllActive());
        initTypeLabelCache("domain", domainLabelRepository.findAllActive());
        initTypeLabelCache("certificate", certificateLabelRepository.findAllActive());
        initTypeLabelCache("session", sessionLabelRepository.findAllActive());
        initTypeLabelCache("fingerprint", fingerprintLabelRepository.findAllActive());
        
        log.info("标签缓存初始化完成");
    }

    @Override
    public void clearLabelCache() {
        log.info("清除标签缓存");
        labelCacheByType.clear();
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 初始化指定类型的标签缓存
     */
    private void initTypeLabelCache(String labelType, List<? extends BaseLabel> labels) {
        Map<Integer, BaseLabel> typeCache = new ConcurrentHashMap<>();
        labels.forEach(label -> typeCache.put(label.getId(), label));
        labelCacheByType.put(labelType, typeCache);
        log.debug("初始化{}标签缓存，共{}个", labelType, labels.size());
    }

    /**
     * 从数据库查询标签
     */
    private BaseLabel queryLabelFromDatabase(Integer labelId, String labelType) {
        return switch (labelType.toLowerCase()) {
            case "ip" -> ipLabelRepository.selectOneById(labelId);
            case "application" -> applicationLabelRepository.selectOneById(labelId);
            case "domain" -> domainLabelRepository.selectOneById(labelId);
            case "certificate" -> certificateLabelRepository.selectOneById(labelId);
            case "session" -> sessionLabelRepository.selectOneById(labelId);
            case "fingerprint" -> fingerprintLabelRepository.selectOneById(labelId);
            default -> null;
        };
    }
}

package com.geeksec.metadata.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 网络协议数据传输对象
 * 对应 NTA 2.0 中的 AppProValue 实体，现已更名为网络协议
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "网络协议")
public class NetworkProtocolDto {
    
    /**
     * 协议ID
     */
    @Schema(description = "协议ID", example = "10001")
    private Integer protocolId;
    
    /**
     * 协议值（协议名称1）
     */
    @Schema(description = "协议值", example = "APP_FTP")
    private String protocolValue;
    
    /**
     * 协议名称（协议名称2）
     */
    @Schema(description = "协议名称", example = "文件传输协议")
    private String protocolName;
    
    /**
     * 协议类型
     */
    @Schema(description = "协议类型", example = "文件共享")
    private String protocolType;
    
    /**
     * 协议说明
     */
    @Schema(description = "协议说明", example = "RFC 959")
    private String protocolExplanation;
    
    /**
     * 类型
     * 1-连接, 2-单包, 3-tcp/udp负载
     */
    @Schema(description = "类型", example = "1")
    private Integer type;
    
    /**
     * 类型描述
     */
    @Schema(description = "类型描述", example = "连接")
    private String typeDescription;
    
    /**
     * 获取类型描述
     * 
     * @return 类型描述
     */
    public String getTypeDescription() {
        if (type == null) {
            return "未知";
        }
        switch (type) {
            case 1:
                return "连接";
            case 2:
                return "单包";
            case 3:
                return "tcp/udp负载";
            default:
                return "未知";
        }
    }
}

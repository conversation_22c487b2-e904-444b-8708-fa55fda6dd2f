package com.geeksec.metadata.repository;

import com.geeksec.metadata.model.entity.Label;
import com.geeksec.metadata.model.enums.LabelTargetType;
import com.geeksec.metadata.model.enums.LabelCategory;
import com.geeksec.metadata.model.enums.LabelSource;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统一标签Repository接口
 * 对应 labels 表
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface LabelRepository extends BaseMapper<Label> {

    /**
     * 根据目标类型查询标签
     * 
     * @param targetType 目标类型
     * @return 标签列表
     */
    default List<Label> selectByTargetType(LabelTargetType targetType) {
        return selectListByQuery(
            QueryWrapper.create()
                .eq("target_type", targetType)
                .eq("is_active", true)
                .orderBy("sort_order", true)
                .orderBy("id", true)
        );
    }

    /**
     * 根据目标类型和类别查询标签
     * 
     * @param targetType 目标类型
     * @param category 标签类别
     * @return 标签列表
     */
    default List<Label> selectByTargetTypeAndCategory(LabelTargetType targetType, LabelCategory category) {
        return selectListByQuery(
            QueryWrapper.create()
                .eq("target_type", targetType)
                .eq("category", category)
                .eq("is_active", true)
                .orderBy("sort_order", true)
                .orderBy("id", true)
        );
    }

    /**
     * 根据标签来源查询标签
     * 
     * @param source 标签来源
     * @return 标签列表
     */
    default List<Label> selectBySource(LabelSource source) {
        return selectListByQuery(
            QueryWrapper.create()
                .eq("source", source)
                .eq("is_active", true)
                .orderBy("sort_order", true)
                .orderBy("id", true)
        );
    }

    /**
     * 根据名称和目标类型查询标签（用于唯一性检查）
     * 
     * @param name 标签名称
     * @param targetType 目标类型
     * @return 标签对象
     */
    default Label selectByNameAndTargetType(String name, LabelTargetType targetType) {
        return selectOneByQuery(
            QueryWrapper.create()
                .eq("name", name)
                .eq("target_type", targetType)
        );
    }

    /**
     * 查询所有激活的标签
     * 
     * @return 标签列表
     */
    default List<Label> selectAllActive() {
        return selectListByQuery(
            QueryWrapper.create()
                .eq("is_active", true)
                .orderBy("target_type", true)
                .orderBy("sort_order", true)
                .orderBy("id", true)
        );
    }

    /**
     * 根据类别统计标签数量
     * 
     * @param category 标签类别
     * @return 标签数量
     */
    default Long countByCategory(LabelCategory category) {
        return selectCountByQuery(
            QueryWrapper.create()
                .eq("category", category)
                .eq("is_active", true)
        );
    }

    /**
     * 根据目标类型统计标签数量
     * 
     * @param targetType 目标类型
     * @return 标签数量
     */
    default Long countByTargetType(LabelTargetType targetType) {
        return selectCountByQuery(
            QueryWrapper.create()
                .eq("target_type", targetType)
                .eq("is_active", true)
        );
    }

    /**
     * 批量更新标签状态
     *
     * @param ids 标签ID列表
     * @param isActive 是否激活
     * @return 更新数量
     */
    default int updateStatusBatch(@Param("ids") List<Integer> ids, @Param("isActive") Boolean isActive) {
        Label updateLabel = new Label();
        updateLabel.setActive(isActive);
        return updateByQuery(
            updateLabel,
            QueryWrapper.create().in("id", ids)
        );
    }
}

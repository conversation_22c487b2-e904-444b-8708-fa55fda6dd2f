package com.geeksec.metadata.service.impl;

import com.geeksec.metadata.model.entity.CyberKillChainDetails;
import com.geeksec.metadata.model.enums.CyberKillChain;
import com.geeksec.metadata.repository.CyberKillChainDetailsRepository;
import com.geeksec.metadata.service.CyberKillChainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Cyber Kill Chain 服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CyberKillChainServiceImpl implements CyberKillChainService {

    /**
     * Cyber Kill Chain 详情 Repository
     */
    private final CyberKillChainDetailsRepository cyberKillChainDetailsRepository;

    /**
     * Cyber Kill Chain 缓存
     */
    private final Map<CyberKillChain, CyberKillChainDetails> cyberKillChainCache = new ConcurrentHashMap<>();

    @Override
    @Cacheable(value = "cyberKillChains", key = "'all'")
    public List<CyberKillChainDetails> getAllCyberKillChainDetails() {
        log.debug("获取所有 Cyber Kill Chain 阶段详情");
        return cyberKillChainDetailsRepository.selectAll();
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "#cyberKillChain")
    public CyberKillChainDetails getCyberKillChainDetails(CyberKillChain cyberKillChain) {
        log.debug("根据 Cyber Kill Chain 阶段获取详情: {}", cyberKillChain);
        
        // 先从缓存获取
        CyberKillChainDetails cached = cyberKillChainCache.get(cyberKillChain);
        if (cached != null) {
            return cached;
        }
        
        // 从数据库获取
        CyberKillChainDetails details = cyberKillChainDetailsRepository.findByCyberKillChain(cyberKillChain);
        if (details != null) {
            cyberKillChainCache.put(cyberKillChain, details);
        }
        
        return details;
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'severity_' + #severityLevel")
    public List<CyberKillChainDetails> getCyberKillChainsBySeverityLevel(Integer severityLevel) {
        log.debug("根据严重程度等级获取 Cyber Kill Chain 阶段: {}", severityLevel);
        return cyberKillChainDetailsRepository.findBySeverityLevel(severityLevel);
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'severity_range_' + #minLevel + '_' + #maxLevel")
    public List<CyberKillChainDetails> getCyberKillChainsBySeverityLevelRange(Integer minLevel, Integer maxLevel) {
        log.debug("根据严重程度等级范围获取 Cyber Kill Chain 阶段: {} - {}", minLevel, maxLevel);
        return cyberKillChainDetailsRepository.findBySeverityLevelRange(minLevel, maxLevel);
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'chinese_' + #chineseName")
    public CyberKillChainDetails getCyberKillChainByChineseName(String chineseName) {
        log.debug("根据中文名称获取 Cyber Kill Chain 阶段详情: {}", chineseName);
        return cyberKillChainDetailsRepository.findByChineseName(chineseName);
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'english_' + #englishName")
    public CyberKillChainDetails getCyberKillChainByEnglishName(String englishName) {
        log.debug("根据英文名称获取 Cyber Kill Chain 阶段详情: {}", englishName);
        return cyberKillChainDetailsRepository.findByEnglishName(englishName);
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'high_risk'")
    public List<CyberKillChainDetails> getHighRiskCyberKillChains() {
        log.debug("获取高危 Cyber Kill Chain 阶段");
        return cyberKillChainDetailsRepository.findHighRiskStages();
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'medium_risk'")
    public List<CyberKillChainDetails> getMediumRiskCyberKillChains() {
        log.debug("获取中危 Cyber Kill Chain 阶段");
        return cyberKillChainDetailsRepository.findMediumRiskStages();
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'low_risk'")
    public List<CyberKillChainDetails> getLowRiskCyberKillChains() {
        log.debug("获取低危 Cyber Kill Chain 阶段");
        return cyberKillChainDetailsRepository.findLowRiskStages();
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'ordered_by_severity'")
    public List<CyberKillChainDetails> getAllCyberKillChainsOrderBySeverity() {
        log.debug("获取所有 Cyber Kill Chain 阶段按严重程度排序");
        return cyberKillChainDetailsRepository.findAllOrderBySeverityLevel();
    }

    @Override
    @Cacheable(value = "cyberKillChains", key = "'statistics'")
    public Map<String, Long> getCyberKillChainStatistics() {
        log.debug("获取 Cyber Kill Chain 阶段统计信息");
        
        List<CyberKillChainDetails> allDetails = getAllCyberKillChainDetails();
        Map<String, Long> statistics = new HashMap<>();
        
        // 按严重程度等级统计
        Map<Integer, Long> severityStats = allDetails.stream()
                .filter(detail -> detail.getSeverityLevel() != null)
                .collect(Collectors.groupingBy(
                        CyberKillChainDetails::getSeverityLevel,
                        Collectors.counting()
                ));
        
        // 转换为字符串键
        severityStats.forEach((level, count) -> 
                statistics.put("severity_level_" + level, count));
        
        // 风险等级统计
        long highRiskCount = allDetails.stream()
                .filter(detail -> detail.getSeverityLevel() != null && detail.getSeverityLevel() >= 7)
                .count();
        long mediumRiskCount = allDetails.stream()
                .filter(detail -> detail.getSeverityLevel() != null && 
                        detail.getSeverityLevel() >= 4 && detail.getSeverityLevel() <= 6)
                .count();
        long lowRiskCount = allDetails.stream()
                .filter(detail -> detail.getSeverityLevel() != null && detail.getSeverityLevel() <= 3)
                .count();
        
        statistics.put("high_risk_count", highRiskCount);
        statistics.put("medium_risk_count", mediumRiskCount);
        statistics.put("low_risk_count", lowRiskCount);
        statistics.put("total_count", (long) allDetails.size());
        
        return statistics;
    }

    @Override
    public List<String> getDefenseRecommendations(CyberKillChain cyberKillChain) {
        log.debug("获取 Cyber Kill Chain 阶段防护建议: {}", cyberKillChain);
        
        CyberKillChainDetails details = getCyberKillChainDetails(cyberKillChain);
        if (details != null && details.getDefenseRecommendations() != null) {
            return Arrays.asList(details.getDefenseRecommendations());
        }
        
        return Collections.emptyList();
    }

    @Override
    public List<String> getTypicalTechniques(CyberKillChain cyberKillChain) {
        log.debug("获取 Cyber Kill Chain 阶段典型技术: {}", cyberKillChain);
        
        CyberKillChainDetails details = getCyberKillChainDetails(cyberKillChain);
        if (details != null && details.getTypicalTechniques() != null) {
            return Arrays.asList(details.getTypicalTechniques());
        }
        
        return Collections.emptyList();
    }

    @Override
    public String getSeverityDescription(CyberKillChain cyberKillChain) {
        log.debug("获取 Cyber Kill Chain 阶段严重程度描述: {}", cyberKillChain);
        
        CyberKillChainDetails details = getCyberKillChainDetails(cyberKillChain);
        if (details != null && details.getSeverityLevel() != null) {
            int level = details.getSeverityLevel();
            if (level >= 7) {
                return "高危 (严重程度: " + level + "/10)";
            } else if (level >= 4) {
                return "中危 (严重程度: " + level + "/10)";
            } else {
                return "低危 (严重程度: " + level + "/10)";
            }
        }
        
        return "未知严重程度";
    }

    @Override
    public boolean isHighRisk(CyberKillChain cyberKillChain) {
        log.debug("判断 Cyber Kill Chain 阶段是否为高危: {}", cyberKillChain);
        
        CyberKillChainDetails details = getCyberKillChainDetails(cyberKillChain);
        return details != null && details.getSeverityLevel() != null && details.getSeverityLevel() >= 7;
    }

    @Override
    public String getUrgencyDescription(CyberKillChain cyberKillChain) {
        log.debug("获取 Cyber Kill Chain 阶段紧急程度描述: {}", cyberKillChain);
        
        CyberKillChainDetails details = getCyberKillChainDetails(cyberKillChain);
        if (details != null && details.getUrgencyDescription() != null) {
            return details.getUrgencyDescription();
        }
        
        return "无紧急程度描述";
    }

    @Override
    public void initCyberKillChainCache() {
        log.info("初始化 Cyber Kill Chain 缓存");
        
        List<CyberKillChainDetails> allDetails = cyberKillChainDetailsRepository.selectAll();
        cyberKillChainCache.clear();
        
        for (CyberKillChainDetails details : allDetails) {
            cyberKillChainCache.put(details.getCyberKillChain(), details);
        }
        
        log.info("Cyber Kill Chain 缓存初始化完成，共缓存 {} 个阶段", cyberKillChainCache.size());
    }

    @Override
    @CacheEvict(value = "cyberKillChains", allEntries = true)
    public void clearCyberKillChainCache() {
        log.info("清除 Cyber Kill Chain 缓存");
        cyberKillChainCache.clear();
        log.info("Cyber Kill Chain 缓存清除完成");
    }
}

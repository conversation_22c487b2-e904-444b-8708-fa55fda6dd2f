package com.geeksec.metadata.service;

import com.geeksec.metadata.model.dto.NetworkProtocolDto;
import com.geeksec.metadata.model.entity.NetworkProtocol;
import com.geeksec.metadata.model.entity.InternetProtocol;

import java.util.List;
import java.util.Map;

/**
 * 协议元数据服务接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface ProtocolMetadataService {
    
    // ==================== 协议类型相关 ====================
    
    /**
     * 获取所有协议类型
     * 
     * @return 协议类型映射
     */
    Map<String, Object> getAllProtocolTypes();
    
    /**
     * 获取协议元数据映射
     * 
     * @return 协议元数据映射
     */
    Map<String, Object> getProtocolMetadata();
    
    // ==================== 网络协议相关 ====================

    /**
     * 获取所有网络协议信息
     *
     * @return 网络协议信息列表
     */
    List<NetworkProtocolDto> getAllNetworkProtocols();

    /**
     * 根据协议ID获取网络协议
     *
     * @param protocolId 协议ID
     * @return 网络协议
     */
    NetworkProtocol getNetworkProtocolById(Integer protocolId);

    /**
     * 根据协议名称获取网络协议
     *
     * @param protocolName 协议名称
     * @return 网络协议列表
     */
    List<NetworkProtocol> getNetworkProtocolsByName(String protocolName);

    /**
     * 获取网络协议ID映射
     *
     * @return 网络协议ID映射
     */
    Map<String, Object> getNetworkProtocolIdMapping();
    
    // ==================== 应用ID和值映射 ====================
    
    /**
     * 获取应用ID映射
     * 
     * @return 应用ID映射
     */
    Map<String, Object> getApplicationIdMapping();
    
    /**
     * 获取应用值映射
     * 
     * @return 应用值映射
     */
    Map<String, Object> getApplicationValueMapping();
    
    /**
     * 获取应用类型映射
     * 
     * @return 应用类型映射
     */
    Map<String, Object> getApplicationTypeMapping();
    
    /**
     * 获取应用ID规则映射
     * 
     * @return 应用ID规则映射
     */
    Map<String, Object> getApplicationIdRuleMapping();
    
    /**
     * 获取应用值规则映射
     * 
     * @return 应用值规则映射
     */
    Map<String, Object> getApplicationValueRuleMapping();
    
    // ==================== 互联网协议相关 ====================
    
    /**
     * 获取所有互联网协议
     * 
     * @return 互联网协议列表
     */
    List<InternetProtocol> getAllInternetProtocols();
    
    /**
     * 根据协议号获取互联网协议
     * 
     * @param protocolNumber 协议号
     * @return 互联网协议
     */
    InternetProtocol getInternetProtocolByNumber(Integer protocolNumber);
    
    /**
     * 根据关键字搜索互联网协议
     * 
     * @param keyword 关键字
     * @return 互联网协议列表
     */
    List<InternetProtocol> searchInternetProtocolsByKeyword(String keyword);
    
    // ==================== 缓存管理 ====================
    
    /**
     * 初始化协议缓存
     */
    void initProtocolCache();
    
    /**
     * 清除协议缓存
     */
    void clearProtocolCache();
    
    /**
     * 刷新协议缓存
     */
    void refreshProtocolCache();
}

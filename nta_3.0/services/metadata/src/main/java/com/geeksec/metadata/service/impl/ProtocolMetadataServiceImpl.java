package com.geeksec.metadata.service.impl;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.geeksec.metadata.model.dto.NetworkProtocolDto;
import com.geeksec.metadata.model.entity.InternetProtocol;
import com.geeksec.metadata.model.entity.NetworkProtocol;
import com.geeksec.metadata.repository.InternetProtocolRepository;
import com.geeksec.metadata.repository.NetworkProtocolRepository;
import com.geeksec.metadata.service.ProtocolMetadataService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 协议元数据服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProtocolMetadataServiceImpl implements ProtocolMetadataService {

    private final NetworkProtocolRepository networkProtocolRepository;
    private final InternetProtocolRepository internetProtocolRepository;

    // ==================== 协议类型相关实现 ====================

    @Override
    @Cacheable(value = "protocolMetadata", key = "'protocolTypes'")
    public Map<String, Object> getAllProtocolTypes() {
        log.debug("获取所有协议类型");
        Map<String, Object> protocolTypes = new LinkedHashMap<>();
        
        // 从数据库获取互联网协议
        List<InternetProtocol> internetProtocols = internetProtocolRepository.selectAll();
        for (InternetProtocol protocol : internetProtocols) {
            protocolTypes.put(protocol.getProtocolNumber().toString(), protocol.getKeyword());
        }
        
        return protocolTypes;
    }

    @Override
    @Cacheable(value = "protocolMetadata", key = "'protocolMetadata'")
    public Map<String, Object> getProtocolMetadata() {
        log.debug("获取协议元数据映射");
        Map<String, Object> protocolMetadata = new LinkedHashMap<>();
        
        // 从数据库获取网络协议
        List<NetworkProtocol> networkProtocols = networkProtocolRepository.selectAll();
        for (NetworkProtocol protocol : networkProtocols) {
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("id", protocol.getId());
            metadata.put("protocolName", protocol.getProtocolName());
            metadata.put("displayName", protocol.getDisplayName());
            metadata.put("description", protocol.getDescription());
            metadata.put("protocolType", protocol.getProtocolType());
            metadata.put("category", protocol.getCategory());
            protocolMetadata.put(protocol.getProtocolName(), metadata);
        }
        
        return protocolMetadata;
    }

    // ==================== 应用协议相关实现 ====================

    @Override
    @Cacheable(value = "protocolMetadata", key = "'networkProtocols'")
    public List<NetworkProtocolDto> getAllNetworkProtocols() {
        log.debug("获取所有网络协议信息");
        List<NetworkProtocol> protocols = networkProtocolRepository.selectAll();

        return protocols.stream()
                .map(this::convertToNetworkProtocolDto)
                .collect(Collectors.toList());
    }

    @Override
    public NetworkProtocol getNetworkProtocolById(Integer protocolId) {
        log.debug("根据协议ID获取网络协议: {}", protocolId);
        return networkProtocolRepository.selectOneById(protocolId);
    }

    @Override
    public List<NetworkProtocol> getNetworkProtocolsByName(String protocolName) {
        log.debug("根据协议名称获取网络协议: {}", protocolName);
        return networkProtocolRepository.findByProtocolName(protocolName);
    }

    @Override
    public Map<String, Object> getNetworkProtocolIdMapping() {
        log.debug("获取网络协议ID映射");
        Map<String, Object> idMapping = new LinkedHashMap<>();

        List<NetworkProtocol> protocols = networkProtocolRepository.selectAll();
        for (NetworkProtocol protocol : protocols) {
            idMapping.put(protocol.getId().toString(), protocol.getProtocolName());
        }

        return idMapping;
    }

    // ==================== 应用ID和值映射实现 ====================

    @Override
    @Cacheable(value = "protocolMetadata", key = "'applicationIdMapping'")
    public Map<String, Object> getApplicationIdMapping() {
        log.debug("获取应用ID映射");
        Map<String, Object> appIdMapping = new LinkedHashMap<>();
        
        List<ApplicationProtocol> protocols = applicationProtocolRepository.selectAll();
        for (ApplicationProtocol protocol : protocols) {
            appIdMapping.put(protocol.getId().toString(), protocol.getName());
        }
        
        return appIdMapping;
    }

    @Override
    @Cacheable(value = "protocolMetadata", key = "'applicationValueMapping'")
    public Map<String, Object> getApplicationValueMapping() {
        log.debug("获取应用值映射");
        Map<String, Object> appValueMapping = new LinkedHashMap<>();
        
        List<ApplicationProtocol> protocols = applicationProtocolRepository.selectAll();
        for (ApplicationProtocol protocol : protocols) {
            appValueMapping.put(protocol.getName(), protocol.getDescription());
        }
        
        return appValueMapping;
    }

    @Override
    @Cacheable(value = "protocolMetadata", key = "'applicationTypeMapping'")
    public Map<String, Object> getApplicationTypeMapping() {
        log.debug("获取应用类型映射");
        Map<String, Object> appTypeMapping = new LinkedHashMap<>();
        
        List<ApplicationProtocol> protocols = applicationProtocolRepository.selectAll();
        Map<String, List<String>> categoryMap = protocols.stream()
                .collect(Collectors.groupingBy(
                    ApplicationProtocol::getCategory,
                    Collectors.mapping(ApplicationProtocol::getName, Collectors.toList())
                ));
        
        appTypeMapping.putAll(categoryMap);
        return appTypeMapping;
    }

    @Override
    @Cacheable(value = "protocolMetadata", key = "'applicationIdRuleMapping'")
    public Map<String, Object> getApplicationIdRuleMapping() {
        log.debug("获取应用ID规则映射");
        Map<String, Object> appIdRuleMapping = new LinkedHashMap<>();
        
        // 这里可以根据实际需求实现规则映射逻辑
        // 暂时返回空映射
        
        return appIdRuleMapping;
    }

    @Override
    @Cacheable(value = "protocolMetadata", key = "'applicationValueRuleMapping'")
    public Map<String, Object> getApplicationValueRuleMapping() {
        log.debug("获取应用值规则映射");
        Map<String, Object> appValueRuleMapping = new LinkedHashMap<>();
        
        // 这里可以根据实际需求实现规则映射逻辑
        // 暂时返回空映射
        
        return appValueRuleMapping;
    }

    // ==================== 互联网协议相关实现 ====================

    @Override
    @Cacheable(value = "protocolMetadata", key = "'internetProtocols'")
    public List<InternetProtocol> getAllInternetProtocols() {
        log.debug("获取所有互联网协议");
        return internetProtocolRepository.selectAll();
    }

    @Override
    public InternetProtocol getInternetProtocolByNumber(Integer protocolNumber) {
        log.debug("根据协议号获取互联网协议: {}", protocolNumber);
        return internetProtocolRepository.selectOneById(protocolNumber);
    }

    @Override
    public List<InternetProtocol> searchInternetProtocolsByKeyword(String keyword) {
        log.debug("根据关键字搜索互联网协议: {}", keyword);
        return internetProtocolRepository.selectListByQuery(
            query -> query.where(InternetProtocol::getKeyword).like(keyword)
                    .or(InternetProtocol::getProtocolDescription).like(keyword)
        );
    }

    // ==================== 缓存管理实现 ====================

    @Override
    @CacheEvict(value = "protocolMetadata", allEntries = true)
    public void initProtocolCache() {
        log.info("初始化协议缓存");
        // 预热缓存
        getAllProtocolTypes();
        getProtocolMetadata();
        getAllApplicationProtocols();
        getApplicationIdMapping();
        getApplicationValueMapping();
        getApplicationTypeMapping();
        getAllInternetProtocols();
    }

    @Override
    @CacheEvict(value = "protocolMetadata", allEntries = true)
    public void clearProtocolCache() {
        log.info("清除协议缓存");
    }

    @Override
    public void refreshProtocolCache() {
        log.info("刷新协议缓存");
        clearProtocolCache();
        initProtocolCache();
    }

    // ==================== 私有方法 ====================

    /**
     * 将 NetworkProtocol 转换为 NetworkProtocolDto DTO
     */
    private NetworkProtocolDto convertToNetworkProtocolDto(NetworkProtocol protocol) {
        NetworkProtocolDto dto = new NetworkProtocolDto();
        dto.setProtocolId(protocol.getId());
        dto.setProtocolValue(protocol.getProtocolName());
        dto.setProtocolName(protocol.getDisplayName());
        dto.setProtocolType(protocol.getCategory());
        dto.setProtocolExplanation(protocol.getDescription());
        dto.setType(protocol.getProtocolType());
        return dto;
    }
}

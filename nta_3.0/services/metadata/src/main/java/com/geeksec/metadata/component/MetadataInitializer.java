package com.geeksec.metadata.component;

import com.geeksec.metadata.service.CyberKillChainService;
import com.geeksec.metadata.service.LabelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 元数据服务初始化组件
 * 在应用启动时初始化缓存和基础数据
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MetadataInitializer implements ApplicationRunner {

    private final LabelService labelService;
    private final ProtocolKnowledgeService protocolKnowledgeService;
    private final CyberKillChainService cyberKillChainService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化元数据服务...");
        
        try {
            // 初始化标签缓存
            log.info("初始化标签缓存...");
            labelService.initLabelCache();
            
            // 初始化协议知识库缓存
            log.info("初始化协议知识库缓存...");
            protocolKnowledgeService.initProtocolCache();
            
            // 初始化 Cyber Kill Chain 缓存
            log.info("初始化 Cyber Kill Chain 缓存...");
            cyberKillChainService.initCyberKillChainCache();
            
            log.info("元数据服务初始化完成");

        } catch (Exception e) {
            log.error("元数据服务初始化失败", e);
            throw e;
        }
    }
}

package com.geeksec.metadata.repository;

import com.geeksec.metadata.model.entity.InternetProtocol;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 互联网协议Repository接口
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface InternetProtocolRepository extends BaseMapper<InternetProtocol> {

    /**
     * 根据协议号查询
     * @param protocolNumber 协议号
     * @return 互联网协议实体
     */
    InternetProtocol findByProtocolNumber(@Param("protocolNumber") Integer protocolNumber);

    /**
     * 根据关键字查询
     * @param keyword 关键字
     * @return 互联网协议列表
     */
    List<InternetProtocol> findByKeyword(@Param("keyword") String keyword);

    /**
     * 根据关键字模糊查询
     * @param keyword 关键字
     * @return 互联网协议列表
     */
    List<InternetProtocol> findByKeywordContaining(@Param("keyword") String keyword);

    /**
     * 根据协议描述模糊查询
     * @param protocolDescription 协议描述
     * @return 互联网协议列表
     */
    List<InternetProtocol> findByProtocolDescriptionContaining(@Param("protocolDescription") String protocolDescription);

    /**
     * 获取所有协议关键字
     * @return 协议关键字列表
     */
    List<String> findAllKeywords();

    /**
     * 获取所有协议号
     * @return 协议号列表
     */
    List<Integer> findAllProtocolNumbers();

    /**
     * 根据协议号列表查询
     * @param protocolNumbers 协议号列表
     * @return 互联网协议列表
     */
    List<InternetProtocol> findByProtocolNumberIn(@Param("protocolNumbers") List<Integer> protocolNumbers);
}

package com.geeksec.metadata.repository;

import com.geeksec.metadata.model.entity.NetworkProtocol;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网络协议Repository接口
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface NetworkProtocolRepository extends BaseMapper<NetworkProtocol> {

    /**
     * 根据协议ID查询
     */
    NetworkProtocol findById(@Param("id") Integer id);

    /**
     * 根据协议名称查询
     */
    List<NetworkProtocol> findByProtocolName(@Param("protocolName") String protocolName);

    /**
     * 根据显示名称查询
     */
    List<NetworkProtocol> findByDisplayName(@Param("displayName") String displayName);

    /**
     * 根据协议分类查询
     */
    List<NetworkProtocol> findByCategory(@Param("category") String category);

    /**
     * 根据协议类型查询
     */
    List<NetworkProtocol> findByProtocolType(@Param("protocolType") Integer protocolType);

    /**
     * 根据协议名称模糊查询
     */
    List<NetworkProtocol> findByProtocolNameContaining(@Param("protocolName") String protocolName);

    /**
     * 根据显示名称模糊查询
     */
    List<NetworkProtocol> findByDisplayNameContaining(@Param("displayName") String displayName);

    /**
     * 查询所有连接类型协议
     */
    List<NetworkProtocol> findAllConnectionProtocols();

    /**
     * 查询所有单包类型协议
     */
    List<NetworkProtocol> findAllSinglePacketProtocols();

    /**
     * 查询所有TCP/UDP负载类型协议
     */
    List<NetworkProtocol> findAllPayloadProtocols();

    /**
     * 获取所有协议分类
     */
    List<String> findAllCategories();

    /**
     * 获取所有协议名称
     */
    List<String> findAllProtocolNames();
}

package com.geeksec.metadata.service.impl;

import com.geeksec.metadata.service.QueryFieldMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 查询字段元数据服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
public class QueryFieldMetadataServiceImpl implements QueryFieldMetadataService {

    // ==================== ES查询字段相关实现 ====================

    @Override
    @Cacheable(value = "queryFieldMetadata", key = "'esQueryFields'")
    public Map<String, Object> getAllEsQueryFields() {
        log.debug("获取所有ES查询字段");
        Map<String, Object> esQueryFields = new LinkedHashMap<>();
        
        // SSL相关字段
        esQueryFields.put("ssl_subject", "SSL证书主题");
        esQueryFields.put("ssl_issuer", "SSL证书颁发者");
        esQueryFields.put("ssl_serial", "SSL证书序列号");
        esQueryFields.put("ssl_fingerprint", "SSL证书指纹");
        
        // HTTP相关字段
        esQueryFields.put("http_host", "HTTP主机");
        esQueryFields.put("http_uri", "HTTP URI");
        esQueryFields.put("http_user_agent", "HTTP用户代理");
        esQueryFields.put("http_method", "HTTP方法");
        esQueryFields.put("http_status", "HTTP状态码");
        
        // DNS相关字段
        esQueryFields.put("dns_query", "DNS查询");
        esQueryFields.put("dns_answer", "DNS应答");
        esQueryFields.put("dns_type", "DNS类型");
        
        // 网络相关字段
        esQueryFields.put("src_ip", "源IP");
        esQueryFields.put("dst_ip", "目标IP");
        esQueryFields.put("src_port", "源端口");
        esQueryFields.put("dst_port", "目标端口");
        esQueryFields.put("protocol", "协议");
        
        return esQueryFields;
    }

    @Override
    public Object getEsQueryFieldConfig(String fieldName) {
        log.debug("获取ES查询字段配置: {}", fieldName);
        return getAllEsQueryFields().get(fieldName);
    }

    @Override
    public String getEsQueryFieldDisplayName(String fieldName) {
        log.debug("获取ES查询字段显示名称: {}", fieldName);
        Object config = getEsQueryFieldConfig(fieldName);
        return config != null ? config.toString() : fieldName;
    }

    // ==================== 下载搜索字段相关实现 ====================

    @Override
    @Cacheable(value = "queryFieldMetadata", key = "'downloadSearchFields'")
    public Map<String, Object> getAllDownloadSearchFields() {
        log.debug("获取所有下载搜索字段");
        Map<String, Object> downloadSearchFields = new LinkedHashMap<>();
        
        downloadSearchFields.put("timestamp", "时间戳");
        downloadSearchFields.put("src_ip", "源IP");
        downloadSearchFields.put("dst_ip", "目标IP");
        downloadSearchFields.put("src_port", "源端口");
        downloadSearchFields.put("dst_port", "目标端口");
        downloadSearchFields.put("protocol", "协议");
        downloadSearchFields.put("app_protocol", "应用协议");
        downloadSearchFields.put("domain", "域名");
        downloadSearchFields.put("url", "URL");
        downloadSearchFields.put("file_name", "文件名");
        downloadSearchFields.put("file_size", "文件大小");
        downloadSearchFields.put("file_hash", "文件哈希");
        
        return downloadSearchFields;
    }

    @Override
    public Object getDownloadSearchFieldConfig(String fieldName) {
        log.debug("获取下载搜索字段配置: {}", fieldName);
        return getAllDownloadSearchFields().get(fieldName);
    }

    @Override
    public String getDownloadSearchFieldDisplayName(String fieldName) {
        log.debug("获取下载搜索字段显示名称: {}", fieldName);
        Object config = getDownloadSearchFieldConfig(fieldName);
        return config != null ? config.toString() : fieldName;
    }

    // ==================== Nebula相关字段实现 ====================

    @Override
    @Cacheable(value = "queryFieldMetadata", key = "'nebulaTypes'")
    public Map<String, Object> getAllNebulaTypes() {
        log.debug("获取所有Nebula类型");
        Map<String, Object> nebulaTypes = new LinkedHashMap<>();
        
        nebulaTypes.put("ip", "IP地址");
        nebulaTypes.put("domain", "域名");
        nebulaTypes.put("cert", "证书");
        nebulaTypes.put("app", "应用");
        nebulaTypes.put("org", "组织");
        nebulaTypes.put("fingerprint", "指纹");
        
        return nebulaTypes;
    }

    @Override
    public String getNebulaTypeName(String type) {
        log.debug("获取Nebula类型名称: {}", type);
        Object name = getAllNebulaTypes().get(type);
        return name != null ? name.toString() : type;
    }

    @Override
    @Cacheable(value = "queryFieldMetadata", key = "'nebulaProperties'")
    public Map<String, Object> getAllNebulaProperties() {
        log.debug("获取所有Nebula属性");
        Map<String, Object> nebulaProperties = new LinkedHashMap<>();
        
        // IP相关属性
        nebulaProperties.put("ip.addr", "IP地址");
        nebulaProperties.put("ip.country", "国家");
        nebulaProperties.put("ip.province", "省份");
        nebulaProperties.put("ip.city", "城市");
        nebulaProperties.put("ip.isp", "ISP");
        
        // 域名相关属性
        nebulaProperties.put("domain.name", "域名");
        nebulaProperties.put("domain.tld", "顶级域名");
        nebulaProperties.put("domain.registrar", "注册商");
        
        // 证书相关属性
        nebulaProperties.put("cert.subject", "证书主题");
        nebulaProperties.put("cert.issuer", "证书颁发者");
        nebulaProperties.put("cert.serial", "证书序列号");
        nebulaProperties.put("cert.fingerprint", "证书指纹");
        
        return nebulaProperties;
    }

    @Override
    public Object getNebulaPropertyConfig(String propertyName) {
        log.debug("获取Nebula属性配置: {}", propertyName);
        return getAllNebulaProperties().get(propertyName);
    }

    // ==================== 字段验证相关实现 ====================

    @Override
    public boolean isValidEsQueryField(String fieldName) {
        return getAllEsQueryFields().containsKey(fieldName);
    }

    @Override
    public boolean isValidDownloadSearchField(String fieldName) {
        return getAllDownloadSearchFields().containsKey(fieldName);
    }

    @Override
    public boolean isValidNebulaType(String type) {
        return getAllNebulaTypes().containsKey(type);
    }

    @Override
    public boolean isValidNebulaProperty(String propertyName) {
        return getAllNebulaProperties().containsKey(propertyName);
    }
}

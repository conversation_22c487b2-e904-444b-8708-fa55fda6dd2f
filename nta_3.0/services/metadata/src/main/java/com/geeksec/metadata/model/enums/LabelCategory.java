package com.geeksec.metadata.model.enums;

import lombok.Getter;

/**
 * 标签分类枚举
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum LabelCategory {
    
    // 实际使用的标签类别
    /**
     * 威胁（533个标签）
     */
    THREAT("THREAT", "威胁", "威胁相关标签"),
    
    /**
     * 知识库（390个标签）
     */
    KNOWLEDGE_BASE("KNOWLEDGE_BASE", "知识库", "知识库相关标签"),
    
    /**
     * 高维度标签（87个标签）
     */
    HIGH_DIMENSION_LABEL("HIGH_DIMENSION_LABEL", "高维度标签", "高维度分析标签"),
    
    /**
     * 代理（56个标签）
     */
    PROXY("PROXY", "代理", "代理服务相关标签"),
    
    /**
     * 加密流量检测（22个标签）
     */
    ENCRYPTED_TRAFFIC_DETECTION("ENCRYPTED_TRAFFIC_DETECTION", "加密流量检测", "加密流量检测相关标签"),
    
    /**
     * 合法性（22个标签）
     */
    LEGITIMACY("LEGITIMACY", "合法性", "合法性验证相关标签"),
    
    /**
     * 基础属性（11个标签）
     */
    BASIC_ATTRIBUTES("BASIC_ATTRIBUTES", "基础属性", "基础属性相关标签"),
    
    /**
     * APT（5个标签）
     */
    APT("APT", "APT", "高级持续性威胁相关标签"),
    
    /**
     * 行为描述（3个标签）
     */
    BEHAVIOR_DESCRIPTION("BEHAVIOR_DESCRIPTION", "行为描述", "行为描述相关标签"),
    
    /**
     * 行为检测模块（2个标签）
     */
    BEHAVIOR_DETECTION_MODULE("BEHAVIOR_DETECTION_MODULE", "行为检测模块", "行为检测模块相关标签"),
    
    /**
     * 命令与控制（1个标签）
     */
    COMMAND_CONTROL("COMMAND_CONTROL", "命令与控制", "命令与控制相关标签"),
    
    /**
     * 指纹描述（1个标签）
     */
    FINGERPRINT_DESCRIPTION("FINGERPRINT_DESCRIPTION", "指纹描述", "指纹描述相关标签"),

    // 证书标签类别
    /**
     * 安全
     */
    SECURITY("SECURITY", "安全", "安全相关标签"),
    
    /**
     * 信任
     */
    TRUST("TRUST", "信任", "信任相关标签"),
    
    /**
     * 使用
     */
    USAGE("USAGE", "使用", "使用相关标签"),
    
    /**
     * 验证
     */
    VALIDATION("VALIDATION", "验证", "验证相关标签"),
    
    /**
     * 恶意
     */
    MALICIOUS("MALICIOUS", "恶意", "恶意相关标签"),

    // 预留类别
    /**
     * 远程控制
     */
    REMOTE_CONTROL("REMOTE_CONTROL", "远程控制", "远程控制相关标签"),
    
    /**
     * 功能描述
     */
    FUNCTION_DESCRIPTION("FUNCTION_DESCRIPTION", "功能描述", "功能描述相关标签"),
    
    /**
     * 攻击入侵
     */
    ATTACK_INTRUSION("ATTACK_INTRUSION", "攻击入侵", "攻击入侵相关标签"),
    
    /**
     * 身份欺骗
     */
    IDENTITY_SPOOFING("IDENTITY_SPOOFING", "身份欺骗", "身份欺骗相关标签"),
    
    /**
     * 翻墙上网
     */
    CIRCUMVENTION("CIRCUMVENTION", "翻墙上网", "翻墙上网相关标签"),
    
    /**
     * 中间人
     */
    MAN_IN_MIDDLE("MAN_IN_MIDDLE", "中间人", "中间人攻击相关标签"),
    
    /**
     * 私有检测
     */
    PRIVATE_DETECTION("PRIVATE_DETECTION", "私有检测", "私有检测相关标签");

    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 中文名称
     */
    private final String chineseName;
    
    /**
     * 描述
     */
    private final String description;

    LabelCategory(String value, String chineseName, String description) {
        this.value = value;
        this.chineseName = chineseName;
        this.description = description;
    }

    /**
     * 根据值获取枚举
     */
    public static LabelCategory fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (LabelCategory category : values()) {
            if (category.value.equals(value)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据中文名称获取枚举
     */
    public static LabelCategory fromChineseName(String chineseName) {
        if (chineseName == null) {
            return null;
        }
        for (LabelCategory category : values()) {
            if (category.chineseName.equals(chineseName)) {
                return category;
            }
        }
        return null;
    }
}

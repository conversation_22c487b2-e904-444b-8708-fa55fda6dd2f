package com.geeksec.metadata.service.impl;

import com.geeksec.common.metadata.dto.CountryInfo;
import com.geeksec.common.metadata.enums.CaptureMode;
import com.geeksec.metadata.service.SystemConfigMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 系统配置元数据服务实现类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
public class SystemConfigMetadataServiceImpl implements SystemConfigMetadataService {

    /**
     * 预定义的国家信息
     */
    private static final List<CountryInfo> COUNTRIES = Arrays.asList(
        CountryInfo.of("CN", "中国", "China"),
        CountryInfo.of("US", "美国", "United States"),
        CountryInfo.of("JP", "日本", "Japan"),
        CountryInfo.of("KR", "韩国", "South Korea"),
        CountryInfo.of("GB", "英国", "United Kingdom"),
        CountryInfo.of("DE", "德国", "Germany"),
        CountryInfo.of("FR", "法国", "France"),
        CountryInfo.of("RU", "俄罗斯", "Russia"),
        CountryInfo.of("IN", "印度", "India"),
        CountryInfo.of("BR", "巴西", "Brazil"),
        CountryInfo.of("CA", "加拿大", "Canada"),
        CountryInfo.of("AU", "澳大利亚", "Australia"),
        CountryInfo.of("SG", "新加坡", "Singapore"),
        CountryInfo.of("HK", "香港", "Hong Kong"),
        CountryInfo.of("TW", "台湾", "Taiwan")
    );

    @Override
    @Cacheable(value = "systemConfigMetadata", key = "'captureModes'")
    public List<CaptureMode> getAllCaptureModes() {
        log.debug("获取所有捕获模式");
        return Arrays.asList(CaptureMode.values());
    }

    @Override
    @Cacheable(value = "systemConfigMetadata", key = "'countries'")
    public List<CountryInfo> getAllCountries() {
        log.debug("获取所有国家信息");
        return COUNTRIES;
    }

    @Override
    public CaptureMode getCaptureModeByCode(int code) {
        log.debug("根据代码获取捕获模式: {}", code);
        return CaptureMode.fromCode(code);
    }

    @Override
    public CountryInfo getCountryByCode(String code) {
        log.debug("根据代码获取国家信息: {}", code);
        return COUNTRIES.stream()
            .filter(country -> country.getCode().equals(code))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("未知的国家代码: " + code));
    }
}

package com.geeksec.metadata.service;

import java.util.Map;

/**
 * 查询字段元数据服务接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface QueryFieldMetadataService {
    
    // ==================== ES查询字段相关 ====================
    
    /**
     * 获取所有ES查询字段
     * 
     * @return ES查询字段映射
     */
    Map<String, Object> getAllEsQueryFields();
    
    /**
     * 根据字段名获取ES查询字段配置
     * 
     * @param fieldName 字段名
     * @return 字段配置
     */
    Object getEsQueryFieldConfig(String fieldName);
    
    /**
     * 获取ES查询字段的显示名称
     * 
     * @param fieldName 字段名
     * @return 显示名称
     */
    String getEsQueryFieldDisplayName(String fieldName);
    
    // ==================== 下载搜索字段相关 ====================
    
    /**
     * 获取所有下载搜索字段
     * 
     * @return 下载搜索字段映射
     */
    Map<String, Object> getAllDownloadSearchFields();
    
    /**
     * 根据字段名获取下载搜索字段配置
     * 
     * @param fieldName 字段名
     * @return 字段配置
     */
    Object getDownloadSearchFieldConfig(String fieldName);
    
    /**
     * 获取下载搜索字段的显示名称
     * 
     * @param fieldName 字段名
     * @return 显示名称
     */
    String getDownloadSearchFieldDisplayName(String fieldName);
    
    // ==================== Nebula相关字段 ====================
    
    /**
     * 获取所有Nebula类型
     * 
     * @return Nebula类型映射
     */
    Map<String, Object> getAllNebulaTypes();
    
    /**
     * 根据类型获取Nebula类型名称
     * 
     * @param type 类型
     * @return 类型名称
     */
    String getNebulaTypeName(String type);
    
    /**
     * 获取所有Nebula属性
     * 
     * @return Nebula属性映射
     */
    Map<String, Object> getAllNebulaProperties();
    
    /**
     * 根据属性名获取Nebula属性配置
     * 
     * @param propertyName 属性名
     * @return 属性配置
     */
    Object getNebulaPropertyConfig(String propertyName);
    
    // ==================== 字段验证相关 ====================
    
    /**
     * 验证ES查询字段是否有效
     * 
     * @param fieldName 字段名
     * @return 是否有效
     */
    boolean isValidEsQueryField(String fieldName);
    
    /**
     * 验证下载搜索字段是否有效
     * 
     * @param fieldName 字段名
     * @return 是否有效
     */
    boolean isValidDownloadSearchField(String fieldName);
    
    /**
     * 验证Nebula类型是否有效
     * 
     * @param type 类型
     * @return 是否有效
     */
    boolean isValidNebulaType(String type);
    
    /**
     * 验证Nebula属性是否有效
     * 
     * @param propertyName 属性名
     * @return 是否有效
     */
    boolean isValidNebulaProperty(String propertyName);
}

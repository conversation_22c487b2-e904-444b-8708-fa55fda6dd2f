package com.geeksec.metadata.service;

import com.geeksec.common.metadata.dto.CountryInfo;
import com.geeksec.common.metadata.enums.CaptureMode;

import java.util.List;

/**
 * 系统配置元数据服务接口
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public interface SystemConfigMetadataService {
    
    /**
     * 获取所有捕获模式
     * 
     * @return 捕获模式列表
     */
    List<CaptureMode> getAllCaptureModes();
    
    /**
     * 获取所有国家信息
     * 
     * @return 国家信息列表
     */
    List<CountryInfo> getAllCountries();
    
    /**
     * 根据代码获取捕获模式
     * 
     * @param code 模式代码
     * @return 捕获模式
     */
    CaptureMode getCaptureModeByCode(int code);
    
    /**
     * 根据代码获取国家信息
     * 
     * @param code 国家代码
     * @return 国家信息
     */
    CountryInfo getCountryByCode(String code);
}

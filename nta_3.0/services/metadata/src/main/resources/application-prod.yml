# Metadata Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8089}
  servlet:
    context-path: /metadata

# 元数据服务特定配置
metadata:
  # 生产环境数据处理配置
  processing:
    # 元数据提取配置
    extraction:
      batch-size: 2000  # 生产环境大批次
      timeout: 300000  # 5分钟超时
      enable-parallel: true  # 生产环境启用并行处理
      parallel-threads: 8
    
    # 元数据存储配置
    storage:
      base-path: ${METADATA_STORAGE_PATH:/data/nta-prod/metadata}
      temp-path: ${METADATA_TEMP_PATH:/data/nta-prod/metadata/temp}
      backup-path: ${METADATA_BACKUP_PATH:/backup/nta-prod/metadata}
      archive-path: ${METADATA_ARCHIVE_PATH:/archive/nta-prod/metadata}
      enable-compression: true  # 生产环境启用压缩
      enable-encryption: true  # 生产环境启用加密
    
    # 元数据索引配置
    indexing:
      enable-auto-index: true
      index-interval: 900  # 15分钟索引一次
      max-index-size: 200000
      enable-incremental: true
      enable-distributed: true
  
  # 生产环境缓存配置
  cache:
    metadata-cache:
      max-size: 100000
      ttl: 7200  # 2小时
    schema-cache:
      max-size: 20000
      ttl: 14400  # 4小时
  
  # 生产环境API配置
  api:
    rate-limit:
      enabled: true  # 生产环境启用限流
      requests-per-minute: 200
      burst-capacity: 500
    
    # 查询配置
    query:
      max-result-size: 10000
      timeout: 120000  # 2分钟查询超时
  
  # 生产环境安全配置
  security:
    enable-metadata-encryption: true
    enable-access-control: true
    enable-audit-log: true
  
  # 生产环境监控配置
  monitoring:
    enable-metrics: true
    enable-performance-tracking: true
    enable-data-quality-check: true
  
  # 生产环境高可用配置
  ha:
    enable-failover: true
    backup-storage-url: ${BACKUP_METADATA_STORAGE}
    health-check-interval: 30

# 外部服务配置
external:
  services:
    session-service:
      url: ${SESSION_SERVICE_URL:http://prod-session.nta.local:8088}
      timeout: 15000
      retry-count: 3
      circuit-breaker-enabled: true
    task-service:
      url: ${TASK_SERVICE_URL:http://prod-task.nta.local:8086}
      timeout: 15000
      retry-count: 3
      circuit-breaker-enabled: true

# 日志配置
logging:
  level:
    '[com.geeksec.metadata]': INFO
    '[org.springframework.cache]': WARN

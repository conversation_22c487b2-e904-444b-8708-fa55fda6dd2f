# Metadata Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8089}
  servlet:
    context-path: /metadata

# 元数据服务特定配置
metadata:
  # 测试环境数据处理配置
  processing:
    # 元数据提取配置
    extraction:
      batch-size: 500  # 测试环境中等批次
      timeout: 60000  # 60秒超时
      enable-parallel: true  # 测试环境启用并行处理
      parallel-threads: 4
    
    # 元数据存储配置
    storage:
      base-path: ${METADATA_STORAGE_PATH:/data/nta-test/metadata}
      temp-path: ${METADATA_TEMP_PATH:/data/nta-test/metadata/temp}
      backup-path: ${METADATA_BACKUP_PATH:/data/nta-test/metadata/backup}
      enable-compression: true  # 测试环境启用压缩
    
    # 元数据索引配置
    indexing:
      enable-auto-index: true
      index-interval: 1800  # 30分钟索引一次
      max-index-size: 50000
      enable-incremental: true
  
  # 测试环境缓存配置
  cache:
    metadata-cache:
      max-size: 25000
      ttl: 3600  # 1小时
    schema-cache:
      max-size: 5000
      ttl: 7200  # 2小时
  
  # 测试环境API配置
  api:
    rate-limit:
      enabled: true  # 测试环境启用限流
      requests-per-minute: 500
    
    # 查询配置
    query:
      max-result-size: 5000
      timeout: 30000  # 30秒查询超时
  
  # 测试环境监控配置
  monitoring:
    enable-metrics: true
    enable-performance-tracking: true

# 外部服务配置
external:
  services:
    session-service:
      url: ${SESSION_SERVICE_URL:http://test-session.nta.local:8088}
      timeout: 10000
      retry-count: 3
    task-service:
      url: ${TASK_SERVICE_URL:http://test-task.nta.local:8086}
      timeout: 10000
      retry-count: 3

# 日志配置
logging:
  level:
    '[com.geeksec.metadata]': INFO
    '[org.springframework.cache]': INFO

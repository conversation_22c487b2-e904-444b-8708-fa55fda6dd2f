# Metadata Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8089}
  servlet:
    context-path: /metadata

# 元数据服务特定配置
metadata:
  # 开发环境数据处理配置
  processing:
    # 元数据提取配置
    extraction:
      batch-size: 100  # 开发环境较小批次
      timeout: 30000  # 30秒超时
      enable-parallel: false  # 开发环境关闭并行处理
    
    # 元数据存储配置
    storage:
      base-path: ${METADATA_STORAGE_PATH:./dev-data/metadata}
      temp-path: ${METADATA_TEMP_PATH:./dev-data/metadata/temp}
      enable-compression: false  # 开发环境关闭压缩
    
    # 元数据索引配置
    indexing:
      enable-auto-index: true
      index-interval: 3600  # 1小时索引一次
      max-index-size: 10000
  
  # 开发环境缓存配置
  cache:
    metadata-cache:
      max-size: 5000
      ttl: 1800  # 30分钟
    schema-cache:
      max-size: 1000
      ttl: 3600  # 1小时
  
  # 开发环境API配置
  api:
    rate-limit:
      enabled: false  # 开发环境关闭限流
      requests-per-minute: 1000
    
    # 查询配置
    query:
      max-result-size: 1000
      timeout: 15000  # 15秒查询超时

# 外部服务配置
external:
  services:
    session-service:
      url: ${SESSION_SERVICE_URL:http://localhost:8088}
      timeout: 5000
    task-service:
      url: ${TASK_SERVICE_URL:http://localhost:8086}
      timeout: 5000

# 日志配置
logging:
  level:
    '[com.geeksec.metadata]': DEBUG
    '[org.springframework.cache]': DEBUG

# Session Management Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

  # 测试环境 Doris 数据库配置
  datasource:
    url: jdbc:mysql://${DORIS_HOST:test-doris.nta.local}:${DORIS_PORT:9030}/${DORIS_DATABASE:nta_test}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DORIS_USERNAME:nta_test}
    password: ${DORIS_PASSWORD:nta_test123}
    driver-class-name: com.mysql.cj.jdbc.Driver

# 服务器配置
server:
  port: ${SERVER_PORT:8083}
  servlet:
    context-path: /session

# 会话管理服务特定配置
session:
  # 测试环境数据保留配置
  retention:
    session-days: 30  # 测试环境保留30天
    raw-data-days: 14  # 原始数据保留14天
    aggregated-data-days: 60  # 聚合数据保留60天
  
  # 测试环境查询配置
  query:
    max-result-size: 50000  # 最大返回结果数
    timeout: 60000  # 60秒查询超时
    enable-cache: true
    cache-ttl: 600  # 10分钟缓存
  
  # 测试环境分页配置
  pagination:
    default-page-size: 200
    max-page-size: 5000
  
  # 测试环境导出配置
  export:
    max-export-size: 200000  # 最大导出记录数
    temp-dir: ${SESSION_TEMP_DIR:/data/nta-test/session/temp}
    formats: [csv, json, excel, parquet]
  
  # 测试环境性能配置
  performance:
    enable-query-optimization: true
    enable-parallel-query: true
    max-parallel-threads: 4

# 外部服务配置
external:
  services:
    metadata-service:
      url: ${METADATA_SERVICE_URL:http://test-metadata.nta.local:8089}
      timeout: 10000
      retry-count: 3
    task-service:
      url: ${TASK_SERVICE_URL:http://test-task.nta.local:8086}
      timeout: 10000
      retry-count: 3

# 日志配置
logging:
  level:
    '[com.geeksec.session]': INFO
    '[org.apache.doris]': WARN

# Session Management Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

  # 开发环境 Doris 数据库配置
  datasource:
    url: jdbc:mysql://${DORIS_HOST:localhost}:${DORIS_PORT:9030}/${DORIS_DATABASE:nta_dev}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DORIS_USERNAME:root}
    password: ${DORIS_PASSWORD:}
    driver-class-name: com.mysql.cj.jdbc.Driver

# 服务器配置
server:
  port: ${SERVER_PORT:8083}
  servlet:
    context-path: /session

# 会话管理服务特定配置
session:
  # 开发环境数据保留配置
  retention:
    session-days: 7  # 开发环境保留7天
    raw-data-days: 3  # 原始数据保留3天
    aggregated-data-days: 14  # 聚合数据保留14天
  
  # 开发环境查询配置
  query:
    max-result-size: 10000  # 最大返回结果数
    timeout: 30000  # 30秒查询超时
    enable-cache: true
    cache-ttl: 300  # 5分钟缓存
  
  # 开发环境分页配置
  pagination:
    default-page-size: 100
    max-page-size: 1000
  
  # 开发环境导出配置
  export:
    max-export-size: 50000  # 最大导出记录数
    temp-dir: ${SESSION_TEMP_DIR:./dev-data/session/temp}
    formats: [csv, json, excel]

# 外部服务配置
external:
  services:
    metadata-service:
      url: ${METADATA_SERVICE_URL:http://localhost:8089}
      timeout: 5000
    task-service:
      url: ${TASK_SERVICE_URL:http://localhost:8086}
      timeout: 5000

# 日志配置
logging:
  level:
    '[com.geeksec.session]': DEBUG
    '[org.apache.doris]': INFO

# Session Management Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

  # 生产环境 Doris 数据库配置
  datasource:
    url: jdbc:mysql://${DORIS_HOST:prod-doris.nta.local}:${DORIS_PORT:9030}/${DORIS_DATABASE:nta_prod}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
    username: ${DORIS_USERNAME:nta_prod}
    password: ${DORIS_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver

# 服务器配置
server:
  port: ${SERVER_PORT:8083}
  servlet:
    context-path: /session

# 会话管理服务特定配置
session:
  # 生产环境数据保留配置
  retention:
    session-days: 90  # 生产环境保留90天
    raw-data-days: 30  # 原始数据保留30天
    aggregated-data-days: 365  # 聚合数据保留1年
  
  # 生产环境查询配置
  query:
    max-result-size: 100000  # 最大返回结果数
    timeout: 300000  # 5分钟查询超时
    enable-cache: true
    cache-ttl: 1800  # 30分钟缓存
    enable-query-log: true  # 启用查询日志
  
  # 生产环境分页配置
  pagination:
    default-page-size: 500
    max-page-size: 10000
  
  # 生产环境导出配置
  export:
    max-export-size: 1000000  # 最大导出记录数
    temp-dir: ${SESSION_TEMP_DIR:/data/nta-prod/session/temp}
    formats: [csv, json, excel, parquet]
    enable-compression: true
    cleanup-interval: 3600  # 1小时清理一次临时文件
  
  # 生产环境性能配置
  performance:
    enable-query-optimization: true
    enable-parallel-query: true
    max-parallel-threads: 8
    enable-connection-pool: true
    connection-pool-size: 20
  
  # 生产环境安全配置
  security:
    enable-query-audit: true
    enable-data-masking: ${SESSION_DATA_MASKING:false}
    enable-access-control: true
  
  # 生产环境监控配置
  monitoring:
    enable-query-metrics: true
    enable-performance-metrics: true
    slow-query-threshold: 10000  # 慢查询阈值10秒

# 外部服务配置
external:
  services:
    metadata-service:
      url: ${METADATA_SERVICE_URL:http://prod-metadata.nta.local:8089}
      timeout: 15000
      retry-count: 3
      circuit-breaker-enabled: true
    task-service:
      url: ${TASK_SERVICE_URL:http://prod-task.nta.local:8086}
      timeout: 15000
      retry-count: 3
      circuit-breaker-enabled: true

# 日志配置
logging:
  level:
    '[com.geeksec.session]': INFO
    '[org.apache.doris]': WARN

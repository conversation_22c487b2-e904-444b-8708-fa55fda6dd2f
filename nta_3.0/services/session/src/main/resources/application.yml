# 应用基本信息
spring:
  application:
    name: session-management-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common
  
  # Doris数据库配置
  datasource:
    url: jdbc:mysql://${DORIS_HOST:localhost}:${DORIS_PORT:9030}/${DORIS_DATABASE:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DORIS_USERNAME:root}
    password: ${DORIS_PASSWORD:}
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false

# 服务器基本配置
server:
  port: 8080
  servlet:
    context-path: /session-api

# 日志配置
logging:
  level:
    root: INFO
    com.geeksec.session: DEBUG
  file:
    name: logs/session-service.log
    max-size: 10MB
    max-history: 7
    total-size-cap: 100MB

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
    labelsSorter: alpha
  show-actuator: true

# Actuator基本配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# Knife4j基本配置
knife4j:
  enable: true
  production: false
  basic:
    enable: false
  setting:
    language: zh-CN

# 应用配置
app:
  # 会话数据保留天数（与Doris动态分区配置保持一致）
  session:
    retention-days: 90
  # 批量处理大小
  batch:
    size: 1000

# 外部服务配置
services:
  metadata-service:
    url: http://metadata-service:8089
  task-service:
    url: http://task-service:8087

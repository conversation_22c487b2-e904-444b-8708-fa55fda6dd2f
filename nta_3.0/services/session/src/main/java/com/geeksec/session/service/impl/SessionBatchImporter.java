package com.geeksec.session.service.impl;

import com.geeksec.session.config.SessionProperties;
import com.geeksec.session.model.entity.Session;
import com.geeksec.session.repository.SessionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会话批量导入服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SessionBatchImporter {

    private final SessionRepository sessionRepository;
    private final SessionProperties sessionProperties;

    /**
     * 批量保存会话数据
     *
     * @param sessions 会话列表
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<Session> sessions) {
        if (sessions == null || sessions.isEmpty()) {
            return;
        }

        try {
            int batchSize = sessionProperties.getBatch().getSize();
            int total = sessions.size();
            log.info("开始批量导入 {} 条会话数据，批次大小: {}", total, batchSize);

            for (int i = 0; i < total; i += batchSize) {
                int end = Math.min(i + batchSize, total);
                List<Session> batchList = sessions.subList(i, end);
                sessionRepository.saveAll(batchList);
                log.debug("已导入 {}-{}/{} 条会话数据", i + 1, end, total);
            }

            log.info("成功导入 {} 条会话数据", total);
        } catch (Exception e) {
            log.error("批量导入会话数据失败", e);
            throw new RuntimeException("批量导入会话数据失败: " + e.getMessage(), e);
        }
    }
}

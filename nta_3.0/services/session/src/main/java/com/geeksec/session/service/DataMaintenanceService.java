package com.geeksec.session.service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 数据维护服务接口 - 基于Doris动态分区的数据生命周期管理
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DataMaintenanceService {
    
    /**
     * 获取Doris表分区信息
     * 
     * @param tableName 表名
     * @return 分区信息列表
     */
    List<Map<String, Object>> getTablePartitions(String tableName);
    
    /**
     * 获取会话日志表分区信息
     * 
     * @return 分区信息列表
     */
    List<Map<String, Object>> getSessionLogPartitions();
    
    /**
     * 获取分区存储统计信息
     * 
     * @param tableName 表名
     * @return 存储统计信息
     */
    Map<String, Object> getPartitionStorageStats(String tableName);
    
    /**
     * 获取数据生命周期配置
     * 
     * @param tableName 表名
     * @return 生命周期配置
     */
    Map<String, Object> getDataLifecycleConfig(String tableName);
    
    /**
     * 更新数据生命周期配置
     * 
     * @param tableName 表名
     * @param config 新的配置
     * @return 操作结果
     */
    Map<String, Object> updateDataLifecycleConfig(String tableName, Map<String, Object> config);
    
    /**
     * 检查过期分区
     * 
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @return 过期分区列表
     */
    List<Map<String, Object>> checkExpiredPartitions(String tableName, int retentionDays);
    
    /**
     * 获取数据清理状态
     * 
     * @return 清理状态信息
     */
    Map<String, Object> getDataCleanupStatus();
    
    /**
     * 获取存储空间使用情况
     * 
     * @return 存储使用情况
     */
    Map<String, Object> getStorageUsage();
    
    /**
     * 获取数据增长趋势
     * 
     * @param days 统计天数
     * @return 数据增长趋势
     */
    List<Map<String, Object>> getDataGrowthTrend(int days);
    
    /**
     * 执行数据压缩优化
     * 
     * @param tableName 表名
     * @param partitionName 分区名（可选）
     * @return 操作结果
     */
    Map<String, Object> executeDataCompaction(String tableName, String partitionName);
    
    /**
     * 获取表统计信息
     * 
     * @param tableName 表名
     * @return 表统计信息
     */
    Map<String, Object> getTableStatistics(String tableName);
    
    /**
     * 刷新表统计信息
     * 
     * @param tableName 表名
     * @return 操作结果
     */
    Map<String, Object> refreshTableStatistics(String tableName);
    
    /**
     * 获取数据质量报告
     * 
     * @param tableName 表名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据质量报告
     */
    Map<String, Object> getDataQualityReport(String tableName, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 检查数据完整性
     * 
     * @param tableName 表名
     * @param checkType 检查类型
     * @return 完整性检查结果
     */
    Map<String, Object> checkDataIntegrity(String tableName, String checkType);
    
    /**
     * 获取系统健康状态
     * 
     * @return 系统健康状态
     */
    Map<String, Object> getSystemHealthStatus();
    
    /**
     * 获取性能监控指标
     * 
     * @return 性能监控指标
     */
    Map<String, Object> getPerformanceMetrics();
    
    /**
     * 执行数据备份
     * 
     * @param tableName 表名
     * @param backupType 备份类型
     * @return 备份结果
     */
    Map<String, Object> executeDataBackup(String tableName, String backupType);
    
    /**
     * 获取备份历史
     * 
     * @param tableName 表名
     * @return 备份历史列表
     */
    List<Map<String, Object>> getBackupHistory(String tableName);
    
    /**
     * 执行数据恢复
     * 
     * @param backupId 备份ID
     * @param targetTable 目标表名
     * @return 恢复结果
     */
    Map<String, Object> executeDataRestore(String backupId, String targetTable);
    
    /**
     * 获取维护任务列表
     * 
     * @return 维护任务列表
     */
    List<Map<String, Object>> getMaintenanceTasks();
    
    /**
     * 创建维护任务
     * 
     * @param taskConfig 任务配置
     * @return 创建结果
     */
    Map<String, Object> createMaintenanceTask(Map<String, Object> taskConfig);
    
    /**
     * 执行维护任务
     * 
     * @param taskId 任务ID
     * @return 执行结果
     */
    Map<String, Object> executeMaintenanceTask(String taskId);
    
    /**
     * 获取维护任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    Map<String, Object> getMaintenanceTaskStatus(String taskId);
    
    /**
     * 取消维护任务
     * 
     * @param taskId 任务ID
     * @return 操作结果
     */
    Map<String, Object> cancelMaintenanceTask(String taskId);
}

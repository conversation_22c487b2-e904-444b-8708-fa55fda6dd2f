package com.geeksec.session.service;

import com.geeksec.session.model.dto.SessionDetailResponse;
import com.geeksec.session.model.dto.SessionRelationResponse;
import com.geeksec.session.model.dto.SessionRemarkRequest;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话详情服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SessionDetailService {
    
    /**
     * 获取会话详情
     * 
     * @param sessionId 会话ID
     * @return 会话详情
     */
    SessionDetailResponse getSessionDetail(String sessionId);
    
    /**
     * 获取会话关联分析
     * 
     * @param sessionId 会话ID
     * @param relationType 关联类型
     * @param timeRange 时间范围(小时)
     * @param limit 返回数量限制
     * @return 关联分析结果
     */
    SessionRelationResponse getSessionRelation(String sessionId, String relationType, int timeRange, int limit);
    
    /**
     * 获取相同源IP的会话
     * 
     * @param sessionId 会话ID
     * @param timeRange 时间范围(小时)
     * @param limit 返回数量限制
     * @return 相同源IP的会话列表
     */
    SessionRelationResponse getSameSourceIpSessions(String sessionId, int timeRange, int limit);
    
    /**
     * 获取相同目标IP的会话
     * 
     * @param sessionId 会话ID
     * @param timeRange 时间范围(小时)
     * @param limit 返回数量限制
     * @return 相同目标IP的会话列表
     */
    SessionRelationResponse getSameDestinationIpSessions(String sessionId, int timeRange, int limit);
    
    /**
     * 获取相同端口的会话
     * 
     * @param sessionId 会话ID
     * @param portType 端口类型 (src_port, dst_port)
     * @param timeRange 时间范围(小时)
     * @param limit 返回数量限制
     * @return 相同端口的会话列表
     */
    SessionRelationResponse getSamePortSessions(String sessionId, String portType, int timeRange, int limit);
    
    /**
     * 获取相同应用的会话
     * 
     * @param sessionId 会话ID
     * @param timeRange 时间范围(小时)
     * @param limit 返回数量限制
     * @return 相同应用的会话列表
     */
    SessionRelationResponse getSameApplicationSessions(String sessionId, int timeRange, int limit);
    
    /**
     * 获取时间相近的会话
     * 
     * @param sessionId 会话ID
     * @param timeWindow 时间窗口(分钟)
     * @param limit 返回数量限制
     * @return 时间相近的会话列表
     */
    SessionRelationResponse getTimeProximitySessions(String sessionId, int timeWindow, int limit);
    
    /**
     * 获取会话的协议栈分析
     * 
     * @param sessionId 会话ID
     * @return 协议栈分析结果
     */
    Map<String, Object> getSessionProtocolStackAnalysis(String sessionId);
    
    /**
     * 获取会话的流量模式分析
     * 
     * @param sessionId 会话ID
     * @return 流量模式分析结果
     */
    Map<String, Object> getSessionTrafficPatternAnalysis(String sessionId);
    
    /**
     * 获取会话的安全风险评估
     * 
     * @param sessionId 会话ID
     * @return 安全风险评估结果
     */
    Map<String, Object> getSessionSecurityRiskAssessment(String sessionId);
    
    /**
     * 获取会话的地理位置信息
     * 
     * @param sessionId 会话ID
     * @return 地理位置信息
     */
    Map<String, Object> getSessionGeolocationInfo(String sessionId);
    
    /**
     * 获取会话的时间线分析
     * 
     * @param sessionId 会话ID
     * @return 时间线分析结果
     */
    List<Map<String, Object>> getSessionTimelineAnalysis(String sessionId);
    
    /**
     * 获取会话的数据包分析
     * 
     * @param sessionId 会话ID
     * @param limit 返回数量限制
     * @return 数据包分析结果
     */
    List<Map<String, Object>> getSessionPacketAnalysis(String sessionId, int limit);
    
    /**
     * 添加会话备注
     * 
     * @param remarkRequest 备注请求
     * @return 操作结果
     */
    Map<String, Object> addSessionRemark(SessionRemarkRequest remarkRequest);
    
    /**
     * 获取会话备注列表
     * 
     * @param sessionId 会话ID
     * @return 备注列表
     */
    List<Map<String, Object>> getSessionRemarks(String sessionId);
    
    /**
     * 删除会话备注
     * 
     * @param sessionId 会话ID
     * @param remarkId 备注ID
     * @return 操作结果
     */
    Map<String, Object> deleteSessionRemark(String sessionId, String remarkId);
    
    /**
     * 获取会话的相似度分析
     * 
     * @param sessionId 会话ID
     * @param compareSessionId 对比会话ID
     * @return 相似度分析结果
     */
    Map<String, Object> getSessionSimilarityAnalysis(String sessionId, String compareSessionId);
    
    /**
     * 获取会话的异常检测结果
     * 
     * @param sessionId 会话ID
     * @return 异常检测结果
     */
    Map<String, Object> getSessionAnomalyDetection(String sessionId);
    
    /**
     * 导出会话详情报告
     * 
     * @param sessionId 会话ID
     * @param format 导出格式 (PDF, EXCEL, JSON)
     * @return 导出文件信息
     */
    Map<String, Object> exportSessionDetailReport(String sessionId, String format);
}

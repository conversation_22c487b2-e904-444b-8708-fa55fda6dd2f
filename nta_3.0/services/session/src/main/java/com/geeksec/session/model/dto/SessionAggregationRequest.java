package com.geeksec.session.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会话聚合查询请求DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话聚合查询请求")
public class SessionAggregationRequest {
    
    @Schema(description = "开始时间", required = true, example = "2024-01-01T00:00:00")
    private LocalDateTime startTime;
    
    @Schema(description = "结束时间", required = true, example = "2024-01-01T23:59:59")
    private LocalDateTime endTime;
    
    @Schema(description = "聚合字段列表", required = true, example = "[\"src_ip\", \"dst_ip\", \"dst_port\", \"app_name\"]")
    @NotEmpty(message = "聚合字段不能为空")
    private List<String> groupByFields;
    
    @Schema(description = "统计字段列表", example = "[\"session_count\", \"total_bytes\", \"avg_duration\"]")
    private List<String> aggregateFields;
    
    @Schema(description = "过滤条件")
    private SessionQueryRequest filterCondition;
    
    @Schema(description = "排序字段", example = "session_count")
    private String orderBy = "session_count";
    
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String orderDirection = "DESC";
    
    @Schema(description = "返回数量限制", example = "100")
    @Min(value = 1, message = "返回数量必须大于0")
    private Integer limit = 100;
    
    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;
    
    @Schema(description = "每页大小", example = "20")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 20;
}

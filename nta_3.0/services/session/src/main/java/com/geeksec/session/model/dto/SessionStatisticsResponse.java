package com.geeksec.session.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 会话统计响应DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话统计响应")
public class SessionStatisticsResponse {
    
    @Schema(description = "统计时间范围开始", example = "2024-01-01T00:00:00")
    private LocalDateTime startTime;
    
    @Schema(description = "统计时间范围结束", example = "2024-01-01T23:59:59")
    private LocalDateTime endTime;
    
    @Schema(description = "总会话数", example = "10000")
    private Long totalSessions;
    
    @Schema(description = "总流量字节数", example = "1073741824")
    private Long totalBytes;
    
    @Schema(description = "平均会话持续时间(秒)", example = "120")
    private Double avgDuration;
    
    @Schema(description = "最大会话持续时间(秒)", example = "3600")
    private Integer maxDuration;
    
    @Schema(description = "最小会话持续时间(秒)", example = "1")
    private Integer minDuration;
    
    @Schema(description = "唯一源IP数量", example = "500")
    private Long uniqueSourceIps;
    
    @Schema(description = "唯一目标IP数量", example = "1000")
    private Long uniqueDestinationIps;
    
    @Schema(description = "唯一应用数量", example = "50")
    private Long uniqueApplications;
    
    @Schema(description = "内部流量会话数", example = "8000")
    private Long internalSessions;
    
    @Schema(description = "外部流量会话数", example = "2000")
    private Long externalSessions;
    
    @Schema(description = "协议分布统计")
    private Map<String, Long> protocolDistribution;
    
    @Schema(description = "应用分布统计")
    private Map<String, Long> applicationDistribution;
    
    @Schema(description = "端口分布统计")
    private Map<String, Long> portDistribution;
    
    @Schema(description = "标签分布统计")
    private Map<String, Long> labelDistribution;
    
    @Schema(description = "时间段分布统计")
    private Map<String, Long> timeDistribution;
}

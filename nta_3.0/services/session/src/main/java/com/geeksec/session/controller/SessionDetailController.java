package com.geeksec.session.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.session.model.dto.SessionDetailResponse;
import com.geeksec.session.model.dto.SessionRelationResponse;
import com.geeksec.session.model.dto.SessionRemarkRequest;
import com.geeksec.session.service.SessionDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 会话详情控制器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Tag(name = "会话详情管理", description = "提供会话详情查询、关联分析、备注管理等功能")
@RestController
@RequestMapping("/api/sessions/detail")
@RequiredArgsConstructor
public class SessionDetailController {
    
    private final SessionDetailService sessionDetailService;
    
    @Operation(summary = "获取会话详情", description = "获取指定会话的完整详情信息")
    @GetMapping("/{sessionId}")
    public ApiResponse<SessionDetailResponse> getSessionDetail(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话详情, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionDetail(sessionId));
    }
    
    @Operation(summary = "获取会话关联分析", description = "获取指定会话的关联分析结果")
    @GetMapping("/{sessionId}/relations")
    public ApiResponse<SessionRelationResponse> getSessionRelation(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "关联类型", example = "SAME_SOURCE_IP")
            @RequestParam(defaultValue = "SAME_SOURCE_IP") String relationType,
            @Parameter(description = "时间范围(小时)", example = "24")
            @RequestParam(defaultValue = "24") int timeRange,
            @Parameter(description = "返回数量限制", example = "20")
            @RequestParam(defaultValue = "20") int limit) {
        log.debug("获取会话关联分析, 会话ID: {}, 关联类型: {}", sessionId, relationType);
        return ApiResponse.success(sessionDetailService.getSessionRelation(sessionId, relationType, timeRange, limit));
    }
    
    @Operation(summary = "获取相同源IP的会话", description = "获取与指定会话具有相同源IP的其他会话")
    @GetMapping("/{sessionId}/relations/same-source-ip")
    public ApiResponse<SessionRelationResponse> getSameSourceIpSessions(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "时间范围(小时)", example = "24")
            @RequestParam(defaultValue = "24") int timeRange,
            @Parameter(description = "返回数量限制", example = "20")
            @RequestParam(defaultValue = "20") int limit) {
        log.debug("获取相同源IP的会话, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSameSourceIpSessions(sessionId, timeRange, limit));
    }
    
    @Operation(summary = "获取相同目标IP的会话", description = "获取与指定会话具有相同目标IP的其他会话")
    @GetMapping("/{sessionId}/relations/same-destination-ip")
    public ApiResponse<SessionRelationResponse> getSameDestinationIpSessions(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "时间范围(小时)", example = "24")
            @RequestParam(defaultValue = "24") int timeRange,
            @Parameter(description = "返回数量限制", example = "20")
            @RequestParam(defaultValue = "20") int limit) {
        log.debug("获取相同目标IP的会话, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSameDestinationIpSessions(sessionId, timeRange, limit));
    }
    
    @Operation(summary = "获取相同端口的会话", description = "获取与指定会话具有相同端口的其他会话")
    @GetMapping("/{sessionId}/relations/same-port")
    public ApiResponse<SessionRelationResponse> getSamePortSessions(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "端口类型", example = "dst_port")
            @RequestParam(defaultValue = "dst_port") String portType,
            @Parameter(description = "时间范围(小时)", example = "24")
            @RequestParam(defaultValue = "24") int timeRange,
            @Parameter(description = "返回数量限制", example = "20")
            @RequestParam(defaultValue = "20") int limit) {
        log.debug("获取相同端口的会话, 会话ID: {}, 端口类型: {}", sessionId, portType);
        return ApiResponse.success(sessionDetailService.getSamePortSessions(sessionId, portType, timeRange, limit));
    }
    
    @Operation(summary = "获取相同应用的会话", description = "获取与指定会话具有相同应用的其他会话")
    @GetMapping("/{sessionId}/relations/same-application")
    public ApiResponse<SessionRelationResponse> getSameApplicationSessions(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "时间范围(小时)", example = "24")
            @RequestParam(defaultValue = "24") int timeRange,
            @Parameter(description = "返回数量限制", example = "20")
            @RequestParam(defaultValue = "20") int limit) {
        log.debug("获取相同应用的会话, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSameApplicationSessions(sessionId, timeRange, limit));
    }
    
    @Operation(summary = "获取时间相近的会话", description = "获取与指定会话时间相近的其他会话")
    @GetMapping("/{sessionId}/relations/time-proximity")
    public ApiResponse<SessionRelationResponse> getTimeProximitySessions(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "时间窗口(分钟)", example = "30")
            @RequestParam(defaultValue = "30") int timeWindow,
            @Parameter(description = "返回数量限制", example = "20")
            @RequestParam(defaultValue = "20") int limit) {
        log.debug("获取时间相近的会话, 会话ID: {}, 时间窗口: {}分钟", sessionId, timeWindow);
        return ApiResponse.success(sessionDetailService.getTimeProximitySessions(sessionId, timeWindow, limit));
    }
    
    @Operation(summary = "获取会话协议栈分析", description = "获取指定会话的协议栈分析结果")
    @GetMapping("/{sessionId}/analysis/protocol-stack")
    public ApiResponse<Map<String, Object>> getSessionProtocolStackAnalysis(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话协议栈分析, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionProtocolStackAnalysis(sessionId));
    }
    
    @Operation(summary = "获取会话流量模式分析", description = "获取指定会话的流量模式分析结果")
    @GetMapping("/{sessionId}/analysis/traffic-pattern")
    public ApiResponse<Map<String, Object>> getSessionTrafficPatternAnalysis(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话流量模式分析, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionTrafficPatternAnalysis(sessionId));
    }
    
    @Operation(summary = "获取会话安全风险评估", description = "获取指定会话的安全风险评估结果")
    @GetMapping("/{sessionId}/analysis/security-risk")
    public ApiResponse<Map<String, Object>> getSessionSecurityRiskAssessment(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话安全风险评估, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionSecurityRiskAssessment(sessionId));
    }
    
    @Operation(summary = "获取会话地理位置信息", description = "获取指定会话的地理位置信息")
    @GetMapping("/{sessionId}/analysis/geolocation")
    public ApiResponse<Map<String, Object>> getSessionGeolocationInfo(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话地理位置信息, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionGeolocationInfo(sessionId));
    }
    
    @Operation(summary = "获取会话时间线分析", description = "获取指定会话的时间线分析结果")
    @GetMapping("/{sessionId}/analysis/timeline")
    public ApiResponse<List<Map<String, Object>>> getSessionTimelineAnalysis(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话时间线分析, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionTimelineAnalysis(sessionId));
    }
    
    @Operation(summary = "获取会话数据包分析", description = "获取指定会话的数据包分析结果")
    @GetMapping("/{sessionId}/analysis/packets")
    public ApiResponse<List<Map<String, Object>>> getSessionPacketAnalysis(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "返回数量限制", example = "50")
            @RequestParam(defaultValue = "50") int limit) {
        log.debug("获取会话数据包分析, 会话ID: {}, 限制: {}", sessionId, limit);
        return ApiResponse.success(sessionDetailService.getSessionPacketAnalysis(sessionId, limit));
    }
    
    @Operation(summary = "添加会话备注", description = "为指定会话添加备注信息")
    @PostMapping("/{sessionId}/remarks")
    public ApiResponse<Map<String, Object>> addSessionRemark(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "备注请求", required = true)
            @Valid @RequestBody SessionRemarkRequest remarkRequest) {
        log.debug("添加会话备注, 会话ID: {}", sessionId);
        remarkRequest.setSessionId(sessionId);
        return ApiResponse.success(sessionDetailService.addSessionRemark(remarkRequest));
    }
    
    @Operation(summary = "获取会话备注列表", description = "获取指定会话的所有备注信息")
    @GetMapping("/{sessionId}/remarks")
    public ApiResponse<List<Map<String, Object>>> getSessionRemarks(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话备注列表, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionRemarks(sessionId));
    }
    
    @Operation(summary = "删除会话备注", description = "删除指定的会话备注")
    @DeleteMapping("/{sessionId}/remarks/{remarkId}")
    public ApiResponse<Map<String, Object>> deleteSessionRemark(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "备注ID", required = true, example = "remark_001")
            @PathVariable String remarkId) {
        log.debug("删除会话备注, 会话ID: {}, 备注ID: {}", sessionId, remarkId);
        return ApiResponse.success(sessionDetailService.deleteSessionRemark(sessionId, remarkId));
    }
    
    @Operation(summary = "获取会话相似度分析", description = "分析两个会话之间的相似度")
    @GetMapping("/{sessionId}/similarity/{compareSessionId}")
    public ApiResponse<Map<String, Object>> getSessionSimilarityAnalysis(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "对比会话ID", required = true, example = "session_0987654321")
            @PathVariable String compareSessionId) {
        log.debug("获取会话相似度分析, 会话ID: {}, 对比会话ID: {}", sessionId, compareSessionId);
        return ApiResponse.success(sessionDetailService.getSessionSimilarityAnalysis(sessionId, compareSessionId));
    }
    
    @Operation(summary = "获取会话异常检测结果", description = "获取指定会话的异常检测结果")
    @GetMapping("/{sessionId}/anomaly")
    public ApiResponse<Map<String, Object>> getSessionAnomalyDetection(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId) {
        log.debug("获取会话异常检测结果, 会话ID: {}", sessionId);
        return ApiResponse.success(sessionDetailService.getSessionAnomalyDetection(sessionId));
    }
    
    @Operation(summary = "导出会话详情报告", description = "导出指定会话的详情报告")
    @PostMapping("/{sessionId}/export")
    public ApiResponse<Map<String, Object>> exportSessionDetailReport(
            @Parameter(description = "会话ID", required = true, example = "session_1234567890")
            @PathVariable String sessionId,
            @Parameter(description = "导出格式", example = "PDF")
            @RequestParam(defaultValue = "PDF") String format) {
        log.debug("导出会话详情报告, 会话ID: {}, 格式: {}", sessionId, format);
        return ApiResponse.success(sessionDetailService.exportSessionDetailReport(sessionId, format));
    }
}

package com.geeksec.session.service;

import com.geeksec.session.exception.SessionNotFoundException;
import com.geeksec.session.model.dto.SessionListResponse;
import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.mybatisflex.core.paginate.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话服务接口 - 基于Doris只读查询
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SessionService {

    /**
     * 根据ID获取会话详情
     *
     * @param id 会话ID
     * @return 会话实体
     * @throws SessionNotFoundException 当会话不存在时抛出异常
     */
    Session getSessionById(String id) throws SessionNotFoundException;

    /**
     * 分页查询会话列表
     *
     * @param queryRequest 查询条件
     * @return 分页结果
     */
    Page<SessionListResponse> querySessionList(SessionQueryRequest queryRequest);

    /**
     * 统计会话数量
     *
     * @param queryRequest 查询条件
     * @return 会话数量
     */
    long countSessions(SessionQueryRequest queryRequest);

    /**
     * 根据多个ID批量查询会话
     *
     * @param sessionIds 会话ID列表
     * @return 会话列表
     */
    List<Session> getSessionsByIds(List<String> sessionIds);

    /**
     * 查询会话统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param groupBy 分组字段
     * @return 统计结果
     */
    List<Map<String, Object>> getSessionStatistics(LocalDateTime startTime, LocalDateTime endTime, String groupBy);

    /**
     * 查询会话聚合信息
     *
     * @param queryRequest 查询条件
     * @return 聚合结果
     */
    Page<Map<String, Object>> getSessionAggregation(SessionQueryRequest queryRequest);

    /**
     * 查询热门源IP统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @return 源IP统计列表
     */
    List<Map<String, Object>> getTopSourceIps(LocalDateTime startTime, LocalDateTime endTime, int limit);

    /**
     * 查询热门目标IP统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @return 目标IP统计列表
     */
    List<Map<String, Object>> getTopDestinationIps(LocalDateTime startTime, LocalDateTime endTime, int limit);

    /**
     * 查询协议分布统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 协议分布统计
     */
    List<Map<String, Object>> getProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询应用分布统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 应用分布统计
     */
    List<Map<String, Object>> getApplicationDistribution(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询时间范围内的会话趋势
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔(分钟)
     * @return 会话趋势数据
     */
    List<Map<String, Object>> getSessionTrend(LocalDateTime startTime, LocalDateTime endTime, int interval);

    /**
     * 根据关键字搜索会话
     *
     * @param keyword 关键字
     * @param page 分页参数
     * @return 搜索结果
     */
    Page<SessionListResponse> searchSessions(String keyword, Page<SessionListResponse> page);

    /**
     * 检查会话是否存在
     *
     * @param id 会话ID
     * @return 是否存在
     */
    boolean existsById(String id);
}

package com.geeksec.session.model.entity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.handler.Fastjson2TypeHandler;

import lombok.Data;

/**
 * 会话实体类 - 对应Doris中的dwd_session_logs表
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Table("dwd_session_logs")
public class Session {

    // ==================== 基础会话标识字段 ====================

    /**
     * 会话ID
     */
    @Id
    private String sessionId;

    /**
     * 源IP地址
     */
    private String srcIp;

    /**
     * 目标IP地址
     */
    private String dstIp;

    /**
     * 源端口
     */
    private Integer srcPort;

    /**
     * 目标端口
     */
    private Integer dstPort;

    /**
     * IP协议号
     */
    private Integer protocol;

    /**
     * 会话开始时间
     */
    private LocalDateTime sessionStartTime;

    /**
     * 会话开始时间纳秒部分
     */
    private Integer sessionStartNsec;

    /**
     * 会话结束时间
     */
    private LocalDateTime sessionEndTime;

    /**
     * 会话结束时间纳秒部分
     */
    private Integer sessionEndNsec;

    /**
     * 服务器IP
     */
    private String serverIp;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 线程ID
     */
    private Integer threadId;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * 批次ID
     */
    private Integer batchId;

    // ==================== 会话基础信息 ====================

    /**
     * 源MAC地址
     */
    private String srcMac;

    /**
     * 目标MAC地址
     */
    private String dstMac;

    /**
     * 规则数量
     */
    private Integer ruleCount;

    /**
     * 规则级别
     */
    private Integer ruleLevel;

    /**
     * SYN二进制流(Base64编码)
     */
    private String synData;

    /**
     * SYN-ACK二进制流(Base64编码)
     */
    private String synAckData;

    /**
     * 规则消息
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private Map<String, Object> ruleMessages;

    // ==================== 会话标签信息 ====================

    /**
     * 会话标签ID数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> labels;

    // ==================== 会话统计信息 ====================

    /**
     * 源标识
     */
    private Integer srcTotalSign;

    /**
     * 目标标识
     */
    private Integer dstTotalSign;

    /**
     * 负载字节分布
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private Map<String, Object> payloadBytesDistribution;

    /**
     * 负载字节数
     */
    private Long payloadBytesCount;

    /**
     * 统计值
     */
    private Double distributionCsq;

    /**
     * 统计t值
     */
    private Double distributionCsqt;

    /**
     * 源包负载长度分布数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> srcPayloadLengthDistribution;

    /**
     * 目标包负载长度分布数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> dstPayloadLengthDistribution;

    /**
     * 源包时间间隔分布
     */
    private Integer srcDurationDistribution;

    /**
     * 目标包时间间隔分布
     */
    private Integer dstDurationDistribution;

    /**
     * 包时间间隔分布数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> durationDistribution;

    /**
     * 包协议栈数量
     */
    private Integer protocolStackCount;

    /**
     * 包协议栈
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private Map<String, Object> protocolStack;

    /**
     * 源IP是否为内部IP
     */
    private Boolean srcIsInternal;

    /**
     * 目标IP是否为内部IP
     */
    private Boolean dstIsInternal;

    /**
     * JSON扩展字段
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private Map<String, Object> extensionData;

    /**
     * 源MSS
     */
    private Integer srcMss;

    /**
     * 目标MSS
     */
    private Integer dstMss;

    /**
     * 源窗口扩展因子
     */
    private Integer srcWindowScale;

    /**
     * 目标窗口扩展因子
     */
    private Integer dstWindowScale;

    /**
     * 源负载最大长度
     */
    private Integer srcPayloadMaxLength;

    /**
     * 目标负载最大长度
     */
    private Integer dstPayloadMaxLength;

    /**
     * 源ACK负载最大长度
     */
    private Integer srcAckPayloadMaxLength;

    /**
     * 目标ACK负载最大长度
     */
    private Integer dstAckPayloadMaxLength;

    /**
     * 源ACK负载最小长度
     */
    private Integer srcAckPayloadMinLength;

    /**
     * 目标ACK负载最小长度
     */
    private Integer dstAckPayloadMinLength;

    /**
     * TCP连接信息数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> tcpConnectionInfo;

    /**
     * SYN序列号数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> synSequenceNumbers;

    /**
     * SYN序列号数量
     */
    private Integer synSequenceCount;

    /**
     * 源IP的ID偏移数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> srcIpIdOffsets;

    /**
     * 目标IP的ID偏移数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> dstIpIdOffsets;

    /**
     * SSL会话块密码数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> sslBlockCiphers;

    // ==================== 会话包统计信息 ====================

    /**
     * 源最大IP包长
     */
    private Integer srcMaxPacketLength;

    /**
     * 目标最大IP包长
     */
    private Integer dstMaxPacketLength;

    /**
     * 源包数
     */
    private Integer srcPacketCount;

    /**
     * 目标包数
     */
    private Integer dstPacketCount;

    /**
     * 源有负载的包数
     */
    private Integer srcPayloadPacketCount;

    /**
     * 目标有负载的包数
     */
    private Integer dstPayloadPacketCount;

    /**
     * 源字节数
     */
    private Long srcTotalBytes;

    /**
     * 目标字节数
     */
    private Long dstTotalBytes;

    /**
     * 源有负载的字节数
     */
    private Long srcPayloadBytes;

    /**
     * 目标有负载的字节数
     */
    private Long dstPayloadBytes;

    /**
     * 源Fin包数量
     */
    private Integer srcFinPacketCount;

    /**
     * 目标Fin包数量
     */
    private Integer dstFinPacketCount;

    /**
     * 源Rst包数量
     */
    private Integer srcRstPacketCount;

    /**
     * 目标Rst包数量
     */
    private Integer dstRstPacketCount;

    /**
     * 源Syn包数量
     */
    private Integer srcSynPacketCount;

    /**
     * 目标Syn包数量
     */
    private Integer dstSynPacketCount;

    /**
     * 源Syn包字节数
     */
    private Integer srcSynBytes;

    /**
     * 目标Syn包字节数
     */
    private Integer dstSynBytes;

    /**
     * 源IP:TTL最大值
     */
    private Integer srcTtlMax;

    /**
     * 目标IP:TTL最大值
     */
    private Integer dstTtlMax;

    /**
     * 源IP:TTL最小值
     */
    private Integer srcTtlMin;

    /**
     * 目标IP:TTL最小值
     */
    private Integer dstTtlMin;

    /**
     * 源包的最大时间间隔
     */
    private Integer srcDurationMax;

    /**
     * 目标包的最大时间间隔
     */
    private Integer dstDurationMax;

    /**
     * 源包的最小时间间隔
     */
    private Integer srcDurationMin;

    /**
     * 目标包的最小时间间隔
     */
    private Integer dstDurationMin;

    /**
     * 源乱序包数
     */
    private Integer srcDisorderPacketCount;

    /**
     * 目标乱序包数
     */
    private Integer dstDisorderPacketCount;

    /**
     * 源重发包数
     */
    private Integer srcResendPacketCount;

    /**
     * 目标重发包数
     */
    private Integer dstResendPacketCount;

    /**
     * 源丢包长度
     */
    private Integer srcLostPacketLength;

    /**
     * 目标丢包长度
     */
    private Integer dstLostPacketLength;

    /**
     * 源PSH包数
     */
    private Integer srcPshPacketCount;

    /**
     * 目标PSH包数
     */
    private Integer dstPshPacketCount;

    /**
     * 包协议数量
     */
    private Integer protocolPacketCount;

    /**
     * 未知协议包数量
     */
    private Integer unknownProtocolPacketCount;

    /**
     * syn包包含负载包数
     */
    private Integer synWithDataCount;

    /**
     * 源错包数量
     */
    private Integer srcBadPacketCount;

    /**
     * 目标错包数量
     */
    private Integer dstBadPacketCount;

    /**
     * 第几个负载包识别到应用
     */
    private Integer appDetectionPacketId;

    /**
     * 源包负载前4个
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<String> srcPayloadSamples;

    /**
     * 目标包负载前4个
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<String> dstPayloadSamples;

    /**
     * 包信息数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> packetInfo;

    // ==================== 协议指纹信息 ====================

    /**
     * TCP客户端指纹
     */
    private Long tcpClientFingerprint;

    /**
     * TCP服务端指纹
     */
    private Long tcpServerFingerprint;

    /**
     * HTTP请求指纹
     */
    private Long httpClientFingerprint;

    /**
     * HTTP应答指纹
     */
    private Long httpServerFingerprint;

    /**
     * SSL请求指纹
     */
    private Long sslClientFingerprint;

    /**
     * SSL应答指纹
     */
    private Long sslServerFingerprint;

    // ==================== TCP指纹特征详情 ====================

    // 客户端TCP指纹特征
    /**
     * 客户端TCP指纹特征-ECN IP ECT
     */
    private Boolean tcpClientEcnIpEct;

    /**
     * 客户端TCP指纹特征-DF设置为1时IPID值是否不为0
     */
    private Boolean tcpClientDfNonzeroIpid;

    /**
     * 客户端TCP指纹特征-CWR标志
     */
    private Boolean tcpClientFlagCwr;

    /**
     * 客户端TCP指纹特征-ECE标志
     */
    private Boolean tcpClientFlagEce;

    /**
     * 客户端TCP指纹特征-时间戳前四位是否为0
     */
    private Boolean tcpClientZeroTimestamp;

    /**
     * 客户端TCP指纹特征-TTL值
     */
    private Integer tcpClientTtl;

    /**
     * 客户端TCP指纹特征-EOL填充字节数
     */
    private Integer tcpClientEolPaddingBytes;

    /**
     * 客户端TCP指纹特征-窗口扩大因子
     */
    private Integer tcpClientWindowScale;

    /**
     * 客户端TCP指纹特征-窗口值与MSS比值
     */
    private Integer tcpClientWindowMssRatio;

    /**
     * 客户端TCP指纹特征-TCP选项布局
     */
    private String tcpClientOptionsLayout;

    // 服务端TCP指纹特征
    /**
     * 服务端TCP指纹特征-ECN IP ECT
     */
    private Boolean tcpServerEcnIpEct;

    /**
     * 服务端TCP指纹特征-DF设置为1时IPID值是否不为0
     */
    private Boolean tcpServerDfNonzeroIpid;

    /**
     * 服务端TCP指纹特征-CWR标志
     */
    private Boolean tcpServerFlagCwr;

    /**
     * 服务端TCP指纹特征-ECE标志
     */
    private Boolean tcpServerFlagEce;

    /**
     * 服务端TCP指纹特征-时间戳前四位是否为0
     */
    private Boolean tcpServerZeroTimestamp;

    /**
     * 服务端TCP指纹特征-TTL值
     */
    private Integer tcpServerTtl;

    /**
     * 服务端TCP指纹特征-EOL填充字节数
     */
    private Integer tcpServerEolPaddingBytes;

    /**
     * 服务端TCP指纹特征-窗口扩大因子
     */
    private Integer tcpServerWindowScale;

    /**
     * 服务端TCP指纹特征-窗口值与MSS比值
     */
    private Integer tcpServerWindowMssRatio;

    /**
     * 服务端TCP指纹特征-TCP选项布局
     */
    private String tcpServerOptionsLayout;

    // ==================== 会话扩展信息 ====================

    /**
     * 会话持续时间(秒)
     */
    private Integer sessionDuration;

    /**
     * 首包发送方IP
     */
    private String firstPacketSender;

    /**
     * 设备码
     */
    private Integer deviceId;

    /**
     * 首层协议
     */
    private Integer firstLayerProtocol;

    /**
     * 代理IP
     */
    private String proxyIp;

    /**
     * 代理端口
     */
    private Integer proxyPort;

    /**
     * 代理访问的真实域名
     */
    private String proxyRealHostname;

    /**
     * 代理类型
     */
    private Integer proxyType;

    /**
     * 开始处理时间
     */
    private LocalDateTime processingStartTime;

    /**
     * 结束处理时间
     */
    private LocalDateTime processingEndTime;

    /**
     * 规则标签
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> ruleLabels;

    /**
     * 端口列表
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Integer> portList;

    // ==================== 协议元数据数组 ====================

    /**
     * HTTP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> httpProtocols;

    /**
     * SSL/TLS协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> sslProtocols;

    /**
     * DNS协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> dnsProtocols;

    /**
     * SSH协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> sshProtocols;

    /**
     * VNC协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> vncProtocols;

    /**
     * TELNET协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> telnetProtocols;

    /**
     * RLOGIN协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> rloginProtocols;

    /**
     * RDP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> rdpProtocols;

    /**
     * ICMP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> icmpProtocols;

    /**
     * NTP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> ntpProtocols;

    /**
     * XDMCP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> xdmcpProtocols;

    /**
     * S7协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> s7Protocols;

    /**
     * Modbus协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> modbusProtocols;

    /**
     * IEC104协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> iec104Protocols;

    /**
     * EIP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> eipProtocols;

    /**
     * OPC协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> opcProtocols;

    /**
     * ESP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> espProtocols;

    /**
     * L2TP协议元数据数组
     */
    @Column(typeHandler = Fastjson2TypeHandler.class)
    private List<Map<String, Object>> l2tpProtocols;

    // ==================== 系统字段 ====================

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;
}

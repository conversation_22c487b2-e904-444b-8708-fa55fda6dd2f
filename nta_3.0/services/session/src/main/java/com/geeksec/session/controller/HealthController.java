package com.geeksec.session.controller;

import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Tag(name = "健康检查", description = "提供服务健康状态检查")
@RestController
@RequestMapping("/api")
public class HealthController extends BaseController {
    
    @Value("${spring.application.name:session-service}")
    private String applicationName;
    
    @Value("${spring.profiles.active:default}")
    private String activeProfile;
    
    @Operation(summary = "健康检查", description = "检查服务是否正常运行")
    @GetMapping("/health")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("service", applicationName);
        healthInfo.put("profile", activeProfile);
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("version", "3.0.0");

        // 检查各组件状态
        Map<String, String> components = new HashMap<>();
        components.put("database", "UP");
        components.put("cache", "UP");
        components.put("message_queue", "UP");
        healthInfo.put("components", components);

        return success(healthInfo);
    }
    
    @Operation(summary = "服务信息", description = "获取服务基本信息")
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", applicationName);
        info.put("version", "3.0.0");
        info.put("description", "NTA 3.0 会话管理服务");
        info.put("profile", activeProfile);
        info.put("build_time", "2024-01-01T00:00:00");
        info.put("java_version", System.getProperty("java.version"));
        info.put("spring_boot_version", "3.0.7");
        
        return success(info);
    }
    
    @Operation(summary = "就绪检查", description = "检查服务是否准备好接收请求")
    @GetMapping("/ready")
    public ApiResponse<Map<String, Object>> ready() {
        Map<String, Object> readiness = new HashMap<>();
        readiness.put("status", "READY");
        readiness.put("timestamp", LocalDateTime.now());
        readiness.put("message", "服务已准备好接收请求");
        
        return success(readiness);
    }
    
    @Operation(summary = "存活检查", description = "检查服务是否存活")
    @GetMapping("/live")
    public ApiResponse<Map<String, Object>> live() {
        Map<String, Object> liveness = new HashMap<>();
        liveness.put("status", "ALIVE");
        liveness.put("timestamp", LocalDateTime.now());
        liveness.put("uptime", getUptime());
        
        return success(liveness);
    }
    
    /**
     * 获取服务运行时间
     */
    private String getUptime() {
        long uptimeMs = System.currentTimeMillis() - getStartTime();
        long seconds = uptimeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        return String.format("%d天%d小时%d分钟%d秒", 
                days, hours % 24, minutes % 60, seconds % 60);
    }
    
    /**
     * 获取服务启动时间（简化实现）
     */
    private long getStartTime() {
        // 这里应该从实际的启动时间获取，这里简化为当前时间减去1小时
        return System.currentTimeMillis() - 3600000;
    }
}

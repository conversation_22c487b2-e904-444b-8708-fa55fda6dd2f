package com.geeksec.session.service;

import com.geeksec.session.model.dto.*;
import com.mybatisflex.core.paginate.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话分析统计服务接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface SessionAnalyticsService {
    
    /**
     * 获取会话统计概览
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param filterCondition 过滤条件
     * @return 统计概览
     */
    SessionStatisticsResponse getSessionStatistics(LocalDateTime startTime, LocalDateTime endTime, SessionQueryRequest filterCondition);
    
    /**
     * 获取会话聚合分析
     * 
     * @param request 聚合查询请求
     * @return 聚合分析结果
     */
    Page<Map<String, Object>> getSessionAggregation(SessionAggregationRequest request);
    
    /**
     * 获取会话趋势分析
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔(分钟)
     * @param filterCondition 过滤条件
     * @return 趋势分析结果
     */
    List<SessionTrendResponse> getSessionTrend(LocalDateTime startTime, LocalDateTime endTime, int interval, SessionQueryRequest filterCondition);
    
    /**
     * 获取热门源IP排行
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @param orderBy 排序字段 (session_count, total_bytes)
     * @return 源IP排行
     */
    List<Map<String, Object>> getTopSourceIpRanking(LocalDateTime startTime, LocalDateTime endTime, int limit, String orderBy);
    
    /**
     * 获取热门目标IP排行
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @param orderBy 排序字段 (session_count, total_bytes)
     * @return 目标IP排行
     */
    List<Map<String, Object>> getTopDestinationIpRanking(LocalDateTime startTime, LocalDateTime endTime, int limit, String orderBy);
    
    /**
     * 获取热门端口排行
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 返回数量限制
     * @param portType 端口类型 (src_port, dst_port)
     * @return 端口排行
     */
    List<Map<String, Object>> getTopPortRanking(LocalDateTime startTime, LocalDateTime endTime, int limit, String portType);
    
    /**
     * 获取应用协议分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 应用协议分布
     */
    List<Map<String, Object>> getApplicationProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取网络协议分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 网络协议分布
     */
    List<Map<String, Object>> getNetworkProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取流量方向分析
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流量方向分析
     */
    Map<String, Object> getTrafficDirectionAnalysis(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取会话持续时间分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 持续时间分布
     */
    List<Map<String, Object>> getSessionDurationDistribution(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取数据包大小分布
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据包大小分布
     */
    List<Map<String, Object>> getPacketSizeDistribution(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取标签使用统计
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 标签使用统计
     */
    List<Map<String, Object>> getLabelUsageStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取异常会话检测
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param threshold 异常阈值配置
     * @return 异常会话列表
     */
    List<Map<String, Object>> getAnomalousSessionDetection(LocalDateTime startTime, LocalDateTime endTime, Map<String, Object> threshold);
    
    /**
     * 获取网络拓扑分析
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param depth 分析深度
     * @return 网络拓扑数据
     */
    Map<String, Object> getNetworkTopologyAnalysis(LocalDateTime startTime, LocalDateTime endTime, int depth);
    
    /**
     * 获取实时会话监控
     * 
     * @param minutes 监控时间窗口(分钟)
     * @return 实时监控数据
     */
    Map<String, Object> getRealTimeSessionMonitoring(int minutes);
}

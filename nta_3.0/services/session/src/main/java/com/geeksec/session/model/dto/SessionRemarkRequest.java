package com.geeksec.session.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 会话备注请求DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话备注请求")
public class SessionRemarkRequest {
    
    @Schema(description = "会话ID", required = true, example = "session_1234567890")
    @NotBlank(message = "会话ID不能为空")
    private String sessionId;
    
    @Schema(description = "备注内容", required = true, example = "这是一个可疑的网络连接，需要进一步分析")
    @NotBlank(message = "备注内容不能为空")
    @Size(max = 1000, message = "备注内容不能超过1000个字符")
    private String remarkContent;
    
    @Schema(description = "备注类型", example = "SECURITY_ANALYSIS")
    private String remarkType;
    
    @Schema(description = "备注级别", example = "HIGH")
    private String remarkLevel;
    
    @Schema(description = "操作人", example = "admin")
    private String operator;
}

package com.geeksec.session.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会话关联分析响应DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Schema(description = "会话关联分析响应")
public class SessionRelationResponse {
    
    @Schema(description = "中心会话ID", example = "session_1234567890")
    private String centerSessionId;
    
    @Schema(description = "关联类型", example = "SAME_SOURCE_IP")
    private String relationType;
    
    @Schema(description = "关联描述", example = "相同源IP的会话")
    private String relationDescription;
    
    @Schema(description = "关联会话列表")
    private List<RelatedSession> relatedSessions;
    
    @Schema(description = "关联会话总数", example = "25")
    private Integer totalRelatedSessions;
    
    @Schema(description = "分析时间范围开始", example = "2024-01-01T00:00:00")
    private LocalDateTime analysisStartTime;
    
    @Schema(description = "分析时间范围结束", example = "2024-01-01T23:59:59")
    private LocalDateTime analysisEndTime;
    
    /**
     * 关联会话信息
     */
    @Data
    @Schema(description = "关联会话信息")
    public static class RelatedSession {
        
        @Schema(description = "会话ID", example = "session_0987654321")
        private String sessionId;
        
        @Schema(description = "源IP地址", example = "*************")
        private String srcIp;
        
        @Schema(description = "目标IP地址", example = "********")
        private String dstIp;
        
        @Schema(description = "源端口", example = "8081")
        private Integer srcPort;
        
        @Schema(description = "目标端口", example = "80")
        private Integer dstPort;
        
        @Schema(description = "应用名称", example = "HTTP")
        private String appName;
        
        @Schema(description = "会话开始时间", example = "2024-01-01T12:01:00")
        private LocalDateTime sessionStartTime;
        
        @Schema(description = "会话持续时间(秒)", example = "120")
        private Integer sessionDuration;
        
        @Schema(description = "总字节数", example = "2048000")
        private Long totalBytes;
        
        @Schema(description = "关联强度", example = "0.85")
        private Double relationStrength;
        
        @Schema(description = "关联原因", example = "相同源IP且时间相近")
        private String relationReason;
        
        @Schema(description = "标签列表", example = "[\"正常流量\"]")
        private List<String> labels;
    }
}

package com.geeksec.session.service.impl;

import com.geeksec.session.exception.SessionNotFoundException;
import com.geeksec.session.model.dto.SessionListResponse;
import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.geeksec.session.repository.SessionRepository;
import com.geeksec.session.service.SessionService;
import com.mybatisflex.core.paginate.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会话服务实现类 - 基于Doris只读查询
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SessionServiceImpl implements SessionService {

    private final SessionRepository sessionRepository;

    @Override
    public Session getSessionById(String id) throws SessionNotFoundException {
        log.debug("根据ID查询会话详情: {}", id);

        Session session = sessionRepository.getSessionById(id);
        if (session == null) {
            throw SessionNotFoundException.withId(id);
        }
        return session;
    }

    @Override
    public Page<SessionListResponse> querySessionList(SessionQueryRequest queryRequest) {
        log.debug("分页查询会话列表, 参数: {}", queryRequest);

        return sessionRepository.querySessionList(queryRequest);
    }

    @Override
    public long countSessions(SessionQueryRequest queryRequest) {
        log.debug("统计会话数量, 参数: {}", queryRequest);

        return sessionRepository.countSessions(queryRequest);
    }

    @Override
    public List<Session> getSessionsByIds(List<String> sessionIds) {
        log.debug("批量查询会话, IDs: {}", sessionIds);

        if (sessionIds == null || sessionIds.isEmpty()) {
            return List.of();
        }

        return sessionRepository.getSessionsByIds(sessionIds);
    }

    @Override
    public List<Map<String, Object>> getSessionStatistics(LocalDateTime startTime, LocalDateTime endTime, String groupBy) {
        log.debug("查询会话统计信息, 开始时间: {}, 结束时间: {}, 分组字段: {}", startTime, endTime, groupBy);

        return sessionRepository.getSessionStatistics(startTime, endTime, groupBy);
    }

    @Override
    public Page<Map<String, Object>> getSessionAggregation(SessionQueryRequest queryRequest) {
        log.debug("查询会话聚合信息, 参数: {}", queryRequest);

        return sessionRepository.getSessionAggregation(queryRequest);
    }

    @Override
    public List<Map<String, Object>> getTopSourceIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        log.debug("查询热门源IP统计, 开始时间: {}, 结束时间: {}, 限制数量: {}", startTime, endTime, limit);

        return sessionRepository.getTopSourceIps(startTime, endTime, limit);
    }

    @Override
    public List<Map<String, Object>> getTopDestinationIps(LocalDateTime startTime, LocalDateTime endTime, int limit) {
        log.debug("查询热门目标IP统计, 开始时间: {}, 结束时间: {}, 限制数量: {}", startTime, endTime, limit);

        return sessionRepository.getTopDestinationIps(startTime, endTime, limit);
    }

    @Override
    public List<Map<String, Object>> getProtocolDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询协议分布统计, 开始时间: {}, 结束时间: {}", startTime, endTime);

        return sessionRepository.getProtocolDistribution(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getApplicationDistribution(LocalDateTime startTime, LocalDateTime endTime) {
        log.debug("查询应用分布统计, 开始时间: {}, 结束时间: {}", startTime, endTime);

        return sessionRepository.getApplicationDistribution(startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getSessionTrend(LocalDateTime startTime, LocalDateTime endTime, int interval) {
        log.debug("查询会话趋势, 开始时间: {}, 结束时间: {}, 间隔: {}分钟", startTime, endTime, interval);

        return sessionRepository.getSessionTrend(startTime, endTime, interval);
    }

    @Override
    public Page<SessionListResponse> searchSessions(String keyword, Page<SessionListResponse> page) {
        log.debug("搜索会话, 关键字: {}", keyword);

        return sessionRepository.searchSessions(keyword, page);
    }

    @Override
    public boolean existsById(String id) {
        log.debug("检查会话是否存在, ID: {}", id);

        try {
            Session session = sessionRepository.getSessionById(id);
            return session != null;
        } catch (Exception e) {
            log.debug("会话不存在, ID: {}", id);
            return false;
        }
    }
}

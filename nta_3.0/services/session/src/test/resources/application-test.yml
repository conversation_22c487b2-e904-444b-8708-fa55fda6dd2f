# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据源配置
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: ""
    
  # MyBatis-Flex 配置
  mybatis-flex:
    mapper-locations: classpath*:/mapper/*.xml
    type-aliases-package: com.geeksec.session.model.entity
    configuration:
      map-underscore-to-camel-case: true
      cache-enabled: false
      
  # 日志配置
  logging:
    level:
      com.geeksec.session: DEBUG
      org.springframework.web: DEBUG
      
# 测试专用配置
test:
  mock:
    enabled: true
  data:
    init: true

package com.geeksec.session.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.session.factory.TestDataFactory;
import com.geeksec.session.model.dto.SessionListResponse;
import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;
import com.geeksec.session.service.SessionService;
import com.mybatisflex.core.paginate.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SessionController 集成测试
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@WebMvcTest(SessionController.class)
@DisplayName("会话控制器测试")
class SessionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private SessionService sessionService;

    private Session testSession;
    private SessionQueryRequest testQueryRequest;

    @BeforeEach
    void setUp() {
        testSession = TestDataFactory.createTestSession();
        testQueryRequest = TestDataFactory.createTestQueryRequest();
    }

    @Test
    @DisplayName("获取会话详情 - 成功")
    void getSessionById_Success() throws Exception {
        // Given
        String sessionId = "session_test_001";
        when(sessionService.getSessionById(sessionId)).thenReturn(testSession);

        // When & Then
        mockMvc.perform(get("/api/sessions/{id}", sessionId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.sessionId").value(sessionId))
                .andExpect(jsonPath("$.data.srcIp").value("*************"))
                .andExpect(jsonPath("$.data.dstIp").value("********"));

        verify(sessionService).getSessionById(sessionId);
    }

    @Test
    @DisplayName("获取会话详情 - 会话不存在")
    void getSessionById_NotFound() throws Exception {
        // Given
        String sessionId = "non_existent_session";
        when(sessionService.getSessionById(sessionId))
                .thenThrow(new com.geeksec.session.exception.SessionNotFoundException("会话不存在: " + sessionId));

        // When & Then
        mockMvc.perform(get("/api/sessions/{id}", sessionId))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("会话不存在: " + sessionId));

        verify(sessionService).getSessionById(sessionId);
    }

    @Test
    @DisplayName("分页查询会话列表 - 成功")
    void querySessionList_Success() throws Exception {
        // Given
        Page<SessionListResponse> mockPage = new Page<>(1, 20);
        mockPage.setTotal(100L);
        mockPage.setRecords(List.of(new SessionListResponse()));

        when(sessionService.querySessionList(any(SessionQueryRequest.class)))
                .thenReturn(mockPage);

        // When & Then
        mockMvc.perform(post("/api/sessions/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testQueryRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.total").value(100))
                .andExpect(jsonPath("$.data.records").isArray())
                .andExpect(jsonPath("$.data.records").isNotEmpty());

        verify(sessionService).querySessionList(any(SessionQueryRequest.class));
    }

    @Test
    @DisplayName("统计会话数量 - 成功")
    void countSessions_Success() throws Exception {
        // Given
        long expectedCount = 1500L;
        when(sessionService.countSessions(any(SessionQueryRequest.class)))
                .thenReturn(expectedCount);

        // When & Then
        mockMvc.perform(post("/api/sessions/count")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testQueryRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(expectedCount));

        verify(sessionService).countSessions(any(SessionQueryRequest.class));
    }

    @Test
    @DisplayName("批量查询会话 - 成功")
    void getSessionsByIds_Success() throws Exception {
        // Given
        List<String> sessionIds = List.of("session_001", "session_002", "session_003");
        List<Session> mockSessions = TestDataFactory.createTestSessions(3);

        when(sessionService.getSessionsByIds(sessionIds)).thenReturn(mockSessions);

        // When & Then
        mockMvc.perform(post("/api/sessions/batch")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(sessionIds)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(3));

        verify(sessionService).getSessionsByIds(sessionIds);
    }
}

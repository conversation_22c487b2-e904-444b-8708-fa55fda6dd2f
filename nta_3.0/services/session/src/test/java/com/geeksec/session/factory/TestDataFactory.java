package com.geeksec.session.factory;

import com.geeksec.session.model.dto.SessionQueryRequest;
import com.geeksec.session.model.entity.Session;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 测试数据工厂
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public class TestDataFactory {
    
    /**
     * 创建测试会话实体
     */
    public static Session createTestSession() {
        return createTestSession("session_test_001");
    }
    
    /**
     * 创建指定ID的测试会话实体
     */
    public static Session createTestSession(String sessionId) {
        Session session = new Session();
        session.setSessionId(sessionId);
        session.setSrcIp("*************");
        session.setDstIp("********");
        session.setSrcPort(8080);
        session.setDstPort(443);
        session.setProtocol(6); // TCP
        session.setAppName("HTTPS");
        session.setAppId(1001);
        session.setSessionStartTime(LocalDateTime.now().minusMinutes(30));
        session.setSessionEndTime(LocalDateTime.now().minusMinutes(25));
        session.setSessionDuration(300); // 5分钟
        session.setSrcTotalBytes(1024000L);
        session.setDstTotalBytes(2048000L);
        session.setSrcPayloadBytes(512000L);
        session.setDstPayloadBytes(1024000L);
        session.setSrcPacketCount(1500);
        session.setDstPacketCount(1200);
        session.setSrcPayloadPacketCount(800);
        session.setDstPayloadPacketCount(600);
        session.setSrcIsInternal(true);
        session.setDstIsInternal(false);
        session.setTaskId(1);
        session.setDeviceId(100);
        session.setThreadId(1);
        session.setBatchId(1001);
        session.setLabels(List.of(1, 2)); // 标签ID列表
        session.setRuleLevel(3);
        session.setRuleCount(2);
        session.setCreateTime(LocalDateTime.now().minusMinutes(30));
        session.setUpdateTime(LocalDateTime.now().minusMinutes(25));
        
        return session;
    }
    
    /**
     * 创建测试会话查询请求
     */
    public static SessionQueryRequest createTestQueryRequest() {
        SessionQueryRequest request = new SessionQueryRequest();
        request.setSrcIp("*************");
        request.setDstIp("********");
        request.setProtocol(6);
        request.setAppName("HTTPS");
        request.setStartTime(LocalDateTime.now().minusHours(1));
        request.setEndTime(LocalDateTime.now());
        request.setPageNumber(1);
        request.setPageSize(20);
        request.setSortBy("sessionStartTime");
        request.setSortOrder("desc");
        
        return request;
    }
    
    /**
     * 创建多个测试会话实体
     */
    public static List<Session> createTestSessions(int count) {
        return java.util.stream.IntStream.range(1, count + 1)
                .mapToObj(i -> createTestSession("session_test_" + String.format("%03d", i)))
                .peek(session -> {
                    // 为每个会话设置不同的IP和端口
                    session.setSrcIp("192.168.1." + (100 + (session.getSessionId().hashCode() % 50)));
                    session.setDstIp("10.0.0." + (1 + (session.getSessionId().hashCode() % 100)));
                    session.setSrcPort(8000 + (session.getSessionId().hashCode() % 1000));
                    session.setDstPort(80 + (session.getSessionId().hashCode() % 100));
                })
                .toList();
    }
    
    /**
     * 创建HTTP会话
     */
    public static Session createHttpSession() {
        Session session = createTestSession("session_http_001");
        session.setDstPort(80);
        session.setAppName("HTTP");
        session.setAppId(1000);
        return session;
    }
    
    /**
     * 创建HTTPS会话
     */
    public static Session createHttpsSession() {
        Session session = createTestSession("session_https_001");
        session.setDstPort(443);
        session.setAppName("HTTPS");
        session.setAppId(1001);
        return session;
    }
    
    /**
     * 创建DNS会话
     */
    public static Session createDnsSession() {
        Session session = createTestSession("session_dns_001");
        session.setProtocol(17); // UDP
        session.setDstPort(53);
        session.setAppName("DNS");
        session.setAppId(1002);
        return session;
    }
    
    /**
     * 创建SSH会话
     */
    public static Session createSshSession() {
        Session session = createTestSession("session_ssh_001");
        session.setDstPort(22);
        session.setAppName("SSH");
        session.setAppId(1003);
        return session;
    }
    
    /**
     * 创建内部网络会话
     */
    public static Session createInternalSession() {
        Session session = createTestSession("session_internal_001");
        session.setSrcIp("*************");
        session.setDstIp("*************");
        session.setSrcIsInternal(true);
        session.setDstIsInternal(true);
        return session;
    }
    
    /**
     * 创建外部网络会话
     */
    public static Session createExternalSession() {
        Session session = createTestSession("session_external_001");
        session.setSrcIp("*************");
        session.setDstIp("*******");
        session.setSrcIsInternal(true);
        session.setDstIsInternal(false);
        return session;
    }
    
    /**
     * 创建高风险会话
     */
    public static Session createHighRiskSession() {
        Session session = createTestSession("session_high_risk_001");
        session.setRuleLevel(8);
        session.setRuleCount(5);
        session.setLabels(List.of(1, 2, 3)); // 多个安全标签
        return session;
    }
    
    /**
     * 创建长时间会话
     */
    public static Session createLongDurationSession() {
        Session session = createTestSession("session_long_001");
        session.setSessionStartTime(LocalDateTime.now().minusHours(2));
        session.setSessionEndTime(LocalDateTime.now().minusMinutes(30));
        session.setSessionDuration(5400); // 90分钟
        return session;
    }
    
    /**
     * 创建大流量会话
     */
    public static Session createHighTrafficSession() {
        Session session = createTestSession("session_high_traffic_001");
        session.setSrcTotalBytes(100 * 1024 * 1024L); // 100MB
        session.setDstTotalBytes(200 * 1024 * 1024L); // 200MB
        session.setSrcPayloadBytes(80 * 1024 * 1024L);
        session.setDstPayloadBytes(160 * 1024 * 1024L);
        session.setSrcPacketCount(50000);
        session.setDstPacketCount(40000);
        return session;
    }
}

package com.geeksec.knowledgebase.integration;

import com.geeksec.knowledgebase.KnowledgeBaseApplication;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * KnowledgeBaseClient 兼容性测试
 * 
 * 验证所有接口是否与 KnowledgeBaseClient 的调用兼容
 *
 * <AUTHOR>
 */
@SpringBootTest(classes = KnowledgeBaseApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class KnowledgeBaseClientCompatibilityTest {

    @LocalServerPort
    private int port;

    private HttpClient httpClient;
    private String baseUrl;

    @BeforeEach
    void setUp() {
        httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        baseUrl = "http://localhost:" + port + "/knowledge-base/api/v1";
    }

    // ==================== 威胁情报相关接口测试 ====================

    @Test
    void testGetThreatIntelligenceByName() throws IOException, InterruptedException {
        String url = baseUrl + "/threat-intelligence/name/test-threat";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    @Test
    void testGetDetectorConfig() throws IOException, InterruptedException {
        String url = baseUrl + "/detector-config/certificate-threat-detector";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    // ==================== 域名威胁检查接口测试 ====================

    @Test
    void testCheckMaliciousDomain() throws IOException, InterruptedException {
        String url = baseUrl + "/domain/check/example.com";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isMalicious\""));
    }

    @Test
    void testCheckMiningDomain() throws IOException, InterruptedException {
        String url = baseUrl + "/domain/mining/check/example.com";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isMining\""));
    }

    @Test
    void testCheckPopularDomain() throws IOException, InterruptedException {
        String url = baseUrl + "/domain/popular/check/google.com";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isPopular\""));
    }

    @Test
    void testCheckVideoDomain() throws IOException, InterruptedException {
        String url = baseUrl + "/domain/video/check/youtube.com";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isVideo\""));
    }

    @Test
    void testCheckValidTld() throws IOException, InterruptedException {
        String url = baseUrl + "/domain/tld/check/com";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isValid\""));
    }

    @Test
    void testGetNewGtldInfo() throws IOException, InterruptedException {
        String url = baseUrl + "/domain/new-gtld/check/tech";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    // ==================== C2威胁检查接口测试 ====================

    @Test
    void testCheckC2ThreatDomain() throws IOException, InterruptedException {
        String url = baseUrl + "/threat/c2/domain/check/example.com";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isC2Threat\""));
    }

    @Test
    void testCheckC2ThreatIp() throws IOException, InterruptedException {
        String url = baseUrl + "/threat/c2/ip/check/***********";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isC2Threat\""));
    }

    @Test
    void testCheckIocIp() throws IOException, InterruptedException {
        String url = baseUrl + "/threat/ioc/ip/check/***********";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isIoc\""));
    }

    // ==================== 地理位置接口测试 ====================

    @Test
    void testGetGeoLocation() throws IOException, InterruptedException {
        String url = baseUrl + "/geo/ip/*******";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    @Test
    void testGetCountryByCode() throws IOException, InterruptedException {
        String url = baseUrl + "/geo/country/code/US";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    @Test
    void testGetCountryByName() throws IOException, InterruptedException {
        String url = baseUrl + "/geo/country/name/China";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    @Test
    void testGetCompanySuffixesByCountry() throws IOException, InterruptedException {
        String url = baseUrl + "/geo/company-suffix/country/US";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"suffixes\""));
    }

    @Test
    void testCheckCompanySuffix() throws IOException, InterruptedException {
        String url = baseUrl + "/geo/company-suffix/check/Inc";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isValid\""));
    }

    // ==================== 证书相关接口测试 ====================

    @Test
    void testGetOidInfo() throws IOException, InterruptedException {
        String url = baseUrl + "/certificate/oid/1.2.3.4";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    @Test
    void testCheckKnownOid() throws IOException, InterruptedException {
        String url = baseUrl + "/certificate/oid/check/1.2.3.4";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isKnown\""));
    }

    @Test
    void testCheckFreeCertificateAuthority() throws IOException, InterruptedException {
        String url = baseUrl + "/certificate/ca/check/Let's Encrypt";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"isFree\""));
    }

    @Test
    void testCheckTrustedCaCertificate() throws IOException, InterruptedException {
        String url = baseUrl + "/certificate/trusted-ca/check/abc123";
        HttpResponse<String> response = sendGetRequest(url);
        
        assertEquals(200, response.statusCode());
        assertNotNull(response.body());
        assertTrue(response.body().contains("\"success\":true"));
    }

    // ==================== 辅助方法 ====================

    private HttpResponse<String> sendGetRequest(String url) throws IOException, InterruptedException {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Accept", "application/json")
                .timeout(Duration.ofSeconds(30))
                .GET()
                .build();

        return httpClient.send(request, HttpResponse.BodyHandlers.ofString());
    }
}

spring:
  application:
    name: knowledge-base-test
  
  # 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 50
        order_inserts: true
        order_updates: true
    
  # Redis测试配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: 6379
      database: 1  # 使用不同的数据库进行测试
      
  # 缓存配置
  cache:
    type: simple
    
# 测试配置
knowledge-base:
  # GeoIP测试配置
  geo-ip:
    enabled: true
    database-path: ${user.home}/.geeksec/geoip-test
    city-db-name: GeoLite2-City-Test.mmdb
    asn-db-name: GeoLite2-ASN-Test.mmdb
    cache-ttl: 300  # 5分钟
    refresh-cron: "0 0 3 * * ?"  # 每天凌晨3点刷新
    
  # 其他测试配置...
  trancobase:
    data-file: classpath:data/tranco/test_tranco.csv
    cache-ttl: 60  # 1分钟
    refresh-cron: "0 0/5 * * * ?"  # 每5分钟刷新一次
    
  domain-whois:
    data-file: classpath:data/whois/test_whois.csv
    cache-ttl: 60  # 1分钟
    refresh-cron: "0 0/5 * * * ?"  # 每5分钟刷新一次
    
  fingerprint:
    data-file: classpath:data/fingerprint/test_fingerprint.csv
    cache-ttl: 60  # 1分钟
    refresh-cron: "0 0/5 * * * ?"  # 每5分钟刷新一次

# 测试数据初始化
spring:
  sql:
    init:
      mode: always
      schema-locations: classpath:db/test-init.sql
      data-locations: classpath:db/test-data.sql

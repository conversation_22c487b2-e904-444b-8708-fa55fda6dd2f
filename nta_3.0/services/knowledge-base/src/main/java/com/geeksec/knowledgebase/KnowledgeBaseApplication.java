package com.geeksec.knowledgebase;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 知识库服务启动类
 *
 * 提供威胁情报、检测规则、地理位置等知识数据的统一管理和API访问
 *
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.geeksec.knowledgebase", "com.geeksec.common"})
@EnableCaching
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@MapperScan("com.geeksec.knowledgebase.repository.mapper")
public class KnowledgeBaseApplication {

    public static void main(String[] args) {
        SpringApplication.run(KnowledgeBaseApplication.class, args);
    }
}

package com.geeksec.knowledgebase.repository.mapper;

import com.geeksec.knowledgebase.domain.entity.FingerprintKnowledge;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Optional;

/**
 * 指纹知识 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface FingerprintKnowledgeMapper extends BaseMapper<FingerprintKnowledge> {

    /**
     * 根据指纹查找知识
     */
    default Optional<FingerprintKnowledge> findByFingerprint(String fingerprint) {
        FingerprintKnowledge knowledge = selectOneByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getFingerprint).eq(fingerprint)
                .and(FingerprintKnowledge::isActive).eq(true));
        return Optional.ofNullable(knowledge);
    }

    /**
     * 根据JA3指纹查找知识
     */
    default Optional<FingerprintKnowledge> findByJa3(String ja3) {
        FingerprintKnowledge knowledge = selectOneByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getJa3).eq(ja3)
                .and(FingerprintKnowledge::isActive).eq(true));
        return Optional.ofNullable(knowledge);
    }

    /**
     * 根据类型查找指纹知识列表
     */
    default List<FingerprintKnowledge> findByType(String type) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getType).eq(type)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据应用名称查找指纹知识列表
     */
    default List<FingerprintKnowledge> findByApplicationContainingIgnoreCase(String application) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getApplication).like(application)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据厂商查找指纹知识列表
     */
    default List<FingerprintKnowledge> findByVendorContainingIgnoreCase(String vendor) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getVendor).like(vendor)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据指纹列表批量查找指纹知识
     */
    default List<FingerprintKnowledge> batchFindByFingerprints(List<String> fingerprints) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getFingerprint).in(fingerprints)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据JA3指纹列表批量查找指纹知识
     */
    default List<FingerprintKnowledge> batchFindByJa3s(List<String> ja3s) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getJa3).in(ja3s)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据操作系统查找指纹知识
     */
    default List<FingerprintKnowledge> findByOs(String os) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getOs).eq(os)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据设备类型查找指纹知识
     */
    default List<FingerprintKnowledge> findByDeviceType(String deviceType) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getDeviceType).eq(deviceType)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据置信度范围查找指纹知识
     */
    default List<FingerprintKnowledge> findByConfidenceBetween(Integer minConfidence, Integer maxConfidence) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getConfidence).between(minConfidence, maxConfidence)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 根据关键词搜索指纹知识
     */
    default List<FingerprintKnowledge> searchByKeyword(String keyword) {
        return selectListByQuery(QueryWrapper.create()
                .where(FingerprintKnowledge::getApplication).like(keyword)
                .or(FingerprintKnowledge::getVendor).like(keyword)
                .or(FingerprintKnowledge::getDescription).like(keyword)
                .and(FingerprintKnowledge::isActive).eq(true));
    }

    /**
     * 获取所有厂商列表
     */
    default List<String> findDistinctVendors() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 获取所有应用列表
     */
    default List<String> findDistinctApplications() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 获取所有类型列表
     */
    default List<String> findDistinctTypes() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 统计各厂商的指纹数量
     */
    default List<Object[]> countByVendor() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 统计各类型的指纹数量
     */
    default List<Object[]> countByType() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }
}

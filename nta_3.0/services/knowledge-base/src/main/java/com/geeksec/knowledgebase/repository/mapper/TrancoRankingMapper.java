package com.geeksec.knowledgebase.repository.mapper;

import com.geeksec.knowledgebase.domain.entity.TrancoRanking;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Optional;

/**
 * Tranco排名 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface TrancoRankingMapper extends BaseMapper<TrancoRanking> {

    /**
     * 根据域名查询排名信息
     */
    default Optional<TrancoRanking> findByDomain(String domain) {
        TrancoRanking ranking = selectOneByQuery(QueryWrapper.create()
                .where(TrancoRanking::getDomain).eq(domain));
        return Optional.ofNullable(ranking);
    }

    /**
     * 根据排名值查询域名
     */
    default Optional<TrancoRanking> findByRankValue(Integer rankValue) {
        TrancoRanking ranking = selectOneByQuery(QueryWrapper.create()
                .where(TrancoRanking::getRankValue).eq(rankValue));
        return Optional.ofNullable(ranking);
    }

    /**
     * 根据排名范围查询域名列表
     */
    default List<TrancoRanking> findByRankValueBetween(Integer startRank, Integer endRank) {
        return selectListByQuery(QueryWrapper.create()
                .where(TrancoRanking::getRankValue).between(startRank, endRank)
                .orderBy(TrancoRanking::getRankValue));
    }

    /**
     * 查询排名前N的域名
     */
    default List<TrancoRanking> findTopRankedDomains(Integer topN) {
        return selectListByQuery(QueryWrapper.create()
                .where(TrancoRanking::getRankValue).le(topN)
                .orderBy(TrancoRanking::getRankValue));
    }

    /**
     * 根据域名模糊查询
     */
    default List<TrancoRanking> findByDomainContaining(String domainPattern) {
        return selectListByQuery(QueryWrapper.create()
                .where(TrancoRanking::getDomain).like(domainPattern)
                .orderBy(TrancoRanking::getRankValue));
    }

    /**
     * 根据数据版本查询所有排名
     */
    default List<TrancoRanking> findByDataVersion(String dataVersion) {
        return selectListByQuery(QueryWrapper.create()
                .where(TrancoRanking::getDataVersion).eq(dataVersion)
                .orderBy(TrancoRanking::getRankValue));
    }

    /**
     * 根据数据来源查询排名
     */
    default List<TrancoRanking> findBySource(String source) {
        return selectListByQuery(QueryWrapper.create()
                .where(TrancoRanking::getSource).eq(source)
                .orderBy(TrancoRanking::getRankValue));
    }

    /**
     * 查询指定域名后缀的排名列表
     */
    default List<TrancoRanking> findByDomainSuffix(String suffix) {
        return selectListByQuery(QueryWrapper.create()
                .where(TrancoRanking::getDomain).like("%" + suffix)
                .orderBy(TrancoRanking::getRankValue));
    }

    /**
     * 批量查询域名列表的排名信息
     */
    default List<TrancoRanking> findByDomainIn(List<String> domains) {
        return selectListByQuery(QueryWrapper.create()
                .where(TrancoRanking::getDomain).in(domains)
                .orderBy(TrancoRanking::getRankValue));
    }

    /**
     * 查询排名在指定值之上的域名数量
     */
    default long countByRankValueGreaterThan(Integer rankValue) {
        return selectCountByQuery(QueryWrapper.create()
                .where(TrancoRanking::getRankValue).gt(rankValue));
    }

    /**
     * 查询排名在指定值之下的域名数量
     */
    default long countByRankValueLessThan(Integer rankValue) {
        return selectCountByQuery(QueryWrapper.create()
                .where(TrancoRanking::getRankValue).lt(rankValue));
    }

    /**
     * 获取最高排名值
     */
    default Integer findMaxRankValue() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回null，在Service层实现具体逻辑
        return null;
    }

    /**
     * 获取最低排名值
     */
    default Integer findMinRankValue() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回null，在Service层实现具体逻辑
        return null;
    }

    /**
     * 获取所有数据版本列表
     */
    default List<String> findDistinctDataVersions() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 统计各数据版本的域名数量
     */
    default List<Object[]> countByDataVersion() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 检查域名是否在Tranco排名中
     */
    default boolean existsByDomain(String domain) {
        return selectCountByQuery(QueryWrapper.create()
                .where(TrancoRanking::getDomain).eq(domain)) > 0;
    }
}

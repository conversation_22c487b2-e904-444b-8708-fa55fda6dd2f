package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.domain.entity.FingerprintKnowledge;
import com.geeksec.knowledgebase.service.FingerprintKnowledgeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/fingerprint")
@RequiredArgsConstructor
@Tag(name = "Fingerprint Knowledge Controller", description = "Web指纹知识库API")
public class FingerprintKnowledgeController {

    private final FingerprintKnowledgeService fingerprintKnowledgeService;

    @GetMapping("/search")
    @Operation(summary = "搜索指纹信息", description = "根据关键词、产品、厂商或分类搜索指纹信息")
    public ApiResponse<List<FingerprintKnowledge>> searchFingerprints(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "产品名称") @RequestParam(required = false) String product,
            @Parameter(description = "厂商名称") @RequestParam(required = false) String vendor,
            @Parameter(description = "分类") @RequestParam(required = false) String category) {
        log.debug("搜索指纹信息: keyword={}, product={}, vendor={}, category={}", keyword, product, vendor, category);
        List<FingerprintKnowledge> results = fingerprintKnowledgeService.search(keyword, product, vendor, category);
        return ApiResponse.success(results);
    }

    @GetMapping("/product/{product}")
    @Operation(summary = "根据产品名获取指纹", description = "获取指定产品的所有指纹信息")
    public ApiResponse<List<FingerprintKnowledge>> getByProduct(
            @Parameter(description = "产品名称") @PathVariable String product) {
        log.debug("根据产品名获取指纹: {}", product);
        List<FingerprintKnowledge> results = fingerprintKnowledgeService.findByProduct(product);
        return ApiResponse.success(results);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取指纹库统计信息", description = "获取指纹库的统计信息，如总数、厂商数、产品数等")
    public ApiResponse<Map<String, Object>> getStatistics() {
        log.debug("获取指纹库统计信息");
        Map<String, Object> statistics = fingerprintKnowledgeService.getStatistics();
        return ApiResponse.success(statistics);
    }
}

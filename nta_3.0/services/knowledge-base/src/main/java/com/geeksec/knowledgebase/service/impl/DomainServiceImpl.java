package com.geeksec.knowledgebase.service.impl;

import com.geeksec.knowledgebase.service.DomainService;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class DomainServiceImpl implements DomainService {

    private final Set<String> tldSet = new HashSet<>();
    private final Map<String, Map<String, Object>> newGtldInfo = new HashMap<>();
    private final Set<String> cdnDomains = new HashSet<>();
    private final Set<String> dynamicDomains = new HashSet<>();

    @PostConstruct
    public void init() {
        loadTopLevelDomains();
        loadNewGtldInfo();
        loadCdnDomains();
        loadDynamicDomains();
    }

    @Override
    public boolean isTopLevelDomain(String domain) {
        return tldSet.contains(domain.toLowerCase());
    }

    @Override
    public Map<String, Object> getNewGtldInfo(String label) {
        return newGtldInfo.getOrDefault(label.toLowerCase(), Collections.emptyMap());
    }

    @Override
    public boolean isDynamicDomain(String domain) {
        return dynamicDomains.contains(domain.toLowerCase());
    }

    @Override
    public boolean isCdnDomain(String domain) {
        return cdnDomains.contains(domain.toLowerCase());
    }

    private void loadCdnDomains() {
        loadResourceFile("/domain/cdn_domains.txt", cdnDomains, "CDN域名");
    }

    private void loadDynamicDomains() {
        loadResourceFile("/domain/dynamic_domains.txt", dynamicDomains, "动态域名");
    }

    private void loadResourceFile(String resourcePath, Set<String> targetSet, String description) {
        try (InputStream is = getClass().getResourceAsStream(resourcePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            if (is == null) {
                log.warn("{}文件未找到: {}", description, resourcePath);
                return;
            }
            String line;
            while ((line = reader.readLine()) != null) {
                targetSet.add(line.trim().toLowerCase());
            }
            log.info("成功加载{} {}个", description, targetSet.size());
        } catch (Exception e) {
            log.error("加载{}失败", description, e);
        }
    }

    private void loadTopLevelDomains() {
        try (InputStream is = getClass().getResourceAsStream("/domain/tld.txt");
             BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            if (is == null) {
                log.warn("顶级域名文件未找到: /domain/tld.txt");
                return;
            }
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.startsWith("#")) {
                    tldSet.add(line.trim().toLowerCase());
                }
            }
            log.info("成功加载顶级域名 {} 个", tldSet.size());
        } catch (IOException e) {
            log.error("加载顶级域名失败", e);
        }
    }

    private void loadNewGtldInfo() {
        try (InputStream is = getClass().getResourceAsStream("/domain/new_gtld.csv");
             BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            if (is == null) {
                log.warn("新顶级域名文件未找到: /domain/new_gtld.csv");
                return;
            }
            String line;
            boolean isFirstLine = true;
            String[] headers = null;
            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    headers = line.split(",");
                    isFirstLine = false;
                    continue;
                }
                String[] parts = line.split(",");
                if (parts.length >= 4 && headers != null) {
                    String label = parts[3].trim();
                    if (!label.isEmpty()) {
                        Map<String, Object> info = new HashMap<>();
                        for (int i = 0; i < Math.min(parts.length, headers.length); i++) {
                            info.put(headers[i].trim(), parts[i].trim());
                        }
                        newGtldInfo.put(label.toLowerCase(), info);
                    }
                }
            }
            log.info("成功加载新顶级域名信息 {} 个", newGtldInfo.size());
        } catch (IOException e) {
            log.error("加载新顶级域名信息失败", e);
        }
    }
}

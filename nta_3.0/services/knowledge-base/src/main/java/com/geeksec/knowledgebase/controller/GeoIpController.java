package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.domain.entity.GeoIpInfo;
import com.geeksec.knowledgebase.service.GeoIpService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/geoip")
@RequiredArgsConstructor
@Tag(name = "GeoIP Controller", description = "GeoIP地理位置查询")
public class GeoIpController {

    private final GeoIpService geoIpService;

    @GetMapping("/lookup/{ip}")
    @Operation(summary = "查询单个IP地址的GeoIP信息")
    public ApiResponse<GeoIpInfo> getGeoIpInfo(@Parameter(description = "IP地址") @PathVariable String ip) {
        log.debug("查询IP地址: {}", ip);
        GeoIpInfo geoIpInfo = geoIpService.getGeoIpInfo(ip).orElse(null);
        return ApiResponse.success(geoIpInfo);
    }

    @PostMapping("/lookup-batch")
    @Operation(summary = "批量查询IP地址的GeoIP信息")
    public ApiResponse<List<GeoIpInfo>> getGeoIpInfoBatch(@Parameter(description = "IP地址列表") @RequestBody List<String> ips) {
        log.debug("批量查询IP地址: {}条", ips.size());
        List<GeoIpInfo> results = geoIpService.getGeoIpInfoBatch(ips);
        return ApiResponse.success(results);
    }

    @GetMapping("/database-info")
    @Operation(summary = "获取GeoIP数据库信息")
    public ApiResponse<String> getDatabaseInfo() {
        log.debug("获取GeoIP数据库信息");
        String info = geoIpService.getDatabaseInfo();
        return ApiResponse.success(info);
    }

    @PostMapping("/update-database")
    @Operation(summary = "更新GeoIP数据库")
    public ApiResponse<Boolean> updateDatabase() {
        log.debug("请求更新GeoIP数据库");
        boolean success = geoIpService.refreshGeoIpDatabase();
        return ApiResponse.success(success);
    }
}

package com.geeksec.knowledgebase.exception;

/**
 * 知识库服务自定义异常
 */
public class KnowledgeBaseException extends RuntimeException {

    private final int code;

    public KnowledgeBaseException(int code, String message) {
        super(message);
        this.code = code;
    }

    public KnowledgeBaseException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}

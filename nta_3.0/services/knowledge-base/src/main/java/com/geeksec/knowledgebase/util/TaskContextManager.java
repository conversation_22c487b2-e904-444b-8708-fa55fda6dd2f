package com.geeksec.knowledgebase.util;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务上下文管理器
 * 提供任务相关信息的获取和缓存功能
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskContextManager {

    private final DataSource dataSource;

    /**
     * 任务信息缓存
     */
    private final Map<Integer, TaskInfo> taskInfoMap = new ConcurrentHashMap<>();

    /**
     * 根据任务ID获取任务信息
     *
     * @param taskId 任务ID
     * @return 任务信息
     */
    public TaskInfo getTaskInfo(int taskId) {
        if (taskInfoMap.isEmpty()) {
            loadTaskInfo();
        }
        return taskInfoMap.get(taskId);
    }

    /**
     * 加载所有任务信息
     *
     * @return 任务信息列表
     */
    public List<TaskInfo> loadTaskInfo() {
        log.info("开始加载任务信息...");
        List<TaskInfo> resultList = new ArrayList<>();
        String sql = "SELECT task_id, batch_id, task_name, task_remark, created_time, state FROM task_info";

        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                TaskInfo info = new TaskInfo();
                info.setTaskId(rs.getInt(1));
                info.setBatchId(rs.getInt(2));
                info.setTaskName(rs.getString(3));
                info.setTaskRemark(rs.getString(4));
                info.setCreatedAt(rs.getInt(5));
                info.setState(rs.getInt(6));

                taskInfoMap.put(info.getTaskId(), info);
                resultList.add(info);
            }

            log.info("任务信息加载成功，共加载 {} 条记录", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("加载任务信息失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 刷新任务上下文缓存
     */
    public void refresh() {
        taskInfoMap.clear();
        loadTaskInfo();
    }

    /**
     * 任务信息类
     */
    @Getter
    @Setter
    public static class TaskInfo {
        private int taskId;
        private int batchId;
        private String taskName;
        private String taskRemark;
        private int createdAt;
        private int state;
    }
}

package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.DomainService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/domain")
@RequiredArgsConstructor
@Tag(name = "Domain Controller", description = "域名知识库查询")
public class DomainController {

    private final DomainService domainService;

    @GetMapping("/is-dynamic-domain/{domain}")
    @Operation(summary = "检查是否为动态域名")
    public ApiResponse<Boolean> isDynamicDomain(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查动态域名: {}", domain);
        boolean isDynamic = domainService.isDynamicDomain(domain);
        return ApiResponse.success(isDynamic);
    }

    @GetMapping("/is-cdn-domain/{domain}")
    @Operation(summary = "检查是否为CDN域名")
    public ApiResponse<Boolean> isCdnDomain(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查CDN域名: {}", domain);
        boolean isCdn = domainService.isCdnDomain(domain);
        return ApiResponse.success(isCdn);
    }

    @GetMapping("/is-sinkhole-domain/{domain}")
    @Operation(summary = "检查是否为Sinkhole域名")
    public ApiResponse<Boolean> isSinkholeDomain(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查Sinkhole域名: {}", domain);
        boolean isSinkhole = domainService.isSinkholeDomain(domain);
        return ApiResponse.success(isSinkhole);
    }

    @GetMapping("/tld-info/{domain}")
    @Operation(summary = "获取顶级域名信息")
    public ApiResponse<Map<String, String>> getTldInfo(@Parameter(description = "域名") @PathVariable String domain) {
        log.debug("获取TLD信息: {}", domain);
        Map<String, String> tldInfo = domainService.getTldInfo(domain);
        return ApiResponse.success(tldInfo);
    }

    @GetMapping("/all-tlds")
    @Operation(summary = "获取所有顶级域名")
    public ApiResponse<List<String>> getAllTlds() {
        log.debug("获取所有TLD");
        List<String> tlds = domainService.getAllTlds();
        return ApiResponse.success(tlds);
    }

    // ==================== KnowledgeBaseClient 需要的接口 ====================

    @GetMapping("/check/{domain}")
    @Operation(summary = "检查域名是否为恶意域名")
    public ApiResponse<Map<String, Object>> checkMaliciousDomain(
            @Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查恶意域名: {}", domain);
        boolean isMalicious = domainService.isMaliciousDomain(domain);
        Map<String, Object> response = Map.of(
                "domain", domain,
                "isMalicious", isMalicious
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/mining/check/{domain}")
    @Operation(summary = "检查域名是否为挖矿域名")
    public ApiResponse<Map<String, Object>> checkMiningDomain(
            @Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查挖矿域名: {}", domain);
        boolean isMining = domainService.isMiningDomain(domain);
        Map<String, Object> response = Map.of(
                "domain", domain,
                "isMining", isMining
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/popular/check/{domain}")
    @Operation(summary = "检查域名是否为热门域名")
    public ApiResponse<Map<String, Object>> checkPopularDomain(
            @Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查热门域名: {}", domain);
        Integer rank = domainService.getPopularDomainRank(domain);
        boolean isPopular = rank != null;
        Map<String, Object> response = Map.of(
                "domain", domain,
                "isPopular", isPopular,
                "rank", rank != null ? rank : 0
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/video/check/{domain}")
    @Operation(summary = "检查域名是否为视频网站域名")
    public ApiResponse<Map<String, Object>> checkVideoDomain(
            @Parameter(description = "域名") @PathVariable String domain) {
        log.debug("检查视频网站域名: {}", domain);
        boolean isVideo = domainService.isVideoDomain(domain);
        Map<String, Object> response = Map.of(
                "domain", domain,
                "isVideo", isVideo
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/tld/check/{tld}")
    @Operation(summary = "检查顶级域名是否有效")
    public ApiResponse<Map<String, Object>> checkValidTld(
            @Parameter(description = "顶级域名") @PathVariable String tld) {
        log.debug("检查顶级域名: {}", tld);
        boolean isValid = domainService.isValidTld(tld);
        Map<String, Object> response = Map.of(
                "tld", tld,
                "isValid", isValid
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/new-gtld/check/{gtld}")
    @Operation(summary = "获取新顶级域名信息")
    public ApiResponse<Map<String, Object>> getNewGtldInfo(
            @Parameter(description = "新顶级域名") @PathVariable String gtld) {
        log.debug("获取新顶级域名信息: {}", gtld);
        Map<String, Object> gtldInfo = domainService.getNewGtldInfo(gtld);
        return ApiResponse.success(gtldInfo);
    }
}

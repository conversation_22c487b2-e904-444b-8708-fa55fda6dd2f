package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.domain.entity.DomainWhoisInfo;
import com.geeksec.knowledgebase.service.DomainWhoisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 域名Whois信息查询服务
 */
@Slf4j
@RestController
@RequestMapping("/domain-whois")
@RequiredArgsConstructor
@Tag(name = "Domain Whois Controller", description = "域名Whois信息查询")
public class DomainWhoisController {

    private final DomainWhoisService domainWhoisService;

    @GetMapping("/info/{domain}")
    @Operation(summary = "查询域名Whois信息")
    @Parameter(name = "domain", description = "要查询的域名", required = true, example = "google.com")
    public ApiResponse<DomainWhoisInfo> getDomainWhoisInfo(@PathVariable String domain) {
        log.debug("查询域名Whois信息: {}", domain);
        DomainWhoisInfo whoisInfo = domainWhoisService.getDomainWhoisInfo(domain);
        return ApiResponse.success(whoisInfo);
    }
}

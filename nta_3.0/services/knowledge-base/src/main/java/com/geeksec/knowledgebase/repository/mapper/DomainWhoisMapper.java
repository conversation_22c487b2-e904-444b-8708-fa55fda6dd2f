package com.geeksec.knowledgebase.repository.mapper;

import com.geeksec.knowledgebase.domain.entity.DomainWhoisInfo;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 域名WHOIS信息 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DomainWhoisMapper extends BaseMapper<DomainWhoisInfo> {

    /**
     * 根据域名查询WHOIS信息
     */
    default Optional<DomainWhoisInfo> findByDomain(String domain) {
        DomainWhoisInfo whoisInfo = selectOneByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getDomain).eq(domain)
                .and(DomainWhoisInfo::isActive).eq(true));
        return Optional.ofNullable(whoisInfo);
    }

    /**
     * 根据注册商查询域名列表
     */
    default List<DomainWhoisInfo> findByRegistrar(String registrar) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getRegistrar).eq(registrar)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 根据注册人查询域名列表
     */
    default List<DomainWhoisInfo> findByRegistrant(String registrant) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getRegistrant).eq(registrant)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 根据注册人组织查询域名列表
     */
    default List<DomainWhoisInfo> findByRegistrantOrg(String registrantOrg) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getRegistrantOrg).eq(registrantOrg)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 根据注册人国家查询域名列表
     */
    default List<DomainWhoisInfo> findByRegistrantCountry(String registrantCountry) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getRegistrantCountry).eq(registrantCountry)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 根据注册日期范围查询域名列表
     */
    default List<DomainWhoisInfo> findByCreateDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getCreateDate).between(startDate, endDate)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 根据过期日期范围查询域名列表
     */
    default List<DomainWhoisInfo> findByExpireDateBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getExpireDate).between(startDate, endDate)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 查询即将过期的域名（指定天数内）
     */
    default List<DomainWhoisInfo> findExpiringDomains(int days) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureDate = now.plusDays(days);
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getExpireDate).between(now, futureDate)
                .and(DomainWhoisInfo::isActive).eq(true)
                .orderBy(DomainWhoisInfo::getExpireDate));
    }

    /**
     * 根据域名模糊查询
     */
    default List<DomainWhoisInfo> findByDomainContaining(String domainPattern) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getDomain).like(domainPattern)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 根据注册商模糊查询
     */
    default List<DomainWhoisInfo> findByRegistrarContaining(String registrarPattern) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::getRegistrar).like(registrarPattern)
                .and(DomainWhoisInfo::isActive).eq(true));
    }

    /**
     * 查询最近更新的域名信息
     */
    default List<DomainWhoisInfo> findRecentlyUpdated(int limit) {
        return selectListByQuery(QueryWrapper.create()
                .where(DomainWhoisInfo::isActive).eq(true)
                .orderBy(DomainWhoisInfo::getUpdatedAt, false)
                .limit(limit));
    }

    /**
     * 统计各注册商的域名数量
     */
    default List<Object[]> countByRegistrar() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 统计各国家的域名数量
     */
    default List<Object[]> countByRegistrantCountry() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }
}

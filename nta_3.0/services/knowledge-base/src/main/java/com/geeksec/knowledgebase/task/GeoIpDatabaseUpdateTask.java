package com.geeksec.knowledgebase.task;

import com.geeksec.knowledgebase.config.KnowledgeBaseProperties;
import com.geeksec.knowledgebase.util.GeoIpDatabaseManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Base64;
import java.util.zip.GZIPInputStream;

/**
 * GeoIP数据库自动更新任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GeoIpDatabaseUpdateTask {

    private static final String MAXMIND_DOWNLOAD_URL = "https://download.maxmind.com/app/geoip_download";
    
    private final KnowledgeBaseProperties.GeoIpProperties geoIpProperties;
    private final GeoIpDatabaseManager geoIpDatabaseManager;
    
    /**
     * 自动更新GeoIP数据库
     * 默认每周一凌晨3点执行
     */
    @Scheduled(cron = "${knowledge-base.geo-ip.refresh-cron:0 0 3 ? * MON}")
    public void autoUpdateGeoIpDatabase() {
        if (!geoIpProperties.isEnabled()) {
            log.info("GeoIP功能未启用，跳过数据库更新");
            return;
        }
        
        log.info("开始自动更新GeoIP数据库...");
        
        try {
            // 下载城市数据库
            String cityDbUrl = buildDownloadUrl("GeoLite2-City");
            InputStream cityDbStream = downloadDatabase(cityDbUrl, "城市数据库");
            
            // 下载ASN数据库
            String asnDbUrl = buildDownloadUrl("GeoLite2-ASN");
            InputStream asnDbStream = downloadDatabase(asnDbUrl, "ASN数据库");
            
            // 更新数据库
            if (cityDbStream != null && asnDbStream != null) {
                boolean success = geoIpDatabaseManager.updateDatabases(cityDbStream, asnDbStream);
                if (success) {
                    log.info("GeoIP数据库自动更新成功");
                } else {
                    log.error("GeoIP数据库自动更新失败");
                }
            } else {
                log.error("下载GeoIP数据库失败，跳过更新");
            }
        } catch (Exception e) {
            log.error("自动更新GeoIP数据库失败", e);
        }
    }
    
    /**
     * 构建MaxMind下载URL
     */
    private String buildDownloadUrl(String dbName) {
        String licenseKey = System.getenv("MAXMIND_LICENSE_KEY");
        if (licenseKey == null || licenseKey.trim().isEmpty()) {
            throw new IllegalStateException("未设置MAXMIND_LICENSE_KEY环境变量，无法下载GeoIP数据库");
        }
        
        return String.format("%s?edition_id=%s&license_key=%s&suffix=tar.gz", 
                MAXMIND_DOWNLOAD_URL, dbName, licenseKey);
    }
    
    /**
     * 下载数据库文件
     */
    private InputStream downloadDatabase(String downloadUrl, String dbName) {
        try {
            log.info("开始下载{}: {}", dbName, downloadUrl);
            
            URL url = new URL(downloadUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(300000);
            
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("下载{}失败，HTTP状态码: {}", dbName, responseCode);
                return null;
            }
            
            // 解压GZIP流
            return new GZIPInputStream(connection.getInputStream());
        } catch (Exception e) {
            log.error("下载{}失败", dbName, e);
            return null;
        }
    }
}

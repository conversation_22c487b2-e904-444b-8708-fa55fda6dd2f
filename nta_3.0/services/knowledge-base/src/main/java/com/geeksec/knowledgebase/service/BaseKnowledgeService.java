package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.util.CsvUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * 知识库基础服务类
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
@Slf4j
public abstract class BaseKnowledgeService<T, ID> {
    
    protected final RedisTemplate<String, Object> redisTemplate;
    protected final CsvUtils csvUtils;
    protected final String cacheKeyPrefix;
    protected final long cacheTtl;
    protected final String dataFile;
    protected final String refreshCron;
    
    protected ValueOperations<String, Object> valueOps;
    
    protected BaseKnowledgeService(RedisTemplate<String, Object> redisTemplate, 
                                 CsvUtils csvUtils,
                                 String cacheKeyPrefix,
                                 long cacheTtl,
                                 String dataFile,
                                 String refreshCron) {
        this.redisTemplate = redisTemplate;
        this.csvUtils = csvUtils;
        this.cacheKeyPrefix = cacheKeyPrefix;
        this.cacheTtl = cacheTtl;
        this.dataFile = dataFile;
        this.refreshCron = refreshCron;
    }
    
    @PostConstruct
    protected void init() {
        this.valueOps = redisTemplate.opsForValue();
        loadData();
    }
    
    /**
     * 加载数据到缓存
     */
    public abstract void loadData();
    
    /**
     * 从CSV记录映射到实体
     */
    protected abstract T mapCsvRecordToEntity(CSVRecord record);
    
    /**
     * 获取缓存的key
     */
    protected abstract String getCacheKey(ID id);
    
    /**
     * 从缓存获取数据
     */
    @SuppressWarnings("unchecked")
    protected T getFromCache(ID id) {
        try {
            String cacheKey = getCacheKey(id);
            return (T) valueOps.get(cacheKey);
        } catch (Exception e) {
            log.error("Error getting data from cache for id: " + id, e);
            return null;
        }
    }
    
    /**
     * 将数据放入缓存
     */
    protected void putInCache(ID id, T entity) {
        try {
            if (entity != null) {
                String cacheKey = getCacheKey(id);
                valueOps.set(cacheKey, entity, cacheTtl, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("Error putting data to cache for id: " + id, e);
        }
    }
    
    /**
     * 批量将数据放入缓存
     */
    protected <E> void batchPutInCache(List<E> items, Function<E, ID> idExtractor, Function<E, T> entityExtractor) {
        if (items == null || items.isEmpty()) {
            return;
        }
        
        items.forEach(item -> {
            ID id = idExtractor.apply(item);
            T entity = entityExtractor.apply(item);
            if (id != null && entity != null) {
                putInCache(id, entity);
            }
        });
    }
    
    /**
     * 从缓存中删除数据
     */
    protected void removeFromCache(ID id) {
        try {
            String cacheKey = getCacheKey(id);
            redisTemplate.delete(cacheKey);
        } catch (Exception e) {
            log.error("Error removing data from cache for id: " + id, e);
        }
    }
    
    /**
     * 清空缓存
     */
    protected void clearCache() {
        try {
            String pattern = cacheKeyPrefix + "*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.info("Cleared cache with pattern: {}", pattern);
        } catch (Exception e) {
            log.error("Error clearing cache with prefix: " + cacheKeyPrefix, e);
        }
    }
}

package com.geeksec.knowledgebase.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.config.ConfigurationManager;
import com.geeksec.knowledgebase.util.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * IP WHOIS数据管理器
 * 直接从JSONL文件读取RIR Data的IP WHOIS数据
 * 提供高效的IP地址enrichment功能
 *
 * <AUTHOR>
 */
@Slf4j
public class IpWhoisManager {

    /** 单例实例 */
    private static volatile IpWhoisManager instance = null;

    /** IP网段数据列表 */
    private final List<IpRangeInfo> ipRanges = new ArrayList<>();

    /** IP查询结果缓存 */
    private final ConcurrentHashMap<String, IpWhoisInfo> queryCache = new ConcurrentHashMap<>();

    /** JSON解析器 */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /** 数据文件路径配置键 */
    private static final String IP_WHOIS_FILE_CONFIG = "ip.whois.data.file";

    /** 默认数据文件路径 */
    private static final String DEFAULT_IP_WHOIS_FILE = "data/ip_whois_data.jsonl";

    /**
     * 私有构造函数
     */
    private IpWhoisManager() {
        initialize();
    }

    /**
     * 获取单例实例
     *
     * @return IpWhoisManager实例
     */
    public static IpWhoisManager getInstance() {
        if (instance == null) {
            synchronized (IpWhoisManager.class) {
                if (instance == null) {
                    instance = new IpWhoisManager();
                }
            }
        }
        return instance;
    }

    /**
     * 初始化IP WHOIS数据
     */
    private void initialize() {
        try {
            // 优先尝试从PersistentVolume挂载路径读取数据
            if (loadFromMountedVolume()) {
                log.info("IP WHOIS管理器初始化成功，从挂载卷加载了 {} 个IP网段", ipRanges.size());
                return;
            }

            // 回退到从本地文件读取
            String dataFile = ConfigurationManager.getConfig()
                .get(IP_WHOIS_FILE_CONFIG, DEFAULT_IP_WHOIS_FILE);

            if (loadFromFile(dataFile)) {
                log.info("IP WHOIS管理器初始化成功，从本地文件加载了 {} 个IP网段", ipRanges.size());
            } else {
                log.warn("IP WHOIS数据加载失败，将使用空数据集");
            }
        } catch (Exception e) {
            log.error("初始化IP WHOIS管理器失败", e);
        }
    }

    /**
     * 从挂载卷加载IP WHOIS数据
     *
     * @return 是否加载成功
     */
    private boolean loadFromMountedVolume() {
        try {
            // 在Kubernetes环境中，IP WHOIS数据存储在PersistentVolume中
            // 使用硬编码的路径
            String mountedPath = "/opt/flink/data/knowledge-base/ip-whois";

            // 查找JSONL文件
            if (Files.exists(Paths.get(mountedPath))) {
                return Files.list(Paths.get(mountedPath))
                    .filter(path -> path.toString().endsWith(".jsonl"))
                    .findFirst()
                    .map(path -> loadFromFile(path.toString()))
                    .orElse(false);
            }

            log.debug("挂载路径不存在: {}", mountedPath);
            return false;
        } catch (Exception e) {
            log.debug("从挂载卷加载IP WHOIS数据失败", e);
            return false;
        }
    }

    /**
     * 从文件加载IP WHOIS数据
     *
     * @param filePath 文件路径
     * @return 是否加载成功
     */
    private boolean loadFromFile(String filePath) {
        try {
            if (!Files.exists(Paths.get(filePath))) {
                log.debug("IP WHOIS数据文件不存在: {}", filePath);
                return false;
            }

            loadIpWhoisData(filePath);
            return true;
        } catch (Exception e) {
            log.error("从文件加载IP WHOIS数据失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 从JSONL文件加载IP WHOIS数据
     *
     * @param jsonlFile JSONL文件路径
     */
    private void loadIpWhoisData(String jsonlFile) {
        ipRanges.clear();

        try (BufferedReader reader = new BufferedReader(new FileReader(jsonlFile))) {
            String line;
            int lineNumber = 0;
            int loadedCount = 0;

            while ((line = reader.readLine()) != null) {
                lineNumber++;

                if (StringUtils.isEmpty(line.trim())) {
                    continue;
                }

                try {
                    IpRangeInfo rangeInfo = parseJsonLine(line);
                    if (rangeInfo != null) {
                        ipRanges.add(rangeInfo);
                        loadedCount++;
                    }
                } catch (Exception e) {
                    log.debug("解析第 {} 行失败: {}", lineNumber, e.getMessage());
                }
            }

            log.info("从 {} 加载了 {} 条IP WHOIS记录", jsonlFile, loadedCount);

        } catch (IOException e) {
            log.error("读取IP WHOIS文件失败: {}", jsonlFile, e);
        }
    }

    /**
     * 解析JSON行数据
     *
     * @param jsonLine JSON行
     * @return IpRangeInfo对象
     */
    private IpRangeInfo parseJsonLine(String jsonLine) {
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonLine);

            // 提取IP段信息
            String ipRange = extractIpRange(jsonNode);
            if (StringUtils.isEmpty(ipRange)) {
                return null;
            }

            // 验证CIDR格式
            if (!IpUtils.isValidCidr(ipRange)) {
                return null;
            }

            // 提取其他信息
            String netname = getStringValue(jsonNode, "netname");
            String description = getStringValue(jsonNode, "descr");
            String country = getStringValue(jsonNode, "country");
            String status = getStringValue(jsonNode, "status");
            String maintainer = getStringValue(jsonNode, "mnt-by");

            return new IpRangeInfo(ipRange, netname, description, country, status, maintainer);

        } catch (Exception e) {
            log.debug("解析JSON行失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 提取IP段信息
     */
    private String extractIpRange(JsonNode jsonNode) {
        JsonNode prefixesNode = jsonNode.get("prefixes");
        if (prefixesNode != null && prefixesNode.isArray() && prefixesNode.size() > 0) {
            return prefixesNode.get(0).asText();
        }
        return null;
    }

    /**
     * 从JSON节点获取字符串值
     */
    private String getStringValue(JsonNode jsonNode, String fieldName) {
        JsonNode fieldNode = jsonNode.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull()) {
            return fieldNode.asText().trim();
        }
        return "";
    }



    /**
     * 查询IP地址的WHOIS信息
     *
     * @param ipAddress IP地址字符串
     * @return WHOIS信息，未找到返回null
     */
    public IpWhoisInfo getIpWhoisInfo(String ipAddress) {
        if (StringUtils.isEmpty(ipAddress)) {
            return null;
        }

        // 检查缓存
        IpWhoisInfo cached = queryCache.get(ipAddress);
        if (cached != null) {
            return cached;
        }

        // 查找匹配的IP段
        for (IpRangeInfo rangeInfo : ipRanges) {
            if (IpUtils.isIpInCidr(ipAddress, rangeInfo.getCidr())) {
                IpWhoisInfo whoisInfo = new IpWhoisInfo(
                    rangeInfo.getNetname(),
                    rangeInfo.getDescription(),
                    rangeInfo.getCountry(),
                    rangeInfo.getStatus(),
                    rangeInfo.getMaintainer()
                );

                // 缓存结果
                queryCache.put(ipAddress, whoisInfo);
                return whoisInfo;
            }
        }

        return null;
    }

    /**
     * 获取IP地址的组织信息
     *
     * @param ipAddress IP地址
     * @return 组织信息
     */
    public String getIpOrganization(String ipAddress) {
        IpWhoisInfo whoisInfo = getIpWhoisInfo(ipAddress);
        if (whoisInfo != null) {
            if (StringUtils.isNotEmpty(whoisInfo.getNetname())) {
                return whoisInfo.getNetname();
            } else if (StringUtils.isNotEmpty(whoisInfo.getDescription())) {
                return whoisInfo.getDescription();
            }
        }
        return "";
    }

    /**
     * 获取IP地址的国家信息
     *
     * @param ipAddress IP地址
     * @return 国家代码
     */
    public String getIpCountry(String ipAddress) {
        IpWhoisInfo whoisInfo = getIpWhoisInfo(ipAddress);
        return whoisInfo != null ? whoisInfo.getCountry() : "";
    }

    /**
     * 刷新数据
     */
    public void refresh() {
        queryCache.clear();
        initialize();
    }

    /**
     * 获取统计信息
     */
    public String getStatistics() {
        return String.format("IP段数量: %d, 缓存大小: %d", ipRanges.size(), queryCache.size());
    }

    /**
     * IP段信息类
     */
    private static class IpRangeInfo {
        private final String cidr;
        private final String netname;
        private final String description;
        private final String country;
        private final String status;
        private final String maintainer;

        public IpRangeInfo(String cidr, String netname, String description,
                          String country, String status, String maintainer) {
            this.cidr = cidr;
            this.netname = netname;
            this.description = description;
            this.country = country;
            this.status = status;
            this.maintainer = maintainer;
        }

        public String getCidr() { return cidr; }
        public String getNetname() { return netname; }
        public String getDescription() { return description; }
        public String getCountry() { return country; }
        public String getStatus() { return status; }
        public String getMaintainer() { return maintainer; }
    }



    /**
     * IP WHOIS信息类
     */
    public static class IpWhoisInfo {
        private final String netname;
        private final String description;
        private final String country;
        private final String status;
        private final String maintainer;

        public IpWhoisInfo(String netname, String description, String country,
                          String status, String maintainer) {
            this.netname = netname;
            this.description = description;
            this.country = country;
            this.status = status;
            this.maintainer = maintainer;
        }

        public String getNetname() { return netname; }
        public String getDescription() { return description; }
        public String getCountry() { return country; }
        public String getStatus() { return status; }
        public String getMaintainer() { return maintainer; }

        public String getOrganizationSummary() {
            StringBuilder summary = new StringBuilder();
            if (StringUtils.isNotEmpty(netname)) {
                summary.append(netname);
            }
            if (StringUtils.isNotEmpty(description) && !description.equals(netname)) {
                if (summary.length() > 0) {
                    summary.append(" - ");
                }
                summary.append(description);
            }
            return summary.toString();
        }
    }
}

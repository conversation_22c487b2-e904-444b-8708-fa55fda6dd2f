package com.geeksec.knowledgebase.service;

import com.geeksec.knowledgebase.domain.entity.GeoIpInfo;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * GeoIP服务接口
 */
public interface GeoIpService {

    String getDatabaseInfo();

    /**
     * 根据IP地址查询GeoIP信息
     *
     * @param ipAddress IP地址
     * @return GeoIP信息
     */
    Optional<GeoIpInfo> getGeoIpInfo(String ipAddress);

    /**
     * 批量查询IP地址的GeoIP信息
     *
     * @param ipAddresses IP地址列表
     * @return IP地址到GeoIP信息的映射
     */
    List<GeoIpInfo> getGeoIpInfoBatch(List<String> ipAddresses);

    /**
     * 获取IP地址的国家代码
     *
     * @param ipAddress IP地址
     * @return 国家代码，如果无法获取则返回"--"
     */
    String getCountryCode(String ipAddress);

    /**
     * 获取IP地址的ASN信息
     *
     * @param ipAddress IP地址
     * @return ASN信息，如果无法获取则返回"--"
     */
    String getAsnInfo(String ipAddress);

    /**
     * 获取IP地址的地理位置信息
     *
     * @param ipAddress IP地址
     * @return 地理位置信息，格式为"国家 省份 城市"
     */
    String getLocation(String ipAddress);

    /**
     * 获取IP地址的经纬度
     *
     * @param ipAddress IP地址
     * @return 经纬度信息，格式为"经度,纬度"
     */
    String getCoordinates(String ipAddress);

    /**
     * 获取IP地址的原始CityResponse
     *
     * @param ipAddress IP地址
     * @return CityResponse对象
     */
    CityResponse getCityResponse(String ipAddress);

    /**
     * 获取IP地址的原始AsnResponse
     *
     * @param ipAddress IP地址
     * @return AsnResponse对象
     */
    AsnResponse getAsnResponse(String ipAddress);

    /**
     * 获取国家/地区分布统计
     *
     * @return 国家/地区分布统计
     */
    List<Map<String, Object>> getCountryDistribution();

    /**
     * 获取省份/州分布统计
     *
     * @return 省份/州分布统计
     */
    List<Map<String, Object>> getProvinceDistribution();

    /**
     * 获取城市分布统计
     *
     * @return 城市分布统计
     */
    List<Map<String, Object>> getCityDistribution();

    /**
     * 获取ASN分布统计
     *
     * @return ASN分布统计
     */
    List<Map<String, Object>> getAsnDistribution();

    /**
     * 刷新GeoIP数据库
     *
     * @return 是否刷新成功
     */
    boolean refreshGeoIpDatabase();
}

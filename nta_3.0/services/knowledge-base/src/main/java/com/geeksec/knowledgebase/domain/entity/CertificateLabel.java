package com.geeksec.knowledgebase.domain.entity;

import com.geeksec.knowledgebase.domain.enums.CertificateLabelType;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 证书标签实体
 *
 * <AUTHOR>
 */
@Data
@Table("certificate_labels")
public class CertificateLabel {

    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 标签ID (对应原有的Tag_Id)
     */
    @Column("label_id")
    private Integer labelId;

    /**
     * 标签名称 (对应原有的Tag_Text)
     */
    @Column("label_name")
    private String labelName;

    /**
     * 标签备注 (对应原有的Tag_Remark)
     */
    @Column("label_remark")
    private String labelRemark;

    /**
     * 标签类型
     */
    @Column("label_type")
    private CertificateLabelType labelType;

    /**
     * 黑名单评分 (对应原有的Black_List)
     */
    @Column("black_score")
    private Integer blackScore = 0;

    /**
     * 白名单评分 (对应原有的White_List)
     */
    @Column("white_score")
    private Integer whiteScore = 0;

    /**
     * 目标类型 (对应原有的Target)
     */
    @Column("target")
    private String target = "cert";

    /**
     * 属性类型 (对应原有的attr_type)
     */
    @Column("attr_type")
    private Integer attrType;

    /**
     * 标签描述
     */
    @Column("description")
    private String description;

    /**
     * 是否激活
     */
    @Column("is_active")
    private Boolean isActive = true;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

}

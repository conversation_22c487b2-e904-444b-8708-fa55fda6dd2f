package com.geeksec.knowledgebase.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 压缩文件工具类，用于解压.tar.gz文件
 */
@Slf4j
public class ArchiveUtils {

    /**
     * 从.tar.gz文件中提取指定扩展名的文件
     *
     * @param inputStream .tar.gz文件输入流
     * @param targetDir   目标目录
     * @param fileExt     要提取的文件扩展名（如.mmdb）
     * @return 提取的文件路径，如果没有找到则返回null
     */
    public static String extractFileFromTarGz(InputStream inputStream, String targetDir, String fileExt) {
        if (inputStream == null || targetDir == null || fileExt == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        TarArchiveInputStream tarIn = null;
        String extractedFilePath = null;

        try {
            // 创建GZIP输入流
            GzipCompressorInputStream gzipIn = new GzipCompressorInputStream(inputStream);
            
            // 创建TAR输入流
            tarIn = new TarArchiveInputStream(gzipIn);
            
            // 确保目标目录存在
            Files.createDirectories(Paths.get(targetDir));
            
            // 遍历TAR文件中的条目
            TarArchiveEntry entry;
            while ((entry = (TarArchiveEntry) tarIn.getNextEntry()) != null) {
                if (!entry.isDirectory() && entry.getName().toLowerCase().endsWith(fileExt.toLowerCase())) {
                    // 获取文件名（不包含路径）
                    String fileName = FilenameUtils.getName(entry.getName());
                    Path targetPath = Paths.get(targetDir, fileName);
                    
                    log.info("正在提取文件: {} 到 {}", entry.getName(), targetPath);
                    
                    // 提取文件
                    try (OutputStream out = Files.newOutputStream(targetPath)) {
                        IOUtils.copy(tarIn, out);
                    }
                    
                    extractedFilePath = targetPath.toString();
                    log.info("文件提取成功: {}", extractedFilePath);
                    
                    // 只提取第一个匹配的文件
                    break;
                }
            }
            
            if (extractedFilePath == null) {
                log.warn("未在压缩包中找到扩展名为 {} 的文件", fileExt);
            }
            
        } catch (IOException e) {
            log.error("从.tar.gz文件中提取文件失败", e);
            throw new RuntimeException("提取文件失败: " + e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(tarIn);
            IOUtils.closeQuietly(inputStream);
        }
        
        return extractedFilePath;
    }
    
    /**
     * 解压.tar.gz文件到指定目录
     *
     * @param tarGzFile .tar.gz文件路径
     * @param outputDir 输出目录
     * @return 解压后的目录路径
     */
    public static String extractTarGz(String tarGzFile, String outputDir) {
        if (tarGzFile == null || outputDir == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        try (FileInputStream fis = new FileInputStream(tarGzFile)) {
            return extractTarGz(fis, outputDir);
        } catch (IOException e) {
            throw new RuntimeException("解压文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解压.tar.gz输入流到指定目录
     *
     * @param inputStream .tar.gz文件输入流
     * @param outputDir   输出目录
     * @return 解压后的目录路径
     */
    public static String extractTarGz(InputStream inputStream, String outputDir) {
        if (inputStream == null || outputDir == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        TarArchiveInputStream tarIn = null;
        String baseDir = "";
        
        try {
            // 创建GZIP输入流
            GzipCompressorInputStream gzipIn = new GzipCompressorInputStream(inputStream);
            
            // 创建TAR输入流
            tarIn = new TarArchiveInputStream(gzipIn);
            
            // 确保输出目录存在
            Path outputPath = Paths.get(outputDir);
            Files.createDirectories(outputPath);
            
            // 遍历TAR文件中的条目
            TarArchiveEntry entry;
            while ((entry = (TarArchiveEntry) tarIn.getNextEntry()) != null) {
                // 获取相对路径
                String entryPath = entry.getName();
                
                // 如果是第一个条目，记录基础目录
                if (baseDir.isEmpty() && entry.isDirectory()) {
                    baseDir = entryPath;
                }
                
                // 构建目标路径
                Path targetPath = outputPath.resolve(entryPath);
                
                if (entry.isDirectory()) {
                    // 创建目录
                    Files.createDirectories(targetPath);
                } else {
                    // 确保父目录存在
                    Files.createDirectories(targetPath.getParent());
                    
                    // 提取文件
                    try (OutputStream out = Files.newOutputStream(targetPath)) {
                        IOUtils.copy(tarIn, out);
                    }
                    
                    // 设置文件权限
                    Files.setPosixFilePermissions(targetPath, 
                        getPosixFilePermissions(entry.getMode()));
                }
                
                // 设置最后修改时间
                Files.setLastModifiedTime(targetPath, 
                    java.nio.file.attribute.FileTime.from(entry.getLastModifiedDate().toInstant()));
            }
            
            log.info("成功解压文件到目录: {}", outputPath);
            return outputPath.toString();
            
        } catch (IOException e) {
            log.error("解压.tar.gz文件失败", e);
            throw new RuntimeException("解压文件失败: " + e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(tarIn);
            IOUtils.closeQuietly(inputStream);
        }
    }
    
    /**
     * 将TAR条目的模式转换为POSIX文件权限
     */
    private static java.util.Set<java.nio.file.attribute.PosixFilePermission> getPosixFilePermissions(int mode) {
        java.util.Set<java.nio.file.attribute.PosixFilePermission> perms = new java.util.HashSet<>();
        
        // 所有者权限
        if ((mode & 0400) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.OWNER_READ);
        if ((mode & 0200) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.OWNER_WRITE);
        if ((mode & 0100) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.OWNER_EXECUTE);
        
        // 组权限
        if ((mode & 0040) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.GROUP_READ);
        if ((mode & 0020) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.GROUP_WRITE);
        if ((mode & 0010) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.GROUP_EXECUTE);
        
        // 其他用户权限
        if ((mode & 0004) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.OTHERS_READ);
        if ((mode & 0002) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.OTHERS_WRITE);
        if ((mode & 0001) != 0) perms.add(java.nio.file.attribute.PosixFilePermission.OTHERS_EXECUTE);
        
        return perms;
    }
}

package com.geeksec.knowledgebase.util;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/**
 * CSV文件工具类
 */
@Slf4j
@Component
public class CsvUtils {

    /**
     * 从CSV文件读取记录
     *
     * @param filePath 文件路径，支持classpath:前缀
     * @param mapper   记录映射函数
     * @param <T>      目标类型
     * @return 记录列表
     */
    public <T> List<T> readCsv(String filePath, Function<CSVRecord, T> mapper) {
        List<T> result = new ArrayList<>();
        
        if (!StringUtils.hasText(filePath)) {
            log.warn("CSV file path is empty");
            return result;
        }

        try (InputStream inputStream = getInputStream(filePath);
             Reader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader()
                     .withIgnoreHeaderCase()
                     .withTrim())) {

            for (CSVRecord record : csvParser) {
                try {
                    T item = mapper.apply(record);
                    if (item != null) {
                        result.add(item);
                    }
                } catch (Exception e) {
                    log.error("Error parsing CSV record: {}", record, e);
                }
            }
            log.info("Successfully loaded {} records from {}", result.size(), filePath);
            
        } catch (Exception e) {
            log.error("Error reading CSV file: " + filePath, e);
        }
        
        return result;
    }

    /**
     * 获取输入流，支持classpath:前缀和绝对路径
     */
    private InputStream getInputStream(String filePath) throws IOException {
        if (filePath.startsWith("classpath:")) {
            String path = filePath.substring("classpath:".length());
            Resource resource = new ClassPathResource(path);
            return resource.getInputStream();
        } else {
            return new FileInputStream(filePath);
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean exists(String filePath) {
        try {
            if (filePath.startsWith("classpath:")) {
                String path = filePath.substring("classpath:".length());
                return new ClassPathResource(path).exists();
            } else {
                return new File(filePath).exists();
            }
        } catch (Exception e) {
            log.error("Error checking file existence: " + filePath, e);
            return false;
        }
    }
}

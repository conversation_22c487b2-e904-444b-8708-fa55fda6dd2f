package com.geeksec.knowledgebase.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Knife4j API文档配置和Web MVC配置
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor
public class Knife4jConfig implements WebMvcConfigurer {

    private final KnowledgeBaseProperties knowledgeBaseProperties;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("NTA 3.0 知识库服务 API")
                        .description("提供威胁情报、检测规则、地理位置等知识数据的统一管理和API访问\n\n" +
                                   "API版本: " + knowledgeBaseProperties.getApi().getVersion() + "\n" +
                                   "API前缀: " + knowledgeBaseProperties.getApi().getFullPrefix())
                        .version("3.0.0-SNAPSHOT")
                        .contact(new Contact()
                                .name("hufengkai")
                                .email("<EMAIL>"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")));
    }

    /**
     * 配置API路径前缀
     */
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix(knowledgeBaseProperties.getApi().getFullPrefix(),
                c -> c.isAnnotationPresent(org.springframework.web.bind.annotation.RestController.class));
    }
}

package com.geeksec.knowledgebase.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 域名相关知识库服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DomainService {

    // 挖矿域名集合
    private Set<String> miningDomains = new HashSet<>();

    // 热门域名映射 (域名 -> 排名)
    private Map<String, Integer> popularDomains = new HashMap<>();

    // 视频网站域名集合
    private Set<String> videoDomains = new HashSet<>();

    // 有效顶级域名集合
    private Set<String> validTlds = new HashSet<>();

    // 新顶级域名信息映射
    private Map<String, Map<String, Object>> newGtldInfo = new HashMap<>();

    // CDN 域名集合
    private final Set<String> cdnDomains = new HashSet<>();

    // 动态域名集合
    private final Set<String> dynamicDomains = new HashSet<>();

    // 恶意域名集合
    private final Set<String> maliciousDomains = new HashSet<>();

    @PostConstruct
    public void init() {
        log.info("初始化域名知识库服务");
        loadMiningDomains();
        loadPopularDomains();
        loadVideoDomains();
        loadValidTlds();
        loadNewGtldInfo();
        loadCdnDomains();
        loadDynamicDomains();
        loadMaliciousDomains();
        log.info("域名知识库服务初始化完成，挖矿域名: {} 个，热门域名: {} 个，视频域名: {} 个，TLD: {} 个，新gTLD: {} 个, CDN域名: {} 个, 动态域名: {} 个, 恶意域名: {} 个",
                miningDomains.size(), popularDomains.size(), videoDomains.size(), validTlds.size(), newGtldInfo.size(), cdnDomains.size(), dynamicDomains.size(), maliciousDomains.size());
    }

    /**
     * 检查域名是否为挖矿域名
     */
    public boolean isMiningDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        return miningDomains.contains(domain.toLowerCase().trim());
    }

    /**
     * 获取所有挖矿域名列表
     */
    public List<String> getMiningDomains() {
        return new ArrayList<>(miningDomains);
    }

    /**
     * 获取热门域名排名
     */
    public Integer getPopularDomainRank(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return null;
        }
        return popularDomains.get(domain.toLowerCase().trim());
    }

    /**
     * 获取热门域名列表（分页）
     */
    public List<Map<String, Object>> getPopularDomains(int page, int size) {
        return popularDomains.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .skip((long) page * size)
                .limit(size)
                .map(entry -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("domain", entry.getKey());
                    result.put("rank", entry.getValue());
                    return result;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取所有热门域名列表
     */
    public List<String> getPopularDomains() {
        return popularDomains.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 检查域名是否为视频网站域名
     */
    public boolean isVideoDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        return videoDomains.contains(domain.toLowerCase().trim());
    }

    /**
     * 获取所有视频网站域名列表
     */
    public List<String> getVideoDomains() {
        return new ArrayList<>(videoDomains);
    }

    /**
     * 检查顶级域名是否有效
     */
    public boolean isValidTld(String tld) {
        if (tld == null || tld.trim().isEmpty()) {
            return false;
        }
        String normalizedTld = tld.toLowerCase().trim();
        if (!normalizedTld.startsWith(".")) {
            normalizedTld = "." + normalizedTld;
        }
        return validTlds.contains(normalizedTld);
    }

    /**
     * 获取所有有效的顶级域名列表
     */
    public List<String> getTlds() {
        return new ArrayList<>(validTlds);
    }

    /**
     * 获取所有有效顶级域名列表
     */
    public List<String> getValidTlds() {
        return new ArrayList<>(validTlds);
    }

    /**
     * 获取新顶级域名信息
     */
    public Map<String, Object> getNewGtldInfo(String label) {
        if (label == null || label.trim().isEmpty()) {
            return Collections.emptyMap();
        }
        return newGtldInfo.getOrDefault(label.toLowerCase().trim(), Collections.emptyMap());
    }

    /**
     * 检查是否为动态域名
     *
     * @param domain 域名
     * @return 是否为动态域名
     */
    public boolean isDynamicDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        return dynamicDomains.contains(domain.toLowerCase().trim());
    }

    /**
     * 检查是否为CDN域名
     *
     * @param domain 域名
     * @return 是否为CDN域名
     */
    public boolean isCdnDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        return cdnDomains.contains(domain.toLowerCase().trim());
    }


    private void loadResourceFile(String resourcePath, Set<String> targetSet, String description) {
        try (InputStream is = getClass().getResourceAsStream(resourcePath)) {
            if (is == null) {
                log.warn("{}文件未找到: {}", description, resourcePath);
                return;
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String trimmedLine = line.trim();
                    if (!trimmedLine.isEmpty() && !trimmedLine.startsWith("#")) {
                        targetSet.add(trimmedLine.toLowerCase());
                    }
                }
                log.info("成功加载{} {}个", description, targetSet.size());
            }
        } catch (Exception e) {
            log.error("加载{}失败: {}", description, resourcePath, e);
        }
    }

    private void loadCdnDomains() {
        loadResourceFile("/domain/cdn_domains.txt", cdnDomains, "CDN域名");
    }

    private void loadDynamicDomains() {
        loadResourceFile("/domain/dynamic_domains.txt", dynamicDomains, "动态域名");
    }


    /**
     * 加载挖矿域名
     */
    private void loadMiningDomains() {
        loadResourceFile("/domain/mining_domains.txt", miningDomains, "挖矿域名");
    }

    /**
     * 加载热门域名
     */
    private void loadPopularDomains() {
        try (InputStream is = getClass().getResourceAsStream("/domain/popular_domains.csv")) {
            if (is == null) {
                log.warn("热门域名文件未找到: /domain/popular_domains.csv");
                return;
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String[] parts = line.split(",");
                    if (parts.length == 2) {
                        popularDomains.put(parts[1].trim().toLowerCase(), Integer.parseInt(parts[0].trim()));
                    }
                }
                log.info("成功加载热门域名 {} 个", popularDomains.size());
            }
        } catch (IOException | NumberFormatException e) {
            log.error("加载热门域名失败", e);
        }
    }

    /**
     * 加载视频网站域名
     */
    private void loadVideoDomains() {
        loadResourceFile("/domain/video_domains.txt", videoDomains, "视频网站域名");
    }

    /**
     * 加载顶级域名
     */
    private void loadValidTlds() {
        try (InputStream is = getClass().getResourceAsStream("/domain/tld.txt")) {
            if (is == null) {
                log.warn("顶级域名文件未找到: /domain/tld.txt");
                return;
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    String tld = line.trim();
                    if (!tld.isEmpty()) {
                        validTlds.add(tld.toLowerCase());
                    }
                }
                log.info("成功加载顶级域名 {} 个", validTlds.size());
            }
        } catch (IOException e) {
            log.error("加载顶级域名失败", e);
        }
    }

    /**
     * 加载新顶级域名信息
     */
    private void loadNewGtldInfo() {
        try (InputStream is = getClass().getResourceAsStream("/domain/new_gtld.csv")) {
            if (is == null) {
                log.warn("新顶级域名文件未找到: /domain/new_gtld.csv");
                return;
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
                String line;
                boolean isFirstLine = true;
                String[] headers = null;

                while ((line = reader.readLine()) != null) {
                    if (isFirstLine) {
                        headers = line.split(",");
                        isFirstLine = false;
                        continue;
                    }

                    String[] parts = line.split(",");
                    if (parts.length >= 4 && headers != null) {
                        String label = parts[3].trim();
                        if (!label.isEmpty()) {
                            Map<String, Object> info = new HashMap<>();
                            for (int i = 0; i < Math.min(parts.length, headers.length); i++) {
                                info.put(headers[i].trim(), parts[i].trim());
                            }
                            newGtldInfo.put(label.toLowerCase(), info);
                        }
                    }
                }
                log.info("成功加载新顶级域名信息 {} 个", newGtldInfo.size());
            }
        } catch (IOException e) {
            log.error("加载新顶级域名信息失败", e);
        }
    }

    /**
     * 检查域名是否为恶意域名
     *
     * @param domain 域名
     * @return 是否为恶意域名
     */
    public boolean isMaliciousDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }
        return maliciousDomains.contains(domain.toLowerCase().trim());
    }

    /**
     * 获取所有顶级域名列表（兼容方法）
     */
    public List<String> getAllTlds() {
        return getValidTlds();
    }

    /**
     * 加载恶意域名列表
     */
    private void loadMaliciousDomains() {
        // 这里可以从威胁情报数据库或外部源加载恶意域名
        // 暂时添加一些示例恶意域名用于测试
        maliciousDomains.addAll(Set.of(
                "malware-example.com",
                "phishing-test.net",
                "botnet-c2.org",
                "suspicious-domain.info"
        ));

        // 也可以从资源文件加载
        loadResourceFile("/domain/malicious_domains.txt", maliciousDomains, "恶意域名");

        log.info("成功加载恶意域名 {} 个", maliciousDomains.size());
    }
}

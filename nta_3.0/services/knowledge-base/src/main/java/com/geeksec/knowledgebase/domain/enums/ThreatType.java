package com.geeksec.knowledgebase.domain.enums;

import lombok.Getter;

/**
 * 威胁类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum ThreatType {
    
    /**
     * 恶意软件
     */
    MALWARE("恶意软件", "包括病毒、木马、蠕虫、间谍软件等各类恶意程序"),
    
    /**
     * APT攻击
     */
    APT("APT攻击", "高级持续性威胁，通常由国家级或高级黑客组织发起"),
    
    /**
     * 僵尸网络
     */
    BOTNET("僵尸网络", "由恶意软件控制的大量计算机网络"),
    
    /**
     * 钓鱼攻击
     */
    PHISHING("钓鱼攻击", "通过伪造网站或邮件获取用户敏感信息"),
    
    /**
     * 勒索软件
     */
    RANSOMWARE("勒索软件", "加密用户文件并要求赎金的恶意软件"),
    
    /**
     * 网络扫描
     */
    SCANNING("网络扫描", "对网络或系统进行探测和扫描的行为"),
    
    /**
     * 暴力破解
     */
    BRUTE_FORCE("暴力破解", "通过大量尝试密码组合进行破解的攻击"),
    
    /**
     * SQL注入
     */
    SQL_INJECTION("SQL注入", "通过恶意SQL语句攻击数据库的行为"),
    
    /**
     * XSS攻击
     */
    XSS("XSS攻击", "跨站脚本攻击，在网页中注入恶意脚本"),
    
    /**
     * DDoS攻击
     */
    DDOS("DDoS攻击", "分布式拒绝服务攻击，使目标服务不可用"),
    
    /**
     * 内网渗透
     */
    LATERAL_MOVEMENT("内网渗透", "在已入侵的网络中横向移动和扩展"),
    
    /**
     * 数据泄露
     */
    DATA_EXFILTRATION("数据泄露", "未经授权获取和传输敏感数据"),
    
    /**
     * 权限提升
     */
    PRIVILEGE_ESCALATION("权限提升", "获取更高级别的系统权限"),
    
    /**
     * 后门
     */
    BACKDOOR("后门", "在系统中植入的隐蔽访问通道"),
    
    /**
     * 社会工程学
     */
    SOCIAL_ENGINEERING("社会工程学", "通过心理操控获取信息或访问权限"),
    
    /**
     * 供应链攻击
     */
    SUPPLY_CHAIN("供应链攻击", "通过攻击软件供应链来感染目标"),
    
    /**
     * 零日漏洞利用
     */
    ZERO_DAY("零日漏洞利用", "利用未公开的安全漏洞进行攻击"),
    
    /**
     * 内部威胁
     */
    INSIDER_THREAT("内部威胁", "来自组织内部人员的安全威胁"),
    
    /**
     * 其他威胁
     */
    OTHER("其他威胁", "不属于上述分类的其他安全威胁");
    
    private final String displayName;
    private final String description;
    
    ThreatType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    

    
    /**
     * 根据显示名称获取枚举值
     */
    public static ThreatType fromDisplayName(String displayName) {
        for (ThreatType type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        return OTHER;
    }
}

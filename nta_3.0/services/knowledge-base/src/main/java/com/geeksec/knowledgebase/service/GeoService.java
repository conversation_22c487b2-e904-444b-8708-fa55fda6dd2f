package com.geeksec.knowledgebase.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 地理位置相关知识库服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class GeoService {

    // 国家信息映射 (国家代码 -> 国家信息)
    private Map<String, Map<String, Object>> countriesByCode = new HashMap<>();
    
    // 国家信息映射 (国家名称 -> 国家信息)
    private Map<String, Map<String, Object>> countriesByName = new HashMap<>();
    
    // 公司后缀映射 (国家 -> 后缀列表)
    private Map<String, List<String>> companySuffixesByCountry = new HashMap<>();
    
    // 后缀到国家的反向映射 (后缀 -> 国家列表)
    private Map<String, List<String>> countriesBySuffix = new HashMap<>();

    @PostConstruct
    public void init() {
        log.info("初始化地理位置知识库服务");
        loadCountries();
        loadCompanySuffixes();
        log.info("地理位置知识库服务初始化完成，国家: {} 个，公司后缀: {} 个国家",
                countriesByCode.size(), companySuffixesByCountry.size());
    }

    /**
     * 根据国家代码获取国家信息
     */
    public Map<String, Object> getCountryByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return Map.of("found", false);
        }
        
        String normalizedCode = code.toUpperCase().trim();
        Map<String, Object> country = countriesByCode.get(normalizedCode);
        
        if (country != null) {
            Map<String, Object> result = new HashMap<>(country);
            result.put("found", true);
            return result;
        }
        
        return Map.of("found", false, "code", code);
    }

    /**
     * 根据国家名称获取国家信息
     */
    public Map<String, Object> getCountryByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return Map.of("found", false);
        }
        
        String normalizedName = name.trim();
        Map<String, Object> country = countriesByName.get(normalizedName);
        
        if (country != null) {
            Map<String, Object> result = new HashMap<>(country);
            result.put("found", true);
            return result;
        }
        
        return Map.of("found", false, "name", name);
    }

    /**
     * 获取所有国家列表
     */
    public List<Map<String, Object>> getAllCountries() {
        return new ArrayList<>(countriesByCode.values());
    }

    /**
     * 根据国家获取公司后缀列表
     */
    public List<String> getCompanySuffixesByCountry(String country) {
        if (country == null || country.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return companySuffixesByCountry.getOrDefault(country.trim(), new ArrayList<>());
    }

    /**
     * 根据后缀获取国家列表
     */
    public List<String> getCountriesBySuffix(String suffix) {
        if (suffix == null || suffix.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return countriesBySuffix.getOrDefault(suffix.trim(), new ArrayList<>());
    }

    /**
     * 获取所有公司后缀映射
     */
    public Map<String, List<String>> getAllCompanySuffixes() {
        return new HashMap<>(companySuffixesByCountry);
    }

    /**
     * 搜索国家
     */
    public List<Map<String, Object>> searchCountries(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        String lowerKeyword = keyword.toLowerCase().trim();
        
        return countriesByCode.values().stream()
                .filter(country -> {
                    String cnName = (String) country.get("cnName");
                    String enName = (String) country.get("enName");
                    String lengthTwo = (String) country.get("lengthTwo");
                    String lengthThree = (String) country.get("lengthThree");
                    
                    return (cnName != null && cnName.toLowerCase().contains(lowerKeyword)) ||
                           (enName != null && enName.toLowerCase().contains(lowerKeyword)) ||
                           (lengthTwo != null && lengthTwo.toLowerCase().contains(lowerKeyword)) ||
                           (lengthThree != null && lengthThree.toLowerCase().contains(lowerKeyword));
                })
                .collect(Collectors.toList());
    }

    /**
     * 加载国家数据
     */
    private void loadCountries() {
        try (InputStream is = getClass().getResourceAsStream("/geo/C.csv");
             BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            
            if (is == null) {
                log.warn("国家数据文件未找到: /geo/C.csv");
                return;
            }
            
            String line;
            boolean isFirstLine = true;
            
            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    isFirstLine = false;
                    continue; // 跳过标题行
                }
                
                String[] parts = line.split(",");
                if (parts.length >= 5) {
                    String cnName = parts[0].trim();
                    String enName = parts[1].trim();
                    String lengthTwo = parts[2].trim();
                    String lengthThree = parts[3].trim();
                    String code = parts[4].trim();
                    
                    Map<String, Object> countryInfo = Map.of(
                            "cnName", cnName,
                            "enName", enName,
                            "lengthTwo", lengthTwo,
                            "lengthThree", lengthThree,
                            "code", code
                    );
                    
                    // 按代码索引
                    if (!lengthTwo.isEmpty() && !lengthTwo.equals("—")) {
                        countriesByCode.put(lengthTwo.toUpperCase(), countryInfo);
                    }
                    if (!lengthThree.isEmpty() && !lengthThree.equals("—")) {
                        countriesByCode.put(lengthThree.toUpperCase(), countryInfo);
                    }
                    
                    // 按名称索引
                    if (!cnName.isEmpty()) {
                        countriesByName.put(cnName, countryInfo);
                    }
                    if (!enName.isEmpty()) {
                        countriesByName.put(enName, countryInfo);
                    }
                }
            }
            
            log.info("成功加载国家数据 {} 个", countriesByCode.size());
            
        } catch (IOException e) {
            log.error("加载国家数据失败", e);
        }
    }

    /**
     * 加载公司后缀数据
     */
    private void loadCompanySuffixes() {
        try (InputStream is = getClass().getResourceAsStream("/geo/companySuffixesByCountry.csv");
             BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            
            if (is == null) {
                log.warn("公司后缀数据文件未找到: /geo/companySuffixesByCountry.csv");
                return;
            }
            
            String line;
            boolean isFirstLine = true;
            
            while ((line = reader.readLine()) != null) {
                if (isFirstLine) {
                    isFirstLine = false;
                    continue; // 跳过标题行
                }
                
                String[] parts = line.split(",");
                if (parts.length >= 2) {
                    String country = parts[0].trim();
                    String suffix = parts[1].trim();
                    
                    if (!country.isEmpty() && !suffix.isEmpty()) {
                        // 添加到国家->后缀映射
                        companySuffixesByCountry.computeIfAbsent(country, k -> new ArrayList<>()).add(suffix);
                        
                        // 添加到后缀->国家映射
                        countriesBySuffix.computeIfAbsent(suffix, k -> new ArrayList<>()).add(country);
                    }
                }
            }
            
            log.info("成功加载公司后缀数据，覆盖 {} 个国家", companySuffixesByCountry.size());
            
        } catch (IOException e) {
            log.error("加载公司后缀数据失败", e);
        }
    }

    /**
     * 获取IP地理位置信息
     *
     * @param ip IP地址
     * @return 地理位置信息
     */
    public Map<String, Object> getGeoLocation(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return Collections.emptyMap();
        }

        // 这里应该集成 GeoIP 数据库查询
        // 暂时返回模拟数据
        Map<String, Object> geoInfo = new HashMap<>();
        geoInfo.put("ip", ip);
        geoInfo.put("country", "Unknown");
        geoInfo.put("countryCode", "XX");
        geoInfo.put("region", "Unknown");
        geoInfo.put("city", "Unknown");
        geoInfo.put("latitude", 0.0);
        geoInfo.put("longitude", 0.0);
        geoInfo.put("timezone", "UTC");
        geoInfo.put("isp", "Unknown");
        geoInfo.put("organization", "Unknown");

        return geoInfo;
    }

}

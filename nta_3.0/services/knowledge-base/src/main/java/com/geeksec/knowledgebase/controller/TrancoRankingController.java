package com.geeksec.knowledgebase.controller;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.TrancoRankingService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/tranco-ranking")
@RequiredArgsConstructor
@Tag(name = "Tranco Ranking Controller", description = "Tranco排名查询")
public class TrancoRankingController {

    private final TrancoRankingService trancoRankingService;

    @GetMapping("/rank/{domain}")
    @Operation(summary = "获取域名Tranco排名", description = "获取单个域名的Tranco排名")
    public ApiResponse<Map<String, Object>> getDomainRank(
            @Parameter(description = "域名", example = "google.com") @PathVariable String domain) {
        log.debug("获取域名Tranco排名: {}", domain);
        int rank = trancoRankingService.getDomainRank(domain);
        Map<String, Object> response = Map.of(
                "domain", domain,
                "rank", rank,
                "exists", rank > 0
        );
        return ApiResponse.success(response);
    }

    @PostMapping("/batch-rank")
    @Operation(summary = "批量获取域名Tranco排名", description = "批量获取多个域名的Tranco排名")
    public ApiResponse<Map<String, Object>> batchGetDomainRanks(
            @Parameter(description = "域名列表") @RequestBody List<String> domains) {
        log.debug("批量获取域名Tranco排名，数量: {}", domains.size());
        Map<String, Integer> rankMap = trancoRankingService.batchGetDomainRanks(domains);
        Map<String, Object> response = Map.of(
                "count", rankMap.size(),
                "results", rankMap
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/top-domains")
    @Operation(summary = "获取Tranco Top域名列表", description = "获取指定数量的Tranco Top域名列表")
    public ApiResponse<List<Map<String, Object>>> getTopDomains(
            @Parameter(description = "限制数量", example = "10000") @RequestParam(defaultValue = "10000") int limit) {
        log.debug("获取Tranco Top域名列表，限制数量: {}", limit);
        List<Map<String, Object>> topDomains = trancoRankingService.getTopDomains(limit);
        return ApiResponse.success(topDomains);
    }

    @PostMapping("/refresh")
    @Operation(summary = "刷新Tranco排名数据", description = "手动触发Tranco排名数据的刷新")
    public ApiResponse<Map<String, Object>> refreshRankingData() {
        log.info("手动触发Tranco排名数据刷新");
        trancoRankingService.initialize();
        Map<String, Object> response = Map.of(
                "success", true,
                "message", "Tranco排名数据刷新成功"
        );
        return ApiResponse.success(response);
    }
}

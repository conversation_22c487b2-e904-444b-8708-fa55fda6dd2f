package com.geeksec.knowledgebase.util;

import com.geeksec.knowledgebase.config.KnowledgeBaseProperties;
import com.maxmind.db.Reader;
import com.maxmind.geoip2.DatabaseReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.locks.ReentrantLock;

/**
 * GeoIP数据库管理器，负责数据库的加载、更新和重载
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GeoIpDatabaseManager {

    private final KnowledgeBaseProperties.GeoIpProperties geoIpProperties;

    private DatabaseReader cityReader;
    private DatabaseReader asnReader;
    private final ReentrantLock lock = new ReentrantLock();

    private String cityDbPath;
    private String asnDbPath;

    @PostConstruct
    public void init() {
        String basePath = geoIpProperties.getDatabasePath();
        this.cityDbPath = basePath + "/" + geoIpProperties.getCityDbName();
        this.asnDbPath = basePath + "/" + geoIpProperties.getAsnDbName();
        ensureDirectoryExists(basePath);
        loadDatabases();
    }

    private void ensureDirectoryExists(String path) {
        try {
            Path dirPath = Paths.get(path);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                log.info("创建GeoIP数据库目录: {}", path);
            }
        } catch (IOException e) {
            log.error("创建GeoIP数据库目录失败: {}", path, e);
        }
    }

    public boolean reloadDatabases() {
        lock.lock();
        try {
            log.info("开始重新加载GeoIP数据库...");
            closeDatabases();
            loadDatabases();
            log.info("GeoIP数据库重新加载成功");
            return true;
        } catch (Exception e) {
            log.error("重新加载GeoIP数据库失败", e);
            return false;
        } finally {
            lock.unlock();
        }
    }

    private void loadDatabases() {
        try {
            File cityDbFile = new File(cityDbPath);
            if (cityDbFile.exists()) {
                this.cityReader = new DatabaseReader.Builder(cityDbFile).build();
                log.info("成功加载城市数据库: {}", cityDbPath);
            } else {
                log.warn("城市数据库文件不存在: {}", cityDbPath);
            }

            File asnDbFile = new File(asnDbPath);
            if (asnDbFile.exists()) {
                this.asnReader = new DatabaseReader.Builder(asnDbFile).build();
                log.info("成功加载ASN数据库: {}", asnDbPath);
            } else {
                log.warn("ASN数据库文件不存在: {}", asnDbPath);
            }
        } catch (IOException e) {
            log.error("加载GeoIP数据库失败", e);
        }
    }

    @PreDestroy
    public void close() {
        closeDatabases();
    }

    private void closeDatabases() {
        try {
            if (cityReader != null) {
                cityReader.close();
            }
            if (asnReader != null) {
                asnReader.close();
            }
            log.info("已关闭GeoIP数据库连接");
        } catch (Exception e) {
            log.error("关闭GeoIP数据库连接失败", e);
        }
    }

    public String getDatabaseInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("城市数据库: ").append(cityDbPath).append("\n");
        if (cityReader != null) {
            try {
                sb.append("  类型: ").append(cityReader.getMetadata().getDatabaseType()).append("\n");
                sb.append("  版本: ").append(cityReader.getMetadata().getBinaryFormatMajorVersion()).append(".").append(cityReader.getMetadata().getBinaryFormatMinorVersion()).append("\n");
                sb.append("  构建时间: ").append(cityReader.getMetadata().getBuildDate()).append("\n");
            } catch (Exception e) {
                sb.append("  获取城市数据库信息失败: ").append(e.getMessage()).append("\n");
            }
        } else {
            sb.append("  未加载\n");
        }
        sb.append("ASN数据库: ").append(asnDbPath).append("\n");
        if (asnReader != null) {
            try {
                sb.append("  类型: ").append(asnReader.getMetadata().getDatabaseType()).append("\n");
                sb.append("  版本: ").append(asnReader.getMetadata().getBinaryFormatMajorVersion()).append(".").append(asnReader.getMetadata().getBinaryFormatMinorVersion()).append("\n");
                sb.append("  构建时间: ").append(asnReader.getMetadata().getBuildDate()).append("\n");
            } catch (Exception e) {
                sb.append("  获取ASN数据库信息失败: ").append(e.getMessage()).append("\n");
            }
        } else {
            sb.append("  未加载\n");
        }
        return sb.toString();
    }

    public boolean updateDatabase(MultipartFile cityFile, MultipartFile asnFile) {
        lock.lock();
        try {
            log.info("开始通过文件上传更新GeoIP数据库...");
            Path cityPath = Paths.get(this.cityDbPath);
            Path asnPath = Paths.get(this.asnDbPath);

            if (cityFile != null && !cityFile.isEmpty()) {
                Files.copy(cityFile.getInputStream(), cityPath, StandardCopyOption.REPLACE_EXISTING);
                log.info("城市数据库文件已更新: {}", cityPath);
            }

            if (asnFile != null && !asnFile.isEmpty()) {
                Files.copy(asnFile.getInputStream(), asnPath, StandardCopyOption.REPLACE_EXISTING);
                log.info("ASN数据库文件已更新: {}", asnPath);
            }
            return reloadDatabases();
        } catch (IOException e) {
            log.error("通过文件上传更新GeoIP数据库失败", e);
            return false;
        } finally {
            lock.unlock();
        }
    }

    public DatabaseReader getCityReader() {
        return cityReader;
    }

    public DatabaseReader getAsnReader() {
        return asnReader;
    }
}

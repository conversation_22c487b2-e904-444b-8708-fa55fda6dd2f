package com.geeksec.knowledgebase.domain.enums;

import lombok.Getter;

/**
 * 证书标签类型枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum CertificateLabelType {
    
    /**
     * 安全相关标签
     */
    SECURITY("安全", "与证书安全性相关的标签"),
    
    /**
     * 信任相关标签
     */
    TRUST("信任", "与证书信任度相关的标签"),
    
    /**
     * 用途相关标签
     */
    USAGE("用途", "与证书用途相关的标签"),
    
    /**
     * 验证相关标签
     */
    VALIDATION("验证", "与证书验证相关的标签"),
    
    /**
     * 威胁相关标签
     */
    THREAT("威胁", "与威胁相关的证书标签"),
    
    /**
     * APT相关标签
     */
    APT("APT", "与APT攻击相关的证书标签"),
    
    /**
     * 恶意相关标签
     */
    MALICIOUS("恶意", "与恶意活动相关的证书标签");

    private final String displayName;
    private final String description;

    CertificateLabelType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }



    /**
     * 根据显示名称获取证书标签类型
     */
    public static CertificateLabelType fromDisplayName(String displayName) {
        for (CertificateLabelType type : values()) {
            if (type.displayName.equals(displayName)) {
                return type;
            }
        }
        return SECURITY;
    }
}

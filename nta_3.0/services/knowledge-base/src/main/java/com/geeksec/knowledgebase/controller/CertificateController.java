package com.geeksec.knowledgebase.controller;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.knowledgebase.service.CertificateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/certificate")
@RequiredArgsConstructor
@Tag(name = "Certificate Controller", description = "证书知识库查询")
public class CertificateController {

    private final CertificateService certificateService;

    // ==================== KnowledgeBaseClient 需要的接口 ====================

    @GetMapping("/oid/{oid}")
    @Operation(summary = "获取OID信息")
    public ApiResponse<Map<String, Object>> getOidInfo(@Parameter(description = "OID值") @PathVariable String oid) {
        log.debug("获取OID信息: {}", oid);
        Map<String, Object> oidInfo = certificateService.getOidInfo(oid);
        return ApiResponse.success(oidInfo);
    }

    @GetMapping("/oid/check/{oid}")
    @Operation(summary = "检查OID是否为已知的证书策略OID")
    public ApiResponse<Map<String, Object>> checkKnownOid(@Parameter(description = "OID值") @PathVariable String oid) {
        log.debug("检查已知OID: {}", oid);
        boolean isKnown = certificateService.isKnownOid(oid);
        Map<String, Object> response = Map.of(
                "oid", oid,
                "isKnown", isKnown
        );
        return ApiResponse.success(response);
    }

    @GetMapping("/ca/check/{issuer}")
    @Operation(summary = "检查是否为免费证书颁发机构")
    public ApiResponse<Map<String, Object>> checkFreeCertificateAuthority(@Parameter(description = "证书颁发机构") @PathVariable String issuer) {
        log.debug("检查免费证书颁发机构: {}", issuer);
        boolean isFree = certificateService.isFreeCertificateAuthority(issuer);
        Map<String, Object> response = Map.of(
                "issuer", issuer,
                "isFree", isFree
        );
        return ApiResponse.success(response);
    }

    // ==================== 现有接口（保持兼容性） ====================

    @GetMapping("/check-free/{issuer}")
    @Operation(summary = "检查是否为免费证书颁发机构（兼容接口）")
    public ApiResponse<Boolean> isFreeCertificateAuthority(@Parameter(description = "证书颁发机构") @PathVariable String issuer) {
        log.debug("检查免费证书颁发机构（兼容接口）: {}", issuer);
        boolean isFree = certificateService.isFreeCertificateAuthority(issuer);
        return ApiResponse.success(isFree);
    }

    @GetMapping("/oid-info/{oid}")
    @Operation(summary = "获取OID信息（兼容接口）")
    public ApiResponse<Map<String, Object>> getOidInfoCompat(@Parameter(description = "OID值") @PathVariable String oid) {
        log.debug("获取OID信息（兼容接口）: {}", oid);
        Map<String, Object> oidInfo = certificateService.getOidInfo(oid);
        return ApiResponse.success(oidInfo);
    }

    @GetMapping("/oids-by-type/{type}")
    @Operation(summary = "根据类型获取OIDs")
    public ApiResponse<List<Map<String, Object>>> getOidsByType(@Parameter(description = "OID类型") @PathVariable String type) {
        log.debug("根据类型获取OIDs: {}", type);
        List<Map<String, Object>> oids = certificateService.getOidsByType(type.toUpperCase());
        return ApiResponse.success(oids);
    }

    @PostMapping("/parse-subject")
    @Operation(summary = "解析证书主题")
    public ApiResponse<Map<String, String>> parseSubjectDN(@Parameter(description = "证书主题DN字符串") @RequestBody String subjectDN) {
        log.debug("解析证书主题: {}", subjectDN);
        Map<String, String> parsedSubject = certificateService.parseSubjectDN(subjectDN);
        return ApiResponse.success(parsedSubject);
    }

    @GetMapping("/trusted-ca/check/{sha1}")
    @Operation(summary = "检查证书是否为可信CA证书")
    public ApiResponse<Boolean> isTrustedCaCertificate(@Parameter(description = "证书SHA1哈希值") @PathVariable String sha1) {
        log.debug("检查可信CA证书: {}", sha1);
        boolean isTrusted = certificateService.isTrustedCaCertificate(sha1);
        return ApiResponse.success(isTrusted);
    }

    @GetMapping("/trusted-ca/list")
    @Operation(summary = "获取所有可信CA证书SHA1列表")
    public ApiResponse<List<String>> getTrustedCaCertificates() {
        log.debug("获取可信CA证书列表");
        List<String> trustedCerts = certificateService.getTrustedCaCertificates();
        return ApiResponse.success(trustedCerts);
    }

    @PostMapping("/trusted-ca/batch-check")
    @Operation(summary = "批量检查证书是否为可信CA证书")
    public ApiResponse<Map<String, Boolean>> batchCheckTrustedCaCertificates(
            @Parameter(description = "证书SHA1哈希值列表") @RequestBody List<String> sha1List) {
        log.debug("批量检查可信CA证书: {} 个", sha1List != null ? sha1List.size() : 0);
        Map<String, Boolean> result = certificateService.batchCheckTrustedCaCertificates(sha1List);
        return ApiResponse.success(result);
    }
}

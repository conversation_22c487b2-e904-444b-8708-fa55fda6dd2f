package com.geeksec.knowledgebase.repository.mapper;

import com.geeksec.knowledgebase.domain.entity.CertificateLabel;
import com.geeksec.knowledgebase.domain.enums.CertificateLabelType;
import com.mybatisflex.core.BaseMapper;
import com.mybatisflex.core.query.QueryWrapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Optional;

/**
 * 证书标签 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface CertificateLabelMapper extends BaseMapper<CertificateLabel> {

    /**
     * 根据标签ID查询证书标签
     */
    default Optional<CertificateLabel> findByLabelId(Integer labelId) {
        CertificateLabel label = selectOneByQuery(QueryWrapper.create()
                .where(CertificateLabel::getLabelId).eq(labelId)
                .and(CertificateLabel::getIsActive).eq(true));
        return Optional.ofNullable(label);
    }

    /**
     * 根据标签名称查询证书标签
     */
    default Optional<CertificateLabel> findByLabelName(String labelName) {
        CertificateLabel label = selectOneByQuery(QueryWrapper.create()
                .where(CertificateLabel::getLabelName).eq(labelName)
                .and(CertificateLabel::getIsActive).eq(true));
        return Optional.ofNullable(label);
    }

    /**
     * 根据标签类型查询激活的标签
     */
    default List<CertificateLabel> findByLabelTypeAndIsActiveTrue(CertificateLabelType labelType) {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getLabelType).eq(labelType)
                .and(CertificateLabel::getIsActive).eq(true));
    }

    /**
     * 查询所有激活的标签
     */
    default List<CertificateLabel> findByIsActiveTrue() {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getIsActive).eq(true)
                .orderBy(CertificateLabel::getLabelId));
    }

    /**
     * 根据目标类型查询激活的标签
     */
    default List<CertificateLabel> findByTargetAndIsActiveTrue(String target) {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getTarget).eq(target)
                .and(CertificateLabel::getIsActive).eq(true));
    }

    /**
     * 根据黑名单评分范围查询
     */
    default List<CertificateLabel> findByBlackScoreBetweenAndIsActiveTrue(Integer minScore, Integer maxScore) {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getBlackScore).between(minScore, maxScore)
                .and(CertificateLabel::getIsActive).eq(true));
    }

    /**
     * 根据白名单评分范围查询
     */
    default List<CertificateLabel> findByWhiteScoreBetweenAndIsActiveTrue(Integer minScore, Integer maxScore) {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getWhiteScore).between(minScore, maxScore)
                .and(CertificateLabel::getIsActive).eq(true));
    }

    /**
     * 根据标签名称模糊查询
     */
    default List<CertificateLabel> findByLabelNameContainingIgnoreCaseAndIsActiveTrue(String labelName) {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getLabelName).like(labelName)
                .and(CertificateLabel::getIsActive).eq(true));
    }

    /**
     * 统计各标签类型的数量
     */
    default List<Object[]> countByLabelType() {
        // 这个方法需要使用原生SQL或者在Service层实现
        // 暂时返回空列表，在Service层实现具体逻辑
        return List.of();
    }

    /**
     * 获取黑名单评分映射
     */
    default List<CertificateLabel> findBlackScoreMapping() {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getBlackScore).gt(0)
                .and(CertificateLabel::getIsActive).eq(true)
                .orderBy(CertificateLabel::getBlackScore, false));
    }

    /**
     * 获取白名单评分映射
     */
    default List<CertificateLabel> findWhiteScoreMapping() {
        return selectListByQuery(QueryWrapper.create()
                .where(CertificateLabel::getWhiteScore).gt(0)
                .and(CertificateLabel::getIsActive).eq(true)
                .orderBy(CertificateLabel::getWhiteScore, false));
    }
}

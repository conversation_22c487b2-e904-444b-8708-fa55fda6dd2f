package com.geeksec.knowledgebase.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警数据解析器
 * <p>
 * 负责从数据流中解析告警配置文件。
 * 此类遵循单一职责原则，专注于告警数据的解析任务。
 *
 * <AUTHOR>
 */
public final class AlarmDataParser {

    private AlarmDataParser() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 从 BufferedReader 加载告警信息映射表。
     * <p>
     * 该方法读取 CSV 格式的告警配置文件，其中第一行为标题行。
     *
     * @param reader BufferedReader 对象，提供告警数据源
     * @return 告警信息的嵌套 Map，外层 Map 的键为告警 ID，内层 Map 包含告警的各项属性
     * @throws IOException 如果读取过程中发生错误
     */
    public static Map<String, Map<String, String>> parseAlarmMap(BufferedReader reader) throws IOException {
        Map<String, Map<String, String>> result = new HashMap<>();
        String line = reader.readLine();
        if (line == null || line.trim().isEmpty()) {
            return result; // 文件为空或只有空行
        }

        // 解析标题行
        List<String> headers = new ArrayList<>();
        Collections.addAll(headers, line.trim().split(","));

        // 假设告警ID在第二列 (index 1)
        int alarmIdIndex = 1;
        if (headers.size() <= alarmIdIndex) {
            throw new IOException("告警配置文件格式错误：必须至少包含两列，且第二列为告警ID。");
        }

        // 读取数据行
        while ((line = reader.readLine()) != null) {
            String[] parts = line.trim().split(",");
            if (parts.length > alarmIdIndex) {
                Map<String, String> alarmInfoMap = new HashMap<>();
                for (int i = 0; i < Math.min(parts.length, headers.size()); i++) {
                    alarmInfoMap.put(headers.get(i), parts[i]);
                }
                result.put(parts[alarmIdIndex], alarmInfoMap);
            }
        }

        return result;
    }
}

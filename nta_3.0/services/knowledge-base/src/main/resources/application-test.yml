# Knowledge Base Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /knowledge-base

# 知识库服务特定配置
knowledge-base:
  # 测试环境数据文件配置
  data:
    # 地理位置数据
    geo:
      maxmind-db-path: ${GEO_DB_PATH:/data/nta-test/geo/GeoLite2-City.mmdb}
      asn-db-path: ${ASN_DB_PATH:/data/nta-test/geo/GeoLite2-ASN.mmdb}
      country-csv-path: ${COUNTRY_CSV_PATH:/data/nta-test/geo/C.csv}
    
    # 威胁情报数据
    threat-intel:
      update-interval: 1800  # 测试环境30分钟更新一次
      cache-size: 50000
      enable-auto-update: true
      external-sources:
        - name: test-feed-1
          url: ${THREAT_FEED_1:http://test-threat.nta.local/feed1}
        - name: test-feed-2
          url: ${THREAT_FEED_2:http://test-threat.nta.local/feed2}
    
    # 证书数据
    certificate:
      oid-csv-path: ${OID_CSV_PATH:/data/nta-test/cert/OID.csv}
      fingerprint-data-path: ${FINGERPRINT_PATH:/data/nta-test/cert/fingerprint}
    
    # Tranco排名数据
    tranco:
      data-file: ${TRANCO_FILE:/data/nta-test/tranco/tranco_latest.csv}
      update-interval: 43200  # 12小时更新一次
  
  # 测试环境缓存配置
  cache:
    # 地理位置缓存
    geo-cache:
      max-size: 50000
      ttl: 7200  # 2小时
    
    # 威胁情报缓存
    threat-cache:
      max-size: 100000
      ttl: 3600  # 1小时
    
    # 证书缓存
    cert-cache:
      max-size: 50000
      ttl: 14400  # 4小时
  
  # 测试环境API配置
  api:
    rate-limit:
      enabled: true  # 测试环境启用限流
      requests-per-minute: 500
    
    # 批量查询配置
    batch:
      max-batch-size: 500  # 测试环境中等批次
      timeout: 60000  # 60秒超时

# 日志配置
logging:
  level:
    '[com.geeksec.knowledge]': INFO
    '[org.springframework.cache]': INFO

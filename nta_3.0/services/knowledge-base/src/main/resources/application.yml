server:
  port: 8080
  servlet:
    context-path: /knowledge-base

spring:
  application:
    name: knowledge-base-service

  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_knowledge}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_user}
    password: ${DB_PASSWORD:nta_password}
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      
      # 监控配置
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin123
      
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"



  # Redis配置 - 使用环境变量
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 5000ms

  # 缓存配置
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=10000,expireAfterWrite=30m

  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis-Flex配置
mybatis-flex:
  type-aliases-package: com.geeksec.knowledgebase.domain.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: reuse
    default-statement-timeout: 25000
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString
    default-scripting-language: org.apache.ibatis.scripting.xmltags.XMLLanguageDriver
    call-setters-on-nulls: false
    return-instance-for-empty-row: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    shrink-whitespaces-in-sql: false
    nullable-on-for-each: false

# Knife4j API文档配置
knife4j:
  enable: true
  openapi:
    title: NTA 3.0 知识库服务 API
    description: 提供威胁情报、检测规则、地理位置等知识数据的统一管理和API访问
    version: 3.0.0-SNAPSHOT
    concat: <EMAIL>
    license: Apache 2.0
    license-url: https://www.apache.org/licenses/LICENSE-2.0.html
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 实体类列表
    enable-host: false
    enable-host-text: ${KNIFE4J_HOST:localhost:8080}
    enable-request-cache: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-after-script: true

# 知识库服务配置
knowledge-base:
  # Tranco排名配置
  tranco:
    data-file: classpath:data/tranco/tranco_NNJPW.csv
    cache-ttl: 86400  # 缓存过期时间(秒)
    refresh-cron: "0 0 3 * * ?"  # 每天凌晨3点刷新

  # GeoIP配置
  geoip:
    enabled: true
    city-db-path: classpath:data/GeoLite2-City.mmdb
    asn-db-path: classpath:data/GeoLite2-ASN.mmdb
    cache-ttl: 86400  # 缓存过期时间(秒)
    refresh-cron: "0 0 2 * * ?"  # 每天凌晨2点刷新

  # 域名WHOIS配置
  domain-whois:
    data-file: classpath:data/sample_whois_db_download.csv
    cache-ttl: 86400  # 缓存过期时间(秒)
    refresh-cron: "0 0 4 * * ?"  # 每天凌晨4点刷新

  # 指纹知识配置
  fingerprint:
    data-file: classpath:data/fingerprint/fingerprint_knowledge.csv
    cache-ttl: 86400  # 缓存过期时间(秒)
    refresh-cron: "0 0 5 * * ?"  # 每天凌晨5点刷新

  # 缓存配置
  cache:
    threat-intelligence:
      ttl: 1h
      max-size: 10000
    certificate-labels:
      ttl: 30m
      max-size: 5000
    detector-config:
      ttl: 15m
      max-size: 1000
    geo-data:
      ttl: 24h
      max-size: 50000
    domain-data:
      ttl: 6h
      max-size: 100000

  # 数据同步配置
  sync:
    enabled: true
    threat-intelligence:
      interval: 6h
      sources:
        - name: internal
          url: http://internal-threat-feed/api/v1/threats
          enabled: true
    malicious-domains:
      interval: 12h
      sources:
        - name: abuse-ch
          url: https://urlhaus.abuse.ch/downloads/csv/
          enabled: true
    geoip:
      interval: 7d
      auto-update: true

# 日志配置
logging:
  level:
    "[com.geeksec.knowledgebase]": INFO
    "[org.springframework.cache]": DEBUG
    "[com.mybatisflex]": DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/knowledge-base.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,caches
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true



---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_knowledge_dev}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai

logging:
  level:
    "[com.geeksec.knowledgebase]": DEBUG
    "[org.springframework.cache]": DEBUG
    "[com.mybatisflex]": DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:postgresql}:${DB_PORT:5432}/${DB_NAME:nta_knowledge}?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_user}
    password: ${DB_PASSWORD}
  
  data:
    redis:
      host: ${REDIS_HOST:redis}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}

logging:
  level:
    "[com.geeksec.knowledgebase]": INFO
    "[org.springframework.cache]": WARN

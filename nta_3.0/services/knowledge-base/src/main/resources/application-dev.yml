# Knowledge Base Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /knowledge-base

# 知识库服务特定配置
knowledge-base:
  # 开发环境数据文件配置
  data:
    # 地理位置数据
    geo:
      maxmind-db-path: ${GEO_DB_PATH:classpath:data/GeoLite2-City.mmdb}
      asn-db-path: ${ASN_DB_PATH:classpath:data/GeoLite2-ASN.mmdb}
      country-csv-path: ${COUNTRY_CSV_PATH:classpath:geo/C.csv}
    
    # 威胁情报数据
    threat-intel:
      update-interval: 3600  # 开发环境1小时更新一次
      cache-size: 10000
      enable-auto-update: true
    
    # 证书数据
    certificate:
      oid-csv-path: ${OID_CSV_PATH:classpath:certificate/OID.csv}
      fingerprint-data-path: ${FINGERPRINT_PATH:classpath:data/fingerprint}
    
    # Tranco排名数据
    tranco:
      data-file: ${TRANCO_FILE:classpath:data/tranco/tranco_NNJPW.csv}
      update-interval: 86400  # 24小时更新一次
  
  # 开发环境缓存配置
  cache:
    # 地理位置缓存
    geo-cache:
      max-size: 10000
      ttl: 3600  # 1小时
    
    # 威胁情报缓存
    threat-cache:
      max-size: 50000
      ttl: 1800  # 30分钟
    
    # 证书缓存
    cert-cache:
      max-size: 20000
      ttl: 7200  # 2小时
  
  # 开发环境API配置
  api:
    rate-limit:
      enabled: false  # 开发环境关闭限流
      requests-per-minute: 1000
    
    # 批量查询配置
    batch:
      max-batch-size: 100  # 开发环境较小批次
      timeout: 30000  # 30秒超时

# 日志配置
logging:
  level:
    '[com.geeksec.knowledge]': DEBUG
    '[org.springframework.cache]': DEBUG

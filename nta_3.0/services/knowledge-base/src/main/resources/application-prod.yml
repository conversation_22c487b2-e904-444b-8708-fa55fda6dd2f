# Knowledge Base Service 生产环境配置
# 继承通用生产环境配置

spring:
  profiles:
    active: prod
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /knowledge-base

# 知识库服务特定配置
knowledge-base:
  # 生产环境数据文件配置
  data:
    # 地理位置数据
    geo:
      maxmind-db-path: ${GEO_DB_PATH:/data/nta-prod/geo/GeoLite2-City.mmdb}
      asn-db-path: ${ASN_DB_PATH:/data/nta-prod/geo/GeoLite2-ASN.mmdb}
      country-csv-path: ${COUNTRY_CSV_PATH:/data/nta-prod/geo/C.csv}
      backup-path: ${GEO_BACKUP_PATH:/backup/nta-prod/geo}
    
    # 威胁情报数据
    threat-intel:
      update-interval: 900  # 生产环境15分钟更新一次
      cache-size: 200000
      enable-auto-update: true
      external-sources:
        - name: prod-feed-1
          url: ${THREAT_FEED_1}
          api-key: ${THREAT_FEED_1_KEY}
        - name: prod-feed-2
          url: ${THREAT_FEED_2}
          api-key: ${THREAT_FEED_2_KEY}
        - name: prod-feed-3
          url: ${THREAT_FEED_3}
          api-key: ${THREAT_FEED_3_KEY}
    
    # 证书数据
    certificate:
      oid-csv-path: ${OID_CSV_PATH:/data/nta-prod/cert/OID.csv}
      fingerprint-data-path: ${FINGERPRINT_PATH:/data/nta-prod/cert/fingerprint}
      backup-path: ${CERT_BACKUP_PATH:/backup/nta-prod/cert}
    
    # Tranco排名数据
    tranco:
      data-file: ${TRANCO_FILE:/data/nta-prod/tranco/tranco_latest.csv}
      update-interval: 21600  # 6小时更新一次
      backup-path: ${TRANCO_BACKUP_PATH:/backup/nta-prod/tranco}
  
  # 生产环境缓存配置
  cache:
    # 地理位置缓存
    geo-cache:
      max-size: 200000
      ttl: 14400  # 4小时
    
    # 威胁情报缓存
    threat-cache:
      max-size: 500000
      ttl: 7200  # 2小时
    
    # 证书缓存
    cert-cache:
      max-size: 100000
      ttl: 28800  # 8小时
  
  # 生产环境API配置
  api:
    rate-limit:
      enabled: true  # 生产环境启用限流
      requests-per-minute: 200
      burst-capacity: 500
    
    # 批量查询配置
    batch:
      max-batch-size: 1000  # 生产环境大批次
      timeout: 120000  # 2分钟超时
  
  # 生产环境安全配置
  security:
    enable-api-key-auth: true
    enable-ip-whitelist: ${KNOWLEDGE_IP_WHITELIST_ENABLED:false}
    ip-whitelist: ${KNOWLEDGE_IP_WHITELIST:}
    enable-audit-log: true
  
  # 生产环境监控配置
  monitoring:
    enable-data-freshness-check: true
    enable-cache-metrics: true
    enable-api-metrics: true

# 日志配置
logging:
  level:
    '[com.geeksec.knowledge]': INFO
    '[org.springframework.cache]': WARN

-- 创建告警表
CREATE TABLE IF NOT EXISTS alarm (
                                     `id` VARCHAR(32) NOT NULL COMMENT 'id',
                                     `alarm_index` VARCHAR(255) COMMENT '告警索引',
                                     `type` VARCHAR(100) COMMENT '告警类型',
                                     `version` INT COMMENT '告警版本',
                                     `score` INT COMMENT '告警分数',
                                     `create_time` DATETIME COMMENT '创建时间'
)
    ENGINE=OLAP
    DUPLICATE KEY(`id`)
COMMENT "告警表，存储检测到的各类安全告警基本信息"
PARTITION BY RANGE(`create_time`) (
    PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(`id`) BUCKETS 16
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "16",
    "dynamic_partition.create_history_partition" = "true"
);

-- 创建告警攻击者表
CREATE TABLE IF NOT EXISTS alarm_attacker (
                                              `id` VARCHAR(32) NOT NULL COMMENT 'id',
                                              `alarm_id` VARCHAR(32) COMMENT '告警主表id',
                                              `ip` VARCHAR(45) COMMENT '攻击者ip',
                                              `create_time` DATETIME COMMENT '创建时间'
)
    ENGINE=OLAP
    DUPLICATE KEY(`id`)
COMMENT "告警-攻击者"
PARTITION BY RANGE(`create_time`) (
    PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(`alarm_id`) BUCKETS 16
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "16",
    "dynamic_partition.create_history_partition" = "true"
);

-- 创建告警原因表
CREATE TABLE IF NOT EXISTS alarm_reason (
                                            `id` VARCHAR(32) NOT NULL COMMENT 'id',
                                            `alarm_id` VARCHAR(32) COMMENT '告警主表id',
                                            `key` VARCHAR(255) COMMENT 'key',
                                            `actual_value` VARCHAR(255) COMMENT '值',
                                            `create_time` DATETIME COMMENT '创建时间'
)
    ENGINE=OLAP
    DUPLICATE KEY(`id`)
COMMENT "告警-原因"
PARTITION BY RANGE(`create_time`) (
    PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(`alarm_id`) BUCKETS 16
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "16",
    "dynamic_partition.create_history_partition" = "true"
);


-- 创建告警源表
CREATE TABLE IF NOT EXISTS alarm_source (
                                            `id` VARCHAR(32) NOT NULL COMMENT 'id',
                                            `alarm_id` VARCHAR(32) COMMENT '告警主表id',
                                            `alarm_status` INT COMMENT '告警状态',
                                            `alarm_type` VARCHAR(100) COMMENT '告警类型',
                                            `attack_chain_name` VARCHAR(255) COMMENT '攻击链名称',
                                            `attack_family` VARCHAR(255) COMMENT '攻击家族',
                                            `ioc` VARCHAR(255) COMMENT 'ioc',
                                            `alarm_knowledge_id` BIGINT COMMENT '告警知识id',
                                            `attack_level` INT COMMENT '攻击等级',
                                            `alarm_name` VARCHAR(255) COMMENT '告警名称',
                                            `alarm_principle` TEXT COMMENT '告警原理',
                                            `time` BIGINT COMMENT '告警时间',
                                            `attack_route` TEXT COMMENT '攻击链',
                                            `alarm_handle_method` TEXT COMMENT '告警处理方法',
                                            `alarm_related_label` TEXT COMMENT '告警关联标签',
                                            `alarm_session_list` TEXT COMMENT '告警会话列表',
                                            `attack_chain_list` TEXT COMMENT '攻击链列表',
                                            `task_id` INT COMMENT '任务id',
                                            `create_time` DATETIME COMMENT '创建时间'
)
    ENGINE=OLAP
    DUPLICATE KEY(`id`)
COMMENT "告警-源"
PARTITION BY RANGE(`create_time`) (
    PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(`alarm_id`) BUCKETS 16
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "16",
    "dynamic_partition.create_history_partition" = "true"
);


-- 创建告警目标表
CREATE TABLE IF NOT EXISTS alarm_targets (
                                             `id` VARCHAR(32) NOT NULL COMMENT 'id',
                                             `alarm_id` VARCHAR(32) COMMENT '告警主表id',
                                             `name` VARCHAR(255) COMMENT '目标名称',
                                             `type` VARCHAR(45) COMMENT 'ip',
                                             `labels` TEXT COMMENT '标签',
                                             `create_time` DATETIME COMMENT '创建时间'
)
    ENGINE=OLAP
    DUPLICATE KEY(`id`)
COMMENT "告警-目标"
PARTITION BY RANGE(`create_time`) (
    PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(`alarm_id`) BUCKETS 16
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "16",
    "dynamic_partition.create_history_partition" = "true"
);

-- 创建告警受害者表
CREATE TABLE IF NOT EXISTS alarm_victim (
                                            `id` VARCHAR(32) NOT NULL COMMENT 'id',
                                            `alarm_id` VARCHAR(32) COMMENT '告警主表id',
                                            `ip` VARCHAR(45) COMMENT 'ip',
                                            `create_time` DATETIME COMMENT '创建时间'
)
    ENGINE=OLAP
    DUPLICATE KEY(`id`)
COMMENT "告警-受害者"
PARTITION BY RANGE(`create_time`) (
    PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(`alarm_id`) BUCKETS 16
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",
    "dynamic_partition.end" = "3",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "16",
    "dynamic_partition.create_history_partition" = "true"
);
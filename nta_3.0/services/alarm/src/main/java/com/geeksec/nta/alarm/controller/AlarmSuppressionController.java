package com.geeksec.nta.alarm.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.dto.suppression.AddAlarmSuppressionRequest;
import com.geeksec.nta.alarm.dto.suppression.SuppressionCheckRequest;
import com.geeksec.nta.alarm.entity.AlarmSuppression;
import com.geeksec.nta.alarm.service.AlarmSuppressionService;
import com.geeksec.nta.alarm.vo.suppression.SuppressionCheckResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警抑制规则控制器
 * 专门处理告警抑制规则的管理和查询
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/alarm-suppression")
@RequiredArgsConstructor
@Validated
@Tag(name = "告警抑制规则管理", description = "告警抑制规则管理相关接口")
public class AlarmSuppressionController {

    private final AlarmSuppressionService alarmSuppressionService;

    // ==================== 告警抑制规则检查接口 ====================

    @PostMapping("/check")
    @Operation(summary = "检查告警是否应被抑制")
    public ApiResponse<SuppressionCheckResponse> checkAlarmSuppression(@Valid @RequestBody SuppressionCheckRequest request) {
        try {
            boolean shouldSuppress = alarmSuppressionService.shouldSuppressAlarm(
                    request.getVictim(), request.getAttacker(), request.getLabel());

            if (shouldSuppress) {
                String matchedValue = String.format("%s->%s:%s",
                        request.getAttacker(), request.getVictim(), request.getLabel());
                return ApiResponse.success(SuppressionCheckResponse.success("ALARM", matchedValue));
            } else {
                return ApiResponse.success(SuppressionCheckResponse.notSuppressed());
            }
        } catch (Exception e) {
            log.error("检查告警抑制规则失败: victim={}, attacker={}, label={}",
                    request.getVictim(), request.getAttacker(), request.getLabel(), e);
            return ApiResponse.error("检查告警抑制规则失败");
        }
    }

    @PostMapping("/check/attack-chain")
    @Operation(summary = "检查攻击链是否应被抑制")
    public ApiResponse<SuppressionCheckResponse> checkAttackChainSuppression(
            @Valid @RequestBody SuppressionCheckRequest request) {
        try {
            boolean shouldSuppress = alarmSuppressionService.shouldSuppressAttackChain(
                    request.getVictim(), request.getAttacker(), request.getLabel());

            if (shouldSuppress) {
                String matchedValue = String.format("%s->%s:%s",
                        request.getAttacker(), request.getVictim(), request.getLabel());
                return ApiResponse.success(SuppressionCheckResponse.success("ATTACK_CHAIN", matchedValue));
            } else {
                return ApiResponse.success(SuppressionCheckResponse.notSuppressed());
            }
        } catch (Exception e) {
            log.error("检查攻击链抑制规则失败: victim={}, attacker={}, label={}",
                    request.getVictim(), request.getAttacker(), request.getLabel(), e);
            return ApiResponse.error("检查攻击链抑制规则失败");
        }
    }

    @PostMapping("/check/batch")
    @Operation(summary = "批量检查告警是否应被抑制")
    public ApiResponse<List<SuppressionCheckResponse>> batchCheckAlarmSuppression(
            @Valid @RequestBody List<SuppressionCheckRequest> requests) {
        try {
            List<SuppressionCheckResponse> responses = new ArrayList<>();
            for (SuppressionCheckRequest request : requests) {
                boolean shouldSuppress = alarmSuppressionService.shouldSuppressAlarm(
                        request.getVictim(), request.getAttacker(), request.getLabel());

                if (shouldSuppress) {
                    String matchedValue = String.format("%s->%s:%s",
                            request.getAttacker(), request.getVictim(), request.getLabel());
                    responses.add(SuppressionCheckResponse.success("ALARM", matchedValue));
                } else {
                    responses.add(SuppressionCheckResponse.notSuppressed());
                }
            }
            return ApiResponse.success(responses);
        } catch (Exception e) {
            log.error("批量检查告警抑制规则失败", e);
            return ApiResponse.error("批量检查告警抑制规则失败");
        }
    }

    // ==================== 告警抑制规则管理接口 ====================

    @PostMapping
    @Operation(summary = "添加告警抑制规则")
    public ApiResponse<String> addSuppressionRule(@Valid @RequestBody AddAlarmSuppressionRequest request) {
        try {
            alarmSuppressionService.addSuppressionRule(
                    request.getVictim(), request.getAttacker(), request.getLabel());
            return ApiResponse.success("添加成功");
        } catch (Exception e) {
            log.error("添加告警抑制规则失败: victim={}, attacker={}, label={}",
                    request.getVictim(), request.getAttacker(), request.getLabel(), e);
            return ApiResponse.error("添加告警抑制规则失败");
        }
    }

    @DeleteMapping
    @Operation(summary = "移除告警抑制规则")
    public ApiResponse<String> removeSuppressionRule(
            @RequestParam String victim,
            @RequestParam String attacker,
            @RequestParam String label) {
        try {
            alarmSuppressionService.removeSuppressionRule(victim, attacker, label);
            return ApiResponse.success("移除成功");
        } catch (Exception e) {
            log.error("移除告警抑制规则失败: victim={}, attacker={}, label={}",
                    victim, attacker, label, e);
            return ApiResponse.error("移除告警抑制规则失败");
        }
    }

    @PostMapping("/batch")
    @Operation(summary = "批量添加告警抑制规则")
    public ApiResponse<String> batchAddSuppressionRules(
            @Valid @RequestBody List<AddAlarmSuppressionRequest> requests) {
        try {
            alarmSuppressionService.batchAddSuppressionRules(requests);
            return ApiResponse.success("批量添加成功");
        } catch (Exception e) {
            log.error("批量添加告警抑制规则失败: requests={}", requests, e);
            return ApiResponse.error("批量添加告警抑制规则失败");
        }
    }

    @DeleteMapping("/batch")
    @Operation(summary = "根据条件批量删除告警抑制规则")
    public ApiResponse<Integer> removeSuppressionRulesByCondition(
            @Parameter(description = "受害者IP") @RequestParam(required = false) String victim,
            @Parameter(description = "攻击者IP") @RequestParam(required = false) String attacker,
            @Parameter(description = "告警标签") @RequestParam(required = false) String label) {
        try {
            int deleteCount = alarmSuppressionService.removeSuppressionRulesByCondition(victim, attacker, label);
            return ApiResponse.success(deleteCount);
        } catch (Exception e) {
            log.error("根据条件删除告警抑制规则失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
            return ApiResponse.error("根据条件删除告警抑制规则失败");
        }
    }

    @GetMapping
    @Operation(summary = "获取告警抑制规则列表")
    public ApiResponse<List<AlarmSuppression>> getSuppressionRules(
            @Parameter(description = "受害者IP") @RequestParam(required = false) String victim,
            @Parameter(description = "攻击者IP") @RequestParam(required = false) String attacker,
            @Parameter(description = "告警标签") @RequestParam(required = false) String label) {
        try {
            List<AlarmSuppression> suppressionRules;

            if (victim != null || attacker != null || label != null) {
                suppressionRules = alarmSuppressionService.getSuppressionRulesByCondition(victim, attacker, label);
            } else {
                suppressionRules = alarmSuppressionService.getSuppressionRules();
            }

            return ApiResponse.success(suppressionRules);
        } catch (Exception e) {
            log.error("获取告警抑制规则列表失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
            return ApiResponse.error("获取告警抑制规则列表失败");
        }
    }

    // ==================== 系统管理接口 ====================

    @PostMapping("/cache/refresh")
    @Operation(summary = "刷新告警抑制规则缓存")
    public ApiResponse<Void> refreshCache() {
        try {
            alarmSuppressionService.refreshCache();
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("刷新告警抑制规则缓存失败", e);
            return ApiResponse.error("刷新告警抑制规则缓存失败");
        }
    }

}

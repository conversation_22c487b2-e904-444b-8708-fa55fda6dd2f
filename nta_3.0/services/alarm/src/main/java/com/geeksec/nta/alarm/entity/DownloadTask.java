package com.geeksec.nta.alarm.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Table("tb_download_task")
public class DownloadTask implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id( keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 创建者用户ID
     */
    @Column("user_id")
    private Integer userId;

    /**
     * 文件路径
     */
    @Column("path")
    private String path;

    /**
     * ES 下载 检索条件
     */
    @Column("query")
    private String query;

    /**
     * 前端展示字段
     */
    @Column("show_query")
    private String showQuery;

    /**
     * 全量下载为1，部分下载为0
     */
    @Column("type")
    private Integer type;

    /**
     * session 列表信息
     */
    @Column("session_id")
    private String sessionId;

    /**
     * 0 准备数据 1可下载 2重新下载 3已删除 4待删除
     */
    @Column("state")
    private Integer state;

    @Column("created_time")
    private Long createdTime;

    /**
     * 数据存储时间
     */
    @Column("end_time")
    private Long endTime;

    /**
     * 数据状态 0 删除 1存在
     */
    @Column("status")
    private Integer status;

    /**
     * 任务ID 数组
     */
    @Column("task_id")
    private String taskId;

}

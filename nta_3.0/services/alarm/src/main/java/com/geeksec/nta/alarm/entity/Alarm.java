package com.geeksec.nta.alarm.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.util.Date;

@Data
@Table(value = "alarm", schema = "nta" , comment = "告警")
public class Alarm {

    @Id(keyType = KeyType.Generator)
    @Column(value = "id" , comment = "id")
    private String id;

    @Column(value = "index" , comment = "告警索引")
    private String index;

    @Column(value = "type" , comment = "告警类型")
    private String type;

    @Column(value = "version" , comment = "告警版本")
    private int version;

    @Column(value = "score" , comment = "告警分数")
    private int score;

    @Column(value = "create_time" , comment = "创建时间")
    private Date createTime;

    @Column(ignore = true)
    private AlarmSource source;

}
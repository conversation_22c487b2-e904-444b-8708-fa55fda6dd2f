package com.geeksec.nta.alarm.controller;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.nta.alarm.dto.subscription.AlarmSubscriptionVo;
import com.geeksec.nta.alarm.dto.subscription.CreateSubscriptionRequest;
import com.geeksec.nta.alarm.dto.subscription.TestSubscriptionRequest;
import com.geeksec.nta.alarm.dto.subscription.TestSubscriptionResult;
import com.geeksec.nta.alarm.dto.subscription.UpdateSubscriptionRequest;
import com.geeksec.nta.alarm.service.AlarmSubscriptionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 告警订阅管理控制器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/alarm/subscription")
@RequiredArgsConstructor
@Tag(name = "告警订阅管理", description = "告警订阅相关接口")
public class AlarmSubscriptionController extends BaseController {

    private final AlarmSubscriptionService subscriptionService;

    /**
     * 创建订阅
     */
    @PostMapping
    @Operation(summary = "创建告警订阅")
    public ApiResponse<String> createSubscription(@Valid @RequestBody CreateSubscriptionRequest request) {
        // TODO: 从认证上下文获取用户ID
        String userId = getCurrentUserId();
        String subscriptionId = subscriptionService.createSubscription(request, userId);
        return success(subscriptionId);
    }

    /**
     * 更新订阅
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新告警订阅")
    public ApiResponse<String> updateSubscription(@PathVariable String id,
            @Valid @RequestBody UpdateSubscriptionRequest request) {
        String userId = getCurrentUserId();
        boolean result = subscriptionService.updateSubscription(id, request, userId);
        return result ? success("更新成功") : error("更新失败");
    }

    /**
     * 删除订阅
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除告警订阅")
    public ApiResponse<String> deleteSubscription(@PathVariable String id) {
        String userId = getCurrentUserId();
        boolean result = subscriptionService.deleteSubscription(id, userId);
        return result ? success("删除成功") : error("删除失败");
    }

    /**
     * 获取订阅列表
     */
    @GetMapping
    @Operation(summary = "获取订阅列表")
    public ApiResponse<PageResultVo<AlarmSubscriptionVo>> getSubscriptions(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String keyword) {
        String userId = getCurrentUserId();
        PageResultVo<AlarmSubscriptionVo> result = subscriptionService.getSubscriptions(userId, page, size, keyword);
        return success(result);
    }

    /**
     * 获取订阅详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取订阅详情")
    public ApiResponse<AlarmSubscriptionVo> getSubscription(@PathVariable String id) {
        String userId = getCurrentUserId();
        AlarmSubscriptionVo result = subscriptionService.getSubscription(id, userId);
        return success(result);
    }

    /**
     * 启用/禁用订阅
     */
    @PutMapping("/{id}/status")
    @Operation(summary = "启用/禁用订阅")
    public ApiResponse<String> toggleSubscription(@PathVariable String id,
            @RequestParam Boolean enabled) {
        String userId = getCurrentUserId();
        boolean result = subscriptionService.toggleSubscription(id, enabled, userId);
        return result ? success("操作成功") : error("操作失败");
    }

    /**
     * 测试订阅规则
     */
    @PostMapping("/test")
    @Operation(summary = "测试订阅规则")
    public ApiResponse<TestSubscriptionResult> testSubscription(@Valid @RequestBody TestSubscriptionRequest request) {
        TestSubscriptionResult result = subscriptionService.testSubscription(request);
        return success(result);
    }

    /**
     * 获取当前用户ID
     * TODO: 实现从认证上下文获取用户ID的逻辑
     */
    private String getCurrentUserId() {
        // 这里应该从Spring Security或其他认证框架获取当前用户ID
        // 暂时返回固定值用于测试
        return "test-user-001";
    }
}

package com.geeksec.nta.alarm.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 告警抑制规则实体类
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("alarm_suppression")
public class AlarmSuppression implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 抑制规则ID
     */
    @Id(keyType = KeyType.Auto)
    @Column("id")
    private Integer id;

    /**
     * 受害者IP
     */
    @Column("victim")
    private String victim;

    /**
     * 攻击者IP
     */
    @Column("attacker")
    private String attacker;

    /**
     * 告警相关标签
     */
    @Column("label")
    private String label;
}

package com.geeksec.nta.alarm.controller;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.alarm.dto.subscription.NotificationResultDto;
import com.geeksec.nta.alarm.dto.subscription.NotificationSubscriptionDto;
import com.geeksec.nta.alarm.service.AlarmSubscriptionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订阅配置同步控制器
 * 供 alarm-notification Flink 作业调用的接口
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/alarm/subscription/sync")
@RequiredArgsConstructor
@Tag(name = "订阅配置同步", description = "供alarm-notification Flink作业调用的接口")
public class SubscriptionSyncController extends BaseController {

    private final AlarmSubscriptionService subscriptionService;

    /**
     * 获取所有启用的订阅配置
     * 供 alarm-notification Flink 作业启动时获取全量订阅信息
     * 
     * @return 订阅配置列表
     */
    @GetMapping("/active")
    @Operation(summary = "获取所有启用的订阅配置", description = "供alarm-notification Flink作业启动时获取全量订阅信息")
    public ApiResponse<List<NotificationSubscriptionDto>> getActiveSubscriptions() {
        log.debug("alarm-notification Flink作业请求获取全量订阅配置");

        try {
            List<NotificationSubscriptionDto> subscriptions = subscriptionService.getActiveSubscriptions();
            log.info("返回 {} 个启用的订阅配置给alarm-notification Flink作业", subscriptions.size());

            return success(subscriptions);

        } catch (Exception e) {
            log.error("获取全量订阅配置失败", e);
            return error("获取订阅配置失败: " + e.getMessage());
        }
    }

    /**
     * 记录通知发送结果
     */
    @PostMapping("/notification-result")
    @Operation(summary = "记录通知发送结果")
    public ApiResponse<String> recordNotificationResult(@Valid @RequestBody List<NotificationResultDto> results) {
        log.debug("alarm-processor 上报通知发送结果，数量: {}", results.size());

        try {
            subscriptionService.recordNotificationResults(results);
            log.info("记录通知发送结果成功，数量: {}", results.size());
            return success("记录成功");
        } catch (Exception e) {
            log.error("记录通知发送结果失败", e);
            return error("记录失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查")
    public ApiResponse<String> health() {
        return success("OK");
    }
}

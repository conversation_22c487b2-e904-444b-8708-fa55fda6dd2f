package com.geeksec.nta.alarm.dto.subscription;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 测试订阅请求 DTO
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class TestSubscriptionRequest {
    
    /**
     * 匹配规则列表
     */
    @NotEmpty(message = "匹配规则不能为空")
    @Valid
    private List<SubscriptionRuleDto> matchRules;
    
    /**
     * 测试数据（模拟告警数据）
     */
    @NotNull(message = "测试数据不能为空")
    private Map<String, Object> testData;
}

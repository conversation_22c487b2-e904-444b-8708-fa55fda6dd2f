package com.geeksec.nta.alarm.utils;

import com.geeksec.nta.alarm.dto.condition.AlarmCommonCondition;
import com.geeksec.nta.alarm.pojo.LevelRange;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 一个用于构建 Doris/SQL 查询条件的辅助类。
 * 它将业务条件对象转换为可供 MyBatis 使用的 Map。
 */
public class DorisQueryHelper {

    /**
     * 将通用的告警查询条件对象转换为一个Map，用于MyBatis动态SQL。
     * 这是 getCommonQueryBuilder 方法的 Doris/SQL 版本。
     *
     * @param commonCondition 包含所有筛选条件的对象。
     * @return 一个包含所有有效查询条件的Map，准备传递给MyBatis。
     */
    public static Map<String, Object> buildAlarmQueryConditions(AlarmCommonCondition commonCondition) {
        Map<String, Object> conditions = new HashMap<>();

        // 1. 时间范围
        if (commonCondition.getLeft() != null && commonCondition.getLeft() > 0) {
            conditions.put("startTime", commonCondition.getLeft());
        }
        if (commonCondition.getRight() != null && commonCondition.getRight() > 0) {
            conditions.put("endTime", commonCondition.getRight());
        }

        // 2. 告警知识ID
        if (CollectionUtils.isNotEmpty(commonCondition.getAlarmIds())) {
            conditions.put("alarmKnowledgeIds", commonCondition.getAlarmIds());
        }

        // 3. 任务ID (原代码有bug，这里修复，必须检查)
        if (CollectionUtils.isNotEmpty(commonCondition.getTaskIds())) {
            conditions.put("taskIds", commonCondition.getTaskIds());
        }

        // 4. 目标名称
        if (StringUtils.isNotEmpty(commonCondition.getTargetName())) {
            conditions.put("targetName", commonCondition.getTargetName());
        }

        // 5. 受害方IP
        if (StringUtils.isNotEmpty(commonCondition.getVictim())) {
            conditions.put("victimIp", commonCondition.getVictim());
        }

        // 6. 攻击方IP
        if (StringUtils.isNotEmpty(commonCondition.getAttackerIp())) {
            conditions.put("attackerIp", commonCondition.getAttackerIp());
        }

        // 7. 处理状态
        if (CollectionUtils.isNotEmpty(commonCondition.getAlarmStatusList())) {
            conditions.put("alarmStatusList", commonCondition.getAlarmStatusList());
        }

        // 8. 威胁等级 (特殊处理，转换为LevelRange对象列表)
        List<String> attackLevelStrings = commonCondition.getAttackLevels();
        if (CollectionUtils.isNotEmpty(attackLevelStrings)) {
            List<LevelRange> levelRanges = new ArrayList<>();
            for (String attackLevel : attackLevelStrings) {
                if (StringUtils.isNotEmpty(attackLevel) && attackLevel.contains("-")) {
                    String[] split = attackLevel.split("-");
                    if (split.length == 2) {
                        try {
                            levelRanges.add(new LevelRange(Integer.parseInt(split[0].trim()), Integer.parseInt(split[1].trim())));
                        } catch (NumberFormatException e) {
                            // 忽略格式错误的范围
                        }
                    }
                }
            }
            if (!levelRanges.isEmpty()) {
                conditions.put("attackLevelRanges", levelRanges);
            }
        }

        return conditions;
    }
}
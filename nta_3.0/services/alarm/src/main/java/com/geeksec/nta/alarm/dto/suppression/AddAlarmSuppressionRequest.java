package com.geeksec.nta.alarm.dto.suppression;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 添加告警抑制规则请求 DTO
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "添加告警抑制规则请求")
public class AddAlarmSuppressionRequest {
    
    @NotBlank(message = "受害者IP不能为空")
    @Schema(description = "受害者IP", required = true)
    private String victim;
    
    @NotBlank(message = "攻击者IP不能为空")
    @Schema(description = "攻击者IP", required = true)
    private String attacker;
    
    @NotBlank(message = "告警标签不能为空")
    @Schema(description = "告警标签", required = true)
    private String label;
}
package com.geeksec.nta.alarm.entity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.util.Date;


@Table(value = "alarm_targets", schema = "nta" , comment = "告警-目标")
@Data
public class AlarmTargets {

    @Id(keyType = KeyType.Generator)
    @Column(value = "id" , comment = "id")
    private String id;

    @Column(value = "alarm_id" , comment = "告警主表id")
    private String alarmId;

    @Column(value = "name" , comment = "目标名称")
    private String name;

    @Column(value = "ip" , comment = "ip")
    private String type;

    @Column(value = "labels" , comment = "标签")
    private String labels;

    @Column(value = "create_time" , comment = "创建时间")
    private Date createTime;


}
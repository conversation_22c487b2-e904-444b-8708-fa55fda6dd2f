package com.geeksec.nta.alarm.entity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Table(value = "alarm_source", schema = "nta" , comment = "告警-源")
@Data
public class AlarmSource {

    @Id(keyType = KeyType.Generator)
    @Column(value = "id" , comment = "id")
    private String id;

    @Column(value = "alarm_id" , comment = "告警主表id")
    private String alarmId;

    @Column(ignore = true)
    private List<AlarmReason> alarmReason;

    @Column(value = "alarm_status" , comment = "告警状态")
    private int alarmStatus;

    @Column(value = "alarm_type" , comment = "告警类型")
    private String alarmType;

    @Column(value = "attack_chain_name" , comment = "攻击链名称")
    private String attackChainName;

    @Column(ignore = true)
    private List<AlarmTargets> targets;

    @Column(ignore = true)
    private List<AlarmVictim> victim;

    @Column(ignore = true)
    private List<AlarmAttacker> attacker;

    @Column(value = "attack_family" , comment = "攻击家族")
    private String attackFamily;

    @Column(value = "ioc" , comment = "ioc")
    private String ioc;

    @Column(value = "alarm_knowledge_id" , comment = "告警知识id")
    private long alarmKnowledgeId;

    @Column(value = "attack_level" , comment = "攻击等级")
    private int attackLevel;

    @Column(value = "alarm_name" , comment = "告警名称")
    private String alarmName;

    @Column(value = "alarm_principle" , comment = "告警原理")
    private String alarmPrinciple;

    @Column(value = "time" , comment = "告警时间")
    private long time;

    @Column(value = "attack_route" , comment = "攻击链")
    private String attackRoute;

    @Column(value = "alarm_handle_method" , comment = "告警处理方法")
    private String alarmHandleMethod;

    @Column(value = "alarm_related_label" , comment = "告警关联标签")
    private String alarmRelatedLabel;

    @Column(value = "alarm_session_list" , comment = "告警会话列表")
    private String alarmSessionList;

    @Column(value = "attack_chain_list" , comment = "攻击链列表")
    private String attackChainList;

    @Column(value = "task_id" , comment = "任务id")
    private int taskId;

    @Column(value = "create_time" , comment = "创建时间")
    private Date createTime;


}
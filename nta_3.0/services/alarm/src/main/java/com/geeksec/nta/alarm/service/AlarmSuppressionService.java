package com.geeksec.nta.alarm.service;

import com.geeksec.nta.alarm.entity.AlarmSuppression;

import java.util.List;

/**
 * 告警白名单服务接口
/**
 * 告警抑制规则服务接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface AlarmSuppressionService {
    
    // ==================== 告警抑制规则检查接口 ====================
    
    /**
     * 检查告警是否满足抑制规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 是否满足抑制规则
     */
    boolean shouldSuppressAlarm(String victim, String attacker, String label);
    
    /**
     * 检查攻击链是否满足抑制规则
     * 
     * @param attackChainList 攻击链列表
     * @return 是否满足抑制规则
     */
    boolean shouldSuppressAttackChain(List<String> attackChainList);
    
    /**
     * 批量检查告警是否满足抑制规则
     * 
     * @param alarmList 告警列表，每个元素包含 victim、attacker、label
     * @return 检查结果列表，与输入列表一一对应
     */
    List<Boolean> batchCheckAlarms(List<AlarmCheckItem> alarmList);
    
    // ==================== 告警抑制规则管理接口 ====================
    
    /**
     * 添加告警抑制规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 是否添加成功
     */
    boolean addSuppressionRule(String victim, String attacker, String label);
    
    /**
     * 移除告警抑制规则
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     * @return 是否移除成功
     */
    boolean removeSuppressionRule(String victim, String attacker, String label);
    
    /**
     * 批量添加告警抑制规则
     * 
     * @param alarmList 告警列表
     * @return 成功添加的数量
     */
    int batchAddSuppressionRules(List<AlarmCheckItem> alarmList);
    
    /**
     * 根据条件批量移除抑制规则
     * 
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @return 移除的数量
     */
    int removeSuppressionRulesByCondition(String victim, String attacker, String label);
    
    /**
     * 获取所有抑制规则
     * 
     * @return 抑制规则列表
     */
    List<AlarmSuppression> getSuppressionRules();
    
    /**
     * 根据条件获取抑制规则
     * 
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @return 抑制规则列表
     */
    List<AlarmSuppression> getSuppressionRulesByCondition(String victim, String attacker, String label);
    
    // ==================== 缓存和统计接口 ====================
    
    /**
     * 刷新缓存
     */
    void refreshCache();
    

    
    /**
     * 告警检查项
     */
    class AlarmCheckItem {
        private String victim;
        private String attacker;
        private String label;
        
        public AlarmCheckItem() {}
        
        public AlarmCheckItem(String victim, String attacker, String label) {
            this.victim = victim;
            this.attacker = attacker;
            this.label = label;
        }
        
        // Getters and Setters
        public String getVictim() {
            return victim;
        }
        
        public void setVictim(String victim) {
            this.victim = victim;
        }
        
        public String getAttacker() {
            return attacker;
        }
        
        public void setAttacker(String attacker) {
            this.attacker = attacker;
        }
        
        public String getLabel() {
            return label;
        }
        
        public void setLabel(String label) {
            this.label = label;
        }
        
        @Override
        public String toString() {
            return String.format("AlarmCheckItem{victim='%s', attacker='%s', label='%s'}", victim, attacker, label);
        }
    }
    

}

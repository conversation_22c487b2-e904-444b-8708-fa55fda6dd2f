# Alarm Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8090}
  servlet:
    context-path: /api/alarm

# 告警服务特定配置
alarm:
  # 测试环境告警规则配置
  rules:
    # 告警阈值配置
    thresholds:
      cpu-usage: 75.0  # CPU使用率告警阈值
      memory-usage: 80.0  # 内存使用率告警阈值
      disk-usage: 85.0  # 磁盘使用率告警阈值
      network-latency: 800  # 网络延迟告警阈值(ms)
    
    # 告警频率限制
    rate-limit:
      max-alarms-per-minute: 50  # 测试环境适度限制
      duplicate-alarm-interval: 600  # 重复告警间隔(秒)
  
  # 测试环境通知配置
  notification:
    # 邮件通知
    email:
      enabled: true  # 测试环境启用邮件通知
      smtp-host: ${SMTP_HOST:test-smtp.nta.local}
      smtp-port: ${SMTP_PORT:587}
      username: ${SMTP_USERNAME:nta-test}
      password: ${SMTP_PASSWORD:}
    
    # 短信通知
    sms:
      enabled: false  # 测试环境关闭短信通知
    
    # Webhook通知
    webhook:
      enabled: true  # 测试环境启用Webhook
      url: ${WEBHOOK_URL:http://test-webhook.nta.local:3000/webhook}
  
  # 测试环境数据保留策略
  retention:
    alarm-history-days: 30  # 告警历史保留30天
    metrics-history-days: 14  # 指标历史保留14天

# 日志配置
logging:
  level:
    '[com.geeksec.nta.alarm]': INFO

send-url:
  #告警报告导出
  alarm_report_export: http://***************:37777/probe_pdf_result

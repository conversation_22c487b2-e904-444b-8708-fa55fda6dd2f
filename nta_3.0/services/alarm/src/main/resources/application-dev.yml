# Alarm Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8090}
  servlet:
    context-path: /api/alarm

# 告警服务特定配置
alarm:
  # 开发环境告警规则配置
  rules:
    # 告警阈值配置
    thresholds:
      cpu-usage: 80.0  # CPU使用率告警阈值
      memory-usage: 85.0  # 内存使用率告警阈值
      disk-usage: 90.0  # 磁盘使用率告警阈值
      network-latency: 1000  # 网络延迟告警阈值(ms)
    
    # 告警频率限制
    rate-limit:
      max-alarms-per-minute: 100  # 开发环境允许更多告警
      duplicate-alarm-interval: 300  # 重复告警间隔(秒)
  
  # 开发环境通知配置
  notification:
    # 邮件通知
    email:
      enabled: false  # 开发环境关闭邮件通知
      smtp-host: ${SMTP_HOST:localhost}
      smtp-port: ${SMTP_PORT:587}
    
    # 短信通知
    sms:
      enabled: false  # 开发环境关闭短信通知
    
    # Webhook通知
    webhook:
      enabled: true  # 开发环境启用Webhook
      url: ${WEBHOOK_URL:http://localhost:3000/webhook}
  
  # 开发环境数据保留策略
  retention:
    alarm-history-days: 7  # 告警历史保留7天
    metrics-history-days: 3  # 指标历史保留3天

# 日志配置
logging:
  level:
    '[com.geeksec.nta.alarm]': DEBUG


send-url:
  #告警报告导出
  alarm_report_export: http://***************:37777/probe_pdf_result
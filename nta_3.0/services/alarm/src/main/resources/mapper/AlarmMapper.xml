<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.mapper.AlarmMapper">


    <!-- 定义一个可复用的通用 WHERE 子句 -->
    <sql id="commonAlarmWhereClause">
        <where>
            <if test="conditions.startTime != null"> AND asrc.time &gt;= #{conditions.startTime} </if>
            <if test="conditions.endTime != null"> AND asrc.time &lt;= #{conditions.endTime} </if>
            <if test="conditions.targetName != null and conditions.targetName != ''"> AND tar.name = #{conditions.targetName} </if>
            <if test="conditions.victimIp != null and conditions.victimIp != ''"> AND vic.ip = #{conditions.victimIp} </if>
            <if test="conditions.attackerIp != null and conditions.attackerIp != ''"> AND att.ip = #{conditions.attackerIp} </if>
            <if test="conditions.alarmKnowledgeIds != null and !conditions.alarmKnowledgeIds.isEmpty()">
                AND asrc.alarm_knowledge_id IN
                <foreach item="id" collection="conditions.alarmKnowledgeIds" open="(" separator="," close=")">#{id}</foreach>
            </if>
            <if test="conditions.taskIds != null and !conditions.taskIds.isEmpty()">
                AND asrc.task_id IN
                <foreach item="id" collection="conditions.taskIds" open="(" separator="," close=")">#{id}</foreach>
            </if>
            <if test="conditions.alarmStatusList != null and !conditions.alarmStatusList.isEmpty()">
                AND asrc.alarm_status IN
                <foreach item="status" collection="conditions.alarmStatusList" open="(" separator="," close=")">#{status}</foreach>
            </if>
            <if test="conditions.attackLevelRanges != null and !conditions.attackLevelRanges.isEmpty()">
                AND (
                <foreach item="range" collection="conditions.attackLevelRanges" separator=" OR ">
                    (asrc.attack_level &gt;= #{range.min} AND asrc.attack_level &lt;= #{range.max})
                </foreach>
                )
            </if>
        </where>
    </sql>

    <!--
      高效的单次聚合查询，替代了原来Java代码中的多个循环和ES请求。
    -->
    <select id="getAlarmAggregations" resultType="java.util.Map">
        SELECT
        -- 1. 告警各等级总数 (使用 getLevelRange() 中的定义)
        COUNT(CASE WHEN asrc.attack_level BETWEEN 60 AND 80 THEN 1 END) AS low_level_count,
        COUNT(CASE WHEN asrc.attack_level BETWEEN 81 AND 90 THEN 1 END) AS middle_level_count,
        COUNT(CASE WHEN asrc.attack_level BETWEEN 91 AND 100 THEN 1 END) AS high_level_count,

        -- 2. 处理状态总数
        COUNT(CASE WHEN asrc.alarm_status = 0 THEN 1 END) AS status_0_count,
        COUNT(CASE WHEN asrc.alarm_status = 1 THEN 1 END) AS status_1_count,
        COUNT(CASE WHEN asrc.alarm_status = 2 THEN 1 END) AS status_2_count
        FROM
        alarm a
        INNER JOIN alarm_source asrc ON a.id = asrc.alarm_id
        LEFT JOIN alarm_attacker att ON a.id = att.alarm_id
        LEFT JOIN alarm_victim vic ON a.id = vic.alarm_id
        LEFT JOIN alarm_targets tar ON a.id = tar.alarm_id

        <!-- 引用上面定义的通用 WHERE 子句进行过滤 -->
        <include refid="commonAlarmWhereClause"/>
    </select>

    <select id="getModelAlarmAttackChainAggr" resultType="java.util.Map">
        SELECT
            b.attack_chain_name AS attack_chain_name,
            COUNT(*) AS chain_count,
            b.alarm_knowledge_id AS alarm_knowledge_id,
            COUNT(*) AS knowledge_count
        FROM
            alarm a JOIN alarm_source b ON a.id = b.alarm_id
        WHERE
            a.alarm_index like concat('alarm','%')
          <if test="condition.endTime != null">
              and b.time &lt;= #{condition.endTime}
          </if>
          <if test="condition.startTime != null">
              and b.time &gt;= #{condition.startTime}
          </if>
          AND b.alarm_type = '模型'
          <if test="condition.taskIds != null and condition.taskIds.size() > 0">
              AND b.task_id IN
              <foreach collection="taskIds" item="taskId" separator="," close=")" open="(">
                  #{taskId}
              </foreach>
          </if>
        GROUP BY
            b.attack_chain_name, b.alarm_knowledge_id
        ORDER BY
            chain_count DESC, knowledge_count DESC;
    </select>


    <sql id="alarmJoins">
        INNER JOIN alarm_source asrc ON a.id = asrc.alarm_id
        LEFT JOIN alarm_attacker attacker ON a.id = attacker.alarm_id
        LEFT JOIN alarm_victim victim ON a.id = victim.alarm_id
        LEFT JOIN alarm_targets targets ON a.id = targets.alarm_id
        LEFT JOIN knowledge_type_vo ktv ON asrc.alarm_knowledge_id = ktv.id
        LEFT JOIN label l ON asrc.alarm_knowledge_id = l.id
        LEFT JOIN feature_rule fr ON asrc.alarm_knowledge_id = fr.id
        LEFT JOIN connect_info ci ON ci.SessionId = (
            CASE
                WHEN asrc.alarm_type = '模型' AND JSON_VALID(asrc.alarm_session_list) THEN JSON_UNQUOTE(JSON_EXTRACT(asrc.alarm_session_list, '$[0]'))
                WHEN asrc.alarm_type = '规则' THEN targets.name
                ELSE NULL
            END
        )
    </sql>


    <sql id="alarmConditions">
        <where>
            <!-- Priority 1: If a list of specific alarm IDs is provided, use that and ignore most other filters -->
            <if test="params.ids != null and !params.ids.isEmpty()">
                a.id IN
                <foreach item="id" collection="params.ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
                <!-- Still filter by task_id even when querying by specific alarm IDs -->
                <if test="params.taskIds != null and !params.taskIds.isEmpty()">
                    AND asrc.task_id IN
                    <foreach item="taskId" collection="params.taskIds" open="(" separator="," close=")">
                        #{taskId}
                    </foreach>
                </if>
            </if>


            <if test="params.ids == null or params.ids.isEmpty()">
                <if test="params.alarmType != null and params.alarmType != ''">
                    AND asrc.alarm_type = #{params.alarmType}
                </if>

                <!-- Time Range Filter (from getCommonQueryBuilder) -->
                <if test="params.left != null and params.left > 0">
                    AND asrc.time >= #{params.left}
                </if>
                <if test="params.right != null and params.right > 0">
                    AND asrc.time &lt;= #{params.right}
                </if>

                <!-- Alarm Name (knowledge_id) Filter -->
                <if test="params.alarmIds != null and !params.alarmIds.isEmpty()">
                    AND asrc.alarm_knowledge_id IN
                    <foreach item="alarmId" collection="params.alarmIds" open="(" separator="," close=")">
                        #{alarmId}
                    </foreach>
                </if>

                <!-- Task ID Filter -->
                <if test="params.taskIds != null and !params.taskIds.isEmpty()">
                    AND asrc.task_id IN
                    <foreach item="taskId" collection="params.taskIds" open="(" separator="," close=")">
                        #{taskId}
                    </foreach>
                </if>

                <!-- Target Name Filter -->
                <if test="params.targetName != null and params.targetName != ''">
                    AND targets.name = #{params.targetName}
                </if>

                <!-- Victim IP Filter -->
                <if test="params.victim != null and params.victim != ''">
                    AND victim.ip = #{params.victim}
                </if>

                <!-- Attacker IP Filter -->
                <if test="params.attackerIp != null and params.attackerIp != ''">
                    AND attacker.ip = #{params.attackerIp}
                </if>

                <!-- Threat Level (Attack Level) Range Filter - This is the most complex one -->
                <if test="params.attackLevelRanges != null and !params.attackLevelRanges.isEmpty()">
                    AND (
                    <foreach item="range" collection="params.attackLevelRanges" separator=" OR ">
                        (asrc.attack_level >= #{range.min} AND asrc.attack_level &lt;= #{range.max})
                    </foreach>
                    )
                </if>

                <!-- Alarm Status Filter -->
                <if test="params.alarmStatusList != null and !params.alarmStatusList.isEmpty()">
                    AND asrc.alarm_status IN
                    <foreach item="status" collection="params.alarmStatusList" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </if>
            </if>
        </where>
    </sql>


    <select id="queryAlarmList" resultType="java.util.Map">
        SELECT
        a.id, a.alarm_index, asrc.alarm_type, asrc.attack_chain_name,
        asrc.attack_family, asrc.ioc, asrc.alarm_knowledge_id, asrc.attack_level,
        asrc.alarm_principle, FROM_UNIXTIME(asrc.time) AS alarm_time,
        asrc.alarm_handle_method, asrc.task_id, a.create_time,
        ktv.attack_type AS attack_type,
        COALESCE(ktv.attack_type_name, '未知') AS attack_type_name,
        COALESCE(asrc.alarm_name, ktv.alarm_name, l.display_name, fr.rule_name, '未知') AS alarm_name,
        attacker.ip AS attacker_ip,
        victim.ip AS victim_ip,
        COALESCE(ci.sIp, '') AS sIp,
        COALESCE(ci.dIp, '') AS dIp,
        ci.sPort, ci.dPort,
        COALESCE(ci.ProName, '') AS ProName
        FROM
        alarm a
        <include refid="alarmJoins"/>
        <include refid="alarmConditions"/>

        <choose>
            <when test="params.orderField != null and params.orderField != ''">
                ORDER BY ${params.orderField} <if test="params.asc == false">DESC</if><if test="params.asc == true">ASC</if>
            </when>
            <otherwise>
                ORDER BY a.create_time DESC
            </otherwise>
        </choose>

        <if test="params.pageSize > 0">
            LIMIT #{params.pageSize} OFFSET #{params.offset}
        </if>
    </select>


    <select id="countAlarmList" resultType="long">
        SELECT COUNT(a.id)
        FROM alarm a
        <!-- For COUNT, only join tables that are necessary for the WHERE clause -->
        INNER JOIN alarm_source asrc ON a.id = asrc.alarm_id
        <if test="params.targetName != null and params.targetName != ''">
            LEFT JOIN alarm_targets targets ON a.id = targets.alarm_id
        </if>
        <if test="params.victim != null and params.victim != ''">
            LEFT JOIN alarm_victim victim ON a.id = victim.alarm_id
        </if>
        <if test="params.attackerIp != null and params.attackerIp != ''">
            LEFT JOIN alarm_attacker attacker ON a.id = attacker.alarm_id
        </if>
        <include refid="alarmConditions"/>
    </select>

    <select id="getAlarmDetail" resultType="java.util.Map">
        SELECT
            -- 1. 从 alarm 和 alarm_source 表中选择所有基础字段
            a.id,
            a.alarm_index,
            asrc.*,

            -- 2. 使用 COALESCE 实现字段的优先级填充，替代Java中的if-else逻辑
            -- 告警名称: 优先用 asrc.alarm_name, 其次是 ktv.alarm_name, 再次是 l.display_name, 最后是 fr.rule_name, 都没有则为 '未知'
            COALESCE(asrc.alarm_name, ktv.alarm_name, l.display_name, fr.rule_name, '未知') AS final_alarm_name,
            -- 攻击类型: 优先用 ktv.attack_type, 否则为 0
            COALESCE(ktv.attack_type, 0) AS attack_type,
            -- 攻击类型名称: 优先用 ktv.attack_type_name, 否则为 '未知'
            COALESCE(ktv.attack_type_name, '未知') AS attack_type_name,

            -- 3. 从关联表中获取攻击者和受害者IP
            attacker.ip AS attacker_ip,
            victim.ip AS victim_ip

        FROM
            alarm a
                -- 内部联接告警源信息表，因为这是一对一的核心数据
                INNER JOIN alarm_source asrc ON a.id = asrc.alarm_id
                -- 使用 LEFT JOIN 关联所有可选信息表。
                -- 因为告警可能没有攻击者信息，或者没有对应的标签/规则信息，LEFT JOIN能确保即使关联不上，主告警信息也能被查出。
                LEFT JOIN alarm_attacker attacker ON a.id = attacker.alarm_id
                LEFT JOIN alarm_victim victim ON a.id = victim.alarm_id
                LEFT JOIN knowledge_type_vo ktv ON asrc.alarm_knowledge_id = ktv.id
                LEFT JOIN label l ON asrc.alarm_knowledge_id = l.id
                LEFT JOIN feature_rule fr ON asrc.alarm_knowledge_id = fr.id
        WHERE
            a.id = #{id} AND a.alarm_index = #{index}
        LIMIT 1; -- 详情查询，确保只返回一条记录
    </select>


    <select id="findSourceForUpdate" resultType="java.util.Map">
        SELECT
            attack_chain_list
        FROM
            alarm_source
        WHERE
            alarm_id = #{id} AND task_id = #{taskId}
        LIMIT 1;
    </select>

    <update id="updateAlarmStatus">
        UPDATE alarm_source
        SET
            alarm_status = #{alarmStatus}
        WHERE
            alarm_id = #{id} AND task_id = #{taskId};
    </update>

    <!-- 第一步：找出所有待删除的 alarm_id -->
    <select id="findAlarmIdsForDeletion" resultType="java.lang.String">
        SELECT alarm_id FROM alarm_source
        <where>
            <!-- 遍历Map，为每个entry生成一个 (task_id = ? AND alarm_id IN (...)) 条件 -->
            <foreach collection="map.entries" item="entry" separator=" OR ">
                <!-- 确保entry的value（即ID列表）不为空 -->
                <if test="entry.value != null and !entry.value.isEmpty()">
                    (task_id = #{entry.key} AND alarm_id IN
                    <foreach collection="entry.value" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    )
                </if>
            </foreach>
        </where>
    </select>

    <!-- 第二步：根据ID列表批量删除 -->
    <!-- 注意：为了逻辑清晰和避免潜在的约束问题，通常先删除子表记录，最后删除主表记录。 -->

    <delete id="deleteFromAlarmSourcesByIds">
        DELETE FROM alarm_source WHERE alarm_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
    <delete id="deleteFromAlarmAttackersByIds">
        DELETE FROM alarm_attacker WHERE alarm_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
    <delete id="deleteFromAlarmReasonsByIds">
        DELETE FROM alarm_reason WHERE alarm_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
    <delete id="deleteFromAlarmTargetsByIds">
        DELETE FROM alarm_targets WHERE alarm_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
    <delete id="deleteFromAlarmVictimsByIds">
        DELETE FROM alarm_victim WHERE alarm_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>
    <!-- 最后删除主表记录 -->
    <delete id="deleteFromAlarmsByIds">
        DELETE FROM alarm WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </delete>


    <!--
        使用 TRUNCATE TABLE 命令来高效地清空告警相关表。
        TRUNCATE 比 DELETE FROM ... (不带WHERE) 的效率高得多。
    -->
    <update id="truncateAlarms">
        TRUNCATE TABLE alarm;
    </update>

    <update id="truncateAlarmSources">
        TRUNCATE TABLE alarm_source;
    </update>

    <update id="truncateAlarmAttackers">
        TRUNCATE TABLE alarm_attacker;
    </update>

    <update id="truncateAlarmVictims">
        TRUNCATE TABLE alarm_victim;
    </update>

    <update id="truncateAlarmTargets">
        TRUNCATE TABLE alarm_targets;
    </update>

    <update id="truncateAlarmReasons">
        TRUNCATE TABLE alarm_reason;
    </update>


    <select id="findAlarmIdsForExport" resultType="java.lang.String">
        SELECT
        a.id
        FROM
        alarm a
        INNER JOIN alarm_source asrc ON a.id = asrc.alarm_id

        <where>
            <!-- 使用 choose...when...otherwise 来处理 "按ID导出" 的特殊逻辑 -->
            <choose>
                <!-- WHEN: 如果传入了ID列表，则忽略所有其他筛选条件 -->
                <when test="condition.ids != null and !condition.ids.isEmpty()">
                    a.id IN
                    <foreach collection="condition.ids" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    <!-- 即使按ID查询，也最好用taskIds做一次限定，防止越权或数据混乱 -->
                    <if test="condition.taskIds != null and !condition.taskIds.isEmpty()">
                        AND asrc.task_id IN
                        <foreach collection="condition.taskIds" item="taskId" open="(" separator="," close=")">
                            #{taskId}
                        </foreach>
                    </if>
                </when>

                <!-- OTHERWISE: 正常根据所有筛选条件进行查询 -->
                <otherwise>
                    <if test="condition.alarmType != null and condition.alarmType != ''">
                        AND asrc.alarm_type = #{condition.alarmType}
                    </if>
                    <if test="condition.taskIds != null and !condition.taskIds.isEmpty()">
                        AND asrc.task_id IN
                        <foreach collection="condition.taskIds" item="taskId" open="(" separator="," close=")">
                            #{taskId}
                        </foreach>
                    </if>
                    <if test="condition.startTime != null">
                        AND asrc.time &gt;= #{condition.startTime}
                    </if>
                    <if test="condition.endTime != null">
                        AND asrc.time &lt;= #{condition.endTime}
                    </if>
                    <if test="condition.alarmStatus != null and !condition.alarmStatus.isEmpty()">
                        AND asrc.alarm_status IN
                        <foreach collection="condition.alarmStatus" item="status" open="(" separator="," close=")">
                            #{status}
                        </foreach>
                    </if>
                    <if test="condition.riskLevel != null and !condition.riskLevel.isEmpty()">
                        AND asrc.risk_level IN
                        <foreach collection="condition.riskLevel" item="level" open="(" separator="," close=")">
                            #{level}
                        </foreach>
                    </if>
                    <if test="condition.keyword != null and condition.keyword != ''">
                        <!-- 假设关键字需要搜索告警名称、源IP和目标IP -->
                        AND CONCAT_WS(',', asrc.alarm_name, asrc.src_ip, asrc.dst_ip) LIKE CONCAT('%', #{condition.keyword}, '%')
                    </if>
                </otherwise>
            </choose>
        </where>

        <!-- 排序逻辑 -->
        <choose>
            <when test="condition.orderField != null and condition.orderField != ''">
                <!-- 警告: ${} 可能存在SQL注入风险。必须在Java层对 orderField 进行白名单校验！ -->
                ORDER BY ${condition.orderField}
                <if test="condition.asc">ASC</if>
                <if test="!condition.asc">DESC</if>
            </when>
            <otherwise>
                ORDER BY asrc.time DESC
            </otherwise>
        </choose>
        <!-- 导出操作不需要分页，因此不加 LIMIT -->
    </select>


    <select id="findSessionMetadataBySessionIds" resultType="java.util.Map">
        SELECT
        -- 使用别名来匹配Java代码中期望的字段名（大小写敏感）
        start_time AS "StartTime",
        end_time AS "EndTime",
        session_id AS "SessionId",
        first_proto AS "FirstProto",
        thread_id AS "ThreadId",
        -- 直接从列中获取 task_id 和 batch_id，不再需要解析索引名
        task_id,
        batch_id
        FROM
        connectinfo
        WHERE
        session_id IN
        <foreach collection="sessionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="findAlarmsByRoleAndIp" resultType="java.util.Map">
        SELECT
        asrc.*
        FROM
        alarm_source asrc
        <if test="roles != null and !roles.isEmpty()">
            LEFT JOIN alarm_attacker att ON asrc.alarm_id = att.alarm_id
            LEFT JOIN alarm_victim vic ON asrc.alarm_id = vic.alarm_id
        </if>
        <where>
            <if test="timeRange != null and timeRange.left != null and timeRange.left > 0">
                AND asrc.time &gt;= #{timeRange.left}
            </if>
            <if test="timeRange != null and timeRange.right != null and timeRange.right > 0">
                AND asrc.time &lt;= #{timeRange.right}
            </if>
            <if test="roles != null and !roles.isEmpty() and ipAddr != null and ipAddr != ''">
                AND (
                <trim prefixOverrides="OR">
                    <if test="roles.contains('attacker')">
                        OR att.ip = #{ipAddr}
                    </if>
                    <if test="roles.contains('victim')">
                        OR vic.ip = #{ipAddr}
                    </if>
                </trim>
                )
            </if>
        </where>
        ORDER BY
        asrc.time DESC
        LIMIT #{limit}
    </select>



</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.mapper.NotificationTemplateMapper">

    <!-- 根据模板类型查询模板列表 -->
    <select id="selectByTemplateType" resultType="com.geeksec.nta.alarm.entity.NotificationTemplate">
        SELECT * FROM notification_template
        WHERE template_type = #{templateType}
        ORDER BY is_default DESC, created_time DESC
    </select>

    <!-- 查询默认模板 -->
    <select id="selectDefaultByType" resultType="com.geeksec.nta.alarm.entity.NotificationTemplate">
        SELECT * FROM notification_template
        WHERE template_type = #{templateType} AND is_default = true
        LIMIT 1
    </select>

    <!-- 查询所有可用模板 -->
    <select id="selectAllAvailable" resultType="com.geeksec.nta.alarm.entity.NotificationTemplate">
        SELECT * FROM notification_template
        ORDER BY template_type, is_default DESC, created_time DESC
    </select>

</mapper>

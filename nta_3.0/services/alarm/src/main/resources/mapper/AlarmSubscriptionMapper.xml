<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.mapper.AlarmSubscriptionMapper">

    <!-- 根据用户ID分页查询订阅 -->
    <select id="selectByUserIdWithPage" resultType="com.geeksec.nta.alarm.entity.AlarmSubscription">
        SELECT * FROM alarm_subscription
        WHERE user_id = #{userId}
        <if test="keyword != null and keyword != ''">
            AND (subscription_name LIKE CONCAT('%', #{keyword}, '%') 
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 查询用户的所有启用订阅 -->
    <select id="selectEnabledByUserId" resultType="com.geeksec.nta.alarm.entity.AlarmSubscription">
        SELECT * FROM alarm_subscription
        WHERE user_id = #{userId} AND enabled = true
        ORDER BY priority_level DESC, created_time DESC
    </select>

    <!-- 查询所有启用的订阅（供 alarm-notification Flink 作业启动时获取全量订阅信息） -->
    <select id="selectAllEnabled" resultType="com.geeksec.nta.alarm.entity.AlarmSubscription">
        SELECT * FROM alarm_subscription
        WHERE enabled = true
        ORDER BY priority_level DESC, created_time DESC
    </select>

    <!-- 批量更新触发统计信息 -->
    <update id="batchUpdateTriggerInfo">
        UPDATE alarm_subscription 
        SET trigger_count = COALESCE(trigger_count, 0) + 1,
            last_triggered_time = #{triggerTime}
        WHERE id IN
        <foreach collection="subscriptionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据用户ID和订阅名称查询（用于重名检查） -->
    <select id="countByUserIdAndName" resultType="long">
        SELECT COUNT(*) FROM alarm_subscription
        WHERE user_id = #{userId} AND subscription_name = #{subscriptionName}
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>

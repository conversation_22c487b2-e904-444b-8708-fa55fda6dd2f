<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.geeksec.nta.alarm.mapper.NotificationLogMapper">

    <!-- 分页查询通知记录 -->
    <select id="selectWithPage" resultType="com.geeksec.nta.alarm.entity.NotificationLog">
        SELECT * FROM notification_log
        WHERE 1=1
        <if test="subscriptionId != null and subscriptionId != ''">
            AND subscription_id = #{subscriptionId}
        </if>
        <if test="startTime != null">
            AND send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND send_time <= #{endTime}
        </if>
        ORDER BY send_time DESC
    </select>

    <!-- 批量插入通知记录 -->
    <insert id="batchInsert">
        INSERT INTO notification_log (
            id, subscription_id, alarm_id, channel_type, recipient,
            send_status, send_time, error_message, retry_count, subject, content
        ) VALUES
        <foreach collection="logs" item="log" separator=",">
            (
                #{log.id}, #{log.subscriptionId}, #{log.alarmId}, #{log.channelType}, #{log.recipient},
                #{log.sendStatus}, #{log.sendTime}, #{log.errorMessage}, #{log.retryCount}, 
                #{log.subject}, #{log.content}
            )
        </foreach>
    </insert>

    <!-- 查询订阅的通知统计 -->
    <select id="selectStatistics" resultType="com.geeksec.nta.alarm.mapper.NotificationLogMapper$NotificationStatistics">
        SELECT 
            COUNT(*) as totalCount,
            SUM(CASE WHEN send_status = 'SUCCESS' THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN send_status = 'FAILED' THEN 1 ELSE 0 END) as failedCount,
            CASE 
                WHEN COUNT(*) > 0 THEN 
                    ROUND(SUM(CASE WHEN send_status = 'SUCCESS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
                ELSE 0 
            END as successRate
        FROM notification_log
        WHERE subscription_id = #{subscriptionId}
        <if test="startTime != null">
            AND send_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND send_time <= #{endTime}
        </if>
    </select>

</mapper>

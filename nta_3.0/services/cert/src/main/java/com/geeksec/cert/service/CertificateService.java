package com.geeksec.cert.service;

import com.geeksec.cert.dto.*;
import com.geeksec.cert.entity.Certificate;

import java.util.List;
import java.util.Map;

/**
 * 证书服务接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface CertificateService {

    /**
     * 根据证书ID查询证书详情
     * 
     * @param certId 证书ID
     * @param certSource 证书来源
     * @return 证书详情
     */
    CertificateDetailResponse getCertificateDetail(String certId, String certSource);

    /**
     * 搜索证书列表
     * 
     * @param condition 搜索条件
     * @return 搜索结果
     */
    Map<String, Object> searchCertificateList(CertificateSearchCondition condition);

    /**
     * 根据类型聚合证书
     * 
     * @param condition 搜索条件
     * @return 聚合结果
     */
    Map<String, Object> aggregateCertificatesByType(CertificateSearchCondition condition);

    /**
     * 根据标签聚合证书
     * 
     * @param condition 搜索条件
     * @return 聚合结果
     */
    Map<String, Object> aggregateCertificatesByLabels(CertificateSearchCondition condition);

    /**
     * 更新证书备注
     * 
     * @param request 更新请求
     * @return 更新结果
     */
    boolean updateCertificateRemarks(CertificateUpdateRequest request);

    /**
     * 修改证书标签
     * 
     * @param request 更新请求
     * @return 更新结果
     */
    boolean modifyCertificateLabels(CertificateUpdateRequest request);

    /**
     * 下载证书文件
     * 
     * @param certId 证书ID
     * @return 证书文件字节数组
     */
    byte[] downloadCertificate(String certId);

    /**
     * 上传证书文件
     * 
     * @param request 上传请求
     * @return 上传结果
     */
    Map<String, Object> uploadCertificateFile(CertificateUploadRequest request);

    /**
     * 获取证书文件上传状态
     * 
     * @return 上传状态
     */
    CertificateFileStatusResponse getCertificateFileStatus();

    /**
     * 获取证书文件列表
     * 
     * @param condition 查询条件
     * @return 文件列表
     */
    Map<String, Object> getCertificateFileList(CertificateSearchCondition condition);

    /**
     * 根据证书ID列表查询证书信息
     * 
     * @param certIds 证书ID列表
     * @return 证书列表
     */
    List<CertificateDto> getCertificatesByCertIds(List<String> certIds);

    /**
     * 根据威胁等级查询证书
     * 
     * @param minThreatLevel 最小威胁等级
     * @param maxThreatLevel 最大威胁等级
     * @param limit 限制数量
     * @return 证书列表
     */
    List<CertificateDto> getCertificatesByThreatLevel(Integer minThreatLevel, Integer maxThreatLevel, Integer limit);

    /**
     * 根据信任等级查询证书
     * 
     * @param minTrustLevel 最小信任等级
     * @param maxTrustLevel 最大信任等级
     * @param limit 限制数量
     * @return 证书列表
     */
    List<CertificateDto> getCertificatesByTrustLevel(Integer minTrustLevel, Integer maxTrustLevel, Integer limit);

    /**
     * 获取证书统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getCertificateStatistics();

    /**
     * 获取最近活跃的证书
     * 
     * @param days 天数
     * @param limit 限制数量
     * @return 证书列表
     */
    List<CertificateDto> getRecentActiveCertificates(Integer days, Integer limit);

    /**
     * 根据关键字搜索证书
     * 
     * @param keyword 关键字
     * @param limit 限制数量
     * @return 证书列表
     */
    List<CertificateDto> searchCertificatesByKeyword(String keyword, Integer limit);

    /**
     * 批量更新证书信息
     * 
     * @param certificates 证书列表
     * @return 更新结果
     */
    boolean batchUpdateCertificates(List<Certificate> certificates);

    /**
     * 检查证书是否在内部白名单中
     * 
     * @param certSha1 证书SHA1
     * @return 是否在白名单中
     */
    boolean isCertificateInWhitelist(String certSha1);
}

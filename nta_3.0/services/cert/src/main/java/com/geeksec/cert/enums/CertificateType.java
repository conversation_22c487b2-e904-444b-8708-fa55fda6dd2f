package com.geeksec.cert.enums;

import lombok.Getter;

/**
 * 证书类型枚举
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Getter
public enum CertificateType {
    
    /**
     * 白名单证书
     */
    WHITELIST(0, "白名单"),
    
    /**
     * 黑名单证书
     */
    BLACKLIST(1, "黑名单");
    
    private final int code;
    private final String description;
    
    CertificateType(int code, String description) {
        this.code = code;
        this.description = description;
    }
    

    
    /**
     * 根据代码获取证书类型
     * 
     * @param code 类型代码
     * @return 证书类型
     */
    public static CertificateType fromCode(int code) {
        for (CertificateType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的证书类型代码: " + code);
    }
}

package com.geeksec.cert.repository.graph;

import com.geeksec.cert.dto.graph.GraphNextCondition;
import com.geeksec.cert.dto.graph.GraphPropertiesCondition;
import com.geeksec.cert.dto.graph.VertexEdgeDto;
import com.geeksec.cert.entity.graph.CertificateVertex;
import org.nebula.contrib.ngbatis.annotations.Space;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 证书图数据库访问接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Repository
@Space("nta_graph")
public interface CertificateGraphRepository extends NebulaDaoBasic<CertificateVertex, String> {

    /**
     * 查询证书所有边类型关联数据
     * 
     * @param certId 证书ID
     * @return 关联数据列表
     */
    List<VertexEdgeDto> listCertAllEdgeTypeAssociation(String certId);

    /**
     * 查询证书所有边类型关联数据 (分页)
     * 
     * @param condition 查询条件
     * @return 关联数据列表
     */
    List<VertexEdgeDto> listCertAllEdgeTypeAssociationNext(GraphNextCondition condition);

    /**
     * 证书属性关联查询 (分页)
     * 
     * @param condition 查询条件
     * @return 关联数据列表
     */
    List<VertexEdgeDto> listCertNebulaNextByProperties(GraphPropertiesCondition condition);

    /**
     * 根据证书ID列表查询存在的证书属性
     * 
     * @param certIds 证书ID列表
     * @return 存在的证书属性列表
     */
    List<Map<String, Object>> listExistCertProperties(List<String> certIds);

    /**
     * 根据证书ID列表查询证书详情
     * 
     * @param certIds 证书ID列表
     * @return 证书详情列表
     */
    List<CertificateVertex> listByCertIds(List<String> certIds);

    /**
     * 根据证书ID列表查询关联的IP列表
     * 
     * @param certIds 证书ID列表
     * @return 关联IP信息列表
     */
    List<Map<String, Object>> listRelatedIpsByCertIds(List<String> certIds);

    /**
     * 根据证书ID列表统计关联域名数量
     * 
     * @param certIds 证书ID列表
     * @return 域名数量统计列表
     */
    List<Map<String, Object>> countDomainNumByCertIds(List<String> certIds);

    /**
     * 根据证书ID列表统计关联IP数量
     * 
     * @param certIds 证书ID列表
     * @return IP数量统计列表
     */
    List<Map<String, Object>> countIpNumByCertIds(List<String> certIds);

    /**
     * 查询证书的父证书链
     * 
     * @param certId 证书ID
     * @return 父证书链列表
     */
    List<CertificateVertex> getCertificateChain(String certId);

    /**
     * 查询证书的子证书列表
     * 
     * @param certId 证书ID
     * @return 子证书列表
     */
    List<CertificateVertex> getChildCertificates(String certId);

    /**
     * 查询与指定证书相似的证书
     * 
     * @param certId 证书ID
     * @param similarity 相似度阈值
     * @param limit 限制数量
     * @return 相似证书列表
     */
    List<CertificateVertex> getSimilarCertificates(String certId, Double similarity, Integer limit);

    /**
     * 查询证书的信任路径
     * 
     * @param fromCertId 起始证书ID
     * @param toCertId 目标证书ID
     * @param maxHops 最大跳数
     * @return 信任路径列表
     */
    List<List<CertificateVertex>> getTrustPaths(String fromCertId, String toCertId, Integer maxHops);

    /**
     * 查询证书的风险传播路径
     * 
     * @param certId 证书ID
     * @param maxHops 最大跳数
     * @param threatThreshold 威胁阈值
     * @return 风险传播路径列表
     */
    List<VertexEdgeDto> getRiskPropagationPaths(String certId, Integer maxHops, Integer threatThreshold);

    /**
     * 统计证书的关联实体数量
     * 
     * @param certId 证书ID
     * @return 关联实体统计
     */
    Map<String, Long> countRelatedEntities(String certId);

    /**
     * 查询证书的时间线关联
     * 
     * @param certId 证书ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间线关联数据
     */
    List<VertexEdgeDto> getTimelineAssociations(String certId, Long startTime, Long endTime);

    /**
     * 批量插入或更新证书顶点
     * 
     * @param certificates 证书列表
     * @return 影响行数
     */
    int batchInsertOrUpdateVertices(List<CertificateVertex> certificates);

    /**
     * 创建证书之间的关联边
     * 
     * @param fromCertId 起始证书ID
     * @param toCertId 目标证书ID
     * @param edgeType 边类型
     * @param properties 边属性
     * @return 是否成功
     */
    boolean createCertificateRelation(String fromCertId, String toCertId, String edgeType, Map<String, Object> properties);

    /**
     * 删除证书之间的关联边
     * 
     * @param fromCertId 起始证书ID
     * @param toCertId 目标证书ID
     * @param edgeType 边类型
     * @return 是否成功
     */
    boolean deleteCertificateRelation(String fromCertId, String toCertId, String edgeType);

    /**
     * 更新证书顶点属性
     * 
     * @param certId 证书ID
     * @param properties 属性映射
     * @return 是否成功
     */
    boolean updateCertificateProperties(String certId, Map<String, Object> properties);

    /**
     * 删除证书顶点及其所有关联边
     * 
     * @param certId 证书ID
     * @return 是否成功
     */
    boolean deleteCertificateVertex(String certId);
}

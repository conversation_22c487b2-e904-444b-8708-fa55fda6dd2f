package com.geeksec.cert.config;

import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;

/**
 * NebulaGraph 配置类
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@Configuration
public class NebulaGraphConfig {

    @Value("${nebula.hosts}")
    private String hosts;

    @Value("${nebula.username}")
    private String username;

    @Value("${nebula.password}")
    private String password;

    @Value("${nebula.space}")
    private String space;

    @Value("${nebula.pool-config.min-conns-size:0}")
    private int minConnsSize;

    @Value("${nebula.pool-config.max-conns-size:10}")
    private int maxConnsSize;

    @Value("${nebula.pool-config.timeout:0}")
    private int timeout;

    @Value("${nebula.pool-config.idle-time:0}")
    private int idleTime;

    @Value("${nebula.pool-config.interval-idle:-1}")
    private int intervalIdle;

    @Value("${nebula.pool-config.wait-time:0}")
    private int waitTime;

    @Value("${nebula.pool-config.min-cluster-health-rate:1.0}")
    private double minClusterHealthRate;

    @Value("${nebula.pool-config.enable-ssl:false}")
    private boolean enableSsl;

    /**
     * NebulaGraph 连接池配置
     */
    @Bean
    public NebulaPoolConfig nebulaPoolConfig() {
        log.info("配置 NebulaGraph 连接池");
        
        NebulaPoolConfig poolConfig = new NebulaPoolConfig();
        poolConfig.setMinConnSize(minConnsSize);
        poolConfig.setMaxConnSize(maxConnsSize);
        poolConfig.setTimeout(timeout);
        poolConfig.setIdleTime(idleTime);
        poolConfig.setIntervalIdle(intervalIdle);
        poolConfig.setWaitTime(waitTime);
        poolConfig.setMinClusterHealthRate(minClusterHealthRate);
        poolConfig.setEnableSsl(enableSsl);
        
        return poolConfig;
    }

    /**
     * NebulaGraph 连接池
     */
    @Bean
    public NebulaPool nebulaPool(NebulaPoolConfig poolConfig) throws UnknownHostException {
        log.info("初始化 NebulaGraph 连接池, hosts: {}", hosts);
        
        NebulaPool pool = new NebulaPool();
        
        // 解析主机地址
        List<NebulaPool.NebulaAddress> addresses = parseHosts(hosts);
        
        // 初始化连接池
        boolean initResult = pool.init(addresses, poolConfig);
        if (!initResult) {
            throw new RuntimeException("NebulaGraph 连接池初始化失败");
        }
        
        log.info("NebulaGraph 连接池初始化成功");
        return pool;
    }

    /**
     * NebulaGraph 会话工厂
     */
    @Bean
    public NebulaSessionFactory nebulaSessionFactory(NebulaPool nebulaPool) {
        log.info("配置 NebulaGraph 会话工厂");
        return new NebulaSessionFactory(nebulaPool, username, password, space);
    }

    /**
     * 解析主机地址
     */
    private List<NebulaPool.NebulaAddress> parseHosts(String hosts) {
        return Arrays.stream(hosts.split(","))
                .map(String::trim)
                .map(host -> {
                    String[] parts = host.split(":");
                    String hostname = parts[0];
                    int port = parts.length > 1 ? Integer.parseInt(parts[1]) : 9669;
                    return new NebulaPool.NebulaAddress(hostname, port);
                })
                .toList();
    }

    /**
     * NebulaGraph 会话工厂
     */
    public static class NebulaSessionFactory {
        private final NebulaPool nebulaPool;
        private final String username;
        private final String password;
        private final String space;

        public NebulaSessionFactory(NebulaPool nebulaPool, String username, String password, String space) {
            this.nebulaPool = nebulaPool;
            this.username = username;
            this.password = password;
            this.space = space;
        }

        /**
         * 获取会话
         */
        public Session getSession() {
            try {
                Session session = nebulaPool.getSession(username, password, false);
                
                // 切换到指定空间
                if (space != null && !space.isEmpty()) {
                    session.execute("USE " + space);
                }
                
                return session;
            } catch (Exception e) {
                log.error("获取 NebulaGraph 会话失败", e);
                throw new RuntimeException("获取 NebulaGraph 会话失败", e);
            }
        }

        /**
         * 释放会话
         */
        public void releaseSession(Session session) {
            if (session != null) {
                try {
                    session.release();
                } catch (Exception e) {
                    log.error("释放 NebulaGraph 会话失败", e);
                }
            }
        }

        /**
         * 关闭连接池
         */
        public void close() {
            try {
                nebulaPool.close();
                log.info("NebulaGraph 连接池已关闭");
            } catch (Exception e) {
                log.error("关闭 NebulaGraph 连接池失败", e);
            }
        }
    }
}

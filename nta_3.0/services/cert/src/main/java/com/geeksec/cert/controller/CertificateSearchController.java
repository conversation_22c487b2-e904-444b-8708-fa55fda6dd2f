package com.geeksec.cert.controller;

import com.geeksec.cert.dto.*;
import com.geeksec.cert.service.CertificateService;
import com.geeksec.common.service.UserDisplayPreferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Map;

/**
 * 证书搜索控制器
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/search")
@RequiredArgsConstructor
@Validated
@Tag(name = "证书搜索", description = "证书搜索相关接口")
public class CertificateSearchController {

    private final CertificateService certificateService;
    private final UserDisplayPreferenceService displayPreferenceService;

    /**
     * 证书列表检索
     */
    @PostMapping("/list")
    @Operation(summary = "证书列表检索", description = "根据条件搜索证书列表")
    public ResponseEntity<Map<String, Object>> searchCertificateList(
            @Valid @RequestBody CertificateSearchCondition condition) {
        log.info("证书列表检索请求, condition: {}", condition);
        
        try {
            condition.setExport(false);
            Map<String, Object> result = certificateService.searchCertificateList(condition);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("证书列表检索失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取证书详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取证书详情", description = "根据证书SHA1和来源获取详细信息")
    public ResponseEntity<CertificateDetailResponse> getCertificateDetail(
            @Parameter(description = "证书SHA1", required = true)
            @RequestParam("cert_sha1") @NotBlank String certSha1,
            @Parameter(description = "证书来源", required = true)
            @RequestParam("cert_source") @NotBlank String certSource) {
        log.info("获取证书详情请求, certSha1: {}, certSource: {}", certSha1, certSource);
        
        try {
            CertificateDetailResponse response = certificateService.getCertificateDetail(certSha1, certSource);
            if (response != null) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("获取证书详情失败, certSha1: {}, error: ", certSha1, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 证书按类型聚合检索
     */
    @PostMapping("/type/aggregation")
    @Operation(summary = "证书按类型聚合", description = "根据证书类型进行聚合统计")
    public ResponseEntity<Map<String, Object>> aggregateCertificatesByType(
            @Valid @RequestBody CertificateSearchCondition condition) {
        log.info("证书按类型聚合请求, condition: {}", condition);
        
        try {
            Map<String, Object> result = certificateService.aggregateCertificatesByType(condition);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("证书按类型聚合失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 证书按标签聚合检索
     */
    @PostMapping("/labels/aggregation")
    @Operation(summary = "证书按标签聚合", description = "根据证书标签进行聚合统计")
    public ResponseEntity<Map<String, Object>> aggregateCertificatesByLabels(
            @Valid @RequestBody CertificateSearchCondition condition) {
        log.info("证书按标签聚合请求, condition: {}", condition);
        
        try {
            Map<String, Object> result = certificateService.aggregateCertificatesByLabels(condition);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("证书按标签聚合失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 修改证书备注
     */
    @PutMapping("/remark")
    @Operation(summary = "修改证书备注", description = "更新证书的备注信息")
    public ResponseEntity<Map<String, Object>> updateCertificateRemarks(
            @Valid @RequestBody CertificateUpdateRequest request) {
        log.info("修改证书备注请求, request: {}", request);
        
        try {
            boolean success = certificateService.updateCertificateRemarks(request);
            Map<String, Object> result = Map.of(
                "success", success,
                "message", success ? "备注更新成功" : "备注更新失败"
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("修改证书备注失败, error: ", e);
            Map<String, Object> result = Map.of(
                "success", false,
                "message", "备注更新失败: " + e.getMessage()
            );
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 修改证书标签
     */
    @PutMapping("/labels")
    @Operation(summary = "修改证书标签", description = "更新证书的标签信息")
    public ResponseEntity<Map<String, Object>> modifyCertificateLabels(
            @Valid @RequestBody CertificateUpdateRequest request) {
        log.info("修改证书标签请求, request: {}", request);
        
        try {
            boolean success = certificateService.modifyCertificateLabels(request);
            Map<String, Object> result = Map.of(
                "success", success,
                "message", success ? "标签更新成功" : "标签更新失败"
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("修改证书标签失败, error: ", e);
            Map<String, Object> result = Map.of(
                "success", false,
                "message", "标签更新失败: " + e.getMessage()
            );
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 证书下载
     */
    @PostMapping("/download")
    @Operation(summary = "证书下载", description = "下载指定的证书文件")
    public ResponseEntity<byte[]> downloadCertificate(
            @RequestBody Map<String, String> request) {
        String certId = request.get("cert_id");
        log.info("证书下载请求, certId: {}", certId);
        
        try {
            byte[] certData = certificateService.downloadCertificate(certId);
            if (certData != null && certData.length > 0) {
                return ResponseEntity.ok()
                        .header("Content-Type", "application/octet-stream")
                        .header("Content-Disposition", "attachment; filename=\"" + certId + ".crt\"")
                        .body(certData);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            log.error("证书下载失败, certId: {}, error: ", certId, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取证书显示配置
     */
    @GetMapping("/display/config")
    @Operation(summary = "获取证书显示配置", description = "获取用户的证书列表显示配置")
    public ResponseEntity<Map<String, Object>> getCertificateDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") Integer userId,
            @Parameter(description = "配置类型", required = false)
            @RequestParam(value = "config_type", defaultValue = "table_view") String configType) {
        log.info("获取证书显示配置请求, userId: {}, configType: {}", userId, configType);

        try {
            Map<String, Object> result = displayPreferenceService.getUserDisplayConfig(
                userId, "certificate", configType);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取证书显示配置失败, userId: {}, configType: {}, error: ", userId, configType, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 保存证书显示配置
     */
    @PostMapping("/display/config")
    @Operation(summary = "保存证书显示配置", description = "保存用户的证书列表显示配置")
    public ResponseEntity<Map<String, Object>> saveCertificateDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") Integer userId,
            @Parameter(description = "配置类型", required = false)
            @RequestParam(value = "config_type", defaultValue = "table_view") String configType,
            @Parameter(description = "配置JSON", required = true)
            @RequestBody String configJson) {
        log.info("保存证书显示配置请求, userId: {}, configType: {}", userId, configType);

        Map<String, Object> result = new HashMap<>(4);

        try {
            boolean success = displayPreferenceService.saveUserDisplayConfig(
                userId, "certificate", configType, configJson, userId);

            result.put("success", success);
            if (success) {
                result.put("message", "配置保存成功");
            } else {
                result.put("message", "配置保存失败");
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("保存证书显示配置失败, userId: {}, configType: {}, error: ", userId, configType, e);
            result.put("success", false);
            result.put("message", "配置保存失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 更新证书显示配置
     */
    @PutMapping("/display/config")
    @Operation(summary = "更新证书显示配置", description = "更新用户的证书列表显示配置")
    public ResponseEntity<Map<String, Object>> updateCertificateDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") Integer userId,
            @Parameter(description = "配置类型", required = false)
            @RequestParam(value = "config_type", defaultValue = "table_view") String configType,
            @Parameter(description = "配置JSON", required = true)
            @RequestBody String configJson) {
        log.info("更新证书显示配置请求, userId: {}, configType: {}", userId, configType);

        Map<String, Object> result = new HashMap<>(4);

        try {
            boolean success = displayPreferenceService.updateUserDisplayConfig(
                userId, "certificate", configType, configJson, userId);

            result.put("success", success);
            if (success) {
                result.put("message", "配置更新成功");
            } else {
                result.put("message", "配置更新失败");
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新证书显示配置失败, userId: {}, configType: {}, error: ", userId, configType, e);
            result.put("success", false);
            result.put("message", "配置更新失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 删除证书显示配置
     */
    @DeleteMapping("/display/config")
    @Operation(summary = "删除证书显示配置", description = "删除用户的证书列表显示配置")
    public ResponseEntity<Map<String, Object>> deleteCertificateDisplayConfig(
            @Parameter(description = "用户ID", required = true)
            @RequestParam("user_id") Integer userId,
            @Parameter(description = "配置类型", required = false)
            @RequestParam(value = "config_type", defaultValue = "table_view") String configType) {
        log.info("删除证书显示配置请求, userId: {}, configType: {}", userId, configType);

        Map<String, Object> result = new HashMap<>(4);

        try {
            boolean success = displayPreferenceService.deleteUserDisplayConfig(
                userId, "certificate", configType);

            result.put("success", success);
            if (success) {
                result.put("message", "配置删除成功");
            } else {
                result.put("message", "配置删除失败");
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除证书显示配置失败, userId: {}, configType: {}, error: ", userId, configType, e);
            result.put("success", false);
            result.put("message", "配置删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 模糊检索
     */
    @PostMapping("/fuzzy")
    @Operation(summary = "模糊检索", description = "根据关键字进行模糊搜索")
    public ResponseEntity<Map<String, Object>> fuzzySearch(
            @RequestBody Map<String, Object> request) {
        String keyword = (String) request.get("keyword");
        Integer limit = (Integer) request.getOrDefault("limit", 20);
        log.info("模糊检索请求, keyword: {}, limit: {}", keyword, limit);
        
        try {
            var certificates = certificateService.searchCertificatesByKeyword(keyword, limit);
            Map<String, Object> result = Map.of(
                "data", certificates,
                "total", certificates.size()
            );
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("模糊检索失败, keyword: {}, error: ", keyword, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}

package com.geeksec.cert.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 证书修改请求
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Schema(description = "证书修改请求")
public class CertificateUpdateRequest {

    /**
     * 证书ID (SHA1哈希值)
     */
    @Schema(description = "证书ID (SHA1哈希值)", required = true)
    @JsonProperty("cert_sha1")
    @NotBlank(message = "证书ID不能为空")
    private String certSha1;

    /**
     * 备注列表
     */
    @Schema(description = "备注列表")
    @JsonProperty("remarks")
    @Size(max = 10, message = "备注数量不能超过10个")
    private List<String> remarks;

    /**
     * 标签ID列表
     */
    @Schema(description = "标签ID列表")
    @JsonProperty("labels")
    private List<Integer> labels;
}

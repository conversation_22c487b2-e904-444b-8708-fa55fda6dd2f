package com.geeksec.cert.entity.graph;

import lombok.Data;
import org.nebula.contrib.ngbatis.annotations.Space;
import org.nebula.contrib.ngbatis.annotations.Tag;

import java.time.LocalDateTime;

/**
 * 证书图数据库顶点实体
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@Space("nta_graph")
@Tag("CERT")
public class CertificateVertex {

    /**
     * 证书ID (SHA1哈希值)
     */
    private String certId;

    /**
     * 首次出现时间
     */
    private LocalDateTime firstSeen;

    /**
     * 末次出现时间
     */
    private LocalDateTime lastSeen;

    /**
     * 威胁等级 (0-100)
     */
    private Integer threatLevel;

    /**
     * 信任等级 (0-100)
     */
    private Integer trustLevel;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 证书类型
     */
    private String certType;

    /**
     * 签发机构
     */
    private String issuer;

    /**
     * 所有者
     */
    private String subject;

    /**
     * 有效期开始时间
     */
    private LocalDateTime notBefore;

    /**
     * 有效期结束时间
     */
    private LocalDateTime notAfter;

    /**
     * 证书序列号
     */
    private String serialNumber;

    /**
     * 证书指纹 (MD5)
     */
    private String fingerprintMd5;

    /**
     * 证书指纹 (SHA256)
     */
    private String fingerprintSha256;

    /**
     * 公钥算法
     */
    private String publicKeyAlgorithm;

    /**
     * 签名算法
     */
    private String signatureAlgorithm;

    /**
     * 密钥长度
     */
    private Integer keyLength;

    /**
     * 是否自签名
     */
    private Boolean selfSigned;

    /**
     * 是否CA证书
     */
    private Boolean isCa;

    /**
     * 证书版本
     */
    private Integer version;

    /**
     * 扩展信息 (JSON格式)
     */
    private String extensions;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}

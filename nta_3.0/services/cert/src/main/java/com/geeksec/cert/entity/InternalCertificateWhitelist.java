package com.geeksec.cert.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 内部证书白名单实体类
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table("internal_certificate_whitelist")
public class InternalCertificateWhitelist {

    /**
     * 证书SHA1
     */
    @Id
    @Column("cert_sha1")
    private String certSha1;

    /**
     * 任务ID
     */
    @Column("task_id")
    private Integer taskId;

    /**
     * 关联IP
     */
    @Column("link_ip")
    private String linkIp;

    /**
     * 备注
     */
    @Column("remark")
    private String remark;
}

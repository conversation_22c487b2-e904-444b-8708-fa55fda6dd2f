package com.geeksec.cert.controller;

import com.geeksec.cert.dto.CertificateDto;
import com.geeksec.cert.service.CertificateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * 证书统计控制器
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
@RestController
@RequestMapping("/statistics")
@RequiredArgsConstructor
@Validated
@Tag(name = "证书统计", description = "证书统计分析相关接口")
public class CertificateStatisticsController {

    private final CertificateService certificateService;

    /**
     * 获取证书统计概览
     */
    @GetMapping("/overview")
    @Operation(summary = "证书统计概览", description = "获取证书的总体统计信息")
    public ResponseEntity<Map<String, Object>> getCertificateStatistics() {
        log.info("获取证书统计概览请求");
        
        try {
            Map<String, Object> statistics = certificateService.getCertificateStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("获取证书统计概览失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据威胁等级查询证书
     */
    @GetMapping("/threat-level")
    @Operation(summary = "按威胁等级查询证书", description = "根据威胁等级范围查询证书列表")
    public ResponseEntity<Map<String, Object>> getCertificatesByThreatLevel(
            @Parameter(description = "最小威胁等级 (0-100)")
            @RequestParam(value = "min_threat_level", defaultValue = "0") 
            @Min(0) @Max(100) Integer minThreatLevel,
            @Parameter(description = "最大威胁等级 (0-100)")
            @RequestParam(value = "max_threat_level", defaultValue = "100") 
            @Min(0) @Max(100) Integer maxThreatLevel,
            @Parameter(description = "限制数量")
            @RequestParam(value = "limit", defaultValue = "20") 
            @Min(1) @Max(1000) Integer limit) {
        log.info("按威胁等级查询证书请求, minThreatLevel: {}, maxThreatLevel: {}, limit: {}", 
                minThreatLevel, maxThreatLevel, limit);
        
        try {
            List<CertificateDto> certificates = certificateService.getCertificatesByThreatLevel(
                    minThreatLevel, maxThreatLevel, limit);
            
            Map<String, Object> result = Map.of(
                "data", certificates,
                "total", certificates.size(),
                "minThreatLevel", minThreatLevel,
                "maxThreatLevel", maxThreatLevel
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("按威胁等级查询证书失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据信任等级查询证书
     */
    @GetMapping("/trust-level")
    @Operation(summary = "按信任等级查询证书", description = "根据信任等级范围查询证书列表")
    public ResponseEntity<Map<String, Object>> getCertificatesByTrustLevel(
            @Parameter(description = "最小信任等级 (0-100)")
            @RequestParam(value = "min_trust_level", defaultValue = "0") 
            @Min(0) @Max(100) Integer minTrustLevel,
            @Parameter(description = "最大信任等级 (0-100)")
            @RequestParam(value = "max_trust_level", defaultValue = "100") 
            @Min(0) @Max(100) Integer maxTrustLevel,
            @Parameter(description = "限制数量")
            @RequestParam(value = "limit", defaultValue = "20") 
            @Min(1) @Max(1000) Integer limit) {
        log.info("按信任等级查询证书请求, minTrustLevel: {}, maxTrustLevel: {}, limit: {}", 
                minTrustLevel, maxTrustLevel, limit);
        
        try {
            List<CertificateDto> certificates = certificateService.getCertificatesByTrustLevel(
                    minTrustLevel, maxTrustLevel, limit);
            
            Map<String, Object> result = Map.of(
                "data", certificates,
                "total", certificates.size(),
                "minTrustLevel", minTrustLevel,
                "maxTrustLevel", maxTrustLevel
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("按信任等级查询证书失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取最近活跃的证书
     */
    @GetMapping("/recent-active")
    @Operation(summary = "最近活跃证书", description = "获取最近活跃的证书列表")
    public ResponseEntity<Map<String, Object>> getRecentActiveCertificates(
            @Parameter(description = "天数")
            @RequestParam(value = "days", defaultValue = "7") 
            @Min(1) @Max(365) Integer days,
            @Parameter(description = "限制数量")
            @RequestParam(value = "limit", defaultValue = "20") 
            @Min(1) @Max(1000) Integer limit) {
        log.info("获取最近活跃证书请求, days: {}, limit: {}", days, limit);
        
        try {
            List<CertificateDto> certificates = certificateService.getRecentActiveCertificates(days, limit);
            
            Map<String, Object> result = Map.of(
                "data", certificates,
                "total", certificates.size(),
                "days", days
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取最近活跃证书失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据证书ID列表查询证书
     */
    @PostMapping("/batch-query")
    @Operation(summary = "批量查询证书", description = "根据证书ID列表批量查询证书信息")
    public ResponseEntity<Map<String, Object>> getCertificatesByCertIds(
            @RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<String> certIds = (List<String>) request.get("cert_ids");
        log.info("批量查询证书请求, certIds: {}", certIds);
        
        if (certIds == null || certIds.isEmpty()) {
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "证书ID列表不能为空"
            );
            return ResponseEntity.badRequest().body(errorResult);
        }
        
        if (certIds.size() > 1000) {
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "单次查询证书数量不能超过1000个"
            );
            return ResponseEntity.badRequest().body(errorResult);
        }
        
        try {
            List<CertificateDto> certificates = certificateService.getCertificatesByCertIds(certIds);
            
            Map<String, Object> result = Map.of(
                "success", true,
                "data", certificates,
                "total", certificates.size(),
                "requestCount", certIds.size()
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量查询证书失败, error: ", e);
            Map<String, Object> errorResult = Map.of(
                "success", false,
                "message", "查询失败: " + e.getMessage()
            );
            return ResponseEntity.ok(errorResult);
        }
    }

    /**
     * 证书威胁等级分布统计
     */
    @GetMapping("/threat-distribution")
    @Operation(summary = "威胁等级分布", description = "获取证书威胁等级的分布统计")
    public ResponseEntity<Map<String, Object>> getThreatLevelDistribution() {
        log.info("获取威胁等级分布统计请求");
        
        try {
            // TODO: 实现威胁等级分布统计
            Map<String, Object> distribution = Map.of(
                "high", 0,      // 高威胁 (80-100)
                "medium", 0,    // 中威胁 (40-79)
                "low", 0,       // 低威胁 (1-39)
                "none", 0       // 无威胁 (0)
            );
            
            Map<String, Object> result = Map.of(
                "success", true,
                "data", distribution
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取威胁等级分布统计失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 证书信任等级分布统计
     */
    @GetMapping("/trust-distribution")
    @Operation(summary = "信任等级分布", description = "获取证书信任等级的分布统计")
    public ResponseEntity<Map<String, Object>> getTrustLevelDistribution() {
        log.info("获取信任等级分布统计请求");
        
        try {
            // TODO: 实现信任等级分布统计
            Map<String, Object> distribution = Map.of(
                "high", 0,      // 高信任 (80-100)
                "medium", 0,    // 中信任 (40-79)
                "low", 0,       // 低信任 (1-39)
                "none", 0       // 无信任 (0)
            );
            
            Map<String, Object> result = Map.of(
                "success", true,
                "data", distribution
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取信任等级分布统计失败, error: ", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 证书时间分布统计
     */
    @GetMapping("/time-distribution")
    @Operation(summary = "时间分布统计", description = "获取证书按时间的分布统计")
    public ResponseEntity<Map<String, Object>> getTimeDistribution(
            @Parameter(description = "统计类型 (day/week/month)")
            @RequestParam(value = "type", defaultValue = "day") String type) {
        log.info("获取时间分布统计请求, type: {}", type);
        
        try {
            // TODO: 实现时间分布统计
            Map<String, Object> distribution = Map.of(
                "labels", List.of(),
                "data", List.of()
            );
            
            Map<String, Object> result = Map.of(
                "success", true,
                "data", distribution,
                "type", type
            );
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取时间分布统计失败, type: {}, error: ", type, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}

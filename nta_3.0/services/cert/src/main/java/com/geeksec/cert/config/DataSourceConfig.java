package com.geeksec.cert.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * 数据源配置类 - 支持双数据源
 *
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Configuration
public class DataSourceConfig {

    /**
     * 主数据源 - PostgreSQL (业务数据)
     * 用于存储证书白名单、标签、模板等业务数据
     */
    @Primary
    @Bean("primaryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource primaryDataSource() {
        return new DruidDataSource();
    }

    /**
     * Doris 数据源 - 证书元数据
     * 用于查询 dim_cert 表中的证书详细信息
     */
    @Bean("dorisDataSource")
    @ConfigurationProperties(prefix = "doris.datasource")
    public DataSource dorisDataSource() {
        return new DruidDataSource();
    }
}

package com.geeksec.cert.repository;

import com.geeksec.cert.entity.InternalCertificateWhitelist;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 内部证书白名单数据访问接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Mapper
public interface InternalCertificateWhitelistRepository extends BaseMapper<InternalCertificateWhitelist> {

    /**
     * 根据任务ID查询白名单证书
     *
     * @param taskId 任务ID
     * @return 白名单证书列表
     */
    @Select("SELECT cert_sha1, task_id, link_ip, remark FROM internal_certificate_whitelist WHERE task_id = #{taskId} ORDER BY cert_sha1")
    List<InternalCertificateWhitelist> selectByTaskId(@Param("taskId") Integer taskId);

    /**
     * 根据证书SHA1列表查询白名单信息
     *
     * @param certSha1List 证书SHA1列表
     * @return 白名单证书列表
     */
    @Select("""
            <script>
            SELECT cert_sha1, task_id, link_ip, remark
            FROM internal_certificate_whitelist
            WHERE cert_sha1 IN
            <foreach collection="certSha1List" item="certSha1" open="(" separator="," close=")">
                #{certSha1}
            </foreach>
            </script>
            """)
    List<InternalCertificateWhitelist> selectByCertSha1List(@Param("certSha1List") List<String> certSha1List);

    /**
     * 根据关联IP查询白名单证书
     *
     * @param linkIp 关联IP
     * @return 白名单证书列表
     */
    @Select("SELECT cert_sha1, task_id, link_ip, remark FROM internal_certificate_whitelist WHERE link_ip LIKE CONCAT('%', #{linkIp}, '%') ORDER BY cert_sha1")
    List<InternalCertificateWhitelist> selectByLinkIp(@Param("linkIp") String linkIp);

    /**
     * 检查证书是否在白名单中
     *
     * @param certSha1 证书SHA1
     * @return 是否在白名单中
     */
    @Select("SELECT COUNT(*) > 0 FROM internal_certificate_whitelist WHERE cert_sha1 = #{certSha1}")
    boolean existsByCertSha1(@Param("certSha1") String certSha1);

    /**
     * 批量插入白名单证书
     *
     * @param whitelistItems 白名单项目列表
     * @return 插入行数
     */
    default int batchInsert(List<InternalCertificateWhitelist> whitelistItems) {
        return insertBatch(whitelistItems);
    }

    /**
     * 根据任务ID删除白名单证书
     *
     * @param taskId 任务ID
     * @return 删除行数
     */
    @Delete("DELETE FROM internal_certificate_whitelist WHERE task_id = #{taskId}")
    int deleteByTaskId(@Param("taskId") Integer taskId);

    /**
     * 更新白名单证书的关联IP
     *
     * @param certSha1 证书SHA1
     * @param linkIp 关联IP
     * @return 更新行数
     */
    @Update("UPDATE internal_certificate_whitelist SET link_ip = #{linkIp} WHERE cert_sha1 = #{certSha1}")
    int updateLinkIp(@Param("certSha1") String certSha1, @Param("linkIp") String linkIp);

    /**
     * 更新白名单证书的备注
     *
     * @param certSha1 证书SHA1
     * @param remark 备注
     * @return 更新行数
     */
    @Update("UPDATE internal_certificate_whitelist SET remark = #{remark} WHERE cert_sha1 = #{certSha1}")
    int updateRemark(@Param("certSha1") String certSha1, @Param("remark") String remark);

    /**
     * 统计任务的白名单证书数量
     *
     * @param taskId 任务ID
     * @return 证书数量
     */
    @Select("SELECT COUNT(*) FROM internal_certificate_whitelist WHERE task_id = #{taskId}")
    long countByTaskId(@Param("taskId") Integer taskId);
}

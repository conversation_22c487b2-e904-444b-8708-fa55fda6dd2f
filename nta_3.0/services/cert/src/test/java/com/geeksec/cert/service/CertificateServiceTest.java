package com.geeksec.cert.service;

import com.geeksec.cert.dto.*;
import com.geeksec.cert.entity.Certificate;
import com.geeksec.cert.repository.CertificateRepository;
import com.geeksec.cert.service.impl.CertificateServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 证书服务测试类
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@ExtendWith(MockitoExtension.class)
class CertificateServiceTest {

    @Mock
    private CertificateRepository certificateRepository;

    @Mock
    private CertificateLabelService certificateLabelService;

    @InjectMocks
    private CertificateServiceImpl certificateService;

    private Certificate testCertificate;
    private String testCertId;

    @BeforeEach
    void setUp() {
        testCertId = "test-cert-sha1-hash";
        testCertificate = new Certificate();
        testCertificate.setCertId(testCertId);
        testCertificate.setFirstSeen(LocalDateTime.now().minusDays(30));
        testCertificate.setLastSeen(LocalDateTime.now());
        testCertificate.setThreatLevel(50);
        testCertificate.setTrustLevel(70);
        testCertificate.setRemark("测试证书");
        testCertificate.setCreatedAt(LocalDateTime.now().minusDays(30));
        testCertificate.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    void testGetCertificateDetail() {
        // Given
        String certSource = "test-source";
        when(certificateRepository.selectById(testCertId)).thenReturn(testCertificate);
        when(certificateLabelService.getCertificateLabels(testCertId)).thenReturn(Arrays.asList());

        // When
        CertificateDetailResponse response = certificateService.getCertificateDetail(testCertId, certSource);

        // Then
        assertNotNull(response);
        assertNotNull(response.getCertInfo());
        assertEquals(testCertId, response.getCertInfo().getCertId());
        assertEquals(testCertificate.getThreatLevel(), response.getCertInfo().getThreatLevel());
        assertEquals(testCertificate.getTrustLevel(), response.getCertInfo().getTrustLevel());
        
        verify(certificateRepository).selectById(testCertId);
        verify(certificateLabelService).getCertificateLabels(testCertId);
    }

    @Test
    void testGetCertificateDetailNotFound() {
        // Given
        when(certificateRepository.selectById(testCertId)).thenReturn(null);

        // When
        CertificateDetailResponse response = certificateService.getCertificateDetail(testCertId, "test-source");

        // Then
        assertNull(response);
        verify(certificateRepository).selectById(testCertId);
        verify(certificateLabelService, never()).getCertificateLabels(any());
    }

    @Test
    void testSearchCertificateList() {
        // Given
        CertificateSearchCondition condition = new CertificateSearchCondition();
        condition.setPage(1);
        condition.setSize(20);
        
        List<Certificate> certificates = Arrays.asList(testCertificate);
        when(certificateRepository.selectAll()).thenReturn(certificates);

        // When
        Map<String, Object> result = certificateService.searchCertificateList(condition);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("data"));
        assertTrue(result.containsKey("total"));
        assertEquals(1, result.get("total"));
        
        @SuppressWarnings("unchecked")
        List<CertificateDto> data = (List<CertificateDto>) result.get("data");
        assertEquals(1, data.size());
        assertEquals(testCertId, data.get(0).getCertId());
    }

    @Test
    void testUpdateCertificateRemarks() {
        // Given
        CertificateUpdateRequest request = new CertificateUpdateRequest();
        request.setCertSha1(testCertId);
        request.setRemarks(Arrays.asList("新备注1", "新备注2"));
        
        when(certificateRepository.updateRemark(eq(testCertId), anyString())).thenReturn(1);

        // When
        boolean result = certificateService.updateCertificateRemarks(request);

        // Then
        assertTrue(result);
        verify(certificateRepository).updateRemark(eq(testCertId), eq("新备注1;新备注2"));
    }

    @Test
    void testModifyCertificateLabels() {
        // Given
        CertificateUpdateRequest request = new CertificateUpdateRequest();
        request.setCertSha1(testCertId);
        request.setLabels(Arrays.asList(1, 2, 3));
        
        when(certificateLabelService.addLabelsToCertificate(testCertId, request.getLabels())).thenReturn(true);

        // When
        boolean result = certificateService.modifyCertificateLabels(request);

        // Then
        assertTrue(result);
        verify(certificateLabelService).addLabelsToCertificate(testCertId, request.getLabels());
    }

    @Test
    void testGetCertificatesByCertIds() {
        // Given
        List<String> certIds = Arrays.asList(testCertId, "another-cert-id");
        when(certificateRepository.selectByCertIds(certIds)).thenReturn(Arrays.asList(testCertificate));

        // When
        List<CertificateDto> result = certificateService.getCertificatesByCertIds(certIds);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testCertId, result.get(0).getCertId());
        verify(certificateRepository).selectByCertIds(certIds);
    }

    @Test
    void testGetCertificatesByThreatLevel() {
        // Given
        Integer minThreatLevel = 40;
        Integer maxThreatLevel = 80;
        Integer limit = 10;
        
        when(certificateRepository.selectByThreatLevelRange(minThreatLevel, maxThreatLevel, limit))
                .thenReturn(Arrays.asList(testCertificate));

        // When
        List<CertificateDto> result = certificateService.getCertificatesByThreatLevel(minThreatLevel, maxThreatLevel, limit);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testCertId, result.get(0).getCertId());
        verify(certificateRepository).selectByThreatLevelRange(minThreatLevel, maxThreatLevel, limit);
    }

    @Test
    void testGetCertificatesByTrustLevel() {
        // Given
        Integer minTrustLevel = 60;
        Integer maxTrustLevel = 90;
        Integer limit = 10;
        
        when(certificateRepository.selectByTrustLevelRange(minTrustLevel, maxTrustLevel, limit))
                .thenReturn(Arrays.asList(testCertificate));

        // When
        List<CertificateDto> result = certificateService.getCertificatesByTrustLevel(minTrustLevel, maxTrustLevel, limit);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testCertId, result.get(0).getCertId());
        verify(certificateRepository).selectByTrustLevelRange(minTrustLevel, maxTrustLevel, limit);
    }

    @Test
    void testGetCertificateStatistics() {
        // Given
        when(certificateRepository.countTotal()).thenReturn(100L);

        // When
        Map<String, Object> result = certificateService.getCertificateStatistics();

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("totalCount"));
        assertEquals(100L, result.get("totalCount"));
        verify(certificateRepository).countTotal();
    }

    @Test
    void testGetRecentActiveCertificates() {
        // Given
        Integer days = 7;
        Integer limit = 20;
        
        when(certificateRepository.selectRecentActive(days, limit))
                .thenReturn(Arrays.asList(testCertificate));

        // When
        List<CertificateDto> result = certificateService.getRecentActiveCertificates(days, limit);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testCertId, result.get(0).getCertId());
        verify(certificateRepository).selectRecentActive(days, limit);
    }

    @Test
    void testSearchCertificatesByKeyword() {
        // Given
        String keyword = "测试";
        Integer limit = 10;
        
        when(certificateRepository.searchByRemarkKeyword(keyword, limit))
                .thenReturn(Arrays.asList(testCertificate));

        // When
        List<CertificateDto> result = certificateService.searchCertificatesByKeyword(keyword, limit);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testCertId, result.get(0).getCertId());
        verify(certificateRepository).searchByRemarkKeyword(keyword, limit);
    }

    @Test
    void testBatchUpdateCertificates() {
        // Given
        List<Certificate> certificates = Arrays.asList(testCertificate);
        when(certificateRepository.batchInsertOrUpdate(certificates)).thenReturn(1);

        // When
        boolean result = certificateService.batchUpdateCertificates(certificates);

        // Then
        assertTrue(result);
        verify(certificateRepository).batchInsertOrUpdate(certificates);
    }
}

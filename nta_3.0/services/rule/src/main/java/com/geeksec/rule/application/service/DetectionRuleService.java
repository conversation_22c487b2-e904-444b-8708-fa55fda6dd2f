package com.geeksec.rule.application.service;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.rule.interfaces.dto.DetectionRuleCreateDTO;
import com.geeksec.rule.interfaces.vo.DetectionRuleVO;

import java.util.List;

/**
 * 检测规则服务接口
 *
 * <AUTHOR>
 */
public interface DetectionRuleService {

    /**
     * 创建检测规则
     *
     * @param createDTO 创建请求
     * @return 创建结果
     */
    ApiResponse<Void> createDetectionRule(DetectionRuleCreateDTO createDTO);

    /**
     * 更新检测规则
     *
     * @param id 规则ID
     * @param updateDTO 更新请求
     * @return 更新结果
     */
    ApiResponse<Void> updateDetectionRule(Long id, DetectionRuleCreateDTO updateDTO);

    /**
     * 删除检测规则
     *
     * @param ids 规则ID列表
     * @return 删除结果
     */
    ApiResponse<Void> deleteDetectionRules(List<Long> ids);

    /**
     * 根据任务ID删除所有检测规则
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    ApiResponse<Void> deleteDetectionRulesByTaskId(Integer taskId);

    /**
     * 获取检测规则详情
     *
     * @param id 规则ID
     * @return 规则详情
     */
    ApiResponse<DetectionRuleVO> getDetectionRule(Long id);

    /**
     * 分页查询检测规则
     *
     * @param taskId 任务ID
     * @param ruleId 规则ID（可选）
     * @param ruleName 规则名称（可选）
     * @param ruleState 规则状态（可选）
     * @param attackStage 攻击阶段（可选）
     * @param orderField 排序字段
     * @param sortOrder 排序方向
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    ApiResponse<PageResultVo<DetectionRuleVO>> getDetectionRules(
            Integer taskId,
            Integer ruleId,
            String ruleName,
            String ruleState,
            String attackStage,
            String orderField,
            String sortOrder,
            Integer currentPage,
            Integer pageSize
    );

    /**
     * 更新规则状态
     *
     * @param id 规则ID
     * @param state 新状态
     * @return 更新结果
     */
    ApiResponse<Void> updateRuleState(Long id, String state);

    /**
     * 批量导入检测规则（CSV）
     *
     * @param taskId 任务ID
     * @param csvContent CSV内容
     * @return 导入结果
     */
    ApiResponse<Object> importDetectionRulesFromCsv(Integer taskId, String csvContent);

    /**
     * 导出检测规则模板
     *
     * @return 模板内容
     */
    ApiResponse<String> exportDetectionRuleTemplate();

    /**
     * 根据规则ID列表获取标签信息（用于会话分析）
     *
     * @param ruleIds 规则ID列表
     * @return 标签信息列表
     */
    ApiResponse<List<Object>> getTagInfoByRuleIds(List<Integer> ruleIds);
}

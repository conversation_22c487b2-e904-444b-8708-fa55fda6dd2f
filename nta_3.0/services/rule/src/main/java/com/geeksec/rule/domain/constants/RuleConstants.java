package com.geeksec.rule.domain.constants;

/**
 * 规则管理相关常量
 * 
 * <AUTHOR>
 */
public final class RuleConstants {

    private RuleConstants() {
        // 工具类，禁止实例化
    }

    // 状态相关常量
    public static final Integer STATUS_ACTIVE = 1;
    public static final Integer STATUS_INACTIVE = 0;

    // 规则状态
    public static final String STATE_ACTIVE = "生效";
    public static final String STATE_INACTIVE = "失效";

    // 特征规则相关常量
    public static final Integer RULE_MAX_ID = 200000;
    public static final Integer DEFAULT_CAPTURE_MODE = 1;

    // 过滤规则相关常量
    public static final Integer FILTER_STATE_KEEP = 0;
    public static final Integer FILTER_STATE_DROP = 1;
    public static final Integer MAX_PORT_VALUE = 65535;

    // 文件相关常量
    public static final String FILTER_RULE_TEMPLATE_FILE = "filter_template.csv";
    public static final String FEATURE_RULE_TEMPLATE_FILE = "feature_template.csv";
    public static final String FEATURE_RULE_LIB_ZIP = "LibFolder.zip";

    // 规则类型标识
    public static final String RULE_TYPE_IP = "1";
    public static final String RULE_TYPE_PROTOCOL = "2";
    public static final String RULE_TYPE_KEY = "3";
    public static final String RULE_TYPE_REGEX = "4";
    public static final String RULE_TYPE_DOMAIN = "5";

    // 分隔符
    public static final String RULE_TYPE_SEPARATOR = ",";
    public static final String AGGS_SPLIT = "_&&&_";

    // 默认值
    public static final Integer DEFAULT_RE_NEW_APP = 1;
    public static final Integer DEFAULT_RESPOND = 2;
    public static final String DEFAULT_REVERSE = "ReverseInfor";
    public static final Integer DEFAULT_TYPE = 100;

    // 布尔值转换
    public static final Integer BOOLEAN_TRUE = 1;
    public static final Integer BOOLEAN_FALSE = 0;
}

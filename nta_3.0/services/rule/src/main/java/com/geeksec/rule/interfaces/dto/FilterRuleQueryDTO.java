package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 过滤规则查询条件DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "过滤规则查询条件")
public class FilterRuleQueryDTO {

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    @Schema(description = "任务ID", example = "1001")
    private Integer taskId;

    /**
     * 批次ID
     */
    @JsonProperty("batch_id")
    @Schema(description = "批次ID", example = "1")
    private Integer batchId;

    /**
     * 过滤状态：0-保留，1-丢弃
     */
    @JsonProperty("state")
    @Schema(description = "过滤状态", example = "0", allowableValues = {"0", "1"})
    private Integer state;

    /**
     * 排序字段
     */
    @JsonProperty("order_field")
    @Schema(description = "排序字段", example = "created_time", allowableValues = {"id", "created_time", "updated_time"})
    private String orderField;

    /**
     * 排序方向
     */
    @JsonProperty("sort_order")
    @Schema(description = "排序方向", example = "DESC", allowableValues = {"ASC", "DESC"})
    private String sortOrder = "DESC";

    /**
     * 当前页码
     */
    @JsonProperty("current_page")
    @Schema(description = "当前页码", example = "1")
    private Integer currentPage = 1;

    /**
     * 每页大小
     */
    @JsonProperty("page_size")
    @Schema(description = "每页大小", example = "20")
    private Integer pageSize = 20;
}

package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 特征字规则DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "特征字规则")
public class KeywordRuleDTO {

    /**
     * 协议ID
     */
    @NotNull(message = "协议ID不能为空")
    @JsonProperty("pro_id")
    @Schema(description = "协议ID", example = "6")
    private Integer proId;

    /**
     * 特征字符串
     */
    @NotBlank(message = "特征字符串不能为空")
    @JsonProperty("keyword")
    @Schema(description = "特征字符串", example = "malware")
    private String keyword;

    /**
     * 大小写敏感：0-区分 1-不区分
     */
    @JsonProperty("is_case_sensitive")
    @Schema(description = "大小写敏感", example = "0", allowableValues = {"0", "1"})
    private Integer isCaseSensitive;
}

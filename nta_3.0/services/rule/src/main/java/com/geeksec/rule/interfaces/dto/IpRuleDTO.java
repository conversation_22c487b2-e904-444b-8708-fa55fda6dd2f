package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * IP规则DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "IP规则")
public class IpRuleDTO {

    /**
     * IP地址
     */
    @NotBlank(message = "IP地址不能为空")
    @JsonProperty("ip")
    @Schema(description = "IP地址", example = "***********")
    private String ip;

    /**
     * IP类型：1-源IP 2-目标IP 3-任意IP
     */
    @NotNull(message = "IP类型不能为空")
    @JsonProperty("type")
    @Schema(description = "IP类型", example = "1", allowableValues = {"1", "2", "3"})
    private Integer type;
}

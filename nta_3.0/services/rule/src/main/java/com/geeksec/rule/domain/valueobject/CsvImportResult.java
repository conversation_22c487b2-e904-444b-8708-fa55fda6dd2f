package com.geeksec.rule.domain.valueobject;

import java.util.List;

/**
 * CSV导入结果记录
 * 使用 JDK 17 的 Record 特性
 * 
 * <AUTHOR>
 */
public record CsvImportResult(
        int totalRows,
        int successRows,
        int errorRows,
        int duplicateRows,
        List<Integer> errorRowNumbers,
        List<Integer> duplicateRowNumbers,
        List<String> errorMessages
) {
    
    /**
     * 创建成功的导入结果
     */
    public static CsvImportResult success(int totalRows, int successRows) {
        return new CsvImportResult(
                totalRows, 
                successRows, 
                0, 
                0, 
                List.of(), 
                List.of(), 
                List.of()
        );
    }
    
    /**
     * 创建包含错误的导入结果
     */
    public static CsvImportResult withErrors(
            int totalRows, 
            int successRows, 
            int errorRows, 
            int duplicateRows,
            List<Integer> errorRowNumbers,
            List<Integer> duplicateRowNumbers,
            List<String> errorMessages) {
        return new CsvImportResult(
                totalRows, 
                successRows, 
                errorRows, 
                duplicateRows, 
                errorRowNumbers, 
                duplicateRowNumbers, 
                errorMessages
        );
    }
    
    /**
     * 检查是否完全成功
     */
    public boolean isCompleteSuccess() {
        return errorRows == 0 && duplicateRows == 0;
    }
    
    /**
     * 检查是否有错误
     */
    public boolean hasErrors() {
        return errorRows > 0 || !errorMessages.isEmpty();
    }
    
    /**
     * 检查是否有重复
     */
    public boolean hasDuplicates() {
        return duplicateRows > 0;
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        return totalRows > 0 ? (double) successRows / totalRows : 0.0;
    }
}

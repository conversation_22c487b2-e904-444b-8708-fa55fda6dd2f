package com.geeksec.rule.infrastructure.mapper;

import com.geeksec.rule.domain.entity.FilterMode;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 过滤模式Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FilterModeMapper extends BaseMapper<FilterMode> {

    /**
     * 根据任务ID查询过滤模式
     *
     * @param taskId 任务ID
     * @return 过滤模式
     */
    @Select("SELECT * FROM tb_filter_state WHERE task_id = #{taskId}")
    FilterMode selectByTaskId(@Param("taskId") Integer taskId);

    /**
     * 根据任务ID更新过滤模式
     *
     * @param taskId 任务ID
     * @param mode 新模式
     * @return 更新数量
     */
    @Update("UPDATE tb_filter_state SET state = #{mode} WHERE task_id = #{taskId}")
    Integer updateModeByTaskId(@Param("taskId") Integer taskId, @Param("mode") Integer mode);

    /**
     * 创建过滤模式记录
     *
     * @param taskId 任务ID
     * @param mode 模式
     * @return 插入数量
     */
    @Insert("INSERT INTO tb_filter_state (task_id, state) VALUES (#{taskId}, #{mode})")
    Integer createFilterMode(@Param("taskId") Integer taskId, @Param("mode") Integer mode);
}

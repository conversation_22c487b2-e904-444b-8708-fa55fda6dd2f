package com.geeksec.rule.interfaces.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.rule.interfaces.dto.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 检测规则VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "检测规则信息")
public class DetectionRuleVO {

    /**
     * 规则ID
     */
    @JsonProperty("id")
    @Schema(description = "规则ID", example = "1")
    private Long id;

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    @Schema(description = "任务ID", example = "1001")
    private Integer taskId;

    /**
     * 规则ID
     */
    @JsonProperty("rule_id")
    @Schema(description = "规则ID", example = "10001")
    private Integer ruleId;

    /**
     * 规则名称
     */
    @JsonProperty("rule_name")
    @Schema(description = "规则名称", example = "HTTP恶意请求检测")
    private String ruleName;

    /**
     * 规则描述
     */
    @JsonProperty("rule_desc")
    @Schema(description = "规则描述", example = "检测HTTP请求中的恶意特征")
    private String ruleDesc;

    /**
     * 规则级别
     */
    @JsonProperty("rule_level")
    @Schema(description = "规则级别", example = "1")
    private Integer ruleLevel;

    /**
     * 规则状态
     */
    @JsonProperty("rule_state")
    @Schema(description = "规则状态", example = "生效")
    private String ruleState;

    /**
     * 规则类型
     */
    @JsonProperty("rule_type")
    @Schema(description = "规则类型", example = "0")
    private Integer ruleType;

    /**
     * 采集模式
     */
    @JsonProperty("capture_mode")
    @Schema(description = "采集模式", example = "1")
    private Integer captureMode;

    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    @Schema(description = "创建时间", example = "1640995200")
    private Integer createdAt;

    /**
     * 修改时间
     */
    @JsonProperty("updated_at")
    @Schema(description = "修改时间", example = "1640995200")
    private Integer updatedAt;

    /**
     * 规则hash
     */
    @JsonProperty("rule_hash")
    @Schema(description = "规则hash")
    private String ruleHash;

    /**
     * 攻击阶段
     */
    @JsonProperty("attack_stage")
    @Schema(description = "攻击阶段", example = "RECONNAISSANCE")
    private String attackStage;

    /**
     * 流量留存限速
     */
    @JsonProperty("traffic_rate_limit_bps")
    @Schema(description = "流量留存限速", example = "1048576")
    private Long trafficRateLimitBps;

    /**
     * 流量留存上限
     */
    @JsonProperty("traffic_retention_limit_bytes")
    @Schema(description = "流量留存上限", example = "104857600")
    private Long trafficRetentionLimitBytes;

    /**
     * 是否留存流量元数据
     */
    @JsonProperty("retain_metadata")
    @Schema(description = "是否留存流量元数据", example = "true")
    private Boolean retainMetadata;

    /**
     * 是否留存原始流量数据
     */
    @JsonProperty("retain_pcap")
    @Schema(description = "是否留存原始流量数据", example = "true")
    private Boolean retainPcap;

    /**
     * 是否开启动态库响应
     */
    @JsonProperty("lib_respond_open")
    @Schema(description = "是否开启动态库响应", example = "0")
    private Integer libRespondOpen;

    /**
     * 动态库路径
     */
    @JsonProperty("lib_respond_lib")
    @Schema(description = "动态库路径")
    private String libRespondLib;

    /**
     * 库配置路径
     */
    @JsonProperty("lib_respond_config")
    @Schema(description = "库配置路径")
    private String libRespondConfig;

    /**
     * 会话结束标识
     */
    @JsonProperty("lib_respond_session_end")
    @Schema(description = "会话结束标识", example = "0")
    private Long libRespondSessionEnd;

    /**
     * 响应包数
     */
    @JsonProperty("lib_respond_pkt_num")
    @Schema(description = "响应包数", example = "10")
    private Long libRespondPktNum;



    /**
     * IP规则集
     */
    @JsonProperty("ip_rules")
    @Schema(description = "IP规则集")
    private List<IpRuleDTO> ipRules;

    /**
     * 协议规则集
     */
    @JsonProperty("pro_rules")
    @Schema(description = "协议规则集")
    private List<ProtocolRuleDTO> proRules;

    /**
     * 特征字规则集
     */
    @JsonProperty("keyword_rules")
    @Schema(description = "特征字规则集")
    private List<KeywordRuleDTO> keywordRules;

    /**
     * 正则规则集
     */
    @JsonProperty("regex_rules")
    @Schema(description = "正则规则集")
    private List<RegexRuleDTO> regexRules;

    /**
     * 域名规则集
     */
    @JsonProperty("domain_rules")
    @Schema(description = "域名规则集")
    private List<DomainRuleDTO> domainRules;

    /**
     * 复杂规则响应
     */
    @JsonProperty("detail_respond")
    @Schema(description = "复杂规则响应")
    private Map<String, Object> detailRespond;
}

package com.geeksec.rule.domain.enums;

import lombok.Getter;

/**
 * 采集模式枚举
 *
 * <AUTHOR>
 */
@Getter
public enum CaptureModeEnum {

    /** 单包 */
    SINGLE_PACKET(1, "单包"),
    /** 连接 */
    CONNECTION(2, "连接"),
    /** 获取源IP */
    SOURCE_IP(3, "获取源IP"),
    /** 获取目的IP */
    DEST_IP(4, "获取目的IP"),
    /** 获取源IP+Port */
    SOURCE_IP_PORT(5, "获取源IP+Port"),
    /** 获取目的IP+Port */
    DEST_IP_PORT(6, "获取目的IP+Port"),
    /** 获取源IP+目的IP */
    SOURCE_DEST_IP(7, "获取源IP+目的IP"),
    /** 获取DNS 客户端IP */
    DNS_CLIENT_IP(8, "获取DNS 客户端IP"),
    /** 获取DNS A记录端IP */
    DNS_A_RECORD_IP(9, "获取DNS A记录端IP"),
    /** 获取DNS 客户端+A记录 */
    DNS_CLIENT_A_RECORD(10, "获取DNS 客户端+A记录");

    private final int code;
    private final String description;

    CaptureModeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码查找枚举
     *
     * @param code 采集模式代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static CaptureModeEnum findByCode(int code) {
        for (CaptureModeEnum modeEnum : values()) {
            if (modeEnum.code == code) {
                return modeEnum;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 采集模式代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return findByCode(code) != null;
    }
}

package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 协议规则DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "协议规则")
public class ProtocolRuleDTO {

    /**
     * 协议ID
     */
    @NotNull(message = "协议ID不能为空")
    @JsonProperty("pro_id")
    @Schema(description = "协议ID", example = "6")
    private Integer proId;

    /**
     * 端口规则
     */
    @Valid
    @JsonProperty("port_rule")
    @Schema(description = "端口规则")
    private PortRuleDTO portRule;
}

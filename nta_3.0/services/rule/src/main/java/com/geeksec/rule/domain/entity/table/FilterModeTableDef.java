package com.geeksec.rule.domain.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

/**
 * 过滤模式实体表定义类
 *
 * 由 MyBatis-Flex 注解处理器生成，用于类型安全的查询构建
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class FilterModeTableDef extends TableDef {

    /**
     * 过滤模式实体表定义实例
     */
    public static final FilterModeTableDef FILTER_MODE = new FilterModeTableDef();

    /**
     * 自增ID
     */
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 任务ID
     */
    public final QueryColumn TASK_ID = new QueryColumn(this, "task_id");

    /**
     * 是否丢弃模式：false-保留 true-丢弃
     */
    public final QueryColumn DROP_MODE = new QueryColumn(this, "drop_mode");

    /**
     * 构造函数
     */
    public FilterModeTableDef() {
        super("", "filter_mode");
    }

    /**
     * 带别名的构造函数
     */
    public FilterModeTableDef(String alias) {
        super("", "filter_mode", alias);
    }
}

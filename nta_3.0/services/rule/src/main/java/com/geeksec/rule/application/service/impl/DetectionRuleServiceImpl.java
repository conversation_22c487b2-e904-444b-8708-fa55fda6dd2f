package com.geeksec.rule.application.service.impl;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.rule.application.service.DetectionRuleService;
import com.geeksec.rule.domain.entity.DetectionRule;
import com.geeksec.rule.infrastructure.mapper.DetectionRuleMapper;
import com.geeksec.rule.interfaces.dto.DetectionRuleCreateDTO;
import com.geeksec.rule.interfaces.vo.DetectionRuleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 检测规则服务实现类
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DetectionRuleServiceImpl implements DetectionRuleService {

    private final DetectionRuleMapper detectionRuleMapper;
    private final RuleValidationService ruleValidationService;

    @Override
    @Transactional
    public ApiResponse<Void> createDetectionRule(DetectionRuleCreateDTO createDTO) {
        log.info("创建检测规则: {}", createDTO);
        
        try {
            // 验证规则
            var validationResult = ruleValidationService.validateDetectionRule(createDTO);
            if (!validationResult.isValid()) {
                return ApiResponse.badRequest("规则验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            // 创建规则实体
            DetectionRule rule = new DetectionRule();
            rule.setRuleName(createDTO.getRuleName());
            rule.setTaskId(createDTO.getTaskId());
            rule.setAttackStage(createDTO.getAttackStage());
            rule.setRuleHash(validationResult.getRuleHash());
            rule.setRuleJson(createDTO.toString());
            rule.setCreatedAt(LocalDateTime.now());
            rule.setUpdatedAt(LocalDateTime.now());
            rule.setRuleState("ON");
            rule.setStatus(1);

            // 获取最大规则ID并设置
            Integer maxRuleId = detectionRuleMapper.getMaxRuleId();
            rule.setRuleId(maxRuleId + 1);

            // 保存规则
            detectionRuleMapper.insert(rule);
            
            log.info("检测规则创建成功，规则ID: {}", rule.getRuleId());
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("创建检测规则失败", e);
            return ApiResponse.error("创建检测规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> updateDetectionRule(Long id, DetectionRuleCreateDTO updateDTO) {
        log.info("更新检测规则，ID: {}, 数据: {}", id, updateDTO);
        
        try {
            // 检查规则是否存在
            DetectionRule existingRule = detectionRuleMapper.selectById(id);
            if (existingRule == null) {
                return ApiResponse.error(404, "检测规则不存在");
            }

            // 验证规则
            var validationResult = ruleValidationService.validateDetectionRule(updateDTO);
            if (!validationResult.isValid()) {
                return ApiResponse.badRequest("规则验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            // 更新规则
            existingRule.setRuleName(updateDTO.getRuleName());
            existingRule.setAttackStage(updateDTO.getAttackStage());
            existingRule.setRuleHash(validationResult.getRuleHash());
            existingRule.setRuleJson(updateDTO.toString());
            existingRule.setUpdatedAt(LocalDateTime.now());

            detectionRuleMapper.updateById(existingRule);
            
            log.info("检测规则更新成功，ID: {}", id);
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("更新检测规则失败，ID: {}", id, e);
            return ApiResponse.error("更新检测规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> deleteDetectionRules(List<Long> ids) {
        log.info("删除检测规则，IDs: {}", ids);
        
        try {
            if (ids == null || ids.isEmpty()) {
                return ApiResponse.badRequest("规则ID列表不能为空");
            }

            // 批量删除（软删除）
            detectionRuleMapper.deleteBatchIds(ids);
            
            log.info("检测规则删除成功，删除数量: {}", ids.size());
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("删除检测规则失败，IDs: {}", ids, e);
            return ApiResponse.error("删除检测规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> deleteDetectionRulesByTaskId(Integer taskId) {
        log.info("根据任务ID删除检测规则，任务ID: {}", taskId);
        
        try {
            if (taskId == null || taskId <= 0) {
                return ApiResponse.badRequest("任务ID无效");
            }

            // 根据任务ID删除规则
            detectionRuleMapper.delete(
                detectionRuleMapper.query()
                    .where(DetectionRule::getTaskId).eq(taskId)
                    .build()
            );
            
            log.info("根据任务ID删除检测规则成功，任务ID: {}", taskId);
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("根据任务ID删除检测规则失败，任务ID: {}", taskId, e);
            return ApiResponse.error("删除检测规则失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<DetectionRuleVO> getDetectionRule(Long id) {
        log.info("获取检测规则详情，ID: {}", id);
        
        try {
            if (id == null || id <= 0) {
                return ApiResponse.badRequest("规则ID无效");
            }

            DetectionRule rule = detectionRuleMapper.selectById(id);
            if (rule == null) {
                return ApiResponse.error(404, "检测规则不存在");
            }

            // 转换为VO
            DetectionRuleVO vo = convertToVO(rule);
            
            return ApiResponse.success(vo);
            
        } catch (Exception e) {
            log.error("获取检测规则详情失败，ID: {}", id, e);
            return ApiResponse.error("获取检测规则详情失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<PageResultVo<DetectionRuleVO>> getDetectionRules(
            Integer taskId, Integer ruleId, String ruleName, String ruleState,
            String attackStage, String orderField, String sortOrder,
            Integer currentPage, Integer pageSize) {
        
        log.info("分页查询检测规则，任务ID: {}, 页码: {}, 页大小: {}", taskId, currentPage, pageSize);
        
        try {
            // TODO: 实现分页查询逻辑
            // 这里需要根据实际的数据访问层实现
            
            // 临时返回空结果
            PageResultVo<DetectionRuleVO> pageResult = new PageResultVo<>();
            pageResult.setRecords(List.of());
            pageResult.setTotal(0);
            pageResult.setCurrentPage(currentPage);
            pageResult.setPageSize(pageSize);
            
            return ApiResponse.success(pageResult);
            
        } catch (Exception e) {
            log.error("分页查询检测规则失败", e);
            return ApiResponse.error("查询检测规则失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ApiResponse<Void> updateRuleState(Long id, String state) {
        log.info("更新规则状态，ID: {}, 状态: {}", id, state);
        
        try {
            if (id == null || id <= 0) {
                return ApiResponse.badRequest("规则ID无效");
            }

            if (!"ON".equals(state) && !"OFF".equals(state)) {
                return ApiResponse.badRequest("规则状态无效，只能是 ON 或 OFF");
            }

            DetectionRule rule = detectionRuleMapper.selectById(id);
            if (rule == null) {
                return ApiResponse.error(404, "检测规则不存在");
            }

            rule.setRuleState(state);
            rule.setUpdatedAt(LocalDateTime.now());
            detectionRuleMapper.updateById(rule);
            
            log.info("规则状态更新成功，ID: {}, 新状态: {}", id, state);
            return ApiResponse.success();
            
        } catch (Exception e) {
            log.error("更新规则状态失败，ID: {}, 状态: {}", id, state, e);
            return ApiResponse.error("更新规则状态失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<Object> importDetectionRulesFromCsv(Integer taskId, String csvContent) {
        log.info("批量导入检测规则，任务ID: {}", taskId);
        
        try {
            // TODO: 实现CSV导入逻辑
            return ApiResponse.success("导入功能待实现");
            
        } catch (Exception e) {
            log.error("批量导入检测规则失败，任务ID: {}", taskId, e);
            return ApiResponse.error("导入检测规则失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<String> exportDetectionRuleTemplate() {
        log.info("导出检测规则模板");
        
        try {
            // TODO: 实现模板导出逻辑
            return ApiResponse.success("模板导出功能待实现");
            
        } catch (Exception e) {
            log.error("导出检测规则模板失败", e);
            return ApiResponse.error("导出模板失败: " + e.getMessage());
        }
    }

    @Override
    public ApiResponse<List<Object>> getTagInfoByRuleIds(List<Integer> ruleIds) {
        log.info("获取标签信息，规则IDs: {}", ruleIds);
        
        try {
            // TODO: 实现标签信息获取逻辑
            return ApiResponse.success(List.of());
            
        } catch (Exception e) {
            log.error("获取标签信息失败，规则IDs: {}", ruleIds, e);
            return ApiResponse.error("获取标签信息失败: " + e.getMessage());
        }
    }

    /**
     * 将实体转换为VO
     */
    private DetectionRuleVO convertToVO(DetectionRule rule) {
        DetectionRuleVO vo = new DetectionRuleVO();
        vo.setId(rule.getId());
        vo.setRuleId(rule.getRuleId());
        vo.setRuleName(rule.getRuleName());
        vo.setTaskId(rule.getTaskId());
        vo.setAttackStage(rule.getAttackStage());
        vo.setRuleState(rule.getRuleState());
        vo.setCreatedAt(rule.getCreatedAt() != null ?
            (int) rule.getCreatedAt().atZone(ZoneId.systemDefault()).toEpochSecond() : null);
        vo.setUpdatedAt(rule.getUpdatedAt() != null ?
            (int) rule.getUpdatedAt().atZone(ZoneId.systemDefault()).toEpochSecond() : null);
        // TODO: 解析 ruleJson 并设置具体的规则字段
        return vo;
    }
}

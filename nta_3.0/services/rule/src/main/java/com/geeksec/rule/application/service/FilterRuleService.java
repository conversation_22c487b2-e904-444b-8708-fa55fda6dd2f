package com.geeksec.rule.application.service;

import com.geeksec.common.dto.ApiResponse;
import com.geeksec.common.entity.PageResultVo;
import com.geeksec.rule.interfaces.dto.FilterRuleCreateDTO;
import com.geeksec.rule.interfaces.dto.FilterRuleQueryDTO;
import com.geeksec.rule.interfaces.vo.FilterRuleVO;
import com.geeksec.rule.interfaces.vo.FilterModeVO;

import java.util.List;

/**
 * 过滤规则服务接口
 * 
 * <AUTHOR>
 */
public interface FilterRuleService {

    /**
     * 创建过滤规则
     *
     * @param createDTO 创建请求
     * @return 创建结果
     */
    ApiResponse<Void> createFilterRule(FilterRuleCreateDTO createDTO);

    /**
     * 更新过滤规则
     *
     * @param id 规则ID
     * @param updateDTO 更新请求
     * @return 更新结果
     */
    ApiResponse<Void> updateFilterRule(Long id, FilterRuleCreateDTO updateDTO);

    /**
     * 删除过滤规则
     *
     * @param ids 规则ID列表
     * @return 删除结果
     */
    ApiResponse<Void> deleteFilterRules(List<Long> ids);

    /**
     * 根据任务ID删除所有过滤规则
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    ApiResponse<Void> deleteFilterRulesByTaskId(Integer taskId);

    /**
     * 分页查询过滤规则
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    ApiResponse<PageResultVo<FilterRuleVO>> getFilterRules(FilterRuleQueryDTO queryDTO);

    /**
     * 修改过滤模式（命中留存/丢弃）
     *
     * @param taskId 任务ID
     * @param mode 过滤模式
     * @return 修改结果
     */
    ApiResponse<Void> modifyFilterMode(Integer taskId, Integer mode);

    /**
     * 获取任务的过滤模式
     *
     * @param taskId 任务ID
     * @return 过滤模式
     */
    ApiResponse<FilterModeVO> getFilterMode(Integer taskId);

    /**
     * 批量导入过滤规则（CSV）
     *
     * @param taskId 任务ID
     * @param csvContent CSV内容
     * @return 导入结果
     */
    ApiResponse<Object> importFilterRulesFromCsv(Integer taskId, String csvContent);

    /**
     * 导出过滤规则模板
     *
     * @return 模板内容
     */
    ApiResponse<String> exportFilterRuleTemplate();

    /**
     * 获取任务的过滤规则列表（用于导出）
     *
     * @param taskId 任务ID
     * @return 规则列表
     */
    ApiResponse<List<List<String>>> getFilterRulesForExport(Integer taskId);
}

package com.geeksec.rule.domain.valueobject;

import java.util.List;

/**
 * 规则验证结果记录
 * 使用 JDK 17 的 Record 特性
 * 
 * <AUTHOR>
 */
public record RuleValidationResult(
        boolean isValid,
        List<String> errorMessages,
        String ruleHash
) {
    
    /**
     * 创建成功的验证结果
     */
    public static RuleValidationResult success(String ruleHash) {
        return new RuleValidationResult(true, List.of(), ruleHash);
    }
    
    /**
     * 创建失败的验证结果
     */
    public static RuleValidationResult failure(List<String> errorMessages) {
        return new RuleValidationResult(false, errorMessages, null);
    }
    
    /**
     * 创建失败的验证结果（单个错误）
     */
    public static RuleValidationResult failure(String errorMessage) {
        return new RuleValidationResult(false, List.of(errorMessage), null);
    }
    
    /**
     * 检查是否有错误
     */
    public boolean hasErrors() {
        return !isValid || (errorMessages != null && !errorMessages.isEmpty());
    }
    
    /**
     * 获取第一个错误消息
     */
    public String getFirstError() {
        return errorMessages != null && !errorMessages.isEmpty() ? errorMessages.get(0) : null;
    }
}

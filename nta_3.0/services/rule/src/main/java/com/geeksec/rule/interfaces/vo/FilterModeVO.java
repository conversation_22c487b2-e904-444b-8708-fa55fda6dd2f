package com.geeksec.rule.interfaces.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 过滤模式VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "过滤模式信息")
public class FilterModeVO {

    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    @Schema(description = "任务ID", example = "1001")
    private Integer taskId;

    /**
     * 过滤模式：0-保留匹配项 1-丢弃匹配项
     */
    @JsonProperty("mode")
    @Schema(description = "过滤模式", example = "0")
    private Integer mode;

    /**
     * 模式描述
     */
    @JsonProperty("mode_desc")
    @Schema(description = "模式描述", example = "保留匹配项")
    private String modeDesc;
}

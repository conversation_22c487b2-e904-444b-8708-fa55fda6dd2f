package com.geeksec.rule.domain.entity;

import com.geeksec.rule.domain.enums.FilterCriteriaEnum;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 过滤规则实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Table("filter_rule")
public class FilterRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @Id(keyType = KeyType.Auto)
    private Long id;

    /**
     * 任务ID
     */
    private Integer taskId;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 过滤规则JSON字符串
     */
    private String filterJson;

    /**
     * 创建时间
     */
    @Column("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 规则hash值，用于校验重复
     */
    private String hash;

    /**
     * 过滤条件：PORT-端口 INTERNET_PROTOCOL-IP协议 SUBNET-网段
     */
    @Column("criteria")
    private FilterCriteriaEnum criteria;

    /**
     * 是否激活：false-删除 true-正常
     */
    @Column("active")
    private boolean active;
}

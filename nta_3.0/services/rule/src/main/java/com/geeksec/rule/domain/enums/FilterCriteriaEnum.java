package com.geeksec.rule.domain.enums;

import lombok.Getter;

/**
 * 过滤条件枚举
 *
 * <AUTHOR>
 */
@Getter
public enum FilterCriteriaEnum {

    /** 按端口过滤 */
    PORT(0, "按端口过滤"),
    /** 按互联网协议过滤（IANA协议号） */
    INTERNET_PROTOCOL(1, "按互联网协议过滤"),
    /** 按子网过滤（IP+子网掩码） */
    SUBNET(2, "按子网过滤");

    private final int code;
    private final String description;

    FilterCriteriaEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码查找枚举
     *
     * @param code 过滤条件代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static FilterCriteriaEnum findByCode(int code) {
        for (FilterCriteriaEnum criteriaEnum : values()) {
            if (criteriaEnum.code == code) {
                return criteriaEnum;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 过滤条件代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        return findByCode(code) != null;
    }
}

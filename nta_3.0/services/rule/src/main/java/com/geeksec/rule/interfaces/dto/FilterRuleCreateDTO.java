package com.geeksec.rule.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 过滤规则创建DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "过滤规则创建请求")
public class FilterRuleCreateDTO {

    /**
     * 任务ID
     */
    @NotNull(message = "任务ID不能为空")
    @JsonProperty("task_id")
    @Schema(description = "任务ID", example = "1001")
    private Integer taskId;

    /**
     * 任务批次ID
     */
    @NotNull(message = "批次ID不能为空")
    @JsonProperty("batch_id")
    @Schema(description = "任务批次ID", example = "1")
    private Integer batchId;

    /**
     * 过滤IP地址
     */
    @JsonProperty("ip")
    @Schema(description = "过滤IP地址", example = "***********")
    private String ip;

    /**
     * 过滤规则对象信息
     */
    @Valid
    @NotNull(message = "过滤规则信息不能为空")
    @JsonProperty("filter_info")
    @Schema(description = "过滤规则对象信息")
    private FilterInfoDTO filterInfo;

    /**
     * 过滤规则类型：0-端口，1-IP协议，2-网段
     */
    @JsonProperty("type")
    @Schema(description = "过滤规则类型", example = "0", allowableValues = {"0", "1", "2"})
    private Integer type;
}

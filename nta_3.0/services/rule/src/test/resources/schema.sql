-- 检测规则配置表
CREATE TABLE IF NOT EXISTS detection_rule_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    rule_id INT NOT NULL UNIQUE,
    rule_level INT NOT NULL,
    rule_name TEXT NOT NULL,
    rule_desc TEXT NOT NULL,
    rule_state VARCHAR(255) NOT NULL DEFAULT '生效',
    rule_type INT NOT NULL DEFAULT 0,
    capture_mode INT NOT NULL,
    rule_json TEXT NOT NULL,
    rule_hash VARCHAR(255) NOT NULL,
    attack_stage VARCHAR(50) DEFAULT 'OTHER',
    traffic_rate_limit_bps BIGINT NOT NULL DEFAULT 0,
    traffic_retention_limit_bytes BIGINT NOT NULL DEFAULT -1,
    retain_metadata BOOLEAN NOT NULL DEFAULT FALSE,
    retain_pcap BOOLEAN NOT NULL DEFAULT FALSE,
    lib_respond_open INT NOT NULL DEFAULT 0,
    lib_respond_lib VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_config VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_session_end BIGINT NOT NULL DEFAULT 0,
    lib_respond_pkt_num BIGINT NOT NULL DEFAULT 0,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 过滤规则配置表
CREATE TABLE IF NOT EXISTS tb_filter_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL,
    ip VARCHAR(255),
    filter_json TEXT,
    created_time INT,
    updated_time INT,
    hash VARCHAR(255),
    type INT DEFAULT 0,
    status INT DEFAULT 1
);

-- 过滤模式表
CREATE TABLE IF NOT EXISTS tb_filter_state (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id INT NOT NULL UNIQUE,
    state INT DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_detection_rule_task_id ON detection_rule_configs(task_id);
CREATE INDEX IF NOT EXISTS idx_detection_rule_rule_id ON detection_rule_configs(rule_id);
CREATE INDEX IF NOT EXISTS idx_detection_rule_hash ON detection_rule_configs(rule_hash);

CREATE INDEX IF NOT EXISTS idx_filter_config_task_id ON tb_filter_config(task_id);
CREATE INDEX IF NOT EXISTS idx_filter_config_hash ON tb_filter_config(hash);
CREATE INDEX IF NOT EXISTS idx_filter_config_status ON tb_filter_config(status);

CREATE INDEX IF NOT EXISTS idx_filter_state_task_id ON tb_filter_state(task_id);

package com.geeksec.rule.application;

import com.geeksec.rule.application.service.impl.RuleValidationService;
import com.geeksec.rule.domain.valueobject.RuleValidationResult;
import com.geeksec.rule.interfaces.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 规则验证服务单元测试
 *
 * <AUTHOR>
 */
@DisplayName("规则验证服务测试")
class RuleValidationServiceTest {

    private RuleValidationService validationService;

    @BeforeEach
    void setUp() {
        validationService = new RuleValidationService();
    }

    @Test
    @DisplayName("验证有效的检测规则")
    void testValidateValidDetectionRule() {
        DetectionRuleCreateDTO dto = createValidDetectionRuleDTO();

        RuleValidationResult result = validationService.validateDetectionRule(dto);

        assertTrue(result.isValid());
        assertFalse(result.hasErrors());
        assertNotNull(result.ruleHash());
        assertFalse(result.ruleHash().isEmpty());
    }

    @Test
    @DisplayName("验证无效的检测规则 - 缺少必填字段")
    void testValidateInvalidDetectionRule_MissingFields() {
        DetectionRuleCreateDTO dto = new DetectionRuleCreateDTO();
        // 不设置任何字段

        RuleValidationResult result = validationService.validateDetectionRule(dto);

        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
        assertTrue(result.errorMessages().size() > 0);
        assertNull(result.ruleHash());
    }

    @Test
    @DisplayName("验证无效的检测规则 - 攻击阶段无效")
    void testValidateInvalidDetectionRule_InvalidAttackStage() {
        DetectionRuleCreateDTO dto = createValidDetectionRuleDTO();
        dto.setAttackStage("INVALID_STAGE"); // 无效攻击阶段

        RuleValidationResult result = validationService.validateDetectionRule(dto);

        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
        assertTrue(result.errorMessages().stream()
                .anyMatch(msg -> msg.contains("无效的攻击阶段")));
    }

    @Test
    @DisplayName("验证无效的IP规则")
    void testValidateInvalidIpRules() {
        DetectionRuleCreateDTO dto = createValidDetectionRuleDTO();

        // 添加无效的IP规则
        IpRuleDTO invalidIpRule = new IpRuleDTO();
        invalidIpRule.setIp("256.256.256.256"); // 无效IP
        invalidIpRule.setType(5); // 无效类型

        dto.setIpRules(List.of(invalidIpRule));

        RuleValidationResult result = validationService.validateDetectionRule(dto);

        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
        assertTrue(result.errorMessages().stream()
                .anyMatch(msg -> msg.contains("IP地址格式无效")));
        assertTrue(result.errorMessages().stream()
                .anyMatch(msg -> msg.contains("IP类型取值范围为1-3")));
    }

    @Test
    @DisplayName("验证无效的正则规则")
    void testValidateInvalidRegexRules() {
        DetectionRuleCreateDTO dto = createValidDetectionRuleDTO();

        // 添加无效的正则规则
        RegexRuleDTO invalidRegexRule = new RegexRuleDTO();
        invalidRegexRule.setRegex("["); // 无效正则表达式
        invalidRegexRule.setProId(6);

        dto.setRegexRules(List.of(invalidRegexRule));

        RuleValidationResult result = validationService.validateDetectionRule(dto);

        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
        assertTrue(result.errorMessages().stream()
                .anyMatch(msg -> msg.contains("正则表达式无效")));
    }

    @Test
    @DisplayName("验证有效的过滤规则")
    void testValidateValidFilterRule() {
        FilterRuleCreateDTO dto = createValidFilterRuleDTO();

        RuleValidationResult result = validationService.validateFilterRule(dto);

        assertTrue(result.isValid());
        assertFalse(result.hasErrors());
        assertNotNull(result.ruleHash());
    }

    @Test
    @DisplayName("验证无效的过滤规则 - 端口过滤")
    void testValidateInvalidFilterRule_PortFilter() {
        FilterRuleCreateDTO dto = createValidFilterRuleDTO();
        dto.setType(0); // 端口过滤

        FilterInfoDTO filterInfo = new FilterInfoDTO();
        filterInfo.setPort(List.of(80, 65536)); // 包含无效端口
        dto.setFilterInfo(filterInfo);

        RuleValidationResult result = validationService.validateFilterRule(dto);

        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
        assertTrue(result.errorMessages().stream()
                .anyMatch(msg -> msg.contains("包含无效端口")));
    }

    @Test
    @DisplayName("验证无效的过滤规则 - 互联网协议过滤")
    void testValidateInvalidFilterRule_InternetProtocolFilter() {
        FilterRuleCreateDTO dto = createValidFilterRuleDTO();
        dto.setType(1); // 互联网协议过滤

        FilterInfoDTO filterInfo = new FilterInfoDTO();
        filterInfo.setProId(-1); // 无效协议ID
        filterInfo.setIp(List.of("256.256.256.256")); // 无效IP
        dto.setFilterInfo(filterInfo);

        RuleValidationResult result = validationService.validateFilterRule(dto);

        assertFalse(result.isValid());
        assertTrue(result.hasErrors());
        assertTrue(result.errorMessages().stream()
                .anyMatch(msg -> msg.contains("协议ID无效")));
        assertTrue(result.errorMessages().stream()
                .anyMatch(msg -> msg.contains("无效IP地址")));
    }

    private DetectionRuleCreateDTO createValidDetectionRuleDTO() {
        DetectionRuleCreateDTO dto = new DetectionRuleCreateDTO();
        dto.setTaskId(1001);
        dto.setRuleName("测试规则");
        dto.setRuleDesc("测试规则描述");
        dto.setRuleLevel(1);
        dto.setAttackStage("RECONNAISSANCE");
        dto.setTrafficRateLimitBps(1048576L);
        dto.setTrafficRetentionLimitBytes(104857600L);
        dto.setRetainMetadata(true);
        dto.setRetainPcap(true);
        dto.setLibRespondOpen(0);

        return dto;
    }

    private FilterRuleCreateDTO createValidFilterRuleDTO() {
        FilterRuleCreateDTO dto = new FilterRuleCreateDTO();
        dto.setTaskId(1001);
        dto.setBatchId(1);
        dto.setIp("***********");
        dto.setType(0); // 端口过滤

        FilterInfoDTO filterInfo = new FilterInfoDTO();
        filterInfo.setPort(List.of(80, 443, 8080));
        dto.setFilterInfo(filterInfo);

        return dto;
    }
}

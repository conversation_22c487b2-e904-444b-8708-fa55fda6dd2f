package com.geeksec.rule.domain;

import com.geeksec.rule.domain.utils.RuleUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 规则工具类单元测试
 * 
 * <AUTHOR>
 */
@DisplayName("规则工具类测试")
class RuleUtilsTest {

    @Test
    @DisplayName("生成规则hash值")
    void testGenerateRuleHash() {
        // 测试正常情况
        String content = "test rule content";
        String hash = RuleUtils.generateRuleHash(content);
        assertNotNull(hash);
        assertFalse(hash.isEmpty());
        assertEquals(32, hash.length()); // MD5 hash长度为32

        // 测试相同内容生成相同hash
        String hash2 = RuleUtils.generateRuleHash(content);
        assertEquals(hash, hash2);

        // 测试不同内容生成不同hash
        String hash3 = RuleUtils.generateRuleHash("different content");
        assertNotEquals(hash, hash3);

        // 测试空内容
        String emptyHash = RuleUtils.generateRuleHash("");
        assertNotNull(emptyHash);

        // 测试null内容
        String nullHash = RuleUtils.generateRuleHash(null);
        assertEquals("", nullHash);
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "***********",
            "********",
            "**********",
            "127.0.0.1",
            "***************",
            "0.0.0.0"
    })
    @DisplayName("验证有效IP地址")
    void testValidIpAddresses(String ip) {
        assertTrue(RuleUtils.isValidIp(ip), "IP地址应该有效: " + ip);
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "256.1.1.1",
            "192.168.1",
            "***********.1",
            "abc.def.ghi.jkl",
            "",
            "192.168.-1.1",
            "192.168.1.256"
    })
    @DisplayName("验证无效IP地址")
    void testInvalidIpAddresses(String ip) {
        assertFalse(RuleUtils.isValidIp(ip), "IP地址应该无效: " + ip);
    }

    @Test
    @DisplayName("验证null IP地址")
    void testNullIpAddress() {
        assertFalse(RuleUtils.isValidIp(null));
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "example.com",
            "www.example.com",
            "sub.domain.example.com",
            "test-domain.com",
            "a.b",
            "localhost"
    })
    @DisplayName("验证有效域名")
    void testValidDomains(String domain) {
        assertTrue(RuleUtils.isValidDomain(domain), "域名应该有效: " + domain);
    }

    @ParameterizedTest
    @ValueSource(strings = {
            "",
            ".example.com",
            "example..com",
            "example.com.",
            "-example.com",
            "example-.com"
    })
    @DisplayName("验证无效域名")
    void testInvalidDomains(String domain) {
        assertFalse(RuleUtils.isValidDomain(domain), "域名应该无效: " + domain);
    }

    @Test
    @DisplayName("验证null域名")
    void testNullDomain() {
        assertFalse(RuleUtils.isValidDomain(null));
    }

    @ParameterizedTest
    @ValueSource(ints = {0, 1, 80, 443, 8080, 65535})
    @DisplayName("验证有效端口")
    void testValidPorts(int port) {
        assertTrue(RuleUtils.isValidPort(port), "端口应该有效: " + port);
    }

    @ParameterizedTest
    @ValueSource(ints = {-1, 65536, 100000})
    @DisplayName("验证无效端口")
    void testInvalidPorts(int port) {
        assertFalse(RuleUtils.isValidPort(port), "端口应该无效: " + port);
    }

    @Test
    @DisplayName("验证null端口")
    void testNullPort() {
        assertFalse(RuleUtils.isValidPort(null));
    }

    @Test
    @DisplayName("验证端口列表")
    void testValidatePorts() {
        // 测试有效端口列表
        List<Integer> validPorts = List.of(80, 443, 8080);
        assertTrue(RuleUtils.isValidPorts(validPorts));

        // 测试包含无效端口的列表
        List<Integer> invalidPorts = List.of(80, 443, 65536);
        assertFalse(RuleUtils.isValidPorts(invalidPorts));

        // 测试空列表
        assertTrue(RuleUtils.isValidPorts(List.of()));

        // 测试null列表
        assertFalse(RuleUtils.isValidPorts(null));
    }

    @Test
    @DisplayName("布尔值转整数")
    void testBooleanToInteger() {
        assertEquals(1, RuleUtils.booleanToInteger(true));
        assertEquals(0, RuleUtils.booleanToInteger(false));
        assertEquals(0, RuleUtils.booleanToInteger(null));
    }

    @Test
    @DisplayName("整数转布尔值")
    void testIntegerToBoolean() {
        assertTrue(RuleUtils.integerToBoolean(1));
        assertFalse(RuleUtils.integerToBoolean(0));
        assertFalse(RuleUtils.integerToBoolean(2));
        assertFalse(RuleUtils.integerToBoolean(-1));
        assertFalse(RuleUtils.integerToBoolean(null));
    }

    @Test
    @DisplayName("验证规则状态")
    void testValidateRuleState() {
        assertTrue(RuleUtils.isValidRuleState("生效"));
        assertTrue(RuleUtils.isValidRuleState("失效"));
        assertFalse(RuleUtils.isValidRuleState("无效状态"));
        assertFalse(RuleUtils.isValidRuleState(null));
        assertFalse(RuleUtils.isValidRuleState(""));
    }

    @Test
    @DisplayName("验证过滤条件")
    void testValidateFilterCriteria() {
        assertTrue(RuleUtils.isValidFilterCriteria(0));
        assertTrue(RuleUtils.isValidFilterCriteria(1));
        assertTrue(RuleUtils.isValidFilterCriteria(2));
        assertFalse(RuleUtils.isValidFilterCriteria(3));
        assertFalse(RuleUtils.isValidFilterCriteria(-1));
        assertFalse(RuleUtils.isValidFilterCriteria(null));
    }

    @Test
    @DisplayName("构建和解析规则类型字符串")
    void testRuleTypeString() {
        // 测试构建
        List<String> types = List.of("1", "2", "3");
        String typeString = RuleUtils.buildRuleTypeString(types);
        assertEquals("1,2,3", typeString);

        // 测试解析
        List<String> parsedTypes = RuleUtils.parseRuleTypeString(typeString);
        assertEquals(types, parsedTypes);

        // 测试空列表
        assertEquals("", RuleUtils.buildRuleTypeString(List.of()));
        assertEquals("", RuleUtils.buildRuleTypeString(null));

        // 测试空字符串解析
        assertEquals(List.of(), RuleUtils.parseRuleTypeString(""));
        assertEquals(List.of(), RuleUtils.parseRuleTypeString(null));
    }

    @Test
    @DisplayName("验证正则表达式")
    void testValidateRegex() {
        // 有效正则表达式
        assertTrue(RuleUtils.isValidRegex(".*"));
        assertTrue(RuleUtils.isValidRegex("\\d+"));
        assertTrue(RuleUtils.isValidRegex("[a-zA-Z]+"));

        // 无效正则表达式
        assertFalse(RuleUtils.isValidRegex("["));
        assertFalse(RuleUtils.isValidRegex("*"));
        assertFalse(RuleUtils.isValidRegex("(?"));

        // 空值
        assertFalse(RuleUtils.isValidRegex(null));
        assertFalse(RuleUtils.isValidRegex(""));
        assertFalse(RuleUtils.isValidRegex("   "));
    }

    @Test
    @DisplayName("获取当前时间戳")
    void testGetCurrentTimestamp() {
        int timestamp1 = RuleUtils.getCurrentTimestamp();
        int timestamp2 = RuleUtils.getCurrentTimestamp();
        
        // 时间戳应该是正数
        assertTrue(timestamp1 > 0);
        assertTrue(timestamp2 > 0);
        
        // 两次调用的时间戳应该相等或第二次稍大
        assertTrue(timestamp2 >= timestamp1);
    }
}

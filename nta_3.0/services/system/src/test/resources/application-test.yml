server:
  port: 0

spring:
  application:
    name: system-service-test
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:

mybatis-flex:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.geeksec.nta.system.domain.model
  configuration:
    map-underscore-to-camel-case: true

# 外部系统URL配置（测试环境） - 使用环境变量
external-system-url:
  shutdown: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/shutdown
  reboot: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/reboot
  disk-data: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/disk_data
  clean-data: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/clean_task_data
  system-reset: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/thd/reset
  clean-data-status: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/clean_task_status
  disk-change: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/fdisk_change
  disk-rebuild: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/fdisk_rebliud
  check-so: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/thd/check_so
  docker-check-so: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/thd/docker_check_so
  disk-rebuild-check: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/fdisk_check
  ready-mount: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/ready_mount
  mount-data: ${EXTERNAL_SYSTEM_URL:http://localhost:59000}/system/mount_data



logging:
  level:
    com.geeksec.nta.system: DEBUG

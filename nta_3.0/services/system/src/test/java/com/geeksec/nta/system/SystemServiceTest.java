package com.geeksec.nta.system;

import com.geeksec.nta.system.domain.model.SystemInfoVo;
import com.geeksec.nta.system.domain.service.SystemService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 系统管理服务测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class SystemServiceTest {

    @Autowired(required = false)
    private SystemService systemService;

    @Test
    public void testGetSystemInfo() {
        if (systemService != null) {
            try {
                SystemInfoVo systemInfo = systemService.getSystemInfo();
                System.out.println("系统信息获取测试通过");
                if (systemInfo != null) {
                    System.out.println("主机名: " + systemInfo.getHostname());
                    System.out.println("操作系统: " + systemInfo.getOsinfo());
                }
            } catch (Exception e) {
                System.out.println("系统信息获取测试失败: " + e.getMessage());
            }
        } else {
            System.out.println("SystemService 未注入，跳过测试");
        }
    }

    @Test
    public void testApplicationContext() {
        System.out.println("Spring 应用上下文加载测试通过");
    }
}

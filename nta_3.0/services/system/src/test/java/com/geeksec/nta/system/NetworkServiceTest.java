package com.geeksec.nta.system;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.nta.system.domain.model.NetworkConfig;
import com.geeksec.nta.system.domain.service.NetworkService;
import com.geeksec.nta.system.domain.service.LibraryCheckService;
import com.geeksec.nta.system.domain.service.DataReadService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 网络服务和其他Java服务测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class NetworkServiceTest {

    @Autowired(required = false)
    private NetworkService networkService;

    @Autowired(required = false)
    private LibraryCheckService libraryCheckService;

    @Autowired(required = false)
    private DataReadService dataReadService;

    @Test
    public void testGetNetworkConfig() {
        if (networkService != null) {
            try {
                NetworkConfig config = networkService.getNetworkConfig();
                System.out.println("网络配置获取测试通过");
                if (config != null) {
                    System.out.println("设备: " + config.getDevice());
                    System.out.println("IP: " + config.getIp());
                }
            } catch (Exception e) {
                System.out.println("网络配置获取测试失败: " + e.getMessage());
            }
        } else {
            System.out.println("NetworkService 未注入，跳过测试");
        }
    }

    @Test
    public void testGetAllNetworkInterfaces() {
        if (networkService != null) {
            try {
                JSONObject result = networkService.getAllNetworkInterfaces();
                System.out.println("网络接口获取测试通过");
                System.out.println("结果: " + result.toJSONString());
            } catch (Exception e) {
                System.out.println("网络接口获取测试失败: " + e.getMessage());
            }
        } else {
            System.out.println("NetworkService 未注入，跳过测试");
        }
    }

    @Test
    public void testCheckSoFiles() {
        if (libraryCheckService != null) {
            try {
                JSONObject result = libraryCheckService.checkSoFiles(null);
                System.out.println("动态库检测测试通过");
                System.out.println("结果: " + result.toJSONString());
            } catch (Exception e) {
                System.out.println("动态库检测测试失败: " + e.getMessage());
            }
        } else {
            System.out.println("LibraryCheckService 未注入，跳过测试");
        }
    }

    @Test
    public void testGetInstalledLibraries() {
        if (libraryCheckService != null) {
            try {
                JSONObject result = libraryCheckService.getInstalledLibraries();
                System.out.println("已安装库列表获取测试通过");
                System.out.println("库数量: " + result.getInteger("total_count"));
            } catch (Exception e) {
                System.out.println("已安装库列表获取测试失败: " + e.getMessage());
            }
        } else {
            System.out.println("LibraryCheckService 未注入，跳过测试");
        }
    }

    @Test
    public void testReadPbSessionData() {
        if (dataReadService != null) {
            try {
                JSONObject result = dataReadService.readPbSessionData("/tmp");
                System.out.println("PB会话数据读取测试通过");
                System.out.println("结果: " + result.getString("status"));
            } catch (Exception e) {
                System.out.println("PB会话数据读取测试失败: " + e.getMessage());
            }
        } else {
            System.out.println("DataReadService 未注入，跳过测试");
        }
    }

    @Test
    public void testJavaServicesIntegration() {
        System.out.println("=== Java服务集成测试 ===");
        
        boolean allServicesAvailable = true;
        
        if (networkService == null) {
            System.out.println("❌ NetworkService 未可用");
            allServicesAvailable = false;
        } else {
            System.out.println("✅ NetworkService 可用");
        }
        
        if (libraryCheckService == null) {
            System.out.println("❌ LibraryCheckService 未可用");
            allServicesAvailable = false;
        } else {
            System.out.println("✅ LibraryCheckService 可用");
        }
        
        if (dataReadService == null) {
            System.out.println("❌ DataReadService 未可用");
            allServicesAvailable = false;
        } else {
            System.out.println("✅ DataReadService 可用");
        }
        
        if (allServicesAvailable) {
            System.out.println("🎉 所有Java服务都已成功替代Python脚本！");
        } else {
            System.out.println("⚠️ 部分服务未可用，请检查配置");
        }
    }
}

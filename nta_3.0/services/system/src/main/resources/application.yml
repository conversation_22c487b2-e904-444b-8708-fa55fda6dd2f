server:
  port: 8089
  servlet:
    context-path: /api/system

spring:
  application:
    name: system-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common
  # 数据源配置 - 统一使用PostgreSQL + Druid
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:push_database}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}

    # Druid 连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

      # 监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}

mybatis-flex:
  type-aliases-package: com.geeksec.nta.system.domain.model
  configuration:
    map-underscore-to-camel-case: true

# 系统管理配置
system-management:
  # 命令执行超时时间（秒）
  command-timeout: 30
  # NTP配置
  ntp:
    # NTP配置文件路径
    config-file: /etc/ntp.conf
    # NTP配置备份路径
    backup-file: /etc/ntp.conf.backup
    # NTP服务名称
    service-name: ntpd
  # 系统权限配置
  privileges:
    # 是否使用sudo执行系统命令
    use-sudo: true
    # sudo命令路径
    sudo-path: /usr/bin/sudo

# 日志配置
logging:
  level:
    com.geeksec.nta.system: INFO
    root: WARN

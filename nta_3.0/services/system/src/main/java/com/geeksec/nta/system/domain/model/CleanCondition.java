package com.geeksec.nta.system.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 清理数据条件
 * 
 * <AUTHOR>
 */
@Data
public class CleanCondition {

    @JsonProperty("user_id")
    private Integer userId;

    @JsonProperty("password")
    private String password;

    @JsonProperty("task_id")
    private Integer taskId;

    @JsonProperty("clean_list")
    private List<String> cleanList;
}

package com.geeksec.nta.system.interfaces.rest;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.common.controller.BaseController;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.nta.system.domain.service.DiskManagementService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 磁盘管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "磁盘管理接口", description = "磁盘管理接口相关操作")
@RequestMapping("/raid")
public class DiskController extends BaseController {

    @Autowired
    private DiskManagementService diskManagementService;

    /**
     * 获取RAID卡磁盘信息
     *
     * @return RAID信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取RAID卡磁盘信息", description = "获取RAID卡磁盘信息操作")
    public JSONObject getRaidInfo() {
        return diskManagementService.getRaidInfo();
    }
}

package com.geeksec.nta.system.domain.service;

import com.alibaba.fastjson2.JSONObject;

/**
 * 磁盘管理服务接口
 *
 * 注意：磁盘监控功能（使用率、IO统计等）已在 monitor 模块中实现
 * 本服务专注于磁盘管理操作
 *
 * <AUTHOR>
 */
public interface DiskManagementService {

    /**
     * 获取RAID配置信息
     *
     * @return RAID信息
     */
    JSONObject getRaidInfo();

    /**
     * 获取可管理的磁盘列表
     *
     * @return 磁盘列表
     */
    JSONObject getManageableDisks();

    /**
     * 磁盘重组操作
     *
     * @return 操作结果
     */
    JSONObject rebuildDisk();

    /**
     * 磁盘更新操作
     *
     * @return 操作结果
     */
    JSONObject updateDisk();

    /**
     * 准备挂载磁盘
     *
     * @return 操作结果
     */
    JSONObject prepareMount();

    /**
     * 挂载数据磁盘
     *
     * @return 操作结果
     */
    JSONObject mountDataDisk();

    /**
     * 格式化磁盘
     *
     * @param devicePath 设备路径
     * @param fileSystem 文件系统类型
     * @return 操作结果
     */
    JSONObject formatDisk(String devicePath, String fileSystem);

    /**
     * 挂载磁盘
     *
     * @param devicePath 设备路径
     * @param mountPoint 挂载点
     * @return 操作结果
     */
    JSONObject mountDisk(String devicePath, String mountPoint);

    /**
     * 卸载磁盘
     *
     * @param mountPoint 挂载点
     * @return 操作结果
     */
    JSONObject unmountDisk(String mountPoint);
}

package com.geeksec.nta.system.infrastructure.external;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

/**
 * 命令执行器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommandExecutor {

    private static final int DEFAULT_TIMEOUT_SECONDS = 30;

    /**
     * 执行系统命令
     *
     * @param command 命令
     * @return 执行结果
     * @throws IOException 执行异常
     */
    public String executeCommand(String command) throws IOException {
        return executeCommand(command, DEFAULT_TIMEOUT_SECONDS);
    }

    /**
     * 执行系统命令（带超时）
     *
     * @param command 命令
     * @param timeoutSeconds 超时时间（秒）
     * @return 执行结果
     * @throws IOException 执行异常
     */
    public String executeCommand(String command, int timeoutSeconds) throws IOException {
        log.info("执行命令: {}", command);

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command("bash", "-c", command);
        processBuilder.redirectErrorStream(true);

        try {
            Process process = processBuilder.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待进程完成
            boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);
            if (!finished) {
                process.destroyForcibly();
                throw new IOException("命令执行超时: " + command);
            }

            int exitCode = process.exitValue();
            String result = output.toString().trim();

            log.info("命令执行完成，退出码: {}, 输出: {}", exitCode, result);

            if (exitCode != 0) {
                throw new IOException("命令执行失败，退出码: " + exitCode + ", 输出: " + result);
            }

            return result;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IOException("命令执行被中断: " + command, e);
        }
    }

    /**
     * 执行系统命令（异步）
     *
     * @param command 命令
     * @return 进程对象
     * @throws IOException 执行异常
     */
    public Process executeCommandAsync(String command) throws IOException {
        log.info("异步执行命令: {}", command);

        ProcessBuilder processBuilder = new ProcessBuilder();
        processBuilder.command("bash", "-c", command);
        processBuilder.redirectErrorStream(true);

        return processBuilder.start();
    }

    /**
     * 检查命令是否存在
     *
     * @param command 命令名称
     * @return 是否存在
     */
    public boolean isCommandAvailable(String command) {
        try {
            String checkCommand = "which " + command;
            executeCommand(checkCommand, 5);
            return true;
        } catch (IOException e) {
            log.debug("命令不存在: {}", command);
            return false;
        }
    }

    /**
     * 执行Python脚本
     *
     * @param scriptPath 脚本路径
     * @param args 参数
     * @return 执行结果
     * @throws IOException 执行异常
     */
    public String executePythonScript(String scriptPath, String... args) throws IOException {
        StringBuilder command = new StringBuilder();
        command.append("python3 ").append(scriptPath);
        
        for (String arg : args) {
            command.append(" ").append(arg);
        }

        return executeCommand(command.toString());
    }

    /**
     * 执行Shell脚本
     *
     * @param scriptPath 脚本路径
     * @param args 参数
     * @return 执行结果
     * @throws IOException 执行异常
     */
    public String executeShellScript(String scriptPath, String... args) throws IOException {
        StringBuilder command = new StringBuilder();
        command.append("bash ").append(scriptPath);
        
        for (String arg : args) {
            command.append(" ").append(arg);
        }

        return executeCommand(command.toString());
    }
}

package com.geeksec.nta.system.application.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.geeksec.nta.system.domain.model.NetworkConfig;
import com.geeksec.nta.system.domain.service.NetworkService;
import com.geeksec.nta.system.infrastructure.external.CommandExecutor;
import com.geeksec.nta.system.infrastructure.system.NetworkConfigManager;
import com.geeksec.nta.system.infrastructure.system.NtpConfigManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 网络管理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NetworkServiceImpl implements NetworkService {

    private static final String CONFIG_FILE_PATH = "/opt/GeekSec/config/network.json";
    private static final String DEFAULT_DEVICE = "eno1";

    private final CommandExecutor commandExecutor;
    private final NetworkConfigManager networkConfigManager;
    private final NtpConfigManager ntpConfigManager;

    @Override
    public JSONObject modifyIpConfig(NetworkConfig config) {
        log.info("修改IP配置: {}", config);

        try {
            // 保存当前配置
            saveNetworkConfig(config);

            // 使用Java实现配置网络
            boolean success = networkConfigManager.configureNetworkInterface(
                config.getDevice(),
                config.getIp(),
                config.getPrefix(),
                config.getGateway(),
                config.getDns()
            );

            if (success) {
                return createSuccessResponse("IP配置修改成功");
            } else {
                return createErrorResponse("IP配置修改失败");
            }

        } catch (Exception e) {
            log.error("修改IP配置失败: {}", e.getMessage());
            return createErrorResponse("修改IP配置失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject setNtpServer(String ntpServer) {
        log.info("设置NTP服务器: {}", ntpServer);

        try {
            // 更新配置文件中的NTP设置
            NetworkConfig config = getNetworkConfig();
            config.setNtp(ntpServer);
            saveNetworkConfig(config);

            // 使用Java实现配置NTP
            boolean success = ntpConfigManager.configureNtpServer(ntpServer);

            if (success) {
                return createSuccessResponse("NTP服务器设置成功");
            } else {
                return createErrorResponse("NTP服务器设置失败");
            }

        } catch (Exception e) {
            log.error("设置NTP服务器失败: {}", e.getMessage());
            return createErrorResponse("设置NTP服务器失败: " + e.getMessage());
        }
    }

    @Override
    public NetworkConfig getNetworkConfig() {
        try {
            Path configPath = Paths.get(CONFIG_FILE_PATH);
            if (Files.exists(configPath)) {
                String content = Files.readString(configPath);
                return JSON.parseObject(content, NetworkConfig.class);
            } else {
                // 如果配置文件不存在，初始化默认配置
                return initializeDefaultConfig();
            }
        } catch (Exception e) {
            log.error("读取网络配置失败: {}", e.getMessage());
            return initializeDefaultConfig();
        }
    }

    @Override
    public JSONObject getNetworkDeviceInfo(String deviceName) {
        log.info("获取网络设备信息: {}", deviceName);

        try {
            NetworkInterface networkInterface = NetworkInterface.getByName(deviceName);
            if (networkInterface == null) {
                return createErrorResponse("网络设备不存在: " + deviceName);
            }

            JSONObject deviceInfo = new JSONObject();

            // 获取IP地址信息
            Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
            List<String> ipAddresses = new ArrayList<>();
            while (addresses.hasMoreElements()) {
                InetAddress address = addresses.nextElement();
                if (!address.isLoopbackAddress() && address.getAddress().length == 4) {
                    ipAddresses.add(address.getHostAddress());
                }
            }

            if (!ipAddresses.isEmpty()) {
                deviceInfo.put("addr", ipAddresses.get(0));
            }

            // 使用Java API获取网关信息
            String gateway = getDefaultGateway(deviceName);
            if (gateway != null && !gateway.isEmpty()) {
                deviceInfo.put("gateway", gateway);
            }

            // 使用Java API获取子网掩码信息
            String netmask = getNetmask(networkInterface);
            if (netmask != null && !netmask.isEmpty()) {
                deviceInfo.put("netmask", netmask);
            }

            // 获取MAC地址
            byte[] mac = networkInterface.getHardwareAddress();
            if (mac != null) {
                StringBuilder macAddress = new StringBuilder();
                for (int i = 0; i < mac.length; i++) {
                    macAddress.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? ":" : ""));
                }
                deviceInfo.put("mac", macAddress.toString());
            }

            // 获取MTU
            deviceInfo.put("mtu", networkInterface.getMTU());

            return createSuccessResponse(deviceInfo);

        } catch (SocketException e) {
            log.error("获取网络设备信息失败: {}", e.getMessage());
            return createErrorResponse("获取网络设备信息失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject getAllNetworkInterfaces() {
        log.info("获取所有网络接口信息");

        try {
            List<JSONObject> interfaces = new ArrayList<>();
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();

            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                
                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }

                JSONObject interfaceInfo = new JSONObject();
                interfaceInfo.put("name", networkInterface.getName());
                interfaceInfo.put("displayName", networkInterface.getDisplayName());

                // 获取IP地址
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                List<String> ipAddresses = new ArrayList<>();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (!address.isLoopbackAddress()) {
                        ipAddresses.add(address.getHostAddress());
                    }
                }
                interfaceInfo.put("addresses", ipAddresses);

                interfaces.add(interfaceInfo);
            }

            return createSuccessResponse(interfaces);

        } catch (SocketException e) {
            log.error("获取网络接口信息失败: {}", e.getMessage());
            return createErrorResponse("获取网络接口信息失败: " + e.getMessage());
        }
    }



    /**
     * 保存网络配置到文件
     */
    private void saveNetworkConfig(NetworkConfig config) throws IOException {
        Path configPath = Paths.get(CONFIG_FILE_PATH);
        Files.createDirectories(configPath.getParent());
        Files.writeString(configPath, JSON.toJSONString(config));
    }

    /**
     * 初始化默认配置
     */
    private NetworkConfig initializeDefaultConfig() {
        NetworkConfig config = new NetworkConfig();
        config.setDevice(DEFAULT_DEVICE);
        config.setPrefix("*************");
        config.setNtp("");

        try {
            // 尝试从系统获取当前IP配置
            String ipCommand = "ip addr show " + DEFAULT_DEVICE + " | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1";
            String currentIp = commandExecutor.executeCommand(ipCommand).trim();
            if (!currentIp.isEmpty()) {
                config.setIp(currentIp);
            }

            // 获取当前网关
            String gatewayCommand = "ip route | grep default | awk '{print $3}'";
            String currentGateway = commandExecutor.executeCommand(gatewayCommand).trim();
            if (!currentGateway.isEmpty()) {
                config.setGateway(currentGateway);
            }

            // 获取当前DNS
            String dnsCommand = "cat /etc/resolv.conf | grep nameserver | head -1 | awk '{print $2}'";
            String currentDns = commandExecutor.executeCommand(dnsCommand).trim();
            if (!currentDns.isEmpty()) {
                config.setDns(currentDns);
            }

        } catch (Exception e) {
            log.warn("获取当前网络配置失败: {}", e.getMessage());
        }

        return config;
    }

    /**
     * 创建成功响应
     */
    private JSONObject createSuccessResponse(Object data) {
        JSONObject response = new JSONObject();
        response.put("result_code", "1");
        response.put("result_desc", "Success");
        response.put("data", data);
        return response;
    }

    /**
     * 创建错误响应
     */
    private JSONObject createErrorResponse(String message) {
        JSONObject response = new JSONObject();
        response.put("result_code", "0");
        response.put("result_desc", message);
        return response;
    }

    /**
     * 获取默认网关（纯Java实现）
     */
    private String getDefaultGateway(String deviceName) {
        try {
            // 读取路由表文件
            Path routeFile = Paths.get("/proc/net/route");
            if (!Files.exists(routeFile)) {
                return null;
            }

            List<String> lines = Files.readAllLines(routeFile);
            for (String line : lines) {
                String[] parts = line.split("\\s+");
                if (parts.length >= 8 && parts[0].equals(deviceName)) {
                    // 检查是否为默认路由（目标为00000000）
                    if ("00000000".equals(parts[1])) {
                        // 解析网关地址（十六进制转IP）
                        String gatewayHex = parts[2];
                        return hexToIp(gatewayHex);
                    }
                }
            }
        } catch (IOException e) {
            log.warn("读取路由表失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取子网掩码（纯Java实现）
     */
    private String getNetmask(NetworkInterface networkInterface) {
        try {
            Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                InetAddress address = addresses.nextElement();
                if (!address.isLoopbackAddress() && address.getAddress().length == 4) {
                    // 获取网络前缀长度
                    short prefixLength = networkInterface.getInterfaceAddresses().get(0).getNetworkPrefixLength();
                    return prefixLengthToNetmask(prefixLength);
                }
            }
        } catch (Exception e) {
            log.warn("获取子网掩码失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 十六进制字符串转IP地址
     */
    private String hexToIp(String hex) {
        if (hex.length() != 8) {
            return null;
        }

        try {
            // 按字节解析（注意字节序）
            int a = Integer.parseInt(hex.substring(6, 8), 16);
            int b = Integer.parseInt(hex.substring(4, 6), 16);
            int c = Integer.parseInt(hex.substring(2, 4), 16);
            int d = Integer.parseInt(hex.substring(0, 2), 16);

            return String.format("%d.%d.%d.%d", a, b, c, d);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 前缀长度转子网掩码
     */
    private String prefixLengthToNetmask(int prefixLength) {
        if (prefixLength < 0 || prefixLength > 32) {
            return "*************"; // 默认值
        }

        int mask = 0xFFFFFFFF << (32 - prefixLength);

        int a = (mask >>> 24) & 0xFF;
        int b = (mask >>> 16) & 0xFF;
        int c = (mask >>> 8) & 0xFF;
        int d = mask & 0xFF;

        return String.format("%d.%d.%d.%d", a, b, c, d);
    }

    /**
     * 验证IP地址格式
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        Pattern pattern = Pattern.compile(ipPattern);
        return pattern.matcher(ip).matches();
    }

    /**
     * 检查网络连通性
     */
    private boolean isNetworkReachable(String host, int timeout) {
        try {
            InetAddress address = InetAddress.getByName(host);
            return address.isReachable(timeout);
        } catch (IOException e) {
            return false;
        }
    }
}

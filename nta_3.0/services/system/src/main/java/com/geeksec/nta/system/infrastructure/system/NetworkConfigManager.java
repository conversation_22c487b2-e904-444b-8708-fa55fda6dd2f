package com.geeksec.nta.system.infrastructure.system;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 网络配置管理器 - Java实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class NetworkConfigManager {

    private static final int COMMAND_TIMEOUT_SECONDS = 30;

    @Value("${system-management.privileges.use-sudo:true}")
    private boolean useSudo;

    @Value("${system-management.privileges.sudo-path:/usr/bin/sudo}")
    private String sudoPath;

    @Value("${system-management.command-timeout:30}")
    private int commandTimeout;

    /**
     * 配置网络接口
     */
    public boolean configureNetworkInterface(String interfaceName, String ipAddress, 
                                           String netmask, String gateway, String dns) {
        try {
            // 1. 验证参数
            if (!validateNetworkParameters(interfaceName, ipAddress, netmask)) {
                return false;
            }

            // 2. 检查网络接口是否存在
            if (!checkNetworkInterfaceExists(interfaceName)) {
                log.error("网络接口不存在: {}", interfaceName);
                return false;
            }

            // 3. 计算CIDR格式的IP地址
            String cidrAddress = convertToCidr(ipAddress, netmask);
            if (cidrAddress == null) {
                return false;
            }

            // 4. 检查连接是否存在，不存在则创建
            if (!checkConnectionExists(interfaceName)) {
                if (!createConnection(interfaceName)) {
                    return false;
                }
            }

            // 5. 配置IP地址
            if (!setIpAddress(interfaceName, cidrAddress)) {
                return false;
            }

            // 6. 配置网关（如果提供）
            if (gateway != null && !gateway.isEmpty()) {
                if (!setGateway(interfaceName, gateway)) {
                    return false;
                }
            }

            // 7. 配置DNS（如果提供）
            if (dns != null && !dns.isEmpty()) {
                if (!setDns(interfaceName, dns)) {
                    return false;
                }
            }

            // 8. 设置为手动配置模式
            if (!setManualMode(interfaceName)) {
                return false;
            }

            // 9. 启用自动连接
            if (!setAutoConnect(interfaceName)) {
                return false;
            }

            // 10. 激活连接
            if (!activateConnection(interfaceName)) {
                return false;
            }

            log.info("网络接口配置成功: {}", interfaceName);
            return true;

        } catch (Exception e) {
            log.error("配置网络接口失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 验证网络参数
     */
    private boolean validateNetworkParameters(String interfaceName, String ipAddress, String netmask) {
        // 验证接口名
        if (interfaceName == null || interfaceName.trim().isEmpty()) {
            log.error("网络接口名不能为空");
            return false;
        }

        // 验证IP地址格式
        if (!isValidIpAddress(ipAddress)) {
            log.error("无效的IP地址: {}", ipAddress);
            return false;
        }

        // 验证子网掩码格式
        if (!isValidNetmask(netmask)) {
            log.error("无效的子网掩码: {}", netmask);
            return false;
        }

        return true;
    }

    /**
     * 检查网络接口是否存在
     */
    private boolean checkNetworkInterfaceExists(String interfaceName) {
        try {
            List<String> command = buildCommand("nmcli", "dev", "show", interfaceName);
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            boolean finished = process.waitFor(commandTimeout, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                return false;
            }

            return process.exitValue() == 0;
        } catch (Exception e) {
            log.error("检查网络接口失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查连接是否存在
     */
    private boolean checkConnectionExists(String connectionName) {
        try {
            ProcessBuilder pb = new ProcessBuilder("nmcli", "con", "show", connectionName);
            Process process = pb.start();
            boolean finished = process.waitFor(COMMAND_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            
            return process.exitValue() == 0;
        } catch (Exception e) {
            log.error("检查连接失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 创建网络连接
     */
    private boolean createConnection(String interfaceName) {
        try {
            ProcessBuilder pb = new ProcessBuilder("nmcli", "con", "add", 
                "con-name", interfaceName, 
                "ifname", interfaceName, 
                "type", "ethernet");
            
            Process process = pb.start();
            boolean finished = process.waitFor(COMMAND_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            
            if (process.exitValue() != 0) {
                logProcessError(process, "创建连接失败");
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("创建连接异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 设置IP地址
     */
    private boolean setIpAddress(String connectionName, String cidrAddress) {
        return executeNmcliCommand("设置IP地址", "nmcli", "con", "mod", connectionName, "ipv4.addr", cidrAddress);
    }

    /**
     * 设置网关
     */
    private boolean setGateway(String connectionName, String gateway) {
        return executeNmcliCommand("设置网关", "nmcli", "con", "mod", connectionName, "ipv4.gateway", gateway);
    }

    /**
     * 设置DNS
     */
    private boolean setDns(String connectionName, String dns) {
        return executeNmcliCommand("设置DNS", "nmcli", "con", "mod", connectionName, "ipv4.dns", dns);
    }

    /**
     * 设置手动模式
     */
    private boolean setManualMode(String connectionName) {
        return executeNmcliCommand("设置手动模式", "nmcli", "con", "mod", connectionName, "ipv4.method", "manual");
    }

    /**
     * 设置自动连接
     */
    private boolean setAutoConnect(String connectionName) {
        return executeNmcliCommand("设置自动连接", "nmcli", "con", "mod", connectionName, "connection.autoconnect", "on");
    }

    /**
     * 激活连接
     */
    private boolean activateConnection(String connectionName) {
        return executeNmcliCommand("激活连接", "nmcli", "con", "up", connectionName);
    }

    /**
     * 执行nmcli命令的通用方法
     */
    private boolean executeNmcliCommand(String operation, String... command) {
        try {
            List<String> fullCommand = buildCommand(command);
            ProcessBuilder pb = new ProcessBuilder(fullCommand);
            Process process = pb.start();
            boolean finished = process.waitFor(commandTimeout, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                log.error("{}超时", operation);
                return false;
            }

            if (process.exitValue() != 0) {
                logProcessError(process, operation + "失败");
                return false;
            }

            log.debug("{}成功", operation);
            return true;

        } catch (Exception e) {
            log.error("{}异常: {}", operation, e.getMessage());
            return false;
        }
    }

    /**
     * 转换为CIDR格式
     */
    private String convertToCidr(String ipAddress, String netmask) {
        try {
            // 使用ipcalc计算前缀长度
            ProcessBuilder pb = new ProcessBuilder("ipcalc", "-p", ipAddress, netmask);
            Process process = pb.start();
            
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line = reader.readLine();
                if (line != null && line.contains("=")) {
                    String prefix = line.split("=")[1].trim();
                    return ipAddress + "/" + prefix;
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("转换CIDR格式失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证IP地址格式
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return ip.matches(ipPattern);
    }

    /**
     * 验证子网掩码格式
     */
    private boolean isValidNetmask(String netmask) {
        return isValidIpAddress(netmask);
    }

    /**
     * 构建命令（支持sudo）
     */
    private List<String> buildCommand(String... command) {
        List<String> fullCommand = new ArrayList<>();

        if (useSudo) {
            fullCommand.add(sudoPath);
        }

        fullCommand.addAll(List.of(command));
        return fullCommand;
    }

    /**
     * 记录进程错误信息
     */
    private void logProcessError(Process process, String operation) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
            StringBuilder error = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                error.append(line).append("\n");
            }
            log.error("{}: {}", operation, error.toString());
        } catch (IOException e) {
            log.error("读取错误信息失败: {}", e.getMessage());
        }
    }
}

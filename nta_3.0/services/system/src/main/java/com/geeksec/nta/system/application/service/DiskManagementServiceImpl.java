package com.geeksec.nta.system.application.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.geeksec.nta.system.domain.service.DiskManagementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;

/**
 * 磁盘管理服务实现类
 *
 * 注意：磁盘监控功能已在 monitor 模块中实现，本服务专注于磁盘管理操作
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DiskManagementServiceImpl implements DiskManagementService {

    @Override
    public JSONObject getManageableDisks() {
        log.info("获取可管理的磁盘列表");

        try {
            JSONObject result = new JSONObject();
            JSONArray disks = new JSONArray();

            // 获取可管理的磁盘设备
            Path devPath = Paths.get("/dev");
            if (Files.exists(devPath)) {
                try (Stream<Path> files = Files.list(devPath)) {
                    files.filter(p -> {
                        String name = p.getFileName().toString();
                        return name.matches("^(sd[a-z]|hd[a-z]|nvme[0-9]+n[0-9]+)$");
                    }).forEach(p -> {
                        JSONObject diskInfo = new JSONObject();
                        diskInfo.put("device", p.toString());
                        diskInfo.put("name", p.getFileName().toString());
                        diskInfo.put("manageable", true);
                        disks.add(diskInfo);
                    });
                }
            }

            result.put("status", "success");
            result.put("manageable_disks", disks);
            result.put("timestamp", System.currentTimeMillis());

            return result;

        } catch (Exception e) {
            log.error("获取可管理磁盘列表失败: {}", e.getMessage());
            return createErrorResponse("获取可管理磁盘列表失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject getRaidInfo() {
        log.info("获取RAID配置信息");

        try {
            JSONObject result = new JSONObject();

            // 检查是否存在RAID配置文件
            Path mdstatPath = Paths.get("/proc/mdstat");
            if (Files.exists(mdstatPath)) {
                List<String> lines = Files.readAllLines(mdstatPath);
                JSONArray raidDevices = new JSONArray();

                for (String line : lines) {
                    if (line.startsWith("md")) {
                        JSONObject raidDevice = parseMdstatLine(line);
                        if (raidDevice != null) {
                            raidDevices.add(raidDevice);
                        }
                    }
                }

                result.put("raid_devices", raidDevices);
            } else {
                result.put("message", "未检测到软RAID配置");
            }

            result.put("status", "success");
            result.put("timestamp", System.currentTimeMillis());

            return result;

        } catch (Exception e) {
            log.error("获取RAID信息失败: {}", e.getMessage());
            return createErrorResponse("获取RAID信息失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject rebuildDisk() {
        log.info("执行磁盘重组操作");

        try {
            JSONObject result = new JSONObject();
            result.put("status", "info");
            result.put("message", "磁盘重组操作需要系统管理员权限");
            result.put("note", "请通过系统管理界面执行磁盘重组操作");
            result.put("timestamp", System.currentTimeMillis());

            return result;

        } catch (Exception e) {
            log.error("磁盘重组操作失败: {}", e.getMessage());
            return createErrorResponse("磁盘重组操作失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject updateDisk() {
        log.info("执行磁盘更新操作");

        try {
            JSONObject result = new JSONObject();
            result.put("status", "info");
            result.put("message", "磁盘更新操作需要系统管理员权限");
            result.put("note", "请通过系统管理界面执行磁盘更新操作");
            result.put("timestamp", System.currentTimeMillis());

            return result;

        } catch (Exception e) {
            log.error("磁盘更新操作失败: {}", e.getMessage());
            return createErrorResponse("磁盘更新操作失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject prepareMount() {
        log.info("准备挂载磁盘操作");

        try {
            JSONObject result = new JSONObject();
            result.put("status", "info");
            result.put("message", "准备挂载操作需要系统管理员权限");
            result.put("note", "请通过系统管理界面执行准备挂载操作");
            result.put("timestamp", System.currentTimeMillis());

            return result;

        } catch (Exception e) {
            log.error("准备挂载操作失败: {}", e.getMessage());
            return createErrorResponse("准备挂载操作失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject mountDataDisk() {
        log.info("挂载数据磁盘操作");

        try {
            JSONObject result = new JSONObject();
            result.put("status", "info");
            result.put("message", "挂载数据磁盘操作需要系统管理员权限");
            result.put("note", "请通过系统管理界面执行挂载操作");
            result.put("timestamp", System.currentTimeMillis());

            return result;

        } catch (Exception e) {
            log.error("挂载数据磁盘操作失败: {}", e.getMessage());
            return createErrorResponse("挂载数据磁盘操作失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject formatDisk(String devicePath, String fileSystem) {
        log.info("格式化磁盘: {} 为 {}", devicePath, fileSystem);

        // 安全检查：这是一个危险操作，需要严格验证
        if (!isValidDevicePath(devicePath)) {
            return createErrorResponse("无效的设备路径: " + devicePath);
        }

        if (!isSupportedFileSystem(fileSystem)) {
            return createErrorResponse("不支持的文件系统: " + fileSystem);
        }

        try {
            JSONObject result = new JSONObject();
            result.put("status", "warning");
            result.put("message", "格式化操作需要管理员权限，请通过系统管理界面执行");
            result.put("device", devicePath);
            result.put("filesystem", fileSystem);

            return result;

        } catch (Exception e) {
            log.error("格式化磁盘失败: {}", e.getMessage());
            return createErrorResponse("格式化磁盘失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject mountDisk(String devicePath, String mountPoint) {
        log.info("挂载磁盘: {} 到 {}", devicePath, mountPoint);

        try {
            // 检查挂载点是否存在
            Path mountPath = Paths.get(mountPoint);
            if (!Files.exists(mountPath)) {
                try {
                    Files.createDirectories(mountPath);
                } catch (IOException e) {
                    return createErrorResponse("无法创建挂载点: " + mountPoint);
                }
            }

            // 检查设备是否存在
            Path deviceFile = Paths.get(devicePath);
            if (!Files.exists(deviceFile)) {
                return createErrorResponse("设备不存在: " + devicePath);
            }

            JSONObject result = new JSONObject();
            result.put("status", "success");
            result.put("message", "挂载操作已准备就绪");
            result.put("device", devicePath);
            result.put("mount_point", mountPoint);
            result.put("note", "实际挂载需要系统权限，请通过系统管理界面执行");

            return result;

        } catch (Exception e) {
            log.error("挂载磁盘失败: {}", e.getMessage());
            return createErrorResponse("挂载磁盘失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject unmountDisk(String mountPoint) {
        log.info("卸载磁盘: {}", mountPoint);

        try {
            Path mountPath = Paths.get(mountPoint);
            if (!Files.exists(mountPath)) {
                return createErrorResponse("挂载点不存在: " + mountPoint);
            }

            JSONObject result = new JSONObject();
            result.put("status", "success");
            result.put("message", "卸载操作已准备就绪");
            result.put("mount_point", mountPoint);
            result.put("note", "实际卸载需要系统权限，请通过系统管理界面执行");

            return result;

        } catch (Exception e) {
            log.error("卸载磁盘失败: {}", e.getMessage());
            return createErrorResponse("卸载磁盘失败: " + e.getMessage());
        }
    }

    /**
     * 解析mdstat行信息
     */
    private JSONObject parseMdstatLine(String line) {
        try {
            String[] parts = line.split("\\s+");
            if (parts.length >= 4) {
                JSONObject raidInfo = new JSONObject();
                raidInfo.put("device", parts[0]);
                raidInfo.put("status", parts[1]);
                raidInfo.put("type", parts[2]);
                raidInfo.put("devices", parts[3]);
                return raidInfo;
            }
        } catch (Exception e) {
            log.warn("解析RAID信息失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 验证设备路径
     */
    private boolean isValidDevicePath(String devicePath) {
        if (devicePath == null || devicePath.isEmpty()) {
            return false;
        }
        // 只允许标准的设备路径
        return devicePath.matches("^/dev/(sd[a-z][0-9]*|hd[a-z][0-9]*|nvme[0-9]+n[0-9]+p?[0-9]*)$");
    }

    /**
     * 检查是否为支持的文件系统
     */
    private boolean isSupportedFileSystem(String fileSystem) {
        String[] supportedFs = {"ext4", "ext3", "ext2", "xfs", "btrfs", "ntfs", "vfat"};
        for (String fs : supportedFs) {
            if (fs.equals(fileSystem)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 创建错误响应
     */
    private JSONObject createErrorResponse(String message) {
        JSONObject response = new JSONObject();
        response.put("status", "error");
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}

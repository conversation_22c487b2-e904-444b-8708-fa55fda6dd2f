package com.geeksec.nta.system.domain.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 网络配置实体
 *
 * <AUTHOR>
 */
@Data
public class NetworkConfig {

    /**
     * 网络设备名称
     */
    @JsonProperty("DEV")
    private String device = "eno1";

    /**
     * IP地址
     */
    @JsonProperty("IP")
    private String ip;

    /**
     * 子网掩码
     */
    @JsonProperty("PREFIX")
    private String prefix = "*************";

    /**
     * 网关
     */
    @JsonProperty("GATEWAY")
    private String gateway;

    /**
     * DNS服务器
     */
    @JsonProperty("DNS")
    private String dns;

    /**
     * NTP服务器
     */
    @JsonProperty("NTP")
    private String ntp;

    /**
     * 操作类型
     */
    @JsonProperty("type")
    private String type;

    /**
     * NTP服务器地址（用于设置NTP）
     */
    @JsonProperty("ntp_server")
    private String ntpServer;
}

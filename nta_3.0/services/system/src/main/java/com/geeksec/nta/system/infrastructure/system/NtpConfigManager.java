package com.geeksec.nta.system.infrastructure.system;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * NTP配置管理器 - Java实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class NtpConfigManager {

    @Value("${system-management.ntp.config-file:/etc/ntp.conf}")
    private String ntpConfPath;

    @Value("${system-management.ntp.backup-file:/etc/ntp.conf.backup}")
    private String ntpConfBackup;

    @Value("${system-management.ntp.service-name:ntpd}")
    private String ntpServiceName;

    @Value("${system-management.privileges.use-sudo:true}")
    private boolean useSudo;

    @Value("${system-management.privileges.sudo-path:/usr/bin/sudo}")
    private String sudoPath;

    @Value("${system-management.command-timeout:30}")
    private int commandTimeout;

    /**
     * 配置NTP服务器
     */
    public boolean configureNtpServer(String ntpServerIp) {
        try {
            // 1. 验证NTP服务器IP
            if (!isValidIpAddress(ntpServerIp)) {
                log.error("无效的NTP服务器IP: {}", ntpServerIp);
                return false;
            }

            // 2. 备份当前配置
            if (!backupNtpConfig()) {
                return false;
            }

            // 3. 停止NTP服务
            if (!stopNtpService()) {
                return false;
            }

            // 4. 生成新的NTP配置
            if (!generateNtpConfig(ntpServerIp)) {
                return false;
            }

            // 5. 启动NTP服务
            if (!startNtpService()) {
                // 如果启动失败，尝试恢复配置
                restoreNtpConfig();
                return false;
            }

            // 6. 执行时间同步
            if (!syncTime(ntpServerIp)) {
                log.warn("时间同步失败，但NTP服务已配置");
            }

            log.info("NTP服务器配置成功: {}", ntpServerIp);
            return true;

        } catch (Exception e) {
            log.error("配置NTP服务器失败: {}", e.getMessage(), e);
            // 尝试恢复配置
            restoreNtpConfig();
            return false;
        }
    }

    /**
     * 备份NTP配置文件
     */
    private boolean backupNtpConfig() {
        try {
            Path configPath = Paths.get(ntpConfPath);
            Path backupPath = Paths.get(ntpConfBackup);

            if (Files.exists(configPath)) {
                Files.copy(configPath, backupPath, StandardCopyOption.REPLACE_EXISTING);
                log.debug("NTP配置文件备份成功");
                return true;
            } else {
                log.warn("NTP配置文件不存在: {}", ntpConfPath);
                return true; // 文件不存在也算成功，后续会创建新文件
            }
        } catch (IOException e) {
            log.error("备份NTP配置文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 恢复NTP配置文件
     */
    private boolean restoreNtpConfig() {
        try {
            Path backupPath = Paths.get(NTP_CONF_BACKUP);
            Path ntpConfPath = Paths.get(NTP_CONF_PATH);
            
            if (Files.exists(backupPath)) {
                Files.copy(backupPath, ntpConfPath, StandardCopyOption.REPLACE_EXISTING);
                log.info("NTP配置文件恢复成功");
                return true;
            } else {
                log.warn("备份文件不存在，无法恢复");
                return false;
            }
        } catch (IOException e) {
            log.error("恢复NTP配置文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成NTP配置文件
     */
    private boolean generateNtpConfig(String ntpServerIp) {
        try {
            String ntpConfig = generateNtpConfigContent(ntpServerIp);
            Path ntpConfPath = Paths.get(NTP_CONF_PATH);
            
            Files.write(ntpConfPath, ntpConfig.getBytes(), 
                StandardOpenOption.CREATE, 
                StandardOpenOption.TRUNCATE_EXISTING);
            
            log.debug("NTP配置文件生成成功");
            return true;
            
        } catch (IOException e) {
            log.error("生成NTP配置文件失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 生成NTP配置内容
     */
    private String generateNtpConfigContent(String ntpServerIp) {
        return String.format("""
            # NTP configuration file generated by Java service
            # Generated at: %s
            
            driftfile /var/lib/ntp/drift
            
            # Permit time synchronization with our time source, but do not
            # permit the source to query or modify the service on this system.
            restrict default nomodify notrap nopeer noquery
            
            # Permit all access over the loopback interface.
            restrict 127.0.0.1 
            restrict ::1
            
            # NTP server configuration
            server %s iburst
            
            # Enable public key cryptography.
            #crypto
            
            includefile /etc/ntp/crypto/pw
            
            # Key file containing the keys and key identifiers used when operating
            # with symmetric key cryptography. 
            keys /etc/ntp/keys
            
            # Specify the key identifiers which are trusted.
            #trustedkey 4 8 42
            
            # Specify the key identifier to use with the ntpdc utility.
            #requestkey 8
            
            # Specify the key identifier to use with the ntpq utility.
            #controlkey 8
            
            # Enable writing of statistics records.
            #statistics clockstats cryptostats loopstats peerstats
            """, 
            java.time.LocalDateTime.now().toString(),
            ntpServerIp);
    }

    /**
     * 停止NTP服务
     */
    private boolean stopNtpService() {
        return executeSystemctlCommand("stop", "ntpd", "停止NTP服务");
    }

    /**
     * 启动NTP服务
     */
    private boolean startNtpService() {
        return executeSystemctlCommand("start", "ntpd", "启动NTP服务");
    }

    /**
     * 重启NTP服务
     */
    private boolean restartNtpService() {
        return executeSystemctlCommand("restart", "ntpd", "重启NTP服务");
    }

    /**
     * 执行systemctl命令
     */
    private boolean executeSystemctlCommand(String action, String service, String operation) {
        try {
            List<String> command = buildCommand("systemctl", action, service);
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            boolean finished = process.waitFor(commandTimeout, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                log.error("{}超时", operation);
                return false;
            }

            if (process.exitValue() != 0) {
                logProcessError(process, operation + "失败");
                return false;
            }

            log.debug("{}成功", operation);
            return true;

        } catch (Exception e) {
            log.error("{}异常: {}", operation, e.getMessage());
            return false;
        }
    }

    /**
     * 执行时间同步
     */
    private boolean syncTime(String ntpServerIp) {
        try {
            ProcessBuilder pb = new ProcessBuilder("ntpdate", "-u", ntpServerIp);
            Process process = pb.start();
            boolean finished = process.waitFor(COMMAND_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                log.error("时间同步超时");
                return false;
            }
            
            if (process.exitValue() != 0) {
                logProcessError(process, "时间同步失败");
                return false;
            }
            
            log.debug("时间同步成功");
            return true;
            
        } catch (Exception e) {
            log.error("时间同步异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证IP地址格式
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String ipPattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        return ip.matches(ipPattern);
    }

    /**
     * 构建命令（支持sudo）
     */
    private List<String> buildCommand(String... command) {
        List<String> fullCommand = new ArrayList<>();

        if (useSudo) {
            fullCommand.add(sudoPath);
        }

        fullCommand.addAll(List.of(command));
        return fullCommand;
    }

    /**
     * 记录进程错误信息
     */
    private void logProcessError(Process process, String operation) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
            StringBuilder error = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                error.append(line).append("\n");
            }
            log.error("{}: {}", operation, error.toString());
        } catch (IOException e) {
            log.error("读取错误信息失败: {}", e.getMessage());
        }
    }

    /**
     * 检查NTP服务状态
     */
    public boolean isNtpServiceRunning() {
        try {
            ProcessBuilder pb = new ProcessBuilder("systemctl", "is-active", "ntpd");
            Process process = pb.start();
            boolean finished = process.waitFor(COMMAND_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            
            if (!finished) {
                process.destroyForcibly();
                return false;
            }
            
            return process.exitValue() == 0;
            
        } catch (Exception e) {
            log.error("检查NTP服务状态异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前NTP服务器配置
     */
    public String getCurrentNtpServer() {
        try {
            Path ntpConfPath = Paths.get(NTP_CONF_PATH);
            if (!Files.exists(ntpConfPath)) {
                return null;
            }
            
            List<String> lines = Files.readAllLines(ntpConfPath);
            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("server ") && !line.contains("127.0.0.1")) {
                    String[] parts = line.split("\\s+");
                    if (parts.length >= 2) {
                        return parts[1];
                    }
                }
            }
            
            return null;
            
        } catch (IOException e) {
            log.error("读取NTP配置失败: {}", e.getMessage());
            return null;
        }
    }
}

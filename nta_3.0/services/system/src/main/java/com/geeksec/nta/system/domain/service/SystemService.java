package com.geeksec.nta.system.domain.service;

import com.alibaba.fastjson2.JSONObject;
import com.geeksec.nta.system.domain.model.CleanCondition;
import com.geeksec.nta.system.domain.model.ProductInfoVo;
import com.geeksec.nta.system.domain.model.SystemInfoVo;

/**
 * 系统管理服务接口
 * 
 * <AUTHOR>
 */
public interface SystemService {

    /**
     * 关闭主机
     *
     * @return 执行状态
     */
    JSONObject shutdown();

    /**
     * 重启主机
     *
     * @return 执行状态
     */
    JSONObject reboot();

    /**
     * 修改密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 执行状态
     */
    JSONObject changePassword(String userName, String password);

    /**
     * 获取磁盘使用情况
     *
     * @return 磁盘信息
     */
    JSONObject getDiskInfoData();

    /**
     * 系统信息获取
     *
     * @return 系统信息
     */
    SystemInfoVo getSystemInfo();

    /**
     * 产品信息获取
     *
     * @return 产品信息
     */
    ProductInfoVo getProductInfo();

    /**
     * 数据清理
     *
     * @param condition 条件
     * @return 是否成功
     */
    JSONObject cleanData(CleanCondition condition);

    /**
     * 系统重置
     *
     * @param json 重置参数
     * @return 执行结果
     */
    JSONObject systemReset(JSONObject json);

    /**
     * 数据清理进度
     *
     * @return 清理进度
     */
    JSONObject cleanDataSchedule();

    /**
     * 更新磁盘
     *
     * @return 执行结果
     */
    JSONObject diskChange();

    /**
     * 重组磁盘
     *
     * @return 执行结果
     */
    JSONObject diskRebuild();

    /**
     * 准备挂载磁盘
     *
     * @return 执行结果
     */
    JSONObject diskMountReady();

    /**
     * 挂载磁盘
     *
     * @return 执行结果
     */
    JSONObject diskMountData();

    /**
     * 动态库文件检测
     *
     * @param ruleId 规则ID
     * @return 检测结果
     */
    JSONObject checkSo(Integer ruleId);

    /**
     * docker 动态库文件检测
     *
     * @param path 地址
     * @return 检测结果
     */
    JSONObject dockerCheckSo(String path);

    /**
     * 查询磁盘是否处于重组状态
     *
     * @return 磁盘状态
     */
    JSONObject checkDiskStatus();

    /**
     * 获取侧拉框展示字段
     * 
     * @return 磁盘字段信息
     */
    JSONObject getDiskField();
}

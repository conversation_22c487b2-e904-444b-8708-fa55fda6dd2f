package com.geeksec.nta.system.infrastructure.repository;

import com.geeksec.nta.system.domain.model.ProductInfoVo;
import com.geeksec.nta.system.domain.model.SystemInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 系统信息数据访问接口
 *
 * 使用 MyBatis-Flex 注解方式实现
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Mapper
public interface SystemInfoDao {

    /**
     * 获取系统信息
     *
     * @return 系统信息
     */
    @Select("""
            select si.hostname, si.osinfo, si.timeS, st.time
            from (select hostname, osinfo, timeS from sys_info order by id desc limit 1) as si
                     join
                     (select `time` from tb_system_time order by id desc limit 1) as st
            """)
    SystemInfoVo getSystemInfo();

    /**
     * 获取产品信息
     *
     * @return 产品信息
     */
    @Select("""
            select product, version, SN, privileged_time
            from product_info
            order by id desc
            limit 1
            """)
    ProductInfoVo getProductInfo();

    /**
     * 修改机器关机/重启操作值
     *
     * @param value 操作值：关机为0，重启为1
     */
    @Update("""
            update tb_valset
            set val = #{value}
            where name = 'shutdown'
            """)
    void modifyShutdownValue(@Param("value") Integer value);
}

package com.geeksec.monitor.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 系统指标实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IndexData {

    /**
     * 本地磁盘空间使用占比
     */
    private Object localDiskSpaceUsageProportion;

    /**
     * 本地磁盘空间总量TB
     */
    private Object totalLocalDiskSpace;

    /**
     * 本地磁盘空间余量TB
     */
    private Object emptyLocalDiskSpace;

    /**
     * 本地磁盘空间inode
     */
    private Object localDiskSpaceINode;

    /**
     * 本地磁盘空间I/O使用率
     */
    private Object localDiskSpaceIOUtilizationRate;

    /**
     * CPU使用占比
     */
    private Object cpuUsageRate;

    /**
     * 处理器
     */
    private Object processor;

    /**
     * 核心
     */
    private Object numberOfProcessorCores;

    /**
     * CPU负载平均值
     */
    private Object averageCpuLoad;

    /**
     * 内存使用占比
     */
    private Object memoryUsageRate;

    /**
     * 内存总量GB
     */
    private Object totalMemory;

    /**
     * 内存余量GB
     */
    private Object emptyMemory;

    /**
     * 缓存
     */
    private Object cache;

    /**
     * 缓冲
     */
    private Object buffer;

    /**
     * 当前进程信息
     */
    private List<CurrentProcessInformation> currentProcessInformationList;



    /**
     * 赋值
     * @param index
     * @param data
     */
    public void setMetric(String index, Object data) {
        try {
            Field field = this.getClass().getDeclaredField(toCamelCase(index));
            field.setAccessible(true);
            field.set(this, data);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }


    /**
     * 字段转换
     * @param underscoreStr
     * @return
     */
    public static String toCamelCase(String underscoreStr) {
        StringBuilder camelCaseStr = new StringBuilder();
        boolean nextUpperCase = false;
        for (char c : underscoreStr.toCharArray()) {
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    camelCaseStr.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    camelCaseStr.append(Character.toLowerCase(c));
                }
            }
        }
        return camelCaseStr.toString();
    }



}

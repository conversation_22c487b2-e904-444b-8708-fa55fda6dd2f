package com.geeksec.monitor.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum IndexEnum {

    //本地磁盘空间
    LOCAL_DISK_SPACE_USAGE_PROPORTION("本地磁盘空间使用占比","local_disk_space_usage_proportion", "(1 - (node_filesystem_free_bytes{mountpoint=\"/data\"} / node_filesystem_size_bytes{mountpoint=\"/data\"})) * 100"),//使用占比
    TOTAL_LOCAL_DISK_SPACE("本地磁盘空间总量TB","total_local_disk_space", "node_filesystem_size_bytes{mountpoint=\"/data\"}"),//总量TB
    EMPTY_LOCAL_DISK_SPACE("本地磁盘空间余量TB","empty_local_disk_space", "node_filesystem_free_bytes{mountpoint=\"/data\"}"),//余量TB
    LOCAL_DISK_SPACE_I_NODE("本地磁盘空间inode","local_disk_space_i_node", "(1 - node_filesystem_files_free{mountpoint=\"/data\"} / node_filesystem_files{mountpoint=\"/data\"}) * 100"),//inode
    LOCAL_DISK_SPACE_I_O_UTILIZATION_RATE("本地磁盘空间I/O使用率","local_disk_space_I_O_utilization_rate", "rate(node_disk_io_time_seconds_total{device!~\"data.*\"}[1m])/60*100"),//I/O使用率

    //CPU
    CPU_USAGE_RATE("CPU使用占比","cpu_usage_rate", "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[1m])) * 100)"),//CPU使用占比
    PROCESSOR("处理器","processor","node_uname_info"),//处理器
    NUMBER_OF_PROCESSOR_CORES("核心","number_of_processor_cores","count by (instance) (count by (instance, cpu) (node_cpu_seconds_total{mode=\"idle\"}))"),//核心
    AVERAGE_CPU_LOAD("CPU负载平均值","average_cpu_load","node_load1"),//CPU负载平均值

    //内存
    MEMORY_USAGE_RATE("内存使用占比","memory_usage_rate","(node_memory_MemTotal_bytes - node_memory_MemFree_bytes) / node_memory_MemTotal_bytes * 100"),//使用占比
    TOTAL_MEMORY("内存总量","total_memory","node_memory_MemTotal_bytes"),//总量GB
    EMPTY_MEMORY("内存余量","empty_memory","node_memory_MemFree_bytes"),//余量GB
    CACHE("缓存","cache","node_memory_Cached_bytes"),//缓存
    BUFFER("缓冲","buffer","node_memory_Buffers_bytes"),//缓冲

    //系统相关--数据库查询  /system/info
    //产品相关--数据库查询 /system/info

    //当前进程信息

    //探针--tips:用kafka接入
    //主任务-进程
    THD10_PROCESS_START_TIME("thd10-进程开始时间","thd10_process_start_time",""),//进程开始时间
    THD10_CPU_USAGE_TIME("thd10-CPU占用时间","thd10_cpu_usage_time",""),//CPU占用时间
    THD10_MEMORY_USAGE("thd10-内存占用","thd10_memory_usage",""),//内存占用
    THD10_PROCESS_NAME("thd10-进程名称","thd10_process_name",""),//进程名称-数据处理
    //从任务-进程
    THD11_PROCESS_START_TIME("thd11-进程开始时间","thd11_process_start_time",""),//进程开始时间
    THD11_CPU_USAGE_TIME("thd11-CPU占用时间","thd11_cpu_usage_time",""),//CPU占用时间
    THD11_MEMORY_USAGE("thd11-内存占用","thd11_memory_usage",""),//内存占用
    THD11_PROCESS_NAME("thd11-进程名称","thd11_process_name",""),//进程名称-数据处理

    //数据处理flink-容器
    FLINK_PROCESS_START_TIME("flink-进程开始时间","flink_process_start_time","container_start_time_seconds{name=~\"flink.*jobmanager.*\"}"),//进程开始时间
    FLINK_CPU_USAGE_TIME("flink-CPU占用时间","flink_cpu_usage_time","sum(container_cpu_usage_seconds_total{name=~\"flink.*manager.*\"})"),//CPU占用时间
    FLINK_MEMORY_USAGE("flink-内存占用","flink_memory_usage","sum(container_memory_usage_bytes{name=~\"flink.*manager.*\"})"),//内存占用
    FLINK_PROCESS_NAME("flink-进程名称","flink_process_name",""),//进程名称-数据处理

    //数据转发nginx-进程
    /*NGINX_PROCESS_START_TIME("nginx-进程开始时间","nginx_process_start_time","process_start_time_seconds{job=\"nginx\"}\n"),//进程开始时间
    NGINX_PROCESS_CPU_USAGE_TIME("nginx-CPU占用时间","nginx_process_cpu_usage_time","process_cpu_seconds_total{job=\"nginx\"}"),//CPU占用时间
    NGINX_MEMORY_USAGE("nginx-内存占用","nginx_memory_usage","process_resident_memory_bytes{job=\"nginx\"}"),//内存占用
    NGINX_PROCESS_NAME("nginx-进程名称","nginx_process_name",""),//进程名称-数据转发*/

    //数据转发nginx-容器
    NGINX_PROCESS_START_TIME("nginx-进程开始时间","nginx_process_start_time","container_start_time_seconds{name=~\".*nginx-gateway.*\"}"),//进程开始时间
    NGINX_PROCESS_CPU_USAGE_TIME("nginx-CPU占用时间","nginx_process_cpu_usage_time","container_cpu_usage_seconds_total{name=~\".*nginx-gateway.*\"}"),//CPU占用时间
    NGINX_MEMORY_USAGE("nginx-内存占用","nginx_memory_usage","container_memory_usage_bytes{name=~\".*nginx-gateway.*\"}"),//内存占用
    NGINX_PROCESS_NAME("nginx-进程名称","nginx_process_name",""),//进程名称-数据转发

    //索引储存elasticsearch-进程
    /*ELASTICSEARCH_PROCESS_START_TIME("elasticsearch-进程开始时间","elasticsearch_process_start_time","process_start_time_seconds{job=\"elasticsearch\"}\n"),//进程开始时间
    ELASTICSEARCH_PROCESS_CPU_USAGE_TIME("elasticsearch-CPU占用时间","elasticsearch_process_cpu_usage_time","process_cpu_seconds_total{job=\"elasticsearch\"}"),//CPU占用时间
    ELASTICSEARCH_MEMORY_USAGE("elasticsearch-内存占用","elasticsearch_memory_usage","process_resident_memory_bytes{job=\"elasticsearch\"}"),//内存占用
    ELASTICSEARCH_PROCESS_NAME("elasticsearch-进程名称","elasticsearch_process_name",""),//进程名称-索引储存*/

    //索引储存elasticsearch-容器
    ELASTICSEARCH_PROCESS_START_TIME("elasticsearch-进程开始时间","elasticsearch_process_start_time","container_start_time_seconds{name=~\".*es_elasticsearch.*\"}"),//进程开始时间
    ELASTICSEARCH_PROCESS_CPU_USAGE_TIME("elasticsearch-CPU占用时间","elasticsearch_process_cpu_usage_time","container_cpu_usage_seconds_total{name=~\".*es_elasticsearch.*\"}"),//CPU占用时间
    ELASTICSEARCH_MEMORY_USAGE("elasticsearch-内存占用","elasticsearch_memory_usage","container_memory_usage_bytes{name=~\".*es_elasticsearch.*\"}"),//内存占用
    ELASTICSEARCH_PROCESS_NAME("elasticsearch-进程名称","elasticsearch_process_name",""),//进程名称-索引储存

    //结构化储存PostgreSQL-进程
    /*POSTGRESQL_PROCESS_START_TIME("postgresql-进程开始时间","postgresql_process_start_time","process_start_time_seconds{job=\"postgresql\"}\n"),//进程开始时间
    POSTGRESQL_PROCESS_CPU_USAGE_TIME("postgresql-CPU占用时间","postgresql_process_cpu_usage_time","process_cpu_seconds_total{job=\"postgresql\"}"),//CPU占用时间
    POSTGRESQL_MEMORY_USAGE("postgresql-内存占用","postgresql_memory_usage","process_resident_memory_bytes{job=\"postgresql\"}"),//内存占用
    POSTGRESQL_PROCESS_NAME("postgresql-进程名称","postgresql_process_name","");//进程名称-结构化储存*/

    //结构化储存PostgreSQL-容器
    POSTGRESQL_PROCESS_START_TIME("postgresql-进程开始时间","postgresql_process_start_time","container_start_time_seconds{name=~\".*postgresql_postgresql.*\"}"),//进程开始时间
    POSTGRESQL_PROCESS_CPU_USAGE_TIME("postgresql-CPU占用时间","postgresql_process_cpu_usage_time","container_cpu_usage_seconds_total{name=~\".*postgresql_postgresql.*\"}"),//CPU占用时间
    POSTGRESQL_MEMORY_USAGE("postgresql-内存占用","postgresql_memory_usage","container_memory_usage_bytes{name=~\".*postgresql_postgresql.*\"}"),//内存占用
    POSTGRESQL_PROCESS_NAME("postgresql-进程名称","postgresql_process_name","");//进程名称-结构化储存


    private final String name;
    private final String index;
    private final String promQL;

    IndexEnum(String name, String index, String promQL) {
        this.name = name;
        this.index = index;
        this.promQL = promQL;
    }

    // 根据指标名称获取枚举常量
    public static IndexEnum fromName(String name) {
        for (IndexEnum index : IndexEnum.values()) {
            if (index.getName().equalsIgnoreCase(name)) {
                return index;
            }
        }
        throw new IllegalArgumentException("Invalid name: " + name);
    }

    // 根据promQL获取枚举常量
    public static IndexEnum fromValue(String promQL) {
        for (IndexEnum index : IndexEnum.values()) {
            if (Objects.equals(index.getPromQL(),promQL)) {
                return index;
            }
        }
        throw new IllegalArgumentException("Invalid promQL: " + promQL);
    }

    @Override
    public String toString() {
        return name + " (" + promQL + ")";
    }
}

# Monitor Service 测试环境配置
# 继承通用测试环境配置

spring:
  profiles:
    active: test
    include: common

# 服务器配置
server:
  port: ${SERVER_PORT:8086}
  servlet:
    context-path: /api/monitor

# 监控服务特定配置
monitor:
  # 测试环境数据采集配置
  collection:
    # 系统指标采集
    system:
      enabled: true
      interval: 15  # 15秒采集一次
      metrics: [cpu, memory, disk, network, io]
    
    # 应用指标采集
    application:
      enabled: true
      interval: 10  # 10秒采集一次
      metrics: [jvm, gc, threads, connections, cache]
    
    # 业务指标采集
    business:
      enabled: true
      interval: 30  # 30秒采集一次
      metrics: [requests, errors, latency, throughput]
  
  # 测试环境存储配置
  storage:
    # 时序数据库配置
    timeseries:
      type: influxdb
      url: ${INFLUXDB_URL:http://test-influxdb.nta.local:8086}
      database: ${INFLUXDB_DB:nta_monitor_test}
      username: ${INFLUXDB_USER:nta_test}
      password: ${INFLUXDB_PASSWORD:nta_test123}
    
    # 数据保留策略
    retention:
      raw-data-days: 30  # 原始数据保留30天
      aggregated-data-days: 90  # 聚合数据保留90天
  
  # 测试环境告警配置
  alerting:
    enabled: true
    # 告警规则
    rules:
      - name: high-cpu
        condition: cpu_usage > 75
        duration: 180  # 持续3分钟
        severity: warning
      - name: high-memory
        condition: memory_usage > 80
        duration: 180
        severity: warning
      - name: critical-cpu
        condition: cpu_usage > 90
        duration: 60  # 持续1分钟
        severity: critical
    
    # 通知配置
    notifications:
      email:
        enabled: true
        smtp-host: ${SMTP_HOST:test-smtp.nta.local}
        recipients: [<EMAIL>]
      webhook:
        enabled: true
        url: ${ALERT_WEBHOOK_URL:http://test-webhook.nta.local:3000/alerts}
  
  # 测试环境仪表板配置
  dashboard:
    enabled: true
    refresh-interval: 15  # 15秒刷新
    default-time-range: 6h  # 默认显示6小时数据
  
  # 测试环境性能配置
  performance:
    enable-metrics-aggregation: true
    aggregation-interval: 300  # 5分钟聚合一次
    enable-data-compression: true

# 日志配置
logging:
  level:
    '[com.geeksec.monitor]': INFO

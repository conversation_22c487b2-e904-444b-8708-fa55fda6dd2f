server:
  port: 8086
  servlet:
    context-path: /api/monitor

spring:
  application:
    name: monitor-service

  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common

  cloud:
    # 启用Kubernetes服务发现
    kubernetes:
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        namespace: ${KUBERNETES_NAMESPACE:nta}
      config:
        enabled: true
        sources:
          - name: ${spring.application.name}-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
          - name: common-config
            namespace: ${KUBERNETES_NAMESPACE:nta}
    config:
      import:
        - "kubernetes:"
        - "classpath:application-kubernetes.yml"

  # Kafka配置 - 使用环境变量
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092}
    consumer:
      group-id: monitor-group
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false

  # Redis配置 - 使用环境变量
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# Prometheus配置 - 使用环境变量
prometheus:
  url: ${PROMETHEUS_URL:http://localhost:9090/}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics

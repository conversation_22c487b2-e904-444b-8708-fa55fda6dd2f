package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.MacDao;
import com.geeksec.graph.service.MacService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Mac服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class MacServiceImpl implements MacService {

    @Autowired
    private MacDao macDao;

    /**
     * Mac关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getMacNebulaAssociationNext(GraphNextInfoCondition condition) {
        return macDao.listMacAllEdgeTypeAssociationNext(condition);
    }
}

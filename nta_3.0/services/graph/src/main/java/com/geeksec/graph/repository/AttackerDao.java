package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.AttackerVertex;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AttackerDao extends NebulaDaoBasic<AttackerVertex, String> {

    /**
     * 查询attack所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listAttackAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}

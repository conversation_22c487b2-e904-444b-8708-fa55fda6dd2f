package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.condition.GraphPropertiesNextCondition;
import com.geeksec.graph.pojo.vertex.DomainVertex;
import com.geeksec.graph.vo.*;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface DomainDao extends NebulaDaoBasic<DomainVertex, String> {

    /**
     * 查询域名所有边类型关联数据
     */
    List<VertexEdgeVo> listDomainAllEdgeTypeAssociation(@Param("domain") String domain);

    /**
     * 查询域名所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listDomainAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    /**
     * 域名属性关联查询Next
     */
    List<VertexEdgeNextVo> listDomainNebulaNextByProperties(@Param("condition") GraphPropertiesNextCondition condition);

    /**
     * 根据ids查询存在域名属性
     */
    List<ExistDomainPropertiesVo> listExistDomainProperties(@Param("domains") List<String> domains);

    /**
     * 根据ids查询域名详情
     */
    List<DomainVo> listByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询标签
     */
    List<DomainHasLabelVo> listHasLabelByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询锚域名
     */
    List<FDomainVo> listFDomainByDomains(@Param("domains") List<String> domains);

    /**
     * 根据id查询标签
     */
    DomainHasLabelVo getHasLabelByDomain(@Param("domain") String domain);

    /**
     * 根据id查询兄弟域名数量
     */
    DomainCountVo countBrotherNumByDomain(@Param("domain") String domain);

    /**
     * 根据ids查询反查ip
     */
    List<DomainRelatedIpsVo> listRelatedIpsByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询CName该域名数量
     */
    List<DomainCountVo> countCnameDomainsByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询CName指向该域名数量
     */
    List<DomainCountVo> countCnamePointDomainsByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询请求该域名的ips
     */
    List<DomainRelatedIpsVo> listRequestDomainIpsByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询回应类型具有cname的边
     */
    List<DomainRelatedIpsVo> listResponseTypeIpsByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询dns_server_domain出现位置
     */
    List<DomainCountVo> countDnsServerDomainByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询server_ssl_connect_domain出现位置
     */
    List<DomainCountVo> countServerSslConnectDomainByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询server_http_connect_domain出现位置
     */
    List<DomainCountVo> countServerHttpConnectDomainByDomains(@Param("domains") List<String> domains);

    /**
     * 根据ids查询sni_bind出现位置
     */
    List<DomainCountVo> countSniBindByDomains(@Param("domains") List<String> domains);

    /**
     * 查询域名详情
     */
    Map<String, Object> getDomainInfo(@Param("domain") String domain);

    /**
     * 根据id查询标签
     */
    List<Long> listHasLabelByDomain(@Param("domain") String domain);

    /**
     * 根据id查询锚域名
     */
    String getFDomainByDomain(@Param("domain") String domain);

    /**
     * 根据fid查询标签
     */
    List<Long> listHasLabelByFDomain(@Param("domain") String domain);

    /**
     * 根据id查询cname_result指向IP数量
     */
    Integer countCnameResultByDomain(@Param("domain") String domain);

    /**
     * 根据id查询parse_to指向IP数量
     */
    Integer countParseToByDomain(@Param("domain") String domain);

    /**
     * 根据id查询客户端热度
     */
    DomainRelatedIpsVo listRelatedClientIpsByDomain(@Param("domain") String domain);

    /**
     * 根据id查询关联证书数量
     */
    Integer countCertNumByDomain(@Param("domain") String domain);

}

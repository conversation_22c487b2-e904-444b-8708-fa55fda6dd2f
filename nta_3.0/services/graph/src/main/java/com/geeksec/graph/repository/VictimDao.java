package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.VictimVertex;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface VictimDao extends NebulaDaoBasic<VictimVertex, String> {

    /**
     * 查询victim所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listVictimAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}

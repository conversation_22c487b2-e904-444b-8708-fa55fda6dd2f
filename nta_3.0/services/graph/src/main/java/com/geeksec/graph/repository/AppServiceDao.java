package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.AppServiceVertex;
import com.geeksec.graph.vo.AppServiceVo;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AppServiceDao extends NebulaDaoBasic<AppServiceVertex, String> {

    /**
     * 查询AppService所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listAppServiceAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

    AppServiceVo getAppService(@Param("serviceKey") String serviceKey);

}

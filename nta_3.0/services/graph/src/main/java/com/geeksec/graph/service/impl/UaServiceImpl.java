package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.UaDao;
import com.geeksec.graph.service.UaService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Ua服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class UaServiceImpl implements UaService {

    @Autowired
    private UaDao uaDao;

    /**
     * Ua关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getUaNebulaAssociationNext(GraphNextInfoCondition condition) {
        return uaDao.listUaAllEdgeTypeAssociationNext(condition);
    }
}

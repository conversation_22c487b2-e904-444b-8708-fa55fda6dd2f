package com.geeksec.graph.pojo;

// Copyright (c) 2022 All project authors. All rights reserved.
//
// This source code is licensed under Apache 2.0 License.


import com.geeksec.graph.annotations.ValueType;
import lombok.Data;

import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Person的实体类示例</p>
 * <AUTHOR>
 * @since 2022-06-10 22:10
 * <br>Now is history!
 */
@Table(name = "person")
@Data
public class Person {

  @Id
  private String name;

  private String gender;
  
  @ValueType(Double.class)
  private BigDecimal height;

  private Integer age;

  private Date birthday;

  @Override
  public String toString() {
    return "Person{"
      + "name='" + name
      + '\''
      + ", age="
      + age
      + '}';
  }
}

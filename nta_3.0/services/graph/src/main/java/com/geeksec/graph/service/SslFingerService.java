package com.geeksec.graph.service;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.vo.*;

import java.util.List;

/**
*@description: SSL指纹服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface SslFingerService {

    /**
     * 根据ids查询SSL指纹描述
     */
    List<SslFingerDescVo> listFingerDescribeByFingers(List<String> fingers);

    /**
     * 根据ids查询SSL指纹标签
     */
    List<SslFingerLabelVo> listFingerLabelByFingers(List<String> fingers);

    /**
     * 根据ids查询SSL指纹服务端IP热度
     */
    List<SslFingerRelatedIpsVo> listServerRelatedIpsByFingers(List<String> fingers);

    /**
     * 根据ids查询SSL指纹客户端IP热度
     */
    List<SslFingerRelatedIpsVo> listClientRelatedIpsByFingers(List<String> fingers);

    /**
     * 根据ids查询关联证书数量
     */
    List<SslFingerCountVo> countCertByFingers(List<String> fingers);

    /**
     * SslFinger关联查询Next
     */
    List<VertexEdgeNextVo> getSslFingerNebulaAssociationNext(GraphNextInfoCondition condition);

}

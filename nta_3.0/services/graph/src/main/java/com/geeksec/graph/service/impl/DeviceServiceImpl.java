package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.DeviceDao;
import com.geeksec.graph.service.DeviceService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Device服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class DeviceServiceImpl implements DeviceService {

    @Autowired
    private DeviceDao deviceDao;

    /**
     * Device关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getDeviceNebulaAssociationNext(GraphNextInfoCondition condition) {
        return deviceDao.listDeviceAllEdgeTypeAssociationNext(condition);
    }
}

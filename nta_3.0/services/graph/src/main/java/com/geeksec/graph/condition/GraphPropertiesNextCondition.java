package com.geeksec.graph.condition;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @author: qiuwen
 * @date: 2022/8/8
 * @Description:
 **/
@Data
public class GraphPropertiesNextCondition {
    /**
     * 点名称
     */
    @JsonProperty("tag_name")
    private String tagName;

    /**
     * 属性名称
     */
    @JsonProperty("properties_name")
    private String propertiesName;

    /**
     * 属性值
     */
    @JsonProperty("properties_value")
    private String propertiesValue;

}

package com.geeksec.graph.service;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.condition.GraphPropertiesNextCondition;
import com.geeksec.graph.condition.SubGraphNextCondition;
import com.geeksec.graph.vo.VertexAssociationNextVo;
import com.geeksec.graph.vo.VertexAssociationVo;

/**
*@description: 点服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface GraphService {

    /**
    * 点关联查询
    */
    VertexAssociationVo getAssociation(String str, String type);

    /**
     * 点关联查询Next
     */
    VertexAssociationNextVo getAssociationNext(GraphNextInfoCondition condition);

    /**
     * 子图遍历点关联查询Next
     */
    VertexAssociationNextVo getSubAssociationNext(SubGraphNextCondition condition);


    /**
     * 属性关联查询Next
     */
    VertexAssociationNextVo getEdgePropertiesNext(GraphPropertiesNextCondition condition);

}

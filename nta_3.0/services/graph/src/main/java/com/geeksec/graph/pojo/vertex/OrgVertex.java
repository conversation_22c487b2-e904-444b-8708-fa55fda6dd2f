package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 企业名
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "ORG")
@Data
public class OrgVertex {

  /**
  * 企业名
  */
  @Id
  @Column(name = "org_name")
  private String orgName;

  /**
  * 企业说明
  */
  @Column(name = "org_desc")
  private String orgDesc;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

  /**
  * 备注
  */
  private String remark;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 锚域名
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "FDOMAIN")
@Data
public class FdomainVertex {

  /**
   * 域名地址
   */
  @Id
  @Column(name = "fdomain_addr")
  private String fdomainAddr;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

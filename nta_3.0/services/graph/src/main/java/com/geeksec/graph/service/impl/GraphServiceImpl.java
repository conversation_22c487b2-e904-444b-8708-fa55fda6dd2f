package com.geeksec.graph.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.geeksec.graph.constants.VertexTypeConstants;
import com.geeksec.graph.service.GraphService;
import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.condition.GraphPropertiesNextCondition;
import com.geeksec.graph.condition.SubGraphNextCondition;
import com.geeksec.graph.repository.AllAtlasDao;
import com.geeksec.graph.service.*;
import com.geeksec.graph.vo.*;
import com.google.common.collect.Lists;
import com.vesoft.nebula.client.graph.data.Node;
import com.vesoft.nebula.client.graph.data.Relationship;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
*@description: 点服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
@RequiredArgsConstructor
public class GraphServiceImpl implements GraphService {

    private final IpService ipService;
    private final DomainService domainService;
    private final CertService certService;
    private final MacService macService;
    private final AppServiceService appServiceService;
    private final SslFingerService sslFingerService;
    private final UaService uaService;
    private final DeviceService deviceService;
    private final OsService osService;
    private final IssuerService issuerService;
    private final SubjectService subjectService;
    private final AppService appService;
    private final FdomainService fdomainService;
    private final OrgService orgService;
    private final AttackerService attackerService;
    private final VictimService victimService;
    private final UrlService urlService;
    private final AllAtlasDao allAtlasDao;
    private static List<String> DISPLAY_LABEL_EDGE = Lists.newArrayList("domain_belong_to_org", "ip_belong_to_org", "cert_belong_to_org", "fDomain_belong_to_org"
            ,"special_business_port_service","issuer_related","subject_related");
    private static List<String> DISPLAY_LABEL_VERTEX = Lists.newArrayList("SUBJECT", "ISSUER", "ORG");

    /**
     * 点关联查询
     */
    @Override
    public VertexAssociationVo getAssociation(String param, String type) {
        VertexAssociationVo vertexAssociationVo = new VertexAssociationVo();
        List<VertexEdgeVo> vertexEdgeVoList = new ArrayList<>();
            switch (type) {
            case VertexTypeConstants.IP:
                vertexEdgeVoList = ipService.getIpNebulaAssociation(param);
                break;
            case VertexTypeConstants.DOMAIN:
                vertexEdgeVoList = domainService.getDomainNebulaAssociation(param);
                break;
            case VertexTypeConstants.CERT:
                vertexEdgeVoList = certService.getCertNebulaAssociation(param);
                break;
        }
        //根据关联数据组装所有关联点和边
        this.setVertexEdgeList(vertexAssociationVo,vertexEdgeVoList);
        return vertexAssociationVo;
    }

    @Override
    public VertexAssociationNextVo getAssociationNext(GraphNextInfoCondition condition) {
        VertexAssociationNextVo vertexAssociationNextVo = new VertexAssociationNextVo();
        List<VertexEdgeNextVo> vertexEdgeNextVoList = new ArrayList<>();
        switch (condition.getType()) {
            case VertexTypeConstants.IP:
                vertexEdgeNextVoList = ipService.getIpNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.MAC:
                vertexEdgeNextVoList = macService.getMacNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.DOMAIN:
                vertexEdgeNextVoList = domainService.getDomainNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.APPSERVICE:
                vertexEdgeNextVoList = appServiceService.getAppServiceNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.SSLFINGER:
                vertexEdgeNextVoList = sslFingerService.getSslFingerNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.CERT:
                vertexEdgeNextVoList = certService.getCertNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.ORG:
                vertexEdgeNextVoList = orgService.getOrgNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.APP:
                vertexEdgeNextVoList = appService.getAppNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.FDOMAIN:
                vertexEdgeNextVoList = fdomainService.getFdomainNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.UA:
                vertexEdgeNextVoList = uaService.getUaNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.DEVICE:
                vertexEdgeNextVoList = deviceService.getDeviceNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.OS:
                vertexEdgeNextVoList = osService.getOsNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.ISSUER:
                vertexEdgeNextVoList = issuerService.getIssuerNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.SUBJECT:
                vertexEdgeNextVoList = subjectService.getSubjectNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.ATTACKER:
                vertexEdgeNextVoList = attackerService.getAttackNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.VICTIM:
                vertexEdgeNextVoList = victimService.getVictimNebulaAssociationNext(condition);
                break;
            case VertexTypeConstants.URL:
                vertexEdgeNextVoList = urlService.getUrlNebulaAssociationNext(condition);
                break;
        }
        //根据关联数据组装所有关联点和边
        this.setVertexEdgeNextList(vertexAssociationNextVo,vertexEdgeNextVoList);
        return vertexAssociationNextVo;
    }

    @Override
    public VertexAssociationNextVo getSubAssociationNext(SubGraphNextCondition condition) {
        VertexAssociationNextVo vertexAssociationNextVo = new VertexAssociationNextVo();
        Set<VertexAssociationVertexNextVo> vertexList = new LinkedHashSet<>();
        Set<VertexAssociationEdgeVo> edgeList = new LinkedHashSet<>();
        Set<String> vidList = new LinkedHashSet<>();
        try {
            ResultSet list = allAtlasDao.vidSubSearch(condition);
            List<ValueWrapper> nodesValueWrapperList = list.colValues("nodes");
            for(ValueWrapper nodesValueWrapper : nodesValueWrapperList) {
                List<ValueWrapper> nodeValueWrapperList = nodesValueWrapper.asList();
                for(ValueWrapper nodeValueWrapper : nodeValueWrapperList){
                    Node node = nodeValueWrapper.asNode();
                    if(CollectionUtil.isEmpty(node.tagNames())){
                        continue;
                    }
                    vidList.add(node.getId().asString());
                }
            }
            if(CollectionUtil.isEmpty(vidList)){
                vertexAssociationNextVo.setVertex(vertexList);
                vertexAssociationNextVo.setEdge(edgeList);
                return vertexAssociationNextVo;
            }
            List<Map<String, Object>> vidInfoList = allAtlasDao.fetchOnTagInfoByVids(new ArrayList<>(vidList));
            for (Map<String, Object> vidInfoMap : vidInfoList) {
                Map<String, Object> vertexInfoMap = (Map<String, Object>) vidInfoMap.get("vertex_info");
                VertexAssociationVertexNextVo vertexAssociationVertexNextVo = new VertexAssociationVertexNextVo();
                String vid = (String) vidInfoMap.get("vertex_id");
                String tagType = (String) vidInfoMap.get("tag_type");
                vertexAssociationVertexNextVo.setType(tagType);
                vertexAssociationVertexNextVo.setLv(0L);
                vertexAssociationVertexNextVo.setLabel(vid);
                switch (tagType) {
                    case VertexTypeConstants.ORG:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("org_name"));
                        break;
                    case VertexTypeConstants.APPSERVICE:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("AppName"));
                        break;
                    case VertexTypeConstants.ISSUER:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("common_name"));
                        break;
                    case VertexTypeConstants.SUBJECT:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("common_name"));
                        break;
                }
                vertexAssociationVertexNextVo.setId(vid);
                vertexAssociationVertexNextVo.setVInfo(vertexInfoMap.entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                e -> e.getValue() != null ? e.getValue().toString() : ""
                        )));
                vertexList.add(vertexAssociationVertexNextVo);
            }
            List<ValueWrapper> relationshipsValueWrapperList = list.colValues("relationships");
            for(ValueWrapper relationshipsValueWrapper : relationshipsValueWrapperList){
                ArrayList<ValueWrapper> relationshipValueWrapperList = relationshipsValueWrapper.asList();
                for(ValueWrapper relationshipValueWrapper : relationshipValueWrapperList){
                    Relationship relationship = relationshipValueWrapper.asRelationship();
                    VertexAssociationEdgeVo vertexAssociationEdgeVo = new VertexAssociationEdgeVo();
                    vertexAssociationEdgeVo.setFrom(relationship.srcId().asString());
                    vertexAssociationEdgeVo.setTo(relationship.dstId().asString());
                    vertexAssociationEdgeVo.setLabel(relationship.edgeName());
                    edgeList.add(vertexAssociationEdgeVo);
                }
            }
        } catch (Exception e) {
            if(e.getMessage().contains("use space failed:  SpaceNotFound")){
//                throw new GkException(GkErrorEnum.SPACE_NOT_FIND_ERROR);
            }
//            throw new GkException(GkErrorEnum.GRAPHDB_PARSE_ERROR);
        }
        vertexAssociationNextVo.setVertex(vertexList);
        vertexAssociationNextVo.setEdge(edgeList);
        return vertexAssociationNextVo;
    }

    /*@Override
    public VertexAssociationNextVo getSubAssociationNext(SubGraphNextCondition condition) {
        VertexAssociationNextVo vertexAssociationNextVo = new VertexAssociationNextVo();
        Set<VertexAssociationVertexNextVo> vertexList = new LinkedHashSet<>();
        Set<VertexAssociationEdgeVo> edgeList = new LinkedHashSet<>();
        Set<String> vidList = new LinkedHashSet<>();
        try {
            ResultSet list = allAtlasDao.vidSubSearch(condition);
            List<ValueWrapper> nodesValueWrapperList = list.colValues("nodes");
            for(ValueWrapper nodesValueWrapper : nodesValueWrapperList) {
                List<ValueWrapper> nodeValueWrapperList = nodesValueWrapper.asList();
                for(ValueWrapper nodeValueWrapper : nodeValueWrapperList){
                    Node node = nodeValueWrapper.asNode();
                    if(CollectionUtil.isEmpty(node.tagNames())){
                        continue;
                    }
                    vidList.add(node.getId().asString());
                }
            }
            List<Map<String, Object>> vidInfoList = allAtlasDao.fetchOnTagInfoByVids(new ArrayList<>(vidList),condition.getSpaceName());
            for (Map<String, Object> vidInfoMap : vidInfoList) {
                Map<String, Object> vertexInfoMap = (Map<String, Object>) vidInfoMap.get("vertex_info");
                VertexAssociationVertexNextVo vertexAssociationVertexNextVo = new VertexAssociationVertexNextVo();
                String vid = (String) vidInfoMap.get("vertex_id");
                String tagType = (String) vidInfoMap.get("tag_type");
                vertexAssociationVertexNextVo.setType(tagType);
                vertexAssociationVertexNextVo.setLv(0L);
                vertexAssociationVertexNextVo.setLabel(vid);
                switch (tagType) {
                    case VertexTypeConstants.ORG:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("org_name"));
                        break;
                    case VertexTypeConstants.APPSERVICE:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("AppName"));
                        break;
                    case VertexTypeConstants.ISSUER:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("common_name"));
                        break;
                    case VertexTypeConstants.SUBJECT:
                        vertexAssociationVertexNextVo.setLabel((String) vertexInfoMap.get("common_name"));
                        break;
                }
                vertexAssociationVertexNextVo.setId(vid);
                vertexAssociationVertexNextVo.setVInfo(vertexInfoMap.entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                e -> e.getValue() != null ? e.getValue().toString() : ""
                        )));
                vertexList.add(vertexAssociationVertexNextVo);
            }
            Set<String> filteredSrcIds = new HashSet<>();
            List<ValueWrapper> relationshipsValueWrapperList = list.colValues("relationships");
            Map<String, Map<String, Integer>> edgeNameSrcIdCount = new HashMap<>();

            for (ValueWrapper relationshipsValueWrapper : relationshipsValueWrapperList) {
                ArrayList<ValueWrapper> relationshipValueWrapperList = relationshipsValueWrapper.asList();
                for (ValueWrapper relationshipValueWrapper : relationshipValueWrapperList) {
                    Relationship relationship = relationshipValueWrapper.asRelationship();
                    String edgeName = relationship.edgeName();
                    String srcId = relationship.srcId().asString();
                    String dstId = relationship.dstId().asString();
                    // 初始化计数 Map
                    edgeNameSrcIdCount.putIfAbsent(edgeName, new HashMap<>());
                    Map<String, Integer> srcIdCount = edgeNameSrcIdCount.get(edgeName);
                    srcIdCount.put(srcId, srcIdCount.getOrDefault(srcId, 0) + 1);

                    // 只处理前5条
                    if (srcIdCount.get(srcId) <= 5) {
                        VertexAssociationEdgeVo vertexAssociationEdgeVo = new VertexAssociationEdgeVo();
                        vertexAssociationEdgeVo.setFrom(srcId);
                        vertexAssociationEdgeVo.setTo(dstId);
                        vertexAssociationEdgeVo.setLabel(edgeName);
                        edgeList.add(vertexAssociationEdgeVo);
                    }else{
                        filteredSrcIds.add(dstId); // 记录被过滤的 srcId
                    }
                }
            }
            // 从 vertexList 中去掉 filteredSrcIds
            vertexList.removeIf(vertex -> filteredSrcIds.contains(vertex.getId()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        vertexAssociationNextVo.setVertex(vertexList);
        vertexAssociationNextVo.setEdge(edgeList);
        return vertexAssociationNextVo;
    }*/

    @Override
    public VertexAssociationNextVo getEdgePropertiesNext(GraphPropertiesNextCondition condition) {
        VertexAssociationNextVo vertexAssociationNextVo = new VertexAssociationNextVo();
        List<VertexEdgeNextVo> vertexEdgeNextVoList = new ArrayList<>();
        switch (condition.getTagName()) {
            case VertexTypeConstants.CERT:
                vertexEdgeNextVoList = certService.listCertNebulaNextByProperties(condition);
                break;
            case VertexTypeConstants.DOMAIN:
                vertexEdgeNextVoList = domainService.listDomainNebulaNextByProperties(condition);
                break;
            case VertexTypeConstants.ISSUER:
                vertexEdgeNextVoList = issuerService.listIssuerNebulaNextByProperties(condition);
                break;
            case VertexTypeConstants.SUBJECT:
                vertexEdgeNextVoList = subjectService.listSubjectNebulaNextByProperties(condition);
                break;
        }
        //根据关联数据组装所有关联点和边
        this.setVertexEdgeNextList(vertexAssociationNextVo,vertexEdgeNextVoList);
        return vertexAssociationNextVo;
    }

    /**
     * 根据关联数据组装所有关联点和边
     */
    public void setVertexEdgeList(VertexAssociationVo vertexAssociationVo,List<VertexEdgeVo> vertexEdgeVoList){
        Set<VertexAssociationVertexVo> vertexList = new LinkedHashSet<>();
        Set<VertexAssociationEdgeVo> edgeList = new LinkedHashSet<>();
        //根据关联数据组装所有关联点和边
        for(VertexEdgeVo vertexEdgeVo : vertexEdgeVoList){
            if(vertexEdgeVo.getSourceStatus()){
                //组装源数据点
                vertexList.add(new VertexAssociationVertexVo(){{
                    setType(vertexEdgeVo.getFromType());
                    setLv(vertexEdgeVo.getFromBlackList());
                    setLabel(vertexEdgeVo.getFromAddr());
                    setId(vertexEdgeVo.getFromAddr());
                    setMain("YES");
                }});
            }else{
                //组装起始点
                vertexList.add(new VertexAssociationVertexVo(){{
                    setType(vertexEdgeVo.getFromType());
                    setLv(vertexEdgeVo.getFromBlackList());
                    setLabel(vertexEdgeVo.getFromAddr());
                    if(DISPLAY_LABEL_EDGE.contains(vertexEdgeVo.getMiddleId())){
                        setLabel(vertexEdgeVo.getFromLabel());
                    }
                    setId(vertexEdgeVo.getFromAddr());
                }});
                //组装终点
                vertexList.add(new VertexAssociationVertexVo(){{
                    setType(vertexEdgeVo.getToType());
                    setLv(vertexEdgeVo.getToBlackList());
                    setLabel(vertexEdgeVo.getToAddr());
                    if(DISPLAY_LABEL_EDGE.contains(vertexEdgeVo.getMiddleId())){
                        setLabel(vertexEdgeVo.getToLabel());
                    }
                    setId(vertexEdgeVo.getToAddr());
                }});
//                //组装中间点
//                vertexList.add(new VertexAssociationVertexVo(){{
//                    setType(vertexEdgeVo.getMiddleType());
//                    setLabel(vertexEdgeVo.getMiddleLabel());
//                    setId(vertexEdgeVo.getMiddleId());
//                }});
                //组装起点到中间点的边
                edgeList.add(new VertexAssociationEdgeVo(){{
                    setFrom(vertexEdgeVo.getFromAddr());
                    setTo(vertexEdgeVo.getToAddr());
                    setLabel(vertexEdgeVo.getMiddleId());
                }});
//                //组装中间点到终点的边
//                edgeList.add(new VertexAssociationEdgeVo(){{
//                    setFrom(vertexEdgeVo.getMiddleId());
//                    setTo(vertexEdgeVo.getToAddr());
//                    if(!vertexEdgeVo.getDirectionStatus()){
//                        //如果是反向设置中间点到终点的边的label
//                        setLabel(vertexEdgeVo.getMiddleId());
//                    }
//                }});
            }
        }
        vertexAssociationVo.setVertex(vertexList);
        vertexAssociationVo.setEdge(edgeList);
    }

    /**
     * 根据关联数据组装所有关联点和边
     */
    public void setVertexEdgeNextList(VertexAssociationNextVo vertexAssociationNextVo,List<VertexEdgeNextVo> vertexEdgeNextVoList){
        Set<VertexAssociationVertexNextVo> vertexList = new LinkedHashSet<>();
        Set<VertexAssociationEdgeVo> edgeList = new LinkedHashSet<>();
        //根据关联数据组装所有关联点和边
        if(vertexEdgeNextVoList!=null && vertexEdgeNextVoList.size()>1){
            for(VertexEdgeNextVo vertexEdgeNextVo : vertexEdgeNextVoList){
                if(vertexEdgeNextVo.getSourceStatus()){
                    //组装源数据点
                    vertexList.add(new VertexAssociationVertexNextVo(){{
                        setType(vertexEdgeNextVo.getFromType());
                        setLv(vertexEdgeNextVo.getFromBlackList());
                        setLabel(vertexEdgeNextVo.getFromAddr());
                        if(DISPLAY_LABEL_VERTEX.contains(vertexEdgeNextVo.getFromType())){
                            setLabel(vertexEdgeNextVo.getFromLabel());
                        }
                        setId(vertexEdgeNextVo.getFromAddr());
                    }});
                }else{
                    //组装终点
                    if(!vertexEdgeNextVo.getDirectionStatus()){
                        vertexList.add(new VertexAssociationVertexNextVo(){{
                            setType(vertexEdgeNextVo.getToType());
                            setLv(vertexEdgeNextVo.getToBlackList());
                            setLabel(vertexEdgeNextVo.getToAddr());
                            if(DISPLAY_LABEL_EDGE.contains(vertexEdgeNextVo.getTeage())){
                                setLabel(vertexEdgeNextVo.getToLabel());
                            }
                            setId(vertexEdgeNextVo.getToAddr());
                            if(vertexEdgeNextVo.getVInfo()!=null){
                                setVInfo(vertexEdgeNextVo.getVInfo().entrySet().stream()
                                        .collect(Collectors.toMap(
                                                Map.Entry::getKey,
                                                e -> e.getValue() != null ? e.getValue().toString() : ""
                                        )));
                            }
                        }});
                    }else{
                        //反向则组装起点
                        vertexList.add(new VertexAssociationVertexNextVo(){{
                            setType(vertexEdgeNextVo.getFromType());
                            setLv(vertexEdgeNextVo.getFromBlackList());
                            setLabel(vertexEdgeNextVo.getFromAddr());
                            if(DISPLAY_LABEL_EDGE.contains(vertexEdgeNextVo.getTeage())){
                                setLabel(vertexEdgeNextVo.getFromLabel());
                            }
                            setId(vertexEdgeNextVo.getFromAddr());
                            if(vertexEdgeNextVo.getVInfo()!=null){
                                setVInfo(vertexEdgeNextVo.getVInfo().entrySet().stream()
                                        .collect(Collectors.toMap(
                                                Map.Entry::getKey,
                                                e -> e.getValue() != null ? e.getValue().toString() : ""
                                        )));
                            }
                        }});
                    }
                    //组装起点到终点的边
                    edgeList.add(new VertexAssociationEdgeVo(){{
                        setFrom(vertexEdgeNextVo.getFromAddr());
                        setTo(vertexEdgeNextVo.getToAddr());
                        setLabel(vertexEdgeNextVo.getTeage());
                    }});
                }
            }
        }
        vertexAssociationNextVo.setVertex(vertexList);
        vertexAssociationNextVo.setEdge(edgeList);
    }



}

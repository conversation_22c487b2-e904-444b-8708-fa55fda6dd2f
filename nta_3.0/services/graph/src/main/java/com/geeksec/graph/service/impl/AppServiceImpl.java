package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.AppDao;
import com.geeksec.graph.service.AppService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: APP服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class AppServiceImpl implements AppService {

    @Autowired
    private AppDao appDao;

    /**
     * APP关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getAppNebulaAssociationNext(GraphNextInfoCondition condition) {
        return appDao.listAppAllEdgeTypeAssociationNext(condition);
    }


}

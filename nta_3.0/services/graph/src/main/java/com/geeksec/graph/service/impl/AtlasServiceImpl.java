package com.geeksec.graph.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.IntStream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.geeksec.common.dto.ApiResponse;
import com.geeksec.graph.condition.VidsSearchCondition;
import com.geeksec.graph.repository.AllAtlasDao;
import com.geeksec.graph.service.AtlasService;
import com.google.common.collect.Lists;
import com.vesoft.nebula.client.graph.data.Node;
import com.vesoft.nebula.client.graph.data.PathWrapper;
import com.vesoft.nebula.client.graph.data.Relationship;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: qiuwen
 * @date: 2022/9/16
 * @Description:
 **/
@Service
@Slf4j
@RequiredArgsConstructor
public class AtlasServiceImpl implements AtlasService {

    private static List<String> orgRelatedList = Lists.newArrayList("domain_belong_to_org", "ip_belong_to_org",
            "cert_belong_to_org");
    private static List<String> urlRealtedList = Lists.newArrayList("domain_url_related", "ip_url_related",
            "cert_url_related");
    private static List<String> connectList = Lists.newArrayList("connect_mac");

    private static Map<String, Map<String, Object>> nebulaDict = new HashMap<>(); // 点边名称字典
    private static Map<String, Map<String, Object>> parseEdgeDict = new HashMap<>(); // 点-边关系字典

    private final AllAtlasDao allAtlasDao;

    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;

    @PostConstruct
    private void initSrcDstDict() throws IOException {

        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        InputStream inputStream = null;
        String jsonStr = StringUtils.EMPTY;
        // 读取nebula_dict文件
        try {
            jsonStr = parseFile("dict/analysis/nebula_dict.json");
            nebulaDict = (Map<String, Map<String, Object>>) JSON.parse(jsonStr);
            log.info("初始化nebula字典成功！");
        } catch (IOException e) {
            log.error("读取nebula_dict文件失败！error->", e);
        }

        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();

    }

    @PostConstruct
    private void initParseEdgeDict() {
        try {
            String jsonStr = parseFile("dict/analysis/parse_conf.json");
            List<Map<String, Object>> relationList = JSON.parseObject(jsonStr,
                    new TypeReference<List<Map<String, Object>>>() {
                    });

            for (Map<String, Object> parseMap : relationList) {
                List<String> handleEdgeList = (List<String>) parseMap.get("handle");
                List<String> rhandleEdgeList = (List<String>) parseMap.get("Rhandle");
                String tagType = (String) parseMap.get("type");

                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("handle", handleEdgeList);
                resultMap.put("Rhandle", rhandleEdgeList);
                parseEdgeDict.put(tagType, resultMap);
            }
        } catch (Exception e) {
            log.error("初始化解析边关系字典失败！error->", e);
        }
    }

    @Override
    public ApiResponse vidSearch(VidsSearchCondition condition) {
        List<String> vidList = condition.getVidList();
        Map<String, Object> resultMap = new HashMap<>();
        // 不允许存在空字符串
        if (vidList.stream().anyMatch(StrUtil::isEmpty)) {
            // throw new BusinessException(GkErrorEnum.REQUEST_PARAM_ERROR);
        }

        // 存留确实存在的VID点
        List<String> allVid = new ArrayList<>();
        JSONArray vertexArray = new JSONArray();
        JSONArray edgeArray = new JSONArray();

        // 先通过FetchOn确定存在的节点，然后确定对应的allVid里的内容
        List<Map<String, Object>> resultList = allAtlasDao.fetchOnTagInfoByVids(vidList);
        if (ObjectUtil.isEmpty(resultList)) {
            resultMap.put("vertex", vertexArray);
            resultMap.put("edge", edgeArray);
            resultMap.put("contains", new ArrayList<>());
            return ApiResponse.success(resultMap);
        }

        try {
            for (Map<String, Object> dataMap : resultList) {
                Map<String, Object> vertexInfoMap = (Map<String, Object>) dataMap.get("vertex_info");
                String tagType = (String) dataMap.get("tag_type");
                // 只取tagType字符串中，'Vertex'前面的内容，用作返回点类型

                String vid = (String) dataMap.get("vertex_id");
                if (allVid.contains(vid)) {
                    continue;
                } else {
                    allVid.add(vid);
                }
                JSONObject vertexJson = new JSONObject();
                vertexJson.put("label", vid);
                vertexJson.put("id", vid);
                vertexJson.put("type", tagType);
                vertexJson.put("v_info", vertexInfoMap);
                vertexArray.add(vertexJson);
            }

            if (allVid.size() > 1) {
                // 通过
                List<Map<String, Object>> pathResultList = allAtlasDao.findPathByVids(allVid);
                if (!CollectionUtils.isEmpty(pathResultList)) {
                    // 一次查询两个以上的节点信息
                    for (Map<String, Object> nebulaMap : pathResultList) {
                        PathWrapper pathWrapper = (PathWrapper) nebulaMap.get("P");
                        List<Node> vertexList = pathWrapper.getNodes();
                        List<Relationship> relationshipList = pathWrapper.getRelationships();
                        // 循环两个点之间的边关系，生成对应边信息
                        for (Relationship relationship : relationshipList) {
                            String from = relationship.srcId().asString();
                            String to = relationship.dstId().asString();
                            String label = relationship.edgeName().toString();
                            JSONObject json = new JSONObject();
                            json.put("from", from);
                            json.put("to", to);
                            json.put("label", label);
                            edgeArray.add(json);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // throw new BusinessException(GkErrorEnum.GRAPHDB_PARSE_ERROR);
        }

        // 获取到所有的点和边之后，做一个containsMap用来所相互牵连,据说是为了美观
        List<Map<String, Object>> containsMapList = new ArrayList<>();
        if (allVid.size() > 1) {
            IntStream.range(0, allVid.size())
                    .forEach(num -> {
                        String fromStr = allVid.get(num);
                        // 最后一个点与第一个点相连，用id str做牵连
                        String toStr = (num == allVid.size() - 1) ? allVid.get(0) : allVid.get(num + 1);
                        Map<String, Object> containMap = new HashMap<>();
                        containMap.put("from", fromStr);
                        containMap.put("to", toStr);
                        containMap.put("label", "contain_edge");
                        containsMapList.add(containMap);
                    });
        }

        resultMap.put("vertex", vertexArray);
        resultMap.put("edge", edgeArray);
        resultMap.put("contains", containsMapList);
        return ApiResponse.success(resultMap);

    }

    private String parseFile(String path) throws IOException {

        BufferedReader bufferedReader = null;
        StringBuilder stringBuilder = new StringBuilder();
        try {
            InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(path);
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
            return stringBuilder.toString();
        } catch (IOException e) {
            log.error("读取path->{}文件失败！error->{}", path, e);
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException ex) {
                    throw ex;
                }
            }
        }
        return null;
    }

}

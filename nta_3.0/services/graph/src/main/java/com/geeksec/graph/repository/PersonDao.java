package com.geeksec.graph.repository;

import com.geeksec.graph.pojo.vertex.Person;
import com.vesoft.nebula.client.graph.data.ResultSet;
import org.nebula.contrib.ngbatis.models.data.NgEdge;
import org.nebula.contrib.ngbatis.models.data.NgSubgraph;
import org.nebula.contrib.ngbatis.models.data.NgVertex;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.nebula.contrib.ngbatis.utils.Page;
import org.springframework.data.repository.query.Param;

import jakarta.persistence.Id;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PersonDao extends NebulaDaoBasic<Person, String> {

    Integer select1();

    Person selectPerson();

    Map selectPersonMap();

    List<Map> selectPersonsMap();

    List<Person> selectPersons();

    Set<Map> selectPersonsSet();

    List<String> selectListString();

    Integer selectInt();

    Person selectV();

    List<Person> selectListV();

    String selectString();

    String selectStringParam(String name);

    Integer selectIntParam(Integer age);

    Boolean selectBoolParam(Boolean finish);

    List<Person> selectCustomPage(Page<Person> page);

    List<Person> selectCustomPageAndName(Page<Person> page, String name);

    ResultSet testMulti();

    Map<String, Object> selectMapWhenNull();

    void testSpaceSwitchStep1();

    Integer testSpaceSwitchStep2();

    void insertWithTimestamp(@Param("person") Person person);

    List<NgVertex<String>> selectVertexes();

    List<NgEdge<String>> selectEdges();

    List<NgSubgraph<String>> selectSubgraph();

    List<Person> selectByPerson(@Param("p") Person a);

    void insertDynamic(List<DynamicNode> list);


    class DynamicNode {
        @Id
        private String vid;
        private String tagName;
        private Map<String, Object> propertyList;

        public String getVid() {
            return vid;
        }

        public void setVid(String vid) {
            this.vid = vid;
        }

        public String getTagName() {
            return tagName;
        }

        public void setTagName(String tagName) {
            this.tagName = tagName;
        }

        public Map<String, Object> getPropertyList() {
            return propertyList;
        }

        public void setPropertyList(Map<String, Object> propertyList) {
            this.propertyList = propertyList;
        }
    }

}

package com.geeksec.graph.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
*@description: 点关联关系点Vo
*@author: shiwenxu
*@createtime: 2023/8/30 11:28
**/
@Data
@EqualsAndHashCode(of = {"num", "lv", "id", "type", "status"})
@JsonPropertyOrder({ "vInfo", "num", "lv", "label", "id", "type", "status"})
public class VertexAssociationVertexNextVo {

    private String num = "";

    private Long lv = 0L;

    private String label = "";

    private String id = "";

    private String type = "";

    private String status = "";

    @JsonProperty("v_info")
    private Map<String,String> vInfo;


}


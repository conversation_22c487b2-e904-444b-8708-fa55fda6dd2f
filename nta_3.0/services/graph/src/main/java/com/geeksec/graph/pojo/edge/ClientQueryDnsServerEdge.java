package com.geeksec.graph.pojo.edge;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 客户端访问DNS（IP->域名）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "client_query_dns_server")
@Data
public class ClientQueryDnsServerEdge {

  /**
   * dns类型
   */
  @Column(name = "dns_type")
  private Long dnsType;

  /**
   * 回复类型
   */
  @Column(name = "answer_type")
  private Long answerType;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}

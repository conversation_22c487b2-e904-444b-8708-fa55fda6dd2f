package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: URL点
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "URL")
@Data
  public class UrlVertex {

  @Id
  @Column(name = "url_key")
  private String urlKey;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: APP
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "APP")
@Data
public class AppVertex {

  /**
  * APP名称
  */
  @Column(name = "app_name")
  private String appName;

  /**
  * APP版本
  */
  @Column(name = "app_version")
  private String appVersion;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

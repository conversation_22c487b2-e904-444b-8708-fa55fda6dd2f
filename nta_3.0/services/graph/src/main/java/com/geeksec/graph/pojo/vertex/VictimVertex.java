package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

/**
*@description: 攻击
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "VICTIM")
@Data
public class VictimVertex {

  /**
  * 攻击id
  */
  @Id
  @Column(name = "victim_id")
  private String victimId;

  /**
   * ip地址
   */
  @Column(name = "ip_addr")
  private String ipAddr;

}

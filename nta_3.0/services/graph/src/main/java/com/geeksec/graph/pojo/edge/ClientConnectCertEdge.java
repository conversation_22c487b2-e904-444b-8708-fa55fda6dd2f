package com.geeksec.graph.pojo.edge;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 客户端访问证书（IP->证书）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "client_connect_cert")
@Data
public class ClientConnectCertEdge {

  /**
  * sni
  */
  private String sni;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

}

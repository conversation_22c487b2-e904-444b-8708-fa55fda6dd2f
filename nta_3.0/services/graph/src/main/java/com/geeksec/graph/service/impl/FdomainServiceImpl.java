package com.geeksec.graph.service.impl;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.repository.FdomainDao;
import com.geeksec.graph.service.FdomainService;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: Fdomain服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class FdomainServiceImpl implements FdomainService {

    @Autowired
    private FdomainDao fdomainDao;

    /**
     * Fdomain关联查询Next
     */
    @Override
    public List<VertexEdgeNextVo> getFdomainNebulaAssociationNext(GraphNextInfoCondition condition) {
        return fdomainDao.listFdomainAllEdgeTypeAssociationNext(condition);
    }
}

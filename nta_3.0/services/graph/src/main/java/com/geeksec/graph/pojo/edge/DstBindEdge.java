package com.geeksec.graph.pojo.edge;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 目的IP关联MAC（IP->MAC）
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "dst_bind")
@Data
public class DstBindEdge {

  /**
   * ip
   */
  private String ip;

  /**
   * mac
   */
  private String mac;

  /**
   * 首次出现时间
   */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
   * 出现次数
   */
  @Column(name = "session_cnt")
  private Long sessionCnt;

  /**
   * 接收的流量大小
   */
  private Long bytes;

  /**
   * 接收的包数
   */
  private Long packets;

}

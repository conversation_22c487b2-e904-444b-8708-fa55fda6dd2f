package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.DeviceVertex;
import com.geeksec.graph.vo.VertexEdgeNextVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface DeviceDao extends NebulaDaoBasic<DeviceVertex, String> {

    /**
     * 查询Device所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listDeviceAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);

}

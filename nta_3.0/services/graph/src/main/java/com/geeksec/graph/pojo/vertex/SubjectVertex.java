package com.geeksec.graph.pojo.vertex;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 所有者
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "SUBJECT")
@Data
public class SubjectVertex {

  /**
  * 应用名
  */
  @Id
  @Column(name = "subject_md5")
  private String subjectMd5;

  /**
  * 国家
  */
  @Column(name = "country")
  private String country;

  /**
  * 通用名称
  */
  @Column(name = "common_name")
  private String commonName;

  /**
   * 组织名称
   */
  @Column(name = "object_name")
  private String objectName;

  /**
   * 首次出现时间
   */
  @Column(name = "first_seen")
  private Timestamp firstTime;

  /**
   * 末次出现时间
   */
  @Column(name = "last_seen")
  private Timestamp lastTime;

}

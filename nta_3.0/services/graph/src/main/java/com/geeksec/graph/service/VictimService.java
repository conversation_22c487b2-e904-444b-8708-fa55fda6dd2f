package com.geeksec.graph.service;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.vo.VertexEdgeNextVo;

import java.util.List;

/**
*@description: Victim服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface VictimService {

    /**
     * Victim关联查询Next
     */
    List<VertexEdgeNextVo> getVictimNebulaAssociationNext(GraphNextInfoCondition condition);

}

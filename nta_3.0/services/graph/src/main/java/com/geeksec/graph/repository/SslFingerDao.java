package com.geeksec.graph.repository;

import com.geeksec.graph.condition.GraphNextInfoCondition;
import com.geeksec.graph.pojo.vertex.SslFingerVertex;
import com.geeksec.graph.vo.*;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface SslFingerDao extends NebulaDaoBasic<SslFingerVertex, String> {

    /**
     * 根据ids查询SSL指纹描述
     */
    List<SslFingerDescVo> listFingerDescribeByFingers(@Param("fingers") List<String> fingers);

    /**
     * 根据ids查询SSL指纹标签
     */
    List<SslFingerLabelVo> listFingerLabelByFingers(@Param("fingers") List<String> fingers);

    /**
     * 根据ids查询SSL指纹服务端IP热度
     */
    List<SslFingerRelatedIpsVo> listServerRelatedIpsByFingers(@Param("fingers") List<String> fingers);

    /**
     * 根据ids查询SSL指纹客户端IP热度
     */
    List<SslFingerRelatedIpsVo> listClientRelatedIpsByFingers(@Param("fingers") List<String> fingers);

    /**
     * 根据ids查询关联证书数量
     */
    List<SslFingerCountVo> countCertByFingers(@Param("fingers") List<String> fingers);

    /**
     * 查询SslFinger所有边类型关联数据Next
     */
    List<VertexEdgeNextVo> listSslFingerAllEdgeTypeAssociationNext(@Param("condition") GraphNextInfoCondition condition);
}

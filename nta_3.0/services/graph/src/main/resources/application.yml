# 应用基本信息
spring:
  application:
    name: graph-service
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
    include: common

  # 数据源配置 - 统一使用PostgreSQL + Druid
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta}
    password: ${DB_PASSWORD:nta123}

    # Druid 连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

      # 监控配置
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: ${DRUID_USERNAME:admin}
        login-password: ${DRUID_PASSWORD:admin123}

# 服务器基本配置
server:
  port: 8087
  servlet:
    context-path: /graph

# Nebula Graph基本配置
nebula:
  # ngbatis配置
  ngbatis:
    # 连接使用 nebula-java 中的 SessionPool
    use-session-pool: true
  # 填入 graphd 的 ip 和端口号 - 使用环境变量
  hosts: ${NEBULA_HOSTS:localhost:9669}
  # 连接图数据库所用的用户名
  username: ${NEBULA_USERNAME:root}
  # 连接图数据库所用的密码
  password: ${NEBULA_PASSWORD:nebula}
  # 所要连接的图数据库图空间名
  space: ${NEBULA_SPACE:gs_analysis_graph}
  # 连接池配置
  pool-config:
    # 连接池中最小空闲连接数
    min-conns-size: 0
    # 连接池中最大空闲连接数
    max-conns-size: 10
    # 客户端同服务端建立连接的超时时间设置，单位为 ms
    timeout: 3000
    # 连接空闲时间，为 0 表示连接永不删除，单位为 ms
    idle-time: 0
    # 连接池检测空闲连接的时间间隔，为 -1 表示不进行检测
    interval-idle: -1
    # 连接等候时间，超过则不再等候连接
    wait-time: 120
    # 集群允许最小的服务可用率
    min-cluster-health-rate: 1.0
    # 是否允许 SSL 连接
    enable-ssl: false
    max_sessions_per_ip_per_user: 2000

# 防止与mybatis冲突，单独配置mapper扫描路径
cql:
  parser:
    mapperLocations: classpath*:repository/*.xml

logging:
  level:
    root: INFO
    org.springframework.web: DEBUG
    com.geeksec: DEBUG
#    com.geeksec.graph: DEBUG

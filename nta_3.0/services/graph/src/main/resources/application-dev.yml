# Graph Service 开发环境配置
# 继承通用开发环境配置

spring:
  profiles:
    active: dev
    include: common

  # Graph服务开发环境数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:nta_graph_dev}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${DB_USERNAME:nta_dev}
    password: ${DB_PASSWORD:nta_dev123}

# 服务器配置
server:
  port: ${SERVER_PORT:8087}
  servlet:
    context-path: /graph

# Nebula Graph 开发环境配置
nebula:
  ngbatis:
    use-session-pool: true
  # 开发环境 Nebula Graph 连接配置
  hosts: ${NEBULA_HOSTS:localhost:9669}
  username: ${NEBULA_USERNAME:root}
  password: ${NEBULA_PASSWORD:nebula}
  space: ${NEBULA_SPACE:gs_analysis_graph_dev}
  
  # 开发环境连接池配置（较小规模）
  pool-config:
    min-conns-size: 0
    max-conns-size: 5
    timeout: 3000
    idle-time: 0
    interval-idle: -1
    wait-time: 60
    min-cluster-health-rate: 1.0
    enable-ssl: false
    max_sessions_per_ip_per_user: 100

# Graph服务特定配置
graph:
  # 图数据处理配置
  processing:
    batch-size: 100  # 开发环境较小批次
    max-depth: 5  # 图遍历最大深度
    timeout: 30000  # 30秒超时
  
  # 缓存配置
  cache:
    enable: true
    ttl: 300  # 5分钟缓存
    max-size: 1000
  
  # 开发环境数据路径
  data:
    import-path: ${GRAPH_IMPORT_PATH:./dev-data/graph}
    export-path: ${GRAPH_EXPORT_PATH:./dev-data/export}

# CQL配置
cql:
  parser:
    mapperLocations: classpath*:repository/*.xml

# 日志配置
logging:
  level:
    com.geeksec.graph: DEBUG
    org.springframework.web: DEBUG
    com.vesoft.nebula: INFO

<mapper namespace="com.geeksec.graph.repository.SubjectDao">

    <select id="listSubjectAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (SUBJECT:SUBJECT)
        WHERE id(SUBJECT) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "SUBJECT" AS fromType,
        0 AS fromBlackList,
        id(SUBJECT) AS fromAddr,
        SUBJECT.SUBJECT.common_name AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'subject_related'){
                UNION ALL
                MATCH
                (SUBJECT:SUBJECT)-[subject_related:subject_related]-(CERT:CERT)
                WHERE
                id(SUBJECT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "SUBJECT" AS toType,
                0 AS toBlackList,
                id(SUBJECT) AS toAddr,
                SUBJECT.SUBJECT.common_name AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(SUBJECT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

    <select id="listSubjectNebulaNextByProperties" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">

    </select>

</mapper>
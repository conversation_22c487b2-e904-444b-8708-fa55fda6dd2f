<mapper namespace="com.geeksec.graph.repository.AllAtlasDao">

    <select id="fetchOnTagInfoByVids" resultType="java.util.Map">
        @var vid_list = ng.join(vids, ',',"ng.valueFmt");
        FETCH PROP ON * ${@vid_list}
        YIELD id(vertex) as vertex_id,
        properties(vertex) as vertex_info,
        labels(vertex)[0] as tag_type
    </select>

    <select id="matchTagInfoByProperties" resultType="java.util.Map">
        MATCH (${condition.tagName}:${condition.tagName})
        WHERE ${condition.tagName}.${condition.tagName}.${condition.propertiesName} contains ${ng.valueFmt(condition.propertiesValue)}
        return id(${condition.tagName}) as vertex_id,
        properties(${condition.tagName}) as vertex_info,
        labels(${condition.tagName})[0] as tag_type
    </select>

    <select id="vidSubSearch">
        @var vid_list = ng.join(condition.vidList, ',',"ng.valueFmt");
        GET SUBGRAPH ${ng.valueFmt(condition.stepCount)} STEPS FROM ${@vid_list} YIELD VERTICES AS nodes, EDGES AS relationships;
    </select>

    <select id="findPathByVids" resultType="java.util.Map">
        @var vid_list = ng.join(vids, ',',"ng.valueFmt");
        FIND SHORTEST PATH WITH PROP
        FROM ${@vid_list} TO ${@vid_list} OVER * UPTO 1 STEPS
        YIELD PATH AS P | ORDER BY $-.P
    </select>

    <select id="getTagRelationList" resultType="java.util.Map">
        GO FROM ${ng.valueFmt(vid)} OVER ${edgeType}
        @if (direct == false) {
        REVERSELY
        @}
        where properties($$) is not null
        @if (edgeType == 'parse_to'){
        and properties(edge).final_parse == true
        @}
        YIELD src(edge) as src, dst(edge) as dst,properties(edge) as edge_info,properties($$) as tag_info
        @select{
        @   case (targetType=="IP" || targetType == "DOMAIN" || targetType == "CERT")&amp;&amp;(orderField==null || orderField=="session_cnt"):
                ,properties(edge).session_cnt as cnt | ORDER BY $-.cnt ${sortOrder} | OFFSET ${offset} LIMIT ${limit}
        @   case (targetType=="IP" || targetType == "DOMAIN" || targetType == "CERT")&amp;&amp;(orderField=="black_list" || orderField=="white_list"):
                ,properties($$).${orderField} as orderField | ORDER BY $-.orderField ${sortOrder} | OFFSET ${offset} LIMIT ${limit}
        @   case (targetType=="FDOMAIN" || targetType == "MAC") &amp;&amp;(orderField==null || orderField=="black_list"):
                ,properties(edge).black_list as orderField | ORDER BY $-.orderField ${sortOrder} | OFFSET ${offset} LIMIT ${limit}
        @   case (targetType=="FDOMAIN" || targetType == "MAC") &amp;&amp;(orderField=="white_list"):
                ,properties(edge).white_list as orderField | ORDER BY $-.orderField ${sortOrder} | OFFSET ${offset} LIMIT ${limit}
        @   case targetType=="SSLFINGER" || targetType == "APPSERVICE":
                ,properties(edge).session_cnt as cnt | ORDER BY $-.cnt ${sortOrder} | OFFSET ${offset} LIMIT ${limit}
        @   case targetType=="UA":
                | OFFSET ${offset} LIMIT ${limit}
        @   default :
                | OFFSET ${offset} LIMIT ${limit}
        @}
    </select>

    <select id="countTagRelation" resultType="java.lang.Integer">
        GO FROM ${ng.valueFmt(vid)} OVER ${edgeType}
        @if (direct == false) {
            REVERSELY
        @}
        where properties($$) is not null
        @if (edgeType == 'parse_to'){
        and properties(edge).final_parse == true
        @}
        YIELD src(edge) as src,dst(edge) as dst
        | GROUP BY
        @if (direct == true) {
            $-.src YIELD count(*) as count
        @} else {
            $-.dst YIELD count(*) as count
        @}
    </select>

    <select id="getAllOrgInfo" resultType="java.util.Map">
        MATCH (ORG:ORG) return id(ORG) as vid ,properties(ORG) as v_info limit 1000
    </select>

    <select id="getOrgHasLabel" resultType="java.util.Map">
        GO FROM 'www.baidu.com'
        OVER has_label YIELD properties($$).label_id as label_id,properties($^).domain_addr as domain_addr
        | GROUP BY $-.domain_addr YIELD collect($-.label_id) as labels
    </select>

    <select id="getTagLabels" resultType="java.lang.String">
        GO FROM ${ng.valueFmt(str)}
        OVER has_label YIELD src(edge) AS src, dst(edge) as dst | group by $-.src YIELD collect($-.dst) as labels
    </select>

    <select id="getVisibleRelation" resultType="java.lang.String">
        GO FROM ${ng.valueFmt(vid)}
        OVER ${edge_str}
        @select{
        @   case (direct == "forward"):
                YIELD
        @   case (direct == "reverse"):
                REVERSELY YIELD
        @   case (direct == "bothway"):
                BIDIRECT YIELD
        @}
        DISTINCT type(edge) AS edgeType
    </select>

    <select id="getFocusTagRelation" resultType="java.util.Map">
        MATCH (v1:${start_type}) - [e] - (v2:${end_type})
        where id(v1) == ${ng.valueFmt(start_vid)} and id(v2) == ${ng.valueFmt(end_vid)}
        return src(e) as src_vid,dst(e) as dst_vid, type(e) as edge_type

    </select>
</mapper>
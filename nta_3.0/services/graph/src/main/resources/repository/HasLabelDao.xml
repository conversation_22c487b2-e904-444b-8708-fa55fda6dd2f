<mapper namespace="com.geeksec.graph.repository.HasLabelDao">

    <select id="listTagVertexByTagIds" resultType="com.geeksec.graph.vo.HasLabelVertexVo">
        GO FROM ${ ng.join(tagIds,",","ng.valueFmt") }
        OVER has_label REVERSELY
        YIELD src(edge) AS src, dst(edge) AS dst, properties($$) AS target
        | GROUP BY $-.dst
        YIELD $-.dst AS tagId, collect($-.target) AS tagList
    </select>

    <delete id="deleteHasLabel">
        GO FROM ${ ng.valueFmt(id) }
        OVER has_label
        YIELD src(edge) AS src, dst(edge) AS dst, rank(edge) AS rank
        | DELETE EDGE has_label $-.src -> $-.dst \@ $-.rank
    </delete>

    <insert id="insertHasLabel">
        @for( label in labels ) {
            INSERT EDGE has_label (analysis_by,remark) VALUES ${ ng.valueFmt(id) } -> ${ ng.valueFmt(label) }:('edit','');
        @}
    </insert>

</mapper>
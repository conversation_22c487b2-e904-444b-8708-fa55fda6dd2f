<mapper namespace="com.geeksec.graph.repository.AttackerDao">

    <select id="listAttackAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (ATTACKER:ATTACKER)
        WHERE id(ATTACKER) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "ATTACKER" AS fromType,
        0 AS fromBlackList,
        ATTACKER.ATTACKER.attacker_id AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        true as sourceStatus,
        false as directionStatus,
        properties(ATTACKER) AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'make_attack'){
                UNION ALL
                MATCH
                (ATTACKER:ATTACKER)-[make_attack:make_attack]->(VICTIM:VICTIM)
                WHERE
                id(ATTACKER) == ${ng.valueFmt(condition.str)}
                RETURN
                "ATTACKER" AS fromType,
                0 AS fromBlackList,
                ATTACKER.ATTACKER.attacker_id AS fromAddr,
                "VICTIM" AS toType,
                0 AS toBlackList,
                VICTIM.VICTIM.victim_id AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(VICTIM) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>
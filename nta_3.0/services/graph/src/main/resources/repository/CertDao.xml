<mapper namespace="com.geeksec.graph.repository.CertDao">

    <select id="listCertAllEdgeTypeAssociation" resultType="com.geeksec.graph.vo.VertexEdgeVo">
        MATCH (CERT:CERT)
        WHERE id(CERT) == $cert
        RETURN "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        "YES" AS middleType,
        "" AS middleId,
        "" AS middleLabel,
        true as sourceStatus,
        true as directionStatus
        LIMIT 1

        UNION ALL
        MATCH (IP:IP)-[server_use_cert:server_use_cert]->(CERT:CERT)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "server_use_cert" AS middleId,
        "d_CERT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_belong_to_org:cert_belong_to_org]->(ORG:ORG)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "ORG" AS toType,
        ORG.ORG.black_list AS toBlackList,
        id(ORG) AS toAddr,
        ORG.ORG.org_name AS toLabel,
        "Folder" AS middleType,
        "cert_belong_to_org" AS middleId,
        "d_ORG" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[client_use_cert:client_use_cert]-(IP:IP)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "client_use_cert" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[client_connect_cert:client_connect_cert]-(IP:IP)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        IP.IP.ip_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "client_connect_cert" AS middleId,
        "s_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[sni_bind:sni_bind]-(DOMAIN:DOMAIN)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        DOMAIN.DOMAIN.domain_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "sni_bind" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[sni_bind_fdomain:sni_bind_fdomain]-(FDOMAIN:FDOMAIN)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "FDOMAIN" AS fromType,
        0 AS fromBlackList,
        FDOMAIN.FDOMAIN.fdomain_addr AS fromAddr,
        FDOMAIN.FDOMAIN.fdomain_addr AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "sni_bind_fdomain" AS middleId,
        "d_FDOMAIN" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[sslfinger_connect_cert:sslfinger_connect_cert]-(SSLFINGER:SSLFINGER)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "SSLFINGER" AS fromType,
        0 AS fromBlackList,
        SSLFINGER.SSLFINGER.finger_id AS fromAddr,
        SSLFINGER.SSLFINGER.finger_id AS fromLabel,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "sslfinger_connect_cert" AS middleId,
        "d_SSLFINGER" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_sign_cert:cert_sign_cert]->(CERT1:CERT)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "CERT" AS toType,
        0 AS toBlackList,
        CERT1.CERT.cert_id AS toAddr,
        CERT1.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "cert_sign_cert" AS middleId,
        "d_CERT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT1:CERT)-[cert_sign_cert:cert_sign_cert]-(CERT:CERT)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT1.CERT.black_list AS fromBlackList,
        CERT1.CERT.cert_id AS fromAddr,
        CERT1.CERT.cert_id AS fromLabel,
        "CERT" AS toType,
        0 AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        CERT.CERT.cert_id AS toLabel,
        "Folder" AS middleType,
        "cert_sign_cert" AS middleId,
        "d_CERT" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[special_business_port_service:special_business_port_service]->(APPSERVICE:APPSERVICE)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "APPSERVICE" AS toType,
        0 AS toBlackList,
        id(APPSERVICE) AS toAddr,
        APPSERVICE.APPSERVICE.AppName AS toLabel,
        "Folder" AS middleType,
        "special_business_port_service" AS middleId,
        "d_APPSERVICE" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_validate_domain:cert_validate_domain]->(DOMAIN:DOMAIN)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        DOMAIN.DOMAIN.domain_addr AS toLabel,
        "Folder" AS middleType,
        "cert_validate_domain" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_validate_fDomain:cert_validate_fDomain]->(FDOMAIN:FDOMAIN)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "FDOMAIN" AS toType,
        0 AS toBlackList,
        FDOMAIN.FDOMAIN.fdomain_addr AS toAddr,
        FDOMAIN.FDOMAIN.fdomain_addr AS toLabel,
        "Folder" AS middleType,
        "cert_validate_fDomain" AS middleId,
        "d_FDOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_related_ip:cert_related_ip]->(IP:IP)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        IP.IP.ip_addr AS toLabel,
        "Folder" AS middleType,
        "cert_related_ip" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[issuer_related:issuer_related]->(ISSUER:ISSUER)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "ISSUER" AS toType,
        0 AS toBlackList,
        id(ISSUER) AS toAddr,
        ISSUER.ISSUER.common_name AS toLabel,
        "Folder" AS middleType,
        "issuer_related" AS middleId,
        "d_ISSUER" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[subject_related:subject_related]->(SUBJECT:SUBJECT)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "SUBJECT" AS toType,
        0 AS toBlackList,
        id(SUBJECT) AS toAddr,
        SUBJECT.SUBJECT.common_name AS toLabel,
        "Folder" AS middleType,
        "subject_related" AS middleId,
        "d_SUBJECT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_belong_app:cert_belong_app]->(APP:APP)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "APP" AS toType,
        0 AS toBlackList,
        id(APP) AS toAddr,
        id(APP) AS toLabel,
        "Folder" AS middleType,
        "cert_belong_app" AS middleId,
        "d_APP" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_url_related:cert_url_related]->(URL:URL)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "URL" AS toType,
        URL.URL.black_list AS toBlackList,
        URL.URL.url_key AS toAddr,
        URL.URL.url_key AS toLabel,
        "Folder" AS middleType,
        "cert_url_related" AS middleId,
        "d_URL" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5
    </select>

    <select id="listCertAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (CERT:CERT)
        WHERE id(CERT) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        CERT.CERT.cert_id AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'server_use_cert'){
                UNION ALL
                MATCH
                (IP:IP)-[server_use_cert:server_use_cert]->(CERT:CERT)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_belong_to_org'){
                UNION ALL
                MATCH
                (CERT:CERT)-[cert_belong_to_org:cert_belong_to_org]->(ORG:ORG)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND ORG.ORG.black_list >= ${edgeItem.weightLimit[0]} AND ORG.ORG.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "ORG" AS toType,
                ORG.ORG.black_list AS toBlackList,
                id(ORG) AS toAddr,
                ORG.ORG.org_name AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(ORG) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_use_cert'){
                UNION ALL
                MATCH
                (CERT:CERT)-[client_use_cert:client_use_cert]-(IP:IP)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'client_connect_cert'){
                UNION ALL
                MATCH
                (CERT:CERT)-[client_connect_cert:client_connect_cert]-(IP:IP)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'sni_bind'){
                UNION ALL
                MATCH
                (CERT:CERT)-[sni_bind:sni_bind]-(DOMAIN:DOMAIN)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'sslfinger_connect_cert'){
                UNION ALL
                MATCH
                (CERT:CERT)-[sslfinger_connect_cert:sslfinger_connect_cert]-(SSLFINGER:SSLFINGER)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND SSLFINGER.SSLFINGER.black_list >= ${edgeItem.weightLimit[0]} AND SSLFINGER.SSLFINGER.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "SSLFINGER" AS fromType,
                0 AS fromBlackList,
                SSLFINGER.SSLFINGER.finger_id AS fromAddr,
                SSLFINGER.SSLFINGER.finger_id AS fromLabel,
                "CERT" AS toType,
                CERT.CERT.black_list AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(SSLFINGER) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'issuer_related'){
                UNION ALL
                MATCH
                (CERT:CERT)-[issuer_related:issuer_related]->(ISSUER:ISSUER)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "ISSUER" AS toType,
                0 AS toBlackList,
                id(ISSUER) AS toAddr,
                ISSUER.ISSUER.common_name AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(ISSUER) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'subject_related'){
                UNION ALL
                MATCH
                (CERT:CERT)-[subject_related:subject_related]->(SUBJECT:SUBJECT)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "SUBJECT" AS toType,
                0 AS toBlackList,
                id(SUBJECT) AS toAddr,
                SUBJECT.SUBJECT.common_name AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(SUBJECT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_belong_app'){
                UNION ALL
                MATCH
                (CERT:CERT)-[cert_belong_app:cert_belong_app]->(APP:APP)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "APP" AS toType,
                0 AS toBlackList,
                id(APP) AS toAddr,
                id(APP) AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(APP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_sign_cert'){
                UNION ALL
                MATCH
                (CERT:CERT)-[cert_sign_cert:cert_sign_cert]->(CERT1:CERT)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "CERT" AS toType,
                0 AS toBlackList,
                CERT1.CERT.cert_id AS toAddr,
                CERT1.CERT.cert_id AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(CERT1) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'r_cert_sign_cert'){
                UNION ALL
                MATCH
                (CERT1:CERT)-[cert_sign_cert:cert_sign_cert]-(CERT:CERT)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT1.CERT.black_list AS fromBlackList,
                CERT1.CERT.cert_id AS fromAddr,
                CERT1.CERT.cert_id AS fromLabel,
                "CERT" AS toType,
                0 AS toBlackList,
                CERT.CERT.cert_id AS toAddr,
                CERT.CERT.cert_id AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(CERT1) AS vInfo,
                "cert_sign_cert" AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'special_business_port_service'){
                UNION ALL
                MATCH
                (CERT:CERT)-[special_business_port_service:special_business_port_service]->(APPSERVICE:APPSERVICE)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "APPSERVICE" AS toType,
                0 AS toBlackList,
                id(APPSERVICE) AS toAddr,
                APPSERVICE.APPSERVICE.AppName AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(APPSERVICE) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_validate_domain'){
                UNION ALL
                MATCH
                (CERT:CERT)-[cert_validate_domain:cert_validate_domain]->(DOMAIN:DOMAIN)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "DOMAIN" AS toType,
                DOMAIN.DOMAIN.black_list AS toBlackList,
                DOMAIN.DOMAIN.domain_addr AS toAddr,
                DOMAIN.DOMAIN.domain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_validate_fDomain'){
                UNION ALL
                MATCH
                (CERT:CERT)-[cert_validate_fDomain:cert_validate_fDomain]->(FDOMAIN:FDOMAIN)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "FDOMAIN" AS toType,
                0 AS toBlackList,
                FDOMAIN.FDOMAIN.fdomain_addr AS toAddr,
                FDOMAIN.FDOMAIN.fdomain_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(FDOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_related_ip'){
                UNION ALL
                MATCH
                (CERT:CERT)-[cert_related_ip:cert_related_ip]->(IP:IP)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "IP" AS toType,
                IP.IP.black_list AS toBlackList,
                IP.IP.ip_addr AS toAddr,
                IP.IP.ip_addr AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_url_related'){
                UNION ALL
                MATCH
                (CERT:CERT)-[cert_url_related:cert_url_related]->(URL:URL)
                WHERE
                id(CERT) == ${ng.valueFmt(condition.str)}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "URL" AS toType,
                URL.URL.black_list AS toBlackList,
                URL.URL.url_key AS toAddr,
                URL.URL.url_key AS toLabel,
                false as sourceStatus,
                false as directionStatus,
                properties(URL) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

    <select id="listCertNebulaNextByProperties" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">

    </select>

    <select id="listExistCertProperties" resultType="com.geeksec.graph.vo.ExistCertPropertiesVo">
        FETCH PROP ON CERT ${ ng.join(certs,",","ng.valueFmt") }
        YIELD properties(vertex).cert_id AS cert_id
        | YIELD $-.cert_id AS certId where $-.cert_id is not null
    </select>

    <select id="listByCerts" resultType="com.geeksec.graph.vo.CertVo">
        MATCH (A:CERT)
        WHERE id(A) IN ${certs}
        RETURN A.CERT.cert_id AS certId,
        A.CERT.first_seen AS firstTime,
        A.CERT.last_seen AS lastTime
    </select>

    <select id="listRelatedIpsByCerts" resultType="com.geeksec.graph.vo.CertRelatedIpsVo">
        GO FROM ${ ng.join(certs,",","ng.valueFmt") }
        OVER client_connect_cert REVERSELY
        YIELD properties($^).cert_id AS cert_id, properties($$).ip_addr AS ip_addr
        | GROUP BY $-.cert_id
        YIELD $-.cert_id AS certId, collect($-.ip_addr) AS ipList
    </select>

    <select id="countDomainNumByCerts" resultType="com.geeksec.graph.vo.CertCountVo">
        MATCH (A:DOMAIN)-[e:sni_bind]->(B:CERT)
        WHERE id(B) IN ${certs}
        RETURN B.CERT.cert_id AS certId, count(A) AS count
    </select>

    <select id="countServerIpNumByCerts" resultType="com.geeksec.graph.vo.CertCountVo">
        MATCH (A:IP)-[e:server_use_cert]->(B:CERT)
        WHERE id(B) IN ${certs}
        RETURN B.CERT.cert_id AS certId, count(A) AS count
    </select>

    <select id="countSslIpNumByCerts" resultType="com.geeksec.graph.vo.CertCountVo">
        MATCH (A:SSLFINGER)-[e:sslfinger_connect_cert]->(B:CERT)
        WHERE id(B) IN ${certs}
        RETURN B.CERT.cert_id AS certId, count(A) AS count
    </select>

    <select id="getCertInfo" resultType="java.util.Map">
        MATCH (cert:CERT)
        WHERE id(cert) == $cert
        RETURN cert.CERT.cert_id AS cert,
        cert.CERT.remark AS remark,
        cert.CERT.first_seen AS firstTime,
        cert.CERT.last_seen AS lastTime,
        cert.CERT.black_list AS blackList,
        cert.CERT.white_list AS whiteList
    </select>

    <select id="listHasLabelByCert" resultType="java.lang.String">
        MATCH (A:CERT)-[e:has_label]->(B:LABEL)
        WHERE id(A) == $cert
        RETURN DISTINCT B.LABEL.label_id AS labelId
    </select>

    <select id="listRelatedServerIpsByCert" resultType="java.lang.String">
        MATCH (A:IP)-[e:server_use_cert]->(B:CERT)
        WHERE id(B) == $cert
        RETURN A.IP.ip_addr AS ipKey
    </select>

    <select id="listRelatedClientIpsByCert" resultType="java.lang.String">
        MATCH (A:IP)-[e:client_connect_cert]->(B:CERT)
        WHERE id(B) == $cert
        RETURN A.IP.ip_addr AS ipKey
    </select>

    <select id="getTaskIds" resultType="java.lang.String">
        MATCH (A:CERT)-[e:task_belong_to]->(B:TASK)
        WHERE id(A) == $cert
        RETURN B.TASK.task_name AS task_name
    </select>

    <select id="listRelatedIpsByCert" resultType="com.geeksec.graph.vo.CertRelatedIpVo">
        GO FROM ${ng.valueFmt(certId)}
        OVER server_use_cert REVERSELY
        YIELD properties($^).cert_id AS certId,
        properties($$).ip_addr AS ipAddr,
        properties(edge).last_time AS lastTime
    </select>

</mapper>
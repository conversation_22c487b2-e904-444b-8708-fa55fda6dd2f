<mapper namespace="com.geeksec.graph.repository.MacDao">

    <select id="listMacAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (MAC:MAC)
        WHERE id(MAC) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "MAC" AS fromType,
        MAC.MAC.black_list AS fromBlackList,
        MAC.MAC.mac AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'connect_mac'){
                UNION ALL
                MATCH
                (MAC:MAC)-[connect_mac:connect_mac]->(MAC1:MAC)
                WHERE
                id(MAC) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND MAC1.MAC.black_list >= ${edgeItem.weightLimit[0]} AND MAC1.MAC.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "MAC" AS fromType,
                MAC.MAC.black_list AS fromBlackList,
                MAC.MAC.mac AS fromAddr,
                "MAC" AS toType,
                MAC1.MAC.black_list AS toBlackList,
                MAC1.MAC.mac AS toAddr,
                false as sourceStatus,
                false as directionStatus,
                properties(MAC1) AS vInfo,
                "connect_mac" AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'src_bind'){
                UNION ALL
                MATCH
                (MAC:MAC)-[src_bind:src_bind]-(IP:IP)
                WHERE
                id(MAC) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                "MAC" AS toType,
                MAC.MAC.black_list AS toBlackList,
                MAC.MAC.mac AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'dst_bind'){
                UNION ALL
                MATCH
                (MAC:MAC)-[dst_bind:dst_bind]-(IP:IP)
                WHERE
                id(MAC) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                "MAC" AS toType,
                MAC.MAC.black_list AS toBlackList,
                MAC.MAC.mac AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'r_connect_mac'){
                UNION ALL
                MATCH
                (MAC1:MAC)-[connect_mac:connect_mac]->(MAC:MAC)
                WHERE
                id(MAC) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND MAC1.MAC.black_list >= ${edgeItem.weightLimit[0]} AND MAC1.MAC.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "MAC" AS fromType,
                MAC1.MAC.black_list AS fromBlackList,
                MAC1.MAC.mac AS fromAddr,
                "MAC" AS toType,
                MAC.MAC.black_list AS toBlackList,
                MAC.MAC.mac AS toAddr,
                false as sourceStatus,
                true as directionStatus,
                properties(MAC) AS vInfo,
                "connect_mac" AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

</mapper>
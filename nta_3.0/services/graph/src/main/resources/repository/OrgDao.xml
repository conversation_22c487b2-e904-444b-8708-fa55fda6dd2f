<mapper namespace="com.geeksec.graph.repository.OrgDao">

    <select id="listOrgAllEdgeTypeAssociationNext" resultType="com.geeksec.graph.vo.VertexEdgeNextVo">
        MATCH (ORG:ORG)
        WHERE id(ORG) == ${ng.valueFmt(condition.str)}
        RETURN DISTINCT
        "ORG" AS fromType,
        ORG.ORG.black_list AS fromBlackList,
        id(ORG) AS fromAddr,
        ORG.ORG.org_name AS fromLabel,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS toLabel,
        true as sourceStatus,
        false as directionStatus,
        null AS vInfo,
        "" AS teage
        LIMIT 1

        @for( edgeItem in condition.edgeInfo ) {
            @if (edgeItem.edge == 'ip_belong_to_org'){
                UNION ALL
                MATCH
                (ORG:ORG)-[ip_belong_to_org:ip_belong_to_org]-(IP:IP)
                WHERE
                id(ORG) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND IP.IP.black_list >= ${edgeItem.weightLimit[0]} AND IP.IP.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "IP" AS fromType,
                IP.IP.black_list AS fromBlackList,
                IP.IP.ip_addr AS fromAddr,
                IP.IP.ip_addr AS fromLabel,
                "ORG" AS toType,
                ORG.ORG.black_list AS toBlackList,
                id(ORG) AS toAddr,
                ORG.ORG.org_name AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(IP) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'domain_belong_to_org'){
                UNION ALL
                MATCH
                (ORG:ORG)-[domain_belong_to_org:domain_belong_to_org]-(DOMAIN:DOMAIN)
                WHERE
                id(ORG) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND DOMAIN.DOMAIN.black_list >= ${edgeItem.weightLimit[0]} AND DOMAIN.DOMAIN.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "DOMAIN" AS fromType,
                DOMAIN.DOMAIN.black_list AS fromBlackList,
                DOMAIN.DOMAIN.domain_addr AS fromAddr,
                DOMAIN.DOMAIN.domain_addr AS fromLabel,
                "ORG" AS toType,
                ORG.ORG.black_list AS toBlackList,
                id(ORG) AS toAddr,
                ORG.ORG.org_name AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(DOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'fDomain_belong_to_org'){
                UNION ALL
                MATCH
                (ORG:ORG)-[fDomain_belong_to_org:fDomain_belong_to_org]-(FDOMAIN:FDOMAIN)
                WHERE
                id(ORG) == ${ng.valueFmt(condition.str)}
                RETURN
                "FDOMAIN" AS fromType,
                0 AS fromBlackList,
                FDOMAIN.FDOMAIN.fdomain_addr AS fromAddr,
                FDOMAIN.FDOMAIN.fdomain_addr AS fromLabel,
                "ORG" AS toType,
                ORG.ORG.black_list AS toBlackList,
                id(ORG) AS toAddr,
                ORG.ORG.org_name AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(FDOMAIN) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
            @if (edgeItem.edge == 'cert_belong_to_org'){
                UNION ALL
                MATCH
                (ORG:ORG)-[cert_belong_to_org:cert_belong_to_org]-(CERT:CERT)
                WHERE
                id(ORG) == ${ng.valueFmt(condition.str)}
                @if (edgeItem.weightLimit[0] != 0 && edgeItem.weightLimit[1] != 100){
                    AND CERT.CERT.black_list >= ${edgeItem.weightLimit[0]} AND CERT.CERT.black_list <= ${edgeItem.weightLimit[1]}
                @}
                RETURN
                "CERT" AS fromType,
                CERT.CERT.black_list AS fromBlackList,
                CERT.CERT.cert_id AS fromAddr,
                CERT.CERT.cert_id AS fromLabel,
                "ORG" AS toType,
                ORG.ORG.black_list AS toBlackList,
                id(ORG) AS toAddr,
                ORG.ORG.org_name AS toLabel,
                false as sourceStatus,
                true as directionStatus,
                properties(CERT) AS vInfo,
                ${ng.valueFmt(edgeItem.edge)} AS teage
                LIMIT ${edgeItem.num}
            @}
        @}
    </select>

    <select id="getOrg" resultType="com.geeksec.graph.vo.OrgVo">
        MATCH (ORG:ORG)
        WHERE id(ORG) == ${ng.valueFmt(orgId)}
        RETURN
        id(ORG) AS vid,
        ORG.ORG.org_name AS orgName,
        ORG.ORG.org_desc AS orgDesc,
        ORG.ORG.black_list AS blackList,
        ORG.ORG.white_list AS whiteList,
        ORG.ORG.remark AS remark
    </select>

</mapper>
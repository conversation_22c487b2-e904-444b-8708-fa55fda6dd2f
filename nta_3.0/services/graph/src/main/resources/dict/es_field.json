{"conn": {"HandleBeginTime": {"filedName": "HandleBeginTime", "probe": 0, "analysis": 0, "Name": "处理起始时间", "Remark": "处理开始时间", "type": "int"}, "HandleEndTime": {"filedName": "HandleEndTime", "probe": 0, "analysis": 0, "Name": "处理结束时间", "Remark": "处理结束时间", "type": "int"}, "sIp": {"filedName": "sIp", "probe": 0, "analysis": 1, "Name": "源IP", "Remark": "源IP地址", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 1, "Name": "源端口", "Remark": "源端口", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 1, "Name": "目的IP", "Remark": "目的IP地址", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 1, "Name": "目的端口", "Remark": "目的端口", "type": "int"}, "ProxyIP": {"filedName": "ProxyIP", "probe": 0, "analysis": 1, "Name": "代理IP", "Remark": "代理IP", "type": "string"}, "ProxyPort": {"filedName": "ProxyPort", "probe": 0, "analysis": 1, "Name": "代理端口", "Remark": "代理端口", "type": "INT"}, "IPPro": {"filedName": "IPPro", "probe": 0, "analysis": 0, "Name": "IP协议号", "Remark": "IP协议号", "type": "int"}, "Labels": {"filedName": "Labels", "probe": 0, "analysis": 1, "Name": "标签", "Remark": "标签数组", "type": "数组"}, "ProxyIPInfor": {"filedName": "ProxyIPInfor", "probe": 0, "analysis": 0, "Name": "代理IP标志", "Remark": "代理IP标志", "type": "int"}, "FirstSender": {"filedName": "FirstSender", "probe": 0, "analysis": 0, "Name": "首发IP", "Remark": "该会话中发送第一个包的IP", "type": "string"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 1, "Name": "会话ID", "Remark": "会话ID；全局唯一", "type": "string"}, "Hkey": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "存储标记", "Remark": "存储标记", "type": "string"}, "AppId": {"filedName": "AppId", "probe": 0, "analysis": 0, "Name": "应用协议ID", "Remark": "应用ID", "type": "int"}, "AppName": {"filedName": "AppName", "probe": 0, "analysis": 1, "Name": "应用", "Remark": "应用名", "type": "string"}, "ProName": {"filedName": "ProName", "probe": 0, "analysis": 1, "Name": "网络协议名", "Remark": "协议名", "type": "string"}, "DeviceID": {"filedName": "DeviceID", "probe": 0, "analysis": 0, "Name": "采集设备ID", "Remark": "当前的设备ID", "type": "int"}, "EthPortID": {"filedName": "EthPortID", "probe": 0, "analysis": 0, "Name": "网口ID", "Remark": "", "type": "int"}, "TaskId": {"filedName": "TaskId", "probe": 0, "analysis": 0, "Name": "任务ID", "Remark": "任务ID", "type": "int"}, "BatchNum": {"filedName": "BatchNum", "probe": 0, "analysis": 0, "Name": "批次ID", "Remark": "批次ID", "type": "int"}, "ThreadId": {"filedName": "ThreadId", "probe": 0, "analysis": 1, "Name": "处理线程号", "Remark": "线程ID", "type": "int"}, "Duration": {"filedName": "Duration", "probe": 0, "analysis": 1, "Name": "持续秒数", "Remark": "会话持续时间-秒", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 1, "Name": "起始时间", "Remark": "会话起始时间-秒", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 0, "analysis": 0, "Name": "起始时间_纳秒", "Remark": "会话起始时间-纳秒", "type": "int"}, "EndTime": {"filedName": "EndTime", "probe": 0, "analysis": 1, "Name": "结束时间", "Remark": "会话结束时间-秒", "type": "int"}, "EndNSec": {"filedName": "EndNSec", "probe": 0, "analysis": 0, "Name": "结束时间_纳秒", "Remark": "会话结束时间-纳秒", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 1, "Name": "入库时间", "Remark": "记录创建时间 -> 入库时间", "type": "int"}, "FirstProto": {"filedName": "FirstProto", "probe": 0, "analysis": 1, "Name": "首层协议ID", "Remark": "首层协议ID", "type": "int"}, "ProListNum": {"filedName": "ProListNum", "probe": 0, "analysis": 1, "Name": "协议层数", "Remark": "协议栈层级", "type": "int"}, "ProList.ProStack": {"filedName": "ProList.ProStack", "probe": 0, "analysis": 0, "Name": "协议栈详情", "Remark": "协议栈详细信息", "type": "json"}, "ExtJson": {"filedName": "ExtJson", "probe": 0, "analysis": 0, "Name": "json扩展信息", "Remark": "基于用户需求添加", "type": "json"}, "PktLenDist": {"filedName": "PktLenDist", "probe": 0, "analysis": 0, "Name": "长度分布", "Remark": "SSL负载长度分布", "type": "bytes[8]"}, "pkt.ProNum": {"filedName": "pkt.ProNum", "probe": 0, "analysis": 0, "Name": "已知协议包数", "Remark": "识别到应用协议的包数", "type": "int"}, "pkt.UnkProNum": {"filedName": "pkt.UnkProNum", "probe": 0, "analysis": 0, "Name": "未知协议包数", "Remark": "未识别的应用的包数", "type": "int"}, "pkt.sPayload": {"filedName": "pkt.sPayload", "probe": 0, "analysis": 0, "Name": "包信息.负载", "Remark": "包负载客户端端前4个，32字节", "type": "int"}, "pkt.dPayload": {"filedName": "pkt.dPayload", "probe": 0, "analysis": 0, "Name": "包信息.负载", "Remark": "包负载服务端前4个", "type": "int"}, "pkt.Infor": {"filedName": "pkt.Infor", "probe": 0, "analysis": 0, "Name": "包信息", "Remark": "包详细信息 前50个", "type": "json"}, "pkt.Infor.Count": {"filedName": "pkt.Infor.Count", "probe": 0, "analysis": 0, "Name": "包信息.包编号", "Remark": "包计数", "type": "int"}, "pkt.Infor.Len": {"filedName": "pkt.Infor.Len", "probe": 0, "analysis": 0, "Name": "包信息.负载字节数", "Remark": "当前包长，C2S为正，C2C为负数", "type": "int"}, "pkt.Infor.Sec": {"filedName": "pkt.Infor.Sec", "probe": 0, "analysis": 0, "Name": "包信息.获取时间", "Remark": "当前包时间-秒", "type": "int"}, "pkt.Infor.nSec": {"filedName": "pkt.Infor.nSec", "probe": 0, "analysis": 0, "Name": "包信息.获取时间_纳秒", "Remark": "当前包时间-纳秒", "type": "int"}, "pkt.SynData": {"filedName": "pkt.SynData", "probe": 0, "analysis": 0, "Name": "SYN包头", "Remark": "syn数据包数据", "type": "int"}, "pkt.APPCount": {"filedName": "pkt.APPCount", "probe": 0, "analysis": 0, "Name": "首次确认应用包数", "Remark": "第几个负载包识别到应用", "type": "int"}, "SbytesDiviDbytes": {"filedName": "SbytesDiviDbytes", "probe": 0, "analysis": 1, "Name": "源目的包数差异", "Remark": "上下行流量比", "type": "float"}, "TotalBytes": {"filedName": "TotalBytes", "probe": 0, "analysis": 1, "Name": "总字节数", "Remark": "流量总字节书", "type": "int"}, "pkt.sNum": {"filedName": "pkt.sNum", "probe": 0, "analysis": 0, "Name": "源端发送包数", "Remark": "会话中的包数", "type": "int"}, "pkt.sPayloadNum": {"filedName": "pkt.sPayloadNum", "probe": 0, "analysis": 1, "Name": "源端发送负载包数", "Remark": "会话中的有负载包数", "type": "int"}, "pkt.sBytes": {"filedName": "pkt.sBytes", "probe": 0, "analysis": 0, "Name": "源端发送字节数", "Remark": "会话中的总字节数", "type": "int"}, "pkt.sPayloadBytes": {"filedName": "pkt.sPayloadBytes", "probe": 0, "analysis": 1, "Name": "源端发送负载字节数", "Remark": "会话中的客户端发送负载字节数", "type": "int"}, "pkt.dNum": {"filedName": "pkt.dNum", "probe": 0, "analysis": 0, "Name": "目的端发送包数", "Remark": "会话中的包数", "type": "int"}, "pkt.dPayloadNum": {"filedName": "pkt.dPayloadNum", "probe": 0, "analysis": 1, "Name": "目的端发送负载包数", "Remark": "会话中的服务端发送负载包数", "type": "int"}, "pkt.dBytes": {"filedName": "pkt.dBytes", "probe": 0, "analysis": 0, "Name": "目的端发送字节数", "Remark": "会话中的总字节数", "type": "int"}, "pkt.dPayloadBytes": {"filedName": "pkt.dPayloadBytes", "probe": 0, "analysis": 1, "Name": "目的端发送负载字节数", "Remark": "会话中的负载字节数", "type": "int"}, "TCPGather": {"filedName": "T<PERSON><PERSON>ather", "probe": 0, "analysis": 0, "Name": "TCP聚合", "Remark": "TCP协议包聚合,基于Seq的连续性", "type": "数组"}, "TCPGather.Bytes": {"filedName": "TCPGather.Bytes", "probe": 0, "analysis": 0, "Name": "聚合字节数", "Remark": "字节数", "type": "int"}, "TCPGather.PacketNum": {"filedName": "TCPGather.PacketNum", "probe": 0, "analysis": 0, "Name": "聚合包数", "Remark": "包数", "type": "int"}, "TCPGather.PSHNum": {"filedName": "TCPGather.PSHNum", "probe": 0, "analysis": 0, "Name": "PSH次数", "Remark": "FLAG：PUSH包数", "type": "int"}, "TCPGather.ACK": {"filedName": "TCPGather.ACK", "probe": 0, "analysis": 0, "Name": "ACK次数", "Remark": "对应的确认号", "type": "int"}, "TCPGather.MinSeq": {"filedName": "TCPGather.MinSeq", "probe": 0, "analysis": 0, "Name": "最小序列号", "Remark": "最小序列号", "type": "int"}, "TCPGather.MaxSeq": {"filedName": "TCPGather.MaxSeq", "probe": 0, "analysis": 0, "Name": "最大序列号", "Remark": "最大序列号", "type": "int"}, "TotalPacketNum": {"filedName": "TotalPacketNum", "probe": 0, "analysis": 1, "Name": "总包数", "Remark": "总包数", "type": "int"}, "PktIntervalTimeDist": {"filedName": "PktIntervalTimeDist", "probe": 0, "analysis": 0, "Name": "包间隔时间分布", "Remark": "时间在2^n秒的分布", "type": "数组"}, "SynSeqList": {"filedName": "SynSeqList", "probe": 0, "analysis": 0, "Name": "SYN包序列号", "Remark": "SYN的序列号取值", "type": "数组"}, "SynNum": {"filedName": "SynNum", "probe": 0, "analysis": 0, "Name": "SYN包数", "Remark": "标识符为SYN的包数", "type": "int"}, "sMac": {"filedName": "sMac", "probe": 0, "analysis": 1, "Name": "源MAC", "Remark": "源MAC", "type": "string"}, "sSSLFinger": {"filedName": "s<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "源SSL指纹", "Remark": "客户端SSL指纹", "type": "string"}, "sHTTPFinger": {"filedName": "sHTTPFinger", "probe": 0, "analysis": 0, "Name": "源HTTP指纹", "Remark": "客户端HTTP指纹；UserAgent", "type": "string"}, "sIpCity": {"filedName": "sIpCity", "probe": 0, "analysis": 1, "Name": "源IP所在城市", "Remark": "IP地址所在城市", "type": "string"}, "sIpCountry": {"filedName": "sIpCountry", "probe": 0, "analysis": 1, "Name": "源IP所在国家", "Remark": "IP地址所在国家", "type": "string"}, "sIpSubdivisions": {"filedName": "sIpSubdivisions", "probe": 0, "analysis": 1, "Name": "源IP所在省份", "Remark": "IP地址的省份", "type": "string"}, "pkt.sMaxLen": {"filedName": "pkt.sMaxLen", "probe": 0, "analysis": 0, "Name": "源端发送最大包长", "Remark": "会话中的最大包长", "type": "int"}, "pkt.sPSHNum": {"filedName": "pkt.sPSHNum", "probe": 0, "analysis": 0, "Name": "源端发送PSH次数", "Remark": "会话中：TCP:flag含push的包数", "type": "int"}, "pkt.sFINNum": {"filedName": "pkt.sFINNum", "probe": 0, "analysis": 0, "Name": "源端发送FIN次数", "Remark": "会话中：TCP:flag含fin的包数", "type": "int"}, "pkt.sRSTNum": {"filedName": "pkt.sRSTNum", "probe": 0, "analysis": 0, "Name": "源端发送RST次数", "Remark": "会话中：TCP:flag含rst的包数", "type": "int"}, "pkt.sSYNNum": {"filedName": "pkt.sSYNNum", "probe": 0, "analysis": 0, "Name": "源端发送SYN次数", "Remark": "会话中：TCP:flag含syn的包数", "type": "int"}, "pkt.sSYNBytes": {"filedName": "pkt.sSYNBytes", "probe": 0, "analysis": 0, "Name": "源端发送SYN包长", "Remark": "会话中：syn包的字节数", "type": "int"}, "pkt.sTTLMax": {"filedName": "pkt.sTTLMax", "probe": 0, "analysis": 0, "Name": "源端发送最大TTL", "Remark": "ttl最大值", "type": "int"}, "pkt.sTTLMin": {"filedName": "pkt.sTTLMin", "probe": 0, "analysis": 0, "Name": "源端发送最小TTL", "Remark": "ttl最小值", "type": "int"}, "sIpIdOffset": {"filedName": "sIpIdOffset", "probe": 0, "analysis": 0, "Name": "源端发送IPID分布", "Remark": "Ipid的分布", "type": "数组"}, "sMinHopCount": {"filedName": "sMinHopCount", "probe": 0, "analysis": 1, "Name": "源端发送TTL最小距离", "Remark": "TTL最小距离距离", "type": "int"}, "sMaxHopCount": {"filedName": "sMaxHopCount", "probe": 0, "analysis": 1, "Name": "源端发送TTL最大距离", "Remark": "会话中的最大距离", "type": "int"}, "sInitialTTL": {"filedName": "sInitialTTL", "probe": 0, "analysis": 1, "Name": "源端发送原始TTL", "Remark": "TTL基线；2的N次方", "type": "int"}, "dMac": {"filedName": "dMac", "probe": 0, "analysis": 1, "Name": "目的MAC", "Remark": "目的MAC", "type": "string"}, "dSSLFinger": {"filedName": "d<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "目的SSL指纹", "Remark": "服务端SSL指纹", "type": "string"}, "dHTTPFinger": {"filedName": "dHTTPFinger", "probe": 0, "analysis": 0, "Name": "目的HTTP指纹", "Remark": "服务端HTTP指纹；UserAgent", "type": "string"}, "dIpCity": {"filedName": "dIpCity", "probe": 0, "analysis": 1, "Name": "目的IP所在城市", "Remark": "IP地址所在城市", "type": "string"}, "dIpCountry": {"filedName": "dIpCountry", "probe": 0, "analysis": 1, "Name": "目的IP所在国家", "Remark": "IP地址所在国家", "type": "string"}, "dIpSubdivisions": {"filedName": "dIpSubdivisions", "probe": 0, "analysis": 1, "Name": "目的IP所在省份", "Remark": "IP所在省份", "type": "int"}, "pkt.dMaxLen": {"filedName": "pkt.dMaxLen", "probe": 0, "analysis": 0, "Name": "目的端发送最大包长", "Remark": "会话中的最大包长", "type": "int"}, "pkt.dFinNum": {"filedName": "pkt.dFinNum", "probe": 0, "analysis": 0, "Name": "目的端发送FIN次数", "Remark": "会话中：TCP:flag含fin的包数", "type": "int"}, "pkt.dRSTNum": {"filedName": "pkt.dRSTNum", "probe": 0, "analysis": 0, "Name": "目的端发送RST次数", "Remark": "会话中：TCP:flag含rst的包数", "type": "int"}, "pkt.dSYNNum": {"filedName": "pkt.dSYNNum", "probe": 0, "analysis": 0, "Name": "目的端发送SYN次数", "Remark": "会话中：TCP:flag含的SYN包数", "type": "int"}, "pkt.dSYNBytes": {"filedName": "pkt.dSYNBytes", "probe": 0, "analysis": 0, "Name": "目的端发送SYN字节数", "Remark": "会话中：syn包的字节数", "type": "int"}, "dTTLMax": {"filedName": "dTTLMax", "probe": 0, "analysis": 1, "Name": "目的端发送最大TTL", "Remark": "ttl最大值", "type": "int"}, "dTTLMin": {"filedName": "dTTLMin", "probe": 0, "analysis": 1, "Name": "目的端发送最小TTL", "Remark": "ttl最小值", "type": "int"}, "pkt.dPSHNum": {"filedName": "pkt.dPSHNum", "probe": 0, "analysis": 0, "Name": "目的端发送PUSH次数", "Remark": "会话中：TCP:flag含push的包数", "type": "int"}, "dIpIdOffset": {"filedName": "dIpIdOffset", "probe": 0, "analysis": 0, "Name": "目的端发送IPID分布", "Remark": "Ipid的分布", "type": "数组"}, "dInitialTTL": {"filedName": "dInitialTTL", "probe": 0, "analysis": 1, "Name": "目的端发送原始TTL", "Remark": "TTL基线；2的N次方", "type": "int"}, "dMinHopCount": {"filedName": "dMinHopCount", "probe": 0, "analysis": 0, "Name": "目的端发送TTL最小距离", "Remark": "TTL距离", "type": "int"}, "dMaxHopCount": {"filedName": "dMaxHopCount", "probe": 0, "analysis": 0, "Name": "目的端发送发送TTL最大距离", "Remark": "会话中的最大距离", "type": "int"}, "sDistLen": {"filedName": "sDistLen", "probe": 0, "analysis": 0, "Name": "源包长分布", "Remark": "上下行包长分布;uint32_t distLen[2][8];", "type": "数组"}, "dDistLen": {"filedName": "dDistLen", "probe": 0, "analysis": 0, "Name": "目的包长分布", "Remark": "", "type": "数组"}, "ip_port_ipport_appid": {"filedName": "ip_port_ipport_appid", "probe": 0, "analysis": 1, "Name": "", "Remark": "目的ip  port ippro appid 联合索引", "type": "string"}, "mac2mac": {"filedName": "mac2mac", "probe": 0, "analysis": 1, "Name": "", "Remark": "mac 到mac 的联合索引", "type": "string"}, "ip2ip": {"filedName": "ip2ip", "probe": 0, "analysis": 1, "Name": "IP对", "Remark": "IP对直接索引；按ip字符从小到大的顺序排列", "type": "string"}, "sip_appid_dport_dip": {"filedName": "sip_appid_dport_dip", "probe": 0, "analysis": 1, "Name": "", "Remark": "源IP appid  目标端口 目标IP 联合索引", "type": "string"}, "HTTP": {"filedName": "HTTP", "probe": 0, "analysis": 1, "Name": "http协议扩展字段", "Remark": "http协议扩展字段", "type": "数组"}, "HTTP.Url": {"filedName": "HTTP.Url", "probe": 0, "analysis": 1, "Name": "http url", "Remark": "http url", "type": "string"}, "HTTP.Act": {"filedName": "HTTP.Act", "probe": 0, "analysis": 1, "Name": "http Act", "Remark": "http Act", "type": "string"}, "HTTP.Host": {"filedName": "HTTP.Host", "probe": 0, "analysis": 1, "Name": "主站", "Remark": "主站", "type": "string"}, "HTTP.User-Agent": {"filedName": "HTTP.User-Agent", "probe": 0, "analysis": 1, "Name": "客户端.客户端信息", "Remark": "客户端.客户端信息", "type": "string"}, "HTTP.Response": {"filedName": "HTTP.Response", "probe": 0, "analysis": 1, "Name": "回应类型", "Remark": "回应类型", "type": "string"}, "DNS": {"filedName": "DNS", "probe": 0, "analysis": 1, "Name": "", "Remark": "", "type": "数组"}, "DNS.Domain": {"filedName": "DNS.Domain", "probe": 0, "analysis": 1, "Name": "本次询问域名", "Remark": "本次询问域名", "type": "string"}, "DNS.DomainIp": {"filedName": "DNS.DomainIp", "probe": 0, "analysis": 1, "Name": "本次答复的IP地址-- 用分割符分割", "Remark": "本次答复的IP地址", "type": "数组"}, "SSL.CH_Ciphersuit": {"filedName": "SSL.CH_Ciphersuit", "probe": 0, "analysis": 1, "Name": "ClientHello密码套件", "Remark": "ClientHello密码套件", "type": "string"}, "SSL.CH_CiphersuitNum": {"filedName": "SSL.CH_CiphersuitNum", "probe": 0, "analysis": 1, "Name": "ClientHello密码套件数量", "Remark": "ClientHello密码套件数量", "type": "int"}, "SSL.CH_ServerName": {"filedName": "SSL.CH_ServerName", "probe": 0, "analysis": 1, "Name": "服务器名", "Remark": "服务器名", "type": "string"}, "SSL.CH_ALPN": {"filedName": "SSL.CH_ALPN", "probe": 0, "analysis": 1, "Name": "应用协议类型", "Remark": "应用协议类型", "type": "json"}, "SSL.sCertHash": {"filedName": "SSL.sCertHash", "probe": 0, "analysis": 1, "Name": "客户端证书Hash列表", "Remark": "客户端证书Hash列表", "type": "string"}, "SSL.dCertHash": {"filedName": "SSL.dCertHash", "probe": 0, "analysis": 1, "Name": "服务端证书Hash", "Remark": "服务端证书Hash", "type": "string"}, "PktNumApp": {"filedName": "PktNumApp", "probe": 0, "analysis": 0, "Name": "识别应用包数", "Remark": "pkt.ProNum", "type": "int"}}, "dns": {"sIp": {"filedName": "sIp", "probe": 0, "analysis": 1, "Name": "源IP", "Remark": "源IP地址", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 1, "Name": "源端口", "Remark": "源端口", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 1, "Name": "目的IP", "Remark": "目的IP地址", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 1, "Name": "目的端口", "Remark": "目的端口", "type": "int"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 1, "Name": "会话ID", "Remark": "会话ID：全局唯一", "type": "string"}, "AppName": {"filedName": "AppName", "probe": 0, "analysis": 1, "Name": "应用", "Remark": "应用名", "type": "string"}, "TaskId": {"filedName": "TaskId", "probe": 0, "analysis": 1, "Name": "任务ID", "Remark": "任务ID", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 1, "Name": "起始时间", "Remark": "会话起始时间-秒", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 0, "analysis": 1, "Name": "起始时间_纳秒", "Remark": "会话起始时间-纳秒", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 1, "Name": "入库时间", "Remark": "记录创建时间", "type": "int"}, "Flags": {"filedName": "Flags", "probe": 0, "analysis": 1, "Name": "DNS标志位", "Remark": "DNS标志位", "type": "int"}, "Que": {"filedName": "Que", "probe": 0, "analysis": 1, "Name": "询问信息数量", "Remark": "询问信息数量", "type": "int"}, "Ans": {"filedName": "<PERSON>s", "probe": 0, "analysis": 1, "Name": "回答信息数量", "Remark": "回答信息数量", "type": "int"}, "Auth": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "认证信息数量", "Remark": "认证信息数量", "type": "int"}, "Add": {"filedName": "Add", "probe": 0, "analysis": 1, "Name": "附加信息数量", "Remark": "附加信息数量", "type": "int"}, "Query": {"filedName": "Query", "probe": 0, "analysis": 1, "Name": "询问", "Remark": "询问信息", "type": "json"}, "Query.class": {"filedName": "Query.class", "probe": 0, "analysis": 1, "Name": "询问.登记", "Remark": "询问信息登记", "type": "int"}, "Query.name": {"filedName": "Query.name", "probe": 0, "analysis": 1, "Name": "询问.名称", "Remark": "询问信息名称", "type": "string"}, "Query.type": {"filedName": "Query.type", "probe": 0, "analysis": 1, "Name": "询问.类型", "Remark": "询问信息类型", "type": "int"}, "Answer": {"filedName": "Answer", "probe": 0, "analysis": 1, "Name": "回答", "Remark": "回答信息", "type": "json数组"}, "Answer.class": {"filedName": "Answer.class", "probe": 0, "analysis": 1, "Name": "回答.登记", "Remark": "回答信息登记", "type": "int"}, "Answer.name": {"filedName": "Answer.name", "probe": 0, "analysis": 1, "Name": "回答.询问内容", "Remark": "本次答复对应的询问值", "type": "string"}, "Answer.ttl": {"filedName": "Answer.ttl", "probe": 0, "analysis": 1, "Name": "回答.有效期", "Remark": "会话数据有效期", "type": "int"}, "Answer.type": {"filedName": "Answer.type", "probe": 0, "analysis": 1, "Name": "回答.类型", "Remark": "回答数据类型", "type": "int"}, "Answer.data_len": {"filedName": "Answer.data_len", "probe": 0, "analysis": 1, "Name": "回答.字节数", "Remark": "回答数据长度", "type": "int"}, "Answer.value": {"filedName": "Answer.value", "probe": 0, "analysis": 1, "Name": "回答.回应值", "Remark": "回答数据值", "type": "string"}, "Domain": {"filedName": "Domain", "probe": 0, "analysis": 1, "Name": "域名", "Remark": "本次询问域名", "type": "int"}, "DomainIp": {"filedName": "DomainIp", "probe": 0, "analysis": 1, "Name": "答复地址", "Remark": "本次答复的IP地址", "type": "数组"}, "Hkey": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "存储标记", "Remark": "存储标记", "type": "string"}}, "http": {"sIp": {"filedName": "sIp", "probe": 0, "analysis": 1, "Name": "源IP", "Remark": "源IP地址", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 1, "Name": "源端口", "Remark": "源端口", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 1, "Name": "目的IP", "Remark": "目的IP地址", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 1, "Name": "目的端口", "Remark": "目的端口", "type": "int"}, "ServerIP": {"filedName": "ServerIP", "probe": 0, "analysis": 1, "Name": "服务端IP", "Remark": "服务器IP", "type": "string"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 1, "Name": "会话ID", "Remark": "会话ID：全局唯一", "type": "string"}, "TaskId": {"filedName": "TaskId", "probe": 0, "analysis": 1, "Name": "任务ID", "Remark": "任务ID", "type": "int"}, "BatchNum": {"filedName": "BatchNum", "probe": 0, "analysis": 1, "Name": "批次ID", "Remark": "批次ID", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 1, "Name": "起始时间", "Remark": "会话起始时间-秒", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 0, "analysis": 1, "Name": "起始时间_纳秒", "Remark": "会话起始时间-纳秒", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 1, "Name": "入库时间", "Remark": "记录创建时间", "type": "string"}, "Url": {"filedName": "Url", "probe": 0, "analysis": 1, "Name": "网址", "Remark": "本次请求的URL", "type": "string"}, "Act": {"filedName": "Act", "probe": 0, "analysis": 1, "Name": "请求类型", "Remark": "本次请求动作类型-Get、Post", "type": "string"}, "Host": {"filedName": "Host", "probe": 0, "analysis": 0, "Name": "服务端信息", "Remark": "", "type": "string"}, "Response": {"filedName": "Response", "probe": 0, "analysis": 1, "Name": "回应类型", "Remark": "本次回应值", "type": "string"}, "sHTTPFinger": {"filedName": "sHTTPFinger", "probe": 0, "analysis": 0, "Name": "源端HTTP指纹", "Remark": "", "type": "string"}, "dHTTPFinger": {"filedName": "dHTTPFinger", "probe": 0, "analysis": 0, "Name": "目的HTTP指纹", "Remark": "", "type": "string"}, "Client.Accept": {"filedName": "Client.Accept", "probe": 0, "analysis": 1, "Name": "客户端.支持页面类型", "Remark": "请求的页面类型", "type": "string"}, "Client.Host": {"filedName": "Client.Host", "probe": 0, "analysis": 1, "Name": "客户端.站点信息", "Remark": "服务器主机信息", "type": "string"}, "Client.User-Agent": {"filedName": "Client.User-Agent", "probe": 0, "analysis": 1, "Name": "客户端.客户端信息", "Remark": "客户端信息", "type": "string"}, "Client.Title": {"filedName": "Client.Title", "probe": 0, "analysis": 1, "Name": "客户端.标头列表", "Remark": "Title列表-按出现顺序", "type": "数组"}, "Client.Payload": {"filedName": "Client.Payload", "probe": 0, "analysis": 0, "Name": "客户端.内容", "Remark": "请求的负责信息；前64字节", "type": "string"}, "Client.Cookie": {"filedName": "Client.<PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "客户端.cookie", "Remark": "cookie 内容", "type": "string"}, "Client.Content-Type": {"filedName": "Client.Content-Type", "probe": 0, "analysis": 1, "Name": "客户端.内容类型", "Remark": "客户端内容类型", "type": "string"}, "Server.Accept-Ranges": {"filedName": "Server.Accept-Ranges", "probe": 0, "analysis": 1, "Name": "服务端.允许断点续传", "Remark": "", "type": "string"}, "Server.Content-Encoding": {"filedName": "Server.Content-Encoding", "probe": 0, "analysis": 1, "Name": "服务端.编码格式", "Remark": "编码格式", "type": "string"}, "Server.Content-Length": {"filedName": "Server.Content-Length", "probe": 0, "analysis": 1, "Name": "服务端.字节数", "Remark": "内容字节数", "type": "string"}, "Server.Content-Type": {"filedName": "Server.Content-Type", "probe": 0, "analysis": 1, "Name": "服务端.内容类型", "Remark": "内容类型", "type": "string"}, "Server.Date": {"filedName": "Server.Date", "probe": 0, "analysis": 0, "Name": "服务端.时间", "Remark": "内容时间", "type": "string"}, "Server.Last-Modified": {"filedName": "Server.Last-Modified", "probe": 0, "analysis": 0, "Name": "服务端.修改时间", "Remark": "上次修改时间", "type": "string"}, "Server.Payload": {"filedName": "Server.Payload", "probe": 0, "analysis": 0, "Name": "服务端.内容", "Remark": "请求的负责信息；前64字节", "type": "string"}, "Server.Title": {"filedName": "Server.Title", "probe": 0, "analysis": 1, "Name": "服务端.标头列表", "Remark": "Title列表-按出现顺序", "type": "数组"}, "Hkey": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "存储标记", "Remark": "存储标记", "type": "string"}, "Sec-WebSocket-Version": {"filedName": "Sec-WebSocket-Version", "probe": 0, "analysis": 0, "Name": "websocket版本", "Remark": "", "type": "string"}, "Sec-WebSocket-Protocol": {"filedName": "Sec-WebSocket-Protocol", "probe": 0, "analysis": 0, "Name": "websocket协议", "Remark": "", "type": "string"}, "Sec-WebSocket-Key": {"filedName": "Sec-WebSocket-Key", "probe": 0, "analysis": 0, "Name": "websocket密钥", "Remark": "", "type": "string"}, "Sec-WebSocket-Accept": {"filedName": "Sec-WebSocket-Accept", "probe": 0, "analysis": 0, "Name": "websocket服务器确认", "Remark": "", "type": "string"}, "Sec-WebSocket-Extensions": {"filedName": "Sec-WebSocket-Extensions", "probe": 0, "analysis": 0, "Name": "websocket拓展信息", "Remark": "", "type": "string"}, "Get": {"filedName": "Get", "probe": 0, "analysis": 0, "Name": "获取", "Remark": "", "type": "string"}, "Post": {"filedName": "Post", "probe": 0, "analysis": 0, "Name": "提交", "Remark": "", "type": "string"}, "HTTP": {"filedName": "HTTP", "probe": 0, "analysis": 0, "Name": "响应", "Remark": "", "type": "string"}, "Referer": {"filedName": "<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "跳转链接", "Remark": "", "type": "string"}, "User-Agent": {"filedName": "User-Agent", "probe": 0, "analysis": 0, "Name": "客户端信息", "Remark": "", "type": "string"}, "Server": {"filedName": "Server", "probe": 0, "analysis": 0, "Name": "服务器软件信息", "Remark": "", "type": "string"}, "Date": {"filedName": "Date", "probe": 0, "analysis": 0, "Name": "当前时间", "Remark": "", "type": "string"}, "Cookie": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "<PERSON><PERSON>", "Remark": "", "type": "string"}, "Last-Modified": {"filedName": "Last-Modified", "probe": 0, "analysis": 0, "Name": "最终修改时间", "Remark": "", "type": "string"}, "Expires": {"filedName": "Expires", "probe": 0, "analysis": 0, "Name": "缓存有效周期", "Remark": "", "type": "string"}, "Content-Type": {"filedName": "Content-Type", "probe": 0, "analysis": 0, "Name": "正文类别", "Remark": "", "type": "string"}, "Content-Length": {"filedName": "Content-Length", "probe": 0, "analysis": 0, "Name": "正文长度", "Remark": "", "type": "string"}, "Content-Encoding": {"filedName": "Content-Encoding", "probe": 0, "analysis": 0, "Name": "正文编码", "Remark": "", "type": "string"}, "Content-Language": {"filedName": "Content-Language", "probe": 0, "analysis": 0, "Name": "正文语言", "Remark": "", "type": "string"}, "Connecttion": {"filedName": "Connecttion", "probe": 0, "analysis": 0, "Name": "传输模式", "Remark": "", "type": "string"}, "Accept": {"filedName": "Accept", "probe": 0, "analysis": 0, "Name": "浏览器支持的MIME类型", "Remark": "", "type": "string"}, "Accept-Language": {"filedName": "Accept-Language", "probe": 0, "analysis": 0, "Name": "浏览器支持的语言", "Remark": "", "type": "string"}, "Accept-Encoding": {"filedName": "Accept-Encoding", "probe": 0, "analysis": 0, "Name": "浏览器支持的编码方式", "Remark": "", "type": "string"}, "Origin": {"filedName": "Origin", "probe": 0, "analysis": 0, "Name": "最初请求来源", "Remark": "", "type": "string"}, "x-xss-Protection": {"filedName": "x-xss-Protection", "probe": 0, "analysis": 0, "Name": "xss防御状态", "Remark": "", "type": "string"}, "X-Content-Type-Options": {"filedName": "X-Content-Type-Options", "probe": 0, "analysis": 0, "Name": "服务器验证正文类型", "Remark": "", "type": "string"}, "x-frame-Options": {"filedName": "x-frame-Options", "probe": 0, "analysis": 0, "Name": "网站Frame防御状态", "Remark": "", "type": "string"}, "strict-transport-security": {"filedName": "strict-transport-security", "probe": 0, "analysis": 0, "Name": "仅支持HTTPS", "Remark": "", "type": "string"}, "public-key-Pins": {"filedName": "public-key-Pins", "probe": 0, "analysis": 0, "Name": "公钥固定", "Remark": "", "type": "string"}, "Age": {"filedName": "Age", "probe": 0, "analysis": 0, "Name": "代理服务器缓存时间", "Remark": "", "type": "string"}, "Etag": {"filedName": "Etag", "probe": 0, "analysis": 0, "Name": "是否加载缓存", "Remark": "", "type": "string"}, "Location": {"filedName": "Location", "probe": 0, "analysis": 0, "Name": "重定向", "Remark": "", "type": "string"}, "Proxy": {"filedName": "Proxy", "probe": 0, "analysis": 0, "Name": "代理", "Remark": "", "type": "string"}, "Retry-After": {"filedName": "Retry-After", "probe": 0, "analysis": 0, "Name": "再次请求等待时间", "Remark": "", "type": "string"}, "Vary": {"filedName": "Vary", "probe": 0, "analysis": 0, "Name": "缓存配置", "Remark": "", "type": "string"}, "www-Auth": {"filedName": "www-Auth", "probe": 0, "analysis": 0, "Name": "认证应答", "Remark": "", "type": "string"}, "upgrade": {"filedName": "upgrade", "probe": 0, "analysis": 0, "Name": "是否启用更高版本", "Remark": "", "type": "string"}}, "ssl": {"sIp": {"filedName": "sIp", "probe": 0, "analysis": 1, "Name": "源IP", "Remark": "源IP地址", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 1, "Name": "源端口", "Remark": "源端口", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 1, "Name": "目的IP", "Remark": "目的IP地址", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 1, "Name": "目的端口", "Remark": "目的端口", "type": "int"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 1, "Name": "会话ID", "Remark": "会话ID：全局唯一", "type": "string"}, "AppName": {"filedName": "AppName", "probe": 0, "analysis": 1, "Name": "应用名", "Remark": "应用；SSL下的应用协议", "type": "string"}, "TaskId": {"filedName": "TaskId", "probe": 0, "analysis": 1, "Name": "任务ID", "Remark": "任务ID", "type": "int"}, "BatchNum": {"filedName": "BatchNum", "probe": 0, "analysis": 1, "Name": "批次ID", "Remark": "", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 1, "Name": "起始时间", "Remark": "会话起始时间-秒", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 0, "analysis": 1, "Name": "起始时间_纳秒", "Remark": "会话起始时间-纳秒", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 1, "Name": "入库时间", "Remark": "记录创建时间", "type": "string"}, "cSSLVersion": {"filedName": "cSSLVersion", "probe": 0, "analysis": 1, "Name": "客户端版本号", "Remark": "SSL客户端版本号", "type": "int"}, "CH_Version": {"filedName": "CH_Version", "probe": 0, "analysis": 1, "Name": "ClientHello版本", "Remark": "ClientHello版本", "type": "int"}, "CH_Time": {"filedName": "CH_Time", "probe": 0, "analysis": 1, "Name": "ClientHello时间戳", "Remark": "ClientHello时间戳", "type": "int"}, "CH_Random": {"filedName": "CH_Random", "probe": 0, "analysis": 0, "Name": "ClientHello随机数", "Remark": "ClientHello的随机数", "type": "string"}, "CH_SessionID": {"filedName": "CH_SessionID", "probe": 0, "analysis": 0, "Name": "ClientHello会话ID", "Remark": "ClientHello的会话ID", "type": "string"}, "CH_SessionIDLen": {"filedName": "CH_SessionIDLen", "probe": 0, "analysis": 1, "Name": "ClientHello会话ID字节数", "Remark": "ClientHello的会话ID字节数", "type": "int"}, "CH_Ciphersuit": {"filedName": "CH_Ciphersuit", "probe": 0, "analysis": 1, "Name": "ClientHello密码套件", "Remark": "ClientHello的密码套件", "type": "string"}, "CH_CiphersuitNum": {"filedName": "CH_CiphersuitNum", "probe": 0, "analysis": 1, "Name": "ClientHello密码套件数量", "Remark": "ClientHello的密码套件数量-注意不是字节数", "type": "int"}, "CH_CompressionMethod": {"filedName": "CH_CompressionMethod", "probe": 0, "analysis": 1, "Name": "ClientHello压缩方法", "Remark": "ClientHello的压缩方法", "type": "string"}, "CH_CompressionMethodLen": {"filedName": "CH_CompressionMethodLen", "probe": 0, "analysis": 1, "Name": "ClientHello压缩方法数量", "Remark": "ClientHello的压缩方法数量", "type": "int"}, "CH_ExtentionNum": {"filedName": "CH_ExtentionNum", "probe": 0, "analysis": 1, "Name": "ClientHello扩展信息数量", "Remark": "ClientHello的扩展信息数量", "type": "int"}, "CH_Extention": {"filedName": "CH_Extention", "probe": 0, "analysis": 0, "Name": "ClientHello扩展详细信息", "Remark": "ClientHello的扩展详细信息", "type": "json"}, "CH_Extention.l": {"filedName": "CH_Extention.l", "probe": 0, "analysis": 1, "Name": "ClientHello扩展信息长度", "Remark": "ClientHello扩展信息的长度", "type": "int"}, "CH_Extention.t": {"filedName": "CH_Extention.t", "probe": 0, "analysis": 1, "Name": "ClientHello扩展信息类型", "Remark": "ClientHello扩展信息的类型", "type": "int"}, "CH_Extention.v": {"filedName": "CH_Extention.v", "probe": 0, "analysis": 1, "Name": "ClientHello扩展信息值", "Remark": "ClientHello扩展信息的值", "type": "string"}, "CH_ServerName": {"filedName": "CH_ServerName", "probe": 0, "analysis": 1, "Name": "服务器名", "Remark": "ClientHello的服务器名-扩展信息内存在；单存一份", "type": "string"}, "CH_ServerNameType": {"filedName": "CH_ServerNameType", "probe": 0, "analysis": 1, "Name": "服务器名类型", "Remark": "ClientHello的服务器名的类型", "type": "int"}, "CH_SessionTicket": {"filedName": "CH_SessionTicket", "probe": 0, "analysis": 1, "Name": "增强性会话ID", "Remark": "ClientHello增强性会话ID-扩展信息内存在；单存一份", "type": "string"}, "CH_ALPN": {"filedName": "CH_ALPN", "probe": 0, "analysis": 1, "Name": "应用协议类型", "Remark": "ClientHello中说明的应用协议类型-扩展信息内存在；单存一份", "type": "json"}, "sCertHash": {"filedName": "sCertHash", "probe": 0, "analysis": 1, "Name": "客户端证书Hash列表", "Remark": "客户端证书Hash列表", "type": "string"}, "sCertNum": {"filedName": "sCertNum", "probe": 0, "analysis": 1, "Name": "客户端证书数量", "Remark": "客户端证书数量", "type": "int"}, "sKeyExchange": {"filedName": "sKeyExchange", "probe": 0, "analysis": 1, "Name": "客户端密钥交换值", "Remark": "客户端密钥交换值", "type": "string"}, "sKeyExchangeLen": {"filedName": "sKeyExchangeLen", "probe": 0, "analysis": 1, "Name": "客户端密钥交换值字节数", "Remark": "客户端密钥交换值的字节数", "type": "int"}, "sSSLFinger": {"filedName": "s<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "客户端指纹", "Remark": "ssl客户端指纹", "type": "string"}, "sSSLVersion": {"filedName": "sSSLVersion", "probe": 0, "analysis": 1, "Name": "服务端版本号", "Remark": "SSL版本号", "type": "int"}, "SH_Version": {"filedName": "SH_Version", "probe": 0, "analysis": 1, "Name": "ServerHello版本", "Remark": "ServerHello版本", "type": "int"}, "SH_Time": {"filedName": "SH_Time", "probe": 0, "analysis": 1, "Name": "ServerHello时间戳", "Remark": "ServerHello时间戳", "type": "int"}, "SH_Random": {"filedName": "SH_Random", "probe": 0, "analysis": 1, "Name": "ServerHello随机数", "Remark": "ServerHello的随机数", "type": "string"}, "SH_SessionId": {"filedName": "SH_SessionId", "probe": 0, "analysis": 1, "Name": "ServerHello会话ID", "Remark": "ServerHello的会话ID", "type": "string"}, "SH_SessionIdLen": {"filedName": "SH_SessionIdLen", "probe": 0, "analysis": 1, "Name": "ServerHello会话ID字节数", "Remark": "ServerHello的会话ID字节数", "type": "int"}, "SH_Cipersuite": {"filedName": "SH_Cipersuite", "probe": 0, "analysis": 1, "Name": "ServerHello密码套件", "Remark": "ServerHello的密码套件", "type": "string"}, "SH_CompressionMethod": {"filedName": "SH_CompressionMethod", "probe": 0, "analysis": 1, "Name": "ServerHello压缩方法", "Remark": "ServerHello的压缩方法", "type": "string"}, "SH_SessionTicket": {"filedName": "SH_SessionTicket", "probe": 0, "analysis": 1, "Name": "增强性会话ID确认", "Remark": "ServerHello增强性会话ID-扩展信息内存在；单存一份", "type": "string"}, "SH_ALPN": {"filedName": "SH_ALPN", "probe": 0, "analysis": 1, "Name": "确认应用协议类型", "Remark": "ServerHello中说明的应用协议类型-扩展信息内存在；单存一份", "type": "json"}, "SH_ExtentionNum": {"filedName": "SH_ExtentionNum", "probe": 0, "analysis": 0, "Name": "ServerHello扩展信息数量", "Remark": "ServerHello的扩展信息数量", "type": "int"}, "SH_Extention": {"filedName": "SH_Extention", "probe": 0, "analysis": 1, "Name": "ServerHello扩展详细信息", "Remark": "ServerHello的扩展详细信息", "type": "json"}, "SH_Extention.l": {"filedName": "SH_Extention.l", "probe": 0, "analysis": 1, "Name": "ServerHello扩展信息长度", "Remark": "ServerHello扩展信息的长度", "type": "int"}, "SH_Extention.t": {"filedName": "SH_Extention.t", "probe": 0, "analysis": 1, "Name": "ServerHello扩展信息类型", "Remark": "ServerHello扩展信息的类型", "type": "int"}, "SH_Extention.v": {"filedName": "SH_Extention.v", "probe": 0, "analysis": 1, "Name": "ServerHello扩展信息值", "Remark": "ServerHello扩展信息的值", "type": "string"}, "dCertHash": {"filedName": "dCertHash", "probe": 0, "analysis": 1, "Name": "服务端证书Hash", "Remark": "服务端证书Hash", "type": "数组"}, "dCertHashStr": {"filedName": "dCertHashStr", "probe": 0, "analysis": 1, "Name": "服务端证书Hash字符串格式", "Remark": "服务端证书Hash字符串格式", "type": "string"}, "dCertNum": {"filedName": "dCertNum", "probe": 0, "analysis": 1, "Name": "服务端证书数量", "Remark": "服务端证书数量", "type": "int"}, "dKeyExchange": {"filedName": "dKeyExchange", "probe": 0, "analysis": 1, "Name": "服务端密钥交互值", "Remark": "服务端密钥交互值", "type": "string"}, "dKeyExchangelen": {"filedName": "dKeyExchangelen", "probe": 0, "analysis": 1, "Name": "服务端密钥交互数据字节数", "Remark": "服务端密钥交互数据字节数", "type": "int"}, "dNewSessionTicket_LifeTime": {"filedName": "dNewSessionTicket_LifeTime", "probe": 0, "analysis": 1, "Name": "新增强型会话ID有效周期", "Remark": "新的增强型会话ID有效周期", "type": "int"}, "dNewSessionTicket_Ticket": {"filedName": "dNewSessionTicket_Ticket", "probe": 0, "analysis": 1, "Name": "新增强型会话ID", "Remark": "新的增强型会话ID", "type": "string"}, "dNewSessionTicket_TicketLen": {"filedName": "dNewSessionTicket_TicketLen", "probe": 0, "analysis": 1, "Name": "新增强型会话ID字节数", "Remark": "新的增强型会话ID字节数", "type": "int"}, "dSSLFinger": {"filedName": "d<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "服务端指纹", "Remark": "服务端指纹", "type": "string"}, "Hkey": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "存储标记", "Remark": "存储标记", "type": "string"}}}
# Graph Service - 图谱服务

## 项目概述

Graph Service 是 NTA 3.0 平台的核心图谱服务，基于领域驱动设计（DDD）分层架构构建，提供网络安全态势感知中的实体关系图谱分析功能。该服务管理和分析 IP、域名、证书、应用服务等网络实体之间的复杂关联关系。

## 技术栈

- **Java 17** - 核心开发语言
- **Spring Boot 3.x** - 应用框架
- **NebulaGraph** - 图数据库
- **ngbatis** - NebulaGraph ORM 框架
- **MapStruct** - 对象映射
- **Maven** - 项目构建工具

## DDD 分层架构设计

本项目严格遵循领域驱动设计（Domain-Driven Design）的分层架构模式，将代码组织为四个清晰的层次：

### 1. 接口层（Interfaces Layer）
**路径**: `com.geeksec.graph.interfaces`

接口层是系统的最外层，负责处理外部请求和响应，包括：

- **REST 控制器**: 提供 RESTful API 接口
- **DTO 转换**: 处理外部数据传输对象
- **请求验证**: 输入参数校验和格式化
- **响应封装**: 统一的 API 响应格式

```
interfaces/
├── rest/
│   ├── GraphController.java              # 图谱基础信息接口
│   ├── GraphAnalysisController.java      # 图谱分析接口
│   ├── cert/                             # 证书相关接口
│   ├── domain/                           # 域名相关接口
│   ├── ip/                               # IP相关接口
│   └── dto/                              # 接口层数据传输对象
```

**核心特点**：
- 使用 Spring HATEOAS 提供超媒体 API
- 统一的异常处理和响应格式
- 支持多种实体类型的图谱查询和分析

### 2. 应用层（Application Layer）
**路径**: `com.geeksec.graph.application`

应用层协调领域服务，实现具体的业务用例，包括：

- **应用服务**: 编排领域服务完成业务流程
- **命令处理**: 处理来自接口层的命令
- **DTO 定义**: 应用层数据传输对象
- **事务管理**: 跨领域的事务协调

```
application/
├── graph/
│   ├── service/
│   │   ├── GraphApplicationService.java           # 图谱应用服务接口
│   │   └── impl/
│   │       └── GraphApplicationServiceImpl.java   # 图谱应用服务实现
│   └── dto/                                       # 图谱相关DTO
├── cert/                                          # 证书应用服务
├── domain/                                        # 域名应用服务
├── ip/                                            # IP应用服务
└── common/                                        # 通用应用服务
```

**核心职责**：
- 实现具体的业务用例（如获取图谱关联关系）
- 协调多个领域服务
- 处理跨领域的业务逻辑
- 管理应用级别的事务

### 3. 领域层（Domain Layer）
**路径**: `com.geeksec.graph.domain`

领域层是 DDD 的核心，包含业务逻辑和规则：

- **领域模型**: 核心业务实体
- **领域服务**: 复杂业务逻辑的封装
- **仓储接口**: 数据访问的抽象
- **值对象**: 不可变的业务概念

```
domain/
├── graph/
│   ├── model/
│   │   └── Graph.java                    # 图谱领域模型
│   ├── service/
│   │   ├── GraphService.java             # 图谱领域服务接口
│   │   └── GraphServiceImpl.java         # 图谱领域服务实现
│   └── repository/
│       └── GraphRepository.java          # 图谱仓储接口
├── cert/                                 # 证书领域
├── domain/                               # 域名领域
├── ip/                                   # IP领域
└── common/
    ├── model/
    │   └── BaseEntity.java               # 基础领域模型
    └── service/
        └── BaseDomainService.java        # 基础领域服务
```

**核心特点**：
- 封装核心业务逻辑和规则
- 领域模型具有丰富的行为
- 通过仓储接口与基础设施层解耦
- 实现复杂的图谱关系分析算法

### 4. 基础设施层（Infrastructure Layer）
**路径**: `com.geeksec.graph.infrastructure`

基础设施层提供技术实现和外部系统集成：

- **数据持久化**: 数据库访问实现
- **外部服务**: 第三方系统集成
- **技术组件**: 缓存、消息队列等
- **配置管理**: 系统配置和环境管理

```
infrastructure/
└── persistence/
    ├── mapper/                           # ngbatis 映射器
    │   ├── CertMapper.java
    │   ├── DomainMapper.java
    │   └── IpMapper.java
    ├── po/                               # 持久化对象
    │   ├── BaseVertexPo.java
    │   ├── vertex/                       # 顶点对象
    │   └── edge/                         # 边对象
    ├── repository/                       # 仓储实现
    │   ├── GraphRepositoryImpl.java
    │   ├── CertRepositoryImpl.java
    │   └── DomainRepositoryImpl.java
    └── exception/
        └── RepositoryException.java      # 持久化异常
```

**技术实现**：
- 使用 NebulaGraph 作为图数据库
- ngbatis 提供 ORM 映射
- 实现领域层定义的仓储接口
- 处理数据库连接和事务管理

## 核心业务功能

### 图谱实体管理
- **IP 地址**: 网络 IP 地址及其属性管理
- **域名**: 域名信息和层级关系
- **证书**: SSL/TLS 证书指纹和属性
- **应用服务**: 网络服务和端口信息
- **组织**: 证书颁发机构和组织信息

### 关系图谱分析
- **关联查询**: 查询实体间的直接关联关系
- **路径分析**: 分析实体间的关联路径
- **聚类分析**: 基于关联关系的实体聚类
- **下钻分析**: 逐层展开实体的关联关系

### 标签管理
- **实体标签**: 为各类实体添加业务标签
- **标签查询**: 基于标签的实体检索
- **标签统计**: 标签使用情况统计

## API 接口

### 基础信息接口
```http
GET /api/v1/graph/domain/{domain}     # 获取域名详情
GET /api/v1/graph/cert/{fingerprint} # 获取证书详情
GET /api/v1/graph/ip/{ip}            # 获取IP详情
```

### 图谱分析接口
```http
GET /api/v1/graph/association         # 获取关联图
POST /api/v1/graph/association/next   # 获取下一级关联
POST /api/v1/graph/path              # 路径分析
POST /api/v1/graph/cluster           # 聚类分析
```

## 配置说明

### NebulaGraph 配置
```yaml
nebula:
  hosts: localhost:9669
  username: root
  password: nebula
  space: nta_graph
```

### 应用配置
```yaml
spring:
  application:
    name: graph-service
  profiles:
    active: dev
```

## 开发指南

### 添加新的实体类型

1. **创建领域模型** (domain/newentity/model/)
2. **定义仓储接口** (domain/newentity/repository/)
3. **实现领域服务** (domain/newentity/service/)
4. **创建应用服务** (application/newentity/service/)
5. **实现基础设施** (infrastructure/persistence/)
6. **添加 REST 接口** (interfaces/rest/)

### 代码规范

- 遵循 DDD 分层架构原则
- 使用 Java 17 语言特性
- 保持领域模型的纯净性
- 通过接口实现层间解耦
- 编写完整的 Javadoc 文档

## 部署说明

### 构建项目
```bash
mvn clean package -DskipTests
```

### Docker 部署
```bash
docker build -t graph-service:latest .
docker run -p 8080:8080 graph-service:latest
```

### 依赖服务
- NebulaGraph 图数据库
- 配置中心服务
- 服务注册中心

## DDD 架构设计原则

### 依赖关系规则

本项目严格遵循 DDD 的依赖倒置原则：

```
接口层 (Interfaces)
    ↓ 依赖
应用层 (Application)
    ↓ 依赖
领域层 (Domain)
    ↑ 实现
基础设施层 (Infrastructure)
```

**核心规则**：
- **接口层**只能依赖应用层，不能直接调用领域层或基础设施层
- **应用层**可以依赖领域层，协调多个领域服务
- **领域层**是核心，不依赖任何其他层，保持纯净
- **基础设施层**实现领域层定义的接口，向上提供技术支持

### 关键设计模式

#### 1. 仓储模式 (Repository Pattern)
```java
// 领域层定义接口
public interface GraphRepository {
    Optional<Graph> findById(String id);
    Graph save(Graph graph);
    Set<String> getRelatedEntities(String id);
}

// 基础设施层实现
@Repository
public class GraphRepositoryImpl implements GraphRepository {
    // NebulaGraph 具体实现
}
```

#### 2. 应用服务模式 (Application Service Pattern)
```java
@Service
public class GraphApplicationServiceImpl implements GraphApplicationService {
    private final GraphService graphService; // 依赖领域服务

    @Override
    public GraphRelationResponseDto getAssociation(String id) {
        return graphService.getAssociation(id); // 委托给领域服务
    }
}
```

#### 3. 领域服务模式 (Domain Service Pattern)
```java
@Service
public class GraphServiceImpl implements GraphService {
    private final GraphRepository repository; // 依赖仓储接口

    @Override
    public GraphRelationResponseDto getAssociation(String id) {
        // 复杂的业务逻辑实现
        // 图谱关系分析算法
    }
}
```

### 数据流转模式

#### 请求处理流程
```
HTTP Request → Controller → ApplicationService → DomainService → Repository → Database
                    ↓              ↓              ↓            ↓
                 DTO转换        业务编排        核心逻辑      数据持久化
```

#### 响应返回流程
```
Database → Repository → DomainModel → ApplicationService → Controller → HTTP Response
            ↓            ↓              ↓                  ↓
         数据查询      领域对象        DTO转换           响应封装
```

### 实体关系设计

#### 核心实体模型
```java
// 基础实体 - 所有领域模型的父类
@SuperBuilder
public abstract class BaseEntity {
    private String id;           // 实体唯一标识
    private String remark;       // 备注信息
    private Set<Long> labels;    // 关联标签
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

// 具体领域实体
public class Domain extends BaseEntity {
    private String name;         // 域名
    private String parentDomain; // 父域名
    private Integer ipCount;     // 关联IP数量
    private Integer certCount;   // 关联证书数量
}
```

#### 图数据库映射
```java
// NebulaGraph 顶点映射
@Vertex("domain")
public class DomainVertexPo extends BaseVertexPo {
    @Id
    private String domainAddr;

    @Property("ip_count")
    private Integer ipCount;

    @Property("cert_count")
    private Integer certCount;
}

// NebulaGraph 边映射
@Edge("domain_ip")
public class DomainIpEdgePo {
    @SrcId
    private String domainId;

    @DstId
    private String ipId;

    @Property("relation_type")
    private String relationType;
}
```

## 测试策略

### 单元测试
- **领域服务测试**: 验证核心业务逻辑
- **应用服务测试**: 验证业务流程编排
- **仓储测试**: 验证数据访问逻辑

### 集成测试
- **API 接口测试**: 验证端到端功能
- **数据库集成测试**: 验证 NebulaGraph 操作
- **服务间集成测试**: 验证微服务协作

### 测试示例
```java
@SpringBootTest
class GraphApplicationServiceTest {

    @MockBean
    private GraphService graphService;

    @Autowired
    private GraphApplicationService applicationService;

    @Test
    void shouldGetAssociation() {
        // Given
        String graphId = "test-graph-id";
        GraphRelationResponseDto expected = GraphRelationResponseDto.builder()
            .nodeId(graphId)
            .build();

        when(graphService.getAssociation(graphId)).thenReturn(expected);

        // When
        GraphRelationResponseDto result = applicationService.getAssociation(graphId);

        // Then
        assertThat(result.getNodeId()).isEqualTo(graphId);
    }
}
```

## 性能优化

### 图数据库优化
- **索引策略**: 为常用查询字段建立索引
- **查询优化**: 使用 NebulaGraph 的查询优化器
- **连接池**: 配置合适的数据库连接池

### 缓存策略
- **应用层缓存**: 缓存频繁查询的图谱关系
- **领域层缓存**: 缓存复杂计算结果
- **分布式缓存**: 使用 Redis 进行跨实例缓存

### 异步处理
- **图谱分析**: 复杂分析任务异步执行
- **批量操作**: 大量数据的批量处理
- **事件驱动**: 基于事件的异步通信

## 监控与运维

### 应用监控
- **性能指标**: 响应时间、吞吐量、错误率
- **业务指标**: 图谱查询次数、实体数量统计
- **资源监控**: CPU、内存、网络使用情况

### 日志管理
- **结构化日志**: 使用 JSON 格式的结构化日志
- **链路追踪**: 分布式请求链路追踪
- **错误告警**: 关键错误的实时告警

### 健康检查
```java
@Component
public class GraphServiceHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        // 检查 NebulaGraph 连接状态
        // 检查关键业务功能
        return Health.up()
            .withDetail("nebula", "connected")
            .withDetail("entities", "available")
            .build();
    }
}
```

---

**作者**: hufengkai
**版本**: 3.0.0-SNAPSHOT
**更新时间**: 2024年

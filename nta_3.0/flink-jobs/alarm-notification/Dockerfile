# 使用官方 Flink 镜像作为基础镜像
FROM flink:1.20.1-scala_2.12-java17

# 设置工作目录
WORKDIR /opt/flink

# 创建应用目录
RUN mkdir -p /opt/flink/usrlib

# 复制应用 JAR 文件
COPY target/alarm-notification-3.0.0.jar /opt/flink/usrlib/

# 复制配置文件
COPY src/main/resources/application.yml /opt/flink/conf/

# 设置环境变量
ENV FLINK_CLASSPATH=/opt/flink/usrlib/alarm-notification-3.0.0.jar

# 创建日志目录
RUN mkdir -p /opt/flink/log

# 设置权限
RUN chown -R flink:flink /opt/flink

# 切换到 flink 用户
USER flink

# 暴露端口
EXPOSE 8081 6123

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8081/overview || exit 1

# 默认命令
CMD ["help"]

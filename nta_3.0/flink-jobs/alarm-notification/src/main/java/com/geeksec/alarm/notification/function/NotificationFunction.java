package com.geeksec.alarm.notification.function;

import com.geeksec.alarm.notification.model.Alarm;
import com.geeksec.alarm.notification.model.NotificationSubscription;
import com.geeksec.alarm.notification.model.NotificationResult;
import com.geeksec.alarm.notification.model.SubscriptionRule;
import com.geeksec.alarm.notification.model.NotificationChannel;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;

/**
 * 通知处理函数
 * 使用专门的流处理模型
 */
@Slf4j
public class NotificationFunction extends BroadcastProcessFunction<Alarm, NotificationSubscription, NotificationResult> {
    
    public static final MapStateDescriptor<String, NotificationSubscription> SUBSCRIPTION_STATE_DESCRIPTOR =
            new MapStateDescriptor<>("subscription-state", String.class, NotificationSubscription.class);
    
    @Override
    public void processElement(Alarm alarm, ReadOnlyContext ctx, Collector<NotificationResult> out) throws Exception {
        // 获取所有订阅配置
        ReadOnlyBroadcastState<String, NotificationSubscription> subscriptionState = 
                ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
        
        // 遍历匹配订阅
        for (Map.Entry<String, NotificationSubscription> entry : subscriptionState.immutableEntries()) {
            NotificationSubscription subscription = entry.getValue();
            
            if (matchesSubscription(alarm, subscription)) {
                // 发送通知
                sendNotifications(alarm, subscription, out);
            }
        }
    }
    
    private boolean matchesSubscription(Alarm alarm, NotificationSubscription subscription) {
        if (!Boolean.TRUE.equals(subscription.getEnabled()) || 
            subscription.getRules() == null || subscription.getRules().isEmpty()) {
            return true;
        }
        
        return subscription.getRules().stream()
                .allMatch(rule -> matchesRule(alarm, rule));
    }
    
    private boolean matchesRule(Alarm alarm, SubscriptionRule rule) {
        // 简化的规则匹配逻辑
        String fieldValue = getFieldValue(alarm, rule.getFieldName());
        String expectedValue = rule.getExpectedValue();
        
        if (fieldValue == null || expectedValue == null) {
            return false;
        }
        
        boolean ignoreCase = Boolean.TRUE.equals(rule.getIgnoreCase());
        if (ignoreCase) {
            fieldValue = fieldValue.toLowerCase();
            expectedValue = expectedValue.toLowerCase();
        }
        
        return switch (rule.getOperator()) {
            case "EQUALS" -> fieldValue.equals(expectedValue);
            case "CONTAINS" -> fieldValue.contains(expectedValue);
            default -> false;
        };
    }
}

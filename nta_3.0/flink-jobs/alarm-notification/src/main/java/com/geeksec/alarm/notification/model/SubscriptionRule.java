package com.geeksec.alarm.notification.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 订阅规则模型
 * 用于告警匹配判断
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubscriptionRule implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 匹配字段名
     */
    private String fieldName;
    
    /**
     * 操作符
     */
    private String operator;
    
    /**
     * 期望值
     */
    private String expectedValue;
    
    /**
     * 是否忽略大小写
     */
    private Boolean ignoreCase;
}
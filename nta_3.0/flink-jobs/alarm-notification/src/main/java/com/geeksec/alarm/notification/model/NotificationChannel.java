package com.geeksec.alarm.notification.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 通知渠道模型
 * 用于发送通知
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationChannel implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 渠道类型
     */
    private String channelType;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 接收地址
     */
    private String address;
    
    /**
     * 模板ID
     */
    private String templateId;
}
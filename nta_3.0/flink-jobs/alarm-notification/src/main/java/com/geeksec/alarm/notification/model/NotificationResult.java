package com.geeksec.alarm.notification.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知发送结果模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 订阅ID
     */
    private String subscriptionId;
    
    /**
     * 告警ID
     */
    private String alarmId;
    
    /**
     * 渠道类型
     */
    private String channelType;
    
    /**
     * 接收者
     */
    private String recipient;
    
    /**
     * 发送状态
     */
    private String sendStatus;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 主题
     */
    private String subject;
    
    /**
     * 内容
     */
    private String content;
}
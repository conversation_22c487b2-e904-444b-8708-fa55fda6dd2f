package com.geeksec.alarm.notification.sender;

import com.geeksec.alarm.notification.config.AlarmNotificationConfig;
import com.geeksec.nta.alarm.dto.subscription.NotificationChannelDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.io.Serializable;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;

/**
 * 通知发送器
 * 只支持邮件和Kafka两种通知方式
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class NotificationSender implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    private final AlarmNotificationConfig config;
    private transient JavaMailSender mailSender;
    private transient KafkaProducer<String, String> kafkaProducer;
    
    public NotificationSender(AlarmNotificationConfig config) {
        this.config = config;
        initializeMailSender();
        initializeKafkaProducer();
    }
    
    /**
     * 初始化邮件发送器
     */
    private void initializeMailSender() {
        try {
            JavaMailSenderImpl sender = new JavaMailSenderImpl();
            sender.setHost(config.getEmailConfig().getSmtpHost());
            sender.setPort(config.getEmailConfig().getSmtpPort());
            sender.setUsername(config.getEmailConfig().getUsername());
            sender.setPassword(config.getEmailConfig().getPassword());
            
            Properties props = sender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.debug", "false");
            
            this.mailSender = sender;
            log.info("邮件发送器初始化成功");
        } catch (Exception e) {
            log.error("邮件发送器初始化失败", e);
        }
    }
    
    /**
     * 初始化Kafka生产者
     */
    private void initializeKafkaProducer() {
        try {
            Properties props = new Properties();
            props.put("bootstrap.servers", config.getKafkaConfig().getBootstrapServers());
            props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
            props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
            props.put("acks", "1");
            props.put("retries", 3);
            props.put("batch.size", 16384);
            props.put("linger.ms", 1);
            props.put("buffer.memory", 33554432);
            
            this.kafkaProducer = new KafkaProducer<>(props);
            log.info("Kafka生产者初始化成功");
        } catch (Exception e) {
            log.error("Kafka生产者初始化失败", e);
        }
    }
    
    /**
     * 发送通知
     * 
     * @param channel 通知渠道
     * @param subject 通知主题
     * @param content 通知内容
     * @return 发送结果
     */
    public CompletableFuture<NotificationResult> sendNotification(NotificationChannelDto channel, 
                                                                 String subject, String content) {
        if (channel == null || !Boolean.TRUE.equals(channel.getEnabled())) {
            return CompletableFuture.completedFuture(
                NotificationResult.failure("通知渠道不可用"));
        }
        
        switch (channel.getChannelType()) {
            case EMAIL:
                return sendEmailNotification(channel, subject, content);
            case KAFKA:
                return sendKafkaNotification(channel, subject, content);
            default:
                return CompletableFuture.completedFuture(
                    NotificationResult.failure("不支持的通知渠道类型: " + channel.getChannelType()));
        }
    }
    
    /**
     * 发送邮件通知
     */
    private CompletableFuture<NotificationResult> sendEmailNotification(NotificationChannelDto channel, 
                                                                       String subject, String content) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (mailSender == null) {
                    return NotificationResult.failure("邮件发送器未初始化");
                }
                
                SimpleMailMessage message = new SimpleMailMessage();
                message.setFrom(config.getEmailConfig().getFromAddress());
                message.setTo(channel.getAddress());
                message.setSubject(subject);
                message.setText(content);
                
                mailSender.send(message);
                
                log.debug("邮件通知发送成功: {}", channel.getAddress());
                return NotificationResult.success("邮件发送成功");
                
            } catch (Exception e) {
                log.error("邮件通知发送失败: {}, 错误: {}", channel.getAddress(), e.getMessage(), e);
                return NotificationResult.failure("邮件发送失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 发送Kafka通知
     */
    private CompletableFuture<NotificationResult> sendKafkaNotification(NotificationChannelDto channel, 
                                                                       String subject, String content) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (kafkaProducer == null) {
                    return NotificationResult.failure("Kafka生产者未初始化");
                }
                
                String topic = channel.getAddress();
                ProducerRecord<String, String> record = new ProducerRecord<>(topic, subject, content);
                
                kafkaProducer.send(record, (metadata, exception) -> {
                    if (exception != null) {
                        log.error("Kafka通知发送失败: topic={}, 错误={}", topic, exception.getMessage());
                    } else {
                        log.debug("Kafka通知发送成功: topic={}, partition={}, offset={}", 
                                topic, metadata.partition(), metadata.offset());
                    }
                });
                
                return NotificationResult.success("Kafka消息发送成功");
                
            } catch (Exception e) {
                log.error("Kafka通知发送失败: topic={}, 错误={}", channel.getAddress(), e.getMessage(), e);
                return NotificationResult.failure("Kafka发送失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 关闭资源
     */
    public void close() {
        if (kafkaProducer != null) {
            try {
                kafkaProducer.close();
                log.info("Kafka生产者已关闭");
            } catch (Exception e) {
                log.error("关闭Kafka生产者失败", e);
            }
        }
    }
    
    /**
     * 通知发送结果
     */
    public static class NotificationResult implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private final boolean success;
        private final String message;
        private final long timestamp;
        
        private NotificationResult(boolean success, String message) {
            this.success = success;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }
        
        public static NotificationResult success(String message) {
            return new NotificationResult(true, message);
        }
        
        public static NotificationResult failure(String message) {
            return new NotificationResult(false, message);
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
        
        @Override
        public String toString() {
            return String.format("NotificationResult{success=%s, message='%s', timestamp=%d}", 
                    success, message, timestamp);
        }
    }
}

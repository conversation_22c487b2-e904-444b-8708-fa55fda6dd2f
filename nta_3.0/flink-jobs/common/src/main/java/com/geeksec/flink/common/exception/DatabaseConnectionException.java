package com.geeksec.flink.common.exception;

/**
 * 数据库连接异常
 * 用于数据库连接相关的异常处理
 * 
 * <AUTHOR>
 */
public class DatabaseConnectionException extends FlinkJobException {
    
    private static final long serialVersionUID = 1L;
    
    private final String databaseType;
    private final String connectionInfo;
    
    public DatabaseConnectionException(String databaseType, String message) {
        super(message);
        this.databaseType = databaseType;
        this.connectionInfo = null;
    }
    
    public DatabaseConnectionException(String databaseType, String connectionInfo, String message) {
        super(message);
        this.databaseType = databaseType;
        this.connectionInfo = connectionInfo;
    }
    
    public DatabaseConnectionException(String databaseType, String message, Throwable cause) {
        super(message, cause);
        this.databaseType = databaseType;
        this.connectionInfo = null;
    }
    
    public DatabaseConnectionException(String databaseType, String connectionInfo, String message, Throwable cause) {
        super(message, cause);
        this.databaseType = databaseType;
        this.connectionInfo = connectionInfo;
    }
    
    public String getDatabaseType() {
        return databaseType;
    }
    
    public String getConnectionInfo() {
        return connectionInfo;
    }
    
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("数据库连接异常");
        
        if (databaseType != null) {
            sb.append(" [").append(databaseType).append("]");
        }
        
        if (connectionInfo != null) {
            sb.append(" [").append(connectionInfo).append("]");
        }
        
        sb.append(": ").append(super.getMessage());
        
        return sb.toString();
    }
}

package com.geeksec.flink.common.utils.flink;

import org.apache.flink.types.Row;

/**
 * Flink Row 类型相关的工具类。
 * <p>
 * 此类遵循单一职责原则，专门用于创建和处理 Flink 的 {@link Row} 对象，
 * 将对 Flink API 的依赖限制在此包内。
 *
 * <AUTHOR>
 */
public final class FlinkRowUtils {

    private FlinkRowUtils() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 创建一个简单的标签记录，包含字段值、标签代码和时间戳。
     *
     * @param fieldValue 字段值
     * @param labelCode  标签代码
     * @return 包含3个字段的行记录 (fieldValue, labelCode, timestamp)
     */
    public static Row createSimpleLabel(Object fieldValue, String labelCode) {
        return Row.of(fieldValue, labelCode, System.currentTimeMillis() / 1000);
    }

    /**
     * 创建一个标准的5字段“添加标签”边记录。
     *
     * @param src 源节点
     * @param dst 目标节点 (通常是标签本身)
     * @return 包含5个字段的行记录 (src, dst, 0, "Add_Label", "")
     */
    public static Row createLabelEdge(Object src, Object dst) {
        Row resultRow = new Row(5);
        resultRow.setField(0, src);
        resultRow.setField(1, dst);
        resultRow.setField(2, 0);
        resultRow.setField(3, "Add_Label");
        resultRow.setField(4, "");
        return resultRow;
    }

    /**
     * 创建一个标准的5字段边记录。
     *
     * @param src        源节点
     * @param dst        目标节点
     * @param edgeType   边类型
     * @param properties 属性
     * @return 包含5个字段的行记录 (src, dst, 0, edgeType, properties)
     */
    public static Row createEdge(Object src, Object dst, String edgeType, String properties) {
        Row resultRow = new Row(5);
        resultRow.setField(0, src);
        resultRow.setField(1, dst);
        resultRow.setField(2, 0);
        resultRow.setField(3, edgeType);
        resultRow.setField(4, properties);
        return resultRow;
    }
}

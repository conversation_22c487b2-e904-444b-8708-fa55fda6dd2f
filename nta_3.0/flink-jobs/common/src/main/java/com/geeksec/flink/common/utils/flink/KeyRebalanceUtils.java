package com.geeksec.flink.common.utils.flink;

import org.apache.flink.runtime.state.KeyGroupRangeAssignment;

import java.util.HashMap;
import java.util.LinkedHashSet;

/**
 * Flink键重平衡工具类
 * 用于创建重平衡键，确保数据在并行任务间的均匀分布
 * 
 * <AUTHOR>
 */
public class KeyRebalanceUtils {
    
    private KeyRebalanceUtils() {
        // 防止实例化
    }
    
    /**
     * 创建重平衡键数组
     * 
     * @param parallelism 并行度
     * @return 重平衡键数组
     */
    public static Integer[] createRebalanceKeys(int parallelism) {
        return createRebalanceKeys(parallelism, 12);
    }
    
    /**
     * 创建重平衡键数组
     * 
     * @param parallelism 并行度
     * @param multiplier 倍数因子，用于计算最大随机键数量
     * @return 重平衡键数组
     */
    public static Integer[] createRebalanceKeys(int parallelism, int multiplier) {
        if (parallelism <= 0) {
            throw new IllegalArgumentException("并行度必须大于0");
        }
        
        if (multiplier <= 0) {
            throw new IllegalArgumentException("倍数因子必须大于0");
        }
        
        HashMap<Integer, LinkedHashSet<Integer>> groupRanges = new HashMap<>();
        int maxParallelism = KeyGroupRangeAssignment.computeDefaultMaxParallelism(parallelism);
        int maxRandomKey = parallelism * multiplier;
        
        // 为每个随机键分配到对应的子任务
        for (int randomKey = 0; randomKey < maxRandomKey; randomKey++) {
            int subtaskIndex = KeyGroupRangeAssignment.assignKeyToParallelOperator(
                    randomKey, maxParallelism, parallelism);
            LinkedHashSet<Integer> randomKeys = groupRanges.computeIfAbsent(
                    subtaskIndex, k -> new LinkedHashSet<>());
            randomKeys.add(randomKey);
        }
        
        // 为每个子任务选择一个重平衡键
        Integer[] rebalanceKeys = new Integer[parallelism];
        for (int i = 0; i < parallelism; i++) {
            LinkedHashSet<Integer> ranges = groupRanges.get(i);
            if (ranges == null || ranges.isEmpty()) {
                throw new IllegalArgumentException("创建重平衡键失败：子任务 " + i + " 没有分配到键");
            }
            // 选择第一个键作为重平衡键
            rebalanceKeys[i] = ranges.iterator().next();
        }
        
        return rebalanceKeys;
    }
    
    /**
     * 验证重平衡键数组的有效性
     * 
     * @param rebalanceKeys 重平衡键数组
     * @param parallelism 并行度
     * @return 如果有效返回true，否则返回false
     */
    public static boolean validateRebalanceKeys(Integer[] rebalanceKeys, int parallelism) {
        if (rebalanceKeys == null) {
            return false;
        }
        
        if (rebalanceKeys.length != parallelism) {
            return false;
        }
        
        // 检查是否有null值
        for (Integer key : rebalanceKeys) {
            if (key == null) {
                return false;
            }
        }
        
        // 检查键的分布是否正确
        int maxParallelism = KeyGroupRangeAssignment.computeDefaultMaxParallelism(parallelism);
        for (int i = 0; i < rebalanceKeys.length; i++) {
            int expectedSubtask = KeyGroupRangeAssignment.assignKeyToParallelOperator(
                    rebalanceKeys[i], maxParallelism, parallelism);
            if (expectedSubtask != i) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取键对应的子任务索引
     * 
     * @param key 键值
     * @param parallelism 并行度
     * @return 子任务索引
     */
    public static int getSubtaskIndex(int key, int parallelism) {
        int maxParallelism = KeyGroupRangeAssignment.computeDefaultMaxParallelism(parallelism);
        return KeyGroupRangeAssignment.assignKeyToParallelOperator(key, maxParallelism, parallelism);
    }
    
    /**
     * 计算默认最大并行度
     * 
     * @param parallelism 并行度
     * @return 默认最大并行度
     */
    public static int computeDefaultMaxParallelism(int parallelism) {
        return KeyGroupRangeAssignment.computeDefaultMaxParallelism(parallelism);
    }
}

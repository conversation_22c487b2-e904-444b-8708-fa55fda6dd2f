package com.geeksec.flink.common.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;

import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 配置管理器，用于加载和管理配置文件
 * 使用单例模式确保配置只被加载一次
 *
 * <AUTHOR> Team
 */
@Slf4j
public class ConfigurationManager {
    private static final AtomicReference<ParameterTool> parameterToolRef = new AtomicReference<>();
    private static final String DEFAULT_CONFIG_PATH = "/opt/flink/usrlib/classes/config.properties";

    private ConfigurationManager() {
    }

    /**
     * 从默认路径加载配置
     *
     * @return 加载的配置，如果加载失败则返回空配置
     */
    private static ParameterTool loadFromDefaultPath() {
        try {
            var config = ParameterTool.fromPropertiesFile(DEFAULT_CONFIG_PATH);
            log.info("Configuration loaded from default path: {}", DEFAULT_CONFIG_PATH);
            return config;
        } catch (IOException e) {
            log.warn("Could not load config from default path: {}, using empty config", DEFAULT_CONFIG_PATH, e);
            return ParameterTool.fromSystemProperties();
        }
    }

    /**
     * 获取配置实例
     *
     * @return ParameterTool实例，包含所有配置项
     */
    public static ParameterTool getConfig() {
        // 使用 AtomicReference 替代 volatile + synchronized 双重检查锁定模式
        return parameterToolRef.updateAndGet(current -> {
            if (current != null) {
                return current;
            }

            try {
                // 从指定路径加载配置文件
                var configPath = Optional.ofNullable(System.getenv("CONFIG_PATH"));

                var fileConfig = configPath
                        .filter(path -> !path.isEmpty())
                        .map(path -> {
                            try {
                                var config = ParameterTool.fromPropertiesFile(path);
                                log.info("Configuration loaded from CONFIG_PATH: {}", path);
                                return config;
                            } catch (IOException e) {
                                log.warn("Failed to load configuration from CONFIG_PATH: {}", path, e);
                                return loadFromDefaultPath();
                            }
                        })
                        .orElseGet(ConfigurationManager::loadFromDefaultPath);

                // 从环境变量加载配置
                var envConfig = ParameterTool.fromMap(System.getenv());

                // 从系统属性加载配置
                var sysConfig = ParameterTool.fromSystemProperties();

                // 合并配置，优先级：环境变量 > 系统属性 > 配置文件
                var mergedConfig = fileConfig.mergeWith(sysConfig).mergeWith(envConfig);

                log.info("Configuration loaded successfully with {} properties",
                        mergedConfig.getProperties().size());

                return mergedConfig;
            } catch (Exception e) {
                log.error("Failed to load configuration", e);
                throw new RuntimeException("Failed to load configuration", e);
            }
        });
    }
}

package com.geeksec.flink.cert;

import com.geeksec.flink.common.config.FlinkConfigLoader;
import com.geeksec.flink.cert.function.CertLabelsAggregator;
import com.geeksec.flink.cert.model.CertLabelChange;
import com.geeksec.flink.cert.model.CertLabelUpdate;
import com.geeksec.flink.cert.serializer.CertLabelChangeDeserializer;
import com.geeksec.flink.cert.serializer.CertLabelUpdateSerializer;
import org.apache.doris.flink.cfg.DorisExecutionOptions;
import org.apache.doris.flink.cfg.DorisOptions;
import org.apache.doris.flink.cfg.DorisReadOptions;
import org.apache.doris.flink.sink.DorisSink;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.cdc.connectors.postgres.PostgreSQLSource;
import org.apache.flink.cdc.debezium.JsonDebeziumDeserializationSchema;
import lombok.extern.slf4j.Slf4j;

import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * Flink CDC 证书标签同步作业
 * 
 * 功能：
 * 1. 监听 PostgreSQL cert_labels 表的变更
 * 2. 聚合每个证书的标签数组
 * 3. 实时更新 Doris dim_cert 表的 labels 字段
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class CertLabelsCdcSyncJob {
    
    public static void main(String[] args) throws Exception {
        // 加载配置
        Configuration config = FlinkConfigLoader.loadConfig("cert-labels-cdc-sync");
        
        // 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment(config);
        
        // 配置检查点
        configureCheckpointing(env, config);
        
        // 配置重启策略
        configureRestartStrategy(env, config);
        
        // 创建 PostgreSQL CDC 源
        PostgreSQLSource<String> postgresSource = createPostgreSQLSource(config);
        
        // 创建数据流
        DataStream<String> sourceStream = env
            .fromSource(postgresSource, WatermarkStrategy.noWatermarks(), "PostgreSQL CDC Source")
            .name("postgresql-cdc-source")
            .uid("postgresql-cdc-source");
        
        // 解析和过滤证书标签变更
        DataStream<CertLabelChange> labelChanges = sourceStream
            .flatMap(new CertLabelChangeDeserializer())
            .name("cert-label-change-deserializer")
            .uid("cert-label-change-deserializer");
        
        // 按证书哈希分组并聚合标签
        DataStream<CertLabelUpdate> labelUpdates = labelChanges
            .keyBy(CertLabelChange::getCertHash)
            .process(new CertLabelsAggregator(config))
            .name("cert-labels-aggregator")
            .uid("cert-labels-aggregator");
        
        // 创建 Doris Sink
        DorisSink<CertLabelUpdate> dorisSink = createDorisSink(config);
        
        // 写入 Doris
        labelUpdates
            .sinkTo(dorisSink)
            .name("doris-sink")
            .uid("doris-sink");
        
        // 执行作业
        log.info("启动证书标签CDC同步作业...");
        env.execute("Certificate Labels CDC Sync Job");
    }
    
    /**
     * 配置检查点
     */
    private static void configureCheckpointing(StreamExecutionEnvironment env, Configuration config) {
        // 启用检查点
        env.enableCheckpointing(config.getLong("checkpoint.interval", 60000L)); // 默认1分钟
        
        // 设置检查点模式
        env.getCheckpointConfig().setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        
        // 设置检查点超时时间
        env.getCheckpointConfig().setCheckpointTimeout(config.getLong("checkpoint.timeout", 300000L)); // 默认5分钟
        
        // 设置最大并发检查点数
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        
        // 设置检查点之间的最小间隔
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(config.getLong("checkpoint.min-pause", 30000L)); // 默认30秒
        
        log.info("检查点配置完成");
    }
    
    /**
     * 配置重启策略
     */
    private static void configureRestartStrategy(StreamExecutionEnvironment env, Configuration config) {
        env.setRestartStrategy(RestartStrategies.failureRateRestart(
            config.getInteger("restart.failure-rate.max-failures", 3), // 最大失败次数
            Time.of(config.getLong("restart.failure-rate.failure-rate-interval", 300), TimeUnit.SECONDS), // 时间窗口：5分钟
            Time.of(config.getLong("restart.failure-rate.delay", 30), TimeUnit.SECONDS) // 重启延迟：30秒
        ));
        
        log.info("重启策略配置完成");
    }
    
    /**
     * 创建 PostgreSQL CDC 源
     */
    private static PostgreSQLSource<String> createPostgreSQLSource(Configuration config) {
        Properties debeziumProps = new Properties();
        debeziumProps.setProperty("decimal.handling.mode", "string");
        debeziumProps.setProperty("bigint.unsigned.handling.mode", "long");
        
        return PostgreSQLSource.<String>builder()
            .hostname(config.getString("postgresql.hostname"))
            .port(config.getInteger("postgresql.port", 5432))
            .database(config.getString("postgresql.database"))
            .schemaList(config.getString("postgresql.schema", "public"))
            .tableList(config.getString("postgresql.table-list", "public.cert_labels"))
            .username(config.getString("postgresql.username"))
            .password(config.getString("postgresql.password"))
            .slotName(config.getString("postgresql.slot-name", "cert_labels_cdc_slot"))
            .decodingPluginName("pgoutput")
            .deserializer(new JsonDebeziumDeserializationSchema())
            .debeziumProperties(debeziumProps)
            .build();
    }
    
    /**
     * 创建 Doris Sink
     */
    private static DorisSink<CertLabelUpdate> createDorisSink(Configuration config) {
        DorisOptions dorisOptions = DorisOptions.builder()
            .setFenodes(config.getString("doris.fenodes"))
            .setTableIdentifier(config.getString("doris.table-identifier", "nta.dim_cert"))
            .setUsername(config.getString("doris.username"))
            .setPassword(config.getString("doris.password"))
            .build();
        
        DorisExecutionOptions executionOptions = DorisExecutionOptions.builder()
            .setBatchSize(config.getInteger("doris.batch-size", 1000))
            .setBatchIntervalMs(config.getLong("doris.batch-interval-ms", 5000L))
            .setMaxRetries(config.getInteger("doris.max-retries", 3))
            .setStreamLoadProp(getStreamLoadProperties(config))
            .build();
        
        return DorisSink.<CertLabelUpdate>builder()
            .setDorisReadOptions(DorisReadOptions.builder().build())
            .setDorisOptions(dorisOptions)
            .setDorisExecutionOptions(executionOptions)
            .setSerializer(new CertLabelUpdateSerializer())
            .build();
    }
    
    /**
     * 获取 Stream Load 属性
     */
    private static Properties getStreamLoadProperties(Configuration config) {
        Properties props = new Properties();
        props.setProperty("format", "json");
        props.setProperty("read_json_by_line", "true");
        props.setProperty("load_to_single_tablet", "false");
        props.setProperty("timeout", "600");
        return props;
    }
}

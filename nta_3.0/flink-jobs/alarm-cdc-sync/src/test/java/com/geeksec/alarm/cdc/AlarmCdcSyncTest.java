package com.geeksec.alarm.cdc;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警CDC同步作业测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class AlarmCdcSyncTest {
    
    private ObjectMapper objectMapper;
    private AlarmRecordTransformer transformer;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        transformer = new AlarmRecordTransformer();
    }
    
    @Test
    void testAlarmRecordTransformation() throws Exception {
        // 模拟 PostgreSQL CDC 数据
        String cdcData = """
            {
                "before": null,
                "after": {
                    "id": 1,
                    "rule_id": 1001,
                    "alarm_title": "恶意域名访问",
                    "alarm_content": "检测到访问恶意域名 malicious.example.com",
                    "severity": "HIGH",
                    "status": "OPEN",
                    "source_data": {
                        "detector": "dns-analyzer",
                        "domain": "malicious.example.com",
                        "query_type": "A"
                    },
                    "triggered_at": 1703145600000,
                    "resolved_at": null,
                    "resolved_by": null,
                    "created_at": 1703145600000,
                    "updated_at": 1703145600000,
                    "alarm_type": "DNS_MALICIOUS",
                    "attack_stage": "RECONNAISSANCE",
                    "threat_type": "MALWARE",
                    "confidence": 0.95,
                    "src_ip": "*************",
                    "src_port": 53124,
                    "dst_ip": "*******",
                    "dst_port": 53,
                    "protocol": "UDP",
                    "session_id": "session_12345",
                    "task_id": "task_67890",
                    "data_source": "dns-probe-01"
                },
                "source": {
                    "version": "1.9.7.Final",
                    "connector": "postgresql",
                    "name": "alarm_cdc",
                    "ts_ms": 1703145600000,
                    "snapshot": "false",
                    "db": "nta",
                    "sequence": "[null,\\"123456\\"]",
                    "schema": "public",
                    "table": "alarm_records",
                    "txId": 789,
                    "lsn": 123456
                },
                "op": "c",
                "ts_ms": 1703145600000,
                "transaction": null
            }
            """;
        
        // 测试过滤器
        AlarmRecordFilter filter = new AlarmRecordFilter();
        assertTrue(filter.filter(cdcData), "应该通过过滤器");
        
        // 测试转换器
        String transformedData = transformer.map(cdcData);
        assertNotNull(transformedData, "转换结果不应为空");
        
        // 验证转换后的数据结构
        JsonNode result = objectMapper.readTree(transformedData);
        
        // 验证基础字段
        assertEquals("恶意域名访问", result.get("alarm_name").asText());
        assertEquals("检测到访问恶意域名 malicious.example.com", result.get("alarm_desc").asText());
        assertEquals("HIGH", result.get("attack_level").asText());
        assertEquals("OPEN", result.get("alarm_status").asText());
        assertEquals("DNS_MALICIOUS", result.get("alarm_type").asText());
        assertEquals("MALWARE", result.get("threat_type").asText());
        assertEquals("RECONNAISSANCE", result.get("attack_stage").asText());
        
        // 验证数值字段
        assertEquals(1001, result.get("rule_id").asLong());
        assertEquals(0.95, result.get("confidence").asDouble(), 0.001);
        
        // 验证网络字段
        assertEquals("*************", result.get("src_ip").asText());
        assertEquals(53124, result.get("src_port").asInt());
        assertEquals("*******", result.get("dst_ip").asText());
        assertEquals(53, result.get("dst_port").asInt());
        assertEquals("UDP", result.get("protocol").asText());
        
        // 验证扩展字段
        assertEquals("session_12345", result.get("session_id").asText());
        assertEquals("task_67890", result.get("task_id").asText());
        assertEquals("dns-probe-01", result.get("data_source").asText());
        
        // 验证时间字段格式
        assertTrue(result.has("event_time"));
        assertTrue(result.has("alarm_create_time"));
        assertTrue(result.has("alarm_update_time"));
        
        // 验证告警ID生成
        assertTrue(result.has("alarm_id"));
        assertFalse(result.get("alarm_id").asText().isEmpty());
        
        System.out.println("转换后的数据: " + transformedData);
    }
    
    @Test
    void testFilterNonAlarmRecords() throws Exception {
        // 测试非告警记录表的数据应该被过滤掉
        String nonAlarmData = """
            {
                "source": {
                    "schema": "public",
                    "table": "other_table"
                },
                "op": "c"
            }
            """;
        
        AlarmRecordFilter filter = new AlarmRecordFilter();
        assertFalse(filter.filter(nonAlarmData), "非告警记录应该被过滤掉");
    }
    
    @Test
    void testFilterDeleteOperations() throws Exception {
        // 测试删除操作
        String deleteData = """
            {
                "source": {
                    "schema": "public",
                    "table": "alarm_records"
                },
                "op": "d"
            }
            """;
        
        AlarmRecordFilter filter = new AlarmRecordFilter();
        assertTrue(filter.filter(deleteData), "删除操作应该通过过滤器");
    }
    
    @Test
    void testTransformationWithNullValues() throws Exception {
        // 测试包含空值的数据转换
        String cdcDataWithNulls = """
            {
                "after": {
                    "id": 2,
                    "alarm_title": "测试告警",
                    "alarm_content": null,
                    "severity": "MEDIUM",
                    "status": "OPEN",
                    "triggered_at": 1703145600000,
                    "created_at": 1703145600000,
                    "updated_at": 1703145600000,
                    "src_ip": "********",
                    "dst_ip": null,
                    "protocol": "TCP"
                },
                "source": {
                    "schema": "public",
                    "table": "alarm_records"
                },
                "op": "c"
            }
            """;
        
        String transformedData = transformer.map(cdcDataWithNulls);
        assertNotNull(transformedData);
        
        JsonNode result = objectMapper.readTree(transformedData);
        assertEquals("测试告警", result.get("alarm_name").asText());
        assertEquals("MEDIUM", result.get("attack_level").asText());
        assertEquals("********", result.get("src_ip").asText());
        
        // 空值字段应该不存在或为null
        assertTrue(!result.has("alarm_desc") || result.get("alarm_desc").isNull());
        assertTrue(!result.has("dst_ip") || result.get("dst_ip").isNull());
    }
}

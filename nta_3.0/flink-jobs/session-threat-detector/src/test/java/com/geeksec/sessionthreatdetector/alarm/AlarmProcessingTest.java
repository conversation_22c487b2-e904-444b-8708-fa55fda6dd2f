package com.geeksec.session.threat.detector.alarm;

import com.geeksec.session.threat.detector.alarm.batch.AlarmBatch;
import com.geeksec.session.threat.detector.alarm.batch.AlarmBatchProcessor;
import com.geeksec.session.threat.detector.alarm.batch.BatchProcessResult;
import com.geeksec.session.threat.detector.alarm.deduplication.AlarmDeduplicationManager;
import com.geeksec.session.threat.detector.alarm.deduplication.strategy.ContentHashDeduplicationStrategy;
import com.geeksec.session.threat.detector.alarm.deduplication.strategy.TimeWindowDeduplicationStrategy;
import com.geeksec.session.threat.detector.alarm.deduplication.strategy.AttackChainDeduplicationStrategy;
import com.geeksec.session.threat.detector.model.output.Alarm;
import com.geeksec.session.threat.detector.detection.DetectorType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警处理测试类
 * 
 * <AUTHOR>
 */
public class AlarmProcessingTest {
    
    private static final Logger log = LoggerFactory.getLogger(AlarmProcessingTest.class);
    
    private AlarmDeduplicationManager deduplicationManager;
    private AlarmBatchProcessor batchProcessor;
    
    @BeforeEach
    void setUp() {
        deduplicationManager = new AlarmDeduplicationManager();
        batchProcessor = new AlarmBatchProcessor("TestProcessor", 5, 10000L);
    }
    
    @Test
    void testAlarmBatch() {
        log.info("测试告警批次功能");
        
        AlarmBatch batch = new AlarmBatch("test-batch", 3, 5000L);
        
        // 测试添加告警
        Alarm alarm1 = createTestAlarm("test1", "192.168.1.1", "192.168.1.2");
        Alarm alarm2 = createTestAlarm("test2", "192.168.1.1", "192.168.1.3");
        Alarm alarm3 = createTestAlarm("test3", "192.168.1.2", "192.168.1.3");
        
        assertTrue(batch.addAlarm(alarm1));
        assertTrue(batch.addAlarm(alarm2));
        assertTrue(batch.addAlarm(alarm3));
        
        // 批次应该已满
        assertEquals(AlarmBatch.BatchStatus.FULL, batch.getStatus());
        assertTrue(batch.shouldProcess());
        assertEquals(3, batch.getAlarmCount().get());
        
        log.info("告警批次测试通过");
    }
    
    @Test
    void testContentHashDeduplication() {
        log.info("测试内容哈希去重");
        
        ContentHashDeduplicationStrategy strategy = new ContentHashDeduplicationStrategy();
        
        Alarm alarm1 = createTestAlarm("test", "192.168.1.1", "192.168.1.2");
        Alarm alarm2 = createTestAlarm("test", "192.168.1.1", "192.168.1.2"); // 相同内容
        Alarm alarm3 = createTestAlarm("different", "192.168.1.1", "192.168.1.2"); // 不同内容
        
        // 第一次检查，不应该重复
        assertFalse(strategy.isDuplicate(alarm1));
        strategy.recordAlarm(alarm1);
        
        // 第二次检查相同告警，应该重复
        assertTrue(strategy.isDuplicate(alarm2));
        
        // 检查不同告警，不应该重复
        assertFalse(strategy.isDuplicate(alarm3));
        
        log.info("内容哈希去重测试通过");
    }
    
    @Test
    void testTimeWindowDeduplication() {
        log.info("测试时间窗口去重");
        
        TimeWindowDeduplicationStrategy strategy = new TimeWindowDeduplicationStrategy(1000L, 100);
        
        Alarm alarm1 = createTestAlarm("test", "192.168.1.1", "192.168.1.2");
        Alarm alarm2 = createTestAlarm("test", "192.168.1.1", "192.168.1.2"); // 相同内容
        
        // 第一次检查，不应该重复
        assertFalse(strategy.isDuplicate(alarm1));
        strategy.recordAlarm(alarm1);
        
        // 在时间窗口内检查相同告警，应该重复
        assertTrue(strategy.isDuplicate(alarm2));
        
        // 等待时间窗口过期
        try {
            Thread.sleep(1100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 时间窗口过期后，不应该重复
        assertFalse(strategy.isDuplicate(alarm2));
        
        log.info("时间窗口去重测试通过");
    }
    
    @Test
    void testAttackChainDeduplication() {
        log.info("测试攻击链去重");
        
        AttackChainDeduplicationStrategy strategy = new AttackChainDeduplicationStrategy();
        
        Alarm alarm1 = createTestAlarm("webshell", "192.168.1.100", "192.168.1.200");
        alarm1.setDetectorType(DetectorType.WEBSHELL);
        alarm1.setThreatType("WebShell攻击");
        
        Alarm alarm2 = createTestAlarm("webshell", "192.168.1.100", "192.168.1.200");
        alarm2.setDetectorType(DetectorType.WEBSHELL);
        alarm2.setThreatType("WebShell攻击");
        
        // 第一次检查，不应该重复
        assertFalse(strategy.isDuplicate(alarm1));
        strategy.recordAlarm(alarm1);
        
        // 检查相同攻击链，应该重复
        assertTrue(strategy.isDuplicate(alarm2));
        
        log.info("攻击链去重测试通过");
    }
    
    @Test
    void testDeduplicationManager() {
        log.info("测试去重管理器");
        
        Alarm alarm1 = createTestAlarm("test", "192.168.1.1", "192.168.1.2");
        Alarm alarm2 = createTestAlarm("test", "192.168.1.1", "192.168.1.2"); // 相同内容
        Alarm alarm3 = createTestAlarm("different", "192.168.1.3", "192.168.1.4"); // 不同内容
        
        // 第一次检查，不应该重复
        assertFalse(deduplicationManager.isDuplicate(alarm1));
        
        // 第二次检查相同告警，应该重复
        assertTrue(deduplicationManager.isDuplicate(alarm2));
        
        // 检查不同告警，不应该重复
        assertFalse(deduplicationManager.isDuplicate(alarm3));
        
        // 检查统计信息
        var stats = deduplicationManager.getOverallStatistics();
        assertEquals(3, stats.getTotalProcessed());
        assertEquals(1, stats.getDuplicateCount());
        assertEquals(2, stats.getPassedCount());
        
        log.info("去重管理器测试通过");
    }
    
    @Test
    void testBatchProcessor() {
        log.info("测试批量处理器");
        
        List<Alarm> alarms = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            alarms.add(createTestAlarm("test" + i, "192.168.1." + i, "192.168.1." + (i + 10)));
        }
        
        // 测试批量处理
        BatchProcessResult result = batchProcessor.processBatch(alarms);
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(3, result.getProcessedCount());
        assertEquals(3, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        
        log.info("批量处理器测试通过: {}", result.getFormattedResult());
    }
    
    @Test
    void testBatchProcessorAutoTrigger() {
        log.info("测试批量处理器自动触发");
        
        // 添加告警直到触发批量处理
        for (int i = 0; i < 5; i++) {
            Alarm alarm = createTestAlarm("test" + i, "192.168.1." + i, "192.168.1." + (i + 10));
            boolean processed = batchProcessor.processAlarm(alarm);
            assertTrue(processed);
        }
        
        // 检查统计信息
        var stats = batchProcessor.getStatistics();
        assertTrue(stats.getTotalBatches() > 0);
        assertTrue(stats.getTotalAlarms() >= 5);
        
        log.info("批量处理器自动触发测试通过");
    }
    
    @Test
    void testBatchTimeout() throws InterruptedException {
        log.info("测试批次超时");
        
        AlarmBatch batch = new AlarmBatch("timeout-test", 10, 1000L); // 1秒超时
        
        // 添加一个告警
        Alarm alarm = createTestAlarm("test", "192.168.1.1", "192.168.1.2");
        assertTrue(batch.addAlarm(alarm));
        
        // 初始状态不应该处理
        assertFalse(batch.shouldProcess());
        
        // 等待超时
        Thread.sleep(1100);
        
        // 超时后应该处理
        assertTrue(batch.shouldProcess());
        assertTrue(batch.isTimeout());
        
        log.info("批次超时测试通过");
    }
    
    @Test
    void testDeduplicationModes() {
        log.info("测试去重模式");
        
        // 测试ANY模式
        deduplicationManager.setMode(AlarmDeduplicationManager.DeduplicationMode.ANY);
        
        Alarm alarm1 = createTestAlarm("test", "192.168.1.1", "192.168.1.2");
        Alarm alarm2 = createTestAlarm("test", "192.168.1.1", "192.168.1.2");
        
        assertFalse(deduplicationManager.isDuplicate(alarm1));
        assertTrue(deduplicationManager.isDuplicate(alarm2));
        
        // 测试ALL模式
        deduplicationManager.reset();
        deduplicationManager.setMode(AlarmDeduplicationManager.DeduplicationMode.ALL);
        
        assertFalse(deduplicationManager.isDuplicate(alarm1));
        // ALL模式下，需要所有策略都认为重复才算重复，通常不会重复
        assertFalse(deduplicationManager.isDuplicate(alarm2));
        
        log.info("去重模式测试通过");
    }
    
    /**
     * 创建测试告警
     */
    private Alarm createTestAlarm(String name, String srcIp, String dstIp) {
        return Alarm.builder()
                .alarmId("alarm-" + System.nanoTime())
                .alarmName(name)
                .alarmType("测试告警")
                .threatType("测试威胁")
                .srcIp(srcIp)
                .dstIp(dstIp)
                .srcPort(12345)
                .dstPort(80)
                .protocol("TCP")
                .description("测试告警描述")
                .detectorName("TestDetector")
                .confidence(0.9)
                .timestamp(LocalDateTime.now())
                .build();
    }
}

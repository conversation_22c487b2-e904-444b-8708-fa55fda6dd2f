package com.geeksec.session.threat.detector.detection.detector.webshell;

import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.HttpInfo;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebShell检测器测试
 * 
 * <AUTHOR>
 */
public class WebShellDetectorTest {
    
    private static final Logger log = LoggerFactory.getLogger(WebShellDetectorTest.class);
    
    private WebShellDetector detector;
    
    @BeforeEach
    void setUp() {
        detector = new WebShellDetector();
    }
    
    @Test
    void testDetectorInfo() {
        log.info("测试检测器基本信息");
        
        assertEquals("WebShell检测", detector.getDetectorType().getDisplayName());
        assertNotNull(detector.getDetectorType());
        assertTrue(detector.isEnabled());
        assertEquals(20, detector.getPriority());
        
        log.info("检测器基本信息测试通过");
    }
    
    @Test
    void testChineseCaidaoDetection() {
        log.info("测试中国菜刀检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置中国菜刀特征
        httpInfo.setRequestBody("cmd=%40eval%01%28base64_decode%28%24_POST%5Bz0%5D%29%29%3B&posix_getpwuid=test&system=test");
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("中国菜刀"));
        assertEquals(DetectionResult.ThreatLevel.HIGH, result.getThreatLevel());
        
        log.info("中国菜刀检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testAntswordDetection() {
        log.info("测试蚁剑检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置蚁剑特征
        httpInfo.setRequestBody("function%20HexAsciiConvert(hex%3AString)=test&other=data");
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("蚁剑"));
        assertEquals(DetectionResult.ThreatLevel.HIGH, result.getThreatLevel());
        
        log.info("蚁剑检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testWebacooDetection() {
        log.info("测试WeBacoo检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置WeBacoo Cookie特征
        Map<String, String> headers = new HashMap<>();
        headers.put("Cookie", "cm=aXBjb25maWc=; cn=test; cp=data");
        httpInfo.setRequestHeaders(headers);
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("WeBacoo"));
        assertEquals(DetectionResult.ThreatLevel.HIGH, result.getThreatLevel());
        
        log.info("WeBacoo检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testBehinderDetection() {
        log.info("测试Behinder检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置Behinder响应特征
        httpInfo.setResponseBody("some data ->| encrypted content |<- more data");
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("Behinder"));
        assertEquals(DetectionResult.ThreatLevel.HIGH, result.getThreatLevel());
        
        log.info("Behinder检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testWeevelyDetection() {
        log.info("测试Weevely检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置Weevely特征 - 请求体和响应体第17-28位字符相同
        String requestBody = "1234567890123456" + "0f1b6a831c3" + "remaining_content";
        String responseBody = "9876543210987654" + "0f1b6a831c3" + "different_content";
        
        httpInfo.setRequestBody(requestBody);
        httpInfo.setResponseBody(responseBody);
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("Weevely"));
        assertEquals(DetectionResult.ThreatLevel.HIGH, result.getThreatLevel());
        
        log.info("Weevely检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testSuspiciousPatternDetection() {
        log.info("测试可疑模式检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置可疑模式
        httpInfo.setRequestBody("some data with eval( function call");
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("可疑WebShell"));
        assertEquals(DetectionResult.ThreatLevel.MEDIUM, result.getThreatLevel());
        
        log.info("可疑模式检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testSuspiciousUrlDetection() {
        log.info("测试可疑URL检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置可疑URL
        httpInfo.setUri("/admin/shell.php?cmd=whoami");
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("可疑WebShell"));
        
        log.info("可疑URL检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testSuspiciousUserAgentDetection() {
        log.info("测试可疑User-Agent检测");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置可疑User-Agent
        httpInfo.setUserAgent("webshell-client/1.0");
        
        List<DetectionResult> results = detector.detect(event);
        
        assertFalse(results.isEmpty());
        DetectionResult result = results.get(0);
        assertTrue(result.getThreatName().contains("可疑WebShell"));
        assertEquals(DetectionResult.ThreatLevel.MEDIUM, result.getThreatLevel());
        
        log.info("可疑User-Agent检测测试通过: {}", result.getThreatName());
    }
    
    @Test
    void testNonHttpEvent() {
        log.info("测试非HTTP事件");
        
        NetworkEvent event = NetworkEvent.builder()
                .eventType(NetworkEvent.EventType.DNS)
                .sessionId("test-session")
                .srcIp("*************")
                .dstIp("*************")
                .srcPort(12345)
                .dstPort(53)
                .protocol("UDP")
                .build();
        
        List<DetectionResult> results = detector.detect(event);
        
        assertTrue(results.isEmpty());
        
        log.info("非HTTP事件测试通过");
    }
    
    @Test
    void testEmptyHttpContent() {
        log.info("测试空HTTP内容");
        
        NetworkEvent event = createHttpEvent();
        HttpInfo httpInfo = event.getHttpInfo();
        
        // 设置空内容
        httpInfo.setRequestBody("");
        httpInfo.setResponseBody("");
        httpInfo.setUri("/normal.html");
        httpInfo.setUserAgent("Mozilla/5.0");
        
        List<DetectionResult> results = detector.detect(event);
        
        assertTrue(results.isEmpty());
        
        log.info("空HTTP内容测试通过");
    }
    
    /**
     * 创建HTTP事件用于测试
     */
    private NetworkEvent createHttpEvent() {
        HttpInfo httpInfo = HttpInfo.builder()
                .method("POST")
                .uri("/test.php")
                .version("HTTP/1.1")
                .host("example.com")
                .userAgent("Mozilla/5.0")
                .contentType("application/x-www-form-urlencoded")
                .statusCode(200)
                .build();
        
        return NetworkEvent.builder()
                .eventType(NetworkEvent.EventType.HTTP)
                .sessionId("test-session")
                .srcIp("*************")
                .dstIp("*************")
                .srcPort(12345)
                .dstPort(80)
                .protocol("TCP")
                .httpInfo(httpInfo)
                .build();
    }
}

{
  "Que": 1,
  "Add": 13,
  "StartNSec": 383060145,
  "dPort": 53,
  "TaskId": 1,
  "Query": [
    {
      "name": "d3g.qq.com",
      "type": 1, // 查一下PTR值进行过滤 （请求类型）
      "class": 1
    }
  ],
  "Ans": 11, //0 不存在 有数字为成功 返回结果
  "DomainIp": "***************|***************|***************|***************|***************|***************|*************",
  "StartTime": 1654859047,
  "dIp": "***************",
  "Flags": 33152, // 用于判断不存在 or 错误
  "type": "dns",
  "sPort": 51746,
  "AppName": "APP_DNS",
  // 5 CNAME 1、2、8 IP
  "Answer": [
    {
      "data_len": 9,
      "name": "d3g.qq.com",
      "type": 5,
      "class": 1,
      "ttl": 895,
      "value": "d3g.tc.qq.com"
    },
    {
      "data_len": 11,
      "name": "d3g.tc.qq.com",
      "type": 5,
      "class": 1,
      "ttl": 600,
      "value": "d3g.tcdn.qq.com"
    },
    {
      "data_len": 7,
      "name": "d3g.tcdn.qq.com",
      "type": 5,
      "class": 1,
      "ttl": 600,
      "value": "3gdl.tc.qq.com"
    },
    {
      "data_len": 7,
      "name": "3gdl.tc.qq.com",
      "type": 5,
      "class": 1,
      "ttl": 600,
      "value": "3gdl.tcdn.qq.com"
    },
    {
      "data_len": 4,
      "name": "3gdl.tcdn.qq.com",
      "type": 1,
      "class": 1,
      "ttl": 600,
      "value": "***************"
    },
    {
      "data_len": 4,
      "name": "3gdl.tcdn.qq.com",
      "type": 1,
      "class": 1,
      "ttl": 600,
      "value": "***************"
    },
    {
      "data_len": 4,
      "name": "3gdl.tcdn.qq.com",
      "type": 1,
      "class": 1,
      "ttl": 600,
      "value": "***************"
    },
    {
      "data_len": 4,
      "name": "3gdl.tcdn.qq.com",
      "type": 1,
      "class": 1,
      "ttl": 600,
      "value": "***************"
    },
    {
      "data_len": 4,
      "name": "3gdl.tcdn.qq.com",
      "type": 1,
      "class": 1,
      "ttl": 600,
      "value": "***************"
    },
    {
      "data_len": 4,
      "name": "3gdl.tcdn.qq.com",
      "type": 1,
      "class": 1,
      "ttl": 600,
      "value": "***************"
    },
    {
      "data_len": 4,
      "name": "3gdl.tcdn.qq.com",
      "type": 1,
      "class": 1,
      "ttl": 600,
      "value": "*************"
    }
  ],
  "es_key": "dns_1_100002",
  "Hkey": "dns_1_100002_20220612_8351333329318288728",
  "Auth": 4,
  "sIp": "**************",
  "Domain": "d3g.qq.com", // 判断PTR请求
  "SessionId": "8351333329318288728"
}

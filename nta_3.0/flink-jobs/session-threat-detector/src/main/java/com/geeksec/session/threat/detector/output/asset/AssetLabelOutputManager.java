package com.geeksec.session.threat.detector.output.asset;

import com.geeksec.session.threat.detector.model.output.AssetLabel;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;

/**
 * 资产标签输出管理器
 * 负责将资产标签写入Nebula图数据库（自环边形式）
 *
 * <AUTHOR>
 */
@Slf4j
public class AssetLabelOutputManager {

    /**
     * 私有构造方法，防止实例化
     */
    private AssetLabelOutputManager() {
        // 工具类，禁止实例化
    }

    /**
     * 配置资产标签输出
     *
     * @param assetLabelStream 资产标签数据流
     * @param config 配置参数
     */
    public static void configure(DataStream<AssetLabel> assetLabelStream, ParameterTool config) {
        log.info("配置资产标签输出到Nebula图数据库");

        // 添加日志输出Sink（用于调试）
        assetLabelStream
                .addSink(new AssetLabelSink())
                .name("资产标签日志输出")
                .uid("asset-label-log-sink");

        // TODO: 添加Nebula Sink
        // assetLabelStream
        //     .addSink(createNebulaSink(config))
        //     .name("资产标签Nebula输出")
        //     .uid("asset-label-nebula-sink");

        log.info("资产标签输出配置完成");
    }

    /**
     * 资产标签输出Sink
     */
    private static class AssetLabelSink implements SinkFunction<AssetLabel> {
        private static final long serialVersionUID = 1L;

        @Override
        public void invoke(AssetLabel value, Context context) {
            log.info("资产标签: 源IP={}, 目标IP={}, 标签ID={}, 标签={}",
                    value.getSrcIp(),
                    value.getDstIp(),
                    value.getLabelId(),
                    value.getLabelValue());
        }
    }
}

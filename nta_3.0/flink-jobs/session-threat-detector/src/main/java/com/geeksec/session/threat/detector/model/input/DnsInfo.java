package com.geeksec.session.threat.detector.model.input;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * DNS协议相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DnsInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询域名
     */
    private String queryName;

    /**
     * 查询类型
     */
    private String queryType;

    /**
     * 查询类
     */
    private String queryClass;

    /**
     * 响应代码
     */
    private Integer responseCode;

    /**
     * 权威应答
     */
    private Boolean authoritative;

    /**
     * 递归查询
     */
    private Boolean recursive;

    /**
     * 查询ID
     */
    private Integer queryId;

    /**
     * 应答记录
     */
    private List<String> answers;

    /**
     * 权威记录
     */
    private List<String> authorities;

    /**
     * 附加记录
     */
    private List<String> additionals;

    /**
     * TTL值
     */
    private Long ttl;

    /**
     * 查询长度
     */
    private Integer queryLength;

    /**
     * 响应长度
     */
    private Integer responseLength;

    /**
     * 是否为反向查询
     */
    private Boolean reverseQuery;
}

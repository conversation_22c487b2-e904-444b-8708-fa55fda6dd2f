package com.geeksec.session.threat.detector.detection.detector.webshell;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * WebShell工具特征签名
 * 定义WebShell工具的检测特征和相关信息
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebShellSignature implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * WebShell工具名称
     */
    private String toolName;
    
    /**
     * 工具类型（加密/非加密）
     */
    private String toolType;
    
    /**
     * 编码类型（base64/hex/xor等）
     */
    private String encodingType;
    
    /**
     * 加密算法类型（如果是加密WebShell）
     */
    private String encryptionType;
    
    /**
     * 是否为单向通信
     */
    private boolean isOneWay;
    
    /**
     * 检测特征模式列表
     */
    private List<String> patterns;
    
    /**
     * 特征描述
     */
    private String description;
    
    /**
     * 检测原理
     */
    private String principle;
    
    /**
     * 处理建议
     */
    private String handleMethod;
    
    /**
     * 威胁等级
     */
    private String threatLevel;
    
    /**
     * 置信度
     */
    private double confidence;
    
    /**
     * 检测位置（payload/cookie/header/response/url）
     */
    private String detectionLocation;
    
    /**
     * 是否需要多段验证
     */
    private boolean requiresMultiSegment;
    
    /**
     * 验证段数（如果需要多段验证）
     */
    private int requiredSegments;
    
    /**
     * 创建基于负载的WebShell特征
     * 
     * @param toolName 工具名称
     * @param patterns 特征模式
     * @param encodingType 编码类型
     * @param description 描述
     * @return WebShell特征
     */
    public static WebShellSignature createPayloadSignature(String toolName, List<String> patterns, 
                                                          String encodingType, String description) {
        return WebShellSignature.builder()
                .toolName(toolName)
                .toolType("非加密webshell")
                .encodingType(encodingType)
                .patterns(patterns)
                .description(description)
                .principle("根据请求包请求体内容检测出符合" + toolName + "攻击工具的特征。")
                .handleMethod("检查服务器是否被" + toolName + "攻击工具攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。")
                .threatLevel("HIGH")
                .confidence(0.9)
                .detectionLocation("payload")
                .requiresMultiSegment(true)
                .requiredSegments(2)
                .build();
    }
    
    /**
     * 创建基于Cookie的WebShell特征
     * 
     * @param toolName 工具名称
     * @param patterns 特征模式
     * @param description 描述
     * @return WebShell特征
     */
    public static WebShellSignature createCookieSignature(String toolName, List<String> patterns, String description) {
        return WebShellSignature.builder()
                .toolName(toolName)
                .toolType("非加密webshell")
                .encodingType("base64")
                .patterns(patterns)
                .description(description)
                .principle("根据请求包请求头内容检测出符合" + toolName + "攻击工具的特征。")
                .handleMethod("检查服务器是否被" + toolName + "攻击工具攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。")
                .threatLevel("HIGH")
                .confidence(0.9)
                .detectionLocation("cookie")
                .requiresMultiSegment(false)
                .build();
    }
    
    /**
     * 创建基于请求头的WebShell特征
     * 
     * @param toolName 工具名称
     * @param patterns 特征模式
     * @param description 描述
     * @return WebShell特征
     */
    public static WebShellSignature createHeaderSignature(String toolName, List<String> patterns, String description) {
        return WebShellSignature.builder()
                .toolName(toolName)
                .toolType("加密webshell")
                .encodingType("base64")
                .patterns(patterns)
                .description(description)
                .principle("根据请求包请求头和响应头内容检测出符合" + toolName + "攻击工具的特征。")
                .handleMethod("检查服务器是否被" + toolName + "攻击工具攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。")
                .threatLevel("HIGH")
                .confidence(0.9)
                .detectionLocation("header")
                .requiresMultiSegment(false)
                .build();
    }
    
    /**
     * 创建基于响应的WebShell特征
     * 
     * @param toolName 工具名称
     * @param patterns 特征模式
     * @param description 描述
     * @return WebShell特征
     */
    public static WebShellSignature createResponseSignature(String toolName, List<String> patterns, String description) {
        return WebShellSignature.builder()
                .toolName(toolName)
                .toolType("加密webshell")
                .encodingType("base64")
                .patterns(patterns)
                .description(description)
                .principle("根据请求包请求体和响应体内容检测出符合" + toolName + "攻击工具的特征。")
                .handleMethod("检查服务器是否被" + toolName + "攻击工具攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。")
                .threatLevel("HIGH")
                .confidence(0.9)
                .detectionLocation("response")
                .requiresMultiSegment(false)
                .build();
    }
    
    /**
     * 创建加密WebShell特征
     * 
     * @param toolName 工具名称
     * @param encryptionType 加密类型
     * @param patterns 特征模式
     * @param description 描述
     * @return WebShell特征
     */
    public static WebShellSignature createEncryptedSignature(String toolName, String encryptionType, 
                                                            List<String> patterns, String description) {
        return WebShellSignature.builder()
                .toolName(toolName)
                .toolType("加密webshell")
                .encodingType("base64")
                .encryptionType(encryptionType)
                .patterns(patterns)
                .description(description)
                .principle("根据请求包请求头和请求体内容检测出符合" + toolName + "攻击工具的特征。")
                .handleMethod("检查服务器是否被" + toolName + "攻击工具攻击，监控系统日志审查是否出现异常行为，特别是注意远程登录、文件上传、进程管理等关键操作的日志记录。")
                .threatLevel("HIGH")
                .confidence(0.95)
                .detectionLocation("payload")
                .requiresMultiSegment(false)
                .build();
    }
    
    /**
     * 检查特征是否匹配
     * 
     * @param content 待检查的内容
     * @return 是否匹配
     */
    public boolean matches(String content) {
        if (content == null || patterns == null || patterns.isEmpty()) {
            return false;
        }
        
        String lowerContent = content.toLowerCase();
        for (String pattern : patterns) {
            if (lowerContent.contains(pattern.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取匹配的特征模式
     * 
     * @param content 待检查的内容
     * @return 匹配的模式，如果没有匹配返回null
     */
    public String getMatchedPattern(String content) {
        if (content == null || patterns == null || patterns.isEmpty()) {
            return null;
        }
        
        String lowerContent = content.toLowerCase();
        for (String pattern : patterns) {
            if (lowerContent.contains(pattern.toLowerCase())) {
                return pattern;
            }
        }
        return null;
    }
}

package com.geeksec.session.threat.detector.model.output;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.geeksec.session.threat.detector.detection.DetectorType;

/**
 * 资产标签数据模型
 * 用于写入Nebula图数据库的资产标签（自环边形式）
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssetLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 源IP地址（资产实体）
     */
    private String srcIp;

    /**
     * 目标IP地址（关联资产）
     */
    private String dstIp;

    /**
     * 标签ID（边的rank值）
     */
    private Long labelId;

    /**
     * 标签键
     */
    private String labelKey;

    /**
     * 标签值
     */
    private String labelValue;

    /**
     * 检测置信度
     */
    private Double confidence;

    /**
     * 检测器类型
     */
    private DetectorType detectorType;

    /**
     * 标签时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 边类型（用于Nebula）
     */
    private String edgeType;

    /**
     * 标签类型
     */
    private String labelType;

    /**
     * 标签描述
     */
    private String description;

    /**
     * 是否为威胁标签
     */
    private Boolean isThreat;

    /**
     * 威胁级别
     */
    private String threatLevel;

    /**
     * 标签权重
     */
    private Double weight;

    /**
     * 标签有效期（小时）
     */
    private Integer validHours;
}

package com.geeksec.session.threat.detector.control;

import java.util.HashMap;
import java.util.Map;

import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import com.geeksec.session.threat.detector.model.enums.DetectorTypeEnum;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * 检测器开关广播处理函数
 * 处理网络事件流和检测器开关配置广播流
 * 根据开关状态决定是否进行威胁检测
 * 
 * <AUTHOR>
 */
@Slf4j
public class DetectorSwitchBroadcastFunction
        extends BroadcastProcessFunction<NetworkEvent, Map<Integer, Integer>, NetworkEvent> {

    private static final long serialVersionUID = 1L;

    /**
     * 检测器开关管理器
     */
    private transient DetectorSwitchManager switchManager;

    /**
     * 统计信息
     */
    private transient long processedEventCount = 0;
    private transient long filteredEventCount = 0;
    private transient long lastLogTime = 0;
    private static final long LOG_INTERVAL_MS = 60000; // 1分钟

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 初始化检测器开关管理器
        switchManager = DetectorSwitchManager.getInstance();
        switchManager.initialize();

        log.info("检测器开关广播函数初始化完成");
        switchManager.printCurrentStatus();
    }

    @Override
    public void processElement(
            NetworkEvent networkEvent,
            BroadcastProcessFunction<NetworkEvent, Map<Integer, Integer>, NetworkEvent>.ReadOnlyContext ctx,
            Collector<NetworkEvent> out) throws Exception {

        if (networkEvent == null) {
            return;
        }

        processedEventCount++;

        try {
            // 获取只读广播状态
            ReadOnlyBroadcastState<Integer, Integer> readOnlyState = 
                    ctx.getBroadcastState(DetectorSwitchManager.DETECTOR_SWITCH_STATE_DESCRIPTOR);
            
            // 更新本地开关管理器状态
            updateLocalSwitchManager(readOnlyState);
            
            // 检查是否应该处理此事件
            if (shouldProcessEvent(networkEvent)) {
                out.collect(networkEvent);
            } else {
                filteredEventCount++;
                if (log.isDebugEnabled()) {
                    log.debug("事件被过滤，事件类型: {}, 源IP: {}, 目标IP: {}", 
                            networkEvent.getEventType(), 
                            networkEvent.getSrcIp(), 
                            networkEvent.getDstIp());
                }
            }
            
            // 定期打印统计信息
            printStatisticsIfNeeded();
            
        } catch (Exception e) {
            log.error("处理网络事件时发生异常: {}", e.getMessage(), e);
            // 异常情况下仍然输出事件，避免数据丢失
            out.collect(networkEvent);
        }
    }

    @Override
    public void processBroadcastElement(
            Map<Integer, Integer> switchUpdates,
            BroadcastProcessFunction<NetworkEvent, Map<Integer, Integer>, NetworkEvent>.Context ctx,
            Collector<NetworkEvent> out) throws Exception {
        
        if (switchUpdates == null || switchUpdates.isEmpty()) {
            return;
        }
        
        // 获取可写的广播状态
        BroadcastState<Integer, Integer> broadcastState = 
                ctx.getBroadcastState(DetectorSwitchManager.DETECTOR_SWITCH_STATE_DESCRIPTOR);
        
        // 更新广播状态
        for (Map.Entry<Integer, Integer> entry : switchUpdates.entrySet()) {
            broadcastState.put(entry.getKey(), entry.getValue());
        }
        
        // 更新本地开关管理器状态
        updateLocalSwitchManager(broadcastState);
        
        log.info("已更新检测器开关状态，更新数量: {}", switchUpdates.size());
    }

    /**
     * 更新本地开关管理器状态
     * 
     * @param broadcastState 广播状态
     */
    private void updateLocalSwitchManager(ReadOnlyBroadcastState<Integer, Integer> readOnlyState) throws Exception {
        try {
            // 创建映射来存储当前状态
            Map<Integer, Integer> currentState = new HashMap<>();
            
            // 使用 get() 方法获取每个键的值
            for (Integer key : switchManager.getAllValidDetectorIds()) {
                Integer value = readOnlyState.get(key);
                if (value != null) {
                    currentState.put(key, value);
                }
            }
            
            // 如果获取到状态，则更新本地管理器
            if (!currentState.isEmpty()) {
                switchManager.updateDetectorSwitches(currentState);
            }
        } catch (Exception e) {
            log.error("更新本地开关管理器状态时发生异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 判断是否应该处理此事件
     * 
     * @param networkEvent 网络事件
     * @return 是否应该处理
     */
    private boolean shouldProcessEvent(NetworkEvent networkEvent) {
        // 基本验证
        if (networkEvent.getEventType() == null) {
            log.warn("网络事件类型为空，跳过处理");
            return false;
        }

        // 根据事件类型判断相关的检测器是否启用
        // 这里可以根据具体的业务逻辑来映射事件类型到检测器
        return isAnyRelevantDetectorEnabled(networkEvent);
    }

    /**
     * 检查是否有相关的检测器启用
     * 
     * @param networkEvent 网络事件
     * @return 是否有相关检测器启用
     */
    private boolean isAnyRelevantDetectorEnabled(NetworkEvent networkEvent) {
        // 根据事件类型检查相关检测器
        switch (networkEvent.getEventType()) {
            case HTTP:
                return switchManager.isDetectorEnabled(DetectorTypeEnum.WEBSHELL) ||
                        switchManager.isDetectorEnabled(DetectorTypeEnum.WEB_LOGIN_BRUTE_FORCE) ||
                        switchManager.isDetectorEnabled(DetectorTypeEnum.HTTP_TUNNEL);

            case DNS:
                return switchManager.isDetectorEnabled(DetectorTypeEnum.DNS_TUNNEL) ||
                        switchManager.isDetectorEnabled(DetectorTypeEnum.MINING_CONNECTION);

            case SSL:
                return switchManager.isDetectorEnabled(DetectorTypeEnum.SSL_FINGERPRINT) ||
                        switchManager.isDetectorEnabled(DetectorTypeEnum.FINGERPRINT_RANDOMIZATION) ||
                        switchManager.isDetectorEnabled(DetectorTypeEnum.SSL_TUNNEL);

            case SESSION:
                return switchManager.isDetectorEnabled(DetectorTypeEnum.PORT_SCAN) ||
                        switchManager.isDetectorEnabled(DetectorTypeEnum.STANDARD_RC_C2) ||
                        switchManager.isDetectorEnabled(DetectorTypeEnum.UNKNOWN_RC_PROTOCOL);

            default:
                // 对于未知事件类型，检查是否有任何检测器启用
                return switchManager.getCurrentConfig().getEnabledDetectorCount() > 0;
        }
    }

    /**
     * 定期打印统计信息
     */
    private void printStatisticsIfNeeded() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastLogTime >= LOG_INTERVAL_MS) {
            log.info("检测器开关统计: 处理事件数={}, 过滤事件数={}, 通过率={:.2f}%",
                    processedEventCount,
                    filteredEventCount,
                    processedEventCount > 0
                            ? (double) (processedEventCount - filteredEventCount) / processedEventCount * 100
                            : 0.0);

            log.info("当前{}", switchManager.getStatistics());
            lastLogTime = currentTime;
        }
    }

    @Override
    public void close() throws Exception {
        super.close();

        // 打印最终统计信息
        log.info("检测器开关广播函数关闭，最终统计:");
        log.info("  总处理事件数: {}", processedEventCount);
        log.info("  过滤事件数: {}", filteredEventCount);
        log.info("  通过事件数: {}", processedEventCount - filteredEventCount);

        if (switchManager != null) {
            log.info("  {}", switchManager.getStatistics());
        }
    }
}

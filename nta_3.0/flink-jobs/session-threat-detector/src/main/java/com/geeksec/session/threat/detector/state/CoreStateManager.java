package com.geeksec.session.threat.detector.state;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.flink.common.config.ConfigurationManager;
import com.geeksec.flink.common.database.redis.RedisConnectionManager;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import redis.clients.jedis.Jedis;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 核心状态管理器，专注于跨作业共享的核心状态
 * 
 * <AUTHOR>
 */
@Slf4j
public class CoreStateManager implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /**
     * JSON序列化工具
     */
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 键前缀
     */
    private static final String KEY_PREFIX = "threat_detector:core:";

    /**
     * 攻击链键前缀
     */
    private static final String ATTACK_CHAIN_PREFIX = KEY_PREFIX + "attack_chain:";

    /**
     * 检测器键前缀
     */
    private static final String DETECTOR_PREFIX = KEY_PREFIX + "detector:";

    /**
     * 系统键前缀
     */
    private static final String SYSTEM_PREFIX = KEY_PREFIX + "system:";

    /**
     * 默认过期时间（秒）
     */
    private static final int DEFAULT_TTL = CONFIG.getInt("threat.detector.state.ttl.default", 24 * 3600); // 24小时
    private static final int ATTACK_CHAIN_TTL = CONFIG.getInt("threat.detector.state.ttl.attack-chain", 7 * 24 * 3600); // 7天
    private static final int DETECTOR_TTL = CONFIG.getInt("threat.detector.state.ttl.detector", 30 * 24 * 3600); // 30天

    /**
     * 单例实例
     */
    private static volatile CoreStateManager instance = null;

    /**
     * 私有构造函数
     */
    private CoreStateManager() {
        // 使用RedisConnectionManager，无需自己初始化连接池
        log.info("核心状态管理器初始化完成");
    }

    /**
     * 获取单例实例
     */
    public static CoreStateManager getInstance() {
        if (instance == null) {
            synchronized (CoreStateManager.class) {
                if (instance == null) {
                    instance = new CoreStateManager();
                }
            }
        }
        return instance;
    }

    // ==================== 攻击链状态管理 ====================

    /**
     * 保存活跃攻击链摘要
     */
    public void saveActiveAttackChain(String chainId, AttackChainSummary summary) {
        if (chainId == null || summary == null) {
            return;
        }

        try {
            String key = ATTACK_CHAIN_PREFIX + chainId;
            String value = serializeObject(summary);

            // 使用Redis连接管理器保存攻击链数据
            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    jedis.setex(key, ATTACK_CHAIN_TTL, value);

                    // 添加到活跃攻击链集合
                    String activeSetKey = ATTACK_CHAIN_PREFIX + "active_set";
                    jedis.sadd(activeSetKey, chainId);
                    jedis.expire(activeSetKey, ATTACK_CHAIN_TTL);
                }
            }

            log.debug("保存活跃攻击链: {}", chainId);

        } catch (Exception e) {
            log.error("保存活跃攻击链失败: {}", chainId, e);
        }
    }

    /**
     * 获取活跃攻击链摘要
     */
    public AttackChainSummary getActiveAttackChain(String chainId) {
        if (chainId == null) {
            return null;
        }

        try {
            String key = ATTACK_CHAIN_PREFIX + chainId;

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    String value = jedis.get(key);
                    if (value != null) {
                        return deserializeObject(value, AttackChainSummary.class);
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取活跃攻击链失败: {}", chainId, e);
        }

        return null;
    }

    /**
     * 获取所有活跃攻击链ID
     */
    public Set<String> getActiveAttackChainIds() {
        try {
            String activeSetKey = ATTACK_CHAIN_PREFIX + "active_set";
            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    return jedis.smembers(activeSetKey);
                }
            }
        } catch (Exception e) {
            log.error("获取活跃攻击链ID失败", e);
        }
        return new HashSet<>();
    }

    /**
     * 移除攻击链
     */
    public void removeAttackChain(String chainId) {
        if (chainId == null) {
            return;
        }

        try {
            String key = ATTACK_CHAIN_PREFIX + chainId;
            String activeSetKey = ATTACK_CHAIN_PREFIX + "active_set";

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    jedis.del(key);
                    jedis.srem(activeSetKey, chainId);
                }
            }

            log.debug("移除攻击链: {}", chainId);

        } catch (Exception e) {
            log.error("移除攻击链失败: {}", chainId, e);
        }
    }

    // ==================== 检测器状态管理 ====================

    /**
     * 设置检测器启用状态
     */
    public void setDetectorEnabled(String detectorName, boolean enabled) {
        if (detectorName == null) {
            return;
        }

        try {
            String key = DETECTOR_PREFIX + "enabled:" + detectorName;

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    jedis.setex(key, DETECTOR_TTL, String.valueOf(enabled));
                }
            }

            log.info("设置检测器状态: {} -> {}", detectorName, enabled);

        } catch (Exception e) {
            log.error("设置检测器状态失败: {}", detectorName, e);
        }
    }

    /**
     * 检查检测器是否启用
     */
    public boolean isDetectorEnabled(String detectorName) {
        if (detectorName == null) {
            // 默认启用
            return true;
        }

        try {
            String key = DETECTOR_PREFIX + "enabled:" + detectorName;

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    String value = jedis.get(key);
                    return value == null || Boolean.parseBoolean(value);
                }
            }
        } catch (Exception e) {
            log.error("检查检测器状态失败: {}", detectorName, e);
        }
        // 默认启用
        return true;
    }

    /**
     * 更新检测器健康状态
     */
    public void updateDetectorHealth(String detectorName, DetectorHealth health) {
        if (detectorName == null || health == null) {
            return;
        }

        try {
            String key = DETECTOR_PREFIX + "health:" + detectorName;
            String value = serializeObject(health);

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    jedis.setex(key, DEFAULT_TTL, value);
                }
            }

        } catch (Exception e) {
            log.error("更新检测器健康状态失败: {}", detectorName, e);
        }
    }

    /**
     * 获取检测器健康状态
     */
    public DetectorHealth getDetectorHealth(String detectorName) {
        if (detectorName == null) {
            return null;
        }

        try {
            String key = DETECTOR_PREFIX + "health:" + detectorName;

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    String value = jedis.get(key);
                    if (value != null) {
                        return deserializeObject(value, DetectorHealth.class);
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取检测器健康状态失败: {}", detectorName, e);
        }

        return null;
    }

    // ==================== 系统状态管理 ====================

    /**
     * 更新系统健康状态
     */
    public void updateSystemHealth(SystemHealth health) {
        if (health == null) {
            return;
        }

        try {
            String key = SYSTEM_PREFIX + "health";
            String value = serializeObject(health);

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    jedis.setex(key, DEFAULT_TTL, value);
                }
            }

        } catch (Exception e) {
            log.error("更新系统健康状态失败", e);
        }
    }

    /**
     * 获取系统健康状态
     */
    public SystemHealth getSystemHealth() {
        try {
            String key = SYSTEM_PREFIX + "health";

            try (Jedis jedis = RedisConnectionManager.getJedis()) {
                if (jedis != null) {
                    String value = jedis.get(key);
                    if (value != null) {
                        return deserializeObject(value, SystemHealth.class);
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
        }

        return null;
    }

    // ==================== 工具方法 ====================

    /**
     * 序列化对象
     */
    private String serializeObject(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("对象序列化失败", e);
        }
    }

    /**
     * 反序列化对象
     */
    private <T> T deserializeObject(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("对象反序列化失败", e);
        }
    }

    /**
     * 关闭连接池
     */
    public void close() {
        // 使用RedisConnectionManager，由其管理连接池的关闭
        RedisConnectionManager.closePool();
        log.info("核心状态管理器已关闭");
    }

    // ==================== 数据模型 ====================

    /**
     * 攻击链摘要（用于跨作业关联）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttackChainSummary implements Serializable {
        private static final long serialVersionUID = 1L;

        private String attackChainId;
        private String attackerIp;
        private String victimIp;
        private String currentStage;
        private double confidence;
        private LocalDateTime startTime;
        private LocalDateTime lastUpdateTime;
        private boolean active;
        private Map<String, Object> metadata;
    }

    /**
     * 检测器健康状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetectorHealth implements Serializable {
        private static final long serialVersionUID = 1L;

        private String detectorName;
        private boolean healthy;
        private String status;
        private LocalDateTime lastHeartbeat;
        private String errorMessage;
        private Map<String, Object> metrics;
    }

    /**
     * 系统健康状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemHealth implements Serializable {
        private static final long serialVersionUID = 1L;

        private boolean healthy;
        private LocalDateTime checkTime;
        private Map<String, Boolean> componentHealth;
        private Map<String, String> errorMessages;
        private Map<String, Object> systemMetrics;
    }
}

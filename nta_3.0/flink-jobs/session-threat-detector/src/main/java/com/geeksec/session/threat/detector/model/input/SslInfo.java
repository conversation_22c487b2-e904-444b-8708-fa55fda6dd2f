package com.geeksec.session.threat.detector.model.input;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * SSL/TLS协议相关信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SslInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SSL/TLS版本
     */
    private String version;

    /**
     * 服务器名称（SNI）
     */
    private String serverName;

    /**
     * 证书主题
     */
    private String certSubject;

    /**
     * 证书颁发者
     */
    private String certIssuer;

    /**
     * 证书序列号
     */
    private String certSerial;

    /**
     * 证书指纹
     */
    private String certFingerprint;

    /**
     * JA3客户端指纹
     */
    private String ja3Hash;

    /**
     * JA3S服务端指纹
     */
    private String ja3sHash;

    /**
     * 加密套件
     */
    private String cipherSuite;

    /**
     * 支持的加密套件列表
     */
    private List<String> supportedCiphers;

    /**
     * 支持的扩展列表
     */
    private List<String> extensions;

    /**
     * 证书链长度
     */
    private Integer certChainLength;

    /**
     * 是否自签名证书
     */
    private Boolean selfSigned;

    /**
     * 证书是否过期
     */
    private Boolean certExpired;

    /**
     * 握手是否成功
     */
    private Boolean handshakeSuccess;
}

package com.geeksec.session.threat.detector.detection;

/**
 * 检测器类型枚举
 * 定义了系统支持的各种威胁检测器类型
 *
 * <AUTHOR>
 */
public enum DetectorType {
    
    /**
     * C2框架检测器
     */
    C2_FRAMEWORK("C2框架检测", "检测各种命令与控制框架"),
    
    /**
     * 隧道检测器
     */
    TUNNEL("隧道检测", "检测各种网络隧道技术"),
    
    /**
     * WebShell检测器
     */
    WEBSHELL("WebShell检测", "检测Web后门和WebShell"),
    
    /**
     * 暴力破解检测器
     */
    BRUTE_FORCE("暴力破解检测", "检测暴力破解攻击"),
    
    /**
     * 端口扫描检测器
     */
    PORT_SCAN("端口扫描检测", "检测端口扫描行为"),
    
    /**
     * DNS隧道检测器
     */
    DNS_TUNNEL("DNS隧道检测", "检测DNS隧道技术"),
    
    /**
     * 异常流量检测器
     */
    ANOMALY_TRAFFIC("异常流量检测", "检测异常网络流量模式"),
    
    /**
     * 地理位置异常检测器
     */
    GEO_ANOMALY("地理位置异常检测", "检测异常的地理位置访问"),
    
    /**
     * 恶意域名检测器
     */
    MALICIOUS_DOMAIN("恶意域名检测", "检测恶意域名访问"),
    
    /**
     * 加密流量检测器
     */
    ENCRYPTED_TRAFFIC("加密流量检测", "检测可疑的加密流量"),

    /**
     * 漏洞扫描检测器
     */
    VULNERABILITY_SCAN("漏洞扫描检测", "检测各种漏洞扫描工具"),

    /**
     * 远程控制检测器
     */
    REMOTE_CONTROL("远程控制检测", "检测远程控制协议和工具"),

    /**
     * 远程控制协议检测器
     */
    REMOTE_CONTROL_PROTOCOL("远程控制协议检测", "检测远程控制协议"),

    /**
     * 挖矿检测器
     */
    MINING("挖矿检测", "检测挖矿行为"),

    /**
     * 指纹检测器
     */
    FINGERPRINT("指纹检测", "检测网络指纹");

    private final String displayName;
    private final String description;

    DetectorType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 获取检测器名称（兼容旧版API）
     * @return 检测器名称（枚举名称）
     */
    public String getDetectorName() {
        return this.name();
    }
}

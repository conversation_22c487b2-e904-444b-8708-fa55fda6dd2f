package com.geeksec.session.threat.detector.control;

import com.geeksec.session.threat.detector.config.ThreatDetectorConfig;
import com.geeksec.session.threat.detector.model.enums.DetectorTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 检测器开关配置加载器
 * 负责从配置文件或其他数据源加载检测器开关配置
 * 
 * <AUTHOR>
 */
@Slf4j
public class DetectorSwitchConfigLoader {
    
    /**
     * 从CSV文件加载检测器开关配置
     * 
     * @param configFilePath 配置文件路径
     * @return 检测器开关配置
     */
    public static DetectorSwitchConfig loadFromCsvFile(String configFilePath) {
        log.info("开始从CSV文件加载检测器开关配置: {}", configFilePath);
        
        try (InputStream inputStream = DetectorSwitchConfigLoader.class.getClassLoader()
                .getResourceAsStream(configFilePath);
             BufferedReader reader = new BufferedReader(
                     new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            if (inputStream == null) {
                log.warn("配置文件不存在: {}，使用默认配置", configFilePath);
                return new DetectorSwitchConfig();
            }
            
            Map<Integer, Integer> switchMap = parseCsvContent(reader);
            
            log.info("成功从CSV文件加载检测器开关配置，配置数量: {}", switchMap.size());
            return new DetectorSwitchConfig(switchMap);
            
        } catch (IOException e) {
            log.error("加载检测器开关配置文件失败: {}", e.getMessage(), e);
            log.warn("使用默认配置");
            return new DetectorSwitchConfig();
        }
    }
    
    /**
     * 从默认配置文件加载检测器开关配置
     * 
     * @return 检测器开关配置
     */
    public static DetectorSwitchConfig loadFromDefaultFile() {
        String defaultConfigFile = ThreatDetectorConfig.getDetectorSwitchConfigFile();
        return loadFromCsvFile(defaultConfigFile);
    }
    
    /**
     * 解析CSV文件内容
     * 
     * @param reader 文件读取器
     * @return 检测器开关映射
     * @throws IOException IO异常
     */
    private static Map<Integer, Integer> parseCsvContent(BufferedReader reader) throws IOException {
        Map<Integer, Integer> switchMap = new HashMap<>();
        String line;
        int lineNumber = 0;
        boolean isFirstLine = true;
        
        while ((line = reader.readLine()) != null) {
            lineNumber++;
            line = line.trim();
            
            // 跳过空行
            if (line.isEmpty()) {
                continue;
            }
            
            // 跳过标题行
            if (isFirstLine) {
                isFirstLine = false;
                if (line.startsWith("detector_id")) {
                    continue;
                }
            }
            
            try {
                String[] parts = line.split(",");
                if (parts.length < 3) {
                    log.warn("配置文件第{}行格式不正确，跳过: {}", lineNumber, line);
                    continue;
                }
                
                // 解析检测器ID
                Integer detectorId = Integer.parseInt(parts[0].trim());
                
                // 解析开关状态
                Integer switchState = Integer.parseInt(parts[2].trim());
                
                // 验证检测器ID
                if (!DetectorTypeEnum.existsDetectorId(detectorId)) {
                    log.warn("配置文件第{}行包含无效的检测器ID: {}，跳过", lineNumber, detectorId);
                    continue;
                }
                
                // 验证开关状态
                if (switchState != DetectorSwitchConfig.SWITCH_ENABLED && 
                    switchState != DetectorSwitchConfig.SWITCH_DISABLED) {
                    log.warn("配置文件第{}行包含无效的开关状态: {}，跳过", lineNumber, switchState);
                    continue;
                }
                
                switchMap.put(detectorId, switchState);
                
                if (log.isDebugEnabled()) {
                    DetectorTypeEnum detectorType = DetectorTypeEnum.getByDetectorId(detectorId);
                    String detectorName = detectorType != null ? detectorType.getDetectorName() : "未知检测器";
                    log.debug("加载检测器开关配置: {} (ID: {}) = {}", 
                            detectorName, detectorId, 
                            switchState == DetectorSwitchConfig.SWITCH_ENABLED ? "启用" : "禁用");
                }
                
            } catch (NumberFormatException e) {
                log.warn("配置文件第{}行数字格式错误，跳过: {}", lineNumber, line);
            } catch (Exception e) {
                log.warn("配置文件第{}行解析异常，跳过: {} - {}", lineNumber, line, e.getMessage());
            }
        }
        
        return switchMap;
    }
    
    /**
     * 从Map创建检测器开关配置
     * 
     * @param switchMap 开关映射
     * @return 检测器开关配置
     */
    public static DetectorSwitchConfig createFromMap(Map<Integer, Integer> switchMap) {
        if (switchMap == null || switchMap.isEmpty()) {
            log.warn("开关映射为空，使用默认配置");
            return new DetectorSwitchConfig();
        }
        
        log.info("从Map创建检测器开关配置，配置数量: {}", switchMap.size());
        return new DetectorSwitchConfig(switchMap);
    }
    
    /**
     * 创建默认的检测器开关配置（所有检测器启用）
     * 
     * @return 默认检测器开关配置
     */
    public static DetectorSwitchConfig createDefaultConfig() {
        log.info("创建默认检测器开关配置（所有检测器启用）");
        return new DetectorSwitchConfig();
    }
    
    /**
     * 创建测试用的检测器开关配置（部分检测器启用）
     * 
     * @return 测试检测器开关配置
     */
    public static DetectorSwitchConfig createTestConfig() {
        log.info("创建测试检测器开关配置");
        
        Map<Integer, Integer> testSwitchMap = new HashMap<>();
        
        // 启用部分检测器用于测试
        testSwitchMap.put(DetectorTypeEnum.SSL_FINGERPRINT.getDetectorId(), DetectorSwitchConfig.SWITCH_ENABLED);
        testSwitchMap.put(DetectorTypeEnum.WEBSHELL.getDetectorId(), DetectorSwitchConfig.SWITCH_ENABLED);
        testSwitchMap.put(DetectorTypeEnum.PORT_SCAN.getDetectorId(), DetectorSwitchConfig.SWITCH_ENABLED);
        testSwitchMap.put(DetectorTypeEnum.DNS_TUNNEL.getDetectorId(), DetectorSwitchConfig.SWITCH_ENABLED);
        
        // 禁用其他检测器
        for (DetectorTypeEnum detectorType : DetectorTypeEnum.values()) {
            testSwitchMap.putIfAbsent(detectorType.getDetectorId(), DetectorSwitchConfig.SWITCH_DISABLED);
        }
        
        return new DetectorSwitchConfig(testSwitchMap);
    }
    
    /**
     * 验证检测器开关配置
     * 
     * @param config 检测器开关配置
     * @return 是否有效
     */
    public static boolean validateConfig(DetectorSwitchConfig config) {
        if (config == null) {
            log.error("检测器开关配置为null");
            return false;
        }
        
        Map<Integer, Integer> switchMap = config.getDetectorSwitches();
        if (switchMap == null || switchMap.isEmpty()) {
            log.error("检测器开关映射为空");
            return false;
        }
        
        int validCount = 0;
        int invalidCount = 0;
        
        for (Map.Entry<Integer, Integer> entry : switchMap.entrySet()) {
            Integer detectorId = entry.getKey();
            Integer switchState = entry.getValue();
            
            // 验证检测器ID
            if (!DetectorTypeEnum.existsDetectorId(detectorId)) {
                log.warn("无效的检测器ID: {}", detectorId);
                invalidCount++;
                continue;
            }
            
            // 验证开关状态
            if (switchState != DetectorSwitchConfig.SWITCH_ENABLED && 
                switchState != DetectorSwitchConfig.SWITCH_DISABLED) {
                log.warn("无效的开关状态: {}，检测器ID: {}", switchState, detectorId);
                invalidCount++;
                continue;
            }
            
            validCount++;
        }
        
        log.info("检测器开关配置验证完成: 有效配置={}, 无效配置={}", validCount, invalidCount);
        
        return invalidCount == 0 && validCount > 0;
    }
    
    /**
     * 打印检测器开关配置摘要
     * 
     * @param config 检测器开关配置
     */
    public static void printConfigSummary(DetectorSwitchConfig config) {
        if (config == null) {
            log.info("检测器开关配置为null");
            return;
        }
        
        log.info("=== 检测器开关配置摘要 ===");
        config.printSwitchStatus();
        log.info("=== 配置摘要完成 ===");
    }
}

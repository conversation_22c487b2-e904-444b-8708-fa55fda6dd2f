package com.geeksec.session.threat.detector.model.output;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.geeksec.session.threat.detector.detection.DetectorType;

/**
 * 会话标签数据模型
 * 用于更新Doris会话表的标签信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 源IP地址
     */
    private String srcIp;

    /**
     * 目标IP地址
     */
    private String dstIp;

    /**
     * 源端口
     */
    private Integer srcPort;

    /**
     * 目标端口
     */
    private Integer dstPort;

    /**
     * 协议类型
     */
    private String protocol;

    /**
     * 标签键
     */
    private String labelKey;

    /**
     * 标签值
     */
    private String labelValue;

    /**
     * 检测置信度
     */
    private Double confidence;

    /**
     * 检测器类型
     */
    private DetectorType detectorType;

    /**
     * 标签时间戳
     */
    private LocalDateTime timestamp;

    /**
     * 标签类型
     */
    private String labelType;

    /**
     * 标签描述
     */
    private String description;

    /**
     * 是否为威胁标签
     */
    private Boolean isThreat;

    /**
     * 威胁级别
     */
    private String threatLevel;
}

package com.geeksec.session.threat.detector.detection.detector.remote;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import com.geeksec.session.threat.detector.detection.DetectorType;
import com.geeksec.session.threat.detector.detection.ThreatDetector;
import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import com.geeksec.session.threat.detector.model.input.SslInfo;
import com.geeksec.session.threat.detector.model.input.TcpInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 远程控制协议检测器
 * 检测标准远程控制协议下的C2行为和未知远程控制协议
 *
 * <AUTHOR>
 */
@Slf4j
public class RemoteControlProtocolDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 标准远程控制协议端口映射
    private static final Map<Integer, String> STANDARD_RC_PORTS = new HashMap<>();
    
    // 已知远程控制工具特征
    private static final Map<String, String> RC_TOOL_SIGNATURES = new HashMap<>();
    
    // 可疑的远程控制行为模式
    private static final Set<String> SUSPICIOUS_RC_PATTERNS = new HashSet<>();
    
    // 连接行为统计（用于检测C2心跳）
    private transient Map<String, RemoteControlStats> connectionStats = new ConcurrentHashMap<>();

    // 检测阈值
    private static final int HEARTBEAT_COUNT_THRESHOLD = 5; // 心跳次数阈值
    private static final long HEARTBEAT_INTERVAL_MIN = 30000; // 最小心跳间隔30秒
    private static final long HEARTBEAT_INTERVAL_MAX = 600000; // 最大心跳间隔10分钟
    private static final long TIME_WINDOW_MS = 1800000; // 30分钟时间窗口

    static {
        initializeStandardRcPorts();
        initializeRcToolSignatures();
        initializeSuspiciousPatterns();
    }

    private static void initializeStandardRcPorts() {
        // 标准远程控制协议端口
        STANDARD_RC_PORTS.put(3389, "RDP");
        STANDARD_RC_PORTS.put(5900, "VNC");
        STANDARD_RC_PORTS.put(5901, "VNC");
        STANDARD_RC_PORTS.put(22, "SSH");
        STANDARD_RC_PORTS.put(23, "Telnet");
        STANDARD_RC_PORTS.put(5985, "WinRM HTTP");
        STANDARD_RC_PORTS.put(5986, "WinRM HTTPS");
        STANDARD_RC_PORTS.put(135, "RPC");
        STANDARD_RC_PORTS.put(445, "SMB");
        
        log.info("初始化标准远程控制端口映射，共 {} 个端口", STANDARD_RC_PORTS.size());
    }

    private static void initializeRcToolSignatures() {
        // 已知远程控制工具特征
        RC_TOOL_SIGNATURES.put("teamviewer_signature", "TeamViewer");
        RC_TOOL_SIGNATURES.put("anydesk_signature", "AnyDesk");
        RC_TOOL_SIGNATURES.put("todesk_signature", "ToDesk");
        RC_TOOL_SIGNATURES.put("sunlogin_signature", "向日葵");
        RC_TOOL_SIGNATURES.put("rdp_signature", "Windows RDP");
        RC_TOOL_SIGNATURES.put("vnc_signature", "VNC");
        
        log.info("初始化远程控制工具特征库，共 {} 个特征", RC_TOOL_SIGNATURES.size());
    }

    private static void initializeSuspiciousPatterns() {
        // 可疑的远程控制行为模式
        SUSPICIOUS_RC_PATTERNS.add("periodic_small_packets"); // 周期性小数据包
        SUSPICIOUS_RC_PATTERNS.add("long_duration_connection"); // 长时间连接
        SUSPICIOUS_RC_PATTERNS.add("bidirectional_traffic"); // 双向流量
        SUSPICIOUS_RC_PATTERNS.add("encrypted_payload"); // 加密负载
        
        log.info("初始化可疑远程控制模式，共 {} 个模式", SUSPICIOUS_RC_PATTERNS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.REMOTE_CONTROL_PROTOCOL;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 初始化连接统计
            if (connectionStats == null) {
                connectionStats = new ConcurrentHashMap<>();
            }

            // 1. 检测标准远程控制协议下的C2行为
            DetectionResult standardRcResult = detectStandardRcC2Behavior(event);
            if (standardRcResult != null) {
                results.add(standardRcResult);
            }

            // 2. 检测未知远程控制协议
            DetectionResult unknownRcResult = detectUnknownRcProtocol(event);
            if (unknownRcResult != null) {
                results.add(unknownRcResult);
            }

            // 3. 检测远程控制工具特征
            DetectionResult rcToolResult = detectRcToolSignatures(event);
            if (rcToolResult != null) {
                results.add(rcToolResult);
            }

        } catch (Exception e) {
            log.error("远程控制协议检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测标准远程控制协议下的C2行为
     */
    private DetectionResult detectStandardRcC2Behavior(NetworkEvent event) {
        Integer dstPort = event.getDstPort();
        String srcIp = event.getSrcIp();
        String dstIp = event.getDstIp();
        
        if (dstPort == null || srcIp == null || dstIp == null || 
            !STANDARD_RC_PORTS.containsKey(dstPort)) {
            return null;
        }

        String connectionKey = srcIp + ":" + dstIp + ":" + dstPort;
        RemoteControlStats stats = connectionStats.computeIfAbsent(connectionKey, 
                k -> new RemoteControlStats());
        
        // 更新连接统计
        long currentTime = System.currentTimeMillis();
        stats.addConnection(currentTime, event.getUpBytes(), event.getDownBytes());
        
        // 清理过期数据
        stats.cleanExpiredData(currentTime, TIME_WINDOW_MS);
        
        // 检查是否符合C2心跳模式
        if (isC2HeartbeatPattern(stats)) {
            String protocol = STANDARD_RC_PORTS.get(dstPort);
            return createDetectionResult(event, "STANDARD_RC_C2", 
                    "标准远程控制协议下的C2行为",
                    DetectionResult.ThreatLevel.HIGH, 0.85,
                    String.format("检测到%s协议的C2心跳行为，连接次数: %d，平均间隔: %d秒", 
                            protocol, stats.getConnectionCount(), stats.getAverageInterval() / 1000));
        }

        return null;
    }

    /**
     * 检测未知远程控制协议
     */
    private DetectionResult detectUnknownRcProtocol(NetworkEvent event) {
        // 检查是否为非标准端口的长时间连接
        Integer dstPort = event.getDstPort();
        if (dstPort == null || STANDARD_RC_PORTS.containsKey(dstPort)) {
            return null;
        }

        // 检查TCP连接特征
        if (event.getEventType() == NetworkEvent.EventType.TCP && event.getTcpInfo() != null) {
            TcpInfo tcpInfo = event.getTcpInfo();
            
            // 检查连接持续时间
            Long duration = tcpInfo.getConnectionDuration();
            if (duration != null && duration > 1800000) { // 30分钟以上
                
                // 检查流量特征
                Long upBytes = event.getUpBytes();
                Long downBytes = event.getDownBytes();
                if (upBytes != null && downBytes != null) {
                    long totalBytes = upBytes + downBytes;
                    
                    // 长时间连接但流量较小，可能是C2心跳
                    if (totalBytes < 102400) { // 小于100KB
                        return createDetectionResult(event, "UNKNOWN_RC_PROTOCOL", 
                                "未知远程控制协议",
                                DetectionResult.ThreatLevel.MEDIUM, 0.7,
                                String.format("检测到未知远程控制协议，端口: %d，持续时间: %d分钟，总流量: %d字节", 
                                        dstPort, duration / 60000, totalBytes));
                    }
                }
            }
        }

        return null;
    }

    /**
     * 检测远程控制工具特征
     */
    private DetectionResult detectRcToolSignatures(NetworkEvent event) {
        // SSL流量中的远程控制工具检测
        if (event.getEventType() == NetworkEvent.EventType.SSL && event.getSslInfo() != null) {
            SslInfo sslInfo = event.getSslInfo();
            
            // 检查服务器名称
            String serverName = sslInfo.getServerName();
            if (serverName != null) {
                String lowerServerName = serverName.toLowerCase();
                
                // 检测ToDesk
                if (lowerServerName.contains("todesk") || lowerServerName.contains("todesk.com")) {
                    return createDetectionResult(event, "TODESK_RC_TOOL", 
                            "ToDesk远程控制工具",
                            DetectionResult.ThreatLevel.MEDIUM, 0.8,
                            "检测到ToDesk远程控制工具连接: " + serverName);
                }
                
                // 检测TeamViewer
                if (lowerServerName.contains("teamviewer") || lowerServerName.contains("teamviewer.com")) {
                    return createDetectionResult(event, "TEAMVIEWER_RC_TOOL", 
                            "TeamViewer远程控制工具",
                            DetectionResult.ThreatLevel.LOW, 0.6,
                            "检测到TeamViewer远程控制工具连接: " + serverName);
                }
                
                // 检测AnyDesk
                if (lowerServerName.contains("anydesk") || lowerServerName.contains("anydesk.com")) {
                    return createDetectionResult(event, "ANYDESK_RC_TOOL", 
                            "AnyDesk远程控制工具",
                            DetectionResult.ThreatLevel.LOW, 0.6,
                            "检测到AnyDesk远程控制工具连接: " + serverName);
                }
            }
        }

        return null;
    }

    /**
     * 检查是否符合C2心跳模式
     */
    private boolean isC2HeartbeatPattern(RemoteControlStats stats) {
        if (stats.getConnectionCount() < HEARTBEAT_COUNT_THRESHOLD) {
            return false;
        }
        
        long avgInterval = stats.getAverageInterval();
        return avgInterval >= HEARTBEAT_INTERVAL_MIN && avgInterval <= HEARTBEAT_INTERVAL_MAX;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String ruleId, String threatType,
                                                  DetectionResult.ThreatLevel level, double confidence, String description) {
        return DetectionResult.builder()
                .detectorName(getDetectorType().getDetectorName())
                .ruleId(ruleId)
                .threatType(threatType)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .timestamp(event.getTimestamp())
                .build();
    }

    /**
     * 远程控制连接统计类
     */
    private static class RemoteControlStats {
        private final List<Long> connectionTimes = new ArrayList<>();
        private final List<Long> upBytesHistory = new ArrayList<>();
        private final List<Long> downBytesHistory = new ArrayList<>();

        public void addConnection(long timestamp, Long upBytes, Long downBytes) {
            connectionTimes.add(timestamp);
            if (upBytes != null) upBytesHistory.add(upBytes);
            if (downBytes != null) downBytesHistory.add(downBytes);
        }

        public void cleanExpiredData(long currentTime, long timeWindowMs) {
            connectionTimes.removeIf(time -> currentTime - time > timeWindowMs);
            // 保持历史数据与连接时间同步
            while (upBytesHistory.size() > connectionTimes.size()) {
                upBytesHistory.remove(0);
            }
            while (downBytesHistory.size() > connectionTimes.size()) {
                downBytesHistory.remove(0);
            }
        }

        public int getConnectionCount() {
            return connectionTimes.size();
        }

        public long getAverageInterval() {
            if (connectionTimes.size() < 2) {
                return 0;
            }
            
            long totalInterval = 0;
            for (int i = 1; i < connectionTimes.size(); i++) {
                totalInterval += connectionTimes.get(i) - connectionTimes.get(i - 1);
            }
            
            return totalInterval / (connectionTimes.size() - 1);
        }
    }
}

package com.geeksec.session.threat.detector.detection.detector.attack;

import com.geeksec.session.threat.detector.detection.ThreatDetector;
import com.geeksec.session.threat.detector.detection.DetectorType;
import com.geeksec.session.threat.detector.model.detection.DetectionResult;
import com.geeksec.session.threat.detector.model.input.NetworkEvent;
import com.geeksec.session.threat.detector.model.input.HttpInfo;
import com.geeksec.session.threat.detector.model.input.SslInfo;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 加密通道攻击检测器
 * 检测通过加密通道进行的各种攻击行为
 *
 * <AUTHOR>
 */
@Slf4j
public class EncryptedChannelAttackDetector implements ThreatDetector {

    private static final long serialVersionUID = 1L;

    // 攻击工具特征库
    private static final Map<String, String> ATTACK_TOOL_SIGNATURES = new HashMap<>();
    
    // 爆破攻击端口映射
    private static final Map<Integer, String> BRUTE_FORCE_PORTS = new HashMap<>();
    
    // 连接统计（用于检测爆破行为）
    private transient Map<String, ConnectionStats> connectionStats = new ConcurrentHashMap<>();

    // 检测阈值
    private static final int BRUTE_FORCE_THRESHOLD = 10; // 爆破尝试次数阈值
    private static final long TIME_WINDOW_MS = 300000; // 5分钟时间窗口

    static {
        initializeAttackToolSignatures();
        initializeBruteForcePortMapping();
    }

    private static void initializeAttackToolSignatures() {
        // AWVS扫描工具特征
        ATTACK_TOOL_SIGNATURES.put("awvs_ua_pattern", "AWVS扫描工具");
        ATTACK_TOOL_SIGNATURES.put("awvs_ja3_hash", "AWVS扫描工具");
        
        // AppScan工具特征
        ATTACK_TOOL_SIGNATURES.put("appscan_ua_pattern", "AppScan扫描工具");
        ATTACK_TOOL_SIGNATURES.put("appscan_ja3_hash", "AppScan扫描工具");
        
        // xRay扫描工具特征
        ATTACK_TOOL_SIGNATURES.put("xray_ua_pattern", "xRay扫描工具");
        ATTACK_TOOL_SIGNATURES.put("xray_ja3_hash", "xRay扫描工具");
        
        log.info("初始化攻击工具特征库，共 {} 个特征", ATTACK_TOOL_SIGNATURES.size());
    }

    private static void initializeBruteForcePortMapping() {
        // 常见爆破攻击端口
        BRUTE_FORCE_PORTS.put(3389, "RDP爆破行为");
        BRUTE_FORCE_PORTS.put(1521, "Oracle爆破行为");
        BRUTE_FORCE_PORTS.put(3306, "MySQL爆破行为");
        BRUTE_FORCE_PORTS.put(445, "SMB爆破行为");
        BRUTE_FORCE_PORTS.put(139, "SMB爆破行为");
        BRUTE_FORCE_PORTS.put(22, "SSH爆破行为");
        BRUTE_FORCE_PORTS.put(21, "FTP爆破行为");
        BRUTE_FORCE_PORTS.put(23, "Telnet爆破行为");
        
        log.info("初始化爆破攻击端口映射，共 {} 个端口", BRUTE_FORCE_PORTS.size());
    }

    @Override
    public DetectorType getDetectorType() {
        return DetectorType.ENCRYPTED_TRAFFIC;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public List<DetectionResult> detect(NetworkEvent event) {
        List<DetectionResult> results = new ArrayList<>();

        try {
            // 初始化连接统计
            if (connectionStats == null) {
                connectionStats = new ConcurrentHashMap<>();
            }

            // 1. 检测扫描工具特征
            DetectionResult scanToolResult = detectScanningTools(event);
            if (scanToolResult != null) {
                results.add(scanToolResult);
            }

            // 2. 检测爆破攻击
            DetectionResult bruteForceResult = detectBruteForceAttack(event);
            if (bruteForceResult != null) {
                results.add(bruteForceResult);
            }

        } catch (Exception e) {
            log.error("加密通道攻击检测异常: {}", e.getMessage(), e);
        }

        return results;
    }

    /**
     * 检测扫描工具
     */
    private DetectionResult detectScanningTools(NetworkEvent event) {
        // HTTP扫描工具检测
        if (event.getEventType() == NetworkEvent.EventType.HTTP && event.getHttpInfo() != null) {
            return detectHttpScanningTools(event);
        }
        
        // SSL扫描工具检测
        if (event.getEventType() == NetworkEvent.EventType.SSL && event.getSslInfo() != null) {
            return detectSslScanningTools(event);
        }
        
        return null;
    }

    /**
     * 检测HTTP扫描工具
     */
    private DetectionResult detectHttpScanningTools(NetworkEvent event) {
        HttpInfo httpInfo = event.getHttpInfo();
        String userAgent = httpInfo.getUserAgent();
        
        if (userAgent == null) {
            return null;
        }

        String lowerUserAgent = userAgent.toLowerCase();

        // 检测AWVS
        if (lowerUserAgent.contains("awvs") || lowerUserAgent.contains("acunetix")) {
            return createDetectionResult(event, "AWVS_SCAN", 
                    "AWVS扫描工具",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    "检测到AWVS扫描工具，User-Agent: " + userAgent);
        }

        // 检测AppScan
        if (lowerUserAgent.contains("appscan") || lowerUserAgent.contains("ibm security")) {
            return createDetectionResult(event, "APPSCAN_SCAN", 
                    "AppScan扫描工具",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    "检测到AppScan扫描工具，User-Agent: " + userAgent);
        }

        // 检测xRay
        if (lowerUserAgent.contains("xray") || lowerUserAgent.contains("长亭科技")) {
            return createDetectionResult(event, "XRAY_SCAN", 
                    "xRay扫描工具",
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    "检测到xRay扫描工具，User-Agent: " + userAgent);
        }

        return null;
    }

    /**
     * 检测SSL扫描工具
     */
    private DetectionResult detectSslScanningTools(NetworkEvent event) {
        SslInfo sslInfo = event.getSslInfo();
        String ja3Hash = sslInfo.getJa3Hash();
        
        if (ja3Hash == null) {
            return null;
        }

        // 检查是否匹配已知扫描工具的JA3指纹
        for (Map.Entry<String, String> entry : ATTACK_TOOL_SIGNATURES.entrySet()) {
            if (entry.getKey().endsWith("_ja3_hash") && ja3Hash.equals(entry.getKey())) {
                return createDetectionResult(event, "SCAN_TOOL_JA3", 
                        entry.getValue(),
                        DetectionResult.ThreatLevel.HIGH, 0.9,
                        String.format("检测到%s，JA3: %s", entry.getValue(), ja3Hash));
            }
        }

        return null;
    }

    /**
     * 检测爆破攻击
     */
    private DetectionResult detectBruteForceAttack(NetworkEvent event) {
        Integer dstPort = event.getDstPort();
        String srcIp = event.getSrcIp();
        
        if (dstPort == null || srcIp == null || !BRUTE_FORCE_PORTS.containsKey(dstPort)) {
            return null;
        }

        // 获取或创建连接统计
        String key = srcIp + ":" + dstPort;
        ConnectionStats stats = connectionStats.computeIfAbsent(key, k -> new ConnectionStats());
        
        // 更新统计信息
        long currentTime = System.currentTimeMillis();
        stats.addConnection(currentTime);
        
        // 清理过期数据
        stats.cleanExpiredConnections(currentTime, TIME_WINDOW_MS);
        
        // 检查是否达到爆破阈值
        if (stats.getConnectionCount() >= BRUTE_FORCE_THRESHOLD) {
            String attackType = BRUTE_FORCE_PORTS.get(dstPort);
            return createDetectionResult(event, "BRUTE_FORCE_ATTACK", 
                    attackType,
                    DetectionResult.ThreatLevel.HIGH, 0.9,
                    String.format("检测到%s，连接次数: %d，端口: %d", 
                            attackType, stats.getConnectionCount(), dstPort));
        }

        return null;
    }

    /**
     * 创建检测结果
     */
    private DetectionResult createDetectionResult(NetworkEvent event, String ruleId, String threatType,
                                                  DetectionResult.ThreatLevel level, double confidence, String description) {
        return DetectionResult.builder()
                .detectorName(getDetectorType().getDetectorName())
                .ruleId(ruleId)
                .threatType(threatType)
                .threatLevel(level)
                .confidence(confidence)
                .description(description)
                .srcIp(event.getSrcIp())
                .dstIp(event.getDstIp())
                .srcPort(event.getSrcPort())
                .dstPort(event.getDstPort())
                .protocol(event.getProtocol())
                .timestamp(event.getTimestamp())
                .build();
    }

    /**
     * 连接统计类
     */
    private static class ConnectionStats {
        private final List<Long> connectionTimes = new ArrayList<>();

        public void addConnection(long timestamp) {
            connectionTimes.add(timestamp);
        }

        public void cleanExpiredConnections(long currentTime, long timeWindowMs) {
            connectionTimes.removeIf(time -> currentTime - time > timeWindowMs);
        }

        public int getConnectionCount() {
            return connectionTimes.size();
        }
    }
}

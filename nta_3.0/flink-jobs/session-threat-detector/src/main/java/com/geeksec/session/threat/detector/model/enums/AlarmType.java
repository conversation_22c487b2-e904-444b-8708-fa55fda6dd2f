package com.geeksec.session.threat.detector.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 告警类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AlarmType {
    // 会话相关
    SESSION_LABEL("会话打标"),
    
    // 指纹相关
    RAT("远控木马"),
    SCAN("扫描行为"),
    ILLEGAL_CONNECTION("违规外联"),
    MINING("挖矿病毒"),
    ENCRYPTED_TUNNEL("加密隐蔽隧道通信"),
    HACKING_TOOL("黑客工具"),
    ENCRYPTED_ATTACK("加密通道攻击行为"),
    
    // DNS相关
    DNS_TUNNEL("DNS隧道"),
    
    // Web Shell相关
    WEBSHELL("WebShell通信"),
    
    // 远程控制工具相关
    UNKNOWN_REMOTE_CONTROL_TOOL("未知远程控制工具"),
    
    // 加密工具相关
    PROTOCOL_ATTACK_TOOL("特定协议攻击工具"),
    
    // ToDesk相关
    TODESK("ToDesk");
    
    private static final Map<String, AlarmType> DISPLAY_NAME_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(AlarmType::getDisplayName, Function.identity()));
    
    private final String displayName;
    
    AlarmType(String displayName) {
        this.displayName = displayName;
    }
    
    /**
     * 根据显示名称获取对应的枚举值
     *
     * @param displayName 显示名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static AlarmType fromDisplayName(String displayName) {
        return displayName == null ? null : DISPLAY_NAME_MAP.get(displayName);
    }
    
    /**
     * 检查给定的显示名称是否匹配当前枚举
     *
     * @param displayName 要检查的显示名称
     * @return 如果匹配返回true，否则返回false
     */
    public boolean matches(String displayName) {
        return this.displayName.equals(displayName);
    }
}

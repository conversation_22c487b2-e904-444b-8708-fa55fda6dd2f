package com.geeksec.session.threat.detector.model.enums;

import lombok.Getter;

@Getter
public enum PatternType {
    SSL_FINGER_INFO(1),
    SIP_DIP_FINGER_ROW(2),
    CLIENT_HTTP_CONNECT_DOMAIN_EDGE(3),
    DNS_PARSE_TO_EDGE(4),
    CLIENT_QUERY_DOMAIN_EDGE(5),
    SERVER_HTTP_CONNECT_DOMAIN_EDGE(6),
    CLIENT_SSL_CONNECT_DOMAIN_EDGE(7),
    SERVER_SSL_CONNECT_DOMAIN_EDGE(8),
    HTTP_WEB_LOGIN(9),
    WEB_LOGIN_INFO(10),
    PORT_SCAN_ROW(11),
    DNS_TUNNEL_INFO(12),
    CONNECT_INFO_DNS(13),
    NEOREGEO_INFO(14),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(15),
    <PERSON><PERSON><PERSON><PERSON><PERSON>OW(16),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>OW(17),
    <PERSON><PERSON><PERSON><PERSON>OW(18),
    <PERSON><PERSON><PERSON>_<PERSON>INGER_ROW(19),
    <PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>O(20),
    <PERSON><PERSON><PERSON>INDER_INFO(21),
    ANTSWORD_INFO(22),
    ANTSWORD_PHP_INFO(23),
    HTTP_TUNNEL_INFO(24),
    TCP_TUNNEL_INFO(25),
    NTP_TUNNEL_INFO(26),
    SSL_TUNNEL_INFO(27),
    <PERSON>CMP_TUNNEL_INFO(28),
    KNOWN_REMOTE_CONTROL_TOOL_INFO_ROW(29),
    UNKNOWN_REMOTE_CONTROL_TOOL_INFO_ROW(30),
    WEB_SHELL_INFO(31),
    ENCRYPTED_TOOL_INFO(32),
    TODESK_ROW(33);

    private final int id;
    PatternType(int id) { this.id = id; }

    public static PatternType fromString(String type) {
        for (PatternType pt : PatternType.values()) {
            if (pt.name().equalsIgnoreCase(type)) {
                return pt;
            }
        }
        throw new IllegalArgumentException("Unknown PatternType: " + type);
    }
}
package com.geeksec.session.threat.detector.exception;

/**
 * 检测器配置异常
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class DetectorConfigurationException extends ThreatDetectionException {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public DetectorConfigurationException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public DetectorConfigurationException(String message, Throwable cause) {
        super(message, cause);
    }
}

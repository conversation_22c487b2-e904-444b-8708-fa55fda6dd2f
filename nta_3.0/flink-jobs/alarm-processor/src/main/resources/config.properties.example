# Alarm Processor 配置示例
# 注意：此配置现在统一在 Helm Chart 的 ConfigMap 中管理
# 请参考 deployment/helm/values/values-flink.yaml 中的 alarm-processor 配置

# ==================== 配置说明 ====================
# 生产环境配置通过以下方式管理：
#    - 基础设施配置：deployment/helm/values/values-infrastructure.yaml
#    - Flink 作业配置：deployment/helm/values/values-flink.yaml
#    - ConfigMap 模板：deployment/helm/templates/configmaps/flink-config.yaml

# ==================== 本地开发配置 ====================
# 以下配置仅用于本地开发和测试

# 作业基础配置
alarm.processor.job.name=alarm-processor
alarm.processor.job.parallelism=4
alarm.processor.job.checkpointInterval=60000
alarm.processor.job.restartAttempts=3
alarm.processor.job.restartDelay=10000

# Kafka 配置（本地开发）
alarm.processor.kafka.bootstrapServers=localhost:9092
alarm.processor.kafka.groupId=alarm-processor-group
alarm.processor.kafka.input.topic=alarm-events
alarm.processor.kafka.output.topic=processed-alarms
alarm.processor.kafka.notification.topic=alarm-notifications
alarm.processor.kafka.input.startingOffsets=latest
alarm.processor.kafka.input.autoCommit=true
alarm.processor.kafka.input.commitInterval=5000

# ==================== 处理配置 ====================
# 去重配置
alarm.processor.processing.deduplication.enabled=true
alarm.processor.processing.deduplication.mode=TIME_WINDOW
alarm.processor.processing.deduplication.timeWindowMs=60000
alarm.processor.processing.deduplication.maxCacheSize=10000
alarm.processor.processing.deduplication.cacheExpirationMs=300000

# 格式化配置
alarm.processor.processing.formatting.enabled=true
alarm.processor.processing.formatting.includeReasonAnalysis=true
alarm.processor.processing.formatting.includeHandlingSuggestions=true

# 攻击链分析配置
alarm.processor.processing.attackChain.enabled=true
alarm.processor.processing.attackChain.correlationWindowMs=300000
alarm.processor.processing.attackChain.maxCacheSize=15000
alarm.processor.processing.attackChain.minEventsForChain=2

# 批量处理配置
alarm.processor.processing.batch.enabled=true
alarm.processor.processing.batch.maxBatchSize=50
alarm.processor.processing.batch.maxWaitTimeMs=30000
alarm.processor.processing.batch.checkIntervalMs=5000

# PostgreSQL 配置（本地开发）
alarm.processor.postgresql.host=localhost
alarm.processor.postgresql.port=5432
alarm.processor.postgresql.database=nta
alarm.processor.postgresql.username=nta_user
alarm.processor.postgresql.password=nta_password

# 通知输出配置（本地开发）
alarm.processor.output.notification.enabled=true
alarm.processor.output.notification.batchSize=50
alarm.processor.output.notification.lingerMs=1000



# ==================== 监控配置 ====================
alarm.processor.monitoring.enabled=true
alarm.processor.monitoring.metricsInterval=30000
alarm.processor.monitoring.performanceLogging=true
alarm.processor.monitoring.detailedMetrics=false

# ==================== 告警抑制规则配置 ====================
alarm.processor.suppression.alarmServiceBaseUrl=http://alarm-service:8080

package com.geeksec.alarm.processor.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 告警事件模型
 * 用于接收来自各个检测模块的原始告警事件
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlarmEvent implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 事件ID（由发送方生成）
     */
    @JsonProperty("event_id")
    private String eventId;
    
    /**
     * 事件来源模块
     */
    @JsonProperty("source_module")
    private String sourceModule;
    
    /**
     * 告警类型
     */
    @JsonProperty("alarm_type")
    private String alarmType;
    
    /**
     * 告警名称
     */
    @JsonProperty("alarm_name")
    private String alarmName;
    
    /**
     * 威胁类型
     */
    @JsonProperty("threat_type")
    private String threatType;
    
    /**
     * 告警级别
     */
    @JsonProperty("alarm_level")
    private AlarmLevel alarmLevel;
    
    /**
     * 源IP地址
     */
    @JsonProperty("src_ip")
    private String srcIp;
    
    /**
     * 目标IP地址
     */
    @JsonProperty("dst_ip")
    private String dstIp;
    
    /**
     * 源端口
     */
    @JsonProperty("src_port")
    private Integer srcPort;
    
    /**
     * 目标端口
     */
    @JsonProperty("dst_port")
    private Integer dstPort;
    
    /**
     * 协议类型
     */
    @JsonProperty("protocol")
    private String protocol;
    
    /**
     * 事件时间戳
     */
    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    /**
     * 检测器类型
     */
    @JsonProperty("detector_type")
    private String detectorType;
    
    /**
     * 置信度 (0.0 - 1.0)
     */
    @JsonProperty("confidence")
    private Double confidence;
    
    /**
     * 告警描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 检测原因列表
     */
    @JsonProperty("detection_reasons")
    private List<String> detectionReasons;
    
    /**
     * 相关标签
     */
    @JsonProperty("labels")
    private List<String> labels;
    
    /**
     * 扩展属性
     */
    @JsonProperty("extended_properties")
    private Map<String, Object> extendedProperties;
    
    /**
     * 证书相关信息（如果是证书告警）
     */
    @JsonProperty("certificate_info")
    private CertificateInfo certificateInfo;
    
    /**
     * 网络会话信息
     */
    @JsonProperty("session_info")
    private SessionInfo sessionInfo;
    
    /**
     * 告警级别枚举
     */
    public enum AlarmLevel {
        LOW(1, "低"),
        MEDIUM(2, "中"),
        HIGH(3, "高"),
        CRITICAL(4, "严重");
        
        private final int level;
        private final String description;
        
        AlarmLevel(int level, String description) {
            this.level = level;
            this.description = description;
        }
        
        public int getLevel() {
            return level;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 证书信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CertificateInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("cert_hash")
        private String certHash;
        
        @JsonProperty("subject_cn")
        private String subjectCn;
        
        @JsonProperty("issuer_cn")
        private String issuerCn;
        
        @JsonProperty("domains")
        private List<String> domains;
        
        @JsonProperty("not_before")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime notBefore;
        
        @JsonProperty("not_after")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime notAfter;
    }
    
    /**
     * 会话信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SessionInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("session_id")
        private String sessionId;
        
        @JsonProperty("flow_id")
        private String flowId;
        
        @JsonProperty("bytes_sent")
        private Long bytesSent;
        
        @JsonProperty("bytes_received")
        private Long bytesReceived;
        
        @JsonProperty("packets_sent")
        private Long packetsSent;
        
        @JsonProperty("packets_received")
        private Long packetsReceived;
        
        @JsonProperty("duration")
        private Long duration;
    }
    
    /**
     * 获取告警的唯一标识键
     * 用于去重和关联分析
     */
    public String getDeduplicationKey() {
        return String.format("%s:%s:%s:%s:%s", 
            alarmType, srcIp, dstIp, threatType, alarmName);
    }
    
    /**
     * 获取攻击链关联键
     * 用于攻击链分析
     */
    public String getAttackChainKey() {
        return String.format("%s:%s", srcIp, dstIp);
    }
    
    /**
     * 检查是否为高优先级告警
     */
    public boolean isHighPriority() {
        return alarmLevel == AlarmLevel.HIGH || alarmLevel == AlarmLevel.CRITICAL;
    }
    
    /**
     * 检查是否为证书相关告警
     */
    public boolean isCertificateAlarm() {
        return certificateInfo != null && 
               ("certificate-analyzer".equals(sourceModule) || 
                alarmType != null && alarmType.toLowerCase().contains("cert"));
    }
}

package com.geeksec.alarm.processor.output.sink;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Properties;

/**
 * PostgreSQL 告警输出 Sink
 * 将处理后的告警数据写入 PostgreSQL 的 alarm_records 表
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class PostgreSQLAlarmSink extends RichSinkFunction<Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    /** 配置对象 */
    private final AlarmProcessorConfig config;
    
    /** JSON 序列化器 */
    private final ObjectMapper objectMapper;
    
    /** 数据库连接 */
    private transient Connection connection;
    
    /** 预编译语句 */
    private transient PreparedStatement insertStatement;
    
    /** 指标计数器 */
    private transient Counter totalRecords;
    private transient Counter successfulRecords;
    private transient Counter failedRecords;
    
    /** 插入SQL语句 */
    private static final String INSERT_SQL = """
        INSERT INTO alarm_records (
            alarm_title, alarm_content, severity, status, source_data, triggered_at,
            alarm_type, attack_stage, threat_type, confidence,
            src_ip, src_port, dst_ip, dst_port, protocol,
            session_id, task_id, data_source,
            created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?::jsonb, ?, ?, ?, ?, ?, ?::inet, ?, ?::inet, ?, ?, ?, ?, ?, ?, ?)
        """;
    
    /**
     * 构造函数
     * 
     * @param config 配置对象
     */
    public PostgreSQLAlarmSink(AlarmProcessorConfig config) {
        this.config = config;
        this.objectMapper = new ObjectMapper();
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        initializeMetrics();
        
        // 创建数据库连接
        createConnection();
        
        // 准备SQL语句
        prepareStatements();
        
        log.info("PostgreSQL 告警输出 Sink 初始化完成，目标表: alarm_records");
    }
    
    /**
     * 初始化指标
     */
    private void initializeMetrics() {
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("postgresql-alarm-sink");
        
        totalRecords = metricGroup.counter("total_records");
        successfulRecords = metricGroup.counter("successful_records");
        failedRecords = metricGroup.counter("failed_records");
    }
    
    /**
     * 创建数据库连接
     */
    private void createConnection() throws SQLException {
        try {
            // 加载PostgreSQL驱动
            Class.forName("org.postgresql.Driver");
            
            // 构建连接URL
            String url = String.format("jdbc:postgresql://%s:%d/%s",
                    config.getPostgresqlHost(),
                    config.getPostgresqlPort(),
                    config.getPostgresqlDatabase());
            
            // 设置连接属性
            Properties props = new Properties();
            props.setProperty("user", config.getPostgresqlUsername());
            props.setProperty("password", config.getPostgresqlPassword());
            props.setProperty("ssl", "false");
            props.setProperty("autoReconnect", "true");
            props.setProperty("maxReconnects", "3");
            props.setProperty("initialTimeout", "2");
            
            // 创建连接
            connection = DriverManager.getConnection(url, props);
            connection.setAutoCommit(true);
            
            log.info("PostgreSQL 连接创建成功: {}", url);
            
        } catch (ClassNotFoundException e) {
            throw new SQLException("PostgreSQL 驱动未找到", e);
        } catch (SQLException e) {
            log.error("创建 PostgreSQL 连接失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 准备SQL语句
     */
    private void prepareStatements() throws SQLException {
        try {
            insertStatement = connection.prepareStatement(INSERT_SQL);
            log.info("PostgreSQL 预编译语句准备完成");
        } catch (SQLException e) {
            log.error("准备 PostgreSQL 语句失败: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    @Override
    public void invoke(Alarm alarm, Context context) throws Exception {
        totalRecords.inc();
        
        try {
            // 检查连接状态
            if (connection == null || connection.isClosed()) {
                log.warn("PostgreSQL 连接已关闭，尝试重新连接");
                createConnection();
                prepareStatements();
            }
            
            // 设置参数并执行插入
            setStatementParameters(alarm);
            insertStatement.executeUpdate();
            
            successfulRecords.inc();
            log.debug("成功写入告警到 PostgreSQL: {}", alarm.getAlarmId());
            
        } catch (Exception e) {
            failedRecords.inc();
            log.error("写入告警到 PostgreSQL 失败: {}, 错误: {}", alarm.getAlarmId(), e.getMessage(), e);
            
            // 尝试重新连接并重试一次
            try {
                createConnection();
                prepareStatements();
                setStatementParameters(alarm);
                insertStatement.executeUpdate();
                
                successfulRecords.inc();
                log.info("重试写入告警到 PostgreSQL 成功: {}", alarm.getAlarmId());
                
            } catch (Exception retryException) {
                log.error("重试写入告警到 PostgreSQL 仍然失败: {}, 错误: {}", 
                        alarm.getAlarmId(), retryException.getMessage(), retryException);
                throw retryException;
            }
        }
    }
    
    /**
     * 设置预编译语句参数
     */
    private void setStatementParameters(Alarm alarm) throws Exception {
        LocalDateTime now = LocalDateTime.now();
        
        // 1. alarm_title - 告警标题
        insertStatement.setString(1, alarm.getAlarmName() != null ? alarm.getAlarmName() : "未知告警");
        
        // 2. alarm_content - 告警内容
        insertStatement.setString(2, alarm.getDescription());
        
        // 3. severity - 严重程度
        String severity = mapAlarmLevelToSeverity(alarm.getAlarmLevel());
        insertStatement.setString(3, severity);
        
        // 4. status - 告警状态
        insertStatement.setString(4, "OPEN");
        
        // 5. source_data - 源数据（JSON格式）
        String sourceDataJson = createSourceDataJson(alarm);
        insertStatement.setString(5, sourceDataJson);
        
        // 6. triggered_at - 触发时间
        Timestamp triggeredAt = alarm.getEventTimestamp() != null ? 
                Timestamp.valueOf(alarm.getEventTimestamp()) : Timestamp.valueOf(now);
        insertStatement.setTimestamp(6, triggeredAt);
        
        // 7. alarm_type - 告警类型
        insertStatement.setString(7, alarm.getAlarmType());
        
        // 8. attack_stage - 攻击阶段
        String attackStage = alarm.getAttackChainInfo() != null ? 
                alarm.getAttackChainInfo().getChainStage() : null;
        insertStatement.setString(8, attackStage);
        
        // 9. threat_type - 威胁类型
        insertStatement.setString(9, alarm.getThreatType());
        
        // 10. confidence - 置信度
        if (alarm.getConfidence() != null) {
            insertStatement.setDouble(10, alarm.getConfidence());
        } else {
            insertStatement.setNull(10, java.sql.Types.DOUBLE);
        }
        
        // 11. src_ip - 源IP
        insertStatement.setString(11, alarm.getSrcIp());
        
        // 12. src_port - 源端口
        if (alarm.getSrcPort() != null) {
            insertStatement.setInt(12, alarm.getSrcPort());
        } else {
            insertStatement.setNull(12, java.sql.Types.INTEGER);
        }
        
        // 13. dst_ip - 目标IP
        insertStatement.setString(13, alarm.getDstIp());
        
        // 14. dst_port - 目标端口
        if (alarm.getDstPort() != null) {
            insertStatement.setInt(14, alarm.getDstPort());
        } else {
            insertStatement.setNull(14, java.sql.Types.INTEGER);
        }
        
        // 15. protocol - 协议
        insertStatement.setString(15, alarm.getProtocol());
        
        // 16. session_id - 会话ID
        String sessionId = alarm.getSessionInfo() != null ? 
                alarm.getSessionInfo().getSessionId() : null;
        insertStatement.setString(16, sessionId);
        
        // 17. task_id - 任务ID（使用原始事件ID）
        insertStatement.setString(17, alarm.getOriginalEventId());
        
        // 18. data_source - 数据源（使用来源模块）
        insertStatement.setString(18, alarm.getSourceModule());
        
        // 19. created_at - 创建时间
        insertStatement.setTimestamp(19, Timestamp.valueOf(now));
        
        // 20. updated_at - 更新时间
        insertStatement.setTimestamp(20, Timestamp.valueOf(now));
    }
    
    /**
     * 映射告警级别到严重程度
     */
    private String mapAlarmLevelToSeverity(AlarmEvent.AlarmLevel alarmLevel) {
        if (alarmLevel == null) {
            return "MEDIUM";
        }

        return switch (alarmLevel) {
            case LOW -> "LOW";
            case MEDIUM -> "MEDIUM";
            case HIGH -> "HIGH";
            case CRITICAL -> "CRITICAL";
        };
    }
    
    /**
     * 创建源数据JSON
     */
    private String createSourceDataJson(Alarm alarm) throws Exception {
        // 创建包含完整告警信息的JSON对象
        return objectMapper.writeValueAsString(alarm);
    }
    
    @Override
    public void close() throws Exception {
        try {
            if (insertStatement != null && !insertStatement.isClosed()) {
                insertStatement.close();
            }
            
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
            
            log.info("PostgreSQL 告警输出 Sink 关闭，写入统计: 总计={}, 成功={}, 失败={}",
                    totalRecords.getCount(),
                    successfulRecords.getCount(),
                    failedRecords.getCount());
                    
        } catch (SQLException e) {
            log.error("关闭 PostgreSQL 连接时发生错误: {}", e.getMessage(), e);
        } finally {
            super.close();
        }
    }
}

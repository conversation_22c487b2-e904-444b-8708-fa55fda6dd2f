package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 标准远程控制C2告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class StandardRemoteControlC2Processor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.STANDARD_REMOTE_CONTROL_C2;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String c2Protocol = getC2Protocol(alarm);
        String c2Server = alarm.getDstIp();
        
        // 标准远程控制C2检测
        reasons.add(createDetectionReason(
                "标准远程控制C2行为",
                String.format("检测到使用%s协议的C2通信行为", c2Protocol),
                "正常远程管理",
                String.format("C2服务器: %s", c2Server),
                "合法远程管理服务器",
                8
        ));
        
        // C2通信模式分析
        if (hasC2Pattern(alarm)) {
            reasons.add(createDetectionReason(
                    "C2通信模式",
                    "检测到符合C2特征的通信模式",
                    "正常管理通信",
                    getC2PatternDescription(alarm),
                    "规律的管理会话",
                    7
            ));
        }
        
        // 命令执行检测
        if (hasCommandExecution(alarm)) {
            reasons.add(createDetectionReason(
                    "远程命令执行",
                    "检测到通过C2通道执行远程命令",
                    "正常管理操作",
                    "远程命令执行特征",
                    "授权的管理操作",
                    9
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String c2Protocol = getC2Protocol(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        String.format("立即阻断%s协议的C2通信", c2Protocol),
                        "隔离受控主机，防止横向移动",
                        String.format("分析C2服务器%s的威胁情报", alarm.getDstIp()),
                        "检查主机是否被完全控制"
                ))
                .investigationSteps(Arrays.asList(
                        "分析C2通信的具体内容和命令",
                        "确认攻击者的控制范围和权限",
                        "检查是否有数据窃取或系统破坏",
                        "分析C2建立的时间和触发条件",
                        "确认网络中其他受控主机"
                ))
                .preventionMeasures(Arrays.asList(
                        String.format("限制%s协议的使用和监控", c2Protocol),
                        "加强对远程管理协议的审计",
                        "实施网络分段，限制C2传播",
                        "部署C2检测和阻断工具",
                        "定期进行C2威胁狩猎"
                ))
                .recoverySteps(Arrays.asList(
                        "断开受感染设备的网络连接，防止恶意行为扩散",
                        "使用可靠的安全软件进行全面扫描和清除",
                        "及时更新操作系统和应用程序到最新版本，修补安全漏洞",
                        "加强账户密码，避免使用弱密码",
                        "备份重要数据，以防万一",
                        "提高对可疑邮件和链接的警觉性，避免点击来源不明的链接或下载不明文件"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String c2Protocol = getC2Protocol(alarm);
        return String.format("攻击者利用%s等标准远程控制协议建立C2通信通道，" +
                "通过合法协议隐藏恶意活动，实现对受害主机的远程控制。", c2Protocol);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过分析远程控制协议的使用模式、连接频率、数据传输特征等，" +
                "识别被滥用于恶意C2通信的标准远程管理协议。";
    }
    
    @Override
    public List<Map<String, String>> getVictimInfo(Alarm alarm) {
        List<Map<String, String>> victims = new ArrayList<>();
        
        // 被控制的主机是受害者
        if (alarm.getSrcIp() != null) {
            Map<String, String> victim = new HashMap<>();
            victim.put("ip", alarm.getSrcIp());
            victim.put("role", "被控制主机");
            victim.put("control_level", "完全控制");
            victims.add(victim);
        }
        
        return victims;
    }
    
    @Override
    public List<Map<String, String>> getAttackerInfo(Alarm alarm) {
        List<Map<String, String>> attackers = new ArrayList<>();
        
        // C2服务器是攻击者
        if (alarm.getDstIp() != null) {
            Map<String, String> attacker = new HashMap<>();
            attacker.put("ip", alarm.getDstIp());
            attacker.put("role", "C2控制服务器");
            attacker.put("protocol", getC2Protocol(alarm));
            attackers.add(attacker);
        }
        
        return attackers;
    }
    
    @Override
    public List<Map<String, Object>> generateAttackRoute(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Map<String, Object>> attackRoute = new ArrayList<>();
        
        // 初始感染
        Map<String, Object> step1 = new HashMap<>();
        step1.put("step", 1);
        step1.put("action", "初始感染");
        step1.put("description", "主机被恶意软件感染");
        attackRoute.add(step1);
        
        // C2连接建立
        Map<String, Object> step2 = new HashMap<>();
        step2.put("step", 2);
        step2.put("action", "C2连接建立");
        step2.put("description", String.format("通过%s协议连接C2服务器", getC2Protocol(alarm)));
        step2.put("c2_server", alarm.getDstIp());
        attackRoute.add(step2);
        
        // 远程控制
        Map<String, Object> step3 = new HashMap<>();
        step3.put("step", 3);
        step3.put("action", "远程控制");
        step3.put("description", "攻击者通过C2通道控制主机");
        attackRoute.add(step3);
        
        return attackRoute;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99165";
    }
    
    @Override
    public boolean requiresSpecialHandling(Alarm alarm) {
        return true; // C2行为需要特殊处理
    }
    
    /**
     * 获取C2协议
     */
    private String getC2Protocol(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object protocol = alarm.getExtendedProperties().get("c2_protocol");
            if (protocol != null) {
                return protocol.toString();
            }
        }
        return alarm.getProtocol() != null ? alarm.getProtocol() : "未知协议";
    }
    
    /**
     * 检查是否有C2模式
     */
    private boolean hasC2Pattern(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object pattern = alarm.getExtendedProperties().get("has_c2_pattern");
            if (pattern instanceof Boolean) {
                return (Boolean) pattern;
            }
        }
        return false;
    }
    
    /**
     * 获取C2模式描述
     */
    private String getC2PatternDescription(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object description = alarm.getExtendedProperties().get("c2_pattern_description");
            if (description != null) {
                return description.toString();
            }
        }
        return "定期心跳通信";
    }
    
    /**
     * 检查是否有命令执行
     */
    private boolean hasCommandExecution(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object execution = alarm.getExtendedProperties().get("has_command_execution");
            if (execution instanceof Boolean) {
                return (Boolean) execution;
            }
        }
        return false;
    }
}

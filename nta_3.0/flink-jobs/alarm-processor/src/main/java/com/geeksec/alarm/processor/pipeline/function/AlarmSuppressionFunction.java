package com.geeksec.alarm.processor.pipeline.function;

import org.apache.flink.api.common.functions.RichFilterFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.suppression.SuppressionRuleManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警抑制函数
 * 用于过滤掉满足抑制规则的告警
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmSuppressionFunction extends RichFilterFunction<Alarm> {

    private final SuppressionRuleManager suppressionManager;

    /** 监控指标 - 总告警数 */
    private transient Counter totalAlarms;

    /** 监控指标 - 抑制命中数 */
    private transient Counter suppressionHits;

    public AlarmSuppressionFunction(AlarmProcessorConfig config) {
        // 从配置创建抑制规则管理器
        this.suppressionManager = new SuppressionRuleManager(config);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化监控指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-suppression");
        this.totalAlarms = metricGroup.counter("total_alarms");
        this.suppressionHits = metricGroup.counter("suppression_hits");
        
        log.info("告警抑制函数初始化完成");
    }

    @Override
    public boolean filter(Alarm alarm) throws Exception {
        totalAlarms.inc();

        // 如果抑制规则管理器未初始化，直接通过
        if (suppressionManager == null) {
            log.warn("抑制规则管理器未初始化，告警直接通过: alarmId={}", alarm.getAlarmId());
            return true;
        }

        try {
            // 检查告警是否满足抑制规则
            // 提取告警信息进行抑制检查
            String victim = alarm.getDstIp();
            String attacker = alarm.getSrcIp();

            boolean shouldSuppress = false;

            // 检查告警的所有标签是否满足抑制规则
            if (alarm.getLabels() != null && !alarm.getLabels().isEmpty()) {
                for (String label : alarm.getLabels()) {
                    if (suppressionManager.shouldSuppress(victim, attacker, label)) {
                        shouldSuppress = true;
                        break;
                    }
                }
            }

            // 如果没有标签或标签都不满足抑制规则，使用告警类型作为标签
            if (!shouldSuppress) {
                shouldSuppress = suppressionManager.shouldSuppress(victim, attacker, alarm.getAlarmType());
            }

            if (shouldSuppress) {
                suppressionHits.inc();
                log.debug("告警满足抑制规则，已过滤: victim={}, attacker={}, alarmType={}",
                        victim, attacker, alarm.getAlarmType());
                // 过滤掉
                return false;
            } else {
                log.debug("告警不满足抑制规则，继续处理: victim={}, attacker={}, alarmType={}",
                        victim, attacker, alarm.getAlarmType());
                // 继续处理
                return true;
            }

        } catch (Exception e) {
            log.error("抑制规则检查失败，告警直接通过: victim={}, attacker={}, alarmType={}",
                    alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType(), e);
            // 出错时直接通过，避免丢失告警
            return true;
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (suppressionManager != null) {
            suppressionManager.close();
            log.info("抑制规则过滤函数已关闭");
        }
    }
}
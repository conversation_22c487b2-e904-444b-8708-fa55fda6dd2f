package com.geeksec.alarm.processor.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 处理后的告警模型
 * 经过去重、格式化、攻击链分析后的最终告警对象
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Alarm implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 告警ID（处理器生成的唯一ID）
     */
    @JsonProperty("alarm_id")
    private String alarmId;
    
    /**
     * 原始事件ID
     */
    @JsonProperty("original_event_id")
    private String originalEventId;
    
    /**
     * 来源模块
     */
    @JsonProperty("source_module")
    private String sourceModule;
    
    /**
     * 告警类型
     */
    @JsonProperty("alarm_type")
    private String alarmType;
    
    /**
     * 告警名称
     */
    @JsonProperty("alarm_name")
    private String alarmName;
    
    /**
     * 威胁类型
     */
    @JsonProperty("threat_type")
    private String threatType;
    
    /**
     * 告警级别
     */
    @JsonProperty("alarm_level")
    private AlarmEvent.AlarmLevel alarmLevel;
    
    /**
     * 源IP地址
     */
    @JsonProperty("src_ip")
    private String srcIp;
    
    /**
     * 目标IP地址
     */
    @JsonProperty("dst_ip")
    private String dstIp;
    
    /**
     * 源端口
     */
    @JsonProperty("src_port")
    private Integer srcPort;
    
    /**
     * 目标端口
     */
    @JsonProperty("dst_port")
    private Integer dstPort;
    
    /**
     * 协议类型
     */
    @JsonProperty("protocol")
    private String protocol;
    
    /**
     * 原始事件时间戳
     */
    @JsonProperty("event_timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime eventTimestamp;
    
    /**
     * 告警处理时间戳
     */
    @JsonProperty("processed_timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processedTimestamp;
    
    /**
     * 检测器类型
     */
    @JsonProperty("detector_type")
    private String detectorType;
    
    /**
     * 置信度
     */
    @JsonProperty("confidence")
    private Double confidence;
    
    /**
     * 告警描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 检测原因分析
     */
    @JsonProperty("reason_analysis")
    private ReasonAnalysis reasonAnalysis;
    
    /**
     * 处理建议
     */
    @JsonProperty("handling_suggestions")
    private HandlingSuggestions handlingSuggestions;
    
    /**
     * 攻击链信息
     */
    @JsonProperty("attack_chain")
    private AttackChainInfo attackChainInfo;
    
    /**
     * 相关标签
     */
    @JsonProperty("labels")
    private List<String> labels;
    
    /**
     * 扩展属性
     */
    @JsonProperty("extended_properties")
    private Map<String, Object> extendedProperties;
    
    /**
     * 证书相关信息
     */
    @JsonProperty("certificate_info")
    private AlarmEvent.CertificateInfo certificateInfo;
    
    /**
     * 网络会话信息
     */
    @JsonProperty("session_info")
    private AlarmEvent.SessionInfo sessionInfo;
    
    /**
     * 处理状态
     */
    @JsonProperty("processing_status")
    private ProcessingStatus processingStatus;
    
    /**
     * 去重信息
     */
    @JsonProperty("deduplication_info")
    private DeduplicationInfo deduplicationInfo;
    
    /**
     * 检测原因分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReasonAnalysis implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("primary_reason")
        private String primaryReason;
        
        @JsonProperty("detailed_reasons")
        private List<DetectionReason> detailedReasons;
        
        @JsonProperty("risk_score")
        private Integer riskScore;
        
        @JsonProperty("evidence")
        private List<String> evidence;
    }
    
    /**
     * 检测原因详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DetectionReason implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("reason_type")
        private String reasonType;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("detected_feature")
        private String detectedFeature;
        
        @JsonProperty("actual_value")
        private String actualValue;
        
        @JsonProperty("expected_value")
        private String expectedValue;
        
        @JsonProperty("importance")
        private Integer importance;
    }
    
    /**
     * 处置建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HandlingSuggestions implements Serializable {

        private static final long serialVersionUID = 1L;

        @JsonProperty("immediate_actions")
        private List<String> immediateActions;

        @JsonProperty("investigation_steps")
        private List<String> investigationSteps;

        @JsonProperty("prevention_measures")
        private List<String> preventionMeasures;

        @JsonProperty("recovery_steps")
        private List<String> recoverySteps;

        @JsonProperty("priority")
        private String priority;
    }
    
    /**
     * 攻击链信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttackChainInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("chain_id")
        private String chainId;
        
        @JsonProperty("chain_stage")
        private String chainStage;
        
        @JsonProperty("related_alarms")
        private List<String> relatedAlarms;
        
        @JsonProperty("attack_timeline")
        private List<AttackEvent> attackTimeline;
        
        @JsonProperty("correlation_score")
        private Double correlationScore;
    }
    
    /**
     * 攻击事件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AttackEvent implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        
        @JsonProperty("event_type")
        private String eventType;
        
        @JsonProperty("description")
        private String description;
    }
    
    /**
     * 处理状态
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcessingStatus implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("is_deduplicated")
        private Boolean deduplicated;

        @JsonProperty("is_formatted")
        private Boolean formatted;

        @JsonProperty("is_attack_chain_analyzed")
        private Boolean attackChainAnalyzed;

        @JsonProperty("is_knowledge_enriched")
        private Boolean knowledgeEnriched;

        @JsonProperty("processing_errors")
        private List<String> processingErrors;
    }
    
    /**
     * 去重信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeduplicationInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        @JsonProperty("dedup_key")
        private String dedupKey;
        
        @JsonProperty("first_occurrence")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime firstOccurrence;
        
        @JsonProperty("occurrence_count")
        private Integer occurrenceCount;
        
        @JsonProperty("last_occurrence")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastOccurrence;
    }
    
    /**
     * 获取告警的唯一标识键
     */
    public String getDeduplicationKey() {
        return String.format("%s:%s:%s:%s:%s", 
            alarmType, srcIp, dstIp, threatType, alarmName);
    }
    
    /**
     * 获取攻击链关联键
     */
    public String getAttackChainKey() {
        return String.format("%s:%s", srcIp, dstIp);
    }
    
    /**
     * 检查是否为高优先级告警
     */
    public boolean isHighPriority() {
        return alarmLevel == AlarmEvent.AlarmLevel.HIGH || 
               alarmLevel == AlarmEvent.AlarmLevel.CRITICAL;
    }
    
    /**
     * 检查是否为证书相关告警
     */
    public boolean isCertificateAlarm() {
        return certificateInfo != null && 
               ("certificate-analyzer".equals(sourceModule) || 
                alarmType != null && alarmType.toLowerCase().contains("cert"));
    }
}

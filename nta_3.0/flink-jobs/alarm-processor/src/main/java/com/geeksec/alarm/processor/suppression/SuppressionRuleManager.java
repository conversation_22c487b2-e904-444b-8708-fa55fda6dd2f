package com.geeksec.alarm.processor.suppression;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.alarm.processor.cache.RedisAlarmCacheManager;
import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.suppression.dto.AlarmSuppressionChangeMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import java.io.Closeable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 抑制规则管理器
 * 负责管理抑制规则的加载、更新和查询
 *
 * 职责：
 * 1. 从外部服务加载初始抑制规则
 * 2. 监听Kafka变更事件并更新规则
 * 3. 提供抑制规则查询接口
 * 4. 管理Redis缓存操作
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class SuppressionRuleManager implements Closeable {

    /** API响应状态常量 */
    private static final String RESPONSE_STATUS_SUCCESS = "success";
    private static final String RESPONSE_KEY_STATUS = "status";
    private static final String RESPONSE_KEY_DATA = "data";

    /** 操作类型常量 */
    private static final String OPERATION_ADD = "ADD";
    private static final String OPERATION_REMOVE = "REMOVE";

    private final AlarmProcessorConfig config;
    private final RedisAlarmCacheManager cacheManager;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    public SuppressionRuleManager(AlarmProcessorConfig config) {
        this.config = config;
        this.cacheManager = new RedisAlarmCacheManager(config.getRedisConfig());
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();

        // 初始化时加载全量抑制规则
        loadInitialSuppressionRules();
    }
    
    /**
     * 初始化时加载全量抑制规则
     */
    protected void loadInitialSuppressionRules() {
        try {
            log.info("开始加载初始抑制规则");

            String url = config.getAlarmServiceBaseUrl() + "/alarm-suppression";
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();

                    // 解析响应
                    Map<String, Object> responseMap = objectMapper.readValue(responseBody,
                            new TypeReference<Map<String, Object>>() {});

                    if (RESPONSE_STATUS_SUCCESS.equals(responseMap.get(RESPONSE_KEY_STATUS))) {
                        Object data = responseMap.get(RESPONSE_KEY_DATA);
                        if (data instanceof List) {
                            @SuppressWarnings("unchecked")
                            List<Map<String, Object>> suppressionRules = (List<Map<String, Object>>) data;

                            // 批量加载到Redis缓存
                            batchLoadSuppressionRules(suppressionRules);

                            log.info("成功加载 {} 条初始抑制规则到Redis", suppressionRules.size());
                        }
                    } else {
                        log.warn("获取抑制规则响应状态异常: {}", responseMap.get(RESPONSE_KEY_STATUS));
                    }
                } else {
                    log.error("获取抑制规则失败: HTTP {}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("加载初始抑制规则失败", e);
        }
    }

    /**
     * 创建抑制规则变更事件数据流
     * 只监听Kafka变更消息，不包含初始加载
     */
    public DataStream<AlarmSuppressionChangeMessage> createSuppressionRuleStream(StreamExecutionEnvironment env) {
        log.info("创建抑制规则变更事件数据流");

        // 从 Kafka 消费抑制规则变更事件
        DataStream<AlarmSuppressionChangeMessage> changeEventStream = createSuppressionChangeEventSource(env);

        // 处理抑制规则变更
        return changeEventStream.process(new SuppressionRuleProcessor());
    }
    
    /**
     * 创建抑制规则变更事件源
     */
    private DataStream<AlarmSuppressionChangeMessage> createSuppressionChangeEventSource(StreamExecutionEnvironment env) {
        log.info("创建抑制规则变更事件源，主题: {}", config.getSuppressionChangesTopic());
        
        KafkaSource<AlarmSuppressionChangeMessage> kafkaSource = KafkaSource.<AlarmSuppressionChangeMessage>builder()
                .setBootstrapServers(config.getKafkaBootstrapServers())
                .setTopics(config.getSuppressionChangesTopic())
                .setGroupId(config.getKafkaGroupId() + "-suppression")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new SuppressionChangeDeserializer())
                .build();
        
        return env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "suppression-change-source")
                .setParallelism(1);
    }
    
    /**
     * 检查告警是否应被抑制
     */
    public boolean shouldSuppress(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return false;
        }

        return cacheManager.shouldSuppressAlarm(victim, attacker, label);
    }

    /**
     * 批量加载抑制规则到Redis
     */
    private void batchLoadSuppressionRules(List<Map<String, Object>> rules) {
        if (rules == null || rules.isEmpty()) {
            return;
        }

        try {
            int successCount = 0;
            for (Map<String, Object> rule : rules) {
                String victim = (String) rule.get("victim");
                String attacker = (String) rule.get("attacker");
                String label = (String) rule.get("label");

                if (victim != null && attacker != null && label != null) {
                    boolean success = cacheManager.addSuppressionRule(
                            victim, attacker, label,
                            config.getRedisConfig().getCacheExpireSeconds());
                    if (success) {
                        successCount++;
                    }
                }
            }
            log.info("批量加载 {}/{} 条抑制规则到Redis", successCount, rules.size());
        } catch (Exception e) {
            log.error("批量加载抑制规则到Redis失败", e);
        }
    }

    /**
     * 抑制规则处理器
     * 只处理ADD和REMOVE两种操作
     */
    private class SuppressionRuleProcessor extends ProcessFunction<AlarmSuppressionChangeMessage, AlarmSuppressionChangeMessage> {

        @Override
        public void processElement(AlarmSuppressionChangeMessage message, Context ctx, Collector<AlarmSuppressionChangeMessage> out) throws Exception {
            if (message == null) {
                return;
            }

            switch (message.getOperation()) {
                case OPERATION_ADD:
                    boolean addSuccess = cacheManager.addSuppressionRule(
                            message.getVictim(),
                            message.getAttacker(),
                            message.getLabel(),
                            config.getRedisConfig().getCacheExpireSeconds());
                    if (addSuccess) {
                        log.debug("添加抑制规则到Redis: {}|{}|{}",
                                message.getVictim(), message.getAttacker(), message.getLabel());
                    }
                    break;
                case OPERATION_REMOVE:
                    boolean removeSuccess = cacheManager.removeSuppressionRule(
                            message.getVictim(),
                            message.getAttacker(),
                            message.getLabel());
                    if (removeSuccess) {
                        log.debug("从Redis移除抑制规则: {}|{}|{}",
                                message.getVictim(), message.getAttacker(), message.getLabel());
                    }
                    break;
                default:
                    log.warn("未知的抑制规则操作类型: {}, 只支持{}和{}",
                            message.getOperation(), OPERATION_ADD, OPERATION_REMOVE);
            }

            // 将消息传递给下游（如果需要的话）
            out.collect(message);
        }
    }

    /**
     * 关闭资源
     */
    @Override
    public void close() {
        try {
            if (cacheManager != null) {
                cacheManager.close();
            }
            log.info("抑制规则管理器已关闭");
        } catch (Exception e) {
            log.error("关闭抑制规则管理器失败", e);
        }
    }
}

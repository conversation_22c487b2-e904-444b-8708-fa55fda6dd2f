package com.geeksec.alarm.processor.source;

import com.geeksec.alarm.processor.model.AlarmEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFilterFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 告警事件验证器
 * 对告警事件进行详细验证和过滤
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmEventValidator extends RichFilterFunction<AlarmEvent> {
    
    private static final long serialVersionUID = 1L;
    
    // 支持的来源模块
    private static final Set<String> SUPPORTED_SOURCE_MODULES = new HashSet<>(Arrays.asList(
            "certificate-analyzer",
            "session-threat-detector",
            "graph-builder",
            "traffic-etl-processor"
    ));
    
    // 支持的协议类型
    private static final Set<String> SUPPORTED_PROTOCOLS = new HashSet<>(Arrays.asList(
            "TCP", "UDP", "HTTP", "HTTPS", "DNS", "SSL", "TLS", "SSH", "FTP", "SMTP", "ICMP"
    ));
    
    // 时间戳容忍度（小时）
    private static final long TIMESTAMP_TOLERANCE_HOURS = 24;
    
    // 指标计数器
    private transient Counter totalEvents;
    private transient Counter validEvents;
    private transient Counter invalidEvents;
    private transient Counter invalidSourceModule;
    private transient Counter invalidTimestamp;
    private transient Counter invalidIpAddress;
    private transient Counter invalidProtocol;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-event-validator");
        
        totalEvents = metricGroup.counter("total_events");
        validEvents = metricGroup.counter("valid_events");
        invalidEvents = metricGroup.counter("invalid_events");
        invalidSourceModule = metricGroup.counter("invalid_source_module");
        invalidTimestamp = metricGroup.counter("invalid_timestamp");
        invalidIpAddress = metricGroup.counter("invalid_ip_address");
        invalidProtocol = metricGroup.counter("invalid_protocol");
        
        log.info("告警事件验证器初始化完成");
    }
    
    @Override
    public boolean filter(AlarmEvent alarmEvent) throws Exception {
        totalEvents.inc();
        
        try {
            // 验证来源模块
            if (!isValidSourceModule(alarmEvent.getSourceModule())) {
                invalidSourceModule.inc();
                log.debug("无效的来源模块: {}", alarmEvent.getSourceModule());
                return false;
            }
            
            // 验证时间戳
            if (!isValidTimestamp(alarmEvent.getTimestamp())) {
                invalidTimestamp.inc();
                log.debug("无效的时间戳: {}", alarmEvent.getTimestamp());
                return false;
            }
            
            // 验证IP地址
            if (!isValidIpAddresses(alarmEvent.getSrcIp(), alarmEvent.getDstIp())) {
                invalidIpAddress.inc();
                log.debug("无效的IP地址: src={}, dst={}", alarmEvent.getSrcIp(), alarmEvent.getDstIp());
                return false;
            }
            
            // 验证协议类型
            if (!isValidProtocol(alarmEvent.getProtocol())) {
                invalidProtocol.inc();
                log.debug("无效的协议类型: {}", alarmEvent.getProtocol());
                return false;
            }
            
            // 验证端口号
            if (!isValidPorts(alarmEvent.getSrcPort(), alarmEvent.getDstPort())) {
                log.debug("无效的端口号: src={}, dst={}", alarmEvent.getSrcPort(), alarmEvent.getDstPort());
                return false;
            }
            
            // 验证告警级别
            if (alarmEvent.getAlarmLevel() == null) {
                log.debug("告警级别为空");
                return false;
            }
            
            // 验证置信度
            if (!isValidConfidence(alarmEvent.getConfidence())) {
                log.debug("无效的置信度: {}", alarmEvent.getConfidence());
                return false;
            }
            
            validEvents.inc();
            log.debug("告警事件验证通过: {}", alarmEvent.getEventId());
            return true;
            
        } catch (Exception e) {
            invalidEvents.inc();
            log.error("验证告警事件时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 验证来源模块
     */
    private boolean isValidSourceModule(String sourceModule) {
        return sourceModule != null && 
               !sourceModule.trim().isEmpty() && 
               SUPPORTED_SOURCE_MODULES.contains(sourceModule);
    }
    
    /**
     * 验证时间戳
     */
    private boolean isValidTimestamp(LocalDateTime timestamp) {
        if (timestamp == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime minTime = now.minus(TIMESTAMP_TOLERANCE_HOURS, ChronoUnit.HOURS);
        LocalDateTime maxTime = now.plus(1, ChronoUnit.HOURS); // 允许1小时的时钟偏差
        
        return timestamp.isAfter(minTime) && timestamp.isBefore(maxTime);
    }
    
    /**
     * 验证IP地址
     */
    private boolean isValidIpAddresses(String srcIp, String dstIp) {
        // 至少需要一个IP地址
        if ((srcIp == null || srcIp.trim().isEmpty()) && 
            (dstIp == null || dstIp.trim().isEmpty())) {
            return false;
        }
        
        // 验证源IP
        if (srcIp != null && !srcIp.trim().isEmpty() && !isValidIpAddress(srcIp)) {
            return false;
        }
        
        // 验证目标IP
        if (dstIp != null && !dstIp.trim().isEmpty() && !isValidIpAddress(dstIp)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证单个IP地址格式
     */
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        // 简单的IPv4格式验证
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 验证协议类型
     */
    private boolean isValidProtocol(String protocol) {
        if (protocol == null || protocol.trim().isEmpty()) {
            return true; // 协议可以为空
        }
        
        return SUPPORTED_PROTOCOLS.contains(protocol.toUpperCase());
    }
    
    /**
     * 验证端口号
     */
    private boolean isValidPorts(Integer srcPort, Integer dstPort) {
        if (srcPort != null && (srcPort < 0 || srcPort > 65535)) {
            return false;
        }
        
        if (dstPort != null && (dstPort < 0 || dstPort > 65535)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证置信度
     */
    private boolean isValidConfidence(Double confidence) {
        if (confidence == null) {
            return true; // 置信度可以为空
        }
        
        return confidence >= 0.0 && confidence <= 1.0;
    }
    
    @Override
    public void close() throws Exception {
        log.info("告警事件验证器关闭，验证统计: 总计={}, 有效={}, 无效={}, 无效来源={}, 无效时间={}, 无效IP={}, 无效协议={}", 
                totalEvents.getCount(), 
                validEvents.getCount(), 
                invalidEvents.getCount(),
                invalidSourceModule.getCount(),
                invalidTimestamp.getCount(),
                invalidIpAddress.getCount(),
                invalidProtocol.getCount());
        super.close();
    }
}

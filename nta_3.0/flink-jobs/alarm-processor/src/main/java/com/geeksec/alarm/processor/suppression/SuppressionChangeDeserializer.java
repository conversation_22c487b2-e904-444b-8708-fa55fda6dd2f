package com.geeksec.alarm.processor.suppression;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.geeksec.alarm.processor.suppression.dto.AlarmSuppressionChangeMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 抑制规则变更消息反序列化器
 * 将 Kafka 消息反序列化为 AlarmSuppressionChangeMessage 对象
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class SuppressionChangeDeserializer implements DeserializationSchema<AlarmSuppressionChangeMessage> {
    
    private static final long serialVersionUID = 1L;
    
    private transient ObjectMapper objectMapper;
    
    @Override
    public void open(InitializationContext context) throws Exception {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        log.info("抑制规则变更消息反序列化器已初始化");
    }
    
    @Override
    public AlarmSuppressionChangeMessage deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            String json = new String(message, StandardCharsets.UTF_8);
            log.debug("反序列化抑制规则变更消息: {}", json);
            
            AlarmSuppressionChangeMessage changeMessage = objectMapper.readValue(json, AlarmSuppressionChangeMessage.class);
            
            // 基本验证
            if (changeMessage.getOperation() == null || changeMessage.getOperation().trim().isEmpty()) {
                log.warn("操作类型为空，跳过处理");
                return null;
            }
            
            if (changeMessage.getVictim() == null || changeMessage.getVictim().trim().isEmpty()) {
                log.warn("受害者IP为空，跳过处理");
                return null;
            }
            
            if (changeMessage.getAttacker() == null || changeMessage.getAttacker().trim().isEmpty()) {
                log.warn("攻击者IP为空，跳过处理");
                return null;
            }
            
            if (changeMessage.getLabel() == null || changeMessage.getLabel().trim().isEmpty()) {
                log.warn("标签为空，跳过处理");
                return null;
            }
            
            return changeMessage;
            
        } catch (Exception e) {
            log.error("反序列化抑制规则变更消息失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(AlarmSuppressionChangeMessage nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<AlarmSuppressionChangeMessage> getProducedType() {
        return TypeInformation.of(AlarmSuppressionChangeMessage.class);
    }
}

package com.geeksec.alarm.processor.pipeline.function;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.suppression.SuppressionRuleManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

/**
 * 增强的告警抑制过滤函数
 * 使用新的抑制规则管理器，支持实时Kafka更新
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public class EnhancedAlarmSuppressionFunction implements FilterFunction<Alarm> {
    
    private static final long serialVersionUID = 1L;
    
    private final SuppressionRuleManager suppressionManager;
    
    // 监控指标
    private transient Counter totalAlarms;
    private transient Counter suppressionHits;
    
    public EnhancedAlarmSuppressionFunction(SuppressionRuleManager suppressionManager) {
        this.suppressionManager = suppressionManager;
    }
    
    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化监控指标
        MetricGroup metricGroup = getRuntimeContext().getMetricGroup()
                .addGroup("alarm-suppression");
        
        totalAlarms = metricGroup.counter("total-alarms");
        suppressionHits = metricGroup.counter("suppression-hits");
        
        log.info("增强的告警抑制过滤函数已初始化");
    }
    
    @Override
    public boolean filter(Alarm alarm) throws Exception {
        totalAlarms.inc();
        
        try {
            // 检查告警是否满足抑制规则
            boolean shouldSuppress = checkSuppressionRules(alarm);
            
            if (shouldSuppress) {
                suppressionHits.inc();
                log.debug("告警满足抑制规则，已过滤: victim={}, attacker={}, alarmType={}",
                        alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType());
                return false; // 过滤掉
            } else {
                log.debug("告警不满足抑制规则，继续处理: victim={}, attacker={}, alarmType={}",
                        alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType());
                return true; // 继续处理
            }
            
        } catch (Exception e) {
            log.error("检查告警抑制规则时发生异常: alarmId={}", alarm.getAlarmId(), e);
            // 发生异常时，为了安全起见，让告警通过
            return true;
        }
    }
    
    /**
     * 检查告警是否满足抑制规则
     */
    private boolean checkSuppressionRules(Alarm alarm) {
        String victim = alarm.getDstIp();
        String attacker = alarm.getSrcIp();
        
        // 检查告警的所有标签是否满足抑制规则
        if (alarm.getLabels() != null && !alarm.getLabels().isEmpty()) {
            for (String label : alarm.getLabels()) {
                if (suppressionManager.shouldSuppress(victim, attacker, label)) {
                    return true;
                }
            }
        }
        
        // 如果没有标签或标签都不满足抑制规则，使用告警类型作为标签
        return suppressionManager.shouldSuppress(victim, attacker, alarm.getAlarmType());
    }
}

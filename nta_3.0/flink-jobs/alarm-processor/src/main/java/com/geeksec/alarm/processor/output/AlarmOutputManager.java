package com.geeksec.alarm.processor.output;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.output.sink.KafkaAlarmSink;
import com.geeksec.alarm.processor.output.sink.PostgreSQLAlarmSink;
import com.geeksec.alarm.processor.pipeline.AlarmProcessingPipeline;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.datastream.DataStream;

/**
 * 告警输出管理器
 * 负责配置和管理所有告警输出
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class AlarmOutputManager {

    /**
     * 私有构造方法，防止实例化
     */
    private AlarmOutputManager() {
        // 工具类，禁止实例化
    }

    /**
     * 配置所有告警输出
     * 
     * @param pipelineResult 流水线结果
     * @param config         配置对象
     */
    public static void configureAllOutputs(AlarmProcessingPipeline.PipelineResult pipelineResult,
            AlarmProcessorConfig config) {

        log.info("开始配置告警输出");

        try {
            // 1. 配置主要告警输出（PostgreSQL）
            configureMainAlarmOutput(pipelineResult, config);

            // 2. 配置高优先级告警特殊处理
            configureHighPriorityAlarmOutput(pipelineResult, config);

            // 3. 配置证书告警特殊处理
            configureCertificateAlarmOutput(pipelineResult, config);

            // 4. 配置处理后告警输出到 Kafka（供 alarm-notification 消费）
            configureProcessedAlarmOutput(pipelineResult, config);

            log.info("告警输出配置完成");

        } catch (Exception e) {
            log.error("配置告警输出时发生异常: {}", e.getMessage(), e);
            throw new RuntimeException("告警输出配置失败", e);
        }
    }

    /**
     * 配置主要告警输出（PostgreSQL）
     */
    private static void configureMainAlarmOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
            AlarmProcessorConfig config) {

        log.info("配置主要告警输出到 PostgreSQL");

        DataStream<Alarm> processedAlarms = pipelineResult.getProcessedAlarms();

        // 添加 PostgreSQL Sink
        processedAlarms
                .addSink(new PostgreSQLAlarmSink(config))
                .name("告警PostgreSQL输出")
                .uid("alarm-postgresql-sink")
                .setParallelism(Math.max(1, config.getJobParallelism() / 2));

        log.info("主要告警输出配置完成");
    }

    /**
     * 配置高优先级告警特殊处理
     */
    private static void configureHighPriorityAlarmOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
            AlarmProcessorConfig config) {

        log.info("配置高优先级告警特殊处理");

        DataStream<Alarm> highPriorityAlarms = pipelineResult.getHighPriorityAlarms();

        // 高优先级告警也写入 PostgreSQL
        highPriorityAlarms
                .addSink(new PostgreSQLAlarmSink(config))
                .name("高优先级告警PostgreSQL输出")
                .uid("high-priority-alarm-postgresql-sink")
                .setParallelism(2);

        log.info("高优先级告警特殊处理配置完成");
    }

    /**
     * 配置证书告警特殊处理
     */
    private static void configureCertificateAlarmOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
            AlarmProcessorConfig config) {

        log.info("配置证书告警特殊处理");

        DataStream<Alarm> certificateAlarms = pipelineResult.getCertificateAlarms();

        // 证书告警通知已由 alarm-notification 服务处理

        // 证书告警也写入 PostgreSQL
        certificateAlarms
                .addSink(new PostgreSQLAlarmSink(config))
                .name("证书告警PostgreSQL输出")
                .uid("certificate-alarm-postgresql-sink")
                .setParallelism(1);

        // 可以添加其他证书告警的特殊处理逻辑
        // 例如：发送到证书管理系统、更新证书黑名单等

        log.info("证书告警特殊处理配置完成");
    }

    /**
     * 配置处理后告警输出到 Kafka（供 alarm-notification 消费）
     */
    private static void configureProcessedAlarmOutput(AlarmProcessingPipeline.PipelineResult pipelineResult,
            AlarmProcessorConfig config) {

        log.info("配置处理后告警输出到 Kafka");

        DataStream<Alarm> processedAlarms = pipelineResult.getProcessedAlarms();

        // 添加 Kafka Sink
        processedAlarms
                .addSink(new KafkaAlarmSink(config, config.getOutputTopic()))
                .name("处理后告警输出")
                .uid("processed-alarm-sink")
                .setParallelism(Math.max(1, config.getJobParallelism() / 4));

        log.info("处理后告警输出配置完成");
    }

    /**
     * 添加调试输出（开发和测试时使用）
     */
    public static void addDebugOutputs(AlarmProcessingPipeline.PipelineResult pipelineResult,
            AlarmProcessorConfig config) {

        if (!config.isMonitoringEnabled()) {
            return;
        }

        log.info("添加调试输出");

        // 添加日志输出用于调试
        pipelineResult.getProcessedAlarms()
                .addSink(new DebugAlarmSink())
                .name("调试日志输出")
                .uid("debug-alarm-sink")
                .setParallelism(1);

        log.info("调试输出添加完成");
    }

    /**
     * 调试告警 Sink
     */
    private static class DebugAlarmSink implements org.apache.flink.streaming.api.functions.sink.SinkFunction<Alarm> {

        private static final long serialVersionUID = 1L;

        @Override
        public void invoke(Alarm alarm, Context context) {
            log.info("调试输出告警: ID={}, 类型={}, 级别={}, 源IP={}, 目标IP={}, 处理时间={}",
                    alarm.getAlarmId(),
                    alarm.getAlarmType(),
                    alarm.getAlarmLevel(),
                    alarm.getSrcIp(),
                    alarm.getDstIp(),
                    alarm.getProcessedTimestamp());
        }
    }
}

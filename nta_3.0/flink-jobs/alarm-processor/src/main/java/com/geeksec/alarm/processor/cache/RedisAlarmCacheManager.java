package com.geeksec.alarm.processor.cache;

import java.io.Closeable;
import java.io.Serializable;
import java.util.Properties;

import com.geeksec.alarm.processor.config.RedisConfig;

import lombok.extern.slf4j.Slf4j;

/**
 * Redis告警缓存管理器
 * 
 * 主要功能：
 * 1. 告警去重缓存
 * 2. 告警抑制规则缓存
 * 3. 告警统计缓存
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class RedisAlarmCacheManager implements Serializable, Closeable {

    private static final long serialVersionUID = 1L;

    /** Redis配置 */
    private final RedisConfig redisConfig;

    /** 是否已初始化 */
    private transient boolean initialized = false;

    /** 告警去重键前缀 */
    private static final String DEDUP_KEY_PREFIX = "alarm:dedup:";

    /** 告警抑制键前缀 */
    private static final String SUPPRESSION_KEY_PREFIX = "alarm:suppression:";

    /** 告警统计键前缀 */
    private static final String STATS_KEY_PREFIX = "alarm:stats:";

    public RedisAlarmCacheManager(RedisConfig redisConfig) {
        this.redisConfig = redisConfig;
        initializeRedisConnectionManager();
    }

    /**
     * 初始化Redis连接管理器
     */
    private void initializeRedisConnectionManager() {
        if (initialized) {
            return;
        }

        try {
            // 将RedisConfig转换为Properties
            Properties redisProperties = convertToProperties(redisConfig);

            // 初始化RedisConnectionManager
            RedisConnectionManager.initConfig(redisProperties);
            RedisConnectionManager.initJedisPool();

            initialized = true;
            log.info("Redis告警缓存管理器初始化成功: {}:{}",
                    redisConfig.getHost(), redisConfig.getPort());
        } catch (Exception e) {
            log.error("Redis告警缓存管理器初始化失败", e);
            throw new RuntimeException("Redis告警缓存管理器初始化失败", e);
        }
    }

    /**
     * 将RedisConfig转换为Properties
     */
    private Properties convertToProperties(RedisConfig config) {
        Properties properties = new Properties();
        properties.setProperty("redis.host", config.getHost());
        properties.setProperty("redis.port", String.valueOf(config.getPort()));
        properties.setProperty("redis.timeout", String.valueOf(config.getConnectionTimeout()));
        properties.setProperty("redis.pool.max-total", String.valueOf(config.getMaxTotal()));
        properties.setProperty("redis.pool.max-idle", String.valueOf(config.getMaxIdle()));
        properties.setProperty("redis.pool.min-idle", String.valueOf(config.getMinIdle()));
        properties.setProperty("redis.pool.max-wait", String.valueOf(config.getSocketTimeout()));

        if (config.getPassword() != null && !config.getPassword().trim().isEmpty()) {
            properties.setProperty("redis.password", config.getPassword());
        }

        return properties;
    }

    // ==================== 告警去重相关方法 ====================

    /**
     * 检查告警是否已存在（用于去重）
     * 
     * @param alarmKey 告警唯一标识
     * @return true-已存在，false-不存在
     */
    public boolean isDuplicateAlarm(String alarmKey) {
        if (alarmKey == null || alarmKey.trim().isEmpty()) {
            return false;
        }

        String key = DEDUP_KEY_PREFIX + alarmKey;
        try {
            return RedisConnectionManager.exists(key);
        } catch (Exception e) {
            log.error("检查告警去重失败: {}", key, e);
            return false;
        }
    }

    /**
     * 添加告警到去重缓存
     * 
     * @param alarmKey      告警唯一标识
     * @param expireSeconds 过期时间（秒）
     * @return true-成功，false-失败
     */
    public boolean addAlarmToDedup(String alarmKey, int expireSeconds) {
        if (alarmKey == null || alarmKey.trim().isEmpty()) {
            return false;
        }

        String key = DEDUP_KEY_PREFIX + alarmKey;
        try {
            if (expireSeconds > 0) {
                return RedisConnectionManager.setex(key, "1", expireSeconds);
            } else {
                return RedisConnectionManager.set(key, "1");
            }
        } catch (Exception e) {
            log.error("添加告警到去重缓存失败: {}", key, e);
            return false;
        }
    }

    // ==================== 告警抑制相关方法 ====================

    /**
     * 检查告警是否应被抑制
     * 
     * @param victim   受害者IP
     * @param attacker 攻击者IP
     * @param label    告警标签
     * @return true-应被抑制，false-不应被抑制
     */
    public boolean shouldSuppressAlarm(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return false;
        }

        String key = SUPPRESSION_KEY_PREFIX + victim + "|" + attacker + "|" + label;
        try {
            return RedisConnectionManager.exists(key);
        } catch (Exception e) {
            log.error("检查告警抑制失败: {}", key, e);
            return false;
        }
    }

    /**
     * 添加告警抑制规则
     * 
     * @param victim        受害者IP
     * @param attacker      攻击者IP
     * @param label         告警标签
     * @param expireSeconds 过期时间（秒），-1表示不过期
     * @return true-成功，false-失败
     */
    public boolean addSuppressionRule(String victim, String attacker, String label, int expireSeconds) {
        if (victim == null || attacker == null || label == null) {
            return false;
        }

        String key = SUPPRESSION_KEY_PREFIX + victim + "|" + attacker + "|" + label;
        try {
            if (expireSeconds > 0) {
                return RedisConnectionManager.setex(key, "1", expireSeconds);
            } else {
                return RedisConnectionManager.set(key, "1");
            }
        } catch (Exception e) {
            log.error("添加告警抑制规则失败: {}", key, e);
            return false;
        }
    }

    /**
     * 移除告警抑制规则
     * 
     * @param victim   受害者IP
     * @param attacker 攻击者IP
     * @param label    告警标签
     * @return true-成功，false-失败
     */
    public boolean removeSuppressionRule(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return false;
        }

        String key = SUPPRESSION_KEY_PREFIX + victim + "|" + attacker + "|" + label;
        try {
            Long deletedCount = RedisConnectionManager.del(key);
            return deletedCount > 0;
        } catch (Exception e) {
            log.error("移除告警抑制规则失败: {}", key, e);
            return false;
        }
    }

    // ==================== 告警统计相关方法 ====================

    /**
     * 增加告警统计计数
     * 
     * @param statsKey      统计键
     * @param expireSeconds 过期时间（秒）
     * @return 当前计数值
     */
    public long incrementAlarmStats(String statsKey, int expireSeconds) {
        if (statsKey == null || statsKey.trim().isEmpty()) {
            return 0;
        }

        String key = STATS_KEY_PREFIX + statsKey;
        try {
            // 获取当前值
            String currentValue = RedisConnectionManager.get(key);
            long count = currentValue != null ? Long.parseLong(currentValue) : 0;
            count++;

            // 设置新值
            if (expireSeconds > 0) {
                RedisConnectionManager.setex(key, String.valueOf(count), expireSeconds);
            } else {
                RedisConnectionManager.set(key, String.valueOf(count));
            }

            return count;
        } catch (Exception e) {
            log.error("增加告警统计计数失败: {}", key, e);
            return 0;
        }
    }

    /**
     * 获取告警统计计数
     * 
     * @param statsKey 统计键
     * @return 计数值
     */
    public long getAlarmStats(String statsKey) {
        if (statsKey == null || statsKey.trim().isEmpty()) {
            return 0;
        }

        String key = STATS_KEY_PREFIX + statsKey;
        try {
            String value = RedisConnectionManager.get(key);
            return value != null ? Long.parseLong(value) : 0;
        } catch (Exception e) {
            log.error("获取告警统计计数失败: {}", key, e);
            return 0;
        }
    }

    /**
     * 设置告警统计值
     * 
     * @param statsKey      统计键
     * @param value         统计值
     * @param expireSeconds 过期时间（秒）
     * @return true-成功，false-失败
     */
    public boolean setAlarmStats(String statsKey, long value, int expireSeconds) {
        if (statsKey == null || statsKey.trim().isEmpty()) {
            return false;
        }

        String key = STATS_KEY_PREFIX + statsKey;
        try {
            if (expireSeconds > 0) {
                return RedisConnectionManager.setex(key, String.valueOf(value), expireSeconds);
            } else {
                return RedisConnectionManager.set(key, String.valueOf(value));
            }
        } catch (Exception e) {
            log.error("设置告警统计值失败: {}", key, e);
            return false;
        }
    }

    @Override
    public void close() {
        try {
            RedisConnectionManager.closePool();
            log.info("Redis告警缓存管理器已关闭");
        } catch (Exception e) {
            log.error("关闭Redis告警缓存管理器失败", e);
        }
    }
}

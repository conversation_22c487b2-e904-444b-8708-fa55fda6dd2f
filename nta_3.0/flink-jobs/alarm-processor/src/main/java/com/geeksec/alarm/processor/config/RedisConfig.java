package com.geeksec.alarm.processor.config;

import lombok.Data;

import java.io.Serializable;

/**
 * Redis配置类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
public class RedisConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** Redis主机地址 */
    private String host = "localhost";
    
    /** Redis端口 */
    private int port = 6379;
    
    /** Redis密码 */
    private String password;
    
    /** Redis数据库索引 */
    private int database = 0;
    
    /** 连接超时时间（毫秒） */
    private int connectionTimeout = 5000;
    
    /** Socket超时时间（毫秒） */
    private int socketTimeout = 5000;
    
    /** 最大连接数 */
    private int maxTotal = 20;
    
    /** 最大空闲连接数 */
    private int maxIdle = 10;
    
    /** 最小空闲连接数 */
    private int minIdle = 2;
    
    /** 抑制规则缓存键前缀 */
    private String suppressionKeyPrefix = "alarm:suppression:";
    
    /** 缓存过期时间（秒），-1表示不过期 */
    private int cacheExpireSeconds = -1;
}

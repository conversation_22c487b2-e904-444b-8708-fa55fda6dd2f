package com.geeksec.alarm.processor.pipeline;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmEvent;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.test.util.MiniClusterWithClientResource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 告警处理流水线测试
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
class AlarmProcessingPipelineTest {
    
    private StreamExecutionEnvironment env;
    private AlarmProcessorConfig config;
    
    @BeforeEach
    void setUp() {
        env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        
        config = new AlarmProcessorConfig();
        config.setDeduplicationEnabled(true);
        config.setFormattingEnabled(true);
        config.setAttackChainEnabled(true);
    }
    
    @Test
    void testPipelineBuild() {
        // 创建测试告警事件
        AlarmEvent testEvent = createTestAlarmEvent();
        
        // 创建数据流
        DataStream<AlarmEvent> alarmEventStream = env.fromElements(testEvent);
        
        // 构建流水线
        AlarmProcessingPipeline.PipelineResult result =
                AlarmProcessingPipeline.build(alarmEventStream, config, env);
        
        // 验证结果不为空
        assertNotNull(result);
        assertNotNull(result.getProcessedAlarms());
        assertNotNull(result.getHighPriorityAlarms());
        assertNotNull(result.getNormalAlarms());
        assertNotNull(result.getCertificateAlarms());
    }
    
    @Test
    void testPipelineWithDisabledComponents() {
        // 禁用所有处理组件
        config.setDeduplicationEnabled(false);
        config.setFormattingEnabled(false);
        config.setAttackChainEnabled(false);
        
        AlarmEvent testEvent = createTestAlarmEvent();
        DataStream<AlarmEvent> alarmEventStream = env.fromElements(testEvent);
        
        // 构建流水线
        AlarmProcessingPipeline.PipelineResult result =
                AlarmProcessingPipeline.build(alarmEventStream, config, env);
        
        // 验证结果仍然有效
        assertNotNull(result);
        assertNotNull(result.getProcessedAlarms());
    }
    
    /**
     * 创建测试告警事件
     */
    private AlarmEvent createTestAlarmEvent() {
        return AlarmEvent.builder()
                .eventId("test-event-001")
                .sourceModule("certificate-analyzer")
                .alarmType("APT证书碰撞")
                .alarmName("测试告警")
                .threatType("APT攻击")
                .alarmLevel(AlarmEvent.AlarmLevel.HIGH)
                .srcIp("*************")
                .dstIp("*********")
                .srcPort(443)
                .dstPort(80)
                .protocol("HTTPS")
                .timestamp(LocalDateTime.now())
                .detectorType("CertificateCollisionDetector")
                .confidence(0.95)
                .description("检测到APT证书碰撞")
                .detectionReasons(Arrays.asList("证书哈希匹配", "域名可疑"))
                .labels(Arrays.asList("APT", "证书", "碰撞"))
                .certificateInfo(AlarmEvent.CertificateInfo.builder()
                        .certHash("abc123def456")
                        .subjectCn("test.example.com")
                        .issuerCn("Test CA")
                        .domains(Arrays.asList("test.example.com", "www.test.example.com"))
                        .notBefore(LocalDateTime.now().minusDays(30))
                        .notAfter(LocalDateTime.now().plusDays(365))
                        .build())
                .build();
    }
}

package com.geeksec.alarm.processor.suppression;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.config.RedisConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 抑制规则管理器测试
 * 注意：这些测试需要Redis服务器运行，或者使用嵌入式Redis进行测试
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
class SuppressionRuleManagerTest {

    private AlarmProcessorConfig config;
    private SuppressionRuleManager suppressionManager;

    @BeforeEach
    void setUp() {
        config = new AlarmProcessorConfig();
        config.setAlarmServiceBaseUrl("http://localhost:8080");
        config.setSuppressionChangesTopic("alarm-suppression-changes");

        // 配置Redis（使用测试配置）
        RedisConfig redisConfig = new RedisConfig();
        redisConfig.setHost("localhost");
        redisConfig.setPort(6379);
        redisConfig.setDatabase(15); // 使用测试数据库
        redisConfig.setSuppressionKeyPrefix("test:alarm:suppression:");
        config.setRedisConfig(redisConfig);
    }

    @Test
    void testShouldSuppressWithNullValues() {
        // 创建管理器（跳过HTTP初始化以避免网络依赖）
        suppressionManager = new SuppressionRuleManager(config) {
            @Override
            protected void loadInitialSuppressionRules() {
                // 跳过HTTP调用以避免网络依赖
            }
        };

        // 测试空值处理
        assertFalse(suppressionManager.shouldSuppress(null, "*************", "malware"));
        assertFalse(suppressionManager.shouldSuppress("*************", null, "malware"));
        assertFalse(suppressionManager.shouldSuppress("*************", "*************", null));

        // 清理
        suppressionManager.close();
    }

    @Test
    void testConfigurationLoading() {
        // 测试配置是否正确加载
        assertNotNull(config.getRedisConfig());
        assertEquals("localhost", config.getRedisConfig().getHost());
        assertEquals(6379, config.getRedisConfig().getPort());
        assertEquals(15, config.getRedisConfig().getDatabase());
        assertEquals("test:alarm:suppression:", config.getRedisConfig().getSuppressionKeyPrefix());
    }

    // 注意：实际的Redis功能测试需要Redis服务器运行
    // 在CI/CD环境中，可以使用testcontainers或嵌入式Redis进行测试
}

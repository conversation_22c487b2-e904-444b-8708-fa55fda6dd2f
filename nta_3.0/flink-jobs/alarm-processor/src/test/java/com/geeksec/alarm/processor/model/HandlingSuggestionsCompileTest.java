package com.geeksec.alarm.processor.model;

import java.util.Arrays;
import java.util.List;

/**
 * 简单的编译测试，验证HandlingSuggestions类的恢复步骤字段是否正确添加
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public class HandlingSuggestionsCompileTest {

    public static void main(String[] args) {
        // 测试HandlingSuggestions类是否包含recoverySteps字段
        List<String> immediateActions = Arrays.asList("立即行动1", "立即行动2");
        List<String> investigationSteps = Arrays.asList("调查步骤1", "调查步骤2");
        List<String> preventionMeasures = Arrays.asList("预防措施1", "预防措施2");
        List<String> recoverySteps = Arrays.asList("恢复步骤1", "恢复步骤2");

        // 使用Builder模式创建HandlingSuggestions对象
        Alarm.HandlingSuggestions suggestions = Alarm.HandlingSuggestions.builder()
                .immediateActions(immediateActions)
                .investigationSteps(investigationSteps)
                .preventionMeasures(preventionMeasures)
                .recoverySteps(recoverySteps)  // 这是新添加的字段
                .priority("高")
                .build();

        // 验证所有字段都可以正常访问
        System.out.println("立即行动: " + suggestions.getImmediateActions());
        System.out.println("调查步骤: " + suggestions.getInvestigationSteps());
        System.out.println("预防措施: " + suggestions.getPreventionMeasures());
        System.out.println("恢复步骤: " + suggestions.getRecoverySteps());  // 新字段
        System.out.println("优先级: " + suggestions.getPriority());

        // 验证恢复步骤字段不为空
        if (suggestions.getRecoverySteps() != null && !suggestions.getRecoverySteps().isEmpty()) {
            System.out.println("✓ 恢复步骤字段添加成功！");
            System.out.println("恢复步骤内容: " + suggestions.getRecoverySteps());
        } else {
            System.out.println("✗ 恢复步骤字段添加失败！");
        }

        // 创建完整的告警对象测试
        Alarm alarm = Alarm.builder()
                .alarmId("test-001")
                .alarmName("测试告警")
                .handlingSuggestions(suggestions)
                .build();

        if (alarm.getHandlingSuggestions() != null && 
            alarm.getHandlingSuggestions().getRecoverySteps() != null) {
            System.out.println("✓ 告警对象中的处理建议包含恢复步骤！");
        } else {
            System.out.println("✗ 告警对象中的处理建议缺少恢复步骤！");
        }

        System.out.println("编译测试完成！");
    }
}

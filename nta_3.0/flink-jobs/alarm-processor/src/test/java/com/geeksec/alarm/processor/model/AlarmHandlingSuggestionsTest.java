package com.geeksec.alarm.processor.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试告警处理建议模型，特别是恢复步骤字段
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
class AlarmHandlingSuggestionsTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    void testHandlingSuggestionsWithRecoverySteps() {
        // 创建包含恢复步骤的处理建议
        List<String> immediateActions = Arrays.asList(
                "立即隔离受感染主机",
                "阻断可疑网络连接"
        );
        
        List<String> investigationSteps = Arrays.asList(
                "分析攻击路径",
                "收集相关证据"
        );
        
        List<String> preventionMeasures = Arrays.asList(
                "更新安全策略",
                "加强监控"
        );
        
        List<String> recoverySteps = Arrays.asList(
                "使用可靠的安全软件进行全面扫描和清除",
                "及时更新操作系统和应用程序到最新版本",
                "修补安全漏洞",
                "备份重要数据，以防万一",
                "恢复正常的网络连接和服务"
        );

        Alarm.HandlingSuggestions suggestions = Alarm.HandlingSuggestions.builder()
                .immediateActions(immediateActions)
                .investigationSteps(investigationSteps)
                .preventionMeasures(preventionMeasures)
                .recoverySteps(recoverySteps)
                .priority("高")
                .build();

        // 验证所有字段都正确设置
        assertNotNull(suggestions);
        assertEquals(immediateActions, suggestions.getImmediateActions());
        assertEquals(investigationSteps, suggestions.getInvestigationSteps());
        assertEquals(preventionMeasures, suggestions.getPreventionMeasures());
        assertEquals(recoverySteps, suggestions.getRecoverySteps());
        assertEquals("高", suggestions.getPriority());
    }

    @Test
    void testHandlingSuggestionsJsonSerialization() throws Exception {
        // 测试JSON序列化和反序列化
        Alarm.HandlingSuggestions suggestions = Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList("立即行动1", "立即行动2"))
                .investigationSteps(Arrays.asList("调查步骤1", "调查步骤2"))
                .preventionMeasures(Arrays.asList("预防措施1", "预防措施2"))
                .recoverySteps(Arrays.asList("恢复步骤1", "恢复步骤2"))
                .priority("中")
                .build();

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(suggestions);
        assertNotNull(json);
        assertTrue(json.contains("recovery_steps"));
        assertTrue(json.contains("恢复步骤1"));
        assertTrue(json.contains("恢复步骤2"));

        // 从JSON反序列化
        Alarm.HandlingSuggestions deserializedSuggestions = 
                objectMapper.readValue(json, Alarm.HandlingSuggestions.class);
        
        assertNotNull(deserializedSuggestions);
        assertEquals(suggestions.getImmediateActions(), deserializedSuggestions.getImmediateActions());
        assertEquals(suggestions.getInvestigationSteps(), deserializedSuggestions.getInvestigationSteps());
        assertEquals(suggestions.getPreventionMeasures(), deserializedSuggestions.getPreventionMeasures());
        assertEquals(suggestions.getRecoverySteps(), deserializedSuggestions.getRecoverySteps());
        assertEquals(suggestions.getPriority(), deserializedSuggestions.getPriority());
    }

    @Test
    void testHandlingSuggestionsWithNullRecoverySteps() {
        // 测试恢复步骤为null的情况
        Alarm.HandlingSuggestions suggestions = Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList("立即行动"))
                .investigationSteps(Arrays.asList("调查步骤"))
                .preventionMeasures(Arrays.asList("预防措施"))
                .recoverySteps(null)
                .priority("低")
                .build();

        assertNotNull(suggestions);
        assertNull(suggestions.getRecoverySteps());
    }

    @Test
    void testHandlingSuggestionsWithEmptyRecoverySteps() {
        // 测试恢复步骤为空列表的情况
        Alarm.HandlingSuggestions suggestions = Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList("立即行动"))
                .investigationSteps(Arrays.asList("调查步骤"))
                .preventionMeasures(Arrays.asList("预防措施"))
                .recoverySteps(Arrays.asList())
                .priority("低")
                .build();

        assertNotNull(suggestions);
        assertNotNull(suggestions.getRecoverySteps());
        assertTrue(suggestions.getRecoverySteps().isEmpty());
    }

    @Test
    void testAlarmWithHandlingSuggestions() {
        // 测试完整的告警对象包含处理建议
        Alarm.HandlingSuggestions suggestions = Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList("立即隔离主机"))
                .investigationSteps(Arrays.asList("分析日志"))
                .preventionMeasures(Arrays.asList("更新规则"))
                .recoverySteps(Arrays.asList("清理恶意软件", "恢复系统"))
                .priority("高")
                .build();

        Alarm alarm = Alarm.builder()
                .alarmId("test-alarm-001")
                .alarmName("测试告警")
                .handlingSuggestions(suggestions)
                .build();

        assertNotNull(alarm);
        assertNotNull(alarm.getHandlingSuggestions());
        assertEquals(2, alarm.getHandlingSuggestions().getRecoverySteps().size());
        assertTrue(alarm.getHandlingSuggestions().getRecoverySteps().contains("清理恶意软件"));
        assertTrue(alarm.getHandlingSuggestions().getRecoverySteps().contains("恢复系统"));
    }
}

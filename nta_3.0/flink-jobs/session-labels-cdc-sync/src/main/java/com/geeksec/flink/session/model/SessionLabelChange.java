package com.geeksec.flink.session.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * 会话标签变更事件
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SessionLabelChange implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("label_id")
    private Integer labelId;
    
    @JsonProperty("operation")
    private String operation; // INSERT, UPDATE, DELETE
    
    @JsonProperty("created_by")
    private Integer createdBy;
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @JsonProperty("source_timestamp")
    private Long sourceTimestamp;
    
    {
        this.timestamp = Instant.now().toEpochMilli();
    }
    
    public SessionLabelChange(String sessionId, Integer labelId, String operation) {
        this();
        this.sessionId = sessionId;
        this.labelId = labelId;
        this.operation = operation;
    }
}

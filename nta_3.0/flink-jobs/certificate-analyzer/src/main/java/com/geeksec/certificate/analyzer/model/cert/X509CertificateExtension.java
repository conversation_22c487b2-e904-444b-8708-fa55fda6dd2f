package com.geeksec.certificate.analyzer.model.cert;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * X509证书扩展信息模型
 * 用于存储证书的扩展字段信息
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class X509CertificateExtension implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 密钥用法扩展
     */
    private KeyUsageExtension keyUsage;

    /**
     * 扩展密钥用法扩展
     */
    private ExtendedKeyUsageExtension extendedKeyUsage;

    /**
     * 基本约束扩展
     */
    private BasicConstraintsExtension basicConstraints;

    /**
     * 主题备用名称扩展
     */
    private SubjectAlternativeNameExtension subjectAlternativeName;

    /**
     * 颁发者备用名称扩展
     */
    private IssuerAlternativeNameExtension issuerAlternativeName;

    /**
     * 主题密钥标识符扩展
     */
    private SubjectKeyIdentifierExtension subjectKeyIdentifier;

    /**
     * 颁发者密钥标识符扩展
     */
    private AuthorityKeyIdentifierExtension authorityKeyIdentifier;

    /**
     * 证书策略扩展
     */
    private CertificatePoliciesExtension certificatePolicies;

    /**
     * CRL分发点扩展
     */
    private CRLDistributionPointsExtension crlDistributionPoints;

    /**
     * 颁发机构信息访问扩展
     */
    private AuthorityInfoAccessExtension authorityInfoAccess;

    /**
     * 其他扩展信息
     */
    private Map<String, Object> otherExtensions;

    /**
     * 密钥用法扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KeyUsageExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean digitalSignature;
        private boolean nonRepudiation;
        private boolean keyEncipherment;
        private boolean dataEncipherment;
        private boolean keyAgreement;
        private boolean keyCertSign;
        private boolean crlSign;
        private boolean encipherOnly;
        private boolean decipherOnly;
    }

    /**
     * 扩展密钥用法扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtendedKeyUsageExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private List<String> keyPurposes;
        private boolean serverAuth;
        private boolean clientAuth;
        private boolean codeSigning;
        private boolean emailProtection;
        private boolean timeStamping;
        private boolean ocspSigning;
    }

    /**
     * 基本约束扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BasicConstraintsExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private boolean ca;
        private Integer pathLenConstraint;
        private boolean critical;
    }

    /**
     * 主题备用名称扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubjectAlternativeNameExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private List<String> dnsNames;
        private List<String> ipAddresses;
        private List<String> emailAddresses;
        private List<String> uris;
        private List<String> otherNames;
    }

    /**
     * 颁发者备用名称扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IssuerAlternativeNameExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private List<String> dnsNames;
        private List<String> ipAddresses;
        private List<String> emailAddresses;
        private List<String> uris;
        private List<String> otherNames;
    }

    /**
     * 主题密钥标识符扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubjectKeyIdentifierExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String keyIdentifier;
    }

    /**
     * 颁发者密钥标识符扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuthorityKeyIdentifierExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String keyIdentifier;
        private String authorityCertIssuer;
        private String authorityCertSerialNumber;
    }

    /**
     * 证书策略扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CertificatePoliciesExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private List<PolicyInformation> policyInformations;
    }

    /**
     * 策略信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PolicyInformation implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String policyIdentifier;
        private List<String> policyQualifiers;
    }

    /**
     * CRL分发点扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CRLDistributionPointsExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private List<String> distributionPoints;
    }

    /**
     * 颁发机构信息访问扩展
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuthorityInfoAccessExtension implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private List<String> ocspUrls;
        private List<String> caIssuerUrls;
    }
}

package com.geeksec.certificate.analyzer.repository;

import java.sql.SQLException;

/**
 * 通用数据库连接提供者接口。
 * 定义了获取和关闭连接的契约。
 *
 * @param <T> 连接对象的类型，例如 java.sql.Connection 或 redis.clients.jedis.Jedis
 * <AUTHOR>
 */
public interface ConnectionProvider<T> extends AutoCloseable {

    /**
     * 获取一个数据库连接。
     *
     * @return 连接对象
     * @throws SQLException 如果获取连接失败
     */
    T getConnection() throws SQLException;

    /**
     * 释放或关闭提供的连接。
     * 如果连接是从连接池获取的，通常是将其返回到池中。
     *
     * @param connection 要关闭的连接对象
     */
    void releaseConnection(T connection);

    /**
     * 关闭连接提供者本身，释放所有底层资源（例如连接池）。
     * 实现 AutoCloseable 接口，允许在 try-with-resources 语句中使用。
     */
    @Override
    void close() throws Exception;
}

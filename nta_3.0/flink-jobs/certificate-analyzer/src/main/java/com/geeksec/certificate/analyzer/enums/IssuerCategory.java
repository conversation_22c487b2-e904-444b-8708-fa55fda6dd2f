package com.geeksec.certificate.analyzer.enums;

/**
 * 证书颁发机构（CA）分类枚举
 */
public enum IssuerCategory {

    TRUSTED_INTERNATIONAL("国际权威CA"),
    SUB_TRUSTED_INTERNATIONAL("国际权威CA的从属CA"),
    NATIONAL_MINISTRY("国家部委CA"),
    SUB_NATIONAL_MINISTRY("国家部委CA的从属CA"),
    LOCAL_GOVERNMENT("地方政府CA"),
    SUB_LOCAL_GOVERNMENT("地方政府CA的从属CA"),
    LARGE_ENTERPRISE("大型企业CA"),
    SUB_LARGE_ENTERPRISE("大型企业CA的从属CA"),
    PERSONAL_HOLDING("个人控股CA"),
    SUB_PERSONAL_HOLDING("个人控股CA的从属CA"),
    PERSONAL_CA("个人CA"),
    OTHER("其他CA");

    private final String chineseName;

    IssuerCategory(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getChineseName() {
        return chineseName;
    }

    /**
     * 根据颁发者信息查找匹配的CA分类。
     *
     * @param issuerText 证书的颁发者信息
     * @return 匹配到的CA分类中文名，如果未匹配到则返回“其他CA”
     */
    public static String findByIssuer(String issuerText) {
        if (issuerText == null || issuerText.trim().isEmpty()) {
            return OTHER.getChineseName();
        }
        for (IssuerCategory category : values()) {
            if (category == OTHER) continue;
            if (issuerText.contains(category.getChineseName())) {
                return category.getChineseName();
            }
        }
        return OTHER.getChineseName();
    }
}

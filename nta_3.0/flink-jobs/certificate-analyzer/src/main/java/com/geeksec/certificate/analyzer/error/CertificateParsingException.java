package com.geeksec.certificate.analyzer.error;

/**
 * 证书解析异常
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class CertificateParsingException extends CertificateAnalysisException {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public CertificateParsingException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public CertificateParsingException(String message, Throwable cause) {
        super(message, cause);
    }
}

package com.geeksec.certificate.analyzer.config;

import com.geeksec.flink.common.config.ConfigurationManager;
import com.geeksec.flink.common.constants.ConfigConstants;
import org.apache.flink.api.java.utils.ParameterTool;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 证书分析器配置类
 * 提供证书分析器专用的配置项和配置获取方法
 * 
 * <AUTHOR>
 */
@Slf4j
public final class CertificateAnalyzerConfig {
    
    private CertificateAnalyzerConfig() {
        // 防止实例化
    }
    
    // ==================== 证书分析器专用配置常量 ====================
    
    /**
     * 证书分析器专用Kafka主题配置
     * 使用 shared-core 中的配置常量
     */
    public static final class Topics {
        /** 证书文件主题 */
        public static final String CERTIFICATE_FILES = ConfigConstants.KAFKA_TOPIC_CERTIFICATE_FILES;
        /** 系统证书主题 */
        public static final String SYSTEM_CERTIFICATES = ConfigConstants.KAFKA_TOPIC_SYSTEM_CERTIFICATES;

        private Topics() {}
    }
    
    /**
     * 证书分析器配置
     * 使用 shared-core 中的配置常量
     */
    public static final class Analysis {
        /** 分析器并行度 */
        public static final String ANALYZER_PARALLELISM = ConfigConstants.CERTIFICATE_ANALYZER_PARALLELISM;
        /** 分析器缓冲区大小 */
        public static final String ANALYZER_BUFFER_SIZE = ConfigConstants.CERTIFICATE_ANALYZER_BUFFER_SIZE;
        /** 分析器超时时间（毫秒） */
        public static final String ANALYZER_TIMEOUT_MS = ConfigConstants.CERTIFICATE_ANALYZER_TIMEOUT_MS;
        /** 是否启用调试模式 */
        public static final String DEBUG_ENABLED = ConfigConstants.CERTIFICATE_ANALYZER_DEBUG_ENABLED;

        private Analysis() {}
    }
    
    /**
     * 去重配置
     */
    public static final class Deduplication {
        /** Redis去重数据库索引 */
        public static final String REDIS_DB_INDEX = "certificate.dedup.redis.db.index";
        /** 布隆过滤器预期插入数量 */
        public static final String BLOOM_EXPECTED_INSERTIONS = "certificate.dedup.bloom.expected.insertions";
        /** 布隆过滤器误判率 */
        public static final String BLOOM_FALSE_POSITIVE_PROBABILITY = "certificate.dedup.bloom.fpp";
        
        private Deduplication() {}
    }
    
    /**
     * 输出配置
     * 使用 shared-core 中的配置常量
     */
    public static final class Output {
        /** 是否启用PostgreSQL输出 */
        public static final String POSTGRESQL_ENABLED = ConfigConstants.CERTIFICATE_OUTPUT_POSTGRESQL_ENABLED;
        /** 是否启用Nebula输出 */
        public static final String NEBULA_ENABLED = ConfigConstants.CERTIFICATE_OUTPUT_NEBULA_ENABLED;
        /** 是否启用Doris输出 */
        public static final String DORIS_ENABLED = "certificate.output.doris.enabled";
        /** Doris输出并行度 */
        public static final String DORIS_PARALLELISM = "certificate.output.doris.parallelism";
        /** Elasticsearch用户证书索引 */
        public static final String ES_USER_INDEX = ConfigConstants.CERTIFICATE_ANALYZER_ES_USER_INDEX;
        /** Elasticsearch系统证书索引 */
        public static final String ES_SYSTEM_INDEX = ConfigConstants.CERTIFICATE_ANALYZER_ES_SYSTEM_INDEX;

        private Output() {}
    }

    /**
     * Nebula图数据库配置
     * 使用 shared-core 中的配置常量
     */
    public static final class Nebula {
        /** Nebula图数据库地址 */
        public static final String GRAPH_ADDR = ConfigConstants.NEBULA_GRAPH_ADDR;
        /** Nebula元数据服务地址 */
        public static final String META_ADDR = ConfigConstants.NEBULA_META_ADDR;
        /** Nebula图空间名称 */
        public static final String SPACE_NAME = ConfigConstants.NEBULA_SPACE_NAME;
        /** Nebula用户名 */
        public static final String USERNAME = ConfigConstants.NEBULA_USERNAME;
        /** Nebula密码 */
        public static final String PASSWORD = ConfigConstants.NEBULA_PASSWORD;
        /** Nebula端口 - 从图数据库地址中解析 */
        public static final String PORT = "nebula.port";

        private Nebula() {}
    }
    
    // ==================== 配置获取方法 ====================
    
    /**
     * 获取配置参数工具
     *
     * @return 配置参数工具
     */
    public static ParameterTool getConfig() {
        return ConfigurationManager.getConfig();
    }
    
    /**
     * 打印配置信息（调试用）
     */
    public static void printConfig() {
        if (isDebugEnabled()) {
            log.info("=== 证书分析器配置信息 ===");
            log.info("证书文件主题: {}", getCertificateFilesTopic());
            log.info("系统证书主题: {}", getSystemCertificatesTopic());
            log.info("分析器并行度: {}", getAnalyzerParallelism());
            log.info("分析器缓冲区大小: {}", getAnalyzerBufferSize());
            log.info("分析器超时时间: {}ms", getAnalyzerTimeoutMs());
            log.info("PostgreSQL输出启用: {}", isPostgreSQLEnabled());
            log.info("Nebula输出启用: {}", isNebulaEnabled());
            log.info("调试模式启用: {}", isDebugEnabled());
            log.info("========================");
        }
    }

    /**
     * 验证配置的有效性
     *
     * @return 配置验证结果
     */
    public static ConfigValidationResult validateConfig() {
        ConfigValidationResult result = new ConfigValidationResult();

        // 验证必需的主题配置
        String certTopic = getCertificateFilesTopic();
        if (certTopic == null || certTopic.trim().isEmpty()) {
            result.addError("证书文件主题不能为空");
        }

        String systemTopic = getSystemCertificatesTopic();
        if (systemTopic == null || systemTopic.trim().isEmpty()) {
            result.addError("系统证书主题不能为空");
        }

        // 验证数值配置的合理性
        int parallelism = getAnalyzerParallelism();
        if (parallelism <= 0 || parallelism > 64) {
            result.addError("分析器并行度必须在1-64之间，当前值: " + parallelism);
        }

        int bufferSize = getAnalyzerBufferSize();
        if (bufferSize <= 0 || bufferSize > 10000) {
            result.addError("分析器缓冲区大小必须在1-10000之间，当前值: " + bufferSize);
        }

        long timeoutMs = getAnalyzerTimeoutMs();
        if (timeoutMs <= 0 || timeoutMs > 300000) { // 最大5分钟
            result.addError("分析器超时时间必须在1-300000ms之间，当前值: " + timeoutMs);
        }

        // 记录验证结果
        if (result.isValid()) {
            log.info("配置验证通过");
        } else {
            log.error("配置验证失败: {}", result.getErrorMessages());
        }

        return result;
    }

    /**
     * 获取配置摘要信息
     *
     * @return 配置摘要
     */
    public static Map<String, Object> getConfigSummary() {
        Map<String, Object> summary = new HashMap<>();

        // 基本配置
        summary.put("certificateFilesTopic", getCertificateFilesTopic());
        summary.put("systemCertificatesTopic", getSystemCertificatesTopic());
        summary.put("analyzerParallelism", getAnalyzerParallelism());
        summary.put("analyzerBufferSize", getAnalyzerBufferSize());
        summary.put("analyzerTimeoutMs", getAnalyzerTimeoutMs());

        // 开关配置
        summary.put("debugEnabled", isDebugEnabled());
        summary.put("postgresqlEnabled", isPostgreSQLEnabled());
        summary.put("nebulaEnabled", isNebulaEnabled());

        return summary;
    }
    
    /**
     * 获取证书文件主题
     * 
     * @return 证书文件主题名称
     */
    public static String getCertificateFilesTopic() {
        return getConfig().get(Topics.CERTIFICATE_FILES, "certfile");
    }
    
    /**
     * 获取系统证书主题
     * 
     * @return 系统证书主题名称
     */
    public static String getSystemCertificatesTopic() {
        return getConfig().get(Topics.SYSTEM_CERTIFICATES, "certfile_system");
    }
    
    /**
     * 获取分析器并行度
     * 
     * @return 分析器并行度
     */
    public static int getAnalyzerParallelism() {
        return getConfig().getInt(Analysis.ANALYZER_PARALLELISM, 4);
    }
    
    /**
     * 获取分析器缓冲区大小
     * 
     * @return 缓冲区大小
     */
    public static int getAnalyzerBufferSize() {
        return getConfig().getInt(Analysis.ANALYZER_BUFFER_SIZE, 1000);
    }
    
    /**
     * 获取分析器超时时间
     * 
     * @return 超时时间（毫秒）
     */
    public static long getAnalyzerTimeoutMs() {
        return getConfig().getLong(Analysis.ANALYZER_TIMEOUT_MS, 30000L);
    }
    
    /**
     * 是否启用调试模式
     *
     * @return 是否启用调试模式
     */
    public static boolean isDebugEnabled() {
        return getConfig().getBoolean(Analysis.DEBUG_ENABLED, false);
    }
    /**
     * 是否启用PostgreSQL输出
     *
     * @return 是否启用PostgreSQL输出
     */
    public static boolean isPostgreSQLEnabled() {
        return getConfig().getBoolean(Output.POSTGRESQL_ENABLED, false);
    }

    /**
     * 是否启用Doris输出
     *
     * @return 是否启用Doris输出
     */
    public static boolean isDorisEnabled() {
        return getConfig().getBoolean(Output.DORIS_ENABLED, true);
    }

    /**
     * 获取Doris输出并行度
     *
     * @return Doris输出并行度
     */
    public static int getDorisParallelism() {
        return getConfig().getInt(Output.DORIS_PARALLELISM, 4);
    }


    /**
     * 是否启用Nebula输出
     *
     * @return 是否启用Nebula输出
     */
    public static boolean isNebulaEnabled() {
        return getConfig().getBoolean(Output.NEBULA_ENABLED, true);
    }
    
    /**
     * 获取Redis去重数据库索引
     * 
     * @return Redis数据库索引
     */
    public static int getRedisDbIndex() {
        return getConfig().getInt(Deduplication.REDIS_DB_INDEX, 3);
    }
    
    /**
     * 获取布隆过滤器预期插入数量
     * 
     * @return 预期插入数量
     */
    public static long getBloomExpectedInsertions() {
        return getConfig().getLong(Deduplication.BLOOM_EXPECTED_INSERTIONS, 1000000L);
    }
    
    /**
     * 获取布隆过滤器误判率
     *
     * @return 误判率
     */
    public static double getBloomFalsePositiveProbability() {
        return getConfig().getDouble(Deduplication.BLOOM_FALSE_POSITIVE_PROBABILITY, 0.01);
    }

    // ==================== Nebula配置获取方法 ====================

    /**
     * 获取Nebula图数据库地址
     *
     * @return Nebula图数据库地址
     */
    public static String getNebulaGraphAddr() {
        return getConfig().get(Nebula.GRAPH_ADDR, "127.0.0.1:9669");
    }

    /**
     * 获取Nebula元数据服务地址
     *
     * @return Nebula元数据服务地址
     */
    public static String getNebulaMetaAddr() {
        return getConfig().get(Nebula.META_ADDR, "127.0.0.1:9559");
    }

    /**
     * 获取Nebula图空间名称
     *
     * @return Nebula图空间名称
     */
    public static String getNebulaSpace() {
        return getConfig().get(Nebula.SPACE_NAME, "nta_graph");
    }

    /**
     * 获取Nebula用户名
     *
     * @return Nebula用户名
     */
    public static String getNebulaUsername() {
        return getConfig().get(Nebula.USERNAME, "root");
    }

    /**
     * 获取Nebula密码
     *
     * @return Nebula密码
     */
    public static String getNebulaPassword() {
        return getConfig().get(Nebula.PASSWORD, "nebula");
    }

    /**
     * 获取Nebula端口
     *
     * @return Nebula端口
     */
    public static int getNebulaPort() {
        return getConfig().getInt(Nebula.PORT, 9669);
    }

    /**
     * 获取Nebula主机列表
     *
     * @return Nebula主机列表
     */
    public static String getNebulaHosts() {
        return getNebulaGraphAddr();
    }


}

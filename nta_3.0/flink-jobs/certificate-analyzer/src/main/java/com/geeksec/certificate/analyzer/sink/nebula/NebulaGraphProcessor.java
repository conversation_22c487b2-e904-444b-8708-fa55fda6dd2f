package com.geeksec.certificate.analyzer.sink.nebula;

import static com.geeksec.certificate.analyzer.pipeline.CertificateProcessingPipeline.PA1;

import java.util.List;

import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.sink.nebula.CertNebulaDedupOutputTag;

/**
 * Nebula图数据库证书数据处理器
 * 负责将证书数据及其关联实体（签发机构、所有者、URL、组织等）写入Nebula图数据库
 * 
 * <AUTHOR>
 * @Date 2024/8/19
 */

public class NebulaGraphProcessor {

    public static void nebulaInfoHandleFunction(SingleOutputStreamOperator<X509Certificate> writeCertModelStream) {

        // 对证书进行Nebula的入库操作
        SingleOutputStreamOperator<Row> NebulaDedupStream = writeCertModelStream
                .map((MapFunction<X509Certificate, Row>) cert -> {
                    Row row = new Row(2);
                    row.setField(0, "cert_id");
                    row.setField(1, cert);
                    return row;
                }).name("cert_id信息提取").setParallelism(PA1)
                .process(new CertNebulaDedup()).name("nebula查询证书sha1是否已经insert").setParallelism(PA1);

        // 对所有入库的证书进行签发机构和所有者的信息提取以及入库nebula
        SingleOutputStreamOperator<Row> NebulaIssuerStream = writeCertModelStream
                .flatMap(new FlatMapFunction<X509Certificate, Row>() {
                    @Override
                    public void flatMap(X509Certificate cert, Collector<Row> collector) throws Exception {
                        String issuerId = cert.getIssuerId();
                        if (issuerId != null && !issuerId.isEmpty()) {
                            Row row = new Row(2);
                            row.setField(0, "issuer_id");
                            row.setField(1, cert);
                            collector.collect(row);
                        }
                    }
                }).name("签发机构的信息提取").setParallelism(PA1)
                .process(new CertNebulaDedup()).name("nebula查询证书实体issuer是否已经insert").setParallelism(PA1);

        SingleOutputStreamOperator<Row> NebulaSubjectStream = writeCertModelStream
                .flatMap(new FlatMapFunction<X509Certificate, Row>() {
                    @Override
                    public void flatMap(X509Certificate cert, Collector<Row> collector) throws Exception {
                        String subjectId = cert.getSubjectId();
                        if (subjectId != null && !subjectId.isEmpty()) {
                            Row row = new Row(2);
                            row.setField(0, "subject_id");
                            row.setField(1, cert);
                            collector.collect(row);
                        }
                    }
                }).name("所有者的信息提取").setParallelism(PA1)
                .process(new CertNebulaDedup()).name("nebula查询证书实体subject是否已经insert").setParallelism(PA1);

        // TODO 写入URL点去重
        SingleOutputStreamOperator<Row> NebulaURLStream = writeCertModelStream
                .flatMap(new FlatMapFunction<X509Certificate, Row>() {
                    @Override
                    public void flatMap(X509Certificate cert, Collector<Row> collector) throws Exception {
                        List<String> urls = cert.getCertificateUrls();
                        if (urls.size() > 0) {
                            for (String url : urls) {
                                Row row = new Row(2);
                                row.setField(0, "URL");
                                row.setField(1, cert);
                                row.setField(2, url);
                                collector.collect(row);
                            }
                        }
                    }
                }).name("URL点的信息提取").setParallelism(PA1)
                .process(new CertNebulaDedup()).name("nebula查询证书关联 URL 是否已经insert").setParallelism(PA1);

        // TODO 写入ORG点去重
        SingleOutputStreamOperator<Row> NebulaORGStream = writeCertModelStream
                .flatMap(new FlatMapFunction<X509Certificate, Row>() {
                    @Override
                    public void flatMap(X509Certificate cert, Collector<Row> collector) throws Exception {
                        String org = cert.getOrganization();
                        if (org != null && !org.isEmpty()) {
                            Row row = new Row(2);
                            row.setField(0, "ORG");
                            row.setField(1, cert);
                            collector.collect(row);
                        }
                    }
                }).name("ORG点的信息提取").setParallelism(PA1)
                .process(new CertNebulaDedup()).name("nebula查询证书实体 ORG 是否已经insert").setParallelism(PA1);

        /**
         * 总体图空间的写入
         */
        // 不重实体进行insert点插入
        NebulaSinkFunction
                .updateVertexData(NebulaDedupStream.getSideOutput(CertNebulaDedupOutputTag.Dedup_Nebula_Cert));
        NebulaSinkFunction
                .insertVertexData(NebulaDedupStream.getSideOutput(CertNebulaDedupOutputTag.Not_Dedup_Nebula_Cert));
        NebulaSinkFunction.insertIssuerVertexData(
                NebulaIssuerStream.getSideOutput(CertNebulaDedupOutputTag.Not_Dedup_Nebula_Issuer));
        NebulaSinkFunction.insertSubjectVertexData(
                NebulaSubjectStream.getSideOutput(CertNebulaDedupOutputTag.Not_Dedup_Nebula_Subject));
        NebulaSinkFunction
                .insertURLVertexData(NebulaURLStream.getSideOutput(CertNebulaDedupOutputTag.Not_Dedup_Nebula_URL));
        NebulaSinkFunction
                .insertORGVertexData(NebulaORGStream.getSideOutput(CertNebulaDedupOutputTag.Not_Dedup_Nebula_ORG));

        // 证书直接写入，证书部分提取的关联关联关系
        NebulaSinkFunction.insertSubjectRelatedCert(writeCertModelStream);
        NebulaSinkFunction.insertIssuerRelatedCert(writeCertModelStream);
        NebulaSinkFunction.insertDomainBelongToOrg(writeCertModelStream);
        NebulaSinkFunction.insertIpBelongToOrg(writeCertModelStream);
        NebulaSinkFunction.insertCertBelongToOrg(writeCertModelStream);
        NebulaSinkFunction.insertDomainUrlRelated(writeCertModelStream);
        NebulaSinkFunction.insertIpUrlRelated(writeCertModelStream);
        NebulaSinkFunction.insertCertUrlRelated(writeCertModelStream);
    }
}

package com.geeksec.certificate.analyzer.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 证书业务分类枚举
 */
@Getter
@RequiredArgsConstructor
public enum BusinessCategory {

    SSL_TLS("SSL/TLS证书"),
    DIGITAL_SIGNATURE("数字签名证书"),
    CODE_SIGNING("代码签名证书"),
    SMIME("S/MIME证书"),
    VPN("VPN证书"),
    WLAN("无线局域网（WLAN）证书"),
    E_TICKET("电子票据证书"),
    DIGITAL_COPYRIGHT("数字版权证书"),
    SERVER_IDENTITY("服务器身份证书"),
    ENTERPRISE_IT("企业信息化证书"),
    IOT_IDENTITY("物联网身份识别证书"),
    OTHER("其他业务");

    private final String chineseName;

    /**
     * 根据文本内容查找匹配的业务分类。
     *
     * @param text 要匹配的文本
     * @return 匹配到的业务分类中文名，如果未匹配到则返回“其他业务”
     */
    public static String findByText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return OTHER.getChineseName();
        }
        for (BusinessCategory category : values()) {
            if (category == OTHER) continue;
            if (text.contains(category.getChineseName())) {
                return category.getChineseName();
            }
        }
        return OTHER.getChineseName();
    }
}

package com.geeksec.certificate.analyzer.util.cert;

import java.io.ByteArrayInputStream;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.Security;
import java.security.cert.CertificateEncodingException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.RSAPublicKey;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.text.similarity.LevenshteinDistance;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import com.geeksec.flink.common.utils.crypto.HashUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书工具类
 * 整合了所有证书相关的工具方法，包括：
 * - 证书解析和格式转换
 * - 哈希指纹计算
 * - DN（专有名称）解析
 * - 证书纠错和验证
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class CertificateUtils {

    private static final CertificateFactory CERTIFICATE_FACTORY;
    private static final LevenshteinDistance LEVENSHTEIN_DISTANCE = new LevenshteinDistance();

    static {
        try {
            // 添加BouncyCastle提供者
            Security.addProvider(new BouncyCastleProvider());
            CERTIFICATE_FACTORY = CertificateFactory.getInstance("X.509");
        } catch (Exception e) {
            throw new RuntimeException("初始化证书工厂失败", e);
        }
    }

    // ==================== 证书解析相关方法 ====================

    /**
     * 解析证书字节数据（基础方法）
     *
     * @param certBytes 证书字节数据
     * @return 解析后的证书对象，解析失败返回null
     */
    public static X509Certificate parseCertificate(byte[] certBytes) {
        if (certBytes == null || certBytes.length == 0) {
            return null;
        }

        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(certBytes);
            return (X509Certificate) CERTIFICATE_FACTORY.generateCertificate(bis);
        } catch (Exception e) {
            log.warn("解析证书失败", e);
            return null;
        }
    }

    /**
     * 解析证书（支持多种格式）
     * 支持DER、PEM、Base64等格式
     *
     * @param input 证书字节数据
     * @return 解析后的证书对象，解析失败返回null
     */
    public static X509Certificate parseCertificateAdvanced(byte[] input) {
        if (input == null || input.length == 0) {
            return null;
        }

        // 尝试直接解析DER格式
        X509Certificate cert = parseCertificate(input);
        if (cert != null) {
            return cert;
        }

        // 尝试解析PEM格式
        try {
            String inputStr = new String(input, StandardCharsets.UTF_8);
            if (inputStr.contains(CertificateConstants.PEM_HEADER)) {
                byte[] derBytes = fromPemFormat(inputStr);
                if (derBytes != null) {
                    return parseCertificate(derBytes);
                }
            }
        } catch (Exception e) {
            log.debug("PEM格式解析失败", e);
        }

        // 尝试使用BouncyCastle解析
        try (StringReader reader = new StringReader(new String(input, StandardCharsets.UTF_8));
                PEMParser pemParser = new PEMParser(reader)) {

            Object pemObject = pemParser.readObject();
            if (pemObject instanceof X509CertificateHolder) {
                JcaX509CertificateConverter converter = new JcaX509CertificateConverter();
                return converter.getCertificate((X509CertificateHolder) pemObject);
            }
        } catch (Exception e) {
            log.debug("BouncyCastle解析失败", e);
        }

        log.warn("无法解析证书数据");
        return null;
    }

    // ==================== 哈希计算相关方法 ====================

    /**
     * 计算证书的SHA1指纹
     *
     * @param cert X509证书对象
     * @return SHA1指纹的十六进制字符串，计算失败返回null
     */
    public static String getSha1Fingerprint(X509Certificate cert) {
        if (cert == null) {
            return null;
        }
        try {
            return HashUtils.sha1(cert.getEncoded());
        } catch (CertificateEncodingException e) {
            log.error("无法获取证书编码 (SHA-1): {}", e.getMessage());
            return null;
        }
    }

    /**
     * 计算证书的MD5指纹
     *
     * @param cert X509证书对象
     * @return MD5指纹的十六进制字符串，计算失败返回null
     */
    public static String getMd5Fingerprint(X509Certificate cert) {
        if (cert == null) {
            return null;
        }
        try {
            return HashUtils.md5(cert.getEncoded());
        } catch (CertificateEncodingException e) {
            log.error("无法获取证书编码 (MD5): {}", e.getMessage());
            return null;
        }
    }

    /**
     * 计算证书的SHA256指纹
     *
     * @param cert X509证书对象
     * @return SHA256指纹的十六进制字符串，计算失败返回null
     */
    public static String getSha256Fingerprint(X509Certificate cert) {
        if (cert == null) {
            return null;
        }
        try {
            return HashUtils.sha256(cert.getEncoded());
        } catch (CertificateEncodingException e) {
            log.error("无法获取证书编码 (SHA-256): {}", e.getMessage());
            return null;
        }
    }

    // ==================== DN解析相关方法 ====================

    /**
     * 解析证书主题(Subject)字符串，提取其键值对
     *
     * @param subject 证书主题DN字符串
     * @return 包含主题信息的Map
     */
    public static Map<String, String> parseSubject(String subject) {
        if (subject == null || subject.isEmpty()) {
            return new HashMap<>(CertificateConstants.DN_MAP_INITIAL_CAPACITY);
        }
        return convertToStringMap(parseDn(subject));
    }

    /**
     * 解析证书颁发者(Issuer)字符串，提取其键值对
     *
     * @param issuer 证书颁发者DN字符串
     * @return 包含颁发者信息的Map
     */
    public static Map<String, String> parseIssuer(String issuer) {
        if (issuer == null || issuer.isEmpty()) {
            return new HashMap<>(CertificateConstants.DN_MAP_INITIAL_CAPACITY);
        }
        return convertToStringMap(parseDn(issuer));
    }

    /**
     * 将Map<String, Object>转换为Map<String, String>
     * 对于多值情况，取第一个值
     *
     * @param objectMap 原始Map
     * @return 转换后的String Map
     */
    private static Map<String, String> convertToStringMap(Map<String, Object> objectMap) {
        Map<String, String> stringMap = new HashMap<>(objectMap.size());

        for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> list = (List<String>) value;
                if (!list.isEmpty()) {
                    stringMap.put(entry.getKey(), list.get(0));
                }
            } else if (value != null) {
                stringMap.put(entry.getKey(), value.toString());
            }
        }

        return stringMap;
    }

    /**
     * 通用DN解析方法
     *
     * @param dn DN字符串
     * @return 解析后的键值对Map
     */
    private static Map<String, Object> parseDn(String dn) {
        Map<String, Object> result = new HashMap<>(CertificateConstants.DN_MAP_INITIAL_CAPACITY);

        if (dn == null || dn.trim().isEmpty()) {
            return result;
        }

        try {
            // 分割DN字符串，处理转义字符
            List<String> components = splitDnComponents(dn);

            for (String component : components) {
                String[] keyValue = component.split("=", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();

                    // 处理引号
                    if (value.startsWith("\"") && value.endsWith("\"")) {
                        value = value.substring(1, value.length() - 1);
                    }

                    // 如果键已存在，转换为列表
                    Object existing = result.get(key);
                    if (existing == null) {
                        result.put(key, value);
                    } else if (existing instanceof List) {
                        @SuppressWarnings("unchecked")
                        List<String> list = (List<String>) existing;
                        list.add(value);
                    } else {
                        List<String> list = new ArrayList<>();
                        list.add((String) existing);
                        list.add(value);
                        result.put(key, list);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析DN失败: {}", dn, e);
        }

        return result;
    }

    /**
     * 分割DN组件，处理转义字符和引号
     *
     * @param dn DN字符串
     * @return DN组件列表
     */
    private static List<String> splitDnComponents(String dn) {
        List<String> components = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inQuotes = false;
        boolean escaped = false;

        for (int i = 0; i < dn.length(); i++) {
            char c = dn.charAt(i);

            if (escaped) {
                current.append(c);
                escaped = false;
            } else if (c == '\\') {
                current.append(c);
                escaped = true;
            } else if (c == '"') {
                current.append(c);
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                if (current.length() > 0) {
                    components.add(current.toString().trim());
                    current.setLength(0);
                }
            } else {
                current.append(c);
            }
        }

        if (current.length() > 0) {
            components.add(current.toString().trim());
        }

        return components;
    }

    // ==================== 格式转换相关方法 ====================

    /**
     * 将X509证书对象转换为PEM格式字符串
     *
     * @param cert X509证书对象
     * @return PEM格式的证书字符串，转换失败返回null
     */
    public static String toPem(X509Certificate cert) {
        if (cert == null) {
            return null;
        }
        try {
            Base64.Encoder encoder = Base64.getMimeEncoder(CertificateConstants.PEM_LINE_LENGTH, "\n".getBytes());
            byte[] rawCrtText = cert.getEncoded();
            String encoded = encoder.encodeToString(rawCrtText);
            return CertificateConstants.PEM_HEADER + "\n" + encoded + "\n" + CertificateConstants.PEM_FOOTER;
        } catch (Exception e) {
            log.error("无法将证书转换为PEM格式: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证证书格式是否正确
     *
     * @param certBytes 证书字节数据
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidCertificateFormat(byte[] certBytes) {
        return parseCertificate(certBytes) != null;
    }

    /**
     * 将证书转换为PEM格式字符串
     * 
     * @param certBytes 证书字节数据
     * @return PEM格式字符串，转换失败返回null
     */
    public static String toPemFormat(byte[] certBytes) {
        if (certBytes == null || certBytes.length == 0) {
            return null;
        }

        try {
            String base64Cert = Base64.getEncoder().encodeToString(certBytes);
            StringBuilder pemBuilder = new StringBuilder();
            pemBuilder.append(CertificateConstants.PEM_HEADER).append("\n");

            // 每行固定字符数换行
            for (int i = 0; i < base64Cert.length(); i += CertificateConstants.PEM_LINE_LENGTH) {
                int endIndex = Math.min(i + CertificateConstants.PEM_LINE_LENGTH, base64Cert.length());
                pemBuilder.append(base64Cert, i, endIndex).append("\n");
            }

            pemBuilder.append(CertificateConstants.PEM_FOOTER);
            return pemBuilder.toString();
        } catch (Exception e) {
            log.warn("转换证书为PEM格式失败", e);
            return null;
        }
    }

    /**
     * 从PEM格式字符串解析证书字节数据
     * 
     * @param pemString PEM格式字符串
     * @return 证书字节数据，解析失败返回null
     */
    public static byte[] fromPemFormat(String pemString) {
        if (pemString == null || pemString.trim().isEmpty()) {
            return null;
        }

        try {
            String base64Cert = pemString
                    .replace(CertificateConstants.PEM_HEADER, "")
                    .replace(CertificateConstants.PEM_FOOTER, "")
                    .replaceAll("\\s", "");

            return Base64.getDecoder().decode(base64Cert);
        } catch (Exception e) {
            log.warn("从PEM格式解析证书失败", e);
            return null;
        }
    }

    // ==================== 纠错验证相关方法 ====================

    /**
     * 计算两个字节数组之间的最小编辑距离
     *
     * @param word1 第一个字节数组
     * @param word2 第二个字节数组
     * @return 最小编辑距离
     */
    public static int calculateMinDistance(byte[] word1, byte[] word2) {
        if (word1 == null || word2 == null) {
            throw new IllegalArgumentException("字节数组不能为null");
        }

        if (word1.length == 0) {
            return word2.length;
        }
        if (word2.length == 0) {
            return word1.length;
        }

        // 转换为字符串进行Levenshtein距离计算
        String str1 = new String(word1, StandardCharsets.UTF_8);
        String str2 = new String(word2, StandardCharsets.UTF_8);

        Integer distance = LEVENSHTEIN_DISTANCE.apply(str1, str2);
        return distance != null ? distance : Integer.MAX_VALUE;
    }

    /**
     * 验证纠错结果是否在可接受范围内
     *
     * @param original  原始数据
     * @param corrected 纠错后数据
     * @param errorType 错误类型
     * @return true表示纠错结果可接受，false表示不可接受
     */
    public static boolean validateCorrectionResult(byte[] original, byte[] corrected, String errorType) {
        if (original == null || corrected == null) {
            return false;
        }

        int distance = calculateMinDistance(original, corrected);
        int threshold = getThresholdForErrorType(errorType);

        boolean isValid = distance <= threshold;
        log.debug("纠错验证: 错误类型={}, 编辑距离={}, 阈值={}, 结果={}",
                errorType, distance, threshold, isValid ? "通过" : "失败");

        return isValid;
    }

    /**
     * 根据错误类型获取相应的阈值
     *
     * @param errorType 错误类型
     * @return 对应的阈值
     */
    private static int getThresholdForErrorType(String errorType) {
        if (errorType == null) {
            return CertificateConstants.DEFAULT_CORRECTION_THRESHOLD;
        }

        switch (errorType.toLowerCase()) {
            case "byte_sequence":
                return CertificateConstants.BYTE_SEQUENCE_THRESHOLD;
            case "chunk_hash":
                return CertificateConstants.CHUNK_HASH_THRESHOLD;
            default:
                return CertificateConstants.DEFAULT_CORRECTION_THRESHOLD;
        }
    }

    /**
     * 反转字节数组
     *
     * @param bytes 原始字节数组
     * @return 反转后的字节数组
     */
    public static byte[] reverseBytes(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return bytes;
        }

        byte[] reversed = new byte[bytes.length];
        for (int i = 0; i < bytes.length; i++) {
            reversed[i] = bytes[bytes.length - 1 - i];
        }
        return reversed;
    }

    /**
     * 修复损坏的证书数据
     * 
     * @param corruptedBytes 损坏的证书字节数据
     * @return 修复后的证书字节数据，修复失败返回原数据
     */
    public static byte[] repairCorruptedCertificate(byte[] corruptedBytes) {
        if (corruptedBytes == null || corruptedBytes.length == 0) {
            return corruptedBytes;
        }

        // 尝试多种修复策略
        byte[][] repairStrategies = {
                // 原始数据
                corruptedBytes,
                // 反转字节序
                reverseBytes(corruptedBytes)
                // 可以添加更多修复策略
        };

        for (byte[] strategy : repairStrategies) {
            if (isValidCertificateFormat(strategy)) {
                return strategy;
            }
        }

        log.warn("无法修复损坏的证书数据");
        return corruptedBytes;
    }

    // ==================== 模型证书处理相关方法 ====================

    /**
     * 获取证书的主题密钥标识符
     *
     * @param certificate 证书对象
     * @return 主题密钥标识符，如果不存在则返回null
     */
    public static String getSubjectKeyIdentifier(
            com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate) {
        if (certificate == null) {
            return null;
        }

        Map<String, String> extensions = certificate.getExtensions();
        if (extensions == null) {
            return null;
        }

        return extensions.get(CertificateConstants.EXTENSION_SUBJECT_KEY_IDENTIFIER);
    }

    /**
     * 获取证书的授权密钥标识符
     *
     * @param certificate 证书对象
     * @return 授权密钥标识符，如果不存在则返回null
     */
    public static String getAuthorityKeyIdentifier(
            com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate) {
        if (certificate == null) {
            return null;
        }

        Map<String, String> extensions = certificate.getExtensions();
        if (extensions == null) {
            return null;
        }

        String authorityKeyId = extensions.get(CertificateConstants.EXTENSION_AUTHORITY_KEY_IDENTIFIER);
        if (authorityKeyId == null) {
            return null;
        }
        if (authorityKeyId.startsWith(CertificateConstants.KEY_ID_PREFIX)) {
            // 提取keyid部分
            String[] parts = authorityKeyId.split(",");
            if (parts.length > 0) {
                return parts[0].replace(CertificateConstants.KEY_ID_PREFIX, "").trim();
            }
        }

        return authorityKeyId;
    }

    /**
     * 获取证书的公钥
     *
     * @param certificate 证书对象
     * @return 公钥对象，如果获取失败则返回null
     */
    public static PublicKey getPublicKey(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate) {
        if (certificate == null || certificate.getCert() == null) {
            return null;
        }

        try {
            // 将证书转换为Java证书对象
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            X509Certificate cert = (X509Certificate) cf.generateCertificate(
                    new ByteArrayInputStream(certificate.getCert()));

            // 获取公钥
            return cert.getPublicKey();
        } catch (Exception e) {
            log.error("获取证书公钥失败", e);
            return null;
        }
    }

    /**
     * 从Base64编码的字符串中获取公钥
     *
     * @param publicKeyBase64 Base64编码的公钥字符串
     * @param algorithm       公钥算法
     * @return 公钥对象，如果获取失败则返回null
     */
    public static PublicKey getPublicKeyFromBase64(String publicKeyBase64, String algorithm) {
        if (publicKeyBase64 == null || publicKeyBase64.isEmpty() || algorithm == null || algorithm.isEmpty()) {
            return null;
        }

        try {
            // 解码Base64字符串
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyBase64);

            // 创建公钥规范
            java.security.spec.X509EncodedKeySpec keySpec = new java.security.spec.X509EncodedKeySpec(publicKeyBytes);

            // 获取密钥工厂
            java.security.KeyFactory keyFactory = java.security.KeyFactory.getInstance(algorithm);

            // 生成公钥
            return keyFactory.generatePublic(keySpec);
        } catch (Exception e) {
            log.error("从Base64字符串获取公钥失败", e);
            return null;
        }
    }

    /**
     * 检查证书是否是自签名证书
     *
     * @param certificate 证书对象
     * @return 是否是自签名证书
     */
    public static boolean isSelfSigned(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate) {
        if (certificate == null) {
            return false;
        }

        // 检查主题和颁发者是否相同
        String subjectDn = certificate.getSubjectDn();
        String issuerDn = certificate.getIssuerDn();

        if (subjectDn != null && issuerDn != null && subjectDn.equals(issuerDn)) {
            return true;
        }

        // 检查主题密钥标识符和授权密钥标识符是否相同
        String subjectKeyId = getSubjectKeyIdentifier(certificate);
        String authorityKeyId = getAuthorityKeyIdentifier(certificate);

        return subjectKeyId != null && authorityKeyId != null && subjectKeyId.equals(authorityKeyId);
    }

    /**
     * 检查证书是否是CA证书
     *
     * @param certificate 证书对象
     * @return 是否是CA证书
     */
    public static boolean isCaCertificate(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate) {
        if (certificate == null) {
            return false;
        }

        Map<String, String> extensions = certificate.getExtensions();
        if (extensions == null) {
            return false;
        }

        String basicConstraints = extensions.get(CertificateConstants.EXTENSION_BASIC_CONSTRAINTS);
        return basicConstraints != null && basicConstraints.contains(CertificateConstants.CA_TRUE);
    }

    // ==================== 证书信息提取方法 ====================

    /**
     * 计算证书的各种哈希值
     */
    public static void calculateHashes(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate,
            X509Certificate javaCert) {
        try {
            byte[] encoded = javaCert.getEncoded();

            // DER格式哈希
            certificate.setDerSha1(HashUtils.sha1(encoded));
            certificate.setDerMd5(HashUtils.md5(encoded));
            certificate.setDerSha256(HashUtils.sha256(encoded));

            // PEM格式哈希
            String pemString = toPem(javaCert);
            if (pemString != null) {
                byte[] pemBytes = pemString.getBytes(StandardCharsets.UTF_8);
                certificate.setPemSha1(HashUtils.sha1(pemBytes));
                certificate.setPemMd5(HashUtils.md5(pemBytes));
                certificate.setPemSha256(HashUtils.sha256(pemBytes));
            }

            // SPKI哈希
            byte[] spkiBytes = javaCert.getPublicKey().getEncoded();
            certificate.setSpkiSha256(HashUtils.sha256(spkiBytes));

        } catch (Exception e) {
            log.error("计算证书哈希值失败", e);
        }
    }

    /**
     * 提取证书基本信息
     */
    public static void extractBasicInfo(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate,
            X509Certificate javaCert) {
        try {
            certificate.setVersion(String.valueOf(javaCert.getVersion()));
            certificate.setSerialNumber(javaCert.getSerialNumber().toString());
            certificate.setFormat("X.509");
        } catch (Exception e) {
            log.error("提取证书基本信息失败", e);
        }
    }

    /**
     * 提取主题和颁发者信息
     */
    public static void extractSubjectAndIssuer(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate,
            X509Certificate javaCert) {
        try {
            // 解析主题信息
            Map<String, String> subject = parseDistinguishedName(javaCert.getSubjectX500Principal().getName());
            certificate.setSubject(subject);
            certificate.setCommonName(subject.get("CN"));

            // 解析颁发者信息
            Map<String, String> issuer = parseDistinguishedName(javaCert.getIssuerX500Principal().getName());
            certificate.setIssuer(issuer);

        } catch (Exception e) {
            log.error("提取主题和颁发者信息失败", e);
        }
    }

    /**
     * 提取时间信息
     */
    public static void extractTimeInfo(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate,
            X509Certificate javaCert) {
        try {
            certificate
                    .setNotBefore(javaCert.getNotBefore().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
            certificate
                    .setNotAfter(javaCert.getNotAfter().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        } catch (Exception e) {
            log.error("提取时间信息失败", e);
        }
    }

    /**
     * 提取公钥信息
     */
    public static void extractPublicKeyInfo(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate,
            X509Certificate javaCert) {
        try {
            PublicKey publicKey = javaCert.getPublicKey();
            certificate.setPublicKeyAlgorithm(publicKey.getAlgorithm());
            certificate.setPublicKey(Base64.getEncoder().encodeToString(publicKey.getEncoded()));

            // 计算密钥长度
            if (publicKey instanceof RSAPublicKey) {
                RSAPublicKey rsaKey = (RSAPublicKey) publicKey;
                certificate.setPublicKeyLength(String.valueOf(rsaKey.getModulus().bitLength()));
            }
        } catch (Exception e) {
            log.error("提取公钥信息失败", e);
        }
    }

    /**
     * 提取签名信息
     */
    public static void extractSignatureInfo(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate,
            X509Certificate javaCert) {
        try {
            certificate.setSignatureAlgorithm(javaCert.getSigAlgName());
            certificate.setSignatureAlgOid(javaCert.getSigAlgOID());
            certificate.setSignature(Base64.getEncoder().encodeToString(javaCert.getSignature()));
        } catch (Exception e) {
            log.error("提取签名信息失败", e);
        }
    }

    /**
     * 提取扩展信息
     */
    public static void extractExtensions(com.geeksec.certificate.analyzer.model.cert.X509Certificate certificate,
            X509Certificate javaCert) {
        try {
            // 提取SAN
            Collection<List<?>> sanCollection = javaCert.getSubjectAlternativeNames();
            if (sanCollection != null) {
                List<String> sanList = new ArrayList<>();
                for (List<?> san : sanCollection) {
                    if (san.size() >= 2) {
                        sanList.add(san.get(1).toString());
                    }
                }
                certificate.setSubjectAltNames(sanList);
            }

            // 提取密钥用途
            boolean[] keyUsage = javaCert.getKeyUsage();
            if (keyUsage != null) {
                List<String> usages = new ArrayList<>();
                String[] usageNames = {
                        "digitalSignature", "nonRepudiation", "keyEncipherment", "dataEncipherment",
                        "keyAgreement", "keyCertSign", "cRLSign", "encipherOnly", "decipherOnly"
                };
                for (int i = 0; i < keyUsage.length && i < usageNames.length; i++) {
                    if (keyUsage[i]) {
                        usages.add(usageNames[i]);
                    }
                }
                certificate.setKeyUsage(String.join(",", usages));
            }
        } catch (Exception e) {
            log.error("提取扩展信息失败", e);
        }
    }

    /**
     * 解析DN字符串为Map
     */
    public static Map<String, String> parseDistinguishedName(String dnString) {
        Map<String, String> result = new HashMap<>(8);
        if (dnString == null || dnString.trim().isEmpty()) {
            return result;
        }

        try {
            // 简单的DN解析，处理常见的格式
            String[] parts = dnString.split(",");
            for (String part : parts) {
                String trimmedPart = part.trim();
                int equalIndex = trimmedPart.indexOf('=');
                if (equalIndex > 0 && equalIndex < trimmedPart.length() - 1) {
                    String key = trimmedPart.substring(0, equalIndex).trim();
                    String value = trimmedPart.substring(equalIndex + 1).trim();
                    // 移除可能的引号
                    if (value.startsWith("\"") && value.endsWith("\"")) {
                        value = value.substring(1, value.length() - 1);
                    }
                    result.put(key, value);
                }
            }
        } catch (Exception e) {
            log.error("解析DN字符串失败: {}", dnString, e);
        }

        return result;
    }

    /**
     * 私有构造函数，防止实例化
     */
    private CertificateUtils() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
}

package com.geeksec.certificate.analyzer.source.kafka;

import com.geeksec.certificate.analyzer.config.CertificateAnalyzerConfig;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.flink.common.config.ConfigConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;

import java.util.Arrays;
import java.util.List;

/**
 * 证书Kafka数据源
 * <p>
 * 负责从Kafka消费证书数据并转换为Flink数据流。
 * 支持多个主题的证书数据消费，包括证书文件和系统证书。
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateKafkaSource {

    /**
     * 创建证书Kafka数据源
     *
     * @param env    Flink执行环境
     * @param config 配置参数
     * @return 证书数据流
     */
    public static DataStream<X509Certificate> createSource(StreamExecutionEnvironment env, ParameterTool config) {
        log.info("创建证书Kafka数据源");

        // 获取Kafka主题列表
        List<String> topics = getKafkaTopics(config);
        log.info("订阅Kafka主题: {}", topics);

        // 构建Kafka源
        KafkaSource<X509Certificate> kafkaSource = buildKafkaSource(config, topics);

        // 创建数据流
        DataStream<X509Certificate> certificateStream = env.fromSource(
                kafkaSource,
                WatermarkStrategy.noWatermarks(),
                "证书Kafka数据源"
        );

        log.info("证书Kafka数据源创建完成");
        return certificateStream;
    }

    /**
     * 获取Kafka主题列表
     *
     * @param config 配置参数
     * @return 主题列表
     */
    private static List<String> getKafkaTopics(ParameterTool config) {
        return Arrays.asList(
                config.get(CertificateAnalyzerConfig.Topics.CERTIFICATE_FILES),
                config.get(CertificateAnalyzerConfig.Topics.SYSTEM_CERTIFICATES)
        );
    }

    /**
     * 构建Kafka源
     *
     * @param config 配置参数
     * @param topics 主题列表
     * @return Kafka源
     */
    private static KafkaSource<X509Certificate> buildKafkaSource(ParameterTool config, List<String> topics) {
        return KafkaSource.<X509Certificate>builder()
                .setBootstrapServers(config.get(ConfigConstants.KAFKA_BOOTSTRAP_SERVERS))
                .setTopics(topics)
                .setGroupId(config.get(ConfigConstants.KAFKA_GROUP_ID))
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new CertificateDeserializationSchema())
                .build();
    }

    /**
     * 私有构造函数，防止实例化
     */
    private CertificateKafkaSource() {
        throw new UnsupportedOperationException("工具类不能被实例化");
    }
}

package com.geeksec.certificate.analyzer.error;

/**
 * 签名验证异常
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class SignatureVerificationException extends CertificateAnalysisException {

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public SignatureVerificationException(String message) {
        super(message);
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public SignatureVerificationException(String message, Throwable cause) {
        super(message, cause);
    }
}

package com.geeksec.certificate.analyzer.enums;

/**
 * 证书用户分类枚举
 */
public enum UserCategory {

    PERSONAL("个人"),
    WEBSITE("网站和服务器"),
    ENTERPRISE("企业"),
    BANK("银行"),
    HOSPITAL("医院"),
    SCHOOL("学校"),
    ORGANIZATION("组织机构"),
    ECOMMERCE("电商组织"),
    BOTNET("BOTNET"),
    APT("APT组织"),
    IOT("物联网设备"),
    DEVELOPER("代码开发者"),
    OTHER("其他用户");

    private final String chineseName;

    UserCategory(String chineseName) {
        this.chineseName = chineseName;
    }

    public String getChineseName() {
        return chineseName;
    }

    /**
     * 根据文本内容查找匹配的用户分类。
     *
     * @param text 要匹配的文本
     * @return 匹配到的用户分类中文名，如果未匹配到则返回“其他用户”
     */
    public static String findByText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return OTHER.getChineseName();
        }
        for (UserCategory category : values()) {
            if (category == OTHER) continue;
            // 使用中文名作为关键词进行匹配
            if (text.contains(category.getChineseName())) {
                return category.getChineseName();
            }
        }
        return OTHER.getChineseName();
    }
}

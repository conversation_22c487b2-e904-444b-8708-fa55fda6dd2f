package com.geeksec.certificate.analyzer.operator.preprocessing;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;

/**
 * <AUTHOR>
 * @Date 2024/10/20
 */

public class NormalizationFunction extends RichMapFunction<X509Certificate, X509Certificate> {

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public X509Certificate map(X509Certificate cert) throws Exception {
        List<String> domainList = cert.getCertificateDomains();
        List<String> formattedDomains = new ArrayList<>();

        for (String domain : domainList) {
            String formattedDomain = formatDomain(domain);
            formattedDomains.add(formattedDomain);
        }
        cert.setCertificateDomains(formattedDomains);
        return cert;
    }

    /**
     * 格式化域名，消除大小写差异，限制长度，去除特殊字符。
     *
     * @param domain 原始域名字符串
     * @return 格式化后的域名
     */
    public static String formatDomain(String domain) {

        // 转换为小写
        domain = domain.toLowerCase();

        // 去除特殊字符，只保留字母、数字、点和连字符
        String sanitized = domain.replaceAll("[^a-z0-9\\-\\.]", "");

        // 限制域名长度
        if (sanitized.length() > 255) {
            sanitized = DigestUtils.md5Hex(sanitized);
        }

        return sanitized;
    }
}

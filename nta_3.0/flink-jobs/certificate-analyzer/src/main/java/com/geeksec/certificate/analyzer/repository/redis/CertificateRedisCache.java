package com.geeksec.certificate.analyzer.repository.redis;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.geeksec.flink.common.database.redis.RedisConnectionManager;

import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.ScanParams;
import redis.clients.jedis.params.SetParams;
import redis.clients.jedis.resps.ScanResult;

/**
 * 证书Redis缓存操作类
 * 提供证书相关的Redis缓存和去重功能
 *
 * <AUTHOR>
 * @Date 2022/10/26
 */
@Slf4j
public class CertificateRedisCache {

    // 使用RedisConnectionManager统一管理Redis连接

    /**
     * 获取Redis连接并选择数据库2
     * 使用RedisConnectionManager统一管理
     */
    public static Jedis getJedis() {
        Jedis jedis = RedisConnectionManager.getJedis();
        jedis.select(2);
        return jedis;
    }

    private static SetParams params = new SetParams();
    // 设置过期时间为10分钟
    static {
        params.ex(2 * 60);
    }

    // 设置redis中分段hash的键
    public static void setRedisCertHashList(LinkedList<String> PositiveHash, LinkedList<String> NegativeHash,
            String SHA1) {
        try (Jedis jedis = getJedis()) {
            if (jedis.exists(String.format("%s_PositiveHash", SHA1))) {
                jedis.ltrim(String.format("%s_PositiveHash", SHA1), 1, 0);
            }
            if (jedis.exists(String.format("%s_NegativeHash", SHA1))) {
                jedis.ltrim(String.format("%s_NegativeHash", SHA1), 1, 0);
            }
            for (String hash : PositiveHash) {
                jedis.rpush(String.format("%s_PositiveHash", SHA1), hash);
            }
            jedis.expire(String.format("%s_PositiveHash", SHA1), 10 * 60);
            for (String hash : NegativeHash) {
                jedis.rpush(String.format("%s_NegativeHash", SHA1), hash);
            }
            jedis.expire(String.format("%s_NegativeHash", SHA1), 10 * 60);
        } catch (Exception e) {
            log.error("redis证书hash设置失败，error--->{},SHA1 is--->{}", e, SHA1);
        }
    }

    // 全量查询并根据列表长度进行过滤,查询的是比特反转
    public static String scanRedisReverse(int listLength, LinkedList<String> PositiveHash) {
        try (Jedis jedis = getJedis()) {
            String cursor = ScanParams.SCAN_POINTER_START;
            ScanParams scanParams = new ScanParams().count(100); // 每次迭代返回100个键

            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                List<String> keys = scanResult.getResult();

                for (String key : keys) {
                    long length = jedis.llen(key); // 获取列表长度
                    if (length == listLength && key.contains("Positive")) {
                        int wrong_count = 0;
                        for (int i = 0; i < length; i++) {
                            if (!PositiveHash.get(i).equals(jedis.lindex(key, i))) {
                                wrong_count += 1;
                            }
                            if (wrong_count > 1) {
                                break;
                            }
                            if (wrong_count == 1 && i == length - 1) {
                                return key.split(":")[0];
                            }
                        }
                    }
                }
            } while (!cursor.equals("0"));
            return null;
        } catch (Exception e) {
            log.error("redis扫描失败，error--->{}", e);
            return null;
        }
    }

    // 全量查询并根据列表长度进行过滤,查询的是Positive的比特冗余或者缺失
    public static String scanRedisNum(int listLength, LinkedList<String> Hash, String HashType) {
        try (Jedis jedis = getJedis()) {
            String cursor = ScanParams.SCAN_POINTER_START;
            ScanParams scanParams = new ScanParams().count(100); // 每次迭代返回100个键

            do {
                ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                cursor = scanResult.getCursor();
                List<String> keys = scanResult.getResult();

                for (String key : keys) {
                    long length = jedis.llen(key); // 获取列表长度
                    if (length == listLength && key.contains(HashType)) {
                        boolean status = true;
                        int changeCount = 0;
                        for (int i = 0; i < length; i++) {
                            if (i == 0) {
                                status = Hash.get(i).equals(jedis.lindex(key, i));
                            }
                            if (Hash.get(i).equals(jedis.lindex(key, i)) != status) {
                                changeCount += 1;
                                status = !status;
                            }
                            if (changeCount > 1) {
                                break;
                            }
                            if (changeCount == 1 && i == length - 1) {
                                return key.split(":")[0];
                            }
                        }
                    }
                }
            } while (!cursor.equals("0"));
            return null;
        } catch (Exception e) {
            log.error("redis扫描失败，error--->{},HashType is--->{}", e, HashType);
            return null;
        }
    }

    public static Map<String, String> getSourceCert(String SHA1) {
        Map<String, String> certMap = new HashMap<>();
        try (Jedis jedis = RedisConnectionManager.getJedis()) {
            jedis.select(4);
            // 直接往redis里面写base64编码后的数据
            String byteString = jedis.get(SHA1);
            certMap.put(SHA1, byteString);
            return certMap;
        } catch (Exception e) {
            log.error("redis原文件查询设置失败，error--->{},SHA1 is--->{}", e, SHA1);
            return certMap;
        }
    }


}

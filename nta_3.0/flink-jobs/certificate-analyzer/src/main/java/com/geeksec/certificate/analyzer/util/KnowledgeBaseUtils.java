package com.geeksec.certificate.analyzer.util;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;
import org.apache.flink.configuration.Configuration;

/**
 * 知识库工具类
 * 统一管理KnowledgeBaseClient的创建和配置
 * 简化设计，每个算子创建独立的客户端实例
 *
 * <AUTHOR>
 * @date 2024/12/22
 */
public final class KnowledgeBaseUtils {

    /**
     * 从Flink配置中创建KnowledgeBaseClient实例
     *
     * @param parameters Flink配置参数
     * @return KnowledgeBaseClient实例
     */
    public static KnowledgeBaseClient createInstance(Configuration parameters) {
        String knowledgeBaseUrl = parameters.getString(
            CertificateConstants.CONFIG_KNOWLEDGE_BASE_URL,
            CertificateConstants.DEFAULT_KNOWLEDGE_BASE_URL
        );
        return new KnowledgeBaseClient(knowledgeBaseUrl);
    }

    /**
     * 使用默认配置创建KnowledgeBaseClient实例
     *
     * @return KnowledgeBaseClient实例
     */
    public static KnowledgeBaseClient createInstance() {
        return new KnowledgeBaseClient();
    }

    /**
     * 使用指定URL创建KnowledgeBaseClient实例
     *
     * @param url 知识库服务URL
     * @return KnowledgeBaseClient实例
     */
    public static KnowledgeBaseClient createInstance(String url) {
        return new KnowledgeBaseClient(url);
    }



    private KnowledgeBaseUtils() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
}

package com.geeksec.certificate.analyzer.util.data;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import java.util.HashMap;
import com.geeksec.certificate.analyzer.config.CertificateConstants;
import java.util.Map;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import com.geeksec.certificate.analyzer.util.CertificateFieldSorter;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import lombok.extern.slf4j.Slf4j;

/**
 * 证书数据读取工具
 * 专门负责从各种数据结构中读取证书相关数据
 *
 * <AUTHOR>
 */
@Slf4j
public final class CertificateDataReader {
    
    /**
     * 私有构造函数，防止实例化
     */
    private CertificateDataReader() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 从Map中读取数据，如果不存在则返回默认值
     * 
     * @param dataMap 数据Map
     * @param key 键
     * @param defaultValue 默认值
     * @return 读取到的值或默认值
     */
    public static Object readDataFromKeys(Map<String, Object> dataMap, String key, Object defaultValue) {
        if (dataMap == null) {
            return defaultValue;
        }
        return dataMap.getOrDefault(key, defaultValue);
    }
    
    /**
     * 从HashMap中读取数据，如果不存在则返回默认值
     * 
     * @param certMap 证书Map
     * @param key 键
     * @param def 默认值
     * @return 读取到的值或默认值
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static Object readDataFromKeys(HashMap certMap, String key, String def) {
        if (certMap == null) {
            return def;
        }
        return certMap.getOrDefault(key, def);
    }

    /**
     * 计算Map的MD5哈希值
     *
     * @param map 数据Map
     * @return MD5哈希值
     */
    public static String getMD5(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return "";
        }

        try {
            // 使用CertificateFieldSorter进行排序并计算MD5
            return CertificateFieldSorter.getMd5ForSortedMap(map);
        } catch (Exception e) {
            log.error("计算Map MD5失败", e);
            return "";
        }
    }

    /**
     * 将对象转换为Map
     *
     * @param obj 对象
     * @return Map对象
     */
    @SuppressWarnings("unchecked")
    public static HashMap<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return new HashMap<>();
        }

        if (obj instanceof HashMap) {
            return (HashMap<String, Object>) obj;
        }

        if (obj instanceof Map) {
            HashMap<String, Object> result = new HashMap<>();
            ((Map<?, ?>) obj).forEach((k, v) -> {
                if (k != null) {
                    result.put(k.toString(), v);
                }
            });
            return result;
        }

        // 对于其他类型，返回空Map
        return new HashMap<>();
    }
}

package com.geeksec.certificate.analyzer.sink.database;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.util.client.KnowledgeBaseClient;
import com.geeksec.certificate.analyzer.util.database.PostgreSQLConnectionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书模型写入器
 *
 * 对应NTA 2.0中的mysqlCertModel
 * 将证书信息写入数据库的tb_cert_model表
 *
 * <AUTHOR>
 */
@Slf4j
public class CertificateModelWriter extends RichMapFunction<X509Certificate, X509Certificate> {

    // 标签到模型的映射
    private Map<String, String> tagModelMap = new HashMap<>();
    
    // 威胁分数映射
    private Map<String, Integer> blackScoreMap = new HashMap<>();
    
    // 信任分数映射
    private Map<String, Integer> whiteScoreMap = new HashMap<>();
    
    // 知识库客户端
    private KnowledgeBaseClient knowledgeBaseClient;
    
    // 数据库连接
    private Connection connection;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        log.info("初始化证书模型数据库写入功能");
        
        try {
            // 初始化知识库客户端
            knowledgeBaseClient = new KnowledgeBaseClient();
            
            // 初始化数据库连接
            connection = PostgreSQLConnectionManager.getConnection();
            
            // 加载配置数据
            loadConfigurationData();
            
            log.info("证书模型数据库写入功能初始化完成");
        } catch (Exception e) {
            log.error("证书模型数据库写入功能初始化失败", e);
            throw new IOException("证书模型数据库写入功能初始化失败", e);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (connection != null && !connection.isClosed()) {
            connection.close();
        }
    }

    @Override
    public X509Certificate map(X509Certificate certificate) throws Exception {
        log.debug("写入证书模型数据，证书ID: {}", certificate.getDerSha1());

        try {
            // 计算证书模型
            String certModel = calculateCertificateModel(certificate);
            
            // 写入数据库
            insertCertificateModel(certificate, certModel);
            
            log.debug("证书模型数据写入完成，证书ID: {}, 模型: {}", certificate.getDerSha1(), certModel);
            
        } catch (Exception e) {
            log.error("写入证书模型数据失败，证书ID: " + certificate.getDerSha1(), e);
            // 不抛出异常，避免影响整个流程
        }

        return certificate;
    }

    /**
     * 计算证书模型
     */
    private String calculateCertificateModel(X509Certificate certificate) {
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null || labels.isEmpty()) {
            return "UNKNOWN";
        }

        // 根据标签计算模型
        Map<String, Integer> modelScores = new HashMap<>();
        
        for (CertificateLabel label : labels) {
            String labelName = label.getDescription();
            String model = tagModelMap.get(labelName);
            
            if (model != null) {
                modelScores.put(model, modelScores.getOrDefault(model, 0) + 1);
            }
        }

        // 返回得分最高的模型
        return modelScores.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("NORMAL");
    }

    /**
     * 写入证书模型到数据库
     */
    private void insertCertificateModel(X509Certificate certificate, String model) throws SQLException {
        String sql = """
            INSERT INTO tb_cert_model (
                cert_sha1, cert_model, cert_source, threat_score, trust_score,
                subject, issuer, not_before, not_after, signature_algorithm,
                key_size, labels, import_time, create_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ON CONFLICT (cert_sha1) DO UPDATE SET
                cert_model = EXCLUDED.cert_model,
                threat_score = EXCLUDED.threat_score,
                trust_score = EXCLUDED.trust_score,
                labels = EXCLUDED.labels,
                import_time = EXCLUDED.import_time
            """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, certificate.getDerSha1());
            stmt.setString(2, model);
            stmt.setString(3, certificate.getCertSource() != null ? certificate.getCertSource() : "User");
            stmt.setInt(4, certificate.getThreatScore() != null ? certificate.getThreatScore() : 0);
            stmt.setInt(5, certificate.getTrustScore() != null ? certificate.getTrustScore() : 50);
            stmt.setString(6, certificate.getSubject());
            stmt.setString(7, certificate.getIssuer());
            stmt.setLong(8, certificate.getNotBefore());
            stmt.setLong(9, certificate.getNotAfter());
            stmt.setString(10, certificate.getSignatureAlgorithm());
            stmt.setInt(11, certificate.getKeySize() != null ? certificate.getKeySize() : 0);
            stmt.setString(12, labelsToString(certificate.getLabels()));
            stmt.setLong(13, certificate.getImportTime() != null ? certificate.getImportTime() : System.currentTimeMillis() / 1000);

            stmt.executeUpdate();
        }
    }

    /**
     * 将标签集合转换为字符串
     */
    private String labelsToString(Set<CertificateLabel> labels) {
        if (labels == null || labels.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (CertificateLabel label : labels) {
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append(label.getCode());
        }
        return sb.toString();
    }

    /**
     * 加载配置数据
     */
    private void loadConfigurationData() throws Exception {
        log.info("开始加载证书模型配置数据");
        
        try {
            // 从知识库加载标签到模型的映射
            tagModelMap = knowledgeBaseClient.getTagModelMapping();
            
            // 从知识库加载评分映射
            blackScoreMap = knowledgeBaseClient.getBlackScoreMapping();
            whiteScoreMap = knowledgeBaseClient.getWhiteScoreMapping();
            
            log.info("证书模型配置数据加载完成: 标签模型映射={}, 威胁评分映射={}, 信任评分映射={}",
                    tagModelMap.size(), blackScoreMap.size(), whiteScoreMap.size());
                    
        } catch (Exception e) {
            log.error("加载证书模型配置数据失败，使用默认值", e);
            
            // 使用默认的映射配置
            initializeDefaultMappings();
        }
    }

    /**
     * 初始化默认映射配置
     */
    private void initializeDefaultMappings() {
        log.info("初始化默认映射配置");
        
        // 默认标签到模型映射
        tagModelMap.put("Root CA", "ROOT_CA");
        tagModelMap.put("Intermediate CA", "INTERMEDIATE_CA");
        tagModelMap.put("End Entity", "END_ENTITY");
        tagModelMap.put("EV Cert", "EV_CERT");
        tagModelMap.put("Self Signed Cert", "SELF_SIGNED");
        tagModelMap.put("Expired Cert", "EXPIRED");
        tagModelMap.put("Weak Key Cert", "WEAK_KEY");
        tagModelMap.put("Malicious Domain Cert", "MALICIOUS");
        tagModelMap.put("APT Related Cert", "APT");
        tagModelMap.put("C2 Threat Cert", "C2");
        tagModelMap.put("Mining Cert", "MINING");
        tagModelMap.put("TOR V2 Cert", "TOR");
        tagModelMap.put("TOR V3 Cert", "TOR");
        
        // 默认威胁评分映射
        blackScoreMap.put("Malicious Domain Cert", 80);
        blackScoreMap.put("APT Related Cert", 90);
        blackScoreMap.put("C2 Threat Cert", 85);
        blackScoreMap.put("Mining Cert", 60);
        blackScoreMap.put("TOR V2 Cert", 70);
        blackScoreMap.put("TOR V3 Cert", 70);
        blackScoreMap.put("Self Signed Cert", 20);
        blackScoreMap.put("Expired Cert", 15);
        blackScoreMap.put("Weak Key Cert", 30);
        
        // 默认信任评分映射
        whiteScoreMap.put("Root CA", 30);
        whiteScoreMap.put("Intermediate CA", 25);
        whiteScoreMap.put("EV Cert", 20);
        whiteScoreMap.put("Authority CA Cert", 30);
        whiteScoreMap.put("Builtin Cert", 25);
        whiteScoreMap.put("Hot Domain Cert", 20);
        whiteScoreMap.put("CDN Cert", 15);
        whiteScoreMap.put("Enterprise Cert", 15);
        
        log.info("默认映射配置初始化完成");
    }
}

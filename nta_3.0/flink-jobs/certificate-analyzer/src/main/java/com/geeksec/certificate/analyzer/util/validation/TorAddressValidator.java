package com.geeksec.certificate.analyzer.util.validation;

import java.util.regex.Pattern;

/**
 * Tor地址验证器
 * 用于检测和验证Tor隐藏服务地址
 * 
 * <AUTHOR>
 * @date 2024/12/22
 */
public class TorAddressValidator {
    
    /** 非Tor地址 */
    public static final String NOT_TOR = "NOT_TOR";
    
    /** Tor V2地址 */
    public static final String TOR_V2 = "TOR_V2";
    
    /** Tor V3地址 */
    public static final String TOR_V3 = "TOR_V3";
    
    /** Tor V2地址正则表达式 - 16个字符的base32编码 + .onion */
    private static final Pattern TOR_V2_PATTERN = Pattern.compile("^[a-z2-7]{16}\\.onion$", Pattern.CASE_INSENSITIVE);
    
    /** Tor V3地址正则表达式 - 56个字符的base32编码 + .onion */
    private static final Pattern TOR_V3_PATTERN = Pattern.compile("^[a-z2-7]{56}\\.onion$", Pattern.CASE_INSENSITIVE);
    
    /** 通用Tor地址正则表达式 */
    private static final Pattern TOR_GENERAL_PATTERN = Pattern.compile("^[a-z2-7]+\\.onion$", Pattern.CASE_INSENSITIVE);
    
    /**
     * 验证Tor地址类型
     * 
     * @param address 待验证的地址
     * @return 返回地址类型：NOT_TOR, TOR_V2, TOR_V3
     */
    public static String validateTorAddress(String address) {
        if (address == null || address.isEmpty()) {
            return NOT_TOR;
        }
        
        // 移除可能的协议前缀
        String cleanAddress = address.toLowerCase();
        if (cleanAddress.startsWith("http://")) {
            cleanAddress = cleanAddress.substring(7);
        } else if (cleanAddress.startsWith("https://")) {
            cleanAddress = cleanAddress.substring(8);
        }
        
        // 移除可能的路径部分
        int slashIndex = cleanAddress.indexOf('/');
        if (slashIndex != -1) {
            cleanAddress = cleanAddress.substring(0, slashIndex);
        }
        
        // 移除可能的端口号
        int colonIndex = cleanAddress.lastIndexOf(':');
        if (colonIndex != -1 && colonIndex > cleanAddress.lastIndexOf('.')) {
            cleanAddress = cleanAddress.substring(0, colonIndex);
        }
        
        // 检查是否为Tor V3地址
        if (TOR_V3_PATTERN.matcher(cleanAddress).matches()) {
            return TOR_V3;
        }
        
        // 检查是否为Tor V2地址
        if (TOR_V2_PATTERN.matcher(cleanAddress).matches()) {
            return TOR_V2;
        }
        
        // 检查是否为其他格式的.onion地址
        if (TOR_GENERAL_PATTERN.matcher(cleanAddress).matches()) {
            // 根据长度判断可能的版本
            String onionPart = cleanAddress.substring(0, cleanAddress.length() - 6); // 移除.onion
            if (onionPart.length() == 16) {
                return TOR_V2;
            } else if (onionPart.length() == 56) {
                return TOR_V3;
            } else {
                // 其他长度的.onion地址，可能是未来版本或非标准格式
                return TOR_V3; // 默认归类为V3
            }
        }
        
        return NOT_TOR;
    }
    
    /**
     * 检查是否为Tor地址（任何版本）
     * 
     * @param address 待检查的地址
     * @return 如果是Tor地址返回true，否则返回false
     */
    public static boolean isTorAddress(String address) {
        return !NOT_TOR.equals(validateTorAddress(address));
    }
    
    /**
     * 检查是否为Tor V2地址
     * 
     * @param address 待检查的地址
     * @return 如果是Tor V2地址返回true，否则返回false
     */
    public static boolean isTorV2Address(String address) {
        return TOR_V2.equals(validateTorAddress(address));
    }
    
    /**
     * 检查是否为Tor V3地址
     * 
     * @param address 待检查的地址
     * @return 如果是Tor V3地址返回true，否则返回false
     */
    public static boolean isTorV3Address(String address) {
        return TOR_V3.equals(validateTorAddress(address));
    }
    
    /**
     * 提取Tor地址的onion部分（不包含.onion后缀）
     * 
     * @param address Tor地址
     * @return onion部分，如果不是Tor地址返回null
     */
    public static String extractOnionPart(String address) {
        if (!isTorAddress(address)) {
            return null;
        }
        
        String cleanAddress = address.toLowerCase();
        if (cleanAddress.startsWith("http://")) {
            cleanAddress = cleanAddress.substring(7);
        } else if (cleanAddress.startsWith("https://")) {
            cleanAddress = cleanAddress.substring(8);
        }
        
        int slashIndex = cleanAddress.indexOf('/');
        if (slashIndex != -1) {
            cleanAddress = cleanAddress.substring(0, slashIndex);
        }
        
        int colonIndex = cleanAddress.lastIndexOf(':');
        if (colonIndex != -1 && colonIndex > cleanAddress.lastIndexOf('.')) {
            cleanAddress = cleanAddress.substring(0, colonIndex);
        }
        
        if (cleanAddress.endsWith(".onion")) {
            return cleanAddress.substring(0, cleanAddress.length() - 6);
        }
        
        return null;
    }
    
    /**
     * 验证base32编码的有效性
     * 
     * @param base32String base32编码字符串
     * @return 如果是有效的base32编码返回true，否则返回false
     */
    public static boolean isValidBase32(String base32String) {
        if (base32String == null || base32String.isEmpty()) {
            return false;
        }
        
        // Base32字符集：A-Z, 2-7
        Pattern base32Pattern = Pattern.compile("^[A-Z2-7]+$", Pattern.CASE_INSENSITIVE);
        return base32Pattern.matcher(base32String).matches();
    }
}

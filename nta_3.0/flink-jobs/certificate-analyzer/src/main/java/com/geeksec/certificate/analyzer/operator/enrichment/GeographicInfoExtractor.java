package com.geeksec.certificate.analyzer.operator.enrichment;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.geeksec.certificate.analyzer.config.CertificateConstants;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.util.cert.CertificateNameParser;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书地理信息提取器
 * 负责从证书中提取地理位置（国家、地区）相关信息。
 *
 * <AUTHOR>
 */
@Slf4j
public class GeographicInfoExtractor {

    private final Map<String, String> countryMap;
    private final List<String> areaList = Arrays.asList(CertificateConstants.FIELD_C, CertificateConstants.FIELD_ST, CertificateConstants.FIELD_L);
    private final List<String> jAreaList = Arrays.asList("JURISDICTION_COUNTRY_NAME", "JURISDICTION_STATE_OR_PROVINCE_NAME", "JURISDICTION_LOCALITY_NAME");
    private final KnowledgeBaseClient knowledgeBaseClient;

    /**
     * 初始化，从知识库服务加载国家代码数据。
     */
    public GeographicInfoExtractor(KnowledgeBaseClient knowledgeBaseClient) {
        this.knowledgeBaseClient = knowledgeBaseClient;
        this.countryMap = loadCountriesFromKnowledgeBase();
    }

    /**
     * 从证书中提取地理位置信息。
     *
     * @param certificate 待处理的证书
     */
    public void extract(X509Certificate certificate) {
        Map<String, Object> subjectInfo = CertificateNameParser.parse(certificate.getSubject());

        // 提取国家
        String countryCode = subjectInfo.getOrDefault(CertificateConstants.FIELD_C, "").toString();
        certificate.setCountry(countryMap.getOrDefault(countryCode, countryCode));

        // 提取地区
        certificate.setArea(getArea(subjectInfo));
    }

    /**
     * 从知识库服务加载国家代码和名称的映射
     */
    private Map<String, String> loadCountriesFromKnowledgeBase() {
        Map<String, String> countries = new HashMap<>(256);
        try {
            // 从知识库服务获取所有国家信息
            List<Map<String, Object>> allCountries = knowledgeBaseClient.getAllCountries();

            for (Map<String, Object> countryInfo : allCountries) {
                String lengthTwo = (String) countryInfo.get("lengthTwo");
                String lengthThree = (String) countryInfo.get("lengthThree");
                String cnName = (String) countryInfo.get("cnName");
                String enName = (String) countryInfo.get("enName");

                // 使用中文名称作为显示名称，如果没有则使用英文名称
                String displayName = (cnName != null && !cnName.isEmpty()) ? cnName : enName;

                if (displayName != null && !displayName.isEmpty()) {
                    // 添加两位国家代码映射
                    if (lengthTwo != null && !lengthTwo.isEmpty() && !lengthTwo.equals("—")) {
                        countries.put(lengthTwo.toUpperCase(), displayName);
                    }
                    // 添加三位国家代码映射
                    if (lengthThree != null && !lengthThree.isEmpty() && !lengthThree.equals("—")) {
                        countries.put(lengthThree.toUpperCase(), displayName);
                    }
                }
            }

            log.info("成功从知识库加载国家代码映射 {} 个", countries.size());

        } catch (Exception e) {
            log.error("从知识库加载国家代码映射失败，使用空映射", e);
            // 返回空映射，避免程序崩溃
        }
        return countries;
    }

    /**
     * 根据主题信息组合地区字符串。
     */
    private String getArea(Map<String, Object> mapInfo) {
        Map<String, String> areaMap = new HashMap<>();
        Map<String, String> jAreaMap = new HashMap<>();
        String street = mapInfo.getOrDefault("STREET_ADDRESS", "").toString();

        for (String area : areaList) {
            if (mapInfo.containsKey(area)) {
                areaMap.put(area, mapInfo.get(area).toString());
            }
        }
        for (String area : jAreaList) {
            if (mapInfo.containsKey(area)) {
                jAreaMap.put(area, mapInfo.get(area).toString());
            }
        }

        if (areaMap.size() < jAreaMap.size()) {
            if (jAreaMap.getOrDefault("JURISDICTION_LOCALITY_NAME", "").equals(jAreaMap.getOrDefault("JURISDICTION_STATE_OR_PROVINCE_NAME", ""))) {
                jAreaMap.put("JURISDICTION_LOCALITY_NAME", "");
            }
            return getJAreaName(jAreaMap) + street;
        } else {
            if (areaMap.getOrDefault(CertificateConstants.FIELD_ST, "").equals(areaMap.getOrDefault(CertificateConstants.FIELD_L, ""))) {
                areaMap.put(CertificateConstants.FIELD_L, "");
            }
            return getAreaName(areaMap) + street;
        }
    }

    private String getAreaName(Map<String, String> areaMap) {
        StringBuilder sb = new StringBuilder();
        for (String key : areaMap.keySet()) {
            sb.append(areaMap.get(key));
        }
        return sb.toString();
    }

    private String getJAreaName(Map<String, String> areaMap) {
        StringBuilder sb = new StringBuilder();
        for (String key : areaMap.keySet()) {
            sb.append(areaMap.get(key));
        }
        return sb.toString();
    }
}

package com.geeksec.certificate.analyzer.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

/**
 * 证书来源枚举
 * 
 * <AUTHOR>
 * @since 2025/06/21
 */
@Getter
@RequiredArgsConstructor
@ToString(of = "displayName")
public enum CertificateSource {
    /**
     * 采集到的证书 - 从实时SSL/TLS流量中提取的证书
     */
    COLLECTED(0, "采集到的证书", "从实时SSL/TLS流量中提取的证书"),
    
    /**
     * 导入的证书 - 用户主动导入的证书（包括直接导入的证书文件或从导入的pcap文件中提取的证书）
     */
    IMPORTED(1, "导入的证书", "用户主动导入的证书文件或从pcap文件中提取的证书"),
    
    /**
     * 系统内置证书 - 系统预装的可信根证书
     */
    SYSTEM_BUILTIN(2, "系统内置证书", "系统预装的可信根证书");

    private final int code;
    private final String displayName;
    private final String description;

    /**
     * 根据代码获取证书来源
     * 
     * @param code 来源代码
     * @return 证书来源枚举，如果找不到则返回null
     */
    public static CertificateSource fromCode(int code) {
        for (CertificateSource source : values()) {
            if (source.code == code) {
                return source;
            }
        }
        return null;
    }

    /**
     * 根据Kafka topic确定证书来源
     * 
     * @param topic Kafka topic名称
     * @return 证书来源枚举
     */
    public static CertificateSource fromKafkaTopic(String topic) {
        switch (topic) {
            case "certfile":
                // 实时流量采集的证书
                return COLLECTED;
            case "certfile_system":
                // 系统内置证书
                return SYSTEM_BUILTIN;
            default:
                // 默认认为是采集到的证书
                return COLLECTED;
        }
    }



    // Lombok @ToString(of = "displayName") 会自动生成 toString 方法
}

package com.geeksec.certificate.analyzer.sink.minio;

import static com.geeksec.common.constants.ConfigConstants.MINIO_ACCESS_KEY;
import static com.geeksec.common.constants.ConfigConstants.MINIO_BUCKET_NAME;
import static com.geeksec.common.constants.ConfigConstants.MINIO_ENDPOINT;
import static com.geeksec.common.constants.ConfigConstants.MINIO_SECRET_KEY;

import java.nio.charset.StandardCharsets;

import org.apache.flink.api.common.serialization.Encoder;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.file.sink.FileSink;
import org.apache.flink.core.fs.Path;
import org.apache.flink.core.io.SimpleVersionedSerializer;
import org.apache.flink.streaming.api.functions.sink.filesystem.BucketAssigner;
import org.apache.flink.streaming.api.functions.sink.filesystem.OutputFileConfig;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.OnCheckpointRollingPolicy;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.flink.common.config.ConfigurationManager;

import lombok.extern.slf4j.Slf4j;

/**
 * MinIO FileSink工厂类，用于创建连接到MinIO的FileSink
 *
 * <AUTHOR>
 */
@Slf4j
public class MinioSinkFactory {

    /**
     * S3协议前缀
     */
    private static final String S3_PROTOCOL = "s3://";

    /**
     * 配置工具实例
     */
    private static final ParameterTool CONFIG = ConfigurationManager.getConfig();

    /**
     * 默认配置值
     */
    private static final String DEFAULT_ENDPOINT = "http://localhost:9000";
    private static final String DEFAULT_ACCESS_KEY = "minioadmin";
    private static final String DEFAULT_SECRET_KEY = "minioadmin";
    private static final String DEFAULT_BUCKET = "certificates";

    /**
     * SHA1前缀长度，用于创建目录结构
     */
    private static final int SHA1_PREFIX_LENGTH = 2;

    private MinioSinkFactory() {
        // 私有构造函数，防止实例化
    }

    /**
     * 创建证书的FileSink
     *
     * @return 配置好的FileSink
     */
    public static FileSink<X509Certificate> createCertificateSink() {
        // 设置S3系统属性
        setS3Properties();

        // 获取MinIO配置
        String bucketName = CONFIG.get(MINIO_BUCKET_NAME, DEFAULT_BUCKET);

        // 构建S3路径
        String s3Path = S3_PROTOCOL + bucketName + "/certificates";
        log.info("Creating certificate FileSink with path: {}", s3Path);

        return createCertificateSink(s3Path);
    }

    /**
     * 创建用于存储证书的FileSink
     *
     * @param basePath MinIO基础路径，例如：s3://bucket-name/certificates
     * @return 配置好的FileSink
     */
    private static FileSink<X509Certificate> createCertificateSink(String basePath) {
        OutputFileConfig config = OutputFileConfig.builder()
                .withPartPrefix("")
                .withPartSuffix("")
                .build();

        BucketAssigner<X509Certificate, String> bucketAssigner = new BucketAssigner<X509Certificate, String>() {
            @Override
            public String getBucketId(X509Certificate element, Context context) {
                String sha1 = element.getDerSha1();
                if (sha1 != null && sha1.length() >= SHA1_PREFIX_LENGTH) {
                    // 使用前两个字符作为目录名，避免单个目录下文件过多
                    // 返回完整SHA1作为文件名，不添加前缀和后缀
                    return String.format("%s/%s",
                            sha1.substring(0, SHA1_PREFIX_LENGTH), // 目录
                            sha1); // 文件名
                }
                // 如果无法获取SHA1，则返回null表示不存储该证书
                return null;
            }

            @Override
            public SimpleVersionedSerializer<String> getSerializer() {
                return new SimpleVersionedSerializer<String>() {
                    private static final int VERSION = 1;

                    @Override
                    public int getVersion() {
                        return VERSION;
                    }

                    @Override
                    public byte[] serialize(String str) {
                        return str.getBytes(StandardCharsets.UTF_8);
                    }

                    @Override
                    public String deserialize(int version, byte[] serialized) {
                        if (version != VERSION) {
                            throw new IllegalStateException("Version mismatch. Expected: " + VERSION + ", but was: " + version);
                        }
                        return new String(serialized, StandardCharsets.UTF_8);
                    }
                };
            }
        };

        // 创建文件编码器
        Encoder<X509Certificate> encoder = (element, stream) -> {
            if (element != null && element.getCert() != null) {
                stream.write(element.getCert());
            }
        };

        // 构建FileSink
        return FileSink.forRowFormat(new Path(basePath), encoder)
                .withBucketAssigner(bucketAssigner)
                .withOutputFileConfig(config)
                // 使用检查点触发滚动，确保数据一致性
                .withRollingPolicy(OnCheckpointRollingPolicy.build())
                .build();
    }

    /**
     * 设置S3系统属性，用于连接MinIO
     */
    private static void setS3Properties() {
        // 获取MinIO配置
        String endpoint = CONFIG.get(MINIO_ENDPOINT, DEFAULT_ENDPOINT);
        String accessKey = CONFIG.get(MINIO_ACCESS_KEY, DEFAULT_ACCESS_KEY);
        String secretKey = CONFIG.get(MINIO_SECRET_KEY, DEFAULT_SECRET_KEY);

        // 设置S3端点
        System.setProperty("s3.endpoint", endpoint);
        // 设置S3路径样式访问（MinIO需要）
        System.setProperty("s3.path.style.access", "true");
        // 设置S3凭证
        System.setProperty("s3.access.key", accessKey);
        System.setProperty("s3.secret.key", secretKey);

        // 设置Hadoop AWS配置
        System.setProperty("fs.s3a.endpoint", endpoint);
        System.setProperty("fs.s3a.path.style.access", "true");
        System.setProperty("fs.s3a.access.key", accessKey);
        System.setProperty("fs.s3a.secret.key", secretKey);

        log.info("S3 properties set for MinIO endpoint: {}", endpoint);
    }
}

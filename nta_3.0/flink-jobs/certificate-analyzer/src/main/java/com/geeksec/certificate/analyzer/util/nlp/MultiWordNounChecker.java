package com.geeksec.certificate.analyzer.util.nlp;

import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 多词名词检测器
 * <p>
 * 用于检测证书中的多词名词模式，主要用于识别 DanaBot 和 Quakbot 等恶意软件
 * 使用的证书特征。基于 NLP 技术分析证书主题字段中的文本模式。
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class MultiWordNounChecker {

    /** 常见的英文停用词 */
    private static final Set<String> STOP_WORDS = Set.of(
            "a", "an", "and", "are", "as", "at", "be", "by", "for", "from",
            "has", "he", "in", "is", "it", "its", "of", "on", "that", "the",
            "to", "was", "will", "with", "the", "this", "but", "they", "have",
            "had", "what", "said", "each", "which", "she", "do", "how", "their",
            "if", "up", "out", "many", "then", "them", "these", "so", "some"
    );

    /** 单词分割模式 */
    private static final Pattern WORD_PATTERN = Pattern.compile("[\\s\\-_\\.]+");

    /** 数字模式 */
    private static final Pattern DIGIT_PATTERN = Pattern.compile("\\d+");

    /** 特殊字符模式 */
    private static final Pattern SPECIAL_CHAR_PATTERN = Pattern.compile("[^a-zA-Z0-9\\s\\-_\\.]");

    /**
     * DanaBot 证书检测
     * <p>
     * 检测证书是否符合 DanaBot 恶意软件的特征：
     * - 主题字段包含多个可读的英文单词
     * - 字段结构符合特定模式
     * - 自签名证书
     *
     * @param certificate 待检测的证书
     * @return 如果检测到 DanaBot 特征返回 true，否则返回 false
     */
    public static boolean isDanaBotCertificate(X509Certificate certificate) {
        if (certificate == null) {
            return false;
        }

        try {
            Map<String, Object> subject = certificate.getSubject();
            if (subject == null || subject.size() != 6) {
                return false;
            }

            // 检查是否包含所有必需字段
            String[] requiredFields = {"CN", "L", "O", "ST", "OU", "C"};
            for (String field : requiredFields) {
                if (!subject.containsKey(field)) {
                    return false;
                }
            }

            // 检查各字段是否为可读文本（不包含数字）
            for (String field : requiredFields) {
                String value = getStringValue(subject, field);
                if (!isReadableTextWithoutNumbers(value)) {
                    return false;
                }
            }

            // 检查是否为自签名证书
            if (!certificate.isSelfSigned()) {
                return false;
            }

            log.debug("检测到潜在的 DanaBot 证书特征: {}", certificate.getSubjectCN());
            return true;

        } catch (Exception e) {
            log.debug("DanaBot 检测过程中发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Quakbot 证书检测
     * <p>
     * 检测证书是否符合 Quakbot 恶意软件的特征：
     * - 主题字段结构特定
     * - 颁发者字段结构特定
     * - CN 字段相同但不是自签名
     *
     * @param certificate 待检测的证书
     * @return 如果检测到 Quakbot 特征返回 true，否则返回 false
     */
    public static boolean isQuakbotCertificate(X509Certificate certificate) {
        if (certificate == null) {
            return false;
        }

        try {
            Map<String, Object> subject = certificate.getSubject();
            Map<String, Object> issuer = certificate.getIssuer();

            if (subject == null || issuer == null) {
                return false;
            }

            // 检查主题字段结构（CN, OU, C）
            if (subject.size() != 3 || 
                !subject.containsKey("CN") || 
                !subject.containsKey("OU") || 
                !subject.containsKey("C")) {
                return false;
            }

            // 检查颁发者字段结构（CN, L, O, ST, C）
            if (issuer.size() != 5 || 
                !issuer.containsKey("CN") || 
                !issuer.containsKey("L") || 
                !issuer.containsKey("O") || 
                !issuer.containsKey("ST") || 
                !issuer.containsKey("C")) {
                return false;
            }

            // 检查 CN 是否相同
            String subjectCN = getStringValue(subject, "CN");
            String issuerCN = getStringValue(issuer, "CN");

            if (!subjectCN.equals(issuerCN)) {
                return false;
            }

            // 确保不是自签名证书（Quakbot 的特征）
            if (certificate.isSelfSigned()) {
                return false;
            }

            log.debug("检测到潜在的 Quakbot 证书特征: {}", subjectCN);
            return true;

        } catch (Exception e) {
            log.debug("Quakbot 检测过程中发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查文本是否为可读文本且不包含数字
     *
     * @param text 待检查的文本
     * @return 如果是可读文本且不包含数字返回 true，否则返回 false
     */
    private static boolean isReadableTextWithoutNumbers(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        text = text.trim().toLowerCase();

        // 检查是否包含特殊字符
        if (SPECIAL_CHAR_PATTERN.matcher(text).find()) {
            return false;
        }

        // 检查是否包含数字
        if (DIGIT_PATTERN.matcher(text).find()) {
            return false;
        }

        // 检查是否包含至少一个有意义的英文单词
        String[] words = WORD_PATTERN.split(text);
        int meaningfulWords = 0;

        for (String word : words) {
            word = word.trim();
            if (word.length() >= 2 && !STOP_WORDS.contains(word)) {
                meaningfulWords++;
            }
        }

        // 至少包含一个有意义的单词
        return meaningfulWords >= 1;
    }

    /**
     * 从 Map 中安全获取字符串值
     *
     * @param map 数据映射
     * @param key 键名
     * @return 字符串值，如果不存在则返回空字符串
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        if (map == null || key == null) {
            return "";
        }
        Object value = map.get(key);
        return value != null ? value.toString().trim() : "";
    }

    /**
     * 检查文本是否包含多个有意义的单词
     *
     * @param text 待检查的文本
     * @return 如果包含多个有意义单词返回 true，否则返回 false
     */
    public static boolean containsMultipleWords(String text) {
        if (text == null || text.trim().isEmpty()) {
            return false;
        }

        String[] words = WORD_PATTERN.split(text.toLowerCase().trim());
        int meaningfulWords = 0;

        for (String word : words) {
            word = word.trim();
            if (word.length() >= 2 && !STOP_WORDS.contains(word) && !DIGIT_PATTERN.matcher(word).matches()) {
                meaningfulWords++;
            }
        }

        return meaningfulWords >= 2;
    }

    /**
     * 私有构造函数，防止实例化
     */
    private MultiWordNounChecker() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
}

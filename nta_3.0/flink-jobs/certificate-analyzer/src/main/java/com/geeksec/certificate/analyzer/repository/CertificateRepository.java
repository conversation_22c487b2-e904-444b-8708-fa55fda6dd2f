package com.geeksec.certificate.analyzer.repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.geeksec.certificate.analyzer.repository.doris.DorisCertRecord; // 新增导入

/**
 * 证书数据仓库接口。
 * 定义了访问和操作证书数据的各种方法。
 *
 * <AUTHOR>
 */
public interface CertificateRepository {

    /**
     * 根据证书SHA1查询证书信息。
     *
     * @param certSha1 证书的SHA1哈希值。
     * @return 包含证书详细信息的 DorisCertRecord，如果未找到则返回null。
     */
    DorisCertRecord queryCertBySha1(String certSha1);

    /**
     * 根据多个SHA1批量查询证书信息。
     *
     * @param certSha1List 证书SHA1哈希值的列表。
     * @return 一个Map，键是证书的SHA1哈希值，值是包含证书详细信息的 DorisCertRecord。
     */
    Map<String, DorisCertRecord> batchQueryCertsBySha1(List<String> certSha1List);

    /**
     * 查询父证书信息。
     * 根据issuer_id和authority_key_identifier查询父证书。
     *
     * @param issuerMd5      颁发者的MD5哈希值。
     * @param authorityKeyId 颁发机构密钥标识符。
     * @return 包含父证书详细信息的 DorisCertRecord 列表。
     */
    List<DorisCertRecord> queryParentCerts(String issuerMd5, String authorityKeyId);

    /**
     * 检查白名单证书。
     * 查询是否存在包含白名单标签的证书。
     *
     * @param certSha1 证书SHA1
     * @return 如果是白名单证书返回父证书ID列表（或其他标识），否则返回null或空列表
     */
    Set<String> findTrustedParentCertificates(String certSha1);

    /**
     * 检查碰撞证书。
     * 查询相同PemMD5但不同ASN1SHA1的证书。
     *
     * @param pemMd5   证书PEM格式MD5
     * @param asn1Sha1 证书ASN1格式SHA1
     * @return 如果是碰撞证书返回true
     */
    boolean checkCollisionCert(String pemMd5, String asn1Sha1);

    /**
     * 检查证书是否存在（用于去重）。
     *
     * @param certSha1 证书SHA1
     * @return 如果证书存在返回true
     */
    boolean checkCertExists(String certSha1);

    /**
     * 更新证书的用户ID列表。
     *
     * @param certSha1   证书SHA1
     * @param userIdList 用户ID列表
     * @return 如果更新成功返回true，否则返回false
     */
    boolean updateCertUserIdList(String certSha1, List<String> userIdList);

    /**
     * 获取证书签发数量。
     * 查询以当前证书为父证书的证书数量。
     *
     * @param certSha1 证书SHA1
     * @return 签发的证书数量
     */
    long getCertSignCount(String certSha1);

    /**
     * 根据正向分块哈希列表查询证书的 der_sha1。
     *
     * @param forwardHashes 正向分块哈希列表。
     * @param listLength    用于查询的哈希列表的有效长度。
     * @return 如果找到，则返回 der_sha1；否则返回 null。
     */
    String findDerSha1ByForwardHashes(List<String> forwardHashes, int listLength);

    /**
     * 根据反向分块哈希列表查询证书的 der_sha1。
     *
     * @param reverseHashes 反向分块哈希列表。
     * @param listLength    用于查询的哈希列表的有效长度。
     * @return 如果找到，则返回 der_sha1；否则返回 null。
     */
    String findDerSha1ByReverseHashes(List<String> reverseHashes, int listLength);

}

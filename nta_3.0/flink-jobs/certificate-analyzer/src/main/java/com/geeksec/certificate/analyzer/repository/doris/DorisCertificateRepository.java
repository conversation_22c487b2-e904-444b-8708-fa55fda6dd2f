package com.geeksec.certificate.analyzer.repository.doris;

import java.sql.Array;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.geeksec.certificate.analyzer.repository.CertificateRepository;
// 导入新的 Record 类
import com.geeksec.certificate.analyzer.repository.doris.DorisCertRecord.ExtensionDetails;
import com.geeksec.certificate.analyzer.repository.ConnectionProvider;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Doris具体实现的证书数据仓库。
 *
 * <AUTHOR>
 */
public class DorisCertificateRepository implements CertificateRepository {

    private static final Logger log = LoggerFactory.getLogger(DorisCertificateRepository.class);

    private final ConnectionProvider<Connection> connectionProvider;

    public DorisCertificateRepository(ConnectionProvider<Connection> connectionProvider) {
        this.connectionProvider = connectionProvider;
    }

    @Override
    public DorisCertRecord queryCertBySha1(String certSha1) {
        String sql = "SELECT * FROM dim_cert WHERE cert_sha1 = ?";
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, certSha1);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return resultSetToRecord(rs);
                }
            }
        } catch (SQLException e) {
            log.error("DorisRepository: 查询证书信息失败，certSha1: {}", certSha1, e);
        }
        return null;
    }

    @Override
    public Map<String, DorisCertRecord> batchQueryCertsBySha1(List<String> certSha1List) {
        if (certSha1List == null || certSha1List.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, DorisCertRecord> result = new HashMap<>();
        StringBuilder sqlBuilder = new StringBuilder("SELECT * FROM dim_cert WHERE cert_sha1 IN (");
        for (int i = 0; i < certSha1List.size(); i++) {
            sqlBuilder.append(i == 0 ? "?" : ",?");
        }
        sqlBuilder.append(")");

        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sqlBuilder.toString())) {
            for (int i = 0; i < certSha1List.size(); i++) {
                stmt.setString(i + 1, certSha1List.get(i));
            }
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String sha1 = rs.getString("cert_sha1");
                    result.put(sha1, resultSetToRecord(rs));
                }
            }
        } catch (SQLException e) {
            log.error("DorisRepository: 批量查询证书信息失败", e);
        }
        return result;
    }

    @Override
    public List<DorisCertRecord> queryParentCerts(String issuerMd5, String authorityKeyId) {
        List<DorisCertRecord> result = new ArrayList<>();
        String sql = "SELECT * FROM dim_cert WHERE subject_id = ? AND subject_key_identifier = ?";
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, issuerMd5);
            stmt.setString(2, authorityKeyId);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    result.add(resultSetToRecord(rs));
                }
            }
        } catch (SQLException e) {
            log.error("DorisRepository: 查询父证书失败，issuerMd5: {}, authorityKeyId: {}", issuerMd5, authorityKeyId, e);
        }
        return result;
    }

    public Set<String> findTrustedParentCertificates(String certSha1) {
        String sql = "SELECT parent_cert_id_list FROM dim_cert WHERE cert_sha1 = ? AND has(labels, 227)";
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, certSha1);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Array sqlArray = rs.getArray("parent_cert_id_list");
                    if (sqlArray != null) {
                        String[] parentCertIdArray = (String[]) sqlArray.getArray();
                        // Ensure the array is not null before converting to list
                        return parentCertIdArray != null ? new HashSet<>(Arrays.asList(parentCertIdArray)) : Collections.emptySet();
                    }
                }
            }
        } catch (SQLException e) {
            log.error("DorisRepository: 检查白名单证书失败，certSha1: {}", certSha1, e);
        }
        return Collections.emptySet();
    }

    @Override
    public boolean checkCollisionCert(String pemMd5, String asn1Sha1) {
        String sql = "SELECT cert_sha1 FROM dim_cert WHERE cert_md5 = ? AND cert_sha1 != ? LIMIT 1";
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, pemMd5);
            stmt.setString(2, asn1Sha1);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        } catch (SQLException e) {
            log.error("DorisRepository: 检查碰撞证书失败，pemMd5: {}, asn1Sha1: {}", pemMd5, asn1Sha1, e);
        }
        return false;
    }

    @Override
    public boolean checkCertExists(String certSha1) {
        String sql = "SELECT 1 FROM dim_cert WHERE cert_sha1 = ? LIMIT 1";
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, certSha1);
            try (ResultSet rs = stmt.executeQuery()) {
                return rs.next();
            }
        } catch (SQLException e) {
            log.error("DorisRepository: 检查证书存在性失败，certSha1: {}", certSha1, e);
        }
        return false;
    }

    @Override
    public boolean updateCertUserIdList(String certSha1, List<String> userIdList) {
        String sql = "UPDATE dim_cert SET user_id_list = ? WHERE cert_sha1 = ?";
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {

            if (userIdList == null || userIdList.isEmpty()) {
                Array emptyArray = connection.createArrayOf("VARCHAR", new String[] {});
                stmt.setArray(1, emptyArray);
            } else {
                Array userIdSqlArray = connection.createArrayOf("VARCHAR", userIdList.toArray(new String[0]));
                stmt.setArray(1, userIdSqlArray);
            }
            stmt.setString(2, certSha1);
            int updatedRows = stmt.executeUpdate();
            log.debug("DorisRepository: 更新证书用户ID列表，certSha1: {}, 影响行数: {}", certSha1, updatedRows);
            return updatedRows > 0;
        } catch (SQLException e) {
            log.error("DorisRepository: 更新证书用户ID列表失败，certSha1: {}", certSha1, e);
        }
        return false;
    }

    @Override
    public long getCertSignCount(String certSha1) {
        String sql = "SELECT COUNT(*) FROM dim_cert WHERE parent_cert_id = ?";
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, certSha1);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getLong(1);
                }
            }
        } catch (SQLException e) {
            log.error("DorisRepository: 获取证书签发数量失败，certSha1: {}", certSha1, e);
        }
        return 0L;
    }

    @Override
    public String findDerSha1ByForwardHashes(List<String> forwardHashes, int listLength) {
        if (forwardHashes == null || forwardHashes.isEmpty() || listLength <= 0) {
            return null;
        }
        List<String> subList = forwardHashes.subList(0, Math.min(forwardHashes.size(), listLength));
        String sql = "SELECT der_sha1 FROM dim_cert WHERE array_size(forward_chunk_hashes) = ? AND array_equals(forward_chunk_hashes, ?) LIMIT 1";
        log.debug("DorisRepository: Executing Doris query for forward_chunk_hashes: SQL='{}', Hashes='{}', Length='{}'",
                sql, subList, subList.size());
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Array forwardHashesArray = connection.createArrayOf("VARCHAR", subList.toArray(new String[0]));
            stmt.setInt(1, subList.size());
            stmt.setArray(2, forwardHashesArray);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    String derSha1 = rs.getString("der_sha1");
                    log.debug(
                            "DorisRepository: Doris query for forward_chunk_hashes successful. Hashes='{}', Found der_sha1='{}'",
                            subList, derSha1);
                    return derSha1;
                }
                log.debug("DorisRepository: Doris query for forward_chunk_hashes returned no results. Hashes='{}'",
                        subList);
            }
        } catch (SQLException e) {
            log.error("DorisRepository: Error querying Doris by forward_chunk_hashes: {}. Hashes='{}'", e.getMessage(),
                    subList, e);
        }
        return null;
    }

    @Override
    public String findDerSha1ByReverseHashes(List<String> reverseHashes, int listLength) {
        if (reverseHashes == null || reverseHashes.isEmpty() || listLength <= 0) {
            return null;
        }
        List<String> subList = reverseHashes.subList(0, Math.min(reverseHashes.size(), listLength));
        String sql = "SELECT der_sha1 FROM dim_cert WHERE array_size(reverse_chunk_hashes) = ? AND array_equals(reverse_chunk_hashes, ?) LIMIT 1";
        log.debug("DorisRepository: Executing Doris query for reverse_chunk_hashes: SQL='{}', Hashes='{}', Length='{}'",
                sql, subList, subList.size());
        try (Connection connection = connectionProvider.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {
            java.sql.Array reverseHashesArray = connection.createArrayOf("VARCHAR", subList.toArray(new String[0]));
            stmt.setInt(1, subList.size());
            stmt.setArray(2, reverseHashesArray);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    String derSha1 = rs.getString("der_sha1");
                    log.debug(
                            "DorisRepository: Doris query for reverse_chunk_hashes successful. Hashes='{}', Found der_sha1='{}'",
                            subList, derSha1);
                    return derSha1;
                }
                log.debug("DorisRepository: Doris query for reverse_chunk_hashes returned no results. Hashes='{}'",
                        subList);
            }
        } catch (SQLException e) {
            log.error("DorisRepository: Error querying Doris by reverse_chunk_hashes: {}. Hashes='{}'", e.getMessage(),
                    subList, e);
        }
        return null;
    }

    private static DorisCertRecord resultSetToRecord(ResultSet rs) throws SQLException {
        String asn1Sha1 = rs.getString("cert_sha1");
        String pemMd5 = rs.getString("cert_md5");
        String pemSha256 = rs.getString("cert_sha256");
        String asn1Md5 = rs.getString("asn1_md5");
        String asn1Sha256 = rs.getString("asn1_sha256");
        String version = rs.getString("version");
        String serialNumber = rs.getString("serial_number");
        String format = rs.getString("format");
        String issuerId = rs.getString("issuer_id");
        String subjectId = rs.getString("subject_id");
        String notBefore = rs.getString("not_before");
        String notAfter = rs.getString("not_after");
        long duration = rs.getLong("duration");
        String importTime = rs.getString("import_time");
        String publicKey = rs.getString("public_key");
        String publicKeyAlgorithm = rs.getString("public_key_algorithm");
        String publicKeyLength = rs.getString("public_key_length");
        String pubAlgOid = rs.getString("public_key_alg_oid");
        String signatureAlgorithm = rs.getString("signature_algorithm");
        String sigAlgName = rs.getString("signature_alg_name");
        String sigAlgOid = rs.getString("signature_alg_oid");
        String certSource = rs.getInt("cert_source") == 0 ? "System" : "User";
        boolean isWellFormed = rs.getBoolean("is_parsed_successfully");
        boolean isCorrupted = rs.getBoolean("is_corrupted");
        String parentCertId = rs.getString("parent_cert_id");

        List<String> parentCertIdList = Collections.emptyList();
        Array parentCertIdSqlArray = rs.getArray("parent_cert_id_list");
        if (parentCertIdSqlArray != null) {
            String[] parentCertIdArray = (String[]) parentCertIdSqlArray.getArray();
            if (parentCertIdArray != null) {
                parentCertIdList = Arrays.asList(parentCertIdArray);
            }
        }

        List<Integer> labels = Collections.emptyList();
        Array labelsSqlArray = rs.getArray("labels");
        if (labelsSqlArray != null) {
            Object labelsJavaArray = labelsSqlArray.getArray();
            if (labelsJavaArray instanceof Integer[]) {
                labels = Arrays.asList((Integer[]) labelsJavaArray);
            } else if (labelsJavaArray instanceof int[]) {
                labels = Arrays.stream((int[]) labelsJavaArray).boxed().collect(Collectors.toList());
            } else if (labelsJavaArray instanceof Object[]) {
                List<Integer> tempList = new ArrayList<>();
                for (Object obj : (Object[]) labelsJavaArray) {
                    if (obj instanceof Number) {
                        tempList.add(((Number) obj).intValue());
                    }
                }
                labels = tempList;
                if (labels.isEmpty() && ((Object[]) labelsJavaArray).length > 0
                        && !(labelsJavaArray instanceof Integer[] || labelsJavaArray instanceof int[])) {
                    log.warn(
                            "DorisRepository: Labels array was not null but could not be fully converted to List<Integer>. Type: {}",
                            labelsJavaArray.getClass().getName());
                }
            } else if (labelsJavaArray == null) {
                // labels remains emptyList
            } else {
                log.warn("DorisRepository: Unknown array type for Labels: {}. Defaulting to empty list.",
                        labelsJavaArray.getClass().getName());
            }
        }

        List<String> userIdList = Collections.emptyList();
        Array userIdSqlArray = rs.getArray("user_id_list");
        if (userIdSqlArray != null) {
            String[] userIdArray = (String[]) userIdSqlArray.getArray();
            if (userIdArray != null) {
                userIdList = Arrays.asList(userIdArray);
            }
        }

        ExtensionDetails extensionDetails = new ExtensionDetails(
                rs.getString("basic_constraints"),
                rs.getString("authority_key_identifier"),
                rs.getString("subject_key_identifier"));

        return new DorisCertRecord(
                asn1Sha1, pemMd5, pemSha256, asn1Md5, asn1Sha256, version, serialNumber, format,
                issuerId, subjectId, notBefore, notAfter, duration, importTime, publicKey,
                publicKeyAlgorithm, publicKeyLength, pubAlgOid, signatureAlgorithm, sigAlgName,
                sigAlgOid, certSource, isWellFormed, isCorrupted, parentCertId,
                parentCertIdList, labels, userIdList, extensionDetails);
    }

    private static List<String> parseArrayString(String arrayStr) {
        if (arrayStr == null || arrayStr.isEmpty() || "[]".equals(arrayStr) || "null".equalsIgnoreCase(arrayStr)) {
            return Collections.emptyList();
        }
        String cleanedStr = arrayStr.replaceAll("[\\[\\]\"']", "");
        if (cleanedStr.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(cleanedStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    private static List<Integer> parseIntArrayString(String arrayStr) {
        if (arrayStr == null || arrayStr.isEmpty() || "[]".equals(arrayStr) || "null".equalsIgnoreCase(arrayStr)) {
            return Collections.emptyList();
        }
        String cleanedStr = arrayStr.replaceAll("[\\[\\]\s]", "");
        if (cleanedStr.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            return Arrays.stream(cleanedStr.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            log.warn("DorisRepository: Failed to parse integer array string: '{}'", arrayStr, e);
            return Collections.emptyList();
        }
    }
}

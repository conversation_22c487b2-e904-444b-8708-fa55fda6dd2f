package com.geeksec.certificate.analyzer.operator.analysis;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import com.geeksec.certificate.analyzer.model.alarm.AlarmEvent;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.flink.common.knowledge.KnowledgeBaseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书告警事件转换器
 * 将证书分析结果转换为告警事件，发送到统一告警处理系统
 *
 * <AUTHOR>
 * @Date 2022/12/30
 * @Modified hufengkai - 重构为生成 AlarmEvent 并发送到 alarm-processor
 * @Date 2025/07/16
 */
@Slf4j
public class AlarmTransformer extends RichMapFunction<X509Certificate, AlarmEvent> {

    /**
     * 知识库客户端
     */
    private KnowledgeBaseClient knowledgeBaseClient;

    @Override
    public void open(Configuration parameters) throws Exception {
        log.info("证书告警事件转换器初始化开始");

        // 初始化知识库客户端
        String knowledgeBaseUrl = parameters.getString("knowledge.base.url", "http://knowledge-base:8080/knowledge-base");
        knowledgeBaseClient = new KnowledgeBaseClient(knowledgeBaseUrl);

        log.info("证书告警事件转换器初始化完成");
        super.open(parameters);
    }

    @Override
    public AlarmEvent map(X509Certificate certificate) throws Exception {
        if (certificate == null) {
            return null;
        }

        // 检查证书是否有需要告警的标签
        Set<CertificateLabel> labels = certificate.getLabels();
        if (labels == null || labels.isEmpty()) {
            return null;
        }

        // 根据标签生成告警事件
        return generateAlarmEvent(certificate, labels);
    }

    /**
     * 根据证书和标签生成告警事件
     *
     * @param certificate 证书对象
     * @param labels 证书标签
     * @return 告警事件
     */
    private AlarmEvent generateAlarmEvent(X509Certificate certificate, Set<CertificateLabel> labels) {
        AlarmEvent alarmEvent = new AlarmEvent();

        // 基础信息
        alarmEvent.setEventId(UUID.randomUUID().toString());
        alarmEvent.setSourceModule("certificate-analyzer");
        alarmEvent.setTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // 根据标签确定告警类型和级别
        String alarmType = determineAlarmType(labels);
        String alarmLevel = determineAlarmLevel(labels);

        alarmEvent.setAlarmType(alarmType);
        alarmEvent.setAlarmName(generateAlarmName(alarmType, certificate));
        alarmEvent.setAlarmLevel(alarmLevel);
        alarmEvent.setThreatType(determineThreatType(labels));

        // 网络信息（证书分析器暂不提供会话信息）
        alarmEvent.setProtocol("HTTPS");

        // 检测器信息
        alarmEvent.setDetectorType("CertificateAnalyzer");
        alarmEvent.setConfidence(calculateConfidence(labels));

        // 描述和原因
        alarmEvent.setDescription(generateDescription(certificate, labels));
        alarmEvent.setDetectionReasons(generateDetectionReasons(labels));

        // 标签
        List<String> labelStrings = new ArrayList<>();
        for (CertificateLabel label : labels) {
            labelStrings.add(label.name());
        }
        alarmEvent.setLabels(labelStrings);

        // 扩展属性
        Map<String, Object> extendedProperties = new HashMap<>();
        extendedProperties.put("cert_hash", certificate.getDerSha1());
        extendedProperties.put("subject_cn", certificate.getCommonName());
        extendedProperties.put("issuer_cn", certificate.getIssuer() != null ? certificate.getIssuer().get("CN") : null);
        extendedProperties.put("domains", certificate.getSubjectAltNames());
        extendedProperties.put("not_before", certificate.getNotBefore());
        extendedProperties.put("not_after", certificate.getNotAfter());
        alarmEvent.setExtendedProperties(extendedProperties);

        // 证书信息
        if (certificate != null) {
            AlarmEvent.CertificateInfo certInfo = new AlarmEvent.CertificateInfo();
            certInfo.setCertHash(certificate.getDerSha1());
            certInfo.setSubjectCn(certificate.getCommonName());
            certInfo.setIssuerCn(certificate.getIssuer() != null ? certificate.getIssuer().get("CN") : null);
            certInfo.setDomains(certificate.getSubjectAltNames());
            certInfo.setNotBefore(certificate.getNotBefore() != null ? certificate.getNotBefore().toString() : null);
            certInfo.setNotAfter(certificate.getNotAfter() != null ? certificate.getNotAfter().toString() : null);
            alarmEvent.setCertificateInfo(certInfo);
        }

        log.debug("生成证书告警事件: 类型={}, 证书={}", alarmType, certificate.getDerSha1());
        return alarmEvent;
    }

    /**
     * 根据标签确定告警类型
     */
    private String determineAlarmType(Set<CertificateLabel> labels) {
        if (labels.contains(CertificateLabel.APT_RELATED_CERT) ||
            labels.contains(CertificateLabel.APT28_CERT) ||
            labels.contains(CertificateLabel.APT29_CERT) ||
            labels.contains(CertificateLabel.APT_PATCHWORK)) {
            return "APT证书碰撞";
        } else if (labels.contains(CertificateLabel.MALICIOUS_DOMAIN_CERT)) {
            return "恶意域名关联证书";
        } else if (labels.contains(CertificateLabel.CC_CERT) ||
                   labels.contains(CertificateLabel.REMOTE_CONTROL_CERT)) {
            return "C2证书请求";
        } else if (labels.contains(CertificateLabel.MINING_CERT)) {
            return "非法挖矿请求";
        } else if (labels.contains(CertificateLabel.TOR) ||
                   labels.contains(CertificateLabel.TOR_V2_CERT) ||
                   labels.contains(CertificateLabel.TOR_V3_CERT)) {
            return "TOR网络访问";
        } else if (labels.contains(CertificateLabel.NETWORK_PENETRATION_CERT)) {
            return "翻墙行为";
        } else if (labels.contains(CertificateLabel.THREAT)) {
            return "威胁证书碰撞";
        } else if (labels.contains(CertificateLabel.IOC_IP_CERT)) {
            return "失陷IP关联证书";
        } else if (labels.contains(CertificateLabel.IOC_DOMAIN_CERT)) {
            return "IOC域名关联证书";
        }
        return "证书威胁检测";
    }

    /**
     * 根据标签确定告警级别
     */
    private String determineAlarmLevel(Set<CertificateLabel> labels) {
        if (labels.contains(CertificateLabel.APT_RELATED_CERT) ||
            labels.contains(CertificateLabel.APT28_CERT) ||
            labels.contains(CertificateLabel.APT29_CERT) ||
            labels.contains(CertificateLabel.CC_CERT)) {
            return "CRITICAL";
        } else if (labels.contains(CertificateLabel.MALICIOUS_DOMAIN_CERT) ||
                   labels.contains(CertificateLabel.THREAT) ||
                   labels.contains(CertificateLabel.MALWARE)) {
            return "HIGH";
        } else if (labels.contains(CertificateLabel.MINING_CERT) ||
                   labels.contains(CertificateLabel.TOR) ||
                   labels.contains(CertificateLabel.TOR_V2_CERT) ||
                   labels.contains(CertificateLabel.TOR_V3_CERT)) {
            return "MEDIUM";
        }
        return "LOW";
    }

    /**
     * 根据标签确定威胁类型
     */
    private String determineThreatType(Set<CertificateLabel> labels) {
        if (labels.contains(CertificateLabel.APT_RELATED_CERT) ||
            labels.contains(CertificateLabel.APT28_CERT) ||
            labels.contains(CertificateLabel.APT29_CERT)) {
            return "APT攻击";
        } else if (labels.contains(CertificateLabel.CC_CERT) ||
                   labels.contains(CertificateLabel.MALWARE)) {
            return "恶意软件";
        } else if (labels.contains(CertificateLabel.MINING_CERT)) {
            return "挖矿木马";
        } else if (labels.contains(CertificateLabel.MALICIOUS_DOMAIN_CERT) ||
                   labels.contains(CertificateLabel.NETWORK_PENETRATION_CERT)) {
            return "网络渗透";
        }
        return "可疑活动";
    }

    /**
     * 生成告警名称
     */
    private String generateAlarmName(String alarmType, X509Certificate certificate) {
        return String.format("%s - %s", alarmType, certificate.getCommonName());
    }

    /**
     * 计算置信度
     */
    private double calculateConfidence(Set<CertificateLabel> labels) {
        // 根据标签数量和类型计算置信度
        double confidence = 0.5; // 基础置信度

        if (labels.contains(CertificateLabel.APT_RELATED_CERT) ||
            labels.contains(CertificateLabel.APT28_CERT) ||
            labels.contains(CertificateLabel.APT29_CERT)) {
            confidence += 0.4;
        }
        if (labels.contains(CertificateLabel.MALICIOUS_DOMAIN_CERT)) {
            confidence += 0.3;
        }
        if (labels.contains(CertificateLabel.CC_CERT)) {
            confidence += 0.3;
        }

        return Math.min(confidence, 1.0);
    }

    /**
     * 生成描述
     */
    private String generateDescription(X509Certificate certificate, Set<CertificateLabel> labels) {
        StringBuilder desc = new StringBuilder();
        desc.append("检测到证书 ").append(certificate.getCommonName()).append(" 存在威胁行为：");

        for (CertificateLabel label : labels) {
            desc.append(" ").append(label.getDisplayName()).append(";");
        }

        return desc.toString();
    }

    /**
     * 生成检测原因
     */
    private List<String> generateDetectionReasons(Set<CertificateLabel> labels) {
        List<String> reasons = new ArrayList<>();

        for (CertificateLabel label : labels) {
            reasons.add(label.getDisplayName());
        }

        return reasons;
    }

    @Override
    public void close() throws Exception {
        // 关闭知识库客户端
        if (knowledgeBaseClient != null) {
            knowledgeBaseClient.close();
        }
        super.close();
    }
}

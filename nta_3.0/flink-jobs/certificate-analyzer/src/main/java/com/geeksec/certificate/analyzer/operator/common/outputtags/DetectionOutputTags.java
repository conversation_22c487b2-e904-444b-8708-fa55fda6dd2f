package com.geeksec.certificate.analyzer.operator.common.outputtags;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * 证书威胁检测阶段输出标签
 * 用于证书威胁检测流程的流控制
 *
 * <AUTHOR>
 */
public final class DetectionOutputTags {
    private DetectionOutputTags() {
        // 防止实例化
    }

    /** 需要僵尸网络检测的证书标签 */
    public static final OutputTag<X509Certificate> NEED_BOTNET_DETECTION =
        new OutputTag<>("need-botnet-detection", TypeInformation.of(X509Certificate.class));

    /** 需要APT检测的证书标签 */
    public static final OutputTag<X509Certificate> NEED_APT_DETECTION =
        new OutputTag<>("need-apt-detection", TypeInformation.of(X509Certificate.class));

    /** 检测完成的证书标签 */
    public static final OutputTag<X509Certificate> DETECTION_COMPLETED =
        new OutputTag<>("detection-completed", TypeInformation.of(X509Certificate.class));
}

package com.geeksec.certificate.analyzer.sink.nebula;

import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * 证书Nebula去重输出标签
 * 用于证书Nebula图数据去重流程的流控制
 *
 * <AUTHOR>
 */
public final class CertNebulaDedupOutputTag {
    private CertNebulaDedupOutputTag() {
        // 防止实例化
    }

    /** 去重的Nebula证书数据标签 */
    public static final OutputTag<Row> Dedup_Nebula_Cert =
        new OutputTag<>("Dedup_Nebula_Cert", TypeInformation.of(Row.class));

    /** 未去重的Nebula证书数据标签 */
    public static final OutputTag<Row> Not_Dedup_Nebula_Cert =
        new OutputTag<>("Not_Dedup_Nebula_Cert", TypeInformation.of(Row.class));

    /** 未去重的Nebula颁发者数据标签 */
    public static final OutputTag<Row> Not_Dedup_Nebula_Issuer =
        new OutputTag<>("Not_Dedup_Nebula_Issuer", TypeInformation.of(Row.class));

    /** 未去重的Nebula主题数据标签 */
    public static final OutputTag<Row> Not_Dedup_Nebula_Subject =
        new OutputTag<>("Not_Dedup_Nebula_Subject", TypeInformation.of(Row.class));

    /** 未去重的Nebula URL数据标签 */
    public static final OutputTag<Row> Not_Dedup_Nebula_URL =
        new OutputTag<>("Not_Dedup_Nebula_URL", TypeInformation.of(Row.class));

    /** 未去重的Nebula组织数据标签 */
    public static final OutputTag<Row> Not_Dedup_Nebula_ORG =
        new OutputTag<>("Not_Dedup_Nebula_ORG", TypeInformation.of(Row.class));
}
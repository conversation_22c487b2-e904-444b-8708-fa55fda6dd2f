package com.geeksec.certificate.analyzer.operator.analysis.threat;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import lombok.extern.slf4j.Slf4j;

/**
 * 基础证书检测器抽象类
 * 提供通用的检测功能和错误处理
 */
@Slf4j
public abstract class BaseCertificateDetector implements CertificateDetector {
    protected final String name;
    
    protected BaseCertificateDetector(String name) {
        this.name = name;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public void detect(X509Certificate certificate) {
        if (certificate == null) {
            return;
        }
        
        try {
            doDetect(certificate);
        } catch (Exception e) {
            handleDetectionError(e, certificate);
        }
    }
    
    /**
     * 具体的检测逻辑由子类实现
     */
    protected abstract void doDetect(X509Certificate certificate);
    
    /**
     * 处理检测过程中的错误
     */
    protected void handleDetectionError(Exception e, X509Certificate certificate) {
        log.error("Error in detector {} for certificate {}: {}", 
            getName(), 
            certificate.getCommonName(), 
            e.getMessage(), 
            e);
    }
}

package com.geeksec.certificate.analyzer.sink.nebula;

import java.util.Properties;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.sink.nebula.CertNebulaDedupOutputTag;
import com.geeksec.flink.common.database.redis.RedisConnectionManager;

import redis.clients.jedis.Jedis;

/**
 * <AUTHOR>
 * @Date 2023/10/12
 */

public class CertNebulaDedup extends ProcessFunction<Row, Row> {

    private static final Logger log = LoggerFactory.getLogger(CertNebulaDedup.class);

    // private static Properties properties = FileUtil.getProperties("/config.properties");
    // public static final String NEBULA_GRAPH_ADDR = properties.getProperty("nebula.graph.addr");
    // public static final String NEBULA_META_ADDR = properties.getProperty("nebula.meta.addr");
    // public static final String NEBULA_GRAPH_SPACE = properties.getProperty("nebula.space.name");


    @Override
    public void open(Configuration parameters) throws Exception {
        // Redis连接管理器已在应用启动时初始化
        super.open(parameters);
        log.info("CertNebulaDedup初始化完成");
    }

    @Override
    public void close() throws Exception {
        // Redis连接池由RedisConnectionManager统一管理
        super.close();
        log.info("CertNebulaDedup关闭完成");
    }

    @Override
    public void processElement(Row row, ProcessFunction<Row, Row>.Context context, Collector<Row> collector) throws Exception {
        String dedupType = (String) row.getField(0);
        X509Certificate cert = (X509Certificate) row.getField(1);
        Jedis jedis = null;
        String key = "";

        try {
            jedis = RedisConnectionManager.getJedis();
            jedis.select(11);

            switch (dedupType){
                // 分类设置sql语句
                case "cert_id":
                    String certId = cert.getCertId();
                    key = certId;
                    break;
                case "issuer_md5":
                    String issuerMd5 = cert.getIssuerMD5();
                    key = issuerMd5;
                    break;
                case "subject_md5":
                    String subjectMd5 = cert.getSubjectMD5();
                    key = subjectMd5;
                    break;
                case "ORG":
                    String org = cert.getOrganization();
                    key = org;
                    break;
                case "URL":
                    String url = (String) row.getField(2);
                    key = url;
                    break;
                default:
                    break;
            }

            // 如果是证书需要更新，如果是实体，不需要更新
            if(jedis.exists(key)){
                switch (dedupType){
                    case "cert_id":
                        context.output(CertNebulaDedupOutputTag.Dedup_Nebula_Cert,row);
                        break;
                    case "issuer_md5":
                        log.info("issuer 重复，无需更新");
                        break;
                    case "subject_md5":
                        log.info("subject 重复，无需更新");
                        break;
                    case "URL":
                        log.info("URL 重复，无需更新");
                        break;
                    case "ORG":
                        log.info("ORG 重复，无需更新");
                        break;
                    default:
                        break;
                }
            }else {
                switch (dedupType){
                    case "cert_id":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutputTag.Not_Dedup_Nebula_Cert,row);
                        break;
                    case "issuer_md5":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutputTag.Not_Dedup_Nebula_Issuer,row);
                        break;
                    case "subject_md5":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutputTag.Not_Dedup_Nebula_Subject,row);
                        break;
                    case "URL":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutputTag.Not_Dedup_Nebula_URL,row);
                        break;
                    case "ORG":
                        jedis.setex(key,86400,"1");
                        context.output(CertNebulaDedupOutputTag.Not_Dedup_Nebula_ORG,row);
                        break;
                    default:
                        break;
                }
            }
        }catch (Exception e){
            log.error("redis连接获取失败");
        }finally {
            if (jedis != null){
                jedis.close();
            }
        }
    }
}

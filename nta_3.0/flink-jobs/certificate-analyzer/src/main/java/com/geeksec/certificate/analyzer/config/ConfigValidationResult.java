package com.geeksec.certificate.analyzer.config;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 配置验证结果
 * 
 * <AUTHOR>
 */
@Data
public class ConfigValidationResult {
    
    /** 是否验证通过 */
    private boolean valid = true;
    
    /** 错误信息列表 */
    private List<String> errorMessages = new ArrayList<>();
    
    /** 警告信息列表 */
    private List<String> warningMessages = new ArrayList<>();
    
    /**
     * 添加错误信息
     * 
     * @param message 错误信息
     */
    public void addError(String message) {
        this.valid = false;
        this.errorMessages.add(message);
    }
    
    /**
     * 添加警告信息
     * 
     * @param message 警告信息
     */
    public void addWarning(String message) {
        this.warningMessages.add(message);
    }
    
    /**
     * 获取所有错误信息的字符串表示
     * 
     * @return 错误信息字符串
     */
    public String getErrorSummary() {
        if (errorMessages.isEmpty()) {
            return "无错误";
        }
        return String.join("; ", errorMessages);
    }
    
    /**
     * 获取所有警告信息的字符串表示
     * 
     * @return 警告信息字符串
     */
    public String getWarningSummary() {
        if (warningMessages.isEmpty()) {
            return "无警告";
        }
        return String.join("; ", warningMessages);
    }
    
    /**
     * 获取验证结果的完整摘要
     * 
     * @return 验证结果摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("配置验证结果: ").append(valid ? "通过" : "失败");
        
        if (!errorMessages.isEmpty()) {
            summary.append("\n错误: ").append(getErrorSummary());
        }
        
        if (!warningMessages.isEmpty()) {
            summary.append("\n警告: ").append(getWarningSummary());
        }
        
        return summary.toString();
    }
    
    /**
     * 检查是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return !errorMessages.isEmpty();
    }
    
    /**
     * 检查是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return !warningMessages.isEmpty();
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errorMessages.size();
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warningMessages.size();
    }
}

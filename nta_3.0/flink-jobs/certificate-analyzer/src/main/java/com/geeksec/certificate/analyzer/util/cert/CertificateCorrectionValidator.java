package com.geeksec.certificate.analyzer.util.cert;

import org.apache.commons.text.similarity.LevenshteinDistance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 证书纠错验证器
 * 用于验证证书纠错结果的有效性
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public final class CertificateCorrectionValidator {
    private static final Logger log = LoggerFactory.getLogger(CertificateCorrectionValidator.class);
    private static final LevenshteinDistance LEVENSHTEIN_DISTANCE = new LevenshteinDistance();
    
    // 不同错误类型的阈值配置
    private static final int DEFAULT_THRESHOLD = 10;
    private static final int BYTE_NUM_POSITIVE_THRESHOLD = 5;
    private static final int BYTE_NUM_NEGATIVE_THRESHOLD = 5;
    private static final int CHUNK_HASH_THRESHOLD = 8;
    
    private CertificateCorrectionValidator() {
        // 工具类，禁止实例化
    }
    
    /**
     * 检查纠错结果的编辑距离是否在可接受范围内
     *
     * @param correctedBytes 纠错后的证书字节
     * @param originalBytes 原始证书字节
     * @param errorType 错误类型
     * @return 是否通过验证
     */
    public static boolean checkDistance(byte[] correctedBytes, byte[] originalBytes, String errorType) {
        if (correctedBytes == null || originalBytes == null) {
            log.warn("证书字节数据为空，验证失败");
            return false;
        }
        
        try {
            // 计算编辑距离
            int distance = calculateByteDistance(correctedBytes, originalBytes);
            int threshold = getThresholdForErrorType(errorType);
            
            boolean isValid = distance <= threshold;
            log.debug("纠错验证: 错误类型={}, 编辑距离={}, 阈值={}, 结果={}",
                    errorType, distance, threshold, isValid ? "通过" : "失败");
            
            return isValid;
        } catch (Exception e) {
            log.error("计算编辑距离时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 计算两个字节数组的编辑距离
     *
     * @param bytes1 字节数组1
     * @param bytes2 字节数组2
     * @return 编辑距离
     */
    private static int calculateByteDistance(byte[] bytes1, byte[] bytes2) {
        // 对于大型字节数组，直接使用字节比较可能效率较低
        // 这里使用简化的方法：转换为十六进制字符串后计算编辑距离
        String hex1 = bytesToHex(bytes1);
        String hex2 = bytesToHex(bytes2);
        
        return LEVENSHTEIN_DISTANCE.apply(hex1, hex2);
    }
    
    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
    
    /**
     * 根据错误类型获取对应的阈值
     *
     * @param errorType 错误类型
     * @return 阈值
     */
    private static int getThresholdForErrorType(String errorType) {
        if (errorType == null) {
            return DEFAULT_THRESHOLD;
        }
        
        switch (errorType.toLowerCase()) {
            case "bytenumpositive":
                return BYTE_NUM_POSITIVE_THRESHOLD;
            case "bytenumnegative":
                return BYTE_NUM_NEGATIVE_THRESHOLD;
            case "chunkhash":
                return CHUNK_HASH_THRESHOLD;
            default:
                return DEFAULT_THRESHOLD;
        }
    }
    
    /**
     * 验证纠错结果的完整性
     *
     * @param original 原始字节
     * @param corrected 纠错后字节
     * @param errorType 错误类型
     * @return 是否有效
     */
    public static boolean validateCorrectionResult(byte[] original, byte[] corrected, String errorType) {
        if (original == null || corrected == null) {
            return false;
        }
        
        int distance = calculateByteDistance(original, corrected);
        int threshold = getThresholdForErrorType(errorType);
        
        boolean isValid = distance <= threshold;
        log.debug("纠错验证: 错误类型={}, 编辑距离={}, 阈值={}, 结果={}",
                errorType, distance, threshold, isValid ? "通过" : "失败");
        
        return isValid;
    }
}
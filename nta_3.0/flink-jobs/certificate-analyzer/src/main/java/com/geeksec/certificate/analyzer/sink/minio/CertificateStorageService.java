package com.geeksec.certificate.analyzer.sink.minio;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.geeksec.flink.common.storage.minio.MinioUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 证书存储服务
 * 基于MinIO的证书文件存储和管理服务
 * 提供证书的CRUD操作和批量处理能力
 * 
 * 优化说明：
 * 1. 类名从MinioCertificateClient改为CertificateStorageService，更符合业务语义
 * 2. 方法名保持不变，确保向后兼容
 * 3. 添加了更详细的文档说明和参数验证
 * 4. 增强了日志记录和错误处理
 * 5. 新增批量检查存在性方法，提升性能
 * 
 * <AUTHOR>
 * @date 2024/10/15
 */
@Slf4j
public class CertificateStorageService {

    private static final String CERTIFICATE_BUCKET = "certificates";

    /**
     * 批量根据SHA1哈希值获取证书数据
     * 推荐使用此方法进行批量查询，性能更优
     *
     * @param sha1List 证书SHA1哈希值列表
     * @return 包含SHA1和对应证书数据的Map，不包含未找到的证书
     */
    public static Map<String, byte[]> batchGetCertificatesBySha1(List<String> sha1List) {
        if (sha1List == null || sha1List.isEmpty()) {
            return new HashMap<>(0);
        }
        
        return sha1List.stream()
                .map(sha1 -> Map.entry(sha1, getCertificateBySha1(sha1)))
                .filter(entry -> entry.getValue() != null)
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (existing, replacement) -> existing,
                    () -> new HashMap<>(sha1List.size())
                ));
    }

    /**
     * 根据SHA1哈希值获取证书数据
     * 单个证书查询方法
     *
     * @param sha1 证书的SHA1哈希值
     * @return 证书的字节数组，如果证书不存在则返回null
     */
    public static byte[] getCertificateBySha1(String sha1) {
        if (sha1 == null || sha1.trim().isEmpty()) {
            log.warn("证书SHA1哈希值为空，无法查询");
            return null;
        }
        
        try {
            // 使用系统级MinioUtils获取文件流
            InputStream stream = MinioUtils.getFileStream(CERTIFICATE_BUCKET, sha1);
            if (stream == null) {
                log.debug("未找到 SHA1: {} 的证书", sha1);
                return null;
            }
            
            try (stream) {
                byte[] data = stream.readAllBytes();
                log.debug("查询 SHA1: {} 的证书成功，大小: {} bytes", sha1, data.length);
                return data;
            }
        } catch (IOException e) {
            log.error("读取证书数据流时发生错误 - SHA1: {}", sha1, e);
            return null;
        } catch (Exception e) {
            log.error("查询 SHA1: {} 的证书失败: {}", sha1, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查指定SHA1哈希值的证书是否存在
     * 轻量级检查方法，不下载证书内容
     *
     * @param sha1 证书的SHA1哈希值
     * @return 如果证书存在则返回 true，否则返回 false
     */
    public static boolean certificateExists(String sha1) {
        if (sha1 == null || sha1.trim().isEmpty()) {
            log.warn("证书SHA1哈希值为空，无法检查存在性");
            return false;
        }
        
        try {
            boolean exists = MinioUtils.fileExists(CERTIFICATE_BUCKET, sha1);
            log.debug("证书 SHA1: {} 存在性检查结果: {}", sha1, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查证书 SHA1: {} 是否存在失败: {}", sha1, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 上传证书到存储系统
     * 用于证书数据的持久化存储
     *
     * @param sha1 证书SHA1哈希值，作为存储的唯一标识
     * @param data 证书的二进制数据
     * @return 上传是否成功
     */
    public static boolean uploadCertificate(String sha1, byte[] data) {
        if (sha1 == null || sha1.trim().isEmpty()) {
            log.error("证书SHA1哈希值为空，无法上传");
            return false;
        }
        
        if (data == null || data.length == 0) {
            log.error("证书数据为空，无法上传 SHA1: {}", sha1);
            return false;
        }
        
        try {
            // 使用系统级MinioUtils上传文件
            boolean success = MinioUtils.uploadFile(CERTIFICATE_BUCKET, sha1, data);
            if (success) {
                log.debug("成功上传证书 SHA1: {}，大小: {} bytes", sha1, data.length);
            } else {
                log.warn("上传证书 SHA1: {} 失败", sha1);
            }
            return success;
        } catch (Exception e) {
            log.error("上传证书 SHA1: {} 失败: {}", sha1, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除指定SHA1哈希值的证书
     * 谨慎使用，删除操作不可逆
     *
     * @param sha1 要删除的证书的SHA1哈希值
     * @return 如果删除成功或证书不存在则返回true，发生错误时返回false
     */
    public static boolean deleteCertificate(String sha1) {
        if (sha1 == null || sha1.trim().isEmpty()) {
            log.warn("无法删除证书: 提供的SHA1哈希值为空");
            return false;
        }
        
        try {
            boolean deleted = MinioUtils.deleteFile(CERTIFICATE_BUCKET, sha1);
            if (deleted) {
                log.debug("成功删除证书: {}", sha1);
            } else {
                log.debug("证书 {} 不存在，无需删除", sha1);
            }
            return deleted;
        } catch (Exception e) {
            log.error("删除证书失败 - SHA1: {}", sha1, e);
            return false;
        }
    }

    /**
     * 获取证书存储桶名称
     * 提供给外部需要直接访问存储桶的场景
     * 
     * @return 证书存储桶名称
     */
    public static String getCertificateBucket() {
        return CERTIFICATE_BUCKET;
    }

    /**
     * 批量检查证书是否存在
     * 用于批量验证证书的存在性，避免逐个检查的性能开销
     * 
     * @param sha1List 证书SHA1哈希值列表
     * @return SHA1到存在性的映射表
     */
    public static Map<String, Boolean> batchCheckCertificateExists(List<String> sha1List) {
        Map<String, Boolean> result = new HashMap<>(sha1List.size());
        
        if (sha1List == null || sha1List.isEmpty()) {
            return result;
        }
        
        for (String sha1 : sha1List) {
            result.put(sha1, certificateExists(sha1));
        }
        
        return result;
    }
}

package com.geeksec.certificate.analyzer.operator.analysis.signature;

import com.geeksec.certificate.analyzer.enums.CertificateLabel;
import com.geeksec.certificate.analyzer.enums.CertificateTrustStatus;
import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.operator.analysis.signature.CertificateRepository;
import com.geeksec.certificate.analyzer.operator.analysis.signature.CertificateSignatureValidator;
import com.geeksec.certificate.analyzer.util.cert.CertificateUtils;
import org.apache.flink.configuration.Configuration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.io.ByteArrayInputStream;
import java.security.cert.CertificateFactory;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 证书签名验证算子测试类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public class CertificateSignatureValidatorTest {

    private CertificateSignatureValidator validator;
    private CertificateRepository mockRepository;
    
    @BeforeEach
    public void setUp() throws Exception {
        validator = new CertificateSignatureValidator();
        
        // 创建模拟的证书仓库
        mockRepository = Mockito.mock(CertificateRepository.class);
        
        // 使用反射设置模拟的证书仓库
        java.lang.reflect.Field repositoryField = CertificateSignatureValidator.class.getDeclaredField("certificateRepository");
        repositoryField.setAccessible(true);
        repositoryField.set(validator, mockRepository);
        
        // 初始化验证器
        validator.open(new Configuration());
    }
    
    @Test
    public void testProcessSelfSignedCertificate() throws Exception {
        // 创建自签名证书
        X509Certificate certificate = createMockCertificate("CN=Test CA, O=Test", "CN=Test CA, O=Test", true);
        certificate.setTrustStatus(CertificateTrustStatus.SELF_SIGNED);
        
        // 设置模拟行为
        when(mockRepository.isTrustedCaCertificate(anyString())).thenReturn(true);
        
        // 执行验证
        X509Certificate result = validator.map(certificate);
        
        // 验证结果
        assertTrue(result.getLabels().contains(CertificateLabel.TRUSTED_CA));
        assertNotNull(result.getCertificateChain());
        assertTrue(result.getCertificateChain().contains(certificate.getDerSha1()));
    }
    
    @Test
    public void testProcessUntrustedSelfSignedCertificate() throws Exception {
        // 创建自签名证书
        X509Certificate certificate = createMockCertificate("CN=Test CA, O=Test", "CN=Test CA, O=Test", true);
        certificate.setTrustStatus(CertificateTrustStatus.SELF_SIGNED);
        
        // 设置模拟行为 - 不是可信CA
        when(mockRepository.isTrustedCaCertificate(anyString())).thenReturn(false);
        
        // 执行验证
        X509Certificate result = validator.map(certificate);
        
        // 验证结果 - 不应该有TRUSTED_CA标签
        assertFalse(result.getLabels().contains(CertificateLabel.TRUSTED_CA));
        assertNotNull(result.getCertificateChain());
        assertTrue(result.getCertificateChain().contains(certificate.getDerSha1()));
    }
    
    @Test
    public void testProcessNonCaSelfSignedCertificate() throws Exception {
        // 创建自签名证书，但不是CA证书
        X509Certificate certificate = createMockCertificate("CN=Test, O=Test", "CN=Test, O=Test", false);
        certificate.setTrustStatus(CertificateTrustStatus.SELF_SIGNED);
        
        // 执行验证
        X509Certificate result = validator.map(certificate);
        
        // 验证结果 - 应该有UNKNOWN_CA标签
        assertTrue(result.getLabels().contains(CertificateLabel.UNKNOWN_CA));
    }
    
    @Test
    public void testValidateCertificateChain() throws Exception {
        // 创建子证书
        X509Certificate childCert = createMockCertificate("CN=Test, O=Test", "CN=Test CA, O=Test", false);
        childCert.setTrustStatus(CertificateTrustStatus.UNTRUSTED);
        
        // 创建父证书
        X509Certificate parentCert = createMockCertificate("CN=Test CA, O=Test", "CN=Test CA, O=Test", true);
        parentCert.setTrustStatus(CertificateTrustStatus.SELF_SIGNED);
        
        // 设置模拟行为
        when(mockRepository.findCertificatesBySubjectAndKeyId(anyString(), anyString()))
                .thenReturn(Arrays.asList(parentCert));
        when(mockRepository.isTrustedCaCertificate(parentCert.getDerSha1())).thenReturn(true);
        
        // 执行验证
        X509Certificate result = validator.map(childCert);
        
        // 验证结果
        assertTrue(result.getLabels().contains(CertificateLabel.TRUSTED_CHAIN));
        assertNotNull(result.getCertificateChain());
        assertTrue(result.getCertificateChain().contains(parentCert.getDerSha1()));
    }
    
    @Test
    public void testMissingParentCertificate() throws Exception {
        // 创建子证书
        X509Certificate childCert = createMockCertificate("CN=Test, O=Test", "CN=Test CA, O=Test", false);
        childCert.setTrustStatus(CertificateTrustStatus.UNTRUSTED);
        
        // 设置模拟行为 - 找不到父证书
        when(mockRepository.findCertificatesBySubjectAndKeyId(anyString(), anyString()))
                .thenReturn(Arrays.asList());
        
        // 执行验证
        X509Certificate result = validator.map(childCert);
        
        // 验证结果
        assertTrue(result.getLabels().contains(CertificateLabel.MISSING_CHAIN));
    }
    
    /**
     * 创建模拟证书
     * 
     * @param subject 主题
     * @param issuer 颁发者
     * @param isCA 是否是CA证书
     * @return 模拟证书
     */
    private X509Certificate createMockCertificate(String subject, String issuer, boolean isCA) {
        X509Certificate certificate = mock(X509Certificate.class);
        
        // 设置基本属性
        when(certificate.getDerSha1()).thenReturn("mock-sha1-" + System.currentTimeMillis());
        when(certificate.getSubjectDn()).thenReturn(subject);
        when(certificate.getIssuerDn()).thenReturn(issuer);
        
        // 设置扩展信息
        Map<String, String> extensions = new HashMap<>();
        if (isCA) {
            extensions.put("basicConstraints", "CA:TRUE");
        } else {
            extensions.put("basicConstraints", "CA:FALSE");
        }
        when(certificate.getExtensions()).thenReturn(extensions);
        
        // 设置标签集合
        Set<CertificateLabel> labels = new HashSet<>();
        when(certificate.getLabels()).thenReturn(labels);
        
        // 模拟添加标签方法
        doAnswer(invocation -> {
            CertificateLabel label = invocation.getArgument(0);
            labels.add(label);
            return null;
        }).when(certificate).addLabel(any(CertificateLabel.class));
        
        // 设置证书链
        when(certificate.getCertificateChain()).thenReturn(new java.util.ArrayList<>());
        
        return certificate;
    }
}

package com.geeksec.certificate.analyzer.pipeline.analysis.chain;

import java.util.ArrayList;
import java.util.List;

/**
 * 证书标签类型处理测试
 * 验证证书标签统一使用Integer类型，与dim_cert表的labels字段定义保持一致
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class LabelTypeTest {

    public static void main(String[] args) {
        LabelTypeTest test = new LabelTypeTest();

        System.out.println("开始执行证书标签类型处理测试...\n");

        test.testLabelHashConsistency();
        test.testCommonCertificateLabels();
        test.testLabelListTypeSafety();
        test.testNullAndEdgeCases();
        test.testDimCertTableCompatibility();

        System.out.println("\n所有测试执行完成！");
    }

    public void testLabelHashConsistency() {
        System.out.println("测试标签哈希转换的一致性...");
        // 测试相同标签字符串应该产生相同的整数ID
        String labelText = "Self Signed Cert";

        // 模拟CertificateChainInitialClassifier中的转换逻辑
        Integer labelId1 = Math.abs(labelText.hashCode()) % 10000;

        // 模拟CertificateChainTrustEvaluator中的转换逻辑
        Integer labelId2 = Math.abs(labelText.hashCode()) % 10000;

        // 模拟CertificateTrustPathOrchestrator中的转换逻辑
        Integer labelId3 = Math.abs(labelText.hashCode()) % 10000;

        assert labelId1.equals(labelId2) : "不同类中相同标签应产生相同的ID";
        assert labelId2.equals(labelId3) : "不同类中相同标签应产生相同的ID";
        assert labelId1.equals(labelId3) : "不同类中相同标签应产生相同的ID";

        System.out.println("✓ 标签哈希转换一致性测试通过");
    }

    public void testCommonCertificateLabels() {
        System.out.println("测试常用证书标签的ID生成...");
        List<String> commonLabels = List.of(
            "Self Signed Cert",
            "White CA Cert",
            "Unknown CA",
            "Fake Cert",
            "Lost CertList",
            "Multi CertList",
            "Chain Mess",
            "Illegal Cert",
            "Insecure Chain",
            "Long Certificate Chain"
        );

        List<Integer> labelIds = new ArrayList<>();

        for (String label : commonLabels) {
            Integer labelId = Math.abs(label.hashCode()) % 10000;
            assert labelId != null : "标签ID不应为null: " + label;
            assert (labelId >= 0 && labelId < 10000) : "标签ID应在0-9999范围内: " + label + " -> " + labelId;
            labelIds.add(labelId);

            System.out.println("标签: '" + label + "' -> ID: " + labelId);
        }

        // 检查是否有重复的ID（虽然哈希冲突是可能的，但在这个小范围内应该很少）
        long uniqueCount = labelIds.stream().distinct().count();
        assert commonLabels.size() == uniqueCount : "标签ID应该尽可能唯一";

        System.out.println("✓ 常用证书标签ID生成测试通过");
    }

    public void testLabelListTypeSafety() {
        System.out.println("测试标签列表操作的类型安全性...");
        // 模拟证书标签列表
        List<Integer> securityLabels = new ArrayList<>();

        // 添加标签
        String labelText = "Test Label";
        Integer labelId = Math.abs(labelText.hashCode()) % 10000;

        // 检查标签是否已存在（模拟addLabelIfNotPresent逻辑）
        if (!securityLabels.contains(labelId)) {
            securityLabels.add(labelId);
        }

        assert securityLabels.size() == 1 : "应该添加一个标签";
        assert securityLabels.contains(labelId) : "标签列表应包含添加的标签ID";

        // 再次添加相同标签，应该不会重复
        if (!securityLabels.contains(labelId)) {
            securityLabels.add(labelId);
        }

        assert securityLabels.size() == 1 : "相同标签不应重复添加";

        System.out.println("✓ 标签列表操作类型安全性测试通过");
    }

    public void testNullAndEdgeCases() {
        System.out.println("测试空值和边界情况处理...");
        // 测试null标签
        String nullLabel = null;
        Integer nullLabelId = convertStringLabelToInteger(nullLabel);
        assert Integer.valueOf(0).equals(nullLabelId) : "null标签应返回0";

        // 测试空字符串标签
        String emptyLabel = "";
        Integer emptyLabelId = convertStringLabelToInteger(emptyLabel);
        assert Integer.valueOf(0).equals(emptyLabelId) : "空字符串标签应返回0";

        // 测试空白字符串标签
        String blankLabel = "   ";
        Integer blankLabelId = convertStringLabelToInteger(blankLabel);
        assert !Integer.valueOf(0).equals(blankLabelId) : "空白字符串标签应有非零ID";

        System.out.println("✓ 空值和边界情况处理测试通过");
    }

    /**
     * 模拟CertificateChainTrustEvaluator中的标签转换方法
     */
    private Integer convertStringLabelToInteger(String label) {
        if (label == null || label.isEmpty()) {
            return 0;
        }
        // 使用标签的哈希码作为ID，确保相同标签得到相同ID
        return Math.abs(label.hashCode()) % 10000;
    }

    public void testDimCertTableCompatibility() {
        System.out.println("验证与dim_cert表labels字段的兼容性...");
        // dim_cert表的labels字段定义为ARRAY<INT>
        // 我们的标签处理应该产生Integer类型的值

        List<Integer> labels = new ArrayList<>();

        // 添加一些测试标签
        labels.add(convertStringLabelToInteger("CA"));
        labels.add(convertStringLabelToInteger("Self Signed Cert"));
        labels.add(convertStringLabelToInteger("White CA Cert"));

        // 验证所有标签都是Integer类型
        for (Object label : labels) {
            assert label instanceof Integer : "所有标签都应该是Integer类型";
        }

        // 验证标签值在合理范围内
        for (Integer label : labels) {
            assert (label >= 0 && label < 10000) : "标签值应在0-9999范围内: " + label;
        }

        System.out.println("生成的标签列表（兼容dim_cert.labels ARRAY<INT>）: " + labels);
        System.out.println("✓ dim_cert表labels字段兼容性测试通过");
    }
}

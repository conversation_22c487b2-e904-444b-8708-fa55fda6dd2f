package com.geeksec.certificate.analyzer.pipeline.analysis.chain;

import com.geeksec.certificate.analyzer.model.cert.X509Certificate;
import com.geeksec.certificate.analyzer.model.chain.CertificateChainContext;
import com.geeksec.certificate.analyzer.model.chain.ValidationResult;
import com.geeksec.certificate.analyzer.pipeline.analysis.validation.verification.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.*;
import java.util.LinkedHashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 证书链重构验证测试
 * 
 * 验证重构后的证书链验证逻辑是否正确工作
 *
 * <AUTHOR>
 */
class CertificateChainRefactoringTest {

    private CertificateChainValidationOrchestrator orchestrator;
    private LeafCertificateVerifier leafVerifier;
    private IntermediateCertificateVerifier intermediateVerifier;
    private RootCertificateVerifier rootVerifier;

    @BeforeEach
    void setUp() {
        orchestrator = new CertificateChainValidationOrchestrator();
        leafVerifier = new LeafCertificateVerifier();
        intermediateVerifier = new IntermediateCertificateVerifier();
        rootVerifier = new RootCertificateVerifier();
    }

    @Test
    @DisplayName("测试自签名根证书验证")
    void testSelfSignedRootCertificateValidation() {
        // 创建自签名根证书
        X509Certificate rootCert = createSelfSignedRootCertificate();

        // 创建验证上下文
        CertificateChainContext context = CertificateChainContext.builder()
                .currentCertificate(rootCert)
                .parentCertificates(new HashMap<>())
                .parentCertificateIds(new ArrayList<>())
                .labels(new ArrayList<>())
                .originalRow(null) // 简化测试，不依赖Flink Row
                .build();

        // 执行根证书验证
        ValidationResult result = rootVerifier.verify(context);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isValid(), "自签名根证书应该验证通过");
    }

    @Test
    @DisplayName("测试中间证书验证")
    void testIntermediateCertificateValidation() {
        // 创建中间证书和父证书
        X509Certificate intermediateCert = createIntermediateCertificate();
        X509Certificate parentCert = createSelfSignedRootCertificate();

        Map<String, X509Certificate> parentCerts = new HashMap<>();
        parentCerts.put(parentCert.getDerSha1(), parentCert);

        // 创建验证上下文
        CertificateChainContext context = CertificateChainContext.builder()
                .currentCertificate(intermediateCert)
                .parentCertificates(parentCerts)
                .parentCertificateIds(Arrays.asList(parentCert.getDerSha1()))
                .labels(new ArrayList<>())
                .originalRow(null) // 简化测试，不依赖Flink Row
                .build();

        // 执行中间证书验证
        ValidationResult result = intermediateVerifier.verify(context);

        // 验证结果
        assertNotNull(result);
        // 注意：由于我们没有实际的密钥对，签名验证可能失败，但结构应该正确
    }

    @Test
    @DisplayName("测试叶证书验证")
    void testLeafCertificateValidation() {
        // 创建叶证书
        X509Certificate leafCert = createLeafCertificate();

        // 创建验证上下文
        CertificateChainContext context = CertificateChainContext.builder()
                .currentCertificate(leafCert)
                .parentCertificates(new HashMap<>())
                .parentCertificateIds(new ArrayList<>())
                .labels(new ArrayList<>())
                .originalRow(null) // 简化测试，不依赖Flink Row
                .build();

        // 执行叶证书验证
        ValidationResult result = leafVerifier.verify(context);

        // 验证结果
        assertNotNull(result);
    }

    @Test
    @DisplayName("测试验证协调器完整流程")
    void testValidationOrchestratorCompleteFlow() {
        // 创建测试证书
        X509Certificate testCert = createLeafCertificate();
        Map<String, X509Certificate> parentCerts = new HashMap<>();
        List<String> parentCertIds = new ArrayList<>();
        List<Integer> labels = new ArrayList<>();

        // 执行验证（不依赖Flink Row）
        ValidationResult result = orchestrator.validateCertificateChain(
                testCert, parentCerts, parentCertIds, labels, null);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getMessage());
    }

    @Test
    @DisplayName("测试标签转换功能")
    void testLabelConversion() {
        List<Integer> labels = new ArrayList<>();
        X509Certificate cert = createLeafCertificate();
        cert.setLabels(labels);
        
        // 创建上下文并添加标签
        CertificateChainContext context = CertificateChainContext.builder()
                .currentCertificate(cert)
                .labels(labels)
                .build();
        
        context.addLabel("Test Label");
        
        // 验证标签已正确转换和添加
        assertFalse(labels.isEmpty(), "标签列表不应为空");
        
        // 验证相同标签不会重复添加
        int originalSize = labels.size();
        context.addLabel("Test Label");
        assertEquals(originalSize, labels.size(), "相同标签不应重复添加");
    }

    @Test
    @DisplayName("测试验证器责任链")
    void testVerifierChain() {
        // 测试验证器责任链的构建
        LeafCertificateVerifier leafVerifier = new LeafCertificateVerifier();
        IntermediateCertificateVerifier intermediateVerifier = new IntermediateCertificateVerifier();
        RootCertificateVerifier rootVerifier = new RootCertificateVerifier();

        // 构建责任链
        leafVerifier.setNextVerifier(intermediateVerifier);
        intermediateVerifier.setNextVerifier(rootVerifier);

        // 验证责任链构建正确
        assertNotNull(leafVerifier);
        assertNotNull(intermediateVerifier);
        assertNotNull(rootVerifier);
    }

    // 辅助方法：创建自签名根证书
    private X509Certificate createSelfSignedRootCertificate() {
        X509Certificate cert = new X509Certificate(new byte[0]); // 使用空字节数组作为测试
        cert.setDerSha1("root-cert-sha1");

        // 设置主题和颁发者相同（自签名）
        LinkedHashMap<String, String> subject = new LinkedHashMap<>();
        subject.put("CN", "Test Root CA");
        subject.put("O", "Test Organization");
        cert.setSubject(subject);
        cert.setIssuer(subject); // 自签名：主题和颁发者相同

        // 设置基本约束为CA
        java.util.HashMap<String, String> extensions = new java.util.HashMap<>();
        extensions.put("basicConstraints", "CA:TRUE");
        cert.setExtension(extensions);

        // 设置密钥用法
        cert.setKeyUsage("keyCertSign");

        cert.setLabels(new ArrayList<>());
        cert.setParentCertIdList(new ArrayList<>());

        return cert;
    }

    // 辅助方法：创建中间证书
    private X509Certificate createIntermediateCertificate() {
        X509Certificate cert = new X509Certificate(new byte[0]); // 使用空字节数组作为测试
        cert.setDerSha1("intermediate-cert-sha1");

        // 设置主题
        LinkedHashMap<String, String> subject = new LinkedHashMap<>();
        subject.put("CN", "Test Intermediate CA");
        subject.put("O", "Test Organization");
        cert.setSubject(subject);

        // 设置不同的颁发者
        LinkedHashMap<String, String> issuer = new LinkedHashMap<>();
        issuer.put("CN", "Test Root CA");
        issuer.put("O", "Test Organization");
        cert.setIssuer(issuer);

        // 设置基本约束为CA
        java.util.HashMap<String, String> extensions = new java.util.HashMap<>();
        extensions.put("basicConstraints", "CA:TRUE");
        cert.setExtension(extensions);

        cert.setKeyUsage("keyCertSign");
        cert.setLabels(new ArrayList<>());
        cert.setParentCertIdList(new ArrayList<>());

        return cert;
    }

    // 辅助方法：创建叶证书
    private X509Certificate createLeafCertificate() {
        X509Certificate cert = new X509Certificate(new byte[0]); // 使用空字节数组作为测试
        cert.setDerSha1("leaf-cert-sha1");

        // 设置主题
        LinkedHashMap<String, String> subject = new LinkedHashMap<>();
        subject.put("CN", "test.example.com");
        subject.put("O", "Test Organization");
        cert.setSubject(subject);

        // 设置颁发者
        LinkedHashMap<String, String> issuer = new LinkedHashMap<>();
        issuer.put("CN", "Test Intermediate CA");
        issuer.put("O", "Test Organization");
        cert.setIssuer(issuer);

        // 叶证书不是CA
        java.util.HashMap<String, String> extensions = new java.util.HashMap<>();
        extensions.put("basicConstraints", "CA:FALSE");
        cert.setExtension(extensions);

        cert.setLabels(new ArrayList<>());
        cert.setParentCertIdList(new ArrayList<>());

        return cert;
    }
}

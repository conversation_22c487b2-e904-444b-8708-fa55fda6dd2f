package com.geeksec.nta.certificate.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Redis工具类测试
 * 使用Mockito模拟外部依赖
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RedisUtilsTest {

    @Mock
    private JedisPool jedisPool;

    @Mock
    private Jedis jedis;

    @Mock
    private Pipeline pipeline;

    @BeforeEach
    public void setUp() {
        // 模拟JedisPool.getResource()返回模拟的Jedis对象
        when(jedisPool.getResource()).thenReturn(jedis);
        when(jedisPool.isClosed()).thenReturn(false);

        // 模拟Jedis.pipelined()返回模拟的Pipeline对象
        when(jedis.pipelined()).thenReturn(pipeline);
    }

    /**
     * 测试清空Redis数据库
     */
    @Test
    public void testClearRedisDb() {
        // 准备测试数据
        int testDb = 2;
        Set<String> mockKeys = new HashSet<>(Arrays.asList("key1", "key2", "key3"));

        // 模拟行为
        when(jedis.select(testDb)).thenReturn("OK");
        when(jedis.keys("*")).thenReturn(mockKeys);
        when(jedis.del("key1", "key2", "key3")).thenReturn(3L);

        // 执行测试
        RedisUtils.clearRedisDb(testDb, jedisPool);

        // 验证交互
        verify(jedis).select(testDb);
        verify(jedis).keys("*");
        verify(jedis).del("key1", "key2", "key3");
    }

    /**
     * 测试批量查询证书
     */
    @Test
    public void testQueryBatchCert() {
        // 准备测试数据
        List<String> testKeys = Arrays.asList("TEST_CERT_1", "TEST_CERT_2");

        // 模拟Response对象
        Response<String> response1 = Mockito.mock(Response.class);
        Response<String> response2 = Mockito.mock(Response.class);

        // 模拟行为
        when(pipeline.get("CERT_TEST_CERT_1")).thenReturn(response1);
        when(pipeline.get("CERT_TEST_CERT_2")).thenReturn(response2);
        when(response1.get()).thenReturn("test_cert_value_1");
        when(response2.get()).thenReturn("test_cert_value_2");

        // 执行测试
        Map<String, String> result = RedisUtils.queryBatchCert(testKeys, jedis);

        // 验证结果
        assertEquals(2, result.size());
        assertEquals("test_cert_value_1", result.get("TEST_CERT_1"));
        assertEquals("test_cert_value_2", result.get("TEST_CERT_2"));

        // 验证交互
        verify(jedis).pipelined();
        verify(pipeline).get("CERT_TEST_CERT_1");
        verify(pipeline).get("CERT_TEST_CERT_2");
        verify(pipeline).sync();
    }

    /**
     * 测试批量写入证书
     */
    @Test
    public void testWriteBatchCert() {
        // 准备测试数据
        Map<String, String> testData = new HashMap<>();
        testData.put("TEST_WRITE_CERT_1", "test_write_value_1");
        testData.put("TEST_WRITE_CERT_2", "test_write_value_2");

        // 执行测试
        RedisUtils.writeBatchCert(testData, jedis);

        // 验证交互
        verify(jedis).pipelined();
        verify(pipeline, times(2)).set(anyString(), anyString(), any());
        verify(pipeline).sync();
    }

    /**
     * 测试获取Redis任务批次信息
     */
    @Test
    public void testGetRedisTaskBatchInfo() {
        // 准备测试数据
        String testSha1 = "TEST_SHA1";
        Set<String> mockKeys = new HashSet<>(Arrays.asList(
                testSha1 + ":task1-batch1",
                testSha1 + ":task2-batch2"
        ));

        // 模拟行为
        when(jedis.keys(testSha1 + ":*")).thenReturn(mockKeys);
        when(jedis.get(testSha1 + ":task1-batch1")).thenReturn("0");
        when(jedis.get(testSha1 + ":task2-batch2")).thenReturn("0");

        // 执行测试
        List<String> result = RedisUtils.getRedisTaskBatchInfo(testSha1, jedis);

        // 验证结果
        assertEquals(2, result.size());
        assertTrue(result.contains("task1-batch1"));
        assertTrue(result.contains("task2-batch2"));

        // 验证交互
        verify(jedis).keys(testSha1 + ":*");
        verify(jedis).get(testSha1 + ":task1-batch1");
        verify(jedis).get(testSha1 + ":task2-batch2");
        verify(jedis, times(2)).set(anyString(), eq("1"));
    }
}

package com.geeksec.nta.traffic.etl.etl.graph.extractor.session.edge;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class FingerprintIdentifiesAppEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.FINGERPRINT_IDENTIFIES_APP_TAG;
    }

    /**
     * TLS指纹识别特定应用程序 (dSSLFINGER -> APP)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取应用信息
        String dIp = value.getField(FieldConstants.FIELD_DST_IP).toString();
        Integer dPort = Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString());
        String appName = value.getField(FieldConstants.FIELD_APP_NAME).toString();
        String appKey = dIp + "_" + dPort + "_" + appName;
        // 获取指纹信息
        String dSSLFinger = value.getField(FieldConstants.FIELD_SSL_S_FINGER).toString();
        return List.of(
                Row.of(dSSLFinger,
                        appKey,
                        0 // rank 暂定0
                ));
    }
}

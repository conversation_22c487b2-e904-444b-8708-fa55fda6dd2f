package com.geeksec.nta.traffic.etl.etl.graph.extractor.base;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import com.geeksec.flink.common.network.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.proto.ZMPNMsg;

import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * 基础边提取器
 * 所有边提取器的基类，提供公共方法和初始化逻辑
 *
 * <AUTHOR> Team
 */
@Slf4j
public abstract class BaseEdgeExtractor {

    /** 公共后缀列表工厂 */
    protected static PublicSuffixListFactory factory = null;

    /** 公共后缀列表 */
    protected static PublicSuffixList suffixList = null;

    /** 客户端类型 */
    protected static final String CLIENT_TYPE = "client";

    /** 服务端类型 */
    protected static final String SERVER_TYPE = "server";

    /** IP类型 */
    protected static final String IP_TYPE = "ip";

    /** MAC类型 */
    protected static final String MAC_TYPE = "mac";

    /**
     * 域名端口分隔符
     */
    private static final String DOMAIN_PORT_SEPARATOR = ":";

    /**
     * 加载公共后缀列表
     */
    static {
        // 初始化公共后缀列表
        factory = new PublicSuffixListFactory();
        suffixList = factory.build();
    }

    /**
     * 获取边关系的输出标签
     * @return
     */
    public abstract OutputTag<Row> getOutputTag();

    /**
     * 提取元数据中的边关系，每种边有对应实现类
     * @param value 元数据
     * @return
     */
    public abstract List<Row> extractEdge(Row value);

    /**
     * 获取基础域名
     * 从域名中提取注册域名
     *
     * @param domain 域名
     * @return 基础域名，如果无法获取则返回null
     */
    protected String getBaseDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return null;
        }

        // 使用getRegistrableDomain方法获取注册域名
        try {
            String registrableDomain = suffixList.getRegistrableDomain(domain);
            if (registrableDomain != null) {
                return registrableDomain;
            }
        } catch (Exception e) {
            log.warn("获取基础域名失败, domain: {}, error: {}", domain, e.getMessage());
        }

        return null;
    }

    /**
     * 从会话元数据获取域名
     * @param value 会话元数据
     */
    protected Set<String> getSessionDomain(Row value) {
        Set<String> domains = new HashSet<>();
        // 获取HTTP信息列表
        List<ZMPNMsg.single_http> httpInfoList = (List<ZMPNMsg.single_http>) value.getField(FieldConstants.SESSION_HTTP_LIST);
        // 获取DNS信息列表
        List<ZMPNMsg.single_dns> dnsInfoList = (List<ZMPNMsg.single_dns>) value.getField(FieldConstants.SESSION_DNS_LIST);
        // 获取SSL信息列表
        List<ZMPNMsg.single_ssl> sslInfoList = (List<ZMPNMsg.single_ssl>) value.getField(FieldConstants.SESSION_SSL_LIST);

        // 处理HTTP中的域名
        if (CollectionUtils.isNotEmpty(httpInfoList)) {
            for (ZMPNMsg.single_http singleHttp : httpInfoList) {
                try {
                    if (singleHttp.hasResponse()) {
                        String httpDomainAddr = singleHttp.getHost();
                        if (DomainUtils.isValidDomain(httpDomainAddr)) {
                            if (httpDomainAddr.contains(DOMAIN_PORT_SEPARATOR)) {
                                httpDomainAddr = httpDomainAddr.split(DOMAIN_PORT_SEPARATOR)[0];
                            }
                            domains.add(httpDomainAddr);
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理HTTP域名信息失败: {}", e.getMessage());
                }
            }
        }

        // 处理DNS中的域名
        if (CollectionUtils.isNotEmpty(dnsInfoList)) {
            for (ZMPNMsg.single_dns singleDns : dnsInfoList) {
                String dnsDomainAddr = singleDns.getDomain();
                if (DomainUtils.isValidDomain(dnsDomainAddr)) {
                    domains.add(dnsDomainAddr);
                }
            }
        }

        // 处理SSL中的域名
        if (CollectionUtils.isNotEmpty(sslInfoList)) {
            ZMPNMsg.single_ssl singleSsl = sslInfoList.get(0);
            String sslDomainAddr = singleSsl.getChServerName();
            if (DomainUtils.isValidDomain(sslDomainAddr)) {
                if (sslDomainAddr.contains(DOMAIN_PORT_SEPARATOR)) {
                    sslDomainAddr = sslDomainAddr.split(DOMAIN_PORT_SEPARATOR)[0];
                }
                domains.add(sslDomainAddr);
            }
        }
        return domains;
    }
}

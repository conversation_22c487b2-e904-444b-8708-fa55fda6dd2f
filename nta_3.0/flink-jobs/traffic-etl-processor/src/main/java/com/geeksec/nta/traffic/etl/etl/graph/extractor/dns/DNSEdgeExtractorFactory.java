package com.geeksec.nta.traffic.etl.etl.graph.extractor.dns;

import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.dns.edge.*;

import java.util.Arrays;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:00$ 2025/6/17$
 **/
public class DNSEdgeExtractorFactory {
    public static List<BaseEdgeExtractor> getEdgeExtractors() {
        return Arrays.asList(
                new ClientQueriesDnsServerEdgeExtractor(),
                new ClientQueriesDomainEdgeExtractor(),
                new CnamePointsDomainEdgeExtractor(),
                new DnsServerResolvesDomainEdgeExtractor(),
                new DomainDerivesRegDomainEdgeExtractor(),
                new DomainResolvesIPEdgeExtractor(),
                new OrgOwnsDomainEdgeExtractor(),
                new OrgOwnsIPEdgeExtractor()
        );
    }
}

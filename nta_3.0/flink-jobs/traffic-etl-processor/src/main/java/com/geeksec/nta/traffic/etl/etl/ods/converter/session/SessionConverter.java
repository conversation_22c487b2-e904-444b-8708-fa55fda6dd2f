package com.geeksec.nta.traffic.etl.etl.ods.converter.session;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网络会话消息转换器，负责将Protobuf格式的会话消息转换为Map表示，以便写入Doris
 * <AUTHOR>
 */
@Slf4j
public class SessionConverter extends AbstractProtobufMessageConverter {

    // 所有字段常量已移至 FieldConstants 类中

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasSingleSession()) {
            log.warn("JKNmsg does not contain SingleSession message");
            return null;
        }

        // 创建存储转换数据的Map
        Map<String, Object> map = new HashMap<>(64);
        ZMPNMsg.single_session_msg session = msg.getSingleSession();
        ZMPNMsg.Comm_msg commMsg = session.getCommMsg();

        if (session.hasCommMsg()){
            // 基本会话信息
            map.put(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
            map.put(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
            map.put(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
            map.put(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
            map.put(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
            map.put(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
            map.put(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
            map.put(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
            map.put(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
            map.put(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
            map.put(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
            map.put(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());

            // 时间相关字段
            int beginTime = commMsg.getBeginTime();
            int beginNsec = commMsg.getBeginNsec();
            // 转换Unix时间戳为LocalDateTime
            LocalDateTime beginDateTime = LocalDateTime.ofEpochSecond(beginTime, beginNsec, ZoneOffset.UTC);
            map.put(FieldConstants.FIELD_BEGIN_TIME, beginDateTime);
            map.put(FieldConstants.FIELD_BEGIN_NSEC, beginNsec);
        }

        // 时间相关字段
        int endTime = session.getEndTime();
        int endNsec = session.hasEndNsec() ? session.getEndNsec() : 0;
        LocalDateTime endDateTime = endTime > 0 ? LocalDateTime.ofEpochSecond(endTime, endNsec, ZoneOffset.UTC) : null;
        map.put(FieldConstants.FIELD_END_TIME, endDateTime);
        map.put(FieldConstants.FIELD_END_NSEC, endNsec);

        // 处理规则标签
        List<Integer> ruleLabelsList = session.getRuleLabelsList();
        // 其他基本字段
        map.put(FieldConstants.FIELD_DURATION, session.getDuration());
        map.put(FieldConstants.FIELD_FIRST_PROTO, session.getFirstProto());
        map.put(FieldConstants.FIELD_RULE_LABELS, ruleLabelsList);
        map.put(FieldConstants.FIELD_PORT_LIST, session.getPortListList());

        // Session basic info
        if (session.hasSsBasic()) {
            ZMPNMsg.ss_basic_msg ssBasic = session.getSsBasic();
            map.put(FieldConstants.FIELD_SMAC, ssBasic.getSmac());
            map.put(FieldConstants.FIELD_DMAC, ssBasic.getDmac());
            map.put(FieldConstants.FIELD_RULE_NUM, ssBasic.getRuleNum());
            map.put(FieldConstants.FIELD_RULE_LEVEL, ssBasic.getRuleLevel());

            // 将二进制数据转换为Base64字符串
            if (ssBasic.hasSyn()) {
                map.put(FieldConstants.FIELD_SYN, bytesToBase64String(ssBasic.getSyn()));
            }
            if (ssBasic.hasSynAck()) {
                map.put(FieldConstants.FIELD_SYN_ACK, bytesToBase64String(ssBasic.getSynAck()));
            }

            // 规则消息转为JSON
            if (ssBasic.hasRule()) {
                map.put(FieldConstants.FIELD_RULE_MSG, com.alibaba.fastjson2.JSON.toJSONString(ssBasic.getRule()));
            }
        }

        // Session packet info
        if (session.hasSsPkt()) {
            ZMPNMsg.ss_pkt_msg ssPkt = session.getSsPkt();
            map.put(FieldConstants.FIELD_PKT_SMAXLEN, ssPkt.getPktSmaxlen());
            map.put(FieldConstants.FIELD_PKT_DMAXLEN, ssPkt.getPktDmaxlen());
            map.put(FieldConstants.FIELD_PKT_SNUM, ssPkt.getPktSnum());
            map.put(FieldConstants.FIELD_PKT_DNUM, ssPkt.getPktDnum());
            map.put(FieldConstants.FIELD_PKT_SPAYLOADNUM, ssPkt.getPktSpayloadnum());
            map.put(FieldConstants.FIELD_PKT_DPAYLOADNUM, ssPkt.getPktDpayloadnum());
            map.put(FieldConstants.FIELD_PKT_SBYTES, ssPkt.getPktSbytes());
            map.put(FieldConstants.FIELD_PKT_DBYTES, ssPkt.getPktDbytes());
            map.put(FieldConstants.FIELD_PKT_SPAYLOADBYTES, ssPkt.getPktSpayloadbytes());
            map.put(FieldConstants.FIELD_PKT_DPAYLOADBYTES, ssPkt.getPktDpayloadbytes());
            map.put(FieldConstants.FIELD_PKT_SFINNUM, ssPkt.getPktSfinnum());
            map.put(FieldConstants.FIELD_PKT_DFINNUM, ssPkt.getPktDfinnum());
            map.put(FieldConstants.FIELD_PKT_SRSTNUM, ssPkt.getPktSrstnum());
            map.put(FieldConstants.FIELD_PKT_DRSTNUM, ssPkt.getPktDrstnum());
            map.put(FieldConstants.FIELD_PKT_SSYNNUM, ssPkt.getPktSsynnum());
            map.put(FieldConstants.FIELD_PKT_DSYNNUM, ssPkt.getPktDsynnum());
            map.put(FieldConstants.FIELD_PKT_SSYNBYTES, ssPkt.getPktSsynbytes());
            map.put(FieldConstants.FIELD_PKT_DSYNBYTES, ssPkt.getPktDsynbytes());
            map.put(FieldConstants.FIELD_PKT_STTLMAX, ssPkt.getPktSttlmax());
            map.put(FieldConstants.FIELD_PKT_DTTLMAX, ssPkt.getPktDttlmax());
            map.put(FieldConstants.FIELD_PKT_STTLMIN, ssPkt.getPktSttlmin());
            map.put(FieldConstants.FIELD_PKT_DTTLMIN, ssPkt.getPktDttlmin());
            map.put(FieldConstants.FIELD_PKT_SDURMAX, ssPkt.getPktSdurmax());
            map.put(FieldConstants.FIELD_PKT_DDURMAX, ssPkt.getPktDdurmax());
            map.put(FieldConstants.FIELD_PKT_SDURMIN, ssPkt.getPktSdurmin());
            map.put(FieldConstants.FIELD_PKT_DDURMIN, ssPkt.getPktDdurmin());
            map.put(FieldConstants.FIELD_PKT_SDISORDER, ssPkt.getPktSdisorder());
            map.put(FieldConstants.FIELD_PKT_DDISORDER, ssPkt.getPktDdisorder());
            map.put(FieldConstants.FIELD_PKT_SRESEND, ssPkt.getPktSresend());
            map.put(FieldConstants.FIELD_PKT_DRESEND, ssPkt.getPktDresend());
            map.put(FieldConstants.FIELD_PKT_SLOST, ssPkt.getPktSlost());
            map.put(FieldConstants.FIELD_PKT_DLOST, ssPkt.getPktDlost());
            map.put(FieldConstants.FIELD_PKT_SPSHNUM, ssPkt.getPktSpshnum());
            map.put(FieldConstants.FIELD_PKT_DPSHNUM, ssPkt.getPktDpshnum());
            map.put(FieldConstants.FIELD_PKT_PRONUM, ssPkt.getPktPronum());
            map.put(FieldConstants.FIELD_PKT_UNKONW_PRONUM, ssPkt.getPktUnkonwPronum());
            map.put(FieldConstants.FIELD_PKT_SYN_DATA, ssPkt.getPktSynData());
            map.put(FieldConstants.FIELD_PKT_SBADNUM, ssPkt.getPktSbadnum());
            map.put(FieldConstants.FIELD_PKT_DBADNUM, ssPkt.getPktDbadnum());
            map.put(FieldConstants.FIELD_APP_PKT_ID, ssPkt.getAppPktId());

            // 处理二进制数据列表
            List<String> encodedSpayloadList = new ArrayList<>();
            for (com.google.protobuf.ByteString payload : ssPkt.getPktSpayloadList()) {
                encodedSpayloadList.add(bytesToBase64String(payload));
            }
            map.put(FieldConstants.FIELD_PKT_SPAYLOAD, encodedSpayloadList);

            List<String> encodedDpayloadList = new ArrayList<>();
            for (com.google.protobuf.ByteString payload : ssPkt.getPktDpayloadList()) {
                encodedDpayloadList.add(bytesToBase64String(payload));
            }
            map.put(FieldConstants.FIELD_PKT_DPAYLOAD, encodedDpayloadList);

            // 包信息
            map.put(FieldConstants.FIELD_PKT_INFOR, getPktInfo(ssPkt));
        }

        // Fingerprints
        map.put(FieldConstants.FIELD_TCP_C_FINGER, session.getTcpCFinger());
        map.put(FieldConstants.FIELD_TCP_S_FINGER, session.getTcpSFinger());
        map.put(FieldConstants.FIELD_HTTP_C_FINGER, session.getHttpCFinger());
        map.put(FieldConstants.FIELD_HTTP_S_FINGER, session.getHttpSFinger());
        map.put(FieldConstants.FIELD_SSL_C_FINGER, session.getSslCFinger());
        map.put(FieldConstants.FIELD_SSL_S_FINGER, session.getSslSFinger());

        // 统计信息
        if (session.hasSsStats()) {
            ZMPNMsg.ss_stats_msg ssStats = session.getSsStats();

            // 协议列表
            if (ssStats.hasStatsProlist()) {
                String proListString = ssStats.getStatsProlist();
                if (proListString != null && !proListString.isEmpty()) {
                    map.put(FieldConstants.FIELD_STATS_PROLIST, com.alibaba.fastjson2.JSON.parseObject(proListString));
                }
            }

            // 其他统计字段
            map.put(FieldConstants.FIELD_STATS_STOTALSIGN, ssStats.getStatsStotalsign());
            map.put(FieldConstants.FIELD_STATS_DTOTALSIGN, ssStats.getStatsDtotalsign());

            // 字节分布
            if (ssStats.hasStatsDistbytes()) {
                map.put(FieldConstants.FIELD_STATS_DISTBYTES, com.alibaba.fastjson2.JSON.parseObject(ssStats.getStatsDistbytes()));
            }

            map.put(FieldConstants.FIELD_STATS_DISTBYTESNUM, ssStats.getStatsDistbytesnum());
            map.put(FieldConstants.FIELD_STATS_DISTCSQ, ssStats.getStatsDistcsq());
            map.put(FieldConstants.FIELD_STATS_DISTCSQT, ssStats.getStatsDistcsqt());
            map.put(FieldConstants.FIELD_STATS_PROLIST_NUM, ssStats.getStatsProlistNum());
            map.put(FieldConstants.FIELD_SIO_SIGN, ssStats.getSioSign());
            map.put(FieldConstants.FIELD_DIO_SIGN, ssStats.getDioSign());

            if (ssStats.hasExtJson()) {
                map.put(FieldConstants.FIELD_EXT_JSON, com.alibaba.fastjson2.JSON.parseObject(ssStats.getExtJson()));
            }

            map.put(FieldConstants.FIELD_STATS_SRC_MSS, ssStats.getStatsSrcMss());
            map.put(FieldConstants.FIELD_STATS_DST_MSS, ssStats.getStatsDstMss());
            map.put(FieldConstants.FIELD_STATS_SRC_WINDOW_SCALE, ssStats.getStatsSrcWindowScale());
            map.put(FieldConstants.FIELD_STATS_DST_WINDOW_SCALE, ssStats.getStatsDstWindowScale());
            map.put(FieldConstants.FIELD_STATS_SPAYLOAD_MAXLEN, ssStats.getStatsSpayloadMaxlen());
            map.put(FieldConstants.FIELD_STATS_DPAYLOAD_MAXLEN, ssStats.getStatsDpayloadMaxlen());
            map.put(FieldConstants.FIELD_STATS_SACK_PAYLOAD_MAXLEN, ssStats.getStatsSackPayloadMaxlen());
            map.put(FieldConstants.FIELD_STATS_DACK_PAYLOAD_MAXLEN, ssStats.getStatsDackPayloadMaxlen());
            map.put(FieldConstants.FIELD_STATS_SACK_PAYLOAD_MINLEN, ssStats.getStatsSackPayloadMinlen());
            map.put(FieldConstants.FIELD_STATS_DACK_PAYLOAD_MINLEN, ssStats.getStatsDackPayloadMinlen());

            // 数组类型字段
            map.put(FieldConstants.FIELD_STATS_SDISTLEN, ssStats.getStatsSdistlenList());
            map.put(FieldConstants.FIELD_STATS_DDISTLEN, ssStats.getStatsDdistlenList());
            map.put(FieldConstants.FIELD_STATS_DISTDUR, ssStats.getStatsDistdurList());
            map.put(FieldConstants.FIELD_STATS_TCP_INFO, convertTcpInfoList(ssStats.getStatsTcpInfoList()));
            map.put(FieldConstants.FIELD_SYN_SEQ, ssStats.getSynSeqList());
            map.put(FieldConstants.FIELD_SYN_SEQ_NUM, ssStats.getSynSeqNum());
            map.put(FieldConstants.FIELD_STATS_SIPID_OFFSET, ssStats.getStatsSipidOffsetList());
            map.put(FieldConstants.FIELD_STATS_DIPID_OFFSET, ssStats.getStatsDipidOffsetList());
            map.put(FieldConstants.FIELD_BLOCK_CIPHER, ssStats.getBlockCipherList());
        }

        // 添加TCP指纹特征字段
        if (session.hasTcpCFingerFeature()) {
            ZMPNMsg.tcp_finger_feature_msg tcpClientFeature = session.getTcpCFingerFeature();
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_ECN_IP_ECT, tcpClientFeature.getEcnIpEct());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_QK_DFNZ_IPID, tcpClientFeature.getQkDfnzIpid());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_FLAG_CWR, tcpClientFeature.getFlagCwr());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_FLAG_ECE, tcpClientFeature.getFlagEce());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_QK_OPT_ZERO_TS1, tcpClientFeature.getQkOptZeroTs1());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_TTL, tcpClientFeature.getTtl());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_TCPOPT_EOL_PADNUM, tcpClientFeature.getTcpoptEolPadnum());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_TCPOPT_WSCALE, tcpClientFeature.getTcpoptWscale());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_QK_WIN_MSS, tcpClientFeature.getQkWinMss());
            map.put(FieldConstants.FIELD_TCP_C_FEATURE_TCPOPT_LAYOUT, tcpClientFeature.getTcpoptLayout());
        }

        if (session.hasTcpSFingerFeature()) {
            ZMPNMsg.tcp_finger_feature_msg tcpServerFeature = session.getTcpSFingerFeature();
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_ECN_IP_ECT, tcpServerFeature.getEcnIpEct());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_QK_DFNZ_IPID, tcpServerFeature.getQkDfnzIpid());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_FLAG_CWR, tcpServerFeature.getFlagCwr());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_FLAG_ECE, tcpServerFeature.getFlagEce());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_QK_OPT_ZERO_TS1, tcpServerFeature.getQkOptZeroTs1());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_TTL, tcpServerFeature.getTtl());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_TCPOPT_EOL_PADNUM, tcpServerFeature.getTcpoptEolPadnum());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_TCPOPT_WSCALE, tcpServerFeature.getTcpoptWscale());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_QK_WIN_MSS, tcpServerFeature.getQkWinMss());
            map.put(FieldConstants.FIELD_TCP_S_FEATURE_TCPOPT_LAYOUT, tcpServerFeature.getTcpoptLayout());
        }

        // 添加其他会话字段
        map.put(FieldConstants.FIELD_FIRST_SENDER, session.getFirstSender());
        map.put(FieldConstants.FIELD_DEVICE_ID, session.getDeviceId());
        map.put(FieldConstants.FIELD_PROXY_IP, session.getProxyIp());
        map.put(FieldConstants.FIELD_PROXY_PORT, session.getProxyPort());
        map.put(FieldConstants.FIELD_PROXY_REAL_HOST, session.getProxyRealHost());
        map.put(FieldConstants.FIELD_PROXY_TYPE, session.getProxyType());

        // 添加处理时间字段
        if (session.hasHandleBeginTime()) {
            LocalDateTime handleBeginTime = LocalDateTime.ofEpochSecond(session.getHandleBeginTime(), 0, ZoneOffset.UTC);
            map.put(FieldConstants.FIELD_HANDLE_BEGIN_TIME, handleBeginTime);
        }

        if (session.hasHandleEndTime()) {
            LocalDateTime handleEndTime = LocalDateTime.ofEpochSecond(session.getHandleEndTime(), 0, ZoneOffset.UTC);
            map.put(FieldConstants.FIELD_HANDLE_END_TIME, handleEndTime);
        }

        // 添加http字段
        if (session.getHttpCount() > 0){
            map.put(FieldConstants.SESSION_HTTP_LIST, session.getHttpList());
        }
        // 添加dns字段
        if (session.getDnsCount() > 0){
            map.put(FieldConstants.SESSION_DNS_LIST, session.getDnsList());

        }
        // 添加ssl字段
        if (session.getSslCount() > 0){
            map.put(FieldConstants.SESSION_SSL_LIST, session.getSslList());
        }

        // 将Map转换为Row
        Row row = Row.withNames();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            row.setField(entry.getKey(), entry.getValue());
        }
        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.SESSION_STREAM;
    }

    /**
     * 提取包信息并转换为Doris表结构中的pkt_infor字段格式
     *
     * @param ssPkt Protobuf包信息消息
     * @return 包信息列表
     */
    private List<Map<String, Object>> getPktInfo(ZMPNMsg.ss_pkt_msg ssPkt) {
        List<ZMPNMsg.packet_info_msg> packetInfoList = ssPkt.getPktInforList();

        // 估计列表大小，避免频繁扩容
        int estimatedSize = Math.min(packetInfoList.size(), 100);
        List<Map<String, Object>> pktInfo = new ArrayList<>(estimatedSize);

        for (ZMPNMsg.packet_info_msg packetInfoMsg : packetInfoList) {
            // 按照Doris表结构中的pkt_infor字段格式构建，预估4个字段
            Map<String, Object> packetInfo = new HashMap<>(4);
            packetInfo.put("count", packetInfoMsg.getCount());
            packetInfo.put("sec", packetInfoMsg.getSec());
            packetInfo.put("nsec", packetInfoMsg.getNsec());
            packetInfo.put("len", packetInfoMsg.getLen());
            pktInfo.add(packetInfo);
        }

        return pktInfo;
    }

    /**
     * 转换TCP信息列表为Doris表结构中的stats_tcp_info字段格式
     *
     * @param tcpInfoList Protobuf TCP信息列表
     * @return 转换后的TCP信息列表
     */
    private List<Map<String, Object>> convertTcpInfoList(List<ZMPNMsg.md_tcp_msg> tcpInfoList) {
        if (tcpInfoList == null || tcpInfoList.isEmpty()) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> result = new ArrayList<>(tcpInfoList.size());
        for (ZMPNMsg.md_tcp_msg tcpInfo : tcpInfoList) {
            Map<String, Object> map = new HashMap<>(6);
            map.put("bytes", tcpInfo.getBytes());
            map.put("packet_num", tcpInfo.getPacketNum());
            map.put("psh_num", tcpInfo.getPshNum());
            map.put("acknowledgement", tcpInfo.getAcknowledgement());
            map.put("min_sequence", tcpInfo.getMinSequence());
            map.put("max_sequence", tcpInfo.getMaxSequence());
            result.add(map);
        }
        return result;
    }
}
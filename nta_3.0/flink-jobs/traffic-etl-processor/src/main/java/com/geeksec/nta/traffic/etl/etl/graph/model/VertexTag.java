package com.geeksec.nta.traffic.etl.etl.graph.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

/**
 * 顶点标签枚举
 * 定义了所有用于Nebula Graph的顶点标签(TAG)
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
@ToString(of = "value")
public enum VertexTag {
    // IP顶点标签
    IP("IP"),

    // MAC顶点标签
    MAC("MAC"),

    // 应用顶点标签
    APPSERVICE("APPSERVICE"),

    // 域名顶点标签
    DOMAIN("DOMAIN"),

    // 可注册域顶点标签
    REGISTRABLE_DOMAIN("REGISTRABLE_DOMAIN"),

    // 证书顶点标签
    CERT("CERT"),

    // UA顶点标签
    UA("UA"),

    // 设备顶点标签
    DEVICE("DEVICE"),

    // 操作系统顶点标签
    OS("OS"),

    // 应用顶点标签
    APP("APP"),

    // SSL指纹顶点标签
    SSL_FINGERPRINT("SSLFINGERPRINT"),

    // 颁发者顶点标签
    ISSUER("ISSUER"),

    // 主体顶点标签
    SUBJECT("SUBJECT"),

    // 组织顶点标签
    ORG("ORG"),

    // URL顶点标签
    URL("URL"),
    
    // 攻击者标签
    ATTACKER("ATTACKER"),

    // 受害者标签
    VICTIM("VICTIM");

    private final String value;
}

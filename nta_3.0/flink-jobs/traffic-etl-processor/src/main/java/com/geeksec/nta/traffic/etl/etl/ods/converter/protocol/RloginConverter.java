package com.geeksec.nta.traffic.etl.etl.ods.converter.protocol;

import com.geeksec.flink.common.utils.time.TimeUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for Rlogin protocol messages.
 * Maps Rlogin protocol data from protobuf messages to Doris
 * ods_rlogin_protocol_metadata table format.
 * This converter is aligned with the latest ods_rlogin_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class RloginConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasRlogin()) {
            log.warn("JKNmsg does not contain Rlogin message");
            return null;
        }
        ZMPNMsg.rlogin_msg rlogin = msg.getRlogin();
        Row row = Row.withNames();
        if (rlogin.hasCommMsg()){
            // Common fields from Comm_msg
            enrichComMsg(row, rlogin.getCommMsg());
        }

        // Rlogin window information
        if (rlogin.hasWininfo()) {
            ZMPNMsg.rlogin_wininfo_msg wininfo = rlogin.getWininfo();
            row.setField(FieldConstants.FIELD_MAGIC_COOKIE, wininfo.getMagicCookie());
            row.setField(FieldConstants.FIELD_WINSIZE_MARKER, wininfo.getWinsizeMarker());
            row.setField(FieldConstants.FIELD_ROWS, wininfo.getRows());
            row.setField(FieldConstants.FIELD_COLUMNS, wininfo.getColumns());
            row.setField(FieldConstants.FIELD_X_PIXELS, wininfo.getXPixels());
            row.setField(FieldConstants.FIELD_Y_PIXELS, wininfo.getYPixels());
            row.setField(FieldConstants.FIELD_TERM_TYPE, wininfo.getTermType());
            row.setField(FieldConstants.FIELD_TERM_SPEED, wininfo.getTermSpeed());
        }

        // Rlogin user information
        if (rlogin.hasUserinfo()) {
            ZMPNMsg.rlogin_userinfo_msg userinfo = rlogin.getUserinfo();
            row.setField(FieldConstants.FIELD_CLIENT_USERNAME, userinfo.getClientUsername());
            row.setField(FieldConstants.FIELD_SERVER_USERNAME, userinfo.getServerUsername());
            row.setField(FieldConstants.FIELD_PASSWD, userinfo.getPasswd());
        }

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.RLOGIN_STREAM;
    }
}

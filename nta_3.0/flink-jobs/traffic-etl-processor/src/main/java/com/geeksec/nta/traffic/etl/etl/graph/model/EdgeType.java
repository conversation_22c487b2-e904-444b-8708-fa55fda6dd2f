package com.geeksec.nta.traffic.etl.etl.graph.model;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

/**
 * 边类型枚举
 * 定义了所有用于Nebula Graph的边类型
 * 与nebula-init.yaml中的定义保持一致
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
@ToString(of = "value")
public enum EdgeType {
    // 网络层关系（IP、MAC）
    IP_MAPS_TO_MAC("ip_maps_to_mac"),
    MAC_CONNECTS_TO_MAC("mac_connects_to_mac"),
    IP_CONNECTS_TO_IP("ip_connects_to_ip"),
    ATTACKS("attacks"),

    // 域名结构关系
    DOMAIN_DERIVES_FROM_REGISTRABLE_DOMAIN("domain_derives_from_registrable_domain"),
    CNAME_POINTS_TO_DOMAIN("cname_points_to_domain"),

    // DNS解析关系
    CLIENT_QUERIES_DOMAIN("client_queries_domain"),
    CLIENT_QUERIES_DNS_SERVER("client_queries_dns_server"),
    DNS_SERVER_RESOLVES_DOMAIN("dns_server_resolves_domain"),
    DOMAIN_RESOLVES_TO_IP("domain_resolves_to_ip"),

    // HTTP/TLS会话关系
    CLIENT_HTTP_REQUESTS_DOMAIN("client_http_requests_domain"),
    SERVER_HTTP_SERVES_DOMAIN("server_http_serves_domain"),
    CLIENT_TLS_REQUESTS_DOMAIN("client_tls_requests_domain"),
    SERVER_TLS_HOSTS_DOMAIN("server_tls_hosts_domain"),

    // 证书相关关系
    IP_PROVIDES_CERT("ip_provides_cert"),
    CLIENT_RECEIVES_CERT("client_receives_cert"),
    CERT_HAS_ISSUER("cert_has_issuer"),
    CERT_HAS_SUBJECT("cert_has_subject"),
    CERT_SIGNS_CERT("cert_signs_cert"),
    CERT_COVERS_DOMAIN("cert_covers_domain"),
    CERT_COVERS_REGISTRABLE_DOMAIN("cert_covers_registrable_domain"),
    CERT_SERVES_SNI("cert_serves_sni"),
    CERT_SECURES_CONNECTION_TO_URL("cert_secures_connection_to_url"),

    // TLS指纹关系
    IP_PRESENTS_FINGERPRINT("ip_presents_fingerprint"),
    FINGERPRINT_APPEARS_WITH_CERT("fingerprint_appears_with_cert"),
    FINGERPRINT_APPEARS_WITH_DOMAIN("fingerprint_appears_with_domain"),
    FINGERPRINT_IDENTIFIES_APP("fingerprint_identifies_app"),

    // 应用服务关系
    CLIENT_ACCESSES_APP("client_accesses_app"),
    APP_DEPLOYS_ON_SERVER("app_deploys_on_server"),
    APP_USES_CERT("app_uses_cert"),
    APP_USES_DOMAIN("app_uses_domain"),
    APP_USES_IP("app_uses_ip"),

    // UA/设备/OS关系
    CLIENT_USES_UA("client_uses_ua"),
    UA_REQUESTS_DOMAIN("ua_requests_domain"),
    UA_HAS_DEVICE("ua_has_device"),
    UA_HAS_OS("ua_has_os"),
    UA_HAS_APP("ua_has_app"),

    // 组织所有权关系
    ORG_OWNS_DOMAIN("org_owns_domain"),
    ORG_OWNS_REGISTRABLE_DOMAIN("org_owns_registrable_domain"),
    ORG_OWNS_IP("org_owns_ip"),
    ORG_OWNS_CERT("org_owns_cert"),

    // URL相关关系
    URL_CONTAINS_DOMAIN("url_contains_domain"),
    IP_PROVIDES_URL_SERVICE("ip_provides_url_service");

    private final String value;
}

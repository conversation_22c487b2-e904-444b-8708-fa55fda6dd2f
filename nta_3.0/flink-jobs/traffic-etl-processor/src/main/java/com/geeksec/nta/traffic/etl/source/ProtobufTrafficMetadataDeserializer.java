package com.geeksec.nta.traffic.etl.source;

import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.io.IOException;

/**
 * Kafka消息反序列化器，用于将Kafka中的二进制protobuf流量元数据反序列化为ZMPNMsg.JKNmsg对象
 *
 * <AUTHOR>
 */
@Slf4j
public class ProtobufTrafficMetadataDeserializer implements KafkaRecordDeserializationSchema<ZMPNMsg.JKNmsg> {

    @Override
    public TypeInformation<ZMPNMsg.JKNmsg> getProducedType() {
        return TypeInformation.of(ZMPNMsg.JKNmsg.class);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<ZMPNMsg.JKNmsg> collector) throws IOException {
        byte[] values = consumerRecord.value();

        if (values == null) {
            log.warn("Received null value from Kafka");
        } else {
            try {
                // 将二进制数据反序列化为ZMPNMsg.JKNmsg对象
                ZMPNMsg.JKNmsg jkNmsg = ZMPNMsg.JKNmsg.parseFrom(values);

                // 验证消息类型
                if (jkNmsg.hasType()) {
                    log.debug("Successfully deserialized message of type: {}", jkNmsg.getType());
                    collector.collect(jkNmsg);
                } else {
                    log.warn("Deserialized message has no type field");
                }
            } catch (Exception e) {
                log.error("Error deserializing protobuf message: {}", e.getMessage(), e);
                throw new SerializationException("Error deserializing protobuf message: " + e.getMessage(), e);
            }
        }
    }
}

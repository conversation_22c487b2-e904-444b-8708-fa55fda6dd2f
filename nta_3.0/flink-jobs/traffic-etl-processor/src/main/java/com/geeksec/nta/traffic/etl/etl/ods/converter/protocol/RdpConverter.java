package com.geeksec.nta.traffic.etl.etl.ods.converter.protocol;

import com.geeksec.flink.common.utils.time.TimeUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.ods.converter.common.AbstractProtobufMessageConverter;
import com.geeksec.nta.traffic.etl.etl.ods.tag.MessageOutputTag;
import com.geeksec.proto.ZMPNMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

/**
 * Converter for RDP protocol messages.
 * Maps RDP protocol data from protobuf messages to Doris
 * ods_rdp_protocol_metadata table format.
 * This converter is aligned with the latest ods_rdp_protocol_metadata table
 * schema.
 *
 * <AUTHOR> Team
 */
@Slf4j
public class RdpConverter extends AbstractProtobufMessageConverter {

    @Override
    protected Row convertMessage(ZMPNMsg.JKNmsg msg) {
        if (!msg.hasRdp()) {
            log.warn("JKNmsg does not contain Rdp message");
            return null;
        }
        ZMPNMsg.rdp_msg rdp = msg.getRdp();

        Row row = Row.withNames();
        if (rdp.hasCommMsg()){
            // Common fields from Comm_msg
            enrichComMsg(row, rdp.getCommMsg());
        }

        // RDP client fields (rdp_client_msg)
        if (rdp.hasRdpClient()) {
            ZMPNMsg.rdp_client_msg client = rdp.getRdpClient();
            row.setField(FieldConstants.FIELD_CLIENT_VERSION_MAJOR, client.getVersionMajor());
            row.setField(FieldConstants.FIELD_CLIENT_VERSION_MINOR, client.getVersionMinor());
            row.setField(FieldConstants.FIELD_DESKTOP_WIDTH, client.getDesktopWidth());
            row.setField(FieldConstants.FIELD_DESKTOP_HEIGHT, client.getDesktopHeight());
            row.setField(FieldConstants.FIELD_COLOR_DEPTH, client.getColorDepth());
            row.setField(FieldConstants.FIELD_SAS_SEQUENCE, client.getSasSequence());
            row.setField(FieldConstants.FIELD_KEYBOARD_LAYOUT, client.getKeyboardLayout());
            row.setField(FieldConstants.FIELD_CLIENT_BUILD, client.getClientBuild());
            row.setField(FieldConstants.FIELD_CLIENT_NAME, client.getClientName());
            row.setField(FieldConstants.FIELD_KEYBOARD_TYPE, client.getKeyboardType());
            row.setField(FieldConstants.FIELD_KEYBOARD_SUBTYPE, client.getKeyboardSubtype());
            row.setField(FieldConstants.FIELD_KEYBOARD_FUNCKEY, client.getKeyboardFunckey());
            row.setField(FieldConstants.FIELD_IME_FILENAME, client.getImeFilename());
            row.setField(FieldConstants.FIELD_CLIENT_PRODUCTID, client.getClientProductid());
            row.setField(FieldConstants.FIELD_CONNECTION_TYPE, client.getConnectionType());
            row.setField(FieldConstants.FIELD_ENCRYPTION_METHODS, client.getEncryptionMethods());
            row.setField(FieldConstants.FIELD_CHANNEL_COUNT, client.getChannelCount());
            row.setField(FieldConstants.FIELD_COOKIE, bytesToBase64String(client.getCookie()));
        }

        // RDP server fields (rdp_server_msg)
        if (rdp.hasRdpServer()) {
            ZMPNMsg.rdp_server_msg server = rdp.getRdpServer();
            row.setField(FieldConstants.FIELD_SERVER_VERSION_MAJOR, server.getVersionMajor());
            row.setField(FieldConstants.FIELD_SERVER_VERSION_MINOR, server.getVersionMinor());
            row.setField(FieldConstants.FIELD_MCS_CHANNELID, server.getMcsChannelid());
            row.setField(FieldConstants.FIELD_SERVER_CHANNEL_COUNT, server.getChannelCount());
            row.setField(FieldConstants.FIELD_ENCRYPTION_METHOD, server.getEncryptionMethod());
            row.setField(FieldConstants.FIELD_ENCRYPTION_LEVEL, server.getEncryptionLevel());
            row.setField(FieldConstants.FIELD_SERVER_RANDOMLEN, server.getServerRandomlen());
            row.setField(FieldConstants.FIELD_SERVER_CERTLEN, server.getServerCertlen());
            row.setField(FieldConstants.FIELD_SERVER_RANDOM, bytesToBase64String(server.getServerRandom()));
            row.setField(FieldConstants.FIELD_SERVER_CERT, bytesToBase64String(server.getServerCert()));
            row.setField(FieldConstants.FIELD_SELECTED_PROTOCOLS, server.getSelectedProtocols());
        }

        // RDP negotiate fields (rdp_negotiate_msg)
        if (rdp.hasRdpNegotiate()) {
            ZMPNMsg.rdp_negotiate_msg negotiate = rdp.getRdpNegotiate();
            row.setField(FieldConstants.FIELD_C_FLAG_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getCFlagProtocolsList()));
            row.setField(FieldConstants.FIELD_C_REQUESTED_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getCRequestedProtocolsList()));
            row.setField(FieldConstants.FIELD_S_FLAG_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getSFlagProtocolsList()));
            row.setField(FieldConstants.FIELD_S_SELECTED_PROTOCOLS,
                    convertProtoListToJavaList(negotiate.getSSelectedProtocolsList()));
        }

        return row;
    }

    @Override
    public OutputTag<Row> getOutputTag() {
        return MessageOutputTag.RDP_STREAM;
    }
}

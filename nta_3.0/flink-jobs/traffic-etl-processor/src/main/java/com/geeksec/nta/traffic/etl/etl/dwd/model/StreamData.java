package com.geeksec.nta.traffic.etl.etl.dwd.model;

import com.geeksec.nta.traffic.etl.common.MessageType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.flink.types.Row;

import java.io.Serializable;

/**
 * 流数据包装类
 * 用于在DWD层处理中携带消息类型信息，避免通过字段判断消息类型
 * 支持会话数据和各种协议数据的统一处理
 * 这是基于OutputTag的优化方案，更准确和高效
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StreamData implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 消息类型，使用枚举统一管理 */
    private MessageType messageType;

    /** 消息数据 */
    private Row data;

    /**
     * 创建流数据包装对象
     *
     * @param messageType 消息类型
     * @param data 消息数据
     * @return 流数据包装对象
     */
    public static StreamData of(MessageType messageType, Row data) {
        return new StreamData(messageType, data);
    }

    /**
     * 从数据中获取session_id
     *
     * @return session_id
     */
    public String getSessionId() {
        try {
            return data.getFieldAs("session_id");
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查是否为会话数据
     *
     * @return 是否为会话数据
     */
    public boolean isSessionData() {
        return MessageType.SESSION.equals(messageType);
    }

    /**
     * 获取协议类型名称（用于向后兼容）
     *
     * @return 协议类型名称
     */
    public String getProtocolType() {
        return messageType.name();
    }
}

package com.geeksec.nta.traffic.etl.etl.graph.extractor.session.edge;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
public class ConnectIPEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.IP_CONNECTS_TO_IP_TAG;
    }

    /**
     * 网络连接发起方 (源IP -> 目的IP) 属性：目的端口
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取源IP
        String sIP = value.getField(FieldConstants.FIELD_SRC_IP).toString();
        // 获取目的IP
        String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
        Integer dPort = Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString());
        return List.of(Row.of(sIP,
                        dIP,
                        0, // rank暂定0
                        dPort));
    }
}

package com.geeksec.nta.traffic.etl.etl.dwd.converter;

import com.geeksec.nta.traffic.etl.etl.dwd.aggregator.SessionAggregator;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DWD行转换器
 * 负责将SessionAggregator聚合的数据转换为DWD层dwd_session_logs表的Row格式
 * 支持单表+JSON数组的架构设计
 *
 * <AUTHOR>
 */
@Slf4j
public class DwdRowConverter implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 将SessionAggregator转换为DWD层的Row
     *
     * @param aggregator 会话聚合器
     * @return DWD层的Row数据
     */
    public Row convert(SessionAggregator aggregator) {
        if (aggregator == null || !aggregator.hasSessionInfo()) {
            log.warn("会话聚合器为空或缺少会话基础信息");
            return null;
        }

        try {
            Row dwdRow = Row.withNames();
            Row sessionInfo = aggregator.getSessionInfo();

            // 添加基础会话标识字段
            addBasicSessionFields(dwdRow, sessionInfo);

            // 添加会话基础信息字段
            addSessionInfoFields(dwdRow, sessionInfo);

            // 添加会话统计信息字段
            addSessionStatisticsFields(dwdRow, sessionInfo);

            // 添加协议指纹信息字段
            addProtocolFingerprintFields(dwdRow, sessionInfo);

            // 添加TCP指纹特征字段
            addTcpFingerprintFields(dwdRow, sessionInfo);

            // 添加会话扩展信息字段
            addSessionExtensionFields(dwdRow, sessionInfo);

            // 添加协议元数据JSON数组字段
            addProtocolJsonArrays(dwdRow, aggregator);

            // 添加系统字段
            addSystemFields(dwdRow);

            log.debug("成功转换会话聚合器为DWD行: sessionId={}, 协议数量={}",
                    aggregator.getSessionId(), aggregator.getProtocolCount());

            return dwdRow;

        } catch (Exception e) {
            log.error("转换会话聚合器为DWD行失败: sessionId={}, error={}",
                    aggregator.getSessionId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加基础会话标识字段
     */
    private void addBasicSessionFields(Row dwdRow, Row sessionInfo) {
        copyFieldIfExists(dwdRow, sessionInfo, "session_id");
        copyFieldIfExists(dwdRow, sessionInfo, "src_ip");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_ip");
        copyFieldIfExists(dwdRow, sessionInfo, "src_port");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_port");
        copyFieldIfExists(dwdRow, sessionInfo, "protocol");
        copyFieldIfExists(dwdRow, sessionInfo, "session_start_time");
        copyFieldIfExists(dwdRow, sessionInfo, "session_start_nsec");
        copyFieldIfExists(dwdRow, sessionInfo, "session_end_time");
        copyFieldIfExists(dwdRow, sessionInfo, "session_end_nsec");
        copyFieldIfExists(dwdRow, sessionInfo, "server_ip");
        copyFieldIfExists(dwdRow, sessionInfo, "app_id");
        copyFieldIfExists(dwdRow, sessionInfo, "app_name");
        copyFieldIfExists(dwdRow, sessionInfo, "thread_id");
        copyFieldIfExists(dwdRow, sessionInfo, "task_id");
        copyFieldIfExists(dwdRow, sessionInfo, "batch_id");
    }

    /**
     * 添加会话基础信息字段
     */
    private void addSessionInfoFields(Row dwdRow, Row sessionInfo) {
        copyFieldIfExists(dwdRow, sessionInfo, "src_mac");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_mac");
        copyFieldIfExists(dwdRow, sessionInfo, "rule_count");
        copyFieldIfExists(dwdRow, sessionInfo, "rule_level");
        copyFieldIfExists(dwdRow, sessionInfo, "syn_data");
        copyFieldIfExists(dwdRow, sessionInfo, "syn_ack_data");
        copyFieldIfExists(dwdRow, sessionInfo, "rule_messages");
    }

    /**
     * 添加会话统计信息字段
     */
    private void addSessionStatisticsFields(Row dwdRow, Row sessionInfo) {
        // 会话统计信息
        copyFieldIfExists(dwdRow, sessionInfo, "src_total_sign");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_total_sign");
        copyFieldIfExists(dwdRow, sessionInfo, "payload_bytes_distribution");
        copyFieldIfExists(dwdRow, sessionInfo, "payload_bytes_count");
        copyFieldIfExists(dwdRow, sessionInfo, "distribution_csq");
        copyFieldIfExists(dwdRow, sessionInfo, "distribution_csqt");
        copyFieldIfExists(dwdRow, sessionInfo, "src_payload_length_distribution");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_payload_length_distribution");
        copyFieldIfExists(dwdRow, sessionInfo, "src_duration_distribution");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_duration_distribution");
        copyFieldIfExists(dwdRow, sessionInfo, "duration_distribution");
        copyFieldIfExists(dwdRow, sessionInfo, "protocol_stack_count");
        copyFieldIfExists(dwdRow, sessionInfo, "protocol_stack");
        copyFieldIfExists(dwdRow, sessionInfo, "src_is_internal");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_is_internal");
        copyFieldIfExists(dwdRow, sessionInfo, "extension_data");

        // TCP连接信息
        copyFieldIfExists(dwdRow, sessionInfo, "src_mss");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_mss");
        copyFieldIfExists(dwdRow, sessionInfo, "src_window_scale");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_window_scale");
        copyFieldIfExists(dwdRow, sessionInfo, "src_payload_max_length");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_payload_max_length");
        copyFieldIfExists(dwdRow, sessionInfo, "src_ack_payload_max_length");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_ack_payload_max_length");
        copyFieldIfExists(dwdRow, sessionInfo, "src_ack_payload_min_length");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_ack_payload_min_length");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_connection_info");
        copyFieldIfExists(dwdRow, sessionInfo, "syn_sequence_numbers");
        copyFieldIfExists(dwdRow, sessionInfo, "syn_sequence_count");
        copyFieldIfExists(dwdRow, sessionInfo, "src_ip_id_offsets");
        copyFieldIfExists(dwdRow, sessionInfo, "dst_ip_id_offsets");
        copyFieldIfExists(dwdRow, sessionInfo, "ssl_block_ciphers");
    }

    /**
     * 添加协议指纹信息字段
     */
    private void addProtocolFingerprintFields(Row dwdRow, Row sessionInfo) {
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_fingerprint");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_fingerprint");
        copyFieldIfExists(dwdRow, sessionInfo, "http_client_fingerprint");
        copyFieldIfExists(dwdRow, sessionInfo, "http_server_fingerprint");
        copyFieldIfExists(dwdRow, sessionInfo, "ssl_client_fingerprint");
        copyFieldIfExists(dwdRow, sessionInfo, "ssl_server_fingerprint");
    }

    /**
     * 添加TCP指纹特征字段
     */
    private void addTcpFingerprintFields(Row dwdRow, Row sessionInfo) {
        // TCP客户端指纹特征
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_ecn_ip_ect");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_df_nonzero_ipid");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_flag_cwr");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_flag_ece");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_zero_timestamp");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_ttl");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_eol_padding_bytes");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_window_scale");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_window_mss_ratio");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_client_options_layout");

        // TCP服务端指纹特征
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_ecn_ip_ect");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_df_nonzero_ipid");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_flag_cwr");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_flag_ece");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_zero_timestamp");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_ttl");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_eol_padding_bytes");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_window_scale");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_window_mss_ratio");
        copyFieldIfExists(dwdRow, sessionInfo, "tcp_server_options_layout");
    }

    /**
     * 添加会话扩展信息字段
     */
    private void addSessionExtensionFields(Row dwdRow, Row sessionInfo) {
        copyFieldIfExists(dwdRow, sessionInfo, "session_duration");
        copyFieldIfExists(dwdRow, sessionInfo, "first_packet_sender");
        copyFieldIfExists(dwdRow, sessionInfo, "device_id");
        copyFieldIfExists(dwdRow, sessionInfo, "first_layer_protocol");
        copyFieldIfExists(dwdRow, sessionInfo, "proxy_ip");
        copyFieldIfExists(dwdRow, sessionInfo, "proxy_port");
        copyFieldIfExists(dwdRow, sessionInfo, "proxy_real_hostname");
        copyFieldIfExists(dwdRow, sessionInfo, "proxy_type");
        copyFieldIfExists(dwdRow, sessionInfo, "processing_start_time");
        copyFieldIfExists(dwdRow, sessionInfo, "processing_end_time");
        copyFieldIfExists(dwdRow, sessionInfo, "rule_labels");
        copyFieldIfExists(dwdRow, sessionInfo, "port_list");
    }

    /**
     * 添加协议元数据JSON数组字段
     */
    private void addProtocolJsonArrays(Row dwdRow, SessionAggregator aggregator) {
        // 为每种协议类型添加JSON数组字段
        addProtocolJsonArray(dwdRow, aggregator, "HTTP", "http_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "SSL", "ssl_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "DNS", "dns_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "SSH", "ssh_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "VNC", "vnc_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "TELNET", "telnet_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "RLOGIN", "rlogin_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "RDP", "rdp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "ICMP", "icmp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "NTP", "ntp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "XDMCP", "xdmcp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "S7", "s7_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "MODBUS", "modbus_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "IEC104", "iec104_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "EIP", "eip_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "OPC", "opc_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "ESP", "esp_protocols");
        addProtocolJsonArray(dwdRow, aggregator, "L2TP", "l2tp_protocols");
    }

    /**
     * 添加单个协议的JSON数组字段
     */
    private void addProtocolJsonArray(Row dwdRow, SessionAggregator aggregator,
                                    String protocolType, String fieldName) {
        List<String> jsonArray = aggregator.getProtocolJsonArray(protocolType);
        if (jsonArray != null && !jsonArray.isEmpty()) {
            dwdRow.setField(fieldName, jsonArray);
        } else {
            dwdRow.setField(fieldName, null);
        }
    }

    /**
     * 添加系统字段
     */
    private void addSystemFields(Row dwdRow) {
        LocalDateTime now = LocalDateTime.now();
        dwdRow.setField("create_time", Timestamp.valueOf(now));
        dwdRow.setField("update_time", Timestamp.valueOf(now));
    }



    /**
     * 复制字段（如果存在）
     */
    private void copyFieldIfExists(Row targetRow, Row sourceRow, String fieldName) {
        try {
            Object value = sourceRow.getField(fieldName);
            targetRow.setField(fieldName, value);
        } catch (Exception e) {
            // 字段不存在，设置为null
            targetRow.setField(fieldName, null);
        }
    }
}

package com.geeksec.nta.traffic.etl.etl.graph.extractor.session.edge;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
public class AppDeployServerEdgeExtractor extends BaseEdgeExtractor {
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.APP_DEPLOYS_ON_SERVER_TAG;
    }

    /**
     * 应用服务部署在服务器 (APPSERVICE -> IP)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        // 获取目标IP
        String dIp = value.getField(FieldConstants.FIELD_DST_IP).toString();
        // 获取应用信息
        Integer dPort = Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString());
        String appName = value.getField(FieldConstants.FIELD_APP_NAME).toString();
        String appKey = dIp + "_" + dPort + "_" + appName;
        return List.of(Row.of(appKey, dIp,
                    0 // rank 暂定0
                ));
    }
}

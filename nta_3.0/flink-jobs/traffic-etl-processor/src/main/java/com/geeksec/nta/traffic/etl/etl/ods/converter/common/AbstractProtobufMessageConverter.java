package com.geeksec.nta.traffic.etl.etl.ods.converter.common;


import com.geeksec.flink.common.utils.time.TimeUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.proto.ZMPNMsg;
import com.google.protobuf.ByteString;
import com.google.protobuf.ProtocolStringList;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Abstract base class for all message-specific protobuf converters.
 * Provides common functionality and defines the interface for all converters.
 *
 * <AUTHOR> Team
 */
@Slf4j
public abstract class AbstractProtobufMessageConverter {

    /**
     * Direction constant for client to server
     */
    protected static final int DIRECTION_CLIENT_TO_SERVER = 0;

    /**
     * Direction constant for server to client
     */
    protected static final int DIRECTION_SERVER_TO_CLIENT = 1;

    /**
     * Convert a protobuf message to a Row representation.
     * This is a template method that calls convertMessage and then adds common
     * metadata.
     *
     * @param msg The protobuf message to convert
     * @return A Row representation of the message with common metadata
     */
    public final Row convert(ZMPNMsg.JKNmsg msg) {
        if (msg == null) {
            log.warn("收到空数据");
            return null;
        }

        try {
            Row row = convertMessage(msg);
            if (row == null) {
                log.debug("跳过空数据映射");
            }
            return row;
        } catch (Exception e) {
            log.error("转换数据时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("转换数据失败", e);
        }
    }

    /**
     * Convert a protobuf message to a Row representation without metadata.
     * Subclasses should implement this method to perform the actual conversion.
     *
     * @param msg The protobuf message to convert
     * @return A Row representation of the message without metadata
     */
    protected abstract Row convertMessage(ZMPNMsg.JKNmsg msg);

    /**
     * 获取对应的输出标签
     * 子类应该重写此方法，返回对应的输出标签
     *
     * @return 输出标签
     */
    public abstract OutputTag<Row> getOutputTag();

    /**
     * Convert a ProtocolStringList to a Java List, filtering out "None" values.
     *
     * @param protoList The protobuf string list to convert
     * @return A filtered Java List
     */
    protected List<String> convertProtoListToJavaList(ProtocolStringList protoList) {
        List<String> list = protoList.stream()
                .filter(s -> !"None".equals(s))
                .collect(Collectors.toList());
        return list;
    }

    /**
     * Convert ByteString to Base64 string representation using CryptoUtils
     *
     * @param bytes The ByteString to convert
     * @return The Base64 string representation
     */
    protected String bytesToBase64String(ByteString bytes) {
        if (bytes == null || bytes.isEmpty()) {
            return "";
        }

        // Convert ByteString to byte array and then encode using Base64
        return java.util.Base64.getEncoder().encodeToString(bytes.toByteArray());
    }

    protected void enrichComMsg(Row row, ZMPNMsg.Comm_msg commMsg) {
        // 设置通用字段
        row.setField(FieldConstants.FIELD_SESSION_ID, commMsg.getSessionId());
        row.setField(FieldConstants.FIELD_SRC_IP, commMsg.getSrcIp());
        row.setField(FieldConstants.FIELD_DST_IP, commMsg.getDstIp());
        row.setField(FieldConstants.FIELD_SRC_PORT, commMsg.getSrcPort());
        row.setField(FieldConstants.FIELD_DST_PORT, commMsg.getDstPort());
        row.setField(FieldConstants.FIELD_IP_PROTOCOL, commMsg.getIppro());
        row.setField(FieldConstants.FIELD_BEGIN_TIME, DateTimeConverter.fromSeconds((commMsg.getBeginTime())));
        row.setField(FieldConstants.FIELD_SERVER_IP, commMsg.getServerIp());
        row.setField(FieldConstants.FIELD_APP_ID, commMsg.getAppId());
        row.setField(FieldConstants.FIELD_APP_NAME, commMsg.getAppName());
        row.setField(FieldConstants.FIELD_THREAD_ID, commMsg.getThreadId());
        row.setField(FieldConstants.FIELD_TASK_ID, commMsg.getTaskId());
        row.setField(FieldConstants.FIELD_BEGIN_NSEC, commMsg.getBeginNsec());
        row.setField(FieldConstants.FIELD_BATCH_ID, commMsg.getBatchId());
    }
}

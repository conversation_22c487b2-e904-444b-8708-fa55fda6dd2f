package com.geeksec.nta.traffic.etl.etl.graph.extractor.dns.edge;

import com.geeksec.flink.common.network.DomainUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.network.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description null.java
 * @Date 9:30$ 2025/6/18$
 **/
@Slf4j
public class DomainResolvesIPEdgeExtractor extends BaseEdgeExtractor {
    private static final ObjectMapper mapper = new ObjectMapper();
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.DOMAIN_RESOLVES_TO_IP_TAG;
    }

    /**
     * 域名解析到IP (DOMAIN -> IP)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        List<Row> edges = new ArrayList<>();
        // 获取源IP和域名
        String domain = value.getField(FieldConstants.FIELD_DNS_DOMAIN).toString();
        String dnsAnsStr = value.getField(FieldConstants.FIELD_DNS_ANSWER).toString();
        if (dnsAnsStr.isEmpty() || DomainUtils.isValidDomain(domain)) {
            return edges;
        }

        // 解析DNS查询数据
        try {
            List<Map<String, Object>> dnsAnsMaps = mapper.readValue(dnsAnsStr, new TypeReference<List<Map<String, Object>>>() {});
            for (Map<String, Object> dnsAns : dnsAnsMaps) {
                Integer type = Integer.valueOf(dnsAns.get("type").toString());
                domain = DomainUtils.formatDomain(domain);
                String domainIP = dnsAns.get("value").toString();
                Integer ttl = Integer.valueOf(dnsAns.get("ttl").toString());
                if (type == 1 || type == 28) {
                    edges.add(Row.of(domain, domainIP,
                            0, // rank暂定0
                            true, // 是否是DNS解析
                            ttl, ttl
                    ));
                } else if (type == 5) {
                    edges.add(Row.of(domain, domainIP,
                            0, // rank暂定0
                            false, // 是否是DNS解析
                            ttl, ttl
                    ));
                }
            }
        } catch (JsonProcessingException | NumberFormatException e) {
            log.error("解析DNS查询数据JSON失败", e);
        }

        return edges;
    }
}

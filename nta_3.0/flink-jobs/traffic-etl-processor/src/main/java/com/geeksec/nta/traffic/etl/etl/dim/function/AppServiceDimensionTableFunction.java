package com.geeksec.nta.traffic.etl.etl.dim.function;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.dim.DimensionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.geeksec.common.utils.time.TimeUtils.DORIS_DATETIME_FORMATTER;


/**
 * 应用服务维度表处理函数，用于生成符合dim_app_service表结构的维度数据
 * <p>
 * 使用Flink Backend State进行缓存管理，支持TTL自动清理
 *
 * <AUTHOR>
 */
@Slf4j
public class AppServiceDimensionTableFunction extends ProcessFunction<Row, Row> {
    private static final long serialVersionUID = 1L;
    /**
     * 应用服务维度数据输出标签
     */
    public static final OutputTag<Row> APP_SERVICE_DIM_TAG = new OutputTag<Row>("app-service-dimension") {};
    public static final OutputTag<Row> APP_SERVICE_VERTEX_TAG = new OutputTag<Row>("app-service-tag") {};
    private final String serviceNameFieldName;
    private final Duration ttl;
    // State for caching App Service dimension data
    private transient MapState<String, Map<String, Object>> appServiceDimensionState;

    /**
     * 构造函数
     *
     * @param serviceNameFieldName 服务名称字段名
     */
    public AppServiceDimensionTableFunction(String serviceNameFieldName) {
        this(serviceNameFieldName, Duration.ofHours(24)); // 默认24小时TTL
    }

    /**
     * 构造函数
     *
     * @param serviceNameFieldName 服务名称字段名
     * @param ttl                  状态TTL时间
     */
    public AppServiceDimensionTableFunction(String serviceNameFieldName, Duration ttl) {
        this.serviceNameFieldName = serviceNameFieldName;
        this.ttl = ttl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(ttl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化应用服务维度状态
        MapStateDescriptor<String, Map<String, Object>> appServiceStateDescriptor =
                new MapStateDescriptor("app-service-dimension-state",
                        TypeInformation.of(new TypeHint<String>() {}),
                        TypeInformation.of(new TypeHint<Map<String, Object>>() {}));
        appServiceStateDescriptor.enableTimeToLive(ttlConfig);
        appServiceDimensionState = getRuntimeContext().getMapState(appServiceStateDescriptor);
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        try {
            String appName = getStringFieldValue(value, serviceNameFieldName);
            String serviceName = value.getField(FieldConstants.FIELD_DST_IP) + "_" + value.getField(FieldConstants.FIELD_DST_PORT) + "_" + appName;

            if (appName == null || appName.trim().isEmpty()) {
                log.warn("应用名称为空，使用默认值: {}", appName);
                return;
            }

            // 更新state map状态
            try {
                Map<String, Object> existingAppInfo = appServiceDimensionState.get(serviceName);
                if (existingAppInfo == null) {
                    appServiceDimensionState.put(serviceName, Map.of(
                            FieldConstants.KEY_APP_DIP, value.getField(FieldConstants.FIELD_DST_IP).toString(),
                            FieldConstants.KEY_APP_NAME, appName,
                            FieldConstants.KEY_APP_DPORT, Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString()),
                            FieldConstants.KEY_APP_IPPRO, value.getField(FieldConstants.FIELD_IP_PROTOCOL).toString(),
                            FieldConstants.KEY_THREAT_SCORE, 0,
                            FieldConstants.KEY_TRUST_SCORE, 100,
                            FieldConstants.KEY_DW_CREATION_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER),
                            FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER)));
                } else {
                    appServiceDimensionState.get(serviceName).put(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER));
                }
            } catch (Exception e) {
                log.error("更新状态数据时发生错误: {}", e.getMessage());
            }

            ctx.output(APP_SERVICE_DIM_TAG, createDimensionRow(serviceName, value));
            ctx.output(APP_SERVICE_VERTEX_TAG, createVertexRow(serviceName, value));
            // 继续传递原始数据
            out.collect(value);
        } catch (Exception e) {
            log.error("处理应用服务维度数据时发生错误: {}", e.getMessage());
            out.collect(value);
        }
    }

    /**
     * 创建符合维度表结构的应用服务维度记录
     *
     * @param serviceName 服务名称
     * @param value       会话元数据
     * @return 符合dim_app_service表结构的Row
     */
    private Row createDimensionRow(String serviceName, Row value) {
        try {
            Map<String, Object> appServiceMapState = appServiceDimensionState.get(serviceName);
            Row dimensionRow = Row.withNames();
            dimensionRow.setField("service_name", serviceName);
            dimensionRow.setField("ip", appServiceMapState.get(FieldConstants.KEY_APP_DIP));
            dimensionRow.setField("app_name", appServiceMapState.get(FieldConstants.KEY_APP_NAME));
            dimensionRow.setField("dport", appServiceMapState.get(FieldConstants.KEY_APP_DPORT));
            dimensionRow.setField("ip_protocol", appServiceMapState.get(FieldConstants.KEY_APP_IPPRO));
            dimensionRow.setField("threat_score", appServiceMapState.get(FieldConstants.KEY_THREAT_SCORE));
            dimensionRow.setField("trust_score", appServiceMapState.get(FieldConstants.KEY_TRUST_SCORE));
            DimensionProcessor.insertTimeStateMap(appServiceDimensionState, serviceName, dimensionRow);

            return dimensionRow;
        } catch (Exception e) {
            log.error("创建应用服务维度数据时发生错误: {}", e.getMessage());
            Row dimensionRow = Row.withNames();
            dimensionRow.setField("service_name", serviceName);
            dimensionRow.setField("ip", value.getField(FieldConstants.FIELD_DST_IP).toString());
            dimensionRow.setField("app_name", value.getField(FieldConstants.FIELD_APP_NAME).toString());
            dimensionRow.setField("dport", Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString()));
            dimensionRow.setField("ip_protocol", value.getField(FieldConstants.FIELD_IP_PROTOCOL).toString());
            dimensionRow.setField("threat_score", 0);
            dimensionRow.setField("trust_score", 100);
            DimensionProcessor.insertTimeStateMap(appServiceDimensionState, serviceName, dimensionRow);

            return dimensionRow;
        }
    }

    /**
     * 创建符合图空间APPSERVICE TAG结构的应用服务维度记录
     *
     * @param serviceName 服务名称
     * @param value       会话元数据
     * @return 符合dim_app_service表结构的Row
     */
    private Row createVertexRow(String serviceName, Row value) {
        try {
            Map<String, Object> appServiceMapState = appServiceDimensionState.get(serviceName);
            // 构建Row
            return Row.of(
                    serviceName, // vid
                    appServiceMapState.get(FieldConstants.KEY_APP_DIP),
                    appServiceMapState.get(FieldConstants.KEY_APP_NAME),
                    appServiceMapState.get(FieldConstants.KEY_APP_DPORT),
                    appServiceMapState.get(FieldConstants.KEY_APP_IPPRO),
                    appServiceMapState.get(FieldConstants.KEY_THREAT_SCORE),
                    appServiceMapState.get(FieldConstants.KEY_TRUST_SCORE)
            );
        } catch (Exception e) {
            log.error("创建应用服务VERTEX数据时发生错误: {}", e.getMessage());
            // 构建Row
            return Row.of(serviceName, value.getField(FieldConstants.FIELD_DST_IP).toString(),
                    value.getField(FieldConstants.FIELD_APP_NAME).toString(), Integer.valueOf(value.getField(FieldConstants.FIELD_DST_PORT).toString()),
                    value.getField(FieldConstants.FIELD_IP_PROTOCOL).toString(),
                    0, 100
            );
        }
    }

    /**
     * 从Row中获取字符串字段值
     *
     * @param row       Row对象
     * @param fieldName 字段名
     * @return 字段值，如果不存在或类型不匹配则返回null
     */
    private String getStringFieldValue(Row row, String fieldName) {
        try {
            Set<String> fieldNames = row.getFieldNames(true);
            if (fieldNames != null && fieldNames.contains(fieldName)) {
                Object fieldValue = row.getField(fieldName);
                if (fieldValue instanceof String) {
                    return (String) fieldValue;
                }
            }
        } catch (Exception e) {
            log.warn("获取字符串字段值时发生错误，字段名: {}, 错误: {}", fieldName, e.getMessage());
        }
        return null;
    }
}

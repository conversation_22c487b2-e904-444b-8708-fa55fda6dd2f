package com.geeksec.nta.traffic.etl.etl.graph.extractor.ssl.edge;

import com.geeksec.flink.common.network.DomainUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.common.network.DomainUtils;
import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import com.geeksec.nta.traffic.etl.etl.graph.extractor.base.BaseEdgeExtractor;
import com.geeksec.nta.traffic.etl.sink.tag.NebulaGraphOutputTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @Description null.java
 * @Date 17:11$ 2025/6/17$
 **/
@Slf4j
public class FingerprintAppearsWithCertEdgeExtractor extends BaseEdgeExtractor {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    @Override
    public OutputTag<Row> getOutputTag() {
        return NebulaGraphOutputTag.Edge.FINGERPRINT_APPEARS_WITH_CERT_TAG;
    }

    /**
     * TLS指纹与特定证书在同一TLS会话中出现 (客户端指纹 -> 服务端证书)
     *
     * @param value 元数据
     * @return
     */
    @Override
    public List<Row> extractEdge(Row value) {
        try {
            String dIP = value.getField(FieldConstants.FIELD_DST_IP).toString();
            String sni = value.getField(FieldConstants.FIELD_SSL_HELLO_C_SERVERNAME).toString();
            String sSSLFinger = value.getField(FieldConstants.FIELD_SSL_S_FINGER).toString();
            String dCertHash = value.getField(FieldConstants.FIELD_SSL_CERT_S_HASH).toString();
            List<String> dCertHashes = objectMapper.readValue(dCertHash, new TypeReference<List<String>>() {});

            if (StringUtils.isNotEmpty(sSSLFinger) && !"0".equals(sSSLFinger)){
                return Collections.emptyList();
            }

            if (CollectionUtils.isEmpty(dCertHashes)){
                return Collections.emptyList();
            }

            if (!DomainUtils.isValidDomain(sni) || sni.equals(dIP)){
                return Collections.emptyList();
            }

            sni = DomainUtils.formatDomain(sni);
            return List.of(Row.of(sSSLFinger, dCertHashes.get(0).toString(),
                    0, // rank暂定0
                    sni
            ));
        } catch (JsonProcessingException e) {
            log.error("Parse SSL CertHash Error {}", e.getMessage());
        }

        return Collections.emptyList();
    }
}

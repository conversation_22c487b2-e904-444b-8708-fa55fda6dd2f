package com.geeksec.nta.traffic.etl.etl.graph.extractor.ssl;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.List;

/**
 * 会话边提取器
 * 从网络会话中提取边关系数据，包括IP绑定关系、连接关系和应用关系
 *
 * <AUTHOR> Team
 */
@Slf4j
public class SSLEdgeExtractor extends ProcessFunction<Row, Row> {

    /**
     * 从会话信息中生成各类边关系
     *
     * @param value 数据映射
     * @param collector 收集器
     * @throws Exception 如果处理失败
     */
    @Override
    public void processElement(Row value, Context ctx, Collector<Row> collector) throws Exception {
        try {
            SSLEdgeExtractorFactory.getEdgeExtractors().forEach(edgeExtractor -> {
                List<Row> edges = edgeExtractor.extractEdge(value);
                for (Row edge : edges){
                    if (edge != null){
                        ctx.output(edgeExtractor.getOutputTag(), edge);
                    }
                }
            });
            collector.collect(value);
        } catch (Exception e) {
            log.error("SSLEdgeExtractor process error: {}", e.getMessage());
        }
    }
}

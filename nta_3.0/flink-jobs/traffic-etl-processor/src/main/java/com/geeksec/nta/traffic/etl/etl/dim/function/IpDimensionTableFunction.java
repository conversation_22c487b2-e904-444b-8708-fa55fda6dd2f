package com.geeksec.nta.traffic.etl.etl.dim.function;

import com.geeksec.flink.common.network.NetworkUtils;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.geeksec.nta.traffic.etl.etl.constant.FieldConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.StateTtlConfig;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

//// import com.geeksec.flink.common.utils.knowledgebase.GeoIpManager;
//// import com.geeksec.flink.common.utils.knowledgebase.IpWhoisManager;
import com.maxmind.geoip2.model.AsnResponse;
import com.maxmind.geoip2.model.CityResponse;

import lombok.extern.slf4j.Slf4j;

import static com.geeksec.common.utils.time.TimeUtils.DORIS_DATETIME_FORMATTER;

/**
 * IP维度表处理函数
 * 专门用于生成符合dim_ipv4/dim_ipv6表结构的维度数据
 * 使用Flink Backend State管理IP地理信息缓存
 *
 * <AUTHOR>
 */
@Slf4j
public class IpDimensionTableFunction extends ProcessFunction<Row, Row> {

    /**
     * IPv4维度侧输出标签
     */
    public static final OutputTag<Row> IPV4_DIM_TAG = new OutputTag<Row>("ipv4_dimension") {
    };

    /**
     * IPv6维度侧输出标签
     */
    public static final OutputTag<Row> IPV6_DIM_TAG = new OutputTag<Row>("ipv6_dimension") {
    };
    public static final OutputTag<Row> IP_VERTEX_TAG = new OutputTag<Row>("ip_tag") {
    };

    /**
     * IP信息缓存状态，使用MapState管理
     */
    private transient MapState<String, Map<String, Object>> ipInfoCache;

    /**
     * 缓存TTL配置，默认12小时
     */
    private final Duration cacheTtl;

    /**
     * IP字段名
     */
    private final String ipFieldName;

    /**
     * IP WHOIS管理器
     */
    private transient IpWhoisManager ipWhoisManager;

    /**
     * 构造函数
     *
     * @param ipFieldName IP字段名
     */
    public IpDimensionTableFunction(String ipFieldName) {
        this(ipFieldName, Duration.ofHours(12));
    }

    /**
     * 构造函数
     *
     * @param ipFieldName IP字段名
     * @param cacheTtl    缓存TTL时间
     */
    public IpDimensionTableFunction(String ipFieldName, Duration cacheTtl) {
        this.ipFieldName = ipFieldName;
        this.cacheTtl = cacheTtl;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);

        // 配置状态TTL
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(cacheTtl)
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 创建MapState描述符
        MapStateDescriptor<String, Map<String, Object>> descriptor = new MapStateDescriptor<>(
                "ip-dimension-cache",
                TypeInformation.of(new TypeHint<String>() {
                }),
                TypeInformation.of(new TypeHint<Map<String, Object>>() {
                }));

        // 启用TTL
        descriptor.enableTimeToLive(ttlConfig);
        // 获取状态
        ipInfoCache = getRuntimeContext().getMapState(descriptor);
        // 初始化IP WHOIS管理器
        ipWhoisManager = IpWhoisManager.getInstance();
    }

    @Override
    public void processElement(Row value, Context ctx, Collector<Row> out) throws Exception {
        // 获取IP地址
        String ipAddress = getIpFromRow(value);
        Set<String> ipSet = new HashSet<>();
        // domain_ip: **************|*************|************|*************|**********|***********
        if (ipAddress.contains("|")) {
            ipSet.addAll(Arrays.asList(ipAddress.split("\\|")));
        } else {
            ipSet.add(ipAddress);
        }
        for (String ip : ipSet) {
            if (StringUtils.isEmpty(ip) || !NetworkUtils.isValidIp(ip)) {
                // IP无效，直接输出原始数据
                out.collect(value);
                return;
            }
            // 获取丰富化的IP信息
            Map<String, Object> enrichedInfo = getEnrichedIpInfo(ip, value);
            // 创建维度表记录
            Row dimensionRow = createDimensionRow(ip, enrichedInfo);

            // 根据IP类型输出到不同的侧输出流
            if (NetworkUtils.isValidIpv6(ip)) {
                ctx.output(IPV6_DIM_TAG, dimensionRow);
                ctx.output(IP_VERTEX_TAG, createVertexRow(ip, "V6", enrichedInfo));
            } else {
                ctx.output(IPV4_DIM_TAG, dimensionRow);
                ctx.output(IP_VERTEX_TAG, createVertexRow(ip, "V4", enrichedInfo));
            }
            // 输出原始数据
            out.collect(value);
        }
    }

    /**
     * 从Row中获取IP地址
     *
     * @param row 输入Row
     * @return IP地址字符串
     */
    private String getIpFromRow(Row row) {
        try {
            Object ipValue = row.getField(ipFieldName);
            return ipValue != null ? ipValue.toString() : null;
        } catch (Exception e) {
            log.warn("无法从Row中获取IP字段 {}: {}", ipFieldName, e.getMessage());
            return null;
        }
    }

    /**
     * 获取丰富化的IP信息，优先从缓存获取
     *
     * @param ipAddress IP地址
     * @param value
     * @return 丰富化的IP信息
     * @throws Exception 状态访问异常
     */
    private Map<String, Object> getEnrichedIpInfo(String ipAddress, Row value) throws Exception {
        // 检查缓存
        Map<String, Object> cachedInfo = ipInfoCache.get(ipAddress);
        if (cachedInfo != null) {
            return cachedInfo;
        }

        // 缓存未命中，重新查询并缓存
        Map<String, Object> enrichedInfo = enrichIpInfo(ipAddress, value);
        ipInfoCache.put(ipAddress, enrichedInfo);

        return enrichedInfo;
    }

    /**
     * 丰富IP信息
     *
     * @param ipAddress IP地址
     * @param value
     * @return 丰富后的IP信息Map
     */
    private Map<String, Object> enrichIpInfo(String ipAddress, Row value) {
        Map<String, Object> ipInfo = new HashMap<>(16);
        ipInfo.put("ip", ipAddress);
        ipInfo.put("is_valid", true);

        // 检查是否为内网IP
        boolean isInternal = NetworkUtils.isPrivateIp(ipAddress);
        ipInfo.put("is_internal", isInternal);
        ipInfo.put("total_bytes", Long.parseLong(value.getField(FieldConstants.FIELD_PKT_DPAYLOADBYTES).toString()) + Long.parseLong(value.getField(FieldConstants.FIELD_PKT_SPAYLOADBYTES).toString()));
        ipInfo.put("total_packets", Long.parseLong(value.getField(FieldConstants.FIELD_PKT_SBYTES).toString()) + Long.parseLong(value.getField(FieldConstants.FIELD_PKT_DBYTES).toString()));
        ipInfo.put("recv_total_bytes", Long.parseLong(value.getField(FieldConstants.FIELD_PKT_DPAYLOADBYTES).toString()));
        ipInfo.put("send_total_bytes", Long.parseLong(value.getField(FieldConstants.FIELD_PKT_SPAYLOADBYTES).toString()));
        if (isInternal) {
            ipInfo.put("country", "内网");
            ipInfo.put("province", "内网");
            ipInfo.put("city", "内网");
            return ipInfo;
        }

        // 添加地理位置信息
        enrichGeoInfo(ipAddress, ipInfo);
        // 添加IP WHOIS信息
        enrichIpWhoisInfo(ipAddress, ipInfo);
        // 添加创建时间和更新时间
        ipInfo.put(FieldConstants.KEY_DW_CREATION_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER));
        ipInfo.put(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP, LocalDateTime.now().format(DORIS_DATETIME_FORMATTER));

        return ipInfo;
    }

    /**
     * 丰富IP地理位置信息
     *
     * @param ipAddress IP地址
     * @param ipInfo    IP信息Map
     */
    private void enrichGeoInfo(String ipAddress, Map<String, Object> ipInfo) {
        try {
            // 获取城市信息
            CityResponse cityResponse = GeoIpManager.getAddrInfo(ipAddress);
            if (cityResponse != null) {
                addCityInfo(cityResponse, ipInfo);
            }

            // 获取ASN信息
            AsnResponse asnResponse = GeoIpManager.getAsnInfo(ipAddress);
            if (asnResponse != null) {
                addAsnInfo(asnResponse, ipInfo);
            }
        } catch (Exception e) {
            log.warn("获取IP地理信息失败: {}", ipAddress, e);
        }
    }

    /**
     * 添加城市信息到IP信息Map
     *
     * @param cityResponse 城市响应
     * @param ipInfo       IP信息Map
     */
    private void addCityInfo(CityResponse cityResponse, Map<String, Object> ipInfo) {
        // 添加国家信息
        String country = GeoIpManager.getCountry(cityResponse);
        if (!StringUtils.isEmpty(country)) {
            ipInfo.put("country", country);
        }

        // 添加国家代码
        String countryCode = cityResponse.getCountry().getIsoCode();
        if (!StringUtils.isEmpty(countryCode)) {
            ipInfo.put("country_code", countryCode);
        }

        // 添加省份信息
        String province = GeoIpManager.getProvince(cityResponse);
        if (!StringUtils.isEmpty(province)) {
            ipInfo.put("province", province);
        }

        // 添加城市信息
        String city = GeoIpManager.getCity(cityResponse);
        if (!StringUtils.isEmpty(city)) {
            ipInfo.put("city", city);
        }

        // 添加经纬度
        Double latitude = GeoIpManager.getLatitude(cityResponse);
        Double longitude = GeoIpManager.getLongitude(cityResponse);
        if (latitude != 0.0 && longitude != 0.0) {
            ipInfo.put("latitude", latitude);
            ipInfo.put("longitude", longitude);
        }

        // 添加邮政编码
        if (cityResponse.getPostal() != null) {
            String postalCode = cityResponse.getPostal().getCode();
            if (!StringUtils.isEmpty(postalCode)) {
                ipInfo.put("postal_code", postalCode);
            }
        }

        // 添加大洲信息
        if (cityResponse.getContinent() != null && cityResponse.getContinent().getNames() != null) {
            String continent = cityResponse.getContinent().getNames().get("zh-CN");
            if (!StringUtils.isEmpty(continent)) {
                ipInfo.put("continent", continent);
            }
        }
    }

    /**
     * 添加ASN信息到IP信息Map
     *
     * @param asnResponse ASN响应
     * @param ipInfo      IP信息Map
     */
    private void addAsnInfo(AsnResponse asnResponse, Map<String, Object> ipInfo) {
        String asn = GeoIpManager.getAsn(asnResponse);
        if (!StringUtils.isEmpty(asn)) {
            ipInfo.put("asn", asn);
        }

        String asnOrg = asnResponse.getAutonomousSystemOrganization();
        if (!StringUtils.isEmpty(asnOrg)) {
            ipInfo.put("asn_org", asnOrg);
        }
    }

    /**
     * 丰富IP WHOIS信息
     *
     * @param ipAddress IP地址
     * @param ipInfo    IP信息Map
     */
    private void enrichIpWhoisInfo(String ipAddress, Map<String, Object> ipInfo) {
        try {
            // 获取IP WHOIS信息
            IpWhoisManager.IpWhoisInfo whoisInfo = ipWhoisManager.getIpWhoisInfo(ipAddress);
            if (whoisInfo != null) {
                // 添加网络名称
                String netname = whoisInfo.getNetname();
                if (StringUtils.isNotEmpty(netname)) {
                    ipInfo.put("netname", netname);
                }

                // 添加描述信息
                String description = whoisInfo.getDescription();
                if (StringUtils.isNotEmpty(description)) {
                    ipInfo.put("whois_description", description);
                }

                // 添加WHOIS国家信息（可能与GeoIP不同）
                String whoisCountry = whoisInfo.getCountry();
                if (StringUtils.isNotEmpty(whoisCountry)) {
                    ipInfo.put("whois_country", whoisCountry);
                }

                // 添加状态信息
                String status = whoisInfo.getStatus();
                if (StringUtils.isNotEmpty(status)) {
                    ipInfo.put("whois_status", status);
                }

                // 添加维护者信息
                String maintainer = whoisInfo.getMaintainer();
                if (StringUtils.isNotEmpty(maintainer)) {
                    ipInfo.put("whois_maintainer", maintainer);
                }

                // 添加组织摘要信息
                String orgSummary = whoisInfo.getOrganizationSummary();
                if (StringUtils.isNotEmpty(orgSummary)) {
                    ipInfo.put("whois_organization", orgSummary);
                }

                log.debug("IP {} WHOIS信息: {}", ipAddress, orgSummary);
            } else {
                log.debug("未找到IP {} 的WHOIS信息", ipAddress);
            }
        } catch (Exception e) {
            log.warn("获取IP WHOIS信息失败: {}", ipAddress, e);
        }
    }

    /**
     * 创建符合维度表结构的IP维度记录
     *
     * @param ipAddress      IP地址
     * @param enrichmentInfo 丰富化信息
     * @return 符合dim_ipv4/dim_ipv6表结构的Row
     */
    private Row createDimensionRow(String ipAddress, Map<String, Object> enrichmentInfo) {
        Row dimensionRow = Row.withNames();

        // 设置主键字段
        dimensionRow.setField("ip", ipAddress);

        // 设置地理位置信息字段
        dimensionRow.setField("city", enrichmentInfo.get("city"));
        dimensionRow.setField("country", enrichmentInfo.get("country"));
        dimensionRow.setField("country_code", enrichmentInfo.get("country_code"));
        dimensionRow.setField("province", enrichmentInfo.get("province"));
        dimensionRow.setField("latitude", enrichmentInfo.get("latitude"));
        dimensionRow.setField("longitude", enrichmentInfo.get("longitude"));
        dimensionRow.setField("postal_code", enrichmentInfo.get("postal_code"));
        dimensionRow.setField("continent", enrichmentInfo.get("continent"));

        // 设置ASN信息字段
        dimensionRow.setField("asn", enrichmentInfo.get("asn"));
        dimensionRow.setField("asn_org", enrichmentInfo.get("asn_org"));

        // 设置IP WHOIS信息字段
        dimensionRow.setField("netname", enrichmentInfo.get("netname"));
        dimensionRow.setField("whois_description", enrichmentInfo.get("whois_description"));
        dimensionRow.setField("whois_country", enrichmentInfo.get("whois_country"));
        dimensionRow.setField("whois_status", enrichmentInfo.get("whois_status"));
        dimensionRow.setField("whois_maintainer", enrichmentInfo.get("whois_maintainer"));
        dimensionRow.setField("whois_organization", enrichmentInfo.get("whois_organization"));

        // 设置是否内网IP
        dimensionRow.setField("is_internal", enrichmentInfo.get("is_internal"));

        // 设置默认值字段
        dimensionRow.setField("threat_score", null);
        dimensionRow.setField("trust_score", null);
        dimensionRow.setField("remark", null);

        // 设置聚合字段初始值
        dimensionRow.setField("total_bytes", Long.parseLong(enrichmentInfo.get("total_bytes").toString()));
        dimensionRow.setField("total_packets", Long.parseLong(enrichmentInfo.get("total_packets").toString()));
        dimensionRow.setField("recv_total_bytes", Long.parseLong(enrichmentInfo.get("recv_total_bytes").toString()));
        dimensionRow.setField("send_total_bytes", Long.parseLong(enrichmentInfo.get("send_total_bytes").toString()));
        dimensionRow.setField("session_count", 1);
        dimensionRow.setField("total_duration", 0L);

        // 设置时间字段
        String createTime = enrichmentInfo.get(FieldConstants.KEY_DW_CREATION_TIMESTAMP).toString();
        String updateTime = enrichmentInfo.get(FieldConstants.KEY_DW_LAST_UPDATED_TIMESTAMP).toString();
        dimensionRow.setField("first_seen", createTime);
        dimensionRow.setField("last_seen", updateTime);
        dimensionRow.setField("create_time", createTime);
        dimensionRow.setField("update_time", updateTime);

        return dimensionRow;
    }

    /**
     * 创建符合维IP TAG结构的记录
     *
     * @param ipAddress      IP地址
     * @param enrichmentInfo 丰富化信息
     * @return 符合IP TAG结构的Row
     */
    private Row createVertexRow(String ipAddress, String version, Map<String, Object> enrichmentInfo) {
        return Row.of(ipAddress, // vid
                ipAddress, version, enrichmentInfo.get("city"), enrichmentInfo.get("country"),
                0, 100, ""
                );
    }
}

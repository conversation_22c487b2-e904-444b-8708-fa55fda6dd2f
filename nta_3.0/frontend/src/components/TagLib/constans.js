// 标签类型
export const tagsType = {
  ip: 0,
  端口: 1,
  应用: 2,
  域名: 3,
  证书: 4,
  MAC目标: 5,
  会话: 6,
  指纹: 7,
  全部: 9999,
};
// 细分类果询
export const tagsAttribute = {
  功能描述: 1,
  功能代理: 2,
  合法性: 3,
  基础属性: 4,
  威胁: 5,
  加密流量检测: 6,
  APT: 7,
  行为描述: 8,
  指纹描述: 9,
  // 威胁: 1,
  // 行为描述: 2,
  // APT: 3,
  // 远程控制: 4,
  // 功能描述: 5,
  // 代理: 6,
  // 指纹描述: 7,
  // 合法性: 8,
  // 基础属性: 9,
  // 加密流量检测: 10,
  // 知识库: 11,
  // 合法性: 12,
  // 知识库加密流量检测: 13,
  // 行为检测模块: 14,
  // 攻击入侵: 15,
  // 行为描述: 16,
  // 命令与控制: 17,
  // 身份欺骗: 18,
  // 翻墙上网: 19,
  // 中间人: 20,
  // APT: 21,
  // 私有检测: 22,
  // 高维度标签: 23,
};

// 标签
// 标签颜色
export const TAG_COLOR = {
  danger: 0,
  warning: 1,
  success: 2,
  positive: 3,
  info: 4,
};

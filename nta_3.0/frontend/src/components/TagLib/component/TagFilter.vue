<template>
  <div class="filter">
    <div class="list">
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :label-position="labelPosition"
      >
        <label class="form-label">黑名单权重</label>
        <div class="flex">
          <el-form-item>
            <el-input-number
              v-model="ruleForm.hStart"
              :min="0"
              :max="100"
              :step="1"
              :controls="false"
            ></el-input-number>
          </el-form-item>
          <el-form-item>
            <div class="mg6">~</div>
          </el-form-item>
          <el-form-item>
            <el-input-number
              v-model="ruleForm.hEnd"
              :min="0"
              :max="100"
              :step="1"
              :controls="false"
            ></el-input-number>
          </el-form-item>
        </div>
        <label class="form-label">白名单权重</label>
        <div class="flex">
          <el-form-item>
            <el-input-number
              v-model="ruleForm.bStart"
              :min="0"
              :max="100"
              :step="1"
              :controls="false"
            ></el-input-number>
          </el-form-item>
          <el-form-item>
            <div class="mg6">~</div>
          </el-form-item>
          <el-form-item>
            <el-input-number
              v-model="ruleForm.bEnd"
              :min="0"
              :max="100"
              :step="1"
              :controls="false"
            ></el-input-number>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="bottom">
      <div class="handle-btn">
        <el-button
          size="mini"
          @click="cancel"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="handleSave"
        >
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name:'TagFilter',
  data() {
    return {
      ruleForm:{
        hStart:0,
        hEnd:100,
        bStart:0,
        bEnd:100,
      },
      labelPosition:'top'
    };
  },
  methods: {
    // 取消
    cancel() {
      this.$emit('handleSave', null);
    },
    // 确定
    handleSave() {
      this.$emit('handleSave', this.ruleForm);
    },
    reset(){
      this.ruleForm.hStart=0;
      this.ruleForm.hEnd=100;
      this.ruleForm.bStart=0;
      this.ruleForm.bEnd=100;
    }
  }
};
</script>

<style lang="scss" scoped>
.filter{
    position: relative;
.list {
  background: #ffffff;
  padding: 8px 12px 56px;
  width: 180px;
  max-height: 240px;
  overflow: auto;
  .form-label{
    font-size: 14px;
    font-weight: 500;
  }
  .flex{
    margin-top: 8px;
    ::v-deep{
        .el-input,.el-input-number{
            width: 70px;
        }
    }
  }
}
  .bottom{
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: absolute;
    width: 100%;
    height: 48px;
    bottom: -6px;
    right: 0;
    background: #F7F8FA;
  }
}
</style>

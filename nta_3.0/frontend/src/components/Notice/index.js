import Notice from './index.vue'

export default function (Vue) {
  const Constructor = Vue.extend(Notice)
  const Instance = new Constructor()
  Instance.$mount()
  document.body.appendChild(Instance.$el)
  Vue.prototype.$Notice = (content) => {
    Instance.showConfirm = true
    Instance.content = content
    return new Promise((resolve, reject) => {
      Instance.confirm = function () {
        resolve()
        Instance.showConfirm = false
      }
      Instance.close = function () {
        reject()
        Instance.showConfirm = false
      }
    })
  }
}

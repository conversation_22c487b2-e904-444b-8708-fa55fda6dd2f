<template>
  <div class="ifonconduct">
    <!-- 更换硬盘 -->
    <div>
      <!-- 第一步 -->
      <div
        v-if="HDDdialogdata"
        class="step1"
      >
        <el-dialog
          :visible.sync="HDDdialogdata"
          width="35%"
          :close-on-click-modal="false"
          @close="closedialogHDD"
        >
          <span
            slot="title"
            class="title"
          > 更换硬盘 </span>
          <div class="HDDbox">
            <div class="HDDbox-top">
              <div class="HDDbox-top-l">
                当前硬盘模式：<span>raid5模式</span>
              </div>
              <div class="HDDbox-top-r">
                <el-button
                  v-show="!diskstatus.state == 0"
                  @click="nextHDDstep"
                >
                  更换硬盘
                </el-button>
                <!-- TODO 测试所需  展示停用:disabled="!showhint" -->
                <el-button
                  v-show="diskstatus.state == 0"
                  @click="nextHDDrecombine"
                >
                  重组数据盘
                </el-button>
              </div>
            </div>
            <div
              v-if="!diskstatus.state == 0"
              class="HDDbox-down"
            >
              <div
                v-for="(item, index) in hddData.used.raid"
                :key="index"
                class="bigbox"
              >
                <div class="HDDbox-down-info">
                  <div
                    v-show="mount"
                    class="tophint"
                  >
                    <img src="../../assets/images/HDDmount.png" />
                  </div>
                  <div
                    v-show="!mount"
                    class="tophint"
                  >
                    <img src="../../assets/images/HDDnomount.png" />
                  </div>
                  <div class="HDDbox-down-info-l">
                    <img src="../../assets/images/HDD.png" />
                    <div class="text">{{ item.dev }}</div>
                  </div>
                  <div class="HDDbox-down-info-r">
                    <div class="HDDbox-down-info-r-top">
                      <!-- <el-tooltip
                        content="Top center"
                        placement="top"
                        effect="light"
                        popper-class="sessionidTooltip"
                      >
                        <div slot="content">
                          <span style="color: #9999a1">已用</span><br />{{
                            item.arrnum
                          }}
                        </div>
                      </el-tooltip> -->
                      <span> 总容量:</span>
                      <span class="fontw500">{{ item.arrnum }}TB</span>（由{{
                        item.slots.length
                      }}块盘组成，容量分别为
                      <span
                        v-for="(item2, index) in item.slots"
                        :key="index"
                      >{{ item2.size }}/</span>
                    </div>
                    <div class="HDDbox-down-info-r-mid">
                      <div
                        class="one"
                        :style="{ width: `${parseInt(item.usr)}%` }"
                      >
                        <span class="text">已用</span>
                      </div>
                      <div
                        class="two"
                        :style="{ width: `${parseInt(item.usable)}%` }"
                      >
                        <span class="text">可用</span>
                      </div>
                      <div
                        class="three"
                        :style="{ width: `${item.Dataredundancy}%` }"
                      >
                        <span class="text">数据冗余</span>
                      </div>
                      <div
                        v-if="item.Spacewasting != 0"
                        class="four"
                        :style="{ width: `${item.Spacewasting}%` }"
                      >
                        <span class="text">空间浪费</span>
                      </div>
                    </div>
                    <div class="HDDbox-down-info-r-down">
                      <div>
                        磁盘占用比：<span class="fontw500">{{ item.usr }}</span>
                      </div>
                      <div>
                        磁盘浪费比：<span class="fontw500">{{ item.Wastethan }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="hint" v-if="item.Spacewasting != 0">
                  *由于挂载的数据盘容量不一致，有3T的空间浪费。
                </div> -->
              </div>
            </div>
            <div
              v-if="diskstatus.state == 0"
              class="HDDbox-down"
            >
              <div
                v-for="index of this.Notmountnum"
                :key="index"
                class="bigbox"
              >
                <div class="HDDbox-down-info">
                  <div class="tophint">
                    <img src="../../assets/images/HDDnomount.png" />
                  </div>
                  <div class="HDDbox-down-info-l">
                    <img src="../../assets/images/HDD.png" />
                  </div>
                  <div
                    class="HDDbox-down-info-r"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: flex-start;
                    "
                  >
                    <div class="HDDbox-down-info-r-top">
                      <span> 总容量:</span>
                      <span class="fontw500">0TB</span>
                    </div>
                    <div class="HDDbox-down-info-r-mid">
                      <div
                        class="one"
                        style="width: 100%; background: #dee0e7"
                      ></div>
                    </div>
                    <div class="HDDbox-down-info-r-down">
                      <div>磁盘占用比：<span class="fontw500">0%</span></div>
                      <div>磁盘浪费比：<span class="fontw500">0%</span></div>
                    </div>
                  </div>
                  <!-- <div class="hint" v-if="item.Spacewasting != 0">
                  *由于挂载的数据盘容量不一致，有3T的空间浪费。
                </div> -->
                </div>
              </div>
            </div>
          </div>
          <el-dialog
            width="30%"
            title="内层 Dialog"
            :visible.sync="showhint"
            append-to-body
            custom-class="hintDialog"
          >
            <template slot="title">
              <div class="hintDialog-title">
                <img
                  src="../../assets/images/hint.png"
                  alt=""
                />

                提醒
              </div>
            </template>
            <div class="hintDialog-main">
              不符合规范,磁盘数量少于6块!请更换完成后，手动重启，再次重置数据盘。
            </div>
          </el-dialog>
        </el-dialog>
      </div>
      <!-- 更换硬盘下一步提示框 -->
      <Replacingdrive
        :h-d-dstep="HDDstep"
        @setHDDstepdata="setHDDstepdata"
      />
      <!-- 重组数据提示 -->
      <Recombine
        :h-d-drecom="HDDrecom"
        @setHDDrecomdata="setHDDrecomdata"
      ></Recombine>
    </div>
    <!-- 读取磁盘 -->
    <div>
      <!-- 第一步 -->
      <div
        v-if="Readdiskdata"
        class="step1"
      >
        <el-dialog
          :visible.sync="Readdiskdata"
          width="35%"
          :close-on-click-modal="false"
          @close="closedialogRead"
        >
          <span
            slot="title"
            class="title"
          > 读取硬盘 </span>
          <div class="HDDbox">
            <div class="HDDbox-top">
              <div class="HDDbox-top-l">
                当前硬盘模式：<span>raid5模式</span>
              </div>
              <div class="HDDbox-top-r">
                <el-button
                  v-show="!diskstatus.state == 0"
                  @click="nextReadstep"
                >
                  更换读取硬盘
                </el-button>
                <!-- TODO 测试所需  展示停用:disabled="!showhint" -->
                <el-button
                  v-show="diskstatus.state == 0"
                  @click="nextReadrecombine"
                >
                  读取数据盘
                </el-button>
              </div>
            </div>
            <div
              v-if="!diskstatus.state == 0"
              class="HDDbox-down"
            >
              <div
                v-for="(item, index) in hddData.used.raid"
                :key="index"
                class="bigbox"
              >
                <div class="HDDbox-down-info">
                  <div
                    v-show="mount"
                    class="tophint"
                  >
                    <img src="../../assets/images/HDDmount.png" />
                  </div>
                  <div
                    v-show="!mount"
                    class="tophint"
                  >
                    <img src="../../assets/images/HDDnomount.png" />
                  </div>
                  <div class="HDDbox-down-info-l">
                    <img src="../../assets/images/HDD.png" />
                    <div class="text">{{ item.dev }}</div>
                  </div>
                  <div class="HDDbox-down-info-r">
                    <div class="HDDbox-down-info-r-top">
                      <!-- <el-tooltip
                        content="Top center"
                        placement="top"
                        effect="light"
                        popper-class="sessionidTooltip"
                      >
                        <div slot="content">
                          <span style="color: #9999a1">已用</span><br />{{
                            item.arrnum
                          }}
                        </div>
                      </el-tooltip> -->
                      <span> 总容量:</span>
                      <span class="fontw500">{{ item.arrnum }}TB</span>（由{{
                        item.slots.length
                      }}块盘组成，容量分别为
                      <span
                        v-for="(item2, index) in item.slots"
                        :key="index"
                      >{{ item2.size }}/</span>
                    </div>
                    <div class="HDDbox-down-info-r-mid">
                      <div
                        class="one"
                        :style="{ width: `${parseInt(item.usr)}%` }"
                      >
                        <span class="text">已用</span>
                      </div>
                      <div
                        class="two"
                        :style="{ width: `${parseInt(item.usable)}%` }"
                      >
                        <span class="text">可用</span>
                      </div>
                      <div
                        class="three"
                        :style="{ width: `${item.Dataredundancy}%` }"
                      >
                        <span class="text">数据冗余</span>
                      </div>
                      <div
                        v-if="item.Spacewasting != 0"
                        class="four"
                        :style="{ width: `${item.Spacewasting}%` }"
                      >
                        <span class="text">空间浪费</span>
                      </div>
                    </div>
                    <div class="HDDbox-down-info-r-down">
                      <div>
                        磁盘占用比：<span class="fontw500">{{ item.usr }}</span>
                      </div>
                      <div>
                        磁盘浪费比：<span class="fontw500">{{ item.Wastethan }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <div class="hint" v-if="item.Spacewasting != 0">
                  *由于挂载的数据盘容量不一致，有3T的空间浪费。
                </div> -->
              </div>
            </div>
            <div
              v-if="diskstatus.state == 0"
              class="HDDbox-down"
            >
              <div
                v-for="index of 2"
                :key="index"
                class="bigbox"
              >
                <div class="HDDbox-down-info">
                  <div class="tophint">
                    <img src="../../assets/images/HDDnomount.png" />
                  </div>
                  <div class="HDDbox-down-info-l">
                    <img src="../../assets/images/HDD.png" />
                  </div>
                  <div
                    class="HDDbox-down-info-r"
                    style="
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      align-items: flex-start;
                    "
                  >
                    <div class="HDDbox-down-info-r-top">
                      <span> 总容量:</span>
                      <span class="fontw500">0TB</span>
                    </div>
                    <div class="HDDbox-down-info-r-mid">
                      <div
                        class="one"
                        style="width: 100%; background: #dee0e7"
                      ></div>
                    </div>
                    <div class="HDDbox-down-info-r-down">
                      <div>磁盘占用比：<span class="fontw500">0%</span></div>
                      <div>磁盘浪费比：<span class="fontw500">0%</span></div>
                    </div>
                  </div>
                  <!-- <div class="hint" v-if="item.Spacewasting != 0">
                  *由于挂载的数据盘容量不一致，有3T的空间浪费。
                </div> -->
                </div>
              </div>
            </div>
          </div>
          <el-dialog
            width="30%"
            title="内层 Dialog"
            :visible.sync="showhint"
            append-to-body
            custom-class="hintDialog"
          >
            <template slot="title">
              <div class="hintDialog-title">
                <img
                  src="../../assets/images/hint.png"
                  alt=""
                />

                提醒
              </div>
            </template>
            <div class="hintDialog-main">
              不符合规范,磁盘数量少于6块!请更换完成后，手动重启，再次重置数据盘。
            </div>
          </el-dialog>
        </el-dialog>
      </div>
      <!-- 读取硬盘下一步提示框 -->
      <resdsteptwo
        :readstep="Readstep"
        @setReadstepdata="setReadstepdata"
      />
      <!-- 读取数据提示 -->
      <resdstepthree
        :readrecom="Readrecom"
        @setReadrecomdata="setReadrecomdata"
      />
    </div>
    <!-- 数据清理 -->
    <div>
      <!-- 第一步 -->
      <div
        v-if="ifondialogdata"
        class="step1"
      >
        <el-dialog
          :visible.sync="ifondialogdata"
          width="30%"
          :close-on-click-modal="false"
          @close="closedialog"
        >
          <span
            slot="title"
            class="title"
          > 数据清理 </span>
          <div class="box">
            <div
              class="box-l"
              @click="nextStep"
            >
              <div class="box-l-img">
                <img
                  src="../../assets/images/Factorydefaults.png"
                  alt=""
                  srcset=""
                />
              </div>
              <div class="title">恢复出厂设置</div>
              <div class="text">
                清理pcap数据、会话元数据、协议元数据、任务配置信息、日志信息、<span>过滤规则、采集规则、证书文件、</span>下载数据和中间件kafka等；<span>该操作将导致系统重启。</span>
              </div>
            </div>
            <div
              class="box-r"
              @click="nextStep2"
            >
              <div class="box-r-img">
                <img
                  src="../../assets/images/cleardata.png"
                  alt=""
                  srcset=""
                />
              </div>
              <div class="title">清理数据</div>
              <div class="text">
                清理任务的配置、过滤规则、特征规则、pcap数据、会话元数据、协议元数据、SSL元数据、HTTP元数据、DNS元数据、日志信息；清理系统证书文件。
              </div>
            </div>
          </div>
        </el-dialog>
      </div>
      <!-- 恢复出场设置 -->
      <FactoryDefaults
        :step2="step2"
        @getstep2data="getstep2"
      ></FactoryDefaults>
      <!-- 清理数据 -->
      <ClearData
        :clearstep2="clearstep2"
        @setcleardata="setcleardata"
      ></ClearData>
    </div>
  </div>
</template>

<script>
import FactoryDefaults from "./components/FactoryDefaults.vue";
import ClearData from "./components/ClearData.vue";
import Replacingdrive from "./components/Replacedisk.vue";
import Recombine from "./components/Recombine.vue";
import resdsteptwo from "./components/Readdisk/readsteptwo.vue";
import resdstepthree from "./components/Readdisk/resdstepthree.vue";
import ItemVue from "@/layout/components/sidebar/Item.vue";
// import { setHDDstatus } from "@/api/SystemData/systemdata";
import { number } from "echarts";
export default {
  name: "Ifonconduct",
  components: {
    FactoryDefaults,
    ClearData,
    Replacingdrive,
    Recombine,
    resdsteptwo,
    resdstepthree,
  },
  data () {
    return {
      // 更换磁盘
      HDDstep: false,
      HDDrecom: false,
      // 读取磁盘
      Readstep: false,
      Readrecom: false,
      onewidth: "50%",
      twowidth: "10%",
      threewidth: "20%",
      fourwidth: "20%",
      step2: false,
      clearstep2: false,
      hddData: {
        used:{
          raid:''
        }
      },
      mount: true,
      // 为挂载硬盘的数量 初始化为0 需求最少6个硬盘
      Notmountnum: 0,
      // 磁盘数量错误提示
      showhint: false,
    };
  },
  computed: {
    ifondialogdata: {
      get () {
        return this.$store.state.ifonconduct.dialogopne;
      },
      set (v) { },
    },

    HDDdialogdata: {
      get () {
        return this.$store.state.ifonconduct.drive;
      },
      set (v) { },
    },
    HDDdata: {
      get () {
        return this.$store.state.ifonconduct.driveData;
      },
      set (v) { },
    },
    diskstatus: {
      get () {
        return this.$store.state.ifonconduct.diskstatus;
      },
      set (v) {
        console.log(v);
      },
    },
    Readdiskdata: {
      get () {
        return this.$store.state.ifonconduct.Readdata;
      },
      set (v) { },
    },
  },
  watch: {
    HDDdata: {
      handler (val) {
        this.HDDinit(val);
      },
    },
  },
  methods: {
    // 数据清理
    // ====================
    closedialog () {
      this.$store.commit("ifonconduct/getdailogopendata", false);
    },
    nextStep () {
      this.$store.commit("ifonconduct/getdailogopendata", false);
      this.step2 = true;
    },
    nextStep2 () {
      this.clearstep2 = true;
      this.$store.commit("ifonconduct/getdailogopendata", false);
    },
    getstep2 (e) {
      this.step2 = false;
    },
    setcleardata (e) {
      this.clearstep2 = false;
    },
    // 更换硬盘 =======
    closedialogHDD () {
      this.$store.commit("ifonconduct/Replacingdrive", false);
    },
    nextHDDstep () {
      this.$store.commit("ifonconduct/Replacingdrive", false);
      this.HDDstep = true;
    },
    // 更换磁盘
    setHDDstepdata () {
      this.HDDstep = false;
    },
    setHDDrecomdata () {
      this.HDDrecom = false;
    },
    // 读取磁盘 ========
    nextReadstep () {
      this.$store.commit("ifonconduct/Readdiskdata", false);
      this.Readstep = true;
    },
    closedialogRead () {
      this.$store.commit("ifonconduct/Readdiskdata", false);
    },
    setReadstepdata () {
      this.Readstep = false;
    },
    setReadrecomdata () {
      this.Readrecom = false;
    },
    // 重组数据
    /**
     * @description: 数据重组，获取到磁盘信息
     * @return {*}
     */
    HDDinit (resdata) {
      this.showhint = false;
      if (this.diskstatus.state !== 0) {
        this.hddData = this.formatList(resdata);
      } else {
        this.Notmountnum = resdata.raid.length;
        if (resdata.raid.length >= 6) {
          this.showhint = false;
        } else {
          this.showhint = true;
        }
      }
    },
    formatList (arr) {
      for (let i in arr) {
        // if (i == "used") {
        if (arr["used"].raid.length != 0) {
          this.mount = true;
          if (i == "used") {
            // 获取到每个磁盘的总量
            arr[i].raid.forEach((v) => {
              let sum = 0;
              let minsun = [];
              v.slots.forEach((n, index, arr) => {
                if (n.size.includes("GB")) {
                  n.size = parseFloat(parseInt(n.size) / 1024).toFixed(3);
                }
                sum += parseFloat(n.size);
                minsun.push(parseFloat(n.size));
              });
              v.arrnum = parseFloat(v.size).toFixed(3);
              v.lfnum = sum;
              // raid的最小值
              v.minarrnum = Math.min(...minsun);
            });
            arr[i].raid.forEach((y) => {
              // 磁盘的浪费比
              y.Wastethan = parseInt(
                (((y.arrnum - parseFloat(y.size)) / y.lfnum) * 10000) / 100
              );
              // 数据冗余
              y.Dataredundancy = parseInt(
                ((y.minarrnum / y.arrnum) * 10000) / 100
              );
              // 空间浪费
              if (
                parseInt(y.arrnum) - parseInt(y.size) - parseInt(y.minarrnum) <=
                0
              ) {
                y.Spacewasting = 0;
              } else {
                y.Spacewasting =
                  (((parseInt(y.arrnum) -
                    parseFloat(y.size) -
                    parseInt(y.minarrnum)) /
                    y.arrnum) *
                    10000) /
                  100;
              }
              // 可用
              y.usable =
                100 -
                y.Dataredundancy -
                y.Spacewasting -
                (100 * parseInt(y.usr)) / 100;
            });
          }
        } else {
          this.mount = false;
        }
      }
      return arr;
    },
    nextHDDrecombine () {
      this.$store.commit("ifonconduct/Replacingdrive", false);

      this.HDDrecom = true;
    },
    nextReadrecombine () {
      this.$store.commit("ifonconduct/Readdiskdata", false);
      this.Readrecom = true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__wrapper {
  // display: flex;
  // justify-content: center;
  // align-items: center;
}
::v-deep .el-dialog {
  background: #ffffff;
  box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
  border-radius: 8px;
}
.title {
  font-family: 'Alibaba PuHuiTi';
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  // line-height: 24px;
  /* identical to box height, or 150% */

  /* Black-Shallow */

  color: #2c2c35;
}
.step1 {
  ::v-deep {
    .el-dialog__body {
      padding: 16px;
    }
    .el-dialog__header {
      padding: 0 24px;
      padding-top: 16px;
    }
    .el-dialog__headerbtn {
      top: 18px !important;
    }
  }
}
.box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &-l {
    padding: 16px;
    width: 100%;
    // height: 202px;
    background: #f7f8fa;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
 
    // justify-content: space-between;
    .img {
      width: 32px;
      height: 32px;
    }
    .title {
      font-family: 'Alibaba PuHuiTi';
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;
      margin:8px 0px;
    }
    .text {
      font-family: 'Alibaba PuHuiTi';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #767684;
      margin:8px 0px;
      // span {
      //   font-weight: 500;
      //   color: #2c2c35;
      // }
    }
  }
  &-l:hover {
    cursor: pointer;
    // padding: 14px;
    border: 2px solid #4a97ff;
    box-sizing: border-box;
  }
  &-r {
    margin-top: 12px;
    padding: 16px;
    width: 100%;
    height: auto;
    background: #f7f8fa;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
    .img {
      width: 32px;
      height: 32px;
    }
    .title {
      font-family: 'Alibaba PuHuiTi';
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;
      margin:8px 0px;
    }
    .text {
      margin:8px 0px;
      font-family: 'Alibaba PuHuiTi';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #767684;
      span {
        font-weight: 500;
        color: #2c2c35;
      }
    }
  }
  &-r:hover {
    cursor: pointer;
    // padding: 14px;
    border: 2px solid #4a97ff;
    box-sizing: border-box;
  }
}
.HDDbox {
  font-family: 'Alibaba PuHuiTi';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  // line-height: 22px;
  color: #9999a1;
  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: 'Alibaba PuHuiTi';
    font-style: normal;
    color: #9999a1;
    margin-bottom: 16px;
    &-l {
      span {
        font-weight: 400;
        font-size: 14px;
        // line-height: 22px;
        color: #2c2c35;
      }
    }
    &-r {
      .el-button {
        font-family: 'Alibaba PuHuiTi';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        // width: 80px;
        height: 24px;
        padding: 1px 12px;
        background: #116ef9;
        color: #ffffff;
      }
    }
  }
  &-down {
    .bigbox {
      margin-bottom: 16px;
    }
    .hint {
      margin-top: 8px;
      font-family: 'Alibaba PuHuiTi';
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: #df0c0c;
    }
    &-info {
      position: relative;
      background: #f7f8fa;
      border: 1px solid #f2f3f7;
      border-radius: 4px;
      // width: 436px;
      height: 104px;
      display: flex;

      .tophint {
        position: absolute;
        top: 0;
        left: 0;
        width: 58px;
        height: 20px;
        img {
          width: 58px;
          height: 20px;
        }
      }
      &-l {
        width: 80px;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        padding-bottom: 16px;
        margin-right: 8px;
        img {
          width: 40px;
          height: 40px;
        }
        .text {
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
          color: #9999a1;
        }
      }
      &-r {
        flex: 1;
        padding: 16px 0;
        padding-right: 8px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .fontw500 {
          font-weight: 500;
          color: #2c2c35;
        }
        &-top {
          margin-bottom: 8px;
        }
        &-mid {
          width: 100%;
          margin-bottom: 8px;
          display: flex;
          font-weight: 400;
          font-size: 12px;
          // line-height: 16px;
          color: #ffffff;

          .one {
            border-top-left-radius: 2px;
            border-bottom-left-radius: 2px;
            height: 16px;
            background: #116ef9;
            line-height: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }
          .two {
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
            height: 16px;
            background: #07d4ab;
            line-height: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }
          .three {
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
            height: 16px;
            background: #767684;
            line-height: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }
          .four {
            border-top-right-radius: 2px;
            border-bottom-right-radius: 2px;
            height: 16px;
            background: #dee0e7;
            line-height: 16px;
            color: #767684;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
          }

          .text {
            margin-left: 4px;
          }
        }
        &-down {
          width: 100%;
          display: flex;
          justify-content: space-between;
        }
      }
    }
    .bigbox:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
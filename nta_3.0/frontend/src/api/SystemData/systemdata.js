
import request from '@/utils/request';

// 1. 数据清理接口
export function cleandata(data) {
  return request({
    url: '/system/clean/data',
    method: 'POST',
    data
  });
}

// 2. 数据清理进度查询接口
export function cleandataSchedule(data) {
  return request({
    url: '/system/clean/data/schedule',
    method: 'POST',
    data
  });
}

// 3. 系统重置接口
export function resetdata(data) {
  return request({
    url: '/system/reset',
    method: 'POST',
    data
  });
}

// 3. 获取磁盘信息
export function getHDDinfo(data) {
  return request({
    url: '/raid/info',
    method: 'POST',
    data
  });
}
// 4. 获取磁盘状态
export function getdiskstatus(data) {
  return request({
    url: '/system/disk/status',
    method: 'POST',
    data
  });
}

//  更新磁盘状态
export function setHDDstatus(data) {
  return request({
    url: '/system/disk/change',
    method: 'POST',
    data
  });
}

//  查看磁盘状态
export function getHDDstatus(data) {
  return request({
    url: '/system/disk/status',
    method: 'POST',
    data
  });
}

//  重组磁盘
export function diskrebuild(data) {
  return request({
    url: '/system/disk/rebuild',
    method: 'POST',
    data
  });
}

// 获取当前硬盘操作模式  更换和读取
export function getdiskmode(data) {
  return request({
    url: '/system/disk/field',
    method: 'POST',
    data
  });
}

//   读取磁盘准备接口
export function readydisk(data) {
  return request({
    url: '/system/disk/mount/ready',
    method: 'POST',
    data
  });
}

//   读取磁盘挂载接口
export function readydiskstart(data) {
  return request({
    url: '/system/disk/mount/data',
    method: 'POST',
    data
  });
}
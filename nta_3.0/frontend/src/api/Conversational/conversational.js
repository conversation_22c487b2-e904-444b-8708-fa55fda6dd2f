import request from '@/utils/request';

// 获取标签
export function getTags(data) {
  return request({
    url: '/session/tag/list',
    method: 'POST',
    data
  });
}

// 获取任务下拉列表
export function getTasklist(data) {
  return request({
    url: 'session/task/list',
    method: 'POST',
    data
  });
}

// 获取ip协议字段
export function getIplist(data) {
  return request({
    url: 'session/protocol/ip',
    method: 'POST',
    data
  });
}

// 获取应用协议字段
export function getProlist(data) {
  return request({
    url: '/session/protocol/app',
    method: 'POST',
    data
  });
}

// 获取ES下拉字段
export function getEslist(data) {
  return request({
    url: '/session/task_list',
    method: 'POST',
    data
  });
}

// 获取会话列表
export function getSessionList(data) {
  return request({
    url: '/session/list',
    method: 'POST',
    data
  });
}
//获取会话分析折线图数据
export function getTargetKbv(data) {
  return request({
    url: '/anay/range/list',
    method: 'POST',
    data
  });
}

//pcap数据准备接口
export function downloadpartpre(data) {
  return request({
    url: '/session/download/prepare/pcap',
    method: 'POST',
    data
  });
}

//获取pcap下载列表
export function getpcapdownlist(data) {
  return request({
    url: '/session/download/pcap/list',
    method: 'POST',
    data
  });
}

//获取pcap下载地址接口
export function pcapdown(data) {
  return request({
    url: '/session/download/pcap/path',
    method: 'POST',
    data
  });
}

//获取pcap任务删除接口
export function sessiondelete(data) {
  return request({
    url: 'session/delete/record',
    method: 'POST',
    data
  });
}


//日志导出创建接口
export function alllogderive(data) {
  return request({
    url: '/task/register/create',
    method: 'POST',
    data
  });
}
//获取日志导出列表
export function getlogderivelist(data) {
  return request({
    url: '/task/register/search/list',
    method: 'POST',
    data
  });
}

//获取日志下载接口
export function logderivedown(data) {
  return request({
    url: '/task/register/download',
    method: 'POST',
    data
  });
}

//删除日志导出列表接口
export function logdelete(data) {
  return request({
    url: '/task/register/delete',
    method: 'POST',
    data
  });
}

//===========================
//获取元数据列表
export function sessionAnalysis(data) {
  return request({
    url: '/anay/pro/metadata',
    method: 'POST',
    data
  });
}

//获取通信信息数据
export function communicationList(data) {
  return request({
    url: '/workbench/communication/list2',
    method: 'POST',
    data
  });
}

//获取标签列表
export function gettaglist(data) {
  return request({
    url: '/anay/tag/list',
    method: 'POST',
    data
  });
}
//获取会话聚合列表
export function getdialogueList(data) {
  return request({
    url: '/anay/session/agg',
    method: 'POST',
    data
  });
}

//获取元数据聚合列表
export function getmetadataList(data) {
  return request({
    url: '/anay/metadata/agg',
    method: 'POST',
    data
  });
}

//获取会话详情页面
export function getsessiondata(data) {
  return request({
    url: '/target/session/detail',
    method: 'POST',
    data
  });
}

//增加会话详情备注接口
export function addRemark(data) {
  return request({
    url: '/target/addRemark',
    method: 'POST',
    data
  });
}

//删除会话详情备注接口
export function deleteRemark(data) {
  return request({
    url: '/target/deleteRemark',
    method: 'POST',
    data
  });
}

//会话详情标签修改接口
export function editTargetTag(data) {
  return request({
    url: '/target/editTargetTag',
    method: 'POST',
    data
  });
}

// 模板下载
export function templateDownn(data) {
  return request({
    url: '/filter/template',
    method: 'GET',
    data
  });
}

// 获取es字段内容
export function getESmain(data) {
  return request({
    url: 'session/es/field',
    method: 'POST',
    data
  });
}

import request from "@/utils/request";
// 查询检索历史记录
export function history(data) {
  return request({
    url: "/query/history/list",
    method: "POST",
    data,
  });
}
// 查询当前用户的查询模板列表
export function temp(data) {
  return request({
    url: "/query/template/list",
    method: "POST",
    data,
  });
}
// 批量删除检索历史记录
export function historyDelete(data) {
  return request({
    url: "/query/history/delete",
    method: "DELETE",
    data,
  });
}
// 批量删除模板记录
export function tempDelete(data) {
  return request({
    url: "/query/template/delete",
    method: "DELETE",
    data,
  });
}
// 使用当前模板进行查询时，进行记录更新，更新查询的使用时间
export function tempUpdate(data) {
  return request({
    url: "/query/template/update",
    method: "PUT",
    data,
  });
}
// 通过查询条件创建对应的查询模板（通过现成的查询条件）
export function templateCreate(data) {
  return request({
    url: "/query/template/create",
    method: "POST",
    data,
  });
}
// 创建自定义证书检索查询模板
export function create(data) {
  return request({
    url: "/query/template/create",
    method: "POST",
    data,
  });
}
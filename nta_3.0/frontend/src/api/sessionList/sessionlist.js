import request from '@/utils/request';



// 获取会话列表
export function getSessionList(data) {
  return request({
    url: '/session/list',
    method: 'POST',
    data
  });
}
// IP列表
export function GetIplist(data) {
  return request({
    url: '/aggr/target/ip/list',
    method: 'post',
    data
  });
}

// Ip详情
export function GetIpInfo(data) {
  return request({
    url: `/analyze/ip/info?${data}`,
    method: 'GET'
  });
}

// 域名列表
export function GetDomianlist(data) {
  return request({
    url: '/aggr/target/domain/list',
    method: 'post',
    data
  });
}

// 域名详情
export function GetDomainInfo(data) {
  return request({
    url: `/analyze/domain/info?${data}`,
    method: 'GET',
  });
}

// 证书列表
export function GetCertlist(data) {
  return request({
    url: '/aggr/target/cert/list',
    method: 'post',
    data
  });
}

// 证书详情
export function GetCertInfo(data) {
  return request({
    url: `/analyze/cert/info?${data}`,
    method: 'GET',
    data
  });
}

// 详情备注编辑
export function SetRemark(data) {
  return request({
    url: '/analyze/remark',
    method: 'PUT',
    data
  });
}
// 详情备注编辑
export function getTemplate() {
  return request({
    url: `/analyze/cert/log/template?user_id=1`,
    method: 'GET',
  });
}
// 详情标签编辑
export function SetTags(data) {
  return request({
    url: '/analyze/label',
    method: 'PUT',
    data
  });
}

//获取元数据列表
export function sessionAnalysis(data) {
  return request({
    url: '/anay/pro/metadata',
    method: 'POST',
    data
  });
}

//获取会话详情页面
export function getsessiondata(data) {
  return request({
    url: '/target/session/detail',
    method: 'POST',
    data
  });
}

//增加会话详情备注接口
export function addRemark(data) {
  return request({
    url: '/target/addRemark',
    method: 'POST',
    data
  });
}

//删除会话详情备注接口
export function deleteRemark(data) {
  return request({
    url: '/target/deleteRemark',
    method: 'POST',
    data
  });
}

//会话详情标签修改接口
export function editTargetTag(data) {
  return request({
    url: '/target/editTargetTag',
    method: 'POST',
    data
  });
}

// 获取MAC关系图数据
export function getMaclist(data) {
  return request({
    url: '/mac/list',
    method: 'POST',
    data
  });
}

// 指纹列表
export function getFingerlist(data) {
  return request({
    url: '/aggr/target/finger/list',
    method: 'POST',
    data
  });
}

//获取会话聚合列表
export function getdialogueList(data) {
  return request({
    url: '/anay/session/agg',
    method: 'POST',
    data
  });
}

//获取元数据聚合列表
export function getmetadataList(data) {
  return request({
    url: '/anay/metadata/agg',
    method: 'POST',
    data
  });
}

//日志导出创建接口
export function alllogderive(data) {
  return request({
    url: '/task/register/create',
    method: 'POST',
    data
  });
}

//pcap数据准备接口
export function downloadpartpre(data) {
  return request({
    url: '/session/download/prepare/pcap',
    method: 'POST',
    data
  });
}

//通信信息桑基图
export function sankey(data) {
  return request({
    url: '/aggr/target/communication/sankey',
    method: 'POST',
    data
  });
}

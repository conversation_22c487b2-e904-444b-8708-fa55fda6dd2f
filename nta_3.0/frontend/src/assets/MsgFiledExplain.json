{"conn": {"HandleBeginTime": {"filedName": "HandleBeginTime", "probe": 1, "analysis": 0, "Name": "处理开始时间", "Remark": "", "type": "int"}, "HandleEndTime": {"filedName": "HandleEndTime", "probe": 1, "analysis": 0, "Name": "处理结束时间", "Remark": "", "type": "int"}, "sIp": {"filedName": "sIp", "probe": 0, "analysis": 1, "Name": "源IP地址", "Remark": "", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 1, "Name": "源端口", "Remark": "", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 1, "Name": "目的IP地址", "Remark": "", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 1, "Name": "目的端口", "Remark": "", "type": "int"}, "ProxyIP": {"filedName": "ProxyIP", "probe": 0, "analysis": 1, "Name": "代理IP", "Remark": "proxy_ip", "type": "string"}, "ProxyPort": {"filedName": "ProxyPort", "probe": 0, "analysis": 1, "Name": "代理端口", "Remark": "proxy_port", "type": "INT"}, "IPPro": {"filedName": "IPPro", "probe": 0, "analysis": 1, "Name": "IP协议号", "Remark": "", "type": "int"}, "Labels": {"filedName": "Labels", "probe": 0, "analysis": 1, "Name": "标签数组", "Remark": "", "type": "json"}, "ProxyIPInfor": {"filedName": "ProxyIPInfor", "probe": 1, "analysis": 0, "Name": "代理IP标志", "Remark": "", "type": "int"}, "FirstSender": {"filedName": "FirstSender", "probe": 1, "analysis": 0, "Name": "该会话中发送第一个包的IP", "Remark": "", "type": "string"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 1, "Name": "会话ID；全局唯一", "Remark": "", "type": "string"}, "AppId": {"filedName": "AppId", "probe": 1, "analysis": 0, "Name": "应用ID", "Remark": "", "type": "int"}, "AppName": {"filedName": "AppName", "probe": 0, "analysis": 1, "Name": "应用名", "Remark": "", "type": "string"}, "ProName": {"filedName": "ProName", "probe": 0, "analysis": 1, "Name": "协议名", "Remark": "", "type": "string"}, "DeviceID": {"filedName": "DeviceID", "probe": 1, "analysis": 0, "Name": "当前的设备ID", "Remark": "", "type": "int"}, "EthPortID": {"filedName": "EthPortID", "probe": 1, "analysis": 0, "Name": "", "Remark": "", "type": "int"}, "TaskId": {"filedName": "TaskId", "probe": 1, "analysis": 0, "Name": "任务ID", "Remark": "", "type": "int"}, "BatchNum": {"filedName": "BatchNum", "probe": 1, "analysis": 0, "Name": "批次ID", "Remark": "batch_id", "type": "int"}, "ThreadId": {"filedName": "ThreadId", "probe": 0, "analysis": 1, "Name": "线程ID", "Remark": "", "type": "int"}, "Duration": {"filedName": "Duration", "probe": 0, "analysis": 1, "Name": "会话持续时间-秒", "Remark": "", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 1, "Name": "会话起始时间-秒", "Remark": "begin_time", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 1, "analysis": 0, "Name": "会话起始时间-纳秒", "Remark": "begin_nsec", "type": "int"}, "EndTime": {"filedName": "EndTime", "probe": 0, "analysis": 1, "Name": "会话结束时间-秒", "Remark": "end_time", "type": "int"}, "EndNSec": {"filedName": "EndNSec", "probe": 1, "analysis": 0, "Name": "会话结束时间-纳秒", "Remark": "end_nsec", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 1, "Name": "记录创建时间 -> 入库时间", "Remark": "", "type": "int"}, "FirstProto": {"filedName": "FirstProto", "probe": 0, "analysis": 1, "Name": "首层协议ID", "Remark": "", "type": "int"}, "ProListNum": {"filedName": "ProListNum", "probe": 0, "analysis": 1, "Name": "协议栈层级", "Remark": "", "type": "int"}, "ProList.ProStack": {"filedName": "ProList.ProStack", "probe": 1, "analysis": 0, "Name": "协议栈详细信息", "Remark": "", "type": "json"}, "ExtJson": {"filedName": "ExtJson", "probe": 0, "analysis": 0, "Name": "基于用户需求添加", "Remark": "", "type": "json"}, "PktLenDist": {"filedName": "PktLenDist", "probe": 1, "analysis": 0, "Name": "SSL负载长度分布", "Remark": "", "type": "bytes[8]"}, "pkt.ProNum": {"filedName": "pkt.ProNum", "probe": 1, "analysis": 0, "Name": "识别到应用协议的包数", "Remark": "", "type": "int"}, "pkt.UnkProNum": {"filedName": "pkt.UnkProNum", "probe": 1, "analysis": 0, "Name": "未识别的应用的包数", "Remark": "", "type": "int"}, "pkt.sPayload": {"filedName": "pkt.sPayload", "probe": 1, "analysis": 0, "Name": "包负载客户端端前4个", "Remark": "", "type": "int"}, "pkt.dPayload": {"filedName": "pkt.dPayload", "probe": 1, "analysis": 0, "Name": "包负载服务端前4个", "Remark": "", "type": "int"}, "pkt.Infor.Count": {"filedName": "pkt.Infor.Count", "probe": 1, "analysis": 0, "Name": "包计数", "Remark": "", "type": "int"}, "pkt.Infor.Len": {"filedName": "pkt.Infor.Len", "probe": 1, "analysis": 0, "Name": "当前包长，C2S为正，C2C为负数", "Remark": "", "type": "int"}, "pkt.Infor.Sec": {"filedName": "pkt.Infor.Sec", "probe": 1, "analysis": 0, "Name": "当前包时间-秒", "Remark": "", "type": "int"}, "pkt.Infor.nSec": {"filedName": "pkt.Infor.nSec", "probe": 1, "analysis": 0, "Name": "当前包时间-纳秒", "Remark": "", "type": "int"}, "pkt.SynData": {"filedName": "pkt.SynData", "probe": 1, "analysis": 0, "Name": "syn数据包数据", "Remark": "", "type": "int"}, "pkt.APPCount": {"filedName": "pkt.APPCount", "probe": 1, "analysis": 0, "Name": "第几个负载包识别到应用", "Remark": "", "type": "int"}, "pkt.sNum": {"filedName": "pkt.sNum", "probe": 1, "analysis": 0, "Name": "会话中的包数", "Remark": "", "type": "int"}, "pkt.sPayloadNum": {"filedName": "pkt.sPayloadNum", "probe": 0, "analysis": 1, "Name": "会话中的有负载包数", "Remark": "", "type": "int"}, "pkt.sBytes": {"filedName": "pkt.sBytes", "probe": 1, "analysis": 0, "Name": "会话中的总字节数", "Remark": "", "type": "int"}, "pkt.sPayloadBytes": {"filedName": "pkt.sPayloadBytes", "probe": 0, "analysis": 1, "Name": "会话中的负载字节数", "Remark": "", "type": "int"}, "pkt.dNum": {"filedName": "pkt.dNum", "probe": 1, "analysis": 0, "Name": "会话中的包数", "Remark": "", "type": "int"}, "pkt.dPayloadNum": {"filedName": "pkt.dPayloadNum", "probe": 0, "analysis": 1, "Name": "会话中的有负载包数", "Remark": "", "type": "int"}, "pkt.dBytes": {"filedName": "pkt.dBytes", "probe": 1, "analysis": 0, "Name": "会话中的总字节数", "Remark": "", "type": "int"}, "pkt.dPayloadBytes": {"filedName": "pkt.dPayloadBytes", "probe": 0, "analysis": 1, "Name": "会话中的负载字节数", "Remark": "", "type": "int"}, "TCPGather.Bytes": {"filedName": "TCPGather.Bytes", "probe": 1, "analysis": 0, "Name": "字节数", "Remark": "", "type": "int"}, "TCPGather.PacketNum": {"filedName": "TCPGather.PacketNum", "probe": 1, "analysis": 0, "Name": "包数", "Remark": "", "type": "int"}, "TCPGather.PSHNum": {"filedName": "TCPGather.PSHNum", "probe": 1, "analysis": 0, "Name": "FLAG：PUSH包数", "Remark": "", "type": "int"}, "TCPGather.ACK": {"filedName": "TCPGather.ACK", "probe": 1, "analysis": 0, "Name": "对应的确认号", "Remark": "", "type": "int"}, "TCPGather.MinSeq": {"filedName": "TCPGather.MinSeq", "probe": 1, "analysis": 0, "Name": "最小序列号", "Remark": "", "type": "int"}, "TCPGather.MaxSeq": {"filedName": "TCPGather.MaxSeq", "probe": 1, "analysis": 0, "Name": "最大序列号", "Remark": "", "type": "int"}, "SbytesDiviDbytes": {"filedName": "SbytesDiviDbytes", "probe": 0, "analysis": 1, "Name": "上下行流量比", "Remark": "", "type": "float"}, "TotalBytes": {"filedName": "TotalBytes", "probe": 0, "analysis": 1, "Name": "流量总字节书", "Remark": "", "type": "int"}, "TotalPacketNum": {"filedName": "TotalPacketNum", "probe": 0, "analysis": 1, "Name": "总包数", "Remark": "", "type": "int"}, "PktIntervalTimeDist": {"filedName": "PktIntervalTimeDist", "probe": 1, "analysis": 0, "Name": "时间在2^n秒的分布", "Remark": "", "type": "数组"}, "SynSeqList": {"filedName": "SynSeqList", "probe": 1, "analysis": 0, "Name": "SYN的序列号取值", "Remark": "", "type": "数组"}, "SynNum": {"filedName": "SynNum", "probe": 1, "analysis": 0, "Name": "标识符为SYN的包数", "Remark": "", "type": "int"}, "sMac": {"filedName": "sMac", "probe": 0, "analysis": 1, "Name": "源MAC", "Remark": "", "type": "string"}, "sSSLFinger": {"filedName": "s<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "客户端SSL指纹", "Remark": "", "type": "string"}, "sHTTPFinger": {"filedName": "sHTTPFinger", "probe": 0, "analysis": 1, "Name": "客户端HTTP指纹；UserAgent", "Remark": "", "type": "string"}, "sIpCity": {"filedName": "sIpCity", "probe": 0, "analysis": 1, "Name": "IP地址所在城市", "Remark": "", "type": "string"}, "sIpCountry": {"filedName": "sIpCountry", "probe": 0, "analysis": 1, "Name": "IP地址所在国家", "Remark": "", "type": "string"}, "sIpSubdivisions": {"filedName": "sIpSubdivisions", "probe": 0, "analysis": 1, "Name": "IP地址的省份", "Remark": "", "type": "string"}, "pkt.sMaxLen": {"filedName": "pkt.sMaxLen", "probe": 1, "analysis": 0, "Name": "会话中的最大包长", "Remark": "", "type": "int"}, "pkt.sPSHNum": {"filedName": "pkt.sPSHNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含push的包数", "Remark": "", "type": "int"}, "pkt.sFINNum": {"filedName": "pkt.sFINNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含fin的包数", "Remark": "", "type": "int"}, "pkt.sRSTNum": {"filedName": "pkt.sRSTNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含rst的包数", "Remark": "", "type": "int"}, "pkt.sSYNNum": {"filedName": "pkt.sSYNNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含syn的包数", "Remark": "", "type": "int"}, "pkt.sSYNBytes": {"filedName": "pkt.sSYNBytes", "probe": 1, "analysis": 0, "Name": "会话中：syn包的字节数", "Remark": "", "type": "int"}, "pkt.sTTLMax": {"filedName": "pkt.sTTLMax", "probe": 1, "analysis": 0, "Name": "ttl最大值", "Remark": "", "type": "int"}, "pkt.sTTLMin": {"filedName": "pkt.sTTLMin", "probe": 1, "analysis": 0, "Name": "ttl最小值", "Remark": "", "type": "int"}, "sIpIdOffset": {"filedName": "sIpIdOffset", "probe": 1, "analysis": 0, "Name": "Ipid的分布", "Remark": "", "type": "数组"}, "sMinHopCount": {"filedName": "sMinHopCount", "probe": 0, "analysis": 1, "Name": "TTL最小距离距离", "Remark": "", "type": "int"}, "sMaxHopCount": {"filedName": "sMaxHopCount", "probe": 0, "analysis": 1, "Name": "会话中的最大距离", "Remark": "", "type": "int"}, "sInitialTTL": {"filedName": "sInitialTTL", "probe": 0, "analysis": 1, "Name": "TTL基线；2的N次方", "Remark": "", "type": "int"}, "dMac": {"filedName": "dMac", "probe": 0, "analysis": 1, "Name": "目的MAC", "Remark": "", "type": "string"}, "dSSLFinger": {"filedName": "d<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 1, "Name": "服务端SSL指纹", "Remark": "", "type": "string"}, "dHTTPFinger": {"filedName": "dHTTPFinger", "probe": 0, "analysis": 1, "Name": "服务端HTTP指纹；UserAgent", "Remark": "", "type": "string"}, "dIpCity": {"filedName": "dIpCity", "probe": 0, "analysis": 1, "Name": "IP地址所在城市", "Remark": "", "type": "string"}, "dIpCountry": {"filedName": "dIpCountry", "probe": 0, "analysis": 1, "Name": "IP地址所在国家", "Remark": "", "type": "string"}, "dIpSubdivisions": {"filedName": "dIpSubdivisions", "probe": 0, "analysis": 1, "Name": "IP所在省份", "Remark": "", "type": "int"}, "pkt.dMaxLen": {"filedName": "pkt.dMaxLen", "probe": 1, "analysis": 0, "Name": "会话中的最大包长", "Remark": "", "type": "int"}, "pkt.dFinNum": {"filedName": "pkt.dFinNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含fin的包数", "Remark": "", "type": "int"}, "pkt.dRSTNum": {"filedName": "pkt.dRSTNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含rst的包数", "Remark": "", "type": "int"}, "pkt.dSYNNum": {"filedName": "pkt.dSYNNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含的SYN包数", "Remark": "", "type": "int"}, "pkt.dSYNBytes": {"filedName": "pkt.dSYNBytes", "probe": 1, "analysis": 0, "Name": "会话中：syn包的字节数", "Remark": "", "type": "int"}, "pkt.dTTLMax": {"filedName": "pkt.dTTLMax", "probe": 0, "analysis": 1, "Name": "ttl最大值", "Remark": "", "type": "int"}, "pkt.dTTLMin": {"filedName": "pkt.dTTLMin", "probe": 0, "analysis": 1, "Name": "ttl最小值", "Remark": "", "type": "int"}, "pkt.dPSHNum": {"filedName": "pkt.dPSHNum", "probe": 1, "analysis": 0, "Name": "会话中：TCP:flag含push的包数", "Remark": "", "type": "int"}, "dIpIdOffset": {"filedName": "dIpIdOffset", "probe": 1, "analysis": 0, "Name": "Ipid的分布", "Remark": "", "type": "数组"}, "dInitialTTL": {"filedName": "dInitialTTL", "probe": 0, "analysis": 1, "Name": "TTL基线；2的N次方", "Remark": "", "type": "int"}, "dMinHopCount": {"filedName": "dMinHopCount", "probe": 1, "analysis": 0, "Name": "TTL距离", "Remark": "", "type": "int"}, "dMaxHopCount": {"filedName": "dMaxHopCount", "probe": 1, "analysis": 0, "Name": "会话中的最大距离", "Remark": "", "type": "int"}, "sDistLen": {"filedName": "sDistLen", "probe": 1, "analysis": 0, "Name": "上下行包长分布;uint32_t distLen[2][8];", "Remark": "", "type": "数组"}, "dDistLen": {"filedName": "dDistLen", "probe": 1, "analysis": 0, "Name": "", "Remark": "", "type": "数组"}, "ip_port_ipport_appid": {"filedName": "ip_port_ipport_appid", "probe": 0, "analysis": 1, "Name": "目的ip  port ippro appid 联合索引", "Remark": "", "type": "string"}, "mac2mac": {"filedName": "mac2mac", "probe": 0, "analysis": 1, "Name": "mac 到mac 的联合索引", "Remark": "", "type": "string"}, "ip2ip": {"filedName": "ip2ip", "probe": 0, "analysis": 1, "Name": "IP对直接索引；按ip字符从小到大的顺序排列", "Remark": "", "type": "string"}, "sip_appid_dport_dip": {"filedName": "sip_appid_dport_dip", "probe": 0, "analysis": 1, "Name": "源IP appid  目标端口 目标IP 联合索引", "Remark": "", "type": "string"}, "filename": {"filedName": "filename", "probe": 0, "analysis": 1, "Name": "", "Remark": "", "type": "string"}, "row": {"filedName": "row", "probe": 0, "analysis": 1, "Name": "", "Remark": "", "type": "int"}, "User-Agent": {"filedName": "User-Agent", "probe": 0, "analysis": 1, "Name": "客户端.客户端信息", "Remark": "", "type": "string"}, "HTTP": {"filedName": "HTTP", "probe": 0, "analysis": 1, "Name": "http协议扩展字段", "Remark": "", "type": "数组"}, "HTTP.Url": {"filedName": "HTTP.Url", "probe": 0, "analysis": 1, "Name": "http url", "Remark": "", "type": "string"}, "HTTP.Act": {"filedName": "HTTP.Act", "probe": 0, "analysis": 1, "Name": "http Act", "Remark": "", "type": "string"}, "HTTP.Host": {"filedName": "HTTP.Host", "probe": 0, "analysis": 1, "Name": "主站", "Remark": "", "type": "string"}, "HTTP.Response": {"filedName": "HTTP.Response", "probe": 0, "analysis": 1, "Name": "回应类型", "Remark": "", "type": "string"}, "DNS": {"filedName": "DNS", "probe": 0, "analysis": 1, "Name": "", "Remark": "", "type": "数组"}, "DNS.Domain": {"filedName": "DNS.Domain", "probe": 0, "analysis": 1, "Name": "本次询问域名", "Remark": "", "type": "string"}, "DNS.DomainIp": {"filedName": "DNS.DomainIp", "probe": 0, "analysis": 1, "Name": "本次答复的IP地址", "Remark": "", "type": "数组"}, "SSL.CH_Ciphersuit": {"filedName": "SSL.CH_Ciphersuit", "probe": 0, "analysis": 1, "Name": "ClientHello密码套件", "Remark": "", "type": "string"}, "SSL.CH_CiphersuitNum": {"filedName": "SSL.CH_CiphersuitNum", "probe": 0, "analysis": 1, "Name": "ClientHello密码套件数量", "Remark": "", "type": "int"}, "SSL.CH_ServerName": {"filedName": "SSL.CH_ServerName", "probe": 0, "analysis": 1, "Name": "服务器名", "Remark": "", "type": "string"}, "SSL.CH_ALPN": {"filedName": "SSL.CH_ALPN", "probe": 0, "analysis": 1, "Name": "应用协议类型", "Remark": "", "type": "json"}, "SSL.sCertHash": {"filedName": "SSL.sCertHash", "probe": 0, "analysis": 1, "Name": "客户端证书Hash列表", "Remark": "", "type": "string"}, "SSL.dCertHash": {"filedName": "SSL.dCertHash", "probe": 0, "analysis": 1, "Name": "服务端证书Hash", "Remark": "", "type": "string"}, "PktNumApp": {"filedName": "PktNumApp", "probe": 0, "analysis": 0, "Name": "pkt.ProNum", "Remark": "", "type": "int"}}, "dns": {"sIp": {"filedName": "sIp", "probe": 0, "analysis": 0, "Name": "源IP地址", "Remark": "", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 0, "Name": "源端口", "Remark": "", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 0, "Name": "目的IP地址", "Remark": "", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 0, "Name": "目的端口", "Remark": "", "type": "int"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 0, "Name": "会话ID：全局唯一", "Remark": "", "type": "string"}, "AppName": {"filedName": "AppName", "probe": 0, "analysis": 0, "Name": "应用名", "Remark": "", "type": "string"}, "TaskId": {"filedName": "TaskId", "probe": 0, "analysis": 0, "Name": "任务ID", "Remark": "", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 0, "Name": "会话起始时间-秒", "Remark": "", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 0, "analysis": 0, "Name": "会话起始时间-纳秒", "Remark": "", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 0, "Name": "记录创建时间", "Remark": "", "type": "int"}, "Flags": {"filedName": "Flags", "probe": 0, "analysis": 0, "Name": "DNS标志位", "Remark": "", "type": "int"}, "Que": {"filedName": "Que", "probe": 0, "analysis": 0, "Name": "询问信息数量", "Remark": "", "type": "int"}, "Ans": {"filedName": "<PERSON>s", "probe": 0, "analysis": 0, "Name": "回答信息数量", "Remark": "", "type": "int"}, "Auth": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "认证信息数量", "Remark": "", "type": "int"}, "Add": {"filedName": "Add", "probe": 0, "analysis": 0, "Name": "附加信息数量", "Remark": "", "type": "int"}, "Query": {"filedName": "Query", "probe": 0, "analysis": 0, "Name": "询问信息", "Remark": "", "type": "json"}, "Query.class": {"filedName": "Query.class", "probe": 0, "analysis": 0, "Name": "询问信息登记", "Remark": "", "type": "int"}, "Query.name": {"filedName": "Query.name", "probe": 0, "analysis": 0, "Name": "询问信息名称", "Remark": "", "type": "string"}, "Query.type": {"filedName": "Query.type", "probe": 0, "analysis": 0, "Name": "询问信息类型", "Remark": "", "type": "int"}, "Answer": {"filedName": "Answer", "probe": 0, "analysis": 0, "Name": "回答信息", "Remark": "", "type": "json数组"}, "Answer.class": {"filedName": "Answer.class", "probe": 0, "analysis": 0, "Name": "回答信息登记", "Remark": "", "type": "int"}, "Answer.name": {"filedName": "Answer.name", "probe": 0, "analysis": 0, "Name": "本次答复对应的询问值", "Remark": "", "type": "string"}, "Answer.ttl": {"filedName": "Answer.ttl", "probe": 0, "analysis": 0, "Name": "会话数据有效期", "Remark": "", "type": "int"}, "Answer.type": {"filedName": "Answer.type", "probe": 0, "analysis": 0, "Name": "回答数据类型", "Remark": "", "type": "int"}, "Answer.data_len": {"filedName": "Answer.data_len", "probe": 0, "analysis": 0, "Name": "回答数据长度", "Remark": "", "type": "int"}, "Answer.value": {"filedName": "Answer.value", "probe": 0, "analysis": 0, "Name": "回答数据值", "Remark": "", "type": "string"}, "Domain": {"filedName": "Domain", "probe": 0, "analysis": 0, "Name": "本次询问域名", "Remark": "", "type": "int"}, "DomainIp": {"filedName": "DomainIp", "probe": 0, "analysis": 0, "Name": "本次答复的IP地址", "Remark": "", "type": "数组"}, "filename": {"filedName": "filename", "probe": 0, "analysis": 0, "Name": "", "Remark": "", "type": "string"}, "row": {"filedName": "row", "probe": 0, "analysis": 0, "Name": "", "Remark": "", "type": "int"}}, "http": {"sIp": {"filedName": "sIp", "probe": 0, "analysis": 0, "Name": "源IP", "Remark": "源IP地址", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 0, "Name": "源端口", "Remark": "源端口", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 0, "Name": "目的IP", "Remark": "目的IP地址", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 0, "Name": "目的端口", "Remark": "目的端口", "type": "int"}, "ServerIP": {"filedName": "ServerIP", "probe": 0, "analysis": 0, "Name": "服务端IP", "Remark": "服务器IP", "type": "string"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 0, "Name": "会话ID", "Remark": "会话ID：全局唯一", "type": "string"}, "TaskId": {"filedName": "TaskId", "probe": 0, "analysis": 0, "Name": "任务ID", "Remark": "任务ID", "type": "int"}, "BatchNum": {"filedName": "BatchNum", "probe": 0, "analysis": 0, "Name": "批次ID", "Remark": "批次ID", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 0, "Name": "起始时间", "Remark": "会话起始时间-秒", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 0, "analysis": 0, "Name": "起始时间(纳秒)", "Remark": "会话起始时间-纳秒", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 0, "Name": "入库时间", "Remark": "记录创建时间", "type": "string"}, "Url": {"filedName": "Url", "probe": 0, "analysis": 0, "Name": "网址", "Remark": "本次请求的URL", "type": "string"}, "Act": {"filedName": "Act", "probe": 0, "analysis": 0, "Name": "请求类型", "Remark": "本次请求动作类型\n-- Get、Post", "type": "string"}, "Host": {"filedName": "Host", "probe": 0, "analysis": 0, "Name": "服务端信息", "Remark": "", "type": "string"}, "Response": {"filedName": "Response", "probe": 0, "analysis": 0, "Name": "回应类型", "Remark": "本次回应值", "type": "string"}, "sHTTPFinger": {"filedName": "sHTTPFinger", "probe": 0, "analysis": 0, "Name": "源端HTTP指纹", "Remark": "源端HTTP指纹", "type": "string"}, "dHTTPFinger": {"filedName": "dHTTPFinger", "probe": 0, "analysis": 0, "Name": "目的HTTP指纹", "Remark": "目的HTTP指纹", "type": "string"}, "Client.Accept": {"filedName": "Client.Accept", "probe": 0, "analysis": 0, "Name": "客户端.支持页面类型", "Remark": "请求的页面类型", "type": "string"}, "Client.Host": {"filedName": "Client.Host", "probe": 0, "analysis": 0, "Name": "客户端.站点信息", "Remark": "服务器主机信息", "type": "string"}, "Client.User-Agent": {"filedName": "Client.User-Agent", "probe": 0, "analysis": 0, "Name": "客户端.客户端信息", "Remark": "客户端信息", "type": "string"}, "Client.Title": {"filedName": "Client.Title", "probe": 0, "analysis": 0, "Name": "客户端.标头列表", "Remark": "Title列表\n-- 按出现顺序", "type": "数组"}, "Client.Payload": {"filedName": "Client.Payload", "probe": 0, "analysis": 0, "Name": "客户端.内容", "Remark": "请求的负责信息；前64字节", "type": "string"}, "Client.Cookie": {"filedName": "Client.<PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "客户端.cookie", "Remark": "cookie 内容", "type": "string"}, "Client.Referer": {"filedName": "<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "客户端.跳转链接", "Remark": "跳转链接", "type": "string"}, "Client.Accept-Language": {"filedName": "Client.Accept-Language", "probe": 0, "analysis": 0, "Name": "客户端.浏览器支持的语言浏览器支持的语言", "Remark": "", "type": "string"}, "Client.Accept-Encoding": {"filedName": "Client.Accept-Encoding", "probe": 0, "analysis": 0, "Name": "客户端.浏览器支持的编码方式", "Remark": "浏览器支持的编码方式", "type": "string"}, "Client.Accept-Charset": {"filedName": "Client.Accept-<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "客户端.浏览器支持的字符集", "Remark": "浏览器支持的字符集", "type": "string"}, "Client.Via": {"filedName": "Client.Via", "probe": 0, "analysis": 0, "Name": "客户端.代理服务器协议代理服务器协议", "Remark": "", "type": "string"}, "Client.Content-Length": {"filedName": "Client.Content-Length", "probe": 0, "analysis": 0, "Name": "客户端.字节数", "Remark": "内容字节数", "type": "string"}, "Client.Content-Type": {"filedName": "Client.Content-Type", "probe": 0, "analysis": 0, "Name": "客户端.内容类型", "Remark": "内容类型", "type": "string"}, "Client.Content-Encoding": {"filedName": "Client.Content-Encoding", "probe": 0, "analysis": 0, "Name": "客户端.编码格式", "Remark": "编码格式", "type": "string"}, "Client.X-Forwarded-For": {"filedName": "Client.X-Forwarded-For", "probe": 0, "analysis": 0, "Name": "客户端.客户端IP和代理IP记录", "Remark": "客户端IP和代理IP记录", "type": "string"}, "Server.Accept-Ranges": {"filedName": "Server.Accept-Ranges", "probe": 0, "analysis": 0, "Name": "服务端.允许断点续传", "Remark": "", "type": "string"}, "Server.Access-Control-Allow-Origin": {"filedName": "Server.Access-Control-Allow-Origin", "probe": 0, "analysis": 0, "Name": "服务端.相应资源共享", "Remark": "该响应的资源允许与给定的origin共享", "type": "string"}, "Server.Content-Encoding": {"filedName": "Server.Content-Encoding", "probe": 0, "analysis": 0, "Name": "服务端.编码格式", "Remark": "编码格式", "type": "string"}, "Server.Content-Length": {"filedName": "Server.Content-Length", "probe": 0, "analysis": 0, "Name": "服务端.字节数", "Remark": "内容字节数", "type": "string"}, "Server.Content-Type": {"filedName": "Server.Content-Type", "probe": 0, "analysis": 0, "Name": "服务端.内容类型", "Remark": "内容类型", "type": "string"}, "Server.Date": {"filedName": "Server.Date", "probe": 0, "analysis": 0, "Name": "服务端.时间", "Remark": "内容时间", "type": "string"}, "Server.Last-Modified": {"filedName": "Server.Last-Modified", "probe": 0, "analysis": 0, "Name": "服务端.修改时间", "Remark": "上次修改时间", "type": "string"}, "Server.Payload": {"filedName": "Server.Payload", "probe": 0, "analysis": 0, "Name": "服务端.内容", "Remark": "请求的负责信息；前64字节", "type": "string"}, "Server.Title": {"filedName": "Server.Title", "probe": 0, "analysis": 0, "Name": "服务端.标头列表", "Remark": "Title列表\n-- 按出现顺序", "type": "数组"}, "Server.Expires": {"filedName": "Server.Expires", "probe": 0, "analysis": 0, "Name": "服务端.缓存有效期", "Remark": "缓存有效期", "type": "string"}, "Server.Server": {"filedName": "Server.Server", "probe": 0, "analysis": 0, "Name": "服务端.服务器信息", "Remark": "服务器信息", "type": "string"}, "row": {"filedName": "row", "probe": 0, "analysis": 0, "Name": "文件偏移", "Remark": "", "type": "int"}, "filename": {"filedName": "filename", "probe": 0, "analysis": 0, "Name": "文件路径", "Remark": "", "type": "string"}, "Sec-WebSocket-Version": {"filedName": "Sec-WebSocket-Version", "probe": 0, "analysis": 0, "Name": "websocket版本", "Remark": "", "type": "string"}, "Sec-WebSocket-Protocol": {"filedName": "Sec-WebSocket-Protocol", "probe": 0, "analysis": 0, "Name": "websocket协议", "Remark": "", "type": "string"}, "Sec-WebSocket-Key": {"filedName": "Sec-WebSocket-Key", "probe": 0, "analysis": 0, "Name": "websocket密钥", "Remark": "", "type": "string"}, "Sec-WebSocket-Accept": {"filedName": "Sec-WebSocket-Accept", "probe": 0, "analysis": 0, "Name": "websocket服务器确认", "Remark": "", "type": "string"}, "Sec-WebSocket-Extensions": {"filedName": "Sec-WebSocket-Extensions", "probe": 0, "analysis": 0, "Name": "websocket拓展信息", "Remark": "", "type": "string"}, "Get": {"filedName": "Get", "probe": 0, "analysis": 0, "Name": "获取", "Remark": "", "type": "string"}, "Post": {"filedName": "Post", "probe": 0, "analysis": 0, "Name": "提交", "Remark": "", "type": "string"}, "HTTP": {"filedName": "HTTP", "probe": 0, "analysis": 0, "Name": "响应", "Remark": "", "type": "string"}, "Referer": {"filedName": "<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "跳转链接", "Remark": "", "type": "string"}, "User-Agent": {"filedName": "User-Agent", "probe": 0, "analysis": 0, "Name": "客户端信息", "Remark": "", "type": "string"}, "Server": {"filedName": "Server", "probe": 0, "analysis": 0, "Name": "服务器软件信息", "Remark": "", "type": "string"}, "Date": {"filedName": "Date", "probe": 0, "analysis": 0, "Name": "当前时间", "Remark": "", "type": "string"}, "Cookie": {"filedName": "<PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "<PERSON><PERSON>", "Remark": "", "type": "string"}, "Last-Modified": {"filedName": "Last-Modified", "probe": 0, "analysis": 0, "Name": "最终修改时间", "Remark": "", "type": "string"}, "Expires": {"filedName": "Expires", "probe": 0, "analysis": 0, "Name": "缓存有效周期", "Remark": "", "type": "string"}, "Content-Type": {"filedName": "Content-Type", "probe": 0, "analysis": 0, "Name": "正文类别", "Remark": "", "type": "string"}, "Content-Length": {"filedName": "Content-Length", "probe": 0, "analysis": 0, "Name": "正文长度", "Remark": "", "type": "string"}, "Content-Encoding": {"filedName": "Content-Encoding", "probe": 0, "analysis": 0, "Name": "正文编码", "Remark": "", "type": "string"}, "Content-Language": {"filedName": "Content-Language", "probe": 0, "analysis": 0, "Name": "正文语言", "Remark": "", "type": "string"}, "Connecttion": {"filedName": "Connecttion", "probe": 0, "analysis": 0, "Name": "传输模式", "Remark": "", "type": "string"}, "Accept": {"filedName": "Accept", "probe": 0, "analysis": 0, "Name": "浏览器支持的MIME类型", "Remark": "", "type": "string"}, "Accept-Language": {"filedName": "Accept-Language", "probe": 0, "analysis": 0, "Name": "浏览器支持的语言", "Remark": "", "type": "string"}, "Accept-Encoding": {"filedName": "Accept-Encoding", "probe": 0, "analysis": 0, "Name": "浏览器支持的编码方式", "Remark": "", "type": "string"}, "Origin": {"filedName": "Origin", "probe": 0, "analysis": 0, "Name": "最初请求来源", "Remark": "", "type": "string"}, "x-xss-Protection": {"filedName": "x-xss-Protection", "probe": 0, "analysis": 0, "Name": "xss防御状态", "Remark": "", "type": "string"}, "X-Content-Type-Options": {"filedName": "X-Content-Type-Options", "probe": 0, "analysis": 0, "Name": "服务器验证正文类型", "Remark": "", "type": "string"}, "x-frame-Options": {"filedName": "x-frame-Options", "probe": 0, "analysis": 0, "Name": "网站Frame防御状态", "Remark": "", "type": "string"}, "strict-transport-security": {"filedName": "strict-transport-security", "probe": 0, "analysis": 0, "Name": "仅支持HTTPS", "Remark": "", "type": "string"}, "public-key-Pins": {"filedName": "public-key-Pins", "probe": 0, "analysis": 0, "Name": "公钥固定", "Remark": "", "type": "string"}, "Age": {"filedName": "Age", "probe": 0, "analysis": 0, "Name": "代理服务器缓存时间", "Remark": "", "type": "string"}, "Etag": {"filedName": "Etag", "probe": 0, "analysis": 0, "Name": "是否加载缓存", "Remark": "", "type": "string"}, "Location": {"filedName": "Location", "probe": 0, "analysis": 0, "Name": "重定向", "Remark": "", "type": "string"}, "Proxy": {"filedName": "Proxy", "probe": 0, "analysis": 0, "Name": "代理", "Remark": "", "type": "string"}, "Retry-After": {"filedName": "Retry-After", "probe": 0, "analysis": 0, "Name": "再次请求等待时间", "Remark": "", "type": "string"}, "Vary": {"filedName": "Vary", "probe": 0, "analysis": 0, "Name": "缓存配置", "Remark": "", "type": "string"}, "www-Auth": {"filedName": "www-Auth", "probe": 0, "analysis": 0, "Name": "认证应答", "Remark": "", "type": "string"}, "upgrade": {"filedName": "upgrade", "probe": 0, "analysis": 0, "Name": "是否启用更高版本", "Remark": "", "type": "string"}}, "ssl": {"sIp": {"filedName": "sIp", "probe": 0, "analysis": 0, "Name": "源IP", "Remark": "源IP地址", "type": "IP"}, "sPort": {"filedName": "sPort", "probe": 0, "analysis": 0, "Name": "源端口", "Remark": "源端口", "type": "int"}, "dIp": {"filedName": "dIp", "probe": 0, "analysis": 0, "Name": "目的IP", "Remark": "目的IP地址", "type": "IP"}, "dPort": {"filedName": "dPort", "probe": 0, "analysis": 0, "Name": "目的端口", "Remark": "目的端口", "type": "int"}, "SessionId": {"filedName": "SessionId", "probe": 0, "analysis": 0, "Name": "会话ID", "Remark": "会话ID：全局唯一", "type": "string"}, "AppName": {"filedName": "AppName", "probe": 0, "analysis": 0, "Name": "应用名", "Remark": "应用；SSL下的应用协议", "type": "string"}, "TaskId": {"filedName": "TaskId", "probe": 0, "analysis": 0, "Name": "任务ID", "Remark": "任务ID", "type": "int"}, "BatchNum": {"filedName": "BatchNum", "probe": 0, "analysis": 0, "Name": "批次ID", "Remark": "", "type": "int"}, "StartTime": {"filedName": "StartTime", "probe": 0, "analysis": 0, "Name": "起始时间", "Remark": "会话起始时间-秒", "type": "int"}, "StartNSec": {"filedName": "StartNSec", "probe": 0, "analysis": 0, "Name": "起始时间(纳秒)", "Remark": "会话起始时间-纳秒", "type": "int"}, "CreateTime": {"filedName": "CreateTime", "probe": 0, "analysis": 0, "Name": "入库时间", "Remark": "记录创建时间", "type": "string"}, "cSSLVersion": {"filedName": "cSSLVersion", "probe": 0, "analysis": 0, "Name": "客户端版本号", "Remark": "SSL客户端版本号", "type": "int"}, "CH_Version": {"filedName": "CH_Version", "probe": 0, "analysis": 0, "Name": "ClientHello版本", "Remark": "ClientHello版本", "type": "int"}, "CH_Time": {"filedName": "CH_Time", "probe": 0, "analysis": 0, "Name": "ClientHello时间戳", "Remark": "ClientHello时间戳", "type": "int"}, "CH_Random": {"filedName": "CH_Random", "probe": 0, "analysis": 0, "Name": "ClientHello随机数", "Remark": "ClientHello的随机数", "type": "string"}, "CH_SessionID": {"filedName": "CH_SessionID", "probe": 0, "analysis": 0, "Name": "ClientHello会话ID", "Remark": "ClientHello的会话ID", "type": "string"}, "CH_SessionIDLen": {"filedName": "CH_SessionIDLen", "probe": 0, "analysis": 0, "Name": "ClientHello会话ID字节数", "Remark": "ClientHello的会话ID字节数", "type": "int"}, "CH_Ciphersuit": {"filedName": "CH_Ciphersuit", "probe": 0, "analysis": 0, "Name": "ClientHello密码套件", "Remark": "ClientHello的密码套件", "type": "string"}, "CH_CiphersuitNum": {"filedName": "CH_CiphersuitNum", "probe": 0, "analysis": 0, "Name": "ClientHello密码套件数量", "Remark": "ClientHello的密码套件数量\n-- 注意不是字节数", "type": "int"}, "CH_CompressionMethod": {"filedName": "CH_CompressionMethod", "probe": 0, "analysis": 0, "Name": "ClientHello压缩方法", "Remark": "ClientHello的压缩方法", "type": "string"}, "CH_CompressionMethodLen": {"filedName": "CH_CompressionMethodLen", "probe": 0, "analysis": 0, "Name": "ClientHello压缩方法数量", "Remark": "ClientHello的压缩方法数量", "type": "int"}, "CH_ExtentionNum": {"filedName": "CH_ExtentionNum", "probe": 0, "analysis": 0, "Name": "ClientHello扩展信息数量", "Remark": "ClientHello的扩展信息数量", "type": "int"}, "CH_Extention": {"filedName": "CH_Extention", "probe": 0, "analysis": 0, "Name": "ClientHello扩展详细信息", "Remark": "ClientHello的扩展详细信息", "type": "json"}, "CH_Extention.l": {"filedName": "CH_Extention.l", "probe": 0, "analysis": 0, "Name": "ClientHello扩展信息长度", "Remark": "ClientHello扩展信息的长度", "type": "int"}, "CH_Extention.t": {"filedName": "CH_Extention.t", "probe": 0, "analysis": 0, "Name": "ClientHello扩展信息类型", "Remark": "ClientHello扩展信息的类型", "type": "int"}, "CH_Extention.v": {"filedName": "CH_Extention.v", "probe": 0, "analysis": 0, "Name": "ClientHello扩展信息值", "Remark": "ClientHello扩展信息的值", "type": "string"}, "CH_ServerName": {"filedName": "CH_ServerName", "probe": 0, "analysis": 0, "Name": "服务器名", "Remark": "ClientHello的服务器名\n-- 扩展信息内存在；单存一份", "type": "string"}, "CH_ServerNameType": {"filedName": "CH_ServerNameType", "probe": 0, "analysis": 0, "Name": "服务器名类型", "Remark": "ClientHello的服务器名的类型", "type": "int"}, "CH_SessionTicket": {"filedName": "CH_SessionTicket", "probe": 0, "analysis": 0, "Name": "增强性会话ID", "Remark": "ClientHello增强性会话ID\n-- 扩展信息内存在；单存一份", "type": "string"}, "CH_ALPN": {"filedName": "CH_ALPN", "probe": 0, "analysis": 0, "Name": "应用协议类型", "Remark": "ClientHello中说明的应用协议类型\n-- 扩展信息内存在；单存一份", "type": "json"}, "sCertHash": {"filedName": "sCertHash", "probe": 0, "analysis": 0, "Name": "客户端证书Hash列表", "Remark": "客户端证书Hash列表", "type": "string"}, "sCertNum": {"filedName": "sCertNum", "probe": 0, "analysis": 0, "Name": "客户端证书数量", "Remark": "客户端证书数量", "type": "int"}, "sKeyExchange": {"filedName": "sKeyExchange", "probe": 0, "analysis": 0, "Name": "客户端密钥交换值", "Remark": "客户端密钥交换值", "type": "string"}, "sKeyExchangeLen": {"filedName": "sKeyExchangeLen", "probe": 0, "analysis": 0, "Name": "客户端密钥交换值字节数", "Remark": "客户端密钥交换值的字节数", "type": "int"}, "sSSLFinger": {"filedName": "s<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "客户端指纹", "Remark": "ssl客户端指纹", "type": "string"}, "sSSLVersion": {"filedName": "sSSLVersion", "probe": 0, "analysis": 0, "Name": "服务端版本号", "Remark": "SSL版本号", "type": "int"}, "SH_Version": {"filedName": "SH_Version", "probe": 0, "analysis": 0, "Name": "ServerHello版本", "Remark": "ServerHello版本", "type": "int"}, "SH_Time": {"filedName": "SH_Time", "probe": 0, "analysis": 0, "Name": "ServerHello时间戳", "Remark": "ServerHello时间戳", "type": "int"}, "SH_Random": {"filedName": "SH_Random", "probe": 0, "analysis": 0, "Name": "ServerHello随机数", "Remark": "ServerHello的随机数", "type": "string"}, "SH_SessionId": {"filedName": "SH_SessionId", "probe": 0, "analysis": 0, "Name": "ServerHello会话ID", "Remark": "ServerHello的会话ID", "type": "string"}, "SH_SessionIdLen": {"filedName": "SH_SessionIdLen", "probe": 0, "analysis": 0, "Name": "ServerHello会话ID字节数", "Remark": "ServerHello的会话ID字节数", "type": "int"}, "SH_Cipersuite": {"filedName": "SH_Cipersuite", "probe": 0, "analysis": 0, "Name": "ServerHello密码套件", "Remark": "ServerHello的密码套件", "type": "string"}, "SH_CompressionMethod": {"filedName": "SH_CompressionMethod", "probe": 0, "analysis": 0, "Name": "ServerHello压缩方法", "Remark": "ServerHello的压缩方法", "type": "string"}, "SH_SessionTicket": {"filedName": "SH_SessionTicket", "probe": 0, "analysis": 0, "Name": "增强性会话ID确认", "Remark": "ServerHello增强性会话ID\n-- 扩展信息内存在；单存一份", "type": "string"}, "SH_ALPN": {"filedName": "SH_ALPN", "probe": 0, "analysis": 0, "Name": "确认应用协议类型", "Remark": "ServerHello中说明的应用协议类型\n-- 扩展信息内存在；单存一份", "type": "json"}, "SH_ExtentionNum": {"filedName": "SH_ExtentionNum", "probe": 0, "analysis": 0, "Name": "ServerHello扩展信息数量", "Remark": "ServerHello的扩展信息数量", "type": "int"}, "SH_Extention": {"filedName": "SH_Extention", "probe": 0, "analysis": 0, "Name": "ServerHello扩展详细信息", "Remark": "ServerHello的扩展详细信息", "type": "json"}, "SH_Extention.l": {"filedName": "SH_Extention.l", "probe": 0, "analysis": 0, "Name": "ServerHello扩展信息长度", "Remark": "ServerHello扩展信息的长度", "type": "int"}, "SH_Extention.t": {"filedName": "SH_Extention.t", "probe": 0, "analysis": 0, "Name": "ServerHello扩展信息类型", "Remark": "ServerHello扩展信息的类型", "type": "int"}, "SH_Extention.v": {"filedName": "SH_Extention.v", "probe": 0, "analysis": 0, "Name": "ServerHello扩展信息值", "Remark": "ServerHello扩展信息的值", "type": "string"}, "dCertHash": {"filedName": "dCertHash", "probe": 0, "analysis": 0, "Name": "服务端证书Hash", "Remark": "服务端证书Hash", "type": "数组"}, "dCertHashStr": {"filedName": "dCertHashStr", "probe": 0, "analysis": 0, "Name": "服务端证书Hash字符串格式", "Remark": "服务端证书Hash字符串格式", "type": "string"}, "dCertNum": {"filedName": "dCertNum", "probe": 0, "analysis": 0, "Name": "服务端证书数量", "Remark": "服务端证书数量", "type": "int"}, "dKeyExchange": {"filedName": "dKeyExchange", "probe": 0, "analysis": 0, "Name": "服务端密钥交互值", "Remark": "服务端密钥交互值", "type": "string"}, "dKeyExchangelen": {"filedName": "dKeyExchangelen", "probe": 0, "analysis": 0, "Name": "服务端密钥交互数据字节数", "Remark": "服务端密钥交互数据字节数", "type": "int"}, "dNewSessionTicket_LifeTime": {"filedName": "dNewSessionTicket_LifeTime", "probe": 0, "analysis": 0, "Name": "新增强型会话ID有效周期", "Remark": "新的增强型会话ID有效周期", "type": "int"}, "dNewSessionTicket_Ticket": {"filedName": "dNewSessionTicket_Ticket", "probe": 0, "analysis": 0, "Name": "新增强型会话ID", "Remark": "新的增强型会话ID", "type": "string"}, "dNewSessionTicket_TicketLen": {"filedName": "dNewSessionTicket_TicketLen", "probe": 0, "analysis": 0, "Name": "新增强型会话ID字节数", "Remark": "新的增强型会话ID字节数", "type": "int"}, "dSSLFinger": {"filedName": "d<PERSON><PERSON><PERSON>", "probe": 0, "analysis": 0, "Name": "服务端指纹", "Remark": "服务端指纹", "type": "string"}}}
@font-face {
  font-family: "iconfont"; /* Project id 3746679 */
  src: url('iconfont.woff2?t=1734076432224') format('woff2'),
       url('iconfont.woff?t=1734076432224') format('woff'),
       url('iconfont.ttf?t=1734076432224') format('truetype'),
       url('iconfont.svg?t=1734076432224#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-nav_offline_line:before {
  content: "\e8c5";
}

.icon-nav_Dashboard_fill:before {
  content: "\e8c6";
}

.icon-nav_Comparison_line:before {
  content: "\e8c7";
}

.icon-nav_Analysis_fill:before {
  content: "\e8c8";
}

.icon-nav_Alarm_fill:before {
  content: "\e8c9";
}

.icon-nav_Analysis_line:before {
  content: "\e8ca";
}

.icon-nav_Dashboard_line:before {
  content: "\e88a";
}

.icon-nav_Opearation_fill:before {
  content: "\e88b";
}

.icon-nav_statistics_line:before {
  content: "\e88d";
}

.icon-nav_Opearation_line:before {
  content: "\e88e";
}

.icon-nav_Setting_line:before {
  content: "\e899";
}

.icon-nav_OfflineAnalysis_fill:before {
  content: "\e8b6";
}

.icon-nav_Relationship_line:before {
  content: "\e8b7";
}

.icon-nav_Dashboard2_fill:before {
  content: "\e8b9";
}

.icon-a-nav_Safetyanalysis_line:before {
  content: "\e8ba";
}

.icon-nav_offline_fill:before {
  content: "\e8bb";
}

.icon-nav_Position_fill:before {
  content: "\e8bf";
}

.icon-nav_Dashboard2_line:before {
  content: "\e8c0";
}

.icon-nav_Model_line:before {
  content: "\e8c1";
}

.icon-nav_Alarm_line:before {
  content: "\e8c2";
}

.icon-nav_Model_fill:before {
  content: "\e8c3";
}

.icon-nav_Comparison_fill:before {
  content: "\e8c4";
}

.icon-nav_Universe_fill:before {
  content: "\e87a";
}

.icon-nav_Task_line:before {
  content: "\e87b";
}

.icon-nav_Upload_line:before {
  content: "\e87c";
}

.icon-nav_Position_line:before {
  content: "\e87d";
}

.icon-nav_SDX_fill:before {
  content: "\e87e";
}

.icon-nav_Universe_line:before {
  content: "\e87f";
}

.icon-nav_Vulnerability_fill:before {
  content: "\e880";
}

.icon-nav_Vulnerability_line:before {
  content: "\e881";
}

.icon-nav_Task_fill:before {
  content: "\e882";
}

.icon-nav_Relationship_fill:before {
  content: "\e883";
}

.icon-nav_Setting_fill:before {
  content: "\e884";
}

.icon-nav_testn_line:before {
  content: "\e885";
}

.icon-nav_OfflineAnalysis_line:before {
  content: "\e886";
}

.icon-nav_SDX_line:before {
  content: "\e887";
}

.icon-nav_statistics_fill:before {
  content: "\e888";
}

.icon-a-nav_Safetyanalysis_fill:before {
  content: "\e889";
}

.icon-nav_testn_fill:before {
  content: "\e878";
}

.icon-nav_Upload_fill:before {
  content: "\e879";
}

.icon-icon_appservice:before {
  content: "\e8be";
}

.icon-navicon_baseline:before {
  content: "\e8bd";
}

.icon-a-24_collect:before {
  content: "\e8b8";
}

.icon-a-30_Option:before {
  content: "\e8bc";
}

.icon-a-16_slicenum:before {
  content: "\e8b5";
}

.icon-a-16_listnum:before {
  content: "\e8b2";
}

.icon-a-16_runtaskname:before {
  content: "\e8b4";
}

.icon-a-16_adminname:before {
  content: "\e8b1";
}

.icon-a-16_runtime:before {
  content: "\e8ae";
}

.icon-a-16_history1:before {
  content: "\e8af";
}

.icon-a-16_state:before {
  content: "\e8b0";
}

.icon-a-16_time1:before {
  content: "\e8b3";
}

.icon-a-16_OSname:before {
  content: "\e8a9";
}

.icon-a-16_linknum:before {
  content: "\e8aa";
}

.icon-a-16_port:before {
  content: "\e8ab";
}

.icon-a-16_cannynum:before {
  content: "\e8ac";
}

.icon-a-16_filenum:before {
  content: "\e8ad";
}

.icon-a-16_version:before {
  content: "\e8a4";
}

.icon-a-16_computer2:before {
  content: "\e8a5";
}

.icon-a-16_indexs:before {
  content: "\e8a6";
}

.icon-a-16_modelname:before {
  content: "\e8a7";
}

.icon-a-16_cannynum2:before {
  content: "\e8a8";
}

.icon-a-16_groupname:before {
  content: "\e8a0";
}

.icon-a-16_model1:before {
  content: "\e8a1";
}

.icon-a-16_SNcode:before {
  content: "\e8a2";
}

.icon-a-16_pointnum:before {
  content: "\e8a3";
}

.icon-a-16_system:before {
  content: "\e89e";
}

.icon-a-16_cpu:before {
  content: "\e89f";
}

.icon-a-16_harddisk1:before {
  content: "\e89a";
}

.icon-a-16_memory1:before {
  content: "\e89b";
}

.icon-a-16_course1:before {
  content: "\e89c";
}

.icon-a-16_computer1:before {
  content: "\e89d";
}

.icon-a-16_internetlock:before {
  content: "\e897";
}

.icon-a-16_lock:before {
  content: "\e898";
}

.icon-icon_question:before {
  content: "\e88f";
}

.icon-icon_warning:before {
  content: "\e890";
}

.icon-icon_check:before {
  content: "\e891";
}

.icon-icon_danger:before {
  content: "\e892";
}

.icon-a-16_refresh:before {
  content: "\e88c";
}

.icon-a-16_list2:before {
  content: "\e877";
}

.icon-a-16_fullright-copy:before {
  content: "\e896";
}

.icon-a-16_add2:before {
  content: "\e82f";
}

.icon-a-16_add1:before {
  content: "\e830";
}

.icon-a-16_add:before {
  content: "\e831";
}

.icon-a-16_fullleft-copy:before {
  content: "\e894";
}

.icon-a-16_alert:before {
  content: "\e832";
}

.icon-a-16_alert_line:before {
  content: "\e833";
}

.icon-a-16_card:before {
  content: "\e834";
}

.icon-a-16_close:before {
  content: "\e835";
}

.icon-a-16_copy:before {
  content: "\e836";
}

.icon-a-16_collect:before {
  content: "\e837";
}

.icon-a-16_computer:before {
  content: "\e838";
}

.icon-a-16_course:before {
  content: "\e839";
}

.icon-a-16_datecopy34:before {
  content: "\e83a";
}

.icon-a-16_delete:before {
  content: "\e83b";
}

.icon-a-16_down:before {
  content: "\e83c";
}

.icon-a-16_download:before {
  content: "\e83d";
}

.icon-a-16_date:before {
  content: "\e83e";
}

.icon-a-16_datecopy33:before {
  content: "\e83f";
}

.icon-a-16_fulldown:before {
  content: "\e840";
}

.icon-a-16_filter:before {
  content: "\e841";
}

.icon-a-16_folder:before {
  content: "\e842";
}

.icon-a-16_fullright-copy1:before {
  content: "\e895";
}

.icon-a-16_fullfile:before {
  content: "\e843";
}

.icon-a-16_edit:before {
  content: "\e844";
}

.icon-a-16_fullrank:before {
  content: "\e845";
}

.icon-a-16_fullright:before {
  content: "\e846";
}

.icon-a-16_CPU:before {
  content: "\e847";
}

.icon-a-16_enter:before {
  content: "\e848";
}

.icon-a-16_fullscreen:before {
  content: "\e849";
}

.icon-a-16_error:before {
  content: "\e84a";
}

.icon-a-16_fullleft:before {
  content: "\e84b";
}

.icon-a-16_harddisk:before {
  content: "\e84c";
}

.icon-a-16_fulltop:before {
  content: "\e84d";
}

.icon-a-16_import:before {
  content: "\e84e";
}

.icon-a-16_history:before {
  content: "\e84f";
}

.icon-a-16_info2:before {
  content: "\e850";
}

.icon-a-16_left:before {
  content: "\e851";
}

.icon-a-16_link:before {
  content: "\e852";
}

.icon-a-16_logout:before {
  content: "\e853";
}

.icon-a-16_model:before {
  content: "\e854";
}

.icon-a-16_info1:before {
  content: "\e855";
}

.icon-a-16_minus2:before {
  content: "\e856";
}

.icon-a-16_rank:before {
  content: "\e857";
}

.icon-a-16_minus:before {
  content: "\e858";
}

.icon-a-16_memory:before {
  content: "\e859";
}

.icon-a-16_right:before {
  content: "\e85a";
}

.icon-a-16_linktrust:before {
  content: "\e85b";
}

.icon-a-16_password:before {
  content: "\e85c";
}

.icon-a-16_more:before {
  content: "\e85d";
}

.icon-a-16_running:before {
  content: "\e85e";
}

.icon-a-16_search1:before {
  content: "\e85f";
}

.icon-a-16_pickup:before {
  content: "\e860";
}

.icon-a-16_pause:before {
  content: "\e861";
}

.icon-a-16_alert-copy:before {
  content: "\e893";
}

.icon-a-16_save:before {
  content: "\e862";
}

.icon-a-16_sysmet:before {
  content: "\e863";
}

.icon-a-16_stop2:before {
  content: "\e864";
}

.icon-a-16_switch:before {
  content: "\e865";
}

.icon-a-16_rule:before {
  content: "\e866";
}

.icon-a-16_share:before {
  content: "\e867";
}

.icon-a-16_sysmet2:before {
  content: "\e868";
}

.icon-a-16_stop1:before {
  content: "\e869";
}

.icon-a-16_shutdown:before {
  content: "\e86a";
}

.icon-a-16_timeforward:before {
  content: "\e86b";
}

.icon-a-16_success:before {
  content: "\e86c";
}

.icon-a-16_timeback:before {
  content: "\e86d";
}

.icon-a-16_words:before {
  content: "\e86e";
}

.icon-a-16_unfold:before {
  content: "\e86f";
}

.icon-a-16_tick:before {
  content: "\e870";
}

.icon-a-16_top:before {
  content: "\e871";
}

.icon-a-16_start:before {
  content: "\e872";
}

.icon-a-16_upload:before {
  content: "\e873";
}

.icon-a-16_threat:before {
  content: "\e874";
}

.icon-a-16_view:before {
  content: "\e875";
}

.icon-a-16_setting:before {
  content: "\e876";
}

.icon-a-24_admin:before {
  content: "\e829";
}

.icon-a-24_blockchain:before {
  content: "\e81f";
}

.icon-a-24_Time:before {
  content: "\e820";
}

.icon-a-24_MAC:before {
  content: "\e821";
}

.icon-a-24_mail:before {
  content: "\e822";
}

.icon-a-24_system:before {
  content: "\e823";
}

.icon-a-ArtboardCopy29:before {
  content: "\e824";
}

.icon-a-24_hardware:before {
  content: "\e825";
}

.icon-a-24_task:before {
  content: "\e826";
}

.icon-a-24_UA:before {
  content: "\e827";
}

.icon-a-24_harddisk:before {
  content: "\e828";
}

.icon-a-24_maillink:before {
  content: "\e82a";
}

.icon-a-24_web:before {
  content: "\e82b";
}

.icon-a-24_sufferer:before {
  content: "\e82c";
}

.icon-a-24_maillocation:before {
  content: "\e82d";
}

.icon-a-24_APT:before {
  content: "\e82e";
}

.icon-a-24_anchor:before {
  content: "\e80f";
}

.icon-a-24_app:before {
  content: "\e810";
}

.icon-a-24_attack:before {
  content: "\e811";
}

.icon-a-24_apptype:before {
  content: "\e812";
}

.icon-a-24_alert:before {
  content: "\e813";
}

.icon-a-24_certificate:before {
  content: "\e814";
}

.icon-a-24_label:before {
  content: "\e815";
}

.icon-a-24_attack-sufferer:before {
  content: "\e816";
}

.icon-a-24_Delete:before {
  content: "\e817";
}

.icon-a-24_client:before {
  content: "\e818";
}

.icon-a-24_download:before {
  content: "\e819";
}

.icon-a-24_enterprise:before {
  content: "\e81a";
}

.icon-a-24_IP:before {
  content: "\e81b";
}

.icon-a-24_ID:before {
  content: "\e81c";
}

.icon-a-24_fingerprint:before {
  content: "\e81d";
}

.icon-a-24_attacker:before {
  content: "\e81e";
}

.icon-a-12_label:before {
  content: "\e800";
}

.icon-a-12_plus:before {
  content: "\e801";
}

.icon-a-12_close:before {
  content: "\e802";
}

.icon-a-12_status:before {
  content: "\e803";
}

.icon-a-12_tag:before {
  content: "\e804";
}

.icon-a-12_invisible:before {
  content: "\e805";
}

.icon-a-12_location:before {
  content: "\e806";
}

.icon-a-12_down:before {
  content: "\e807";
}

.icon-a-12_remark:before {
  content: "\e808";
}

.icon-a-12_up:before {
  content: "\e809";
}

.icon-a-12_minus:before {
  content: "\e80a";
}

.icon-a-12_time:before {
  content: "\e80b";
}

.icon-a-12_view:before {
  content: "\e80c";
}

.icon-a-12_task:before {
  content: "\e80d";
}

.icon-a-12_hiddenlabel:before {
  content: "\e80e";
}

.icon-a-16_filefill:before {
  content: "\e7ff";
}

.icon-a-16_info:before {
  content: "\e7fe";
}

.icon-a-16_search:before {
  content: "\e7fb";
}

.icon-a-16_time:before {
  content: "\e7fc";
}

.icon-a-16_speed:before {
  content: "\e7fd";
}

.icon-a-16_file2:before {
  content: "\e7f8";
}

.icon-a-16_stop:before {
  content: "\e7f9";
}

.icon-a-16_check:before {
  content: "\e7fa";
}


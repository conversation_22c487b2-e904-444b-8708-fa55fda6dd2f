export default {
  data() {
    return {
      page: {
        page_size: 10,
        current_page: 1,
      },
      sizes: [10, 20, 30, 50],
      total: 0,
      layout: 'total,prev, pager, next,sizes, jumper'
    };
  },
  methods: {
    handleSizeChange(val) {
      this.page.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.page.current_page = val;
      this.getList();
    }
  }
};

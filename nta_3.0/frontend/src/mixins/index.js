export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title:{
      type:String,
      default:'提示'
    },
    headerTitle: {
      type: String,
      default: "提示",
    },
  },
  computed: {
    isShow: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit("input", value);
      },
    },
    // 表格头部样式
    headerStyle() {
      return { color: "#1B428D", background: "#F2F7FF" };
    },
  },
};

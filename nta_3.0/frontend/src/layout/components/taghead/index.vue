<template>
  <div class="taghead">
    <div class="bac"></div>
    <div class="headtag">
      <el-tabs type="border-card" closable>
        <el-tab-pane label="消息中心">消息中心</el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
    name:"TagHead",
    
};
</script>

<style lang="scss" scoped>
.taghead {
  position: relative;
  //   display: flex;
  //   justify-content: center;
  width: 100%;
  .bac {
    border-top: 1px solid #f2f3f7;
    width: 100%;
    background: #ffffff;
    height: 50px;
  }
  .headtag {
    width: 95%;
    height: 38px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -36%);
    ::v-deep {
      .el-tabs--border-card {
        box-shadow: none !important;
        border: 0;
      }
      .el-tabs__nav-scroll {
        background: #ffffff !important;
        font-weight: 400;
        font-size: 14px;
        color: #767684;
      }
      .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
        background: #f2f3f7;
        font-weight: 500;
        font-size: 14px;
        color: #2c2c35;
      }
      .el-tabs__content {
        background: #eef1f9;
      }
      .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
        border: 0;
        border-radius: 10px;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
      }
      .el-tabs--border-card > .el-tabs__header .el-tabs__item {
        border: 0;
      }
    }
  }
}
</style>
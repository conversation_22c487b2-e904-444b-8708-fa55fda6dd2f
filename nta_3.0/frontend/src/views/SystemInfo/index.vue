<template>
  <div class="systembox">
    <!-- <div class="headtitle">系统信息</div> -->
    <div class="systembox-top">
      <!-- 本地磁盘空间 -->
      <div class="systembox-top-l">
        <div class="title">
          <svg-icon icon-class="diskderive" style="margin-right: 8px" />
          本地磁盘空间
        </div>
        <div v-if="show1" class="waringtext">
          本地磁盘空间占用已高于90%，请尽快处理!
        </div>
        <article>
          <div class="systembox-top-l-box1">
            <v-chart ref="chart" :option="taskline_options" autoresize :update-options="{ notMerge: true }">
            </v-chart>
            <div class="ifon">
              <span>0%</span>
              <span>使用占比</span>
              <span>100%</span>
            </div>
          </div>
        </article>
        <div class="systembox-top-l-box2">
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">总量</span>
            <span style="font-size: 24px;">{{
              TOOL_NUMBER(parseFloat(sysinfo.sum_fdisk).toFixed(2))
            }}TB</span>
          </div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">余量</span>
            <span style="font-size: 24px;">{{
              TOOL_NUMBER(parseFloat(sysinfo.free_fdisk).toFixed(2))
            }}TB</span>
          </div>
          <div :class="parseInt(sysinfo.io_state) > 90 ? 'active' : 'noactive'">
            <span class="fontstyle" style="margin-bottom: 4px; color: #ffffff">I/O使用率</span>
            <span>{{ sysinfo.io_state }}%</span>
          </div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">近期新增</span>
            <div class="newly">
              <v-chart :option="category_options" autoresize :update-options="{ notMerge: true }">
              </v-chart>
            </div>
          </div>
        </div>
      </div>
      <!-- CPU -->
      <div class="systembox-top-m">
        <div class="title">
          <svg-icon icon-class="cpu" style="margin-right: 8px" />
          CPU
        </div>
        <div v-if="show2" class="waringtext">CPU占用已高于90%!</div>
        <div class="systembox-top-m-gress1">
          <el-progress type="circle" :percentage="percentage" :stroke-width="num"></el-progress>
          <div class="text">使用占比</div>
        </div>
        <div class="systembox-top-m-box">
          <div class="fontstyle" style="margin-bottom: 4px">处理器</div>
          <div>{{ sysinfo.cpu_info }}</div>
        </div>
        <div class="systembox-top-m-box1">
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">核心</span>
            <span>{{ sysinfo.cpu_ker }}核</span>
            <!-- <span>24核</span> -->
          </div>
          <div class="line"></div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">CPU负载平均值</span>
            <span>{{ sysinfo.cpu_average }}%</span>
          </div>
        </div>
      </div>
      <!-- 内存 -->
      <div class="systembox-top-r">
        <div class="title">
          <svg-icon icon-class="memory" style="margin-right: 8px" />
          内存
          <div v-if="show3" class="waringtext">物理内存占用已高于90%!</div>
        </div>
        <div class="systembox-top-r-gress">
          <el-progress type="circle" :percentage="parseFloat(percentage2)" :stroke-width="num"></el-progress>
          <div class="text">使用占比</div>
        </div>
        <div class="systembox-top-r-box1">
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">总量</span>
            <span>{{ sysinfo.sum_mem | formatMB }}</span>
          </div>
          <div class="line"></div>
          <div>
            <span class="fontstyle" style="margin-bottom: 4px">余量</span>
            <span>{{ sysinfo.free_mem | formatMB }}</span>
          </div>
        </div>
        <div class="systembox-top-r-box">
          <div class="fontstyle" style="
              margin-bottom: 4px;
              display: flex;
              justify-content: space-between;
            "
          >
            虚拟内存使用率<span style="color: #2c2c35">{{ sysinfo.swap_status }}%</span>
          </div>
          <div style="position: relative">
            <el-progress :class="showProgressColor(parseInt(sysinfo.swap_status))"
                         :percentage="parseInt(sysinfo.swap_status)" status="warning" :format="format"
            ></el-progress>
            <div class="whitebox">
              <div v-for="item in 9" :key="item"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="systembox-down">
      <div class="systembox-down-l">
        <!-- 系统相关 -->
        <div class="top">
          <div class="title">
            <svg-icon icon-class="systemAbout" style="margin-right: 8px" />
            系统相关
          </div>
          <div>
            <span class="headline">系统主机名</span>
            <span class="headline-l">{{ sysinfo.hostname }}</span>
          </div>
          <div>
            <span class="headline">操作系统</span>
            <span class="headline-l">{{ sysinfo.osinfo }}</span>
          </div>
          <div>
            <span class="headline">系统时间</span>
            <span class="headline-l">{{ sysinfo.timeS }}</span>
          </div>
          <div>
            <span class="headline">系统运行时间</span>
            <span class="headline-l">{{
              sysinfo.run_time | convertSecToHHmmss
            }}</span>
          </div>
        </div>
        <!-- 产品相关 -->
        <div class="down">
          <div class="title">
            <svg-icon icon-class="product-icon" style="margin-right: 8px" />
            产品相关
          </div>
          <div>
            <span class="headline">产品型号</span>
            <span class="headline-l">{{ sysinfo.product }}</span>
          </div>
          <div>
            <span class="headline">产品版本号</span>
            <span class="headline-l">{{ sysinfo.version }}</span>
          </div>
          <div>
            <span class="headline">产品SN码</span>
            <span class="headline-l">{{ sysinfo.SN }}</span>
          </div>
          <div>
            <span class="headline">产品到期时间</span>
            <span class="headline-l">{{
              moment
                .unix(parseInt(sysinfo.privileged_time))
                .format("YYYY-MM-DD HH:mm:ss")
            
            
            
            
            }}</span>
          </div>
        </div>
      </div>
      <!-- 当前进程信息 -->
      <div class="systembox-down-r">
        <div class="title">
          <svg-icon icon-class="course-icon" style="margin-right: 8px" />
          当前进程信息
        </div>
        <div class="systembox-down-r-tab">
          <el-tabs v-model="tabPosition" type="card" @tab-click="tabclick">
            <el-tab-pane v-for="(item, index) in this.psinfo" :key="index" :label="item.name" :name="item.name">
              <div class="box">
                <div>
                  <span class="headline">进程开始时间</span>
                  <span class="headline-l">{{
                    moment
                      .unix(parseInt(item.begintime))
                      .format("YYYY-MM-DD HH:mm:ss")
                  }}</span>
                </div>
                <div>
                  <span class="headline">CPU占用时间</span>
                  <span class="headline-l">{{
                    item.cpu_times | formatSEC
                  }}</span>
                </div>
                <div>
                  <span class="headline">内存占用</span>
                  <span class="headline-l">{{ item.meminfo | formatB }}</span>
                </div>
                <div>
                  <span class="headline">进程名称</span>
                  <span class="headline-l">{{ item.name }}</span>
                </div>
                <div>
                  <span class="headline">进程线程数</span>
                  <span class="headline-l">{{ item.thread_num }}</span>
                </div>
                <div>
                  <span class="headline">运行进程用户</span>
                  <span class="headline-l">{{ item.username }}</span>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- TODO -->
    <svg v-if="percentagesvg1" width="1" height="1">
      <defs>
        <linearGradient id="write" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color: #116EF9" stop-opacity="0.5"></stop>
          <stop offset="100%" style="stop-color: #116EF9" stop-opacity="1"></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg2" width="1" height="1">
      <defs>
        <linearGradient id="write" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color: #FA8213" stop-opacity="0.5"></stop>
          <stop offset="100%" style="stop-color: #FA8213" stop-opacity="1"></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg3" width="1" height="1">
      <defs>
        <linearGradient id="write" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color: #F91111" stop-opacity="0.5"></stop>
          <stop offset="100%" style="stop-color: #F91111" stop-opacity="1"></stop>
        </linearGradient>
      </defs>
    </svg>
    <!-- -------- -->
    <svg v-if="percentagesvg4" width="1" height="1">
      <defs>
        <linearGradient id="write2" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color: #116EF9" stop-opacity="0.5"></stop>
          <stop offset="100%" style="stop-color: #116EF9" stop-opacity="1"></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg5" width="1" height="1">
      <defs>
        <linearGradient id="write2" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color: #FA8213" stop-opacity="0.5"></stop>
          <stop offset="100%" style="stop-color: #FA8213" stop-opacity="1"></stop>
        </linearGradient>
      </defs>
    </svg>
    <svg v-if="percentagesvg6" width="1" height="1">
      <defs>
        <linearGradient id="write2" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color: #F91111" stop-opacity="0.5"></stop>
          <stop offset="100%" style="stop-color: #F91111" stop-opacity="1"></stop>
        </linearGradient>
      </defs>
    </svg>
  </div>
</template>

<script>
import { taskline_options, category_options } from "./systeminfoline";
// import { getsysteminfo } from "@/api/system";
import moment from "moment";
export default {
  filters: {
    formatB(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toPrecision(3) + " " + sizes[i];
    },
    formatMB(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return (bytes / Math.pow(k, i)).toPrecision(3) + " " + sizes[i];
    },
    formatTB(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      return (bytes * Math.pow(k, 1)).toPrecision(3) + " " + "GB";
    },
    formatSEC(value) {
      const time = moment.duration(value, "seconds");
      const hours = time.hours();
      const minutes = time.minutes();
      const seconds = time.seconds();
      return moment({ h: hours, m: minutes, s: seconds }).format("HH:mm:ss");
    },
    convertSecToHHmmss(sec) {
      let currentTime = moment.duration(sec, "seconds");
      // 将获取到的下轮间隔秒数转换成时分秒
      return moment({
        h: currentTime.hours(),
        m: currentTime.minutes(),
        s: currentTime.seconds(),
      }).format("HH:mm:ss");
    },
  },

  data() {
    return {
      moment,
      taskline_options,
      category_options,
      tabPosition: "探针主程序",
      // 产品相关数据
      product: {},
      // 系统相关数据
      system: {},
      // 显示数据
      sysinfo: {},
      psinfo: {},
      fdiskdata: [],
      // 进度条设置
      num: 12,
      // cpu
      percentage: 90,
      // 内存
      percentage2: 65,
      // cpu 进度条设置
      percentagesvg1: false,
      percentagesvg2: false,
      percentagesvg3: false,
      // 内存 进度条设置
      percentagesvg4: false,
      percentagesvg5: false,
      percentagesvg6: false,
      // 警告显示
      show1: false,
      show2: false,
      show3: false,
      show4: false,
    };
  },
  watch: {
    "$store.state.long.tableType0": {
      immediate: true,
      handler: function (val, old) {
        this.percentage = parseInt(val.sys_info[0].cpu_status);
        this.percentage2 = val.sys_info[0].mem_status;
        this.taskline_options.series[0].data[0].value =
          val.sys_info[0].fdisk_status;
        this.sysinfo = val.sys_info[0];
        this.psinfo = JSON.parse(val.sys_info[0].ps_info);
        this.fdiskdata = val.fdisk_data;
        let timedata = [];
        let xdata = [];
        this.fdiskdata.forEach((item) => {
          timedata.push(item.date.substr(-2));
          xdata.push(item.fdisk);
        });

        let currentTime = moment().month("DD").format("DD");
        let newdata = timedata.reverse().splice(2, 5);
        for (let i in newdata) {
          if (newdata[i] === currentTime) {
            newdata[i] = "今";
          }
        }
        this.category_options.xAxis.data = newdata;
        this.category_options.series[0].data = xdata.reverse().splice(2, 5);
        this.showProgressColor();
        if (parseInt(this.sysinfo.fdisk_status) >= 90) {
          this.show1 = true;
        } else {
          this.show1 = false;
        }
        if (this.percentage >= 90) {
          this.show2 = true;
        } else {
          this.show2 = false;
        }
        if (parseInt(this.mem_status) >= 90) {
          this.show3 = true;
        } else {
          this.show3 = false;
        }
        if (parseInt(this.sysinfo.io_state) >= 9) {
        }
      },
    },
  },
  mounted() {
    // getsysteminfo().then((res) => {
    //   if (res.err == 0) {
    //     this.product = JSON.parse(res.data.product);
    //     console.log(this.product.privilegedTime);
    //     this.product.privilegedTime = moment(
    //       parseInt(this.product.privilegedTime)
    //     ).format("YYYY-MM-DD HH:mm:ss");
    //     this.system = JSON.parse(res.data.system);
    //     console.log(this.system.time);
    //     this.system.time = moment(this.system.time).format(
    //       "YYYY-MM-DD HH:mm:ss"
    //     );
    //   }
    // });
    // cpu进度圈的颜色判断
    if (1 < this.percentage && this.percentage <= 59) {
      console.log("蓝色");
      this.percentagesvg1 = true;
      this.percentagesvg2 = false;
      this.percentagesvg3 = false;
    } else if (60 <= this.percentage && this.percentage <= 89) {
      console.log("橙色");

      this.percentagesvg1 = false;
      this.percentagesvg2 = true;
      this.percentagesvg3 = false;
    } else if (90 <= this.percentage && this.percentage <= 100) {
      console.log("红色");
      this.percentagesvg1 = false;
      this.percentagesvg2 = false;
      this.percentagesvg3 = true;
    }
    // 内存进度圈的颜色判断
    if (1 < this.percentage2 && this.percentage2 <= 59) {
      console.log("蓝色");
      this.percentagesvg4 = true;
      this.percentagesvg5 = false;
      this.percentagesvg6 = false;
    } else if (60 <= this.percentage2 && this.percentage2 <= 89) {
      console.log("橙色");

      this.percentagesvg4 = false;
      this.percentagesvg5 = true;
      this.percentagesvg6 = false;
    } else if (90 <= this.percentage2 && this.percentage2 <= 100) {
      console.log("红色");
      this.percentagesvg4 = false;
      this.percentagesvg5 = false;
      this.percentagesvg6 = true;
    }
  },
  methods: {
    tabclick(e) {
      console.log(e.name);
    },
    format(percentage) {
      return percentage === "";
    },
    showProgressColor: function (val) {
      if (1 < val && val <= 59) {
        return (val = "el-bg-inner-running");
      } else if (60 <= val && val <= 89) {
        return (val = "el-bg-inner-error");
      } else if (90 <= val && val <= 100) {
        return (val = "el-bg-inner-done");
      }
    },
    // 解决JS强制科学计数
    TOOL_NUMBER(num_str) {
      num_str = num_str.toString();
      if (num_str.indexOf("+") != -1) {
        num_str = num_str.replace("+", "");
      }
      if (num_str.indexOf("E") != -1 || num_str.indexOf("e") != -1) {
        var resValue = "",
          power = "",
          result = null,
          dotIndex = 0,
          resArr = [],
          sym = "";
        var numStr = num_str.toString();
        if (numStr[0] == "-") {
          // 如果为负数，转成正数处理，先去掉‘-’号，并保存‘-’.
          numStr = numStr.substr(1);
          sym = "-";
        }
        if (numStr.indexOf("E") != -1 || numStr.indexOf("e") != -1) {
          var regExp = new RegExp(
            "^(((\\d+.?\\d+)|(\\d+))[Ee]{1}((-(\\d+))|(\\d+)))$",
            "ig"
          );
          result = regExp.exec(numStr);
          if (result != null) {
            resValue = result[2];
            power = result[5];
            result = null;
          }
          if (!resValue && !power) {
            return false;
          }
          dotIndex = resValue.indexOf(".") == -1 ? 0 : resValue.indexOf(".");
          resValue = resValue.replace(".", "");
          resArr = resValue.split("");
          if (Number(power) >= 0) {
            var subres = resValue.substr(dotIndex);
            power = Number(power);
            //幂数大于小数点后面的数字位数时，后面加0
            for (var i = 0; i <= power - subres.length; i++) {
              resArr.push("0");
            }
            if (power - subres.length < 0) {
              resArr.splice(dotIndex + power, 0, ".");
            }
          } else {
            power = power.replace("-", "");
            power = Number(power);
            //幂数大于等于 小数点的index位置, 前面加0
            for (var i = 0; i < power - dotIndex; i++) {
              resArr.unshift("0");
            }
            var n = power - dotIndex >= 0 ? 1 : -(power - dotIndex);
            resArr.splice(n, 0, ".");
          }
        }
        resValue = resArr.join("");

        return sym + resValue;
      } else {
        return num_str;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.systembox {

  .headtitle {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #000000;
    margin-bottom: 14px;
  }

  .title {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #2c2c35;
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .whitebox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: absolute;
    top: 4.2px;

    div {
      width: 1.5px;
      height: 8px;
      background: #f7f8fa;
      border-radius: 1px;
    }
  }

  .fontstyle {
    font-family: "Alibaba PuHuiTi";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #767684;
  }

  &-top {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #000000;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    &-l {
      position: relative;
      padding: 16px;
      min-width: 856px;
      height: 448px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      margin-right: 16px;

      .waringtext {
        border-radius: 8px;
        padding: 0 5px;
        background: #fce7e7;
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translate(-50%, 0);
        color: #f56c6c;
        font-size: 10px;
      }

      &-box1 {
        width: 100%;
        height: 380px;
        position: relative;

        .ifon {
          font-weight: 400;
          font-size: 20px;
          line-height: 28px;
          color: #9999a1;
          width: 40%;
          position: absolute;
          bottom: 75px;
          left: 255px;
          display: flex;
          justify-content: space-between;
        }
      }

      &-box2 {
        padding: 0 8px;
        position: absolute;
        bottom: 15px;
        left: 0px;
        width: 100%;
        display: flex;
        justify-content: space-between;

        .noactive {
          background: linear-gradient(122.58deg, #116EF9 30.51%, rgba(17, 110, 249, 0.7) 100%);
          font-weight: 500;
          font-size: 24px;
          line-height: 32px;
          color: #ffffff;
          border: 1px solid #4a97ff;
          border-radius: 8px;
        }

        .active {
          background: linear-gradient(122.58deg, #F91111 30.51%, rgba(249, 17, 17, 0.7) 100%);
          font-weight: 500;
          font-size: 24px;
          line-height: 32px;
          color: #ffffff;
          border: 1px solid #f91111;
          border-radius: 8px;
        }

        div {
          padding: 12px;
          width: 200px;
          height: 74px;
          background: #f7f8fa;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .newly {
          padding: 0 !important;
          width: 100%;
        }
      }
    }

    &-m {
      position: relative;
      padding: 16px;
      margin-right: 16px;
      // width: 422px;
      width: 100%;
      height: 448px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;

      .waringtext {
        border-radius: 8px;
        padding: 0 5px;
        background: #fce7e7;
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translate(-50%, 0);
        color: #f56c6c;
        font-size: 10px;
      }

      &-box {
        padding: 12px;
        width: 100%;
        height: 74px;
        background: #f7f8fa;
        border-top: 1px solid #f2f3f7;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      &-box1 {
        margin-top: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        div {
          padding: 12px;
          // width: 128px;
          width: 100%;
          height: 74px;
          background: #f7f8fa;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .line {
          width: 8px;
          height: 74px;
          background: #fff;
        }
      }

      &-gress1 {
        position: relative;
        margin: 30px 0;
        display: flex;
        justify-content: center;

        ::v-deep {
          .el-progress-circle {
            width: 170px !important;
            height: 170px !important;
          }

          svg>path:nth-child(2) {
            stroke: url(#write);
          }

          .el-progress__text {
            color: #000000;
            font-weight: bold;
            font-size: 24px !important;
          }
        }

        .text {
          position: absolute;
          bottom: 46px;
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #9999a1;
        }
      }
    }

    &-r {
      position: relative;
      padding: 16px;
      // width: 422px;
      width: 100%;
      height: 448px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;

      .waringtext {
        border-radius: 8px;
        padding: 0 5px;
        background: #fce7e7;
        position: absolute;
        top: 30px;
        left: 50%;
        transform: translate(-50%, 0);
        color: #f56c6c;
        font-size: 10px;
      }

      &-box {
        margin-top: 8px;
        padding: 12px;
        // width: 264px;
        width: 100%;
        height: 74px;
        background: #f7f8fa;
        border-top: 1px solid #f2f3f7;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: center;

        ::v-deep {
          .el-progress-bar__outer {
            width: 100%;
            height: 8px !important;
          }

          .el-progress-bar {
            padding: 0 !important;
          }

          .el-icon-warning:before {
            content: none;
          }

          .el-bg-inner-running .el-progress-bar__inner {
            border-radius: 0 !important;
            background-color: unset;
            background: linear-gradient(122.58deg, #116EF9 30.51%, rgba(17, 110, 249, 0.7) 100%);
          }

          .el-bg-inner-error .el-progress-bar__inner {
            border-radius: 0 !important;
            background-color: unset;
            background: linear-gradient(122.58deg, #FA8213 30.51%, rgba(250, 130, 19, 0.7) 100%);
          }

          .el-bg-inner-done .el-progress-bar__inner {
            border-radius: 0 !important;
            background-color: unset;
            background: linear-gradient(122.58deg, #F91111 30.51%, rgba(249, 17, 17, 0.7) 100%);
          }

          .el-progress-bar__outer {
            border-radius: 0 !important;
          }
        }
      }

      &-box1 {
        display: flex;
        justify-content: space-between;
        align-items: center;

        div {
          padding: 12px;
          // width: 128px;
          width: 100%;
          height: 74px;
          background: #f7f8fa;
          border-radius: 8px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .line {
          width: 8px;
          height: 74px;
          background: #ffffff;
        }
      }

      &-gress {
        position: relative;
        margin: 30px 0;
        display: flex;
        justify-content: center;

        ::v-deep {
          .el-progress-circle {
            width: 170px !important;
            height: 170px !important;
          }

          svg>path:nth-child(2) {
            stroke: url(#write2);
          }

          .el-progress__text {
            color: #000000;
            font-weight: bold;
            font-size: 24px !important;
          }
        }

        .text {
          position: absolute;
          bottom: 46px;
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #9999a1;
        }
      }
    }
  }

  &-down {
    display: flex;
    justify-content: space-between;

    &-l {
      // padding: 16px;
      margin-right: 16px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #2c2c35;
      min-width: 856px;

      .top {
        padding: 16px;
        // width: 856px;
        width: 100%;
        height: 194px;
        background: #ffffff;
        // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        div {
          display: flex;
          justify-content: flex-start;

          .headline {
            color: #767684;
            width: 150px;
          }

          .headline-l {
            flex: 1;
          }
        }
      }

      .down {
        padding: 16px;
        // width: 856px;
        width: 100%;
        height: 216px;
        background: #ffffff;
        // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .title {
          font-family: "Alibaba PuHuiTi";
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          color: #2c2c35;
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }

        div {
          display: flex;
          justify-content: flex-start;

          .headline {
            color: #767684;
            width: 150px;
          }

          .headline-l {
            flex: 1;
          }
        }
      }
    }

    &-r {
      padding: 16px;
      // width: 860px;
      width: 100%;
      height: 426px;
      background: #ffffff;
      // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 8px;

      &-tab {
        margin-top: 12px;
        
        ::v-deep {
          .el-tabs__header {
            margin: 0 !important;
          }

          .el-tabs--card>.el-tabs__header {
            margin-left: 10px;
            border: 0;
          }

          .el-tabs--card>.el-tabs__header .el-tabs__item {
            width: 95px;
            height: 26px;
            padding: 0;
            border: 0;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
          }

          .el-tabs--card>.el-tabs__header .el-tabs__item.is-active {
            width: 95px;
            height: 26px;
            padding: 0;
            background: #ffffff;
            border-radius: 4px;
            display: flex;
            justify-content: center;
            align-items: center;
            // margin: 0 10px !important;
          }
        }

        ::v-deep .el-tabs__nav {
          padding: 0 8px;
          // min-width: 592px;
          height: 34px !important;
          border: 0;
          background: #f2f3f7;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          transform: translateX(0px) !important;
        }

        .box {
          margin-top: 16px;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          background: #ffffff !important;

          div {
            display: flex;
            margin-bottom: 16px;

            .headline {
              width: 200px;
              font-family: "Alibaba PuHuiTi";
              font-style: normal;
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
              color: #767684;
            }

            .headline-l {
              font-family: "Alibaba PuHuiTi";
              font-style: normal;
              font-weight: 400;
              font-size: 14px;
              line-height: 22px;
              color: #2c2c35;
              flex: 1;
            }
          }
        }
      }
    }
  }
}
</style>
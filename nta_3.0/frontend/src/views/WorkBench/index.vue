<template>
  <div class="workbench">
    <mbps-data />
    <div class="tab-box">
      <aside class="temp"></aside>
      <el-tabs v-model="activeName" class="tabs" @tab-click="handleClick">
        <el-tab-pane label="任务态势" name="first">
          <task-state v-if="activeName === 'first'" ref="task" />
        </el-tab-pane>
        <el-tab-pane label="过滤规则" name="second">
          <filtrate-rule />
        </el-tab-pane>
        <el-tab-pane label="采集规则" name="third">
          <CollectRule />
        </el-tab-pane>
        <el-tab-pane label="威胁情报" name="threat">
          <ThreatIsr />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import TaskState from "./components/TaskState";
import MbpsData from "./components/MbpsData";
import FiltrateRule from "./components/FiltrateRule";
import CollectRule from "./components/CollectRule";
import ThreatIsr from "./components/ThreatISR";
import {getdiskmode } from '@/api/SystemData/systemdata';
import { getTasklist } from "@/api/Conversational/conversational";
export default {
  name: "Workbench",
  components: {
    TaskState,
    MbpsData,
    FiltrateRule,
    CollectRule,
    ThreatIsr
  },
  data () {
    return {
      activeName: "first",
    };
  },
  created () {
    this.CHECK_CACHE();
  },
  mounted () {
    // 获取当前硬盘模式
    this.Getdiskmode();
    this.A();
  },
  beforeDestroy () { },
  methods: {
    handleClick (tab, event) {
      // console.log(tab, event);
      // this.$refs.task.REMOVE_LINE()
    },
    // 获取硬盘操作当前模式
    Getdiskmode () {
      getdiskmode().then((res) => {
        this.$store.commit('ifonconduct/getdiskmode', res.data);
      });
    },
    // 检查缓存并初始化最新websocket数据
    CHECK_CACHE () {
      if (!localStorage.getItem("task_id")) {
        localStorage.setItem("task_id", 0);
      }
    },
    A () {
      this.$store.dispatch("WS_MESSAGE", "request-data");
    },
  },
};
</script>

<style lang="scss" scoped>
.workbench {
}

.tab-box {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  padding: 12px 16px;
  box-sizing: border-box;
}

.tab-box {
 

  .temp {
    width: 100%;
    height: 130px;
  }
}

.tabs {
  margin-top: 20px;
  ::v-deep .el-tabs__content {
    overflow: visible;
  }

  ::v-deep .el-tabs__nav-scroll {
    padding-left: 24px;
  }

  ::v-deep.el-tabs__item.is-active {
    color: #116EF9;
  }

  // ::v-deep.el-tabs__item::hover {
  //   color: #2c235;
  // }
  ::v-deep .el-tabs__active-bar {
    background-color: #116EF9;
    width: 40px !important;
    height: 2px;
  }

  .tabs-num {
    color: #9999a1;
  }

  ::v-deep .el-tabs__item {
    color: #2c2c35;
    font-weight: 600;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    height: 2px;
    background-color: #F2F3F7;
  }

  ::v-deep .el-tabs__nav {
    height: 54px;
    display: flex;
    align-items: center;
  }
}

.tab-box {
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  padding: 12px 16px;
  padding: 12px 16px;
  box-sizing: border-box;

  .temp {
    width: 100%;
    height: 130px;
  }

}

.tabs {

  ::v-deep .el-tabs__content {
    overflow: visible;
  }

  ::v-deep .el-tabs__nav-scroll {
    padding-left: 24px;
  }

  ::v-deep.el-tabs__item.is-active {
    color: #116ef9;
  }

  // ::v-deep.el-tabs__item::hover {
  //   color: #2c235;
  // }
  ::v-deep .el-tabs__active-bar {
    background-color: #116ef9;
    width: 40px !important;
    height: 2px;
  }

  .tabs-num {
    color: #9999a1;
  }

  ::v-deep .el-tabs__item {
    color: #2c2c35;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #f2f3f7;
  }

  ::v-deep .el-tabs__nav {
    height: 54px;
    display: flex;
    align-items: center;
  }
}
</style>
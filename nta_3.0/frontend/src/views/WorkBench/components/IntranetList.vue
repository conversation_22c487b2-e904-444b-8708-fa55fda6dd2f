<template>
  <div class="intranet-list">
    <div class="intranet-list-title">
      <div class="intranet-list-title-text">内网IP网段列表</div>
      <div>
        <el-button
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="mini"
          style="color: #fff"
          @click="ADD"
        >
          添加
        </el-button>
      </div>
    </div>
    <div class="intranet-list-content">
      <el-table
        ref="multipleTable"
        tooltip-effect="dark"
        :data="tableData"
        style="width: 100%"
        stripe
        border
      >
        <!-- <el-table-column type="selection" width="60"> </el-table-column> -->
        <el-table-column label="序号" width="60">
          <template scope="scoped">
            {{ TABLE_INDEX(scoped.$index) }}
          </template>
        </el-table-column>
        <el-table-column prop="inter_ip" label="IP">
          <template slot-scope="scope">
            <span style="color: #116ef9">{{ scope.row.inter_ip }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="ip_mask" label="掩码"> </el-table-column>
        <el-table-column prop="mac" label="mac地址"> </el-table-column>
        <el-table-column prop="created_time" label="添加时间">
          <template slot-scope="scope">
            <span style="color: #116ef9">{{ scope.row.created_time }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <el-button type="text" @click="EDIT(scope.row)">编辑</el-button>
            <el-button type="text" @click="DEL(scope.row.id)">删除</el-button>
            <!-- <svg-icon icon-class="icon_16_edit" class="handle-icon" @click="EDIT(scope.row)"></svg-icon>
            <svg-icon icon-class="icon_16_delete" class="handle-icon" @click="DEL(scope.row.id)"></svg-icon> -->
          </template>
        </el-table-column>
      </el-table>
      <div class="page">
        <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="PAGESIZE_CHANGE"
          @current-change="PAGE_CHANGE"
        >
        </el-pagination>
      </div>
    </div>
    <el-drawer
      title="添加"
      :visible.sync="addDrawer"
      direction="rtl"
      :wrapper-closable="false"
      :with-header="false"
      class="add-drawer"
      @close="
        () => {
          this.$refs['form'].resetFields();
        }
      "
    >
      <div class="title">
        <div>{{ drawerTitle }}</div>
        <i class="el-icon-close" style="cursor: pointer" @click="CANCEL()"></i>
      </div>
      <div class="body">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="form"
          size="mini"
        >
          <el-form-item label="IP地址" prop="inter_ip">
            <el-input v-model="form.inter_ip"></el-input>
          </el-form-item>
          <el-form-item label="掩码" prop="ip_mask">
            <el-input v-model="form.ip_mask"></el-input>
          </el-form-item>
          <el-form-item v-if="drawerTitle === '修改'" label="mac地址">
            <el-input v-model="form.mac" disabled></el-input>
          </el-form-item>
          <el-form-item v-if="drawerTitle === '修改'" label="添加时间">
            <el-input v-model="form.created_time" disabled></el-input>
          </el-form-item>
        </el-form>
        <div class="btn">
          <div class="cancel" @click="CANCEL()">取消</div>
          <div class="submit" @click="SUBMIT()">
            {{ drawerTitle === "添加" ? "添加" : "修改" }}
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  internal_list,
  internal_delete,
  internal_add,
  internal_update,
} from "@/api/Intranetlist";
import api from "@/api/offline";
import mixins from "@/views/offline/mixins";
export default {
  name: "IntranetList",
  mixins: [mixins],
  data() {
    // IP校验规则+IPv6
    let validateIP = (rule, value, callback) => {
      const ipReg =
        /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/;
      if (value === "") {
        callback();
      }
      const ipv6Reg =
        /^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/;
      if (!ipReg.test(value)) {
        if (!ipv6Reg.test(value)) {
          callback(new Error("IP格式有误"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    // 子网掩码校验规则
    let validateSubnet = (rule, value, callback) => {
      const subnetReg =
        /^((128|192)|2(24|4[08]|5[245]))(\.(0|(128|192)|2((24)|(4[08])|(5[245])))){3}$/;
      if (value === "") {
        callback();
      }
      if (!subnetReg.test(value)) {
        callback(new Error("子网掩码格式有误"));
      } else {
        callback();
      }
    };
    return {
      tableData: [],
      page: 1,
      pageSize: 10,
      pageTotal: 0,
      addDrawer: false,
      drawerTitle: "添加",
      // 表单
      form: {
        inter_ip: "",
        ip_mask: "",
      },
      // 表单校验
      rules: {
        inter_ip: [
          { required: true, message: "IP为必填项" },
          { validator: validateIP, trigger: "blur" },
        ],
        ip_mask: [
          { required: true, message: "掩码为必填项" },
          { validator: validateSubnet, trigger: "blur" },
        ],
      },
      task_id: 0,
      batch_id: 1000001,
    };
  },
  watch: {
    offlineId:{
      handler(val){
        if(val&&this.isOffLine){
          this.GET_LIST();
        }
      },
      immediate:true
    }
  },
  mounted() {
    !this.isOffLine && this.GET_TASKID();
  },
  methods: {
    // TASK_ID，区分主从任务
    GET_TASKID() {
      this.task_id = localStorage.getItem("task_id") || "0";
      if (this.task_id == 0) {
        this.batch_id = 1000001;
      }
      if (this.task_id == 1) {
        this.batch_id = 1000002;
      }
      this.GET_LIST();
    },
    // 列表
    GET_LIST() {
      if (this.isOffLine) {
        if (this.offlineId) {
          api
            .offlineInternalList({
              current_page: this.page,
              page_size: this.pageSize,
              sort_order: "desc",
              task_id: this.offlineId,
            })
            .then((res) => {
              if (res.err === 0) {
                this.tableData = res.data?.result;
                this.pageTotal = res.data.total;
              }
            });
        }
      } else {
        internal_list({
          current_page: this.page,
          page_size: this.pageSize,
          sort_order: "desc",
          task_id: this.task_id,
        }).then((res) => {
          if (res.err === 0) {
            this.tableData = res.data?.result;
            this.pageTotal = res.data.total;
          }
        });
      }
    },
    TABLE_INDEX(index) {
      return (this.page - 1) * this.pageSize + (index + 1);
    },
    PAGESIZE_CHANGE(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      this.GET_LIST();
    },
    PAGE_CHANGE(val) {
      console.log(`当前页: ${val}`);
      this.page = val;
      this.GET_LIST();
    },
    // 删除
    DEL(id) {
      this.$confirm("此操作将会删除本条信息，请确认?", "删除提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (this.isOffLine) {
            if (this.offlineId) {
              api
                .offlineInternalDelete({
                  ids: [id],
                  batch_id: this.batch_id,
                  task_id: this.task_id,
                }).then((res) => {
                  if (res.err === 0) {
                    this.$message({
                      type: "success",
                      message: res.msg,
                    });
                  }
                  this.GET_LIST();
                });
            }
          } else {
            internal_delete({
              ids: [id],
              batch_id: this.batch_id,
              task_id: this.task_id,
            }).then((res) => {
              if (res.err === 0) {
                this.$message({
                  type: "success",
                  message: res.msg,
                });
              }
              this.GET_LIST();
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 添加
    ADD() {
      this.form = {
        inter_ip: "",
        ip_mask: "*************",
      };
      this.drawerTitle = "添加";
      this.addDrawer = true;
      //   this.$refs['form'].resetFields()
    },
    // 修改
    EDIT(row) {
      this.form = {
        inter_ip: row.inter_ip,
        ip_mask: row.ip_mask,
        mac: row.mac,
        created_time: row.created_time,
        id: row.id,
      };
      this.drawerTitle = "修改";
      this.addDrawer = true;
    },
    // 取消
    CANCEL() {
      this.form = {
        inter_ip: "",
        ip_mask: "",
      };
      this.addDrawer = false;
    },
    // 确认
    SUBMIT() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          switch (this.drawerTitle) {
          case "添加":
            let date = new Date();
            this.form.created_time = date;
            if (this.isOffLine) {
              if (this.offlineId) {
                api
                  .offlineInternalAdd({
                    ...this.form,
                    task_id: this.offlineId,
                    batch_id: this.batch_id,
                  }).then((res) => {
                    if (res.err === 0) {
                      this.$message.success(res.msg || "添加成功");
                      this.CANCEL();
                      this.GET_LIST();
                    }
                  });
              }
            } else {
              internal_add({
                ...this.form,
                task_id: this.task_id,
                batch_id: this.batch_id,
              }).then((res) => {
                if (res.err === 0) {
                  this.$message.success(res.msg || "添加成功");
                  this.CANCEL();
                  this.GET_LIST();
                }
              });
            }
            break;
          case "修改":
            if (this.isOffLine) {
              if (this.offlineId) {
                api
                  .offlineInternalUpdate({
                    id: this.form.id,
                    inter_ip: this.form.inter_ip,
                    ip_mask: this.form.ip_mask,
                    mac: this.form.mac,
                    task_id: this.offlineId,
                    batch_id: this.batch_id,
                  }).then((res) => {
                    if (res.err === 0) {
                      this.$message.success(res.msg || "添加成功");
                      this.form = {
                        inter_ip: "",
                        ip_mask: "",
                      };
                      this.addDrawer = false;
                      this.GET_LIST();
                    }
                  });
              }
            } else {
              internal_update({
                id: this.form.id,
                inter_ip: this.form.inter_ip,
                ip_mask: this.form.ip_mask,
                mac: this.form.mac,
                task_id: this.task_id,
                batch_id: this.batch_id,
              }).then((res) => {
                if (res.err === 0) {
                  this.$message.success(res.msg || "添加成功");
                  this.form = {
                    inter_ip: "",
                    ip_mask: "",
                  };
                  this.addDrawer = false;
                  this.GET_LIST();
                }
              });
            }
           
            break;
          default:
            break;
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.intranet-list {
  width: 100%;
  //   height: 400px;
  margin-top: 22px;
  display: flex;
  flex-direction: column;

  &-title {
    width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    &-text {
      color: #2b2b2b;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .el-table {
    .handle-icon {
      cursor: pointer;
      margin: 0 10px;
      color: #4a97ff;
    }

    ::v-deep .el-table__header-wrapper {
      height: 44px;
    }

    ::v-deep .el-table__row {
      height: 56px;

      td {
        padding: 0;
      }
    }

    ::v-deep .el-table__row--striped td {
      background: #f7f8fa;
    }
  }

  .page {
    width: 100%;
    height: 35px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 16px 0;
  }
}

::v-deep .rel-node-peel {
  padding: 0 !important;
}

::v-deep.add-drawer {
  .el-drawer__body {
    // padding: 0 24px;
    // padding-top: 54px;

    .title {
      width: 100%;
      height: 54px;
      font-size: 14px;
      color: #000;
      border-bottom: 1px solid #f2f3f7;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0px 24px;
    }

    .body {
      width: 100%;
      padding: 0 24px;
      display: flex;
      flex-direction: column;
      margin-top: 24px;

      .form {
        color: #0f0f13;
        font-size: 12px;

        .el-form-item__label-wrap {
          margin-left: 0 !important;
        }

        .el-form-item__label {
          color: inherit;
          font-size: 12px;
          font-weight: 400;
          width: 70px !important;
        }

        .el-form-item__content {
          margin-left: 42px !important;
        }

        .el-select {
          width: 100%;
        }

        .el-form-item__content {
          display: flex;
        }
      }
    }

    .btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;

      > div {
        width: 78px;
        height: 32px;
        padding: 0;
        font-size: 14px;
        text-align: center;
        line-height: 32px;
        cursor: pointer;
      }

      .cancel {
        background: #ffffff;
        border: 1px solid #f2f3f7;
        border-radius: 4px;
        color: #000;
      }

      .submit {
        background: #116ef9;
        border-radius: 4px;
        color: #fff;
        margin-left: 16px;
      }
    }
  }
}
</style>
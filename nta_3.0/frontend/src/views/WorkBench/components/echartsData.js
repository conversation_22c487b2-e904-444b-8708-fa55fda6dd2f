// Echarts
import * as echarts from 'echarts';

export let mbps1_options = {
  title: {},
  tooltip: {
    show: false
  },
  grid: {
    left: '0',
    right: '0',
    bottom: '15',
    top: '0',
  },
  xAxis: {
    type: 'category',
    show: false
  },
  yAxis: {
    type: 'value',
    show: false
  },
  series: [{
    type: 'bar',
    stack: 'Total',
    itemStyle: {
      borderColor: 'transparent',
      color: 'transparent',

    },
    emphasis: {
      itemStyle: {
        borderColor: 'transparent',
        color: 'transparent',

      }
    },
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
  },
  {
    type: 'bar',
    stack: 'Total',
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    barCategoryGap: '60%',
    itemStyle: {
      normal: {
        // 边弧度
        barBorderRadius: 2,
        // 柱宽
        barWidth: 4,
        // 颜色
        color: '#4A97FF',
        // 柱间距
        barGap: 50,
        // shadowColor: 'rgba(17, 110, 249, 0.2)',
        // shadowBlur: 10,
        // shadowOffsetY: 10
      }
    }
  }
  ]
};
export let mbps2_options = {
  title: {},
  tooltip: {
    show: false
  },
  grid: {
    left: '0',
    right: '0',
    bottom: '15',
    top: '0',
  },
  xAxis: {
    type: 'category',
    show: false
  },
  yAxis: {
    type: 'value',
    show: false
  },
  series: [{
    type: 'bar',
    stack: 'Total',
    itemStyle: {
      borderColor: 'transparent',
      color: 'transparent',

    },
    emphasis: {
      itemStyle: {
        borderColor: 'transparent',
        color: 'transparent',

      }
    },
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
  },
  {
    type: 'bar',
    stack: 'Total',
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    barCategoryGap: '60%',
    itemStyle: {
      normal: {
        // 边弧度
        barBorderRadius: 2,
        // 柱宽
        barWidth: 4,
        // 颜色
        // color: '#4FDE4D',
        color: '#4A97FF',
        // 柱间距
        barGap: 50,
        // shadowColor: 'rgba(79, 222, 77, 0.2)',
        // shadowBlur: 10,
        // shadowOffsetY: 10
      }
    }
  }
  ]
};
export let mbps3_options = {
  title: {},
  tooltip: {
    show: false
  },
  grid: {
    left: '0',
    right: '0',
    bottom: '15',
    top: '0',
  },
  xAxis: {
    type: 'category',
    show: false
  },
  yAxis: {
    type: 'value',
    show: false
  },
  series: [{
    type: 'bar',
    stack: 'Total',
    itemStyle: {
      borderColor: 'transparent',
      color: 'transparent',

    },
    emphasis: {
      itemStyle: {
        borderColor: 'transparent',
        color: 'transparent',

      }
    },
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
  },
  {
    type: 'bar',
    stack: 'Total',
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    barCategoryGap: '60%',
    itemStyle: {
      normal: {
        // 边弧度
        barBorderRadius: 2,
        // 柱宽
        barWidth: 4,
        // 颜色
        // color: '#FFA048',
        color: '#4A97FF',
        // 柱间距
        // barGap: 50,
        // shadowColor: 'rgba(255, 160, 72, 0.2)',
        // shadowBlur: 10,
        // shadowOffsetY: 10
      }
    }
  }
  ]
};
export let mbps4_options = {
  title: {},
  tooltip: {
    show: false
  },
  grid: {
    left: '0',
    right: '0',
    bottom: '15',
    top: '0',
  },
  xAxis: {
    type: 'category',
    show: false
  },
  yAxis: {
    type: 'value',
    show: false
  },
  series: [{
    type: 'bar',
    stack: 'Total',
    itemStyle: {
      borderColor: 'transparent',
      color: 'transparent',

    },
    emphasis: {
      itemStyle: {
        borderColor: 'transparent',
        color: 'transparent',

      }
    },
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
  },
  {
    type: 'bar',
    stack: 'Total',
    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    barCategoryGap: '60%',
    itemStyle: {
      normal: {
        // 边弧度
        barBorderRadius: 2,
        // 柱宽
        barWidth: 4,
        // 颜色
        // color: '#AC85FF',
        color: '#4A97FF',
        // 柱间距
        barGap: 50,
        // shadowColor: 'rgba(172, 133, 255, 0.2)',
        // shadowBlur: 10,
        // shadowOffsetY: 10
      }
    }
  }
  ]
};
export let ip_options = {

  bacbackgroundColor: '#f7f8fa',
  series: [{
    type: 'sankey',
    layout: 'none',
    layoutIterations: 0,
    label: {
      show: true,
      color: '#2C2C35'
    },
    emphasis: {
      focus: 'adjacency'
    },
    nodeAlign: 'justify',
    // draggable:false,
    borderCap: 'butt',
    levels: [{
      depth: 0,
      itemStyle: {
        color: '#116EF9',
        shadowColor: 'rgba(108, 73, 172, 0.1)',
        shadowBlur: 15,
        shadowOffsetY: 10
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    }, {
      depth: 1,
      itemStyle: {
        color: '#8ABCFF'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },
    {
      depth: 2,
      itemStyle: {
        color: '#C699FF'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },
    {
      depth: 3,
      itemStyle: {
        color: '#7D4BE8'
      },
      lineStyle: {
        color: 'gradient',
        opacity: 0.4,
        curveness: 0
      }
    },

    ],
    itemStyle: {

    },
    lineStyle: {
      color: 'gradient',
      curveness: 0.5, //设置边的曲度
      opacity: 0.2 //设置边的透明度
    },
    top: '2%',
    right: '15%',
    bottom: '2%',
    left: '1%',
    // 节点宽度
    nodeWidth: 6,
    // 是否可拖动
    draggable: true,
    // 左对齐
    nodeAlign: 'left',
    data: [],
    links: []
  }],
  tooltip: {
    trigger: 'item'
  }
};
export let ip2_options = {
  layouts: [{
    // 布局类型
    layoutName: 'tree',
    // 方向
    from: "left"
  }],
  // 节点class

  // 连线控制
  defaultLineMarker: {
    markerWidth: "0",
    markerHeight: "0",
    refX: 0,
    refY: 0
  },
  // 默认的连线粗细
  defaultLineWidth: 20,
  // 默认线条样式
  defaultLineShape: 6,
  // 默认线条链接点
  defaultJunctionPoint: "border",
  // 节点的默认边框粗细
  defaultNodeBorderWidth: 0,
  // 节点的默认宽高
  defaultNodeWidth: "150",
  defaultNodeHeight: "75",
  // 节点默认边框
  defaultNodeBorderWidth: 0,
  // 节点默认文字颜色
  defaultNodeFontColor: '#FFFFFF',
  // 形状
  defaultNodeShape: 1,
  // 是否显示工具栏
  allowShowMiniToolBar: false,
  defaultJunctionPoint: 'border'
  // 这里可以参考"Graph 图谱"中的参数进行设置
};
let graph = {
  nodes: [
    // {
    //   "id": "sdfsdf5143",
    //   "name": "c8:e7:f0:6c:f3:33",
    //   "symbolSize": 19.12381,
    //   "value": 28.685715,
    //   "category": 0
    // },
    // {
    //   "id": "1",
    //   "name": "c8:e7:f0:6d:33:d8",
    //   "symbolSize": 2.6666666666666665,
    //   "value": 4,
    //   "category": 0
    // },
    // {
    //   "id": "2",
    //   "name": "3c:8a:b0:86:65:27",
    //   "symbolSize": 6.323809333333333,
    //   "value": 19.485714,
    //   "category": 1
    // },
    // {
    //   "id": "3",
    //   "name": "3c:8a:b0:32:59:f1",
    //   "symbolSize": 6.323809333333333,
    //   "value": 9.485714,
    //   "category": 1
    // },
    // {
    //   "id": "4",
    //   "name": "c8:e7:f0:6c:f7:e7",
    //   "symbolSize": 6.323809333333333,
    //   "value": 19.485714,
    //   "category": 0
    // },
    // {
    //   "id": "5",
    //   "name": "10:39:e9:b7:86:a7",
    //   "symbolSize": 6.323809333333333,
    //   "value": 9.485714,
    //   "category": 1
    // }
  ],
  links: [
    // {
    //   source: "1",
    //   target: "sdfsdf5143"
    // },
    // {
    //   source: "1",
    //   target: "5"
    // },
    // {
    //   source: "1",
    //   target: "3"
    // },
    // {
    //   source: "2",
    //   target: "3"
    // },
    // {
    //   source: "4",
    //   target: "5"
    // }

  ],
  // "categories": [{
  //     "name": "内网地址"
  //   },
  //   {
  //     "name": "外网地址"
  //   }
  // ]

};
export let mac_options = {

  tooltip: {
    formatter: (data) => {
      if (data.dataType === 'edge') {
        return `<div>
                    <b>起始点:</b>
                    <span>${data.data.source}</span>
                </div>
                <div>
                    <b>目标点:</b>
                    <span>${data.data.target}</span>
                </div>
                `;
      }
      if (data.dataType === 'node') {
        return `<div>
                    <b>Mac地址:</b>
                    <span>${data.data.name}</span>
                </div>
                <div>
                    <b>厂商地址:</b>
                    <span>${data.data.organization_name}</span>
                </div>
                <div>
                    <b>内/互联网:</b>
                    <span>${data.data.mac_inter}</span>
                </div>
                <div>
                    <b>关联网段:</b>
                    <span>${data.data.ip}</span>
                </div>
                <div>
                    <b>掩码:</b>
                    <span>${data.data.mask}</span>
                </div>
                `;
      }


    },
  },
  // legend: [{
  //   data: graph.categories.map(function (a) {
  //     return a.name;
  //   })
  // }],
  animationDurationUpdate: 1500,
  animationEasingUpdate: 'quinticInOut',

  series: [{
    // top: 0,
    // right: 0,
    // bottom: 0,
    // left: 0,
    zoom: 0.8,
    // name: 'Les Miserables',
    type: 'graph',
    layout: 'circular',
    circular: {
      rotateLabel: true
    },
    data: graph.nodes,
    links: graph.links,
    // categories: graph.categories,
    // 是否可缩放：缩放:'scale',拖动:'move',全开：true
    roam: false,
    label: {
      show: true,
      position: 'right',
      formatter: '{b}',
      distance: 30,
      color: '#000',
      fontSize: 12,
      rotate: 0
    },
    itemStyle: {

    },
    // 连接线的样式
    lineStyle: {
      width: 1,

      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [{
          offset: 0,
          color: 'blue' // 0% 处的颜色
        },
        {
          offset: 1,
          color: 'red' // 100% 处的颜色
        }
        ],
      },
      // 节点之间连线的弧度
      curveness: 0.3,
      // 阴影
      shadowColor: 'rgba(54, 36, 226, 0.7)',
      shadowBlur: 40,
      shadowOffsetX: 0,
      shadowOffsetY: 10
    }
  }]

};
export let bag_options = {
  width: '100%',
  height: '100%',
  circular: 'circular',
  bacbackgroundColor: 'transparent',
  tooltip: {
    // position: ['50%', '50%']
    show: false
  },

  series: [{
    type: 'pie',
    radius: ['75%', '100%'],
    avoidLabelOverlap: false,
    // 逆时针
    clockWise: false,
    color: ['#ffffff', '#ffa048'],
    label: {
      show: false,
      position: 'center'
    },
    itemStyle: {
      borderRadius: 10,
      borderColor: '#fff',
      borderWidth: 6,
      // shadowColor: 'rgba(87,235,146,0.4)',
      // shadowBlur: 10,
      // shadowOffsetY: 10
    },
    emphasis: {
      disabled: true
    },
    data: [{
      value: 0,
      name: '丢包'
    }, {
      value: 0,
      name: '未丢包'
    }]
  }]
};
export let taskline_options = {
  // 数据点hover之后展示
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: { // 坐标轴指示器，坐标轴触发有效
      type: 'line', // 默认为直线，可选为：'line' | 'shadow'   
      label: {
        // show: true
      }
    },
    showContent: true,
    alwaysShowContent: false,
    enterable: true,
    confine: true,
    position: 'top',
    formatter: (data) => {
      let size = data[0].data;
      let bytes = parseInt(size);
      if (bytes === 0) return '0 B';
      let k = 1000, // or 1024
        sizes = ['bps', 'Kbps', 'Mbps', 'Gbps'],
        i = Math.floor(Math.log(bytes) / Math.log(k));
      let fmtSize = (bytes / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
      let div = `<div style="color:#116EF9;">进：${fmtSize}</div>`;
      return div;
    },

    padding: 3,
    backgroundColor: '#FFFFFF',
    textStyle: {
      color: '#000000',
      fontWeight: 'bold',
      fontSize: 12
    },
  },
  // 工具栏
  toolbox: {
    show: true
  },
  // 坐标系
  grid: {
    width: '100%',
    height: '100%',
    left: 0,
    right: '10%',
    top: 0,
    bottom: 0,
    show: false,
    containLabel: true,
    borderColor: '#ccc'
  },
  // 图例组件
  legend: {
    show: false
  },
  xAxis: [{
    show: true,
    type: 'category',
    data: [],
    axisLine: {
      show: true
    },
    axisLabel: {
      borderColor: '#CCC',
    },
    axisTick: {
      lineStyle: {
        //  color:'transparent',
        width: 0
      },

    },
    nameLocation: 'middle',
    silent: true,
    splitLine: {
      show: false
    },
    boundaryGap: true
  }],
  yAxis: [{
    type: 'value',
    name: '',
    show: true,
    axisLabel: {
      show: false
    }
  }],
  series: [

    // 线
    {
      name: '',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 12, //拐点大小
      showSymbol: false,
      legendHoverLink: false,
      cursor: 'pointer',
      itemStyle: {
        normal: {
          color: '#116EF9', //拐点颜色        
          borderColor: '#FFFFFF',
          borderWidth: 2,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 3
        },

      },
      lineStyle: {
        normal: {
          color: '#116EF9', //折线颜色
          type: 'solid',
          width: 2
        }
      },
      label: {
        show: false
      },
      areaStyle: {
        opacity: 0.8,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(17, 110, 249, 0.1)'
        },
        {
          offset: 1,
          color: 'rgba(17, 110, 249, 0)'
        }
        ])
      },
      emphasis: {
        scale: true,
        focus: 'none'
      },
      data: [480, 750, 610, 588, 620, 650, 760, 480, 550, 480, 550, 590, 700, 810, 850, 840, 480, 550]
    }
  ]
};
export let taskline_options2 = {
  // 数据点hover之后展示
  tooltip: {
    show: true,
    trigger: 'axis',
    axisPointer: { // 坐标轴指示器，坐标轴触发有效
      type: 'line', // 默认为直线，可选为：'line' | 'shadow'   
      label: {
        // show: true
      }
    },
    showContent: true,
    alwaysShowContent: false,
    enterable: true,
    confine: true,
    position: 'top',
    formatter: (data) => {
      function fmtSize(size) {
        let bytes = parseInt(size);
        if (bytes === 0) return '0 B';
        let k = 1000, // or 1024
          sizes = ['bps', 'Kbps', 'Mbps', 'Gbps'],
          i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat(bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i];

      }
      let div = `<div style="color:#116EF9;">进：${(fmtSize(data[0].data))}</div><div style="color:#07D4AB;">出：${fmtSize(data[1].data)}</div>`;
      return div;
    },
    padding: 3,
    backgroundColor: '#FFFFFF',
    textStyle: {
      color: '#000000',
      fontWeight: 'bold',
      fontSize: 12
    },
    // position: [10, 10]
  },
  // 工具栏
  toolbox: {
    show: true
  },
  // 坐标系
  grid: {
    width: '100%',
    height: '100%',
    left: 0,
    right: '10%',
    top: 0,
    bottom: 0,
    show: false,
    containLabel: true,
    borderColor: '#ccc'
  },
  // 图例组件
  legend: {
    show: false
  },
  xAxis: [{
    show: true,
    type: 'category',
    data: [],
    axisLine: {
      show: true
    },
    axisLabel: {
      borderColor: '#CCC',
    },
    axisTick: {
      lineStyle: {
        //  color:'transparent',
        width: 0
      },

    },
    nameLocation: 'middle',
    silent: true,
    splitLine: {
      show: false
    },
    boundaryGap: true
  }],
  yAxis: [{
    type: 'value',
    name: '',
    show: true,
    axisLabel: {
      show: false
    }
  }],
  series: [

    // 线
    {
      name: '',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 12, //拐点大小
      showSymbol: false,
      legendHoverLink: false,
      cursor: 'pointer',
      itemStyle: {
        normal: {
          color: '#116EF9', //拐点颜色        
          borderColor: '#FFFFFF',
          borderWidth: 2,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 3
        },

      },
      lineStyle: {
        normal: {
          color: '#116EF9', //折线颜色
          type: 'solid',
          width: 2
        }
      },
      areaStyle: {
        opacity: 0.8,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(17, 110, 249, 0.1)'
        },
        {
          offset: 1,
          color: 'rgba(17, 110, 249, 0)'
        }
        ])
      },
      label: {
        show: false
      },
      emphasis: {
        scale: true,
        focus: 'none'
      },
      data: []
    },
    {
      name: '',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 12, //拐点大小
      showSymbol: false,
      legendHoverLink: false,
      cursor: 'pointer',
      itemStyle: {
        normal: {
          color: '#07D4AB', //拐点颜色        
          borderColor: '#FFFFFF',
          borderWidth: 2,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 10,
          shadowOffsetY: 3
        },

      },
      lineStyle: {
        normal: {
          color: '#07D4AB', //折线颜色
          type: 'solid',
          width: 2
        }
      },
      areaStyle: {
        opacity: 0.8,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(17, 249, 179, 0.1)'
        },
        {
          offset: 1,
          color: 'rgba(17, 249, 193, 0)'
        }
        ])
      },
      label: {
        show: false
      },
      emphasis: {
        scale: true,
        focus: 'none'
      },
      data: []
    }
  ]
};

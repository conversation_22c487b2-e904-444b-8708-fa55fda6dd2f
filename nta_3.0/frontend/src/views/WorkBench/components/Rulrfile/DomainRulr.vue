/* 域名规则 */
<template>
  <div class="domainRulr p-relative">
    <el-form ref="domainRule" :rules="rules" :model="formData">
      <div class="domainRulr-top">
        <div class="domainRulr-top-r">
          <div class="title">域名规则</div>
          <el-form-item prop="domain" :rules="rules.ruleDomain">
            <el-input v-model="formData.domain" placeholder="例如精确域名：wiki.centos.org 或 泛域名：qq.com（以*.开始）"></el-input>
          </el-form-item>
        </div>
        <!-- <div class="domainRulr-top-l">
          <el-form-item prop="ruleDomains"  >
            <el-radio-group v-model="formData.type" size="medium">
              <el-radio :label="1">精确域名</el-radio>
              <el-radio :label="2">n级域名</el-radio>
            </el-radio-group>
          </el-form-item>
        </div> -->
      </div>
    </el-form>
    <div class="domainRulr-down">
      <el-table ref="domainRuleList" :data="domainRuleList" style="width: 100%">
        <el-table-column type="index" label="序号" width="100" fixed>
        </el-table-column>
        <el-table-column prop="domain" label="域名"> </el-table-column>
        <el-table-column prop="type" label="域名类型">
          <template slot-scope="scope">
            <span>{{ scope.row.type === 1 ? "精确域名" : "n级域名" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <div class="btn">
              <el-button type="text" size="mini" @click="deleteRuleInfo(scope.row)">删除</el-button>
              <el-button size="mini" type="text" @click="editRuleInfo(scope.row)">修改</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <tablescroll :table-ref="$refs.domainRuleList"></tablescroll>
    </div>
    <!-- 添加\保存 按钮 -->
    <div class="tab-box-btn">
      <div v-if="showsave" class="tab-box-btn-l" @click="addToList('domainRule')">
        <el-button>
          <svg-icon icon-class="frule-add" />
        </el-button>
      </div>
      <div v-if="!showsave" class="tab-box-btn-r" @click="editDomainRule">
        <el-button>
          <svg-icon icon-class="saveRule" />
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import tablescroll from "../../../../components/TableScroll/idnex.vue";
import { validateDomain } from "@/utils";
export default {
  name: "DomainRulr",
  components: {
    tablescroll,
  },
  props: ["domainRuleData"],
  data() {
    return {
      showsave: true,
      formData: {},
      domainRuleList: [],
      domainRuleParam: [],
      showAddForm: this.showForm,
      startPort: 0,
      endPort: 65535,
      rules: {
        ruleDomain: [
          { required: true, message: "不能为空", trigger: ["blur"] },
          {
            required: true,
            message: "请输入符合规则的域名",
            trigger: ["change"],
            validator: validateDomain,
          },
        ],
      },
    };
  },
  watch: {
    domainRuleData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.domainRule.clearValidate();
        });
        this.changeShowFormat();
      },
      deep: true,
    },
  },
  mounted() {
    this.initFormData();
    this.domainRuleList = [];
    this.domainRuleParam = [];
    // this.showAddForm = true
  },
  methods: {
    initFormData() {
      this.formData = {
        domain: "",
        type: 1,
      };
    },
    // 展示数据转换为规则传参数据
    showDataToRuleParams() {
      this.domainRuleParam = [];
      this.domainRuleList.forEach((item) => {
        let tempdomainRule = {
          domain: item.domain,
          type: item.type,
        };

        this.domainRuleParam.push(tempdomainRule);
      });
      this.$emit("getDomainRuleParam", this.domainRuleParam);
    },
    // 将后端返回数据规范为前端可展示绑定数据
    changeShowFormat() {
      this.initFormData();
      this.domainRuleList = [];
      this.domainRuleParam = [];
      // this.showAddForm = true
      if (this.domainRuleData.length !== 0) {
        for (let domainRuleItem of this.domainRuleData) {
          let tempItem = {
            index: Math.floor(Math.random() * 10000),
            domain: domainRuleItem.domain,
            type: domainRuleItem.type,
          };
          this.domainRuleList.push(tempItem);
        }
      }
    },
    // 将表单数据转换为表格可用数据
    formDataToTableData() {
      console.log(this.formData.domain);
      let text = this.formData.domain.slice(0, 2);
      if (text === "*.") {
        let domainRuleItem = {
          index: Math.floor(Math.random() * 10000),
          domain: this.formData.domain,
          type: 2,
        };
        console.log(domainRuleItem);
        return domainRuleItem;
      } else {
        let domainRuleItem = {
          index: Math.floor(Math.random() * 10000),
          domain: this.formData.domain,
          type: 1,
        };
        console.log(domainRuleItem);
        return domainRuleItem;
      }
    },
    addToList() {
      this.$refs["domainRule"].validate((valid) => {
        if (valid) {
          this.domainRuleList.unshift(this.formDataToTableData());
          this.showDataToRuleParams();
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 修改域名规则
    editDomainRule() {
      this.$refs["domainRule"].validate((valid) => {
        if (valid) {
          this.domainRuleList = this.domainRuleList.map((item, index) => {
            if (item.index === this.formData.index) {
              item = this.formDataToTableData();
            }
            return item;
          });
          this.showDataToRuleParams();
          this.initFormData();
          this.showAddForm = false;
          this.$message.success("保存成功");
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 将要修改的域名规则反写到表单里
    editRuleInfo(row) {
      this.formData = JSON.parse(JSON.stringify(row));
      this.showAddForm = true;
      var scrollTop = document.querySelector("#addRules");
      if (scrollTop.scrollTop > 390) {
        scrollTop.scrollTop = 390;
      }
      this.showsave = false;
    },
    deleteRuleInfo(row) {
      this.domainRuleList.forEach((item, index) => {
        if (item.index === row.index) {
          this.domainRuleList.splice(index, 1);
        }
      });
      this.showDataToRuleParams();
    },
  },
};
</script>

<style lang="scss" scoped>
.p-relative {
  position: relative;
  .tab-box-btn {
    position: absolute;
    top: 0px;
    right: 20px;

    .el-button {
      border: 1px solid #cecece;
      width: 35px;
      height: 27px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.domainRulr {
  &-top {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;

    ::v-deep .el-input .el-input__inner {
      height: 27px;
      width: 425px;
      font-weight: 400;
      font-size: 12px;
      padding-left: 5px;
    }

    ::v-deep .el-input__suffix {
      height: 54px;
      top: -14px;
    }

    .title {
      display: inline-block;
    }

    &-r {
      display: flex;
      flex-direction: column;
      margin-right: 5px;
    }

    &-l {
      margin-left: 5px;

      ::v-deep .el-radio {
        margin-right: 5px;
      }
    }
  }

  &-down {
    margin-top: 24px;

    .btn {
      ::v-deep .el-button {
        border: none;
      }
    }
  }
}
</style>
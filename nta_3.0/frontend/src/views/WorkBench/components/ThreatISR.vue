<template>
  <div class="filtrate-rule">
    <div class="handle-box">
      <div class="handle-box-left">
        已选择<span style="color: #116ef9">{{
          tableDataSelsections.length
        }}</span>条
      </div>
      <div class="handle-box-right">
        <el-popover
          placement="bottom"
          trigger="click"
          :visible-arrow="false"
          popper-class="alldownbox"
          :disabled="downloadDisable"
        >
          <div
            slot="reference"
            :class="downloadDisable ? 'alldown' : 'qlactive'"
          >
            <div class="alldown-l">删除</div>
            <div class="alldown-r">
              <svg-icon icon-class="del-down2" />
            </div>
          </div>
          <div class="alldownfoot">
            <el-button
              class="alldownfoot-t"
              @click="REMOVES"
            >
              删除选中项
            </el-button>
            <el-button
              class="alldownfoot-d"
              @click="ALL_REMOVE"
            >
              删除全部项
            </el-button>
          </div>
        </el-popover>
        <div class="line"></div>

        <el-button @click="UPLOAD_CLICK"> 导入 </el-button>
        <el-button @click="DOWNLOAD_CLICK"> 导出 </el-button>

        <el-button
          class="all-out"
          @click="DOWNLOAD_ALL"
        >
          全部导出
        </el-button>
      </div>
    </div>
    <div class="table-box">
      <el-table
        border
        :data="tableData"
        style="width: 100%"
        :default-sort="{ prop: 'date', order: 'descending' }"
        stripe
        @selection-change="SELECTTION_CHANGE"
        @sort-change="changeTableSort"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        >
        </el-table-column>
        <el-table-column
          type="index"
          label="序号"
          width="100"
          sortable
          align="center"
        >
          <template scope="scoped">
            {{ TABLE_INDEX(scoped.$index) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="ip"
          label="情报字段"
        >
          <template slot-scope="scoped">
            {{ scoped.row.target }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="类型"
          width="95"
        >
          <template slot-scope="scoped">
            {{ scoped.row.targetType }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="来源"
        >
          <template slot-scope="scoped">
            {{ scoped.row.source }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="情报标签"
        >
          <template slot-scope="scoped">
            {{ scoped.row.tagName }}
          </template>
        </el-table-column>
        <el-table-column
          sortable=""
          prop="time"
          label="生效时间"
          :formatter="UPDATED_TIME_FMT"
        >
        </el-table-column>
      </el-table>
      <div class="page-box">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          @size-change="PAGESIZE_CHANGE"
          @current-change="PAGE_CHANGE"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :visible.sync="uploadDiaglog"
      :before-close="UPLOADDIALOG_CLOSE"
      class="upload-diglog"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div slot="title">
        <div>上传威胁情报</div>
        <div @click="DEMO_DOWNLOAD">威胁情报模板.CSV</div>
        <div>
          <!-- <i class="el-icon-close"></i> -->
        </div>
      </div>
      <el-upload
        v-show="fileList.length === 0"
        ref="upload"
        class="upload-demo"
        drag
        :headers="uploadHeaders"
        :action="uploadUrl"
        :data="uploadData"
        multiple
        :show-file-list="false"
        :on-change="UPLOAD_CHANGE"
        :before-upload="BEFORE_UPLOAD"
        :on-success="UPLOAD_SUCCESS"
        :on-error="UPLOAD_ERROR"
      >
        <svg-icon
          icon-class="upload-file"
          class="upload-file-icon"
        ></svg-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="text-maxsize">文件不能大于25M</div>
      </el-upload>
      <div
        v-show="fileList.length > 0"
        class="upload-timing"
      >
        <div class="uploading-box">
          <div>
            <span>{{ fileName }}</span>
            <span>{{ uploadStatusText }}</span>
          </div>
          <div>
            <el-progress
              :percentage="uploadPlan"
              color="#39D979"
              :show-text="false"
            ></el-progress>
          </div>
          <svg-icon
            v-if="uploadPlan === 100"
            class="success-icon"
            icon-class="upload-success"
            color="#a4efa4"
          ></svg-icon>
        </div>
        <!-- <div v-if="uploadPlan === 100">
          上传规则总数：<span style="color: #116ef9">{{ uploadTotal }}</span
          >个; 导入成功规则：<span style="color: #39d979">{{
            uploadSucNum
          }}</span
          >个; 导入失败规则：<span style="color: red">{{ uploadFailNum }}</span
          >个;
        </div> -->
      </div>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          class="cancel"
          @click="UPLOAD_CANCEL"
        >取 消</el-button>
        <el-button
          type="primary"
          :class="uploadPlan > 0 && uploadPlan < 100 ? 'submit-d' : 'submit'"
          @click="UPLOAD_SUBMIT"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import dayjs from "dayjs";
import FileSaver from "file-saver";
import axios from "axios";
import { ThreatISR_list, ThreatISR_delete } from "@/api/ThreatISR.js";
export default {
  name: "FiltrateRule",
  data () {
    return {
      uploadHeaders: { token: getToken() },
      task_id: "",
      batch_id: "",
      tableData: [],
      // ip协议列表
      ipProtocol: [],
      ipProtocolOptions: [],
      // 准备修改的条目id
      putId: "",
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      pageTotal: 0,
      // 上传相关
      baseURL: "",
      uploadDiaglog: false,
      uploadUrl: "",
      fileList: [],
      fileName: "",
      uploadData: {},
      // 上传进度条相关
      uploadPlan: 10,
      uploadStatusText: "上传中！",
      uploadSucNum: 0,
      uploadFailNum: 0,
      uploadTotal: 0,
      downloadDisable: false,
      tableDataSelsections: [],
      asc: true,
    };
  },
  created () {
    // this.TASK_ID();
    this.BASE_URL();
    this.GET_LIST();
  },
  mounted () { },
  methods: {
    TASK_ID () {
      this.task_id = JSON.parse(localStorage.getItem("task_id"));
      if (this.task_id === 0) {
        this.batch_id = 1000001;
      }
      if (this.task_id === 1) {
        this.batch_id = 1000002;
      }
      this.uploadData.task_id = this.task_id;
      this.uploadData.batch_id = this.batch_id;
    },
    BASE_URL () {
      let temp;
      if (process.env.VUE_APP_BASE_API === "") {
        temp = `${process.env.VUE_APP_BASE_API}/api`;
      } else {
        temp = process.env.VUE_APP_BASE_API;
      }
      this.baseURL = temp;
      this.uploadUrl = `${this.baseURL}/information/upload`;
    },
    // 获取列表数据，基础事件
    GET_LIST () {
      ThreatISR_list({
        // task_id: this.task_id,
        // batch_id: this.batch_id,
        page_size: this.pageSize,
        current_page: this.currentPage,
        // order_field: "id",
        sort_order: "desc",
        asc: this.asc,
      }).then((res) => {
        if (res.err === 0) {
          this.tableData = res.data.records;
          this.pageTotal = res.data.total;
        }
      });
    },
    // 页码变化
    PAGE_CHANGE (currentPage) {
      this.currentPage = currentPage;
      this.GET_LIST();
    },
    // 每一页条数变化
    PAGESIZE_CHANGE (pagesize) {
      this.pageSize = pagesize;
      this.GET_LIST();
    },
    TABLE_INDEX (index) {
      return ((this.currentPage - 1) * this.pageSize) + (index + 1);
    },
    // updated_time，修改时间格式化
    UPDATED_TIME_FMT (row) {
      // return dayjs(row.validFrom * 1000).format("YYYY-MM-DD HH:mm:ss");
      return dayjs(row.validFrom).format("YYYY-MM-DD HH:mm:ss");
    },
    // 点击删除某一行
    REMOVE_ROW (id) {
      filter_remove({
        ids: [id],
        task_id: this.task_id,
        batch_id: this.batch_id,
      }).then((res) => {
        this.$message.success(res.msg);
        this.GET_LIST();
      });
    },
    // 删除选中的多行
    REMOVES () {
      if (this.tableDataSelsections.length > 0) {
        this.$Notice({
          title: "删除提示",
          type: "warn",
          message: "确认删除选中的条目？",
        })
          .then(() => {
            ThreatISR_delete({
              information_list: this.tableDataSelsections,
            }).then((res) => {
              this.$message.success("删除成功");
              this.GET_LIST();
            });
          })
          .catch(() => {
            // on cancel
          });
      } else {
        this.$message.warning("未选择条目!");
      }
    },
    // 全部删除
    ALL_REMOVE () {
      this.$Notice({
        title: "全部删除提示",
        type: "error",
        message: "确认删除所有的条目？",
      })
        .then(() => {
          ThreatISR_delete({
            whole: true,
          }).then((res) => {
            this.$message.success("删除成功");
            this.GET_LIST();
          });
        })
        .catch(() => {
          // on cancel
        });
    },
    // 多选的条目发生变化
    SELECTTION_CHANGE (val) {
      let arr = [];
      for (let i = 0; i < val.length; i++) {
        arr.push(val[i].id);
      }
      console.log(arr);
      this.tableDataSelsections = arr;
    },
    changeTableSort (data) {
      this.asc = data.column.order == "ascending" ? true : false;
      this.GET_LIST();
    },
    // 点击上传按钮
    UPLOAD_CLICK () {
      this.uploadPlan = 0;
      this.fileList = [];
      this.uploadStatusText = "上传中！";
      this.uploadDiaglog = true;
    },
    // 上传文件状态改变时的钩子
    UPLOAD_CHANGE (file, fileList) {
      console.log(file, fileList);
      this.fileName = file.name;
      if (fileList.length > 0) {
        this.fileList = fileList;
      }
    },
    // 上传文件前的回调
    BEFORE_UPLOAD () {
      this.uploadStatusText = "上传中！";
      this.uploadPlan = 30;
    },
    // 上传文件成功时的回调
    UPLOAD_SUCCESS (res, file, fileList) {
      if (res.err === 0) {
        this.uploadPlan = 100;
        this.uploadStatusText = "上传完成！";
        this.uploadSucNum = res.data.sucNum;
        this.uploadFailNum = res.data.failNum;
        this.uploadTotal = res.data.totalNum;
        this.$message.success("上传成功！");
        this.GET_LIST();
      } else {
        // this.uploadDiaglog = false;
        this.$refs.upload.abort();
        this.$refs.upload.clearFiles();
        this.fileList = [];
        this.uploadPlan = 0;
        this.$message.error(res.msg);
      }
    },
    // 上传失败时的回调
    UPLOAD_ERROR () {
      this.$message.error("上传失败！");
    },
    // 上传弹窗点击确定
    UPLOAD_SUBMIT (res) {
      console.log(res);
      this.uploadDiaglog = false;
    },
    // 上传弹窗点击取消
    UPLOAD_CANCEL () {
      if (this.uploadPlan > 0 && this.uploadPlan < 100) {
        this.$refs.upload.abort();
        this.$refs.upload.clearFiles();
        this.fileList = [];
        this.uploadPlan = 0;
        this.uploadStatusText = "上传中！";
        this.$message.warning("已取消上传！");
      } else {
        this.uploadDiaglog = false;
      }
    },
    // 上传弹窗关闭时
    UPLOADDIALOG_CLOSE (done) {
      this.$refs.upload.abort();
      this.uploadPlan = 0;
      this.fileList = [];
      this.uploadStatusText = "上传中！";
      done();
    },
    // 点击导出
    DOWNLOAD_CLICK () {
      if (this.tableDataSelsections.length > 0) {
        axios({
          method: "POST",
          url: this.baseURL + "/information/export", // 后端下载接口地址
          responseType: "blob", // 设置接受的流格式
          headers: {
            token: `${getToken()}`,
          },
          data: {
            information_list: this.tableDataSelsections,
          },
        }).then((res) => {
          if (res.err === 40005) {
            this.$message.error(res.msg);
          } else {
            const blob = new Blob([res.data], { type: "application/json" });
            FileSaver.saveAs(blob, res.headers["content-disposition"]);
            this.$message.success("导出成功");
          }
        });
      } else {
        this.$message.warning("未选择条目!");
      }
    },
    // 全部导出
    DOWNLOAD_ALL () {
      axios({
        method: "POST",
        url: this.baseURL + "/information/export", // 后端下载接口地址
        responseType: "blob", // 设置接受的流格式
        headers: {
          token: `${getToken()}`,
        },
        data: {
          whole: true,
        },
      }).then((res) => {
        if (res.err === 40005) {
          this.$message.error(res.msg);
        } else {
          const blob = new Blob([res.data], { type: "application/json" });
          FileSaver.saveAs(blob, res.headers["content-disposition"]);
          this.$message.success("导出成功");
        }
      });
    },
    // 模板csv下载
    DEMO_DOWNLOAD () {
      axios({
        method: "POST",
        url: this.baseURL + "/information/template", // 后端下载接口地址
        responseType: "blob", // 设置接受的流格式
        headers: {
          token: `${getToken()}`,
        },
      }).then((res) => {
        if (res.err === 40005) {
          this.$message.error(res.msg);
        } else {
          const blob = new Blob([res.data], { type: "application/json" });
          FileSaver.saveAs(blob, res.headers["content-disposition"]);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.filtrate-rule {
  width: 100%;
  position: relative;
  .handle-box {
    width: 100%;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    &-left {
      font-size: 12px;
      > span {
        color: #0f0f13;
      }
      .el-radio {
        margin-right: 12px;
      }
      ::v-deep .el-radio__inner {
        border: 2px solid #767684;
        width: 12px;
        height: 12px;
      }
      ::v-deep .el-radio__input.is-checked .el-radio__inner {
        background: #ffffff;
        border: 2px solid #116ef9;
      }
      ::v-deep .el-radio__label {
        color: #0f0f13;
        font-size: 12px;
      }
      ::v-deep .el-radio__inner::after {
        width: 6px;
        height: 6px;
        background-color: #116ef9;
      }
    }
    &-right {
      display: flex;
      align-items: center;
      .el-button {
        width: 60px;
        height: 32px;
        padding: 0 10px;
        // color: #0f0f13;
        // background: #ffffff;
        // border: 1px solid #f2f3f7;
        box-sizing: border-box;
      }
      .all-out {
        width: 88px;
        height: 32px;
        background: #07d4ab;
        border-radius: 4px;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #ffffff;
        border-color: #07d4ab;
      }

      .alldown {
        cursor: not-allowed;
        position: relative;
        // width: 119px;
        height: 32px;
        border: 1px solid #8abcff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #4a97ff;
        // margin-right: 8px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 60px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #8abcff;
        }

        &-r {
          width: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }
      .qlactive {
        cursor: pointer;
        position: relative;
        // width: 119px;
        height: 32px;
        border: 1px solid #8abcff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #4a97ff;
        // margin-right: 8px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 60px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #8abcff;
        }

        &-r {
          width: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }
      .qlactive:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }
      .line::after {
        margin: 0 8px;
        content: '';
        display: block;
        width: 1px;
        height: 16px;
        background: #dee0e7;
      }
    }
  }
  .page-box {
    // width: 100%;
    // display: flex;
    // justify-content: center;
    // align-items: center;
    // margin-top: 10px;
    // margin-bottom: 20;
    z-index: 999;
    padding: 10px 24px;
    position: sticky;
    right: 34px;
    bottom: 0px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    // margin-bottom: 20;
    background: #ffffff;
  }

  ::v-deep .el-drawer {
    border-radius: 10px 0 0 10px;
    .el-drawer__header {
      font-size: 12px;
      color: #000;
      font-weight: 600;
      margin-bottom: 28px;
      padding-top: 12px;
      padding-left: 16px;
    }
    .el-drawer__body {
      padding-left: 16px;
      padding-right: 16px;
      box-sizing: border-box;
      .right-form {
        color: #0f0f13;
        font-size: 12px;
        .el-form-item__label {
          color: inherit;
          font-size: 12px;
          font-weight: 400;
        }
        .el-radio {
          color: inherit;
        }
        .el-select {
          width: 100%;
        }
        .el-form-item__content {
          display: flex;
        }
        .el-checkbox {
          margin-left: 10px;
        }
      }
      .ip-form {
        .el-form-item__label-wrap {
          margin-left: 0 !important;
        }
        .el-form-item__label {
          width: auto !important;
          font-weight: 600;
        }
        .el-form-item__content {
          margin-left: 42px !important;
        }
      }
      .form-type {
        .el-form-item__content {
          width: 100%;
          margin-left: 0 !important;
          .el-radio-group {
            width: 100%;
            display: flex;
            justify-content: space-evenly;
          }
        }
      }
      .protocol-list {
        width: 100%;
        height: 504px;
        padding-bottom: 60px;
        overflow-y: auto;
        .el-table__header-wrapper {
          display: none;
        }
        .el-table__row > td {
          padding: 10px 0;
          font-size: 12px;
        }
        .select-row {
          td {
            color: #116ef9 !important;
            font-weight: 600;
          }
        }
      }
      .form-ipport {
        .el-select__tags {
          max-width: 100%;
          flex-wrap: nowrap;
          overflow-x: auto;
        }
        // .el-select .el-input__inner {
        //   padding-right: inherit;
        // }
        // .el-input__suffix {
        //   display: none;
        // }
      }
      .form-btn {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-left: 0 !important;
        margin-bottom: 0;
        position: sticky;
        bottom: 0;
        background-color: #ffffff;
        .submit {
          width: 78px;
          height: 32px;
          background-color: #116ef9;
          color: #fff;
          font-size: 14px;
        }
        .cancel {
          width: 78px;
          height: 32px;
          background-color: #fff;
          color: #0f0f13;
          font-size: 14px;
        }
      }
    }
  }

  ::v-deep .el-dialog__wrapper.upload-diglog {
    .el-dialog {
      width: 613px;
      height: 313px;
      box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .el-dialog__header {
        width: 100%;
        height: 20px;
        padding: 12px 16px 0 16px;
        > div {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          > div:nth-of-type(1) {
            color: #0f0f13;
            font-size: 14px;
            margin-right: 10px;
          }
          > div:nth-of-type(2) {
            color: #116ef9;
            margin-right: auto;
            font-size: 8px;
            cursor: pointer;
          }
          > div:nth-of-type(3) {
            font-size: 14px;
            color: #2c2c35;
          }
        }
      }
      .el-dialog__body {
        padding: 0;
        display: flex;
        justify-content: center;
        .upload-demo {
          width: 517px;
          height: 192px;
          margin: 0;
          display: flex;
          justify-content: center;
          .upload-file-icon {
            width: 69px;
            height: 45px;
            margin-top: 50px;
          }
          .el-upload.el-upload--text {
            width: 100%;
            height: 100%;
            .el-upload-dragger {
              width: 100%;
              height: 100%;
              margin: 0;
              .el-upload__text {
                color: #0f0f13;
                em {
                  color: #116ef9;
                }
              }
              .text-maxsize {
                font-size: 10px;
                margin-top: 7px;
                color: #0f0f13;
              }
            }
          }
        }
        .upload-timing {
          .uploading-box {
            width: 517px;
            height: 76px;
            border: 1px solid #f2f3f7;
            box-sizing: border-box;
            border-radius: 8px;
            padding: 0 24px;
            padding-bottom: 16px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            position: relative;
            .el-progress-bar__outer {
              height: 6px !important;
            }
            > div:nth-of-type(1) {
              font-size: 14px;
              margin-bottom: 4px;
              padding-right: 50px;
              box-sizing: border-box;
              > span:nth-of-type(1) {
                color: #116ef9;
              }
              > span:nth-of-type(2) {
                color: #0f0f13;
              }
            }

            .success-icon {
              width: 38px;
              height: 38px;
              font-size: 38px;
              position: absolute;
              top: 8px;
              right: 8px;
            }
          }
          > div:nth-of-type(2) {
            margin-top: 8px;
            font-size: 10px;
          }
        }
      }
      .el-dialog__footer {
        padding: 0 16px 16px 16px;
        .cancel {
          width: 78px;
          height: 32px;
          background: #ffffff;
          border: 1px solid #f2f3f7;
          box-sizing: border-box;
          border-radius: 4px;
          color: #0f0f13;
          padding: 0;
        }
        .submit {
          width: 78px;
          height: 32px;
          background: #116ef9;
          border-radius: 4px;
          color: #ffffff;
          padding: 0;
        }
        .submit-d {
          width: 78px;
          height: 32px;
          background: #cecece;
          border-radius: 4px;
          color: #ffffff;
          padding: 0;
          border: 0;
          // cursor: not-allowed;
          pointer-events: none;
        }
      }
    }
  }
}
</style>
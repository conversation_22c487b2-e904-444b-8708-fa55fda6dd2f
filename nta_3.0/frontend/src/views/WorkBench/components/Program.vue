<template>
  <div class="Program-box">
    <el-form ref="ruleRespond" :rules="rules" :model="formData">
      <div class="Program-box-top">
        <div>复杂规则ID</div>
        <el-form-item prop="id" :rules="rules.ruleEmpty">
          <el-select
            v-model="formData.id"
            placeholder="请选择"
            :reserve-keyword="false"
          >
            <el-option
              v-for="item in idFilterOptions"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="Program-box-down">
        <div class="Program-box-down-l">
          <div class="title">规则类型</div>
          <el-form-item prop="ruleType" :rules="rules.ruleEmpty">
            <el-select
              v-model="formData.ruleType"
              placeholder="请选择"
              :reserve-keyword="false"
              size="medium"
            >
              <el-option
                v-for="item in ruleTypeOption"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
                <!-- <span style="float: left; color: #8492a6; font-size: 13px">{{
                  item.name
                }}</span> -->
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="Program-box-down-r">
          <div class="title">规则值</div>
          <el-form-item
            prop="ruleValue"
            :rules="
              formData.ruleType === 'ip'
                ? rules.ruleIP
                : formData.ruleType === 'ipseg'
                  ? rules.ruleIPMask
                  : formData.ruleType === 'port'
                    ? rules.rulePort
                    : formData.ruleType === 'keyword'
                      ? rules.ruleReg
                      : formData.ruleType === 'regex'
                        ? rules.ruleRegexp
                        : rules.ruleEmpty
            "
          >
            <div v-if="formData.ruleType === 'portrange'">
              <el-input-number
                v-model="formData.portrange[0]"
                :controls="false"
                :min="0"
                :max="Number(formData.portrange[1]) || 65535"
                class="number-input"
                size="small"
              ></el-input-number>
              <span class="to-line label-style">——</span>
              <el-input-number
                v-model="formData.portrange[1]"
                :controls="false"
                :min="Number(formData.portrange[0])"
                :max="65535"
                class="number-input"
                size="small"
              ></el-input-number>
            </div>
            <el-select
              v-else-if="formData.ruleType === 'ipproto'"
              v-model="formData.ruleValue"
              clearable
              filterable
              placeholder="请选择"
              :reserve-keyword="false"
              style="width: 340px"
              size="medium"
            >
              <el-option
                v-for="item in ipprotoOption"
                :key="item.id"
                :label="item.value"
                :value="item.id"
              >
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right">
                  {{ item.value }}
                </span>
              </el-option>
            </el-select>
            <el-select
              v-else-if="formData.ruleType === 'appid'"
              v-model="formData.ruleValue"
              clearable
              filterable
              placeholder="请选择"
              :reserve-keyword="false"
              style="width: 380px"
              size="medium"
            >
              <el-option
                v-for="item in appidOption"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
                <!-- <span style="float: left; color: #8492a6; font-size: 13px">{{
                  item.label
                }}</span> -->
              </el-option>
            </el-select>
            <el-input
              v-else-if="formData.ruleType == 'keyword'"
              v-model="formData.ruleValue"
              placeholder="请输入规则值"
              size="medium"
              style="width: 340px"
            ></el-input>
            <el-input
              v-else-if="formData.ruleType == 'regex'"
              v-model="formData.ruleValue"
              placeholder="请输入规则值"
              size="medium"
              style="width: 340px"
            ></el-input>
            <el-input
              v-else
              v-model="formData.ruleValue"
              placeholder="请输入规则值"
              size="medium"
              style="width: 340px"
            ></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <!--  运算关系 -->
    <div class="Program-box-filter">
      <div class="title">运算关系：</div>
      <el-form ref="filter" :rules="rules" :model="filter" class="filter">
        <el-form-item
          prop="operation"
          :rules="rules.ruleFilter"
          style="text-align: left"
        >
          <el-input v-model="filter.operation" size="medium"></el-input>
        </el-form-item>
      </el-form>
      <div style="margin-left: 10px">注：运算符号：& || ! ()</div>
    </div>
    <!-- 复杂规则列表 -->
    <el-table :data="ruleRespondList" style="width: 100%">
      <el-table-column prop="id" label="复杂规则ID" align="center">
      </el-table-column>
      <el-table-column prop="ruleTypeCN" label="规则类型" align="center">
      </el-table-column>
      <el-table-column
        prop="ruleValue"
        label="规则值"
        width="400"
        align="center"
      >
      </el-table-column>
      <el-table-column label="操作" width="180" align="center">
        <template slot-scope="scope">
          <el-button type="text" size="medium" @click="editRuleInfo(scope.row)">
            修改
          </el-button>
          <el-button
            type="text"
            size="medium"
            @click="deleteRuleInfo(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加\保存 按钮 -->
    <div class="tab-box-btn">
      <div v-if="showsave" class="tab-box-btn-l" @click="addToList">
        <el-button>
          <svg-icon icon-class="frule-add" />
        </el-button>
      </div>
      <div v-if="!showsave" class="tab-box-btn-r" @click="editruleRespond">
        <el-button>
          <svg-icon icon-class="saveRule" />
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { validateIP, validateIPMask, validateRuleName } from "@/utils";
import dict from "@/assets/dict.json";
export default {
  name: "RuleRespond",
  // eslint-disable-next-line vue/require-prop-types
  props: ["ruleRespondData"],
  data() {
    let idOptions = [];
    for (let i = 65; i <= 90; i++) {
      idOptions.push(String.fromCharCode(i));
    }
    function validateFilter(rule, value, callback) {
      if (value == "" || value == undefined || value == null) {
        callback();
      } else {
        try {
          // 出现非运算关系字符报错
          let new_value = value.replace(/&|\|\||!|\(|\)/g, ",");
          let arr = new_value.split(",").reduce((prev, cur) => {
            if (cur) prev.push(cur);
            return prev;
          }, []);
          let fitstResult = arr.every((item) => {
            return idOptions.includes(item);
          });
          if (!fitstResult) throw new Error();

          // 不能正常执行报错
          let str = "";
          value.split("").forEach((item) => {
            str += idOptions.includes(item) ? '"' + item + '"' : item;
          });
          str = str.replace("&", "&&");
          eval(str);
          callback();
        } catch (e) {
          callback(new Error("输入格式不正确"));
        }
      }
    }

    function validatePort(rule, value, callback) {
      if (value == "" || value == undefined || value == null) {
        callback();
      } else {
        if (
          !(/^[1-9]\d*$/.test(value) && 1 <= 1 * value && 1 * value <= 65535)
        ) {
          callback(new Error("请输入正确的端口号"));
        } else {
          callback();
        }
      }
    }
    return {
      formData: {},
      ruleRespondList: [],
      ruleRespondParam: {},
      idOptions,
      idDisabled: [],
      filter: {
        operation: "",
      },
      ruleType: "",
      proType: { 1: "连接", 2: "单包", 3: "负载" },
      ruleTypeOption: [
        {
          name: "源IP地址",
          value: "srcip",
        },
        {
          name: "目的IP地址",
          value: "dstip",
        },
        {
          name: "IP网段",
          value: "ipseg",
        },
        {
          name: "源端口",
          value: "srcport",
        },
        {
          name: "目的端口",
          value: "dstport",
        },
        {
          name: "端口范围",
          value: "portrange",
        },
        {
          name: "IP协议号",
          value: "ipproto",
        },
        {
          name: "协议名称",
          value: "appid",
        },
        {
          name: "特征字（仅针对负载）",
          value: "keyword",
        },
        {
          name: "正则表达式（仅针对负载）",
          value: "regex",
        },
      ],
      ruleValueOption: [],
      ipprotoOption: [],
      appidOption: [],
      showsave: true,
      rules: {
        ruleEmpty: [{ required: true, message: "不能为空", trigger: [] }],
        ruleIP: [
          { required: true, message: "不能为空", trigger: [] },
          { required: true, trigger: ["change"], validator: validateIP },
        ],
        ruleIPMask: [
          { required: true, message: "不能为空", trigger: [] },
          { required: true, trigger: ["change"], validator: validateIPMask },
        ],
        rulePort: [
          { required: true, message: "不能为空", trigger: [] },
          { required: true, trigger: ["change"], validator: validatePort },
        ],
        ruleFilter: [
          { required: true, message: "不能为空", trigger: ["change"] },
          { required: true, trigger: ["change"], validator: validateFilter },
        ],
        ruleRegexp: [
          { required: true, message: "不能为空", trigger: ["blur"] },
          {
            required: true,
            message: "请输入正确的正则",
            trigger: ["change"],
            validator: validateRuleName,
          },
        ],
        ruleReg: [
          { required: true, message: "不能为空", trigger: ["blur"] },
          {
            required: true,
            message: "请输入正确的特征字",
            trigger: ["change"],
            validator: validateRuleName,
          },
        ],
      },
    };
  },
  computed: {
    idFilterOptions() {
      return this.idOptions.filter((item) => !this.idDisabled.includes(item));
    },
  },
  watch: {
    formData: {
      handler(val) {
        if (val.ruleType !== this.ruleType) {
          this.formData.ruleValue = "";
          this.ruleType = val.ruleType;
        }
        // if (val.ruleType === 'ipproto') {
        //   this.ruleValueOption = this.ipprotoOption
        // } else if (val.ruleType === 'appid') {
        //   this.ruleValueOption = this.appidOption
        // }
      },
      deep: true,
    },
    ruleRespondData: {
      handler() {
        this.$nextTick(() => {
          this.$refs.ruleRespond.clearValidate();
        });
        this.changeShowFormat();
      },
      deep: true,
    },
    ruleRespondList: {
      handler(val) {
        this.idDisabled = [];
        val.forEach(({ id }) => {
          this.idDisabled.push(id);
        });
        for (let i = 0; i <= this.idOptions.length; i++) {
          if (!this.idDisabled.includes(this.idOptions[i])) {
            this.formData.id = this.idOptions[i];
            break;
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    for (let i in dict.data.protocol_type) {
      if (dict.data.protocol_type[i].protocol_remark) {
        this.ipprotoOption.push({
          id: i,
          label: dict.data.protocol_type[i].protocol_remark,
          value: dict.data.protocol_type[i].protocol_type,
        });
      }
    }
    this.initIDProOptions();
    // for (let i in dict.data.app_value_rule) {
    //   this.appidOption.push({
    //     id: i,
    //     label: dict.data.app_value_rule[i],
    //   });
    // }
    this.initFormData();
    this.ruleRespondList = [];
    this.ruleRespondParam = {};
  },
  methods: {
    initFormData() {
      this.formData = {
        id: "",
        ruleType: "",
        ruleValue: "",
        portrange: [],
      };
    },
    // 参数规则检验
    ValidationRules() {
      let flag = true;
      this.$refs["filter"].validate((valid) => {
        if (!valid) {
          flag = false;
          // this.$message.error("检验错误，请检查必填项是否填写。");
          return false;
        }
      });
      return flag;
    },
    // 展示数据转换为规则传参数据
    showDataToRuleParams() {
      this.ruleRespondParam = {};
      let list = [...this.ruleRespondList];
      list.forEach((item) => {
        let ruleValue = item.ruleValue;
        if (item.ruleType === "ipproto") {
          ruleValue = item.ipprotoId;
        } else if (item.ruleType === "appid") {
          ruleValue = item.appId;
        }
        this.ruleRespondParam[item.id] = {
          type: item.ruleType,
          rule: {
            [item.ruleType]: ruleValue,
          },
        };
      });
      console.log(this.ruleRespondParam, "复杂规则");
      this.$emit("getRuleRespondParam", this.ruleRespondParam);
    },
    // 将后端返回数据规范为前端可展示绑定数据
    changeShowFormat() {
      this.initFormData();
      this.ruleRespondList = [];
      this.ruleRespondParam = {};
      if (Object.keys(this.ruleRespondData).length !== 0) {
        if (this.ruleRespondData.EXPR) {
          this.filter.operation = this.ruleRespondData.EXPR;
        }
        let arr = [];
        for (let id in this.ruleRespondData) {
          let tempItem;
          if (typeof this.ruleRespondData[id] === "object") {
            tempItem = {
              index: id,
              id: id,
              ruleType: this.ruleRespondData[id].type,
              ruleValue:
                this.ruleRespondData[id]["rule"][this.ruleRespondData[id].type],
            };
            this.ruleTypeOption.forEach((item) => {
              if (item.value === tempItem.ruleType) {
                tempItem.ruleTypeCN = item.name;
              }
            });
            // 存储协议id，规则值由协议id转换为值
            if (tempItem.ruleType === "ipproto") {
              tempItem.ipprotoId = tempItem.ruleValue;
              tempItem.ruleValue =
                dict.data.protocol_type[tempItem.ruleValue].protocol_remark;
            } else if (tempItem.ruleType === "appid") {
              tempItem.appId = tempItem.ruleValue;
              tempItem.ruleValue =
                this.proType[
                  dict.data.app_type_map[parseInt(tempItem.ruleValue)]
                ] +
                " - " +
                dict.data.app_id[tempItem.ruleValue] +
                "(" +
                dict.data.app_value[parseInt(tempItem.ruleValue)] +
                ")";
            }
            arr.push(tempItem);
          }
        }
        this.ruleRespondList = arr.slice(0);
      }
    },
    // 将表单数据转换为表格可用数据
    formDataToTableData(flag) {
      let ruleRespondItem = {
        index: this.formData.id,
        id: this.formData.id,
        ruleType: this.formData.ruleType,
        ruleValue: this.formData.ruleValue,
      };
      // 修改时类型为端口范围，转换为ruleValue
      if (flag === "edit" && ruleRespondItem.ruleType === "portrange") {
        ruleRespondItem.ruleValue =
          this.formData.portrange[0] + "~" + this.formData.portrange[1];
      }
      this.ruleTypeOption.forEach((item) => {
        if (item.value === ruleRespondItem.ruleType) {
          ruleRespondItem.ruleTypeCN = item.name;
        }
      });
      // 存储协议id，规则值由协议id转换为值
      if (ruleRespondItem.ruleType === "ipproto") {
        ruleRespondItem.ipprotoId = ruleRespondItem.ruleValue;
        ruleRespondItem.ruleValue =
          dict.data.protocol_type[ruleRespondItem.ruleValue].protocol_remark;
      } else if (ruleRespondItem.ruleType === "appid") {
        ruleRespondItem.appId = ruleRespondItem.ruleValue;
        ruleRespondItem.ruleValue =
          this.proType[
            dict.data.app_type_map[parseInt(ruleRespondItem.ruleValue)]
          ] +
          " - " +
          dict.data.app_id[ruleRespondItem.ruleValue] +
          "(" +
          dict.data.app_value[parseInt(ruleRespondItem.ruleValue)] +
          ")";
      }
      return ruleRespondItem;
    },
    addToList() {
      // 类型为端口范围时，转换为ruleValue，通过校验
      if (this.formData.ruleType === "portrange") {
        this.formData.ruleValue =
          this.formData.portrange[0] + "~" + this.formData.portrange[1];
      }
      this.$refs["ruleRespond"].validate((valid) => {
        if (valid) {
          if (this.ruleRespondList.length === 0) {
            this.ruleRespondList.unshift(this.formDataToTableData());
          } else {
            let i = 0;
            for (; i < this.ruleRespondList.length; i++) {
              let item = this.ruleRespondList[i];
              if (item.id > this.formDataToTableData().id) {
                this.ruleRespondList.splice(i, 0, this.formDataToTableData());
                break;
              }
            }
            if (i === this.ruleRespondList.length) {
              this.ruleRespondList.push(this.formDataToTableData());
            }
          }
          this.showDataToRuleParams();
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 修改规则
    editruleRespond() {
      this.$refs["ruleRespond"].validate((valid) => {
        if (valid) {
          this.ruleRespondList = this.ruleRespondList.map((item, index) => {
            if (item.id === this.formData.id) {
              item = this.formDataToTableData("edit");
            }
            return item;
          });
          this.showDataToRuleParams();
          this.initFormData();
          this.$message.success("保存成功");
          this.showsave = true;
        } else {
          this.$message.error("检验错误，请检查必填项是否填写。");
        }
      });
    },
    // 将要修改的规则反写到表单里
    editRuleInfo(row) {
      let data = JSON.parse(JSON.stringify(row));
      if (data.ruleType === "portrange") {
        data.portrange = data.ruleValue.split("~");
      } else if (data.ruleType === "ipproto") {
        data.ruleValue = data.ipprotoId;
      } else if (data.ruleType === "appid") {
        data.ruleValue = data.appId;
      }
      this.ruleType = data.ruleType;
      this.formData = data;
      this.showsave = false;
      var scrollTop = document.querySelector("#addRules");
      if (scrollTop.scrollTop > 390) {
        scrollTop.scrollTop = 390;
      }
    },
    deleteRuleInfo(row) {
      this.ruleRespondList.forEach((item, index) => {
        if (item.id === row.id) {
          this.ruleRespondList.splice(index, 1);
        }
      });
      this.showDataToRuleParams();
    },
    // 获取协议ID
    initIDProOptions() {
      let id_pro_full_options = [];
      for (let key in dict.data.app_id) {
        if (dict.data.app_type_map[key] != 2) {
          id_pro_full_options.push({
            id: key,
            label:
              this.proType[dict.data.app_type_map[key]] +
              " - " +
              dict.data.app_id[key] +
              "(" +
              dict.data.app_value[key] +
              ")",
          });
        }
        this.appidOption = id_pro_full_options;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.Program-box {
  position: relative;
  ::v-deep .el-input__inner {
    height: 27px !important;
    font-weight: 400;
    font-size: 12px;
  }
  .el-select {
    width: 310px;
  }
  title {
    font-weight: 500;
    font-size: 12px;
  }
  .tab-box-btn {
    position: absolute;
    top: 0px;
    right: 20px;
    .el-button {
      width: 35px;
      height: 27px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  &-down {
    display: flex;
    align-items: center;
    &-l {
      margin-right: 30px;
    }
  }
  &-filter {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .el-form-item {
      margin: 0;
    }
    .el-input {
      width: 447px;
    }
  }
}
</style>
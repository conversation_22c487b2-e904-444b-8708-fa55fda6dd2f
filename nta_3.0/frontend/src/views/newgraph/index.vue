<template>
  <div v-loading="loading" class="newgraph" element-loading-text="数据请求中" element-loading-spinner="el-icon-loading"
       element-loading-background="rgba(255, 255, 255, 0.8)"
  >
    <div id="header" class="header">
      <div class="tool">
      </div>
      <div class="search">
        <div class="search-sign">
          <el-button @click="RESET_SIGN">清除标记</el-button>
        </div>
        <div class="full" alt="">
          <img src="@/assets/graph/icon_16_setting-config.svg" @click="TOOL_MENU">
        </div>
        <el-input v-model="searchValue" type="textarea" :autosize="{ minRows: 1, maxRows: 10 }"
                  placeholder="请输入VID,一行一个数据，最多十条" @input="INPUT"
        ></el-input>
        <el-button @click="START_SEARCH(1)">添加节点</el-button>
        <el-button class="clear" @click="RESET_SEARCH">清除</el-button>
      </div>
    </div>
    <aside class="type-box">
      <div v-for="(item, index) of asideList" :key="index" class="type-box-cell">
        <div>
          <img :src="item.src" alt="">
        </div>
        <div>{{ item.name }}</div>
      </div>
    </aside>
    <div ref="mainbox" class="graph-box" @click="isShowNodeMenuPanel = false">
      <SeeksRelationGraph ref="seeksRelationGraph" :options="G" :on-node-click="onNodeClick" :on-line-click="onLineClick">
        <div slot="node" slot-scope="{node}">
          <!-- 普通节点 -->
          <div v-if="node.data.type !== 'Folder' && node.data.type !== 'LABELS'" id="node" :class="NODE_CLASS(node.data.identity)"
               @contextmenu.prevent.stop="showNodeMenus(node, $event)"
          >
            <img :src="node.data.img" />
            <aside v-if="node.data.sign === true" class="sign">
              <div>
                <img src="@/assets/images/20230417-174410.png" alt="">
              </div>
            </aside>
          </div>
          <!-- 文件夹节点 -->
          <div v-if="node.data.type === 'Folder'" class="folder-node"
               @contextmenu.prevent.stop="showNodeMenus(node, $event)"
          >
            <img :src="node.data.img" />
          </div>
          <!-- 标签节点 -->
          <div v-if="node.data.type === 'LABELS'" class="labels-node">
            <section v-for="(item, index) of node.data.labels" :key="index" :class="LABEL_LV(item)">
              {{ item.tagText }}
            </section>
            <aside class="del" @click="DEL_LABELS(node, $event)">
              <i class="el-icon-close"></i>
            </aside>
          </div>
          <div v-if="node.data.type !== 'LABELS'" :class="settingForm.name == '1' ? 'node-label' : 'node-label-hide'">
            {{ node.data.label }}
          </div>
        </div>
      </SeeksRelationGraph>
    </div>
    <div v-show="isShowNodeMenuPanel"
         :style="{ left: nodeMenuPanelPosition.x + 'px', top: nodeMenuPanelPosition.y + 'px' }" class="clickmenu"
    >
      <section v-if="currentNode.lot.childs.length" @click="onNodeExpand">{{ currentNode.expanded?'收起':'展开' }}</section>
      <section @click="toggle">{{ currentNode.data.label?'隐藏':'显示' }}</section>
      <section @click="REAL">关联</section>
      <section v-if="currentNode.data.sign" @click="CANCEL_SIGN">取消标记</section>
      <section v-else @click="SIGN">标记</section>
      <section v-if="currentNode.data.type === 'IP' && initType === 'JUDGE'" @click="CAUTION">告警角色</section>
      <section @click="VIEW_LABELS">查看标签</section>
      <section @click="DEL_NODE">删除</section>
    </div>
    <!-- -------------------关联弹出框----------------------- -->
    <el-dialog :title="relaTitle" :visible.sync="relaShow" width="600px" class="rela-dialog" destroy-on-close
               :show-close="!relaLoading" :close-on-press-escape="!relaLoading" :close-on-click-modal="!relaLoading"
    >
      <div v-loading="relaLoading" element-loading-text="数据请求中" element-loading-spinner="el-icon-loading"
           element-loading-background="rgba(255, 255, 255, 1)"
      >
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全部关联</el-checkbox>
        <div style="margin: 15px 0;"></div>
        <el-checkbox-group v-model="relaArr" @change="handleCheckedCitiesChange">
          <div v-for="(item, index) in relaOptions" :key="index" class="check-cell">
            <el-checkbox :key="index" :label="item.name">
              {{ item.label }}
            </el-checkbox>
            <el-select v-model="item.black" clearable size="mini" style="margin-left:auto;margin-right: 5px;">
              <el-option v-for="(item2,index2) in balckOptions" :key="index2" :label="item2.label" :value="item2.black">
              </el-option>
            </el-select>
            <el-input-number v-model="item.num" :min="1" :max="10" size="mini"></el-input-number>
          </div>
        </el-checkbox-group>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button :disabled="relaLoading" @click="relaShow = false">取 消</el-button>
        <el-button type="primary" :disabled="handleSubmit || relaLoading" @click="CLICK_REAL">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 设置弹出框 -->
    <el-dialog title="设置" width="480px" :visible.sync="settingShow" custom-class="setting">
      <el-form label-position="top" label-width="80px" :model="settingForm">
        <el-form-item label="方向">
          <el-radio v-model="settingForm.position" label="all">双向</el-radio>
          <el-radio v-model="settingForm.position" label="out">流出</el-radio>
          <el-radio v-model="settingForm.position" label="in">流入</el-radio>
        </el-form-item>
        <el-form-item label="节点名称">
          <el-radio v-model="settingForm.name" label="1">显示</el-radio>
          <el-radio v-model="settingForm.name" label="0">隐藏</el-radio>
        </el-form-item>
        <el-form-item label="默认最大展开节点数">
          <el-input v-model="settingForm.num"></el-input>
        </el-form-item>
        <el-form-item label="节点是否跟随移动" :input="NODE_FOLLOW">
          <el-radio v-model="G.isMoveByParentNode" :label="1">是</el-radio>
          <el-radio v-model="G.isMoveByParentNode" :label="0">否</el-radio>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="" width="480px" :visible.sync="cautionShow" custom-class="cautionDialog">
      <el-form label-position="top" label-width="80px" :model="cautionFrom">
        <el-form-item label="告警角色">
          <el-checkbox-group v-model="cautionFrom.role" :min="1" :max="2" style="flex-direction: flex;">
            <el-checkbox label="victim">受害者</el-checkbox>
            <el-checkbox label="attacker">攻击者</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker v-model="cautionFrom.time_range" popper-class="caution-time" value-format="timestamp"
                          type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="告警条数">
          <el-input-number v-model="cautionFrom.query_num" :min="1" :max="10" size="small" label=""></el-input-number>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cautionShow = false">取 消</el-button>
        <el-button type="primary" @click="CAUTION_SUBMIT()">确 定</el-button>
      </span>
    </el-dialog>
    <Detail v-if="visibleShow" :visible-show="visibleShow" :detail-value="detailValue" :detail-type="detailType"
            :detail-label="detailLabel" @DETAIL_CLOSE="DETAIL_CLOSE"
    />
  </div>
</template>

<script>
import SeeksRelationGraph from 'relation-graph';
import { get_data, get_next, get_edge_json, alarm_data, search_node, tagLabels, visibleRelation, alarm_judge, judge_role,focusTag } from '@/api/graph';
import { formatGraphData } from './fmtData';
import Detail from './detail/index.vue';
import moment from "moment";
import request from "@/utils/request";

export default {
  name: 'Newgraph',
  components: { SeeksRelationGraph, Detail },
  data () {
    return {
      loading: false,
      isShowNodeMenuPanel: false,
      nodeMenuPanelPosition: { x: 0, y: 0 },
      currentNode: {
        data: {
          type: '',
        },
        lot:{
          childs:[]
        }
      },
      G: {
        // 这里可以参考"Graph 图谱"中的参数进行设置
        g_loading: true,
        demoname: '---',
        defaultNodeBorderWidth: 0,
        defaultNodeColor: 'rgba(238, 178, 94, 1)',
        defaultLineColor: '#000',
        allowSwitchLineShape: true,
        defaultLineShape: 1,
        layouts: [
          {
            label: '力导',
            layoutName: 'force',
          },
          {
            label: '中心',
            layoutName: 'center',
          }

        ],
        defaultJunctionPoint: 'border',
        defaultLineMarker: {
          "markerWidth": 20,
          "markerHeight": 20,
          "refX": 3,
          "refY": 3,
          "data": "M 0 0, V 6, L 4 3, Z"
        },
        // 节点跟随
        isMoveByParentNode: 0
      },

      // 关联弹出框
      handleList: [],
      relaShow: false,
      relaTitle: '',
      checkAll: false,
      allOptions: [],
      relaArr: [],
      relaOptions: [],
      isIndeterminate: false,
      handleSubmit: true,
      relaLoading: false,
      // 设置弹出框
      settingShow: false,
      settingForm: {
        position: 'all',
        name: '1',
        num: 5
      },
      // 搜索的值
      searchValue: '',
      // 详情组件相关
      visibleShow: false,
      detailValue: '',
      detailType: '',
      detailLabel: '',
      // 左侧图例
      asideList: [
        {
          name: '区块链',
          src: require('../../assets/graph/icon_blockchain.svg')
        },
        {
          name: '资产测绘数据',
          src: require('../../assets/graph/icon_资产测绘.svg')
        },
        {
          name: 'URL',
          src: require('../../assets/graph/icon_URL.svg')
        },
        {
          name: 'MAC',
          src: require('../../assets/graph/icon_MAC.svg')
        },
        {
          name: 'IP',
          src: require('../../assets/graph/icon_IP.svg')
        },
        {
          name: '应用服务',
          src: require('../../assets/graph/icon_APP.svg')
        },
        {
          name: '域名',
          src: require('../../assets/graph/icon_web.svg')
        },
        {
          name: '锚域名',
          src: require('../../assets/graph/icon_anchor.svg')
        },
        {
          name: '证书',
          src: require('../../assets/graph/icon_certificate.svg')
        },
        {
          name: '企业名',
          src: require('../../assets/graph/icon_enterprise.svg')
        },
        {
          name: 'SSL指纹',
          src: require('../../assets/graph/icon_fingerprint.svg')
        },
        {
          name: 'UA',
          src: require('../../assets/graph/icon_UA.svg')
        },
        {
          name: '硬件类型',
          src: require('../../assets/graph/icon_hardware.svg')
        },
        {
          name: '系统类型',
          src: require('../../assets/graph/icon_system.svg')
        },
        {
          name: '应用类型',
          src: require('../../assets/graph/icon_Apptype.svg')
        },
        {
          name: '攻击者',
          src: require('../../assets/graph/icon_attacker.svg')
        },
        {
          name: '受害者',
          src: require('../../assets/graph/icon_sufferer.svg')
        },
        {
          name: '攻击者/受害者',
          src: require('../../assets/graph/攻击_受害者.svg')
        },
      ],
      // 黑名单权重下拉
      balckOptions: [
        {
          label: '默认(所有等级)',
          black: ''
        },
        {
          label: '高危',
          black: [91, 100]
        },
        {
          label: '可疑',
          black: [61, 90]
        },
        {
          label: '未知',
          black: [1, 60]
        },
        {
          label: '安全',
          black: [0, 0]
        }
      ],
      balckValue: '',
      initId: '',
      initIndex: '',
      // 告警研判
      // 弹出框
      cautionShow: false,
      cautionFrom: {
        ip_addr: '',
        role: [],
        query_num: 5,
        time_range: []
      },
      requestData:[]
    };
  },
  watch: {
    'settingForm.position': function (val, old) { 
      localStorage.setItem('detailLablePosition', val);
    },
    'settingForm.num': function (val, old) {
      localStorage.setItem('nextNum', val);
    },
    'settingForm.name': function (val, old) {
      this.settingForm.name = val;
      localStorage.setItem('nameShow', val);
    },
    'G.isMoveByParentNode': function (val, old) {
      localStorage.setItem('isMoveByParentNode', val);
      this.$refs.seeksRelationGraph.setOptions({
        isMoveByParentNode: val
      });
    }
  },
  created () {
    this.GET_DICT();
    if (this.$route.query.initValue && this.$route.query.initType) {
      this.initValue = this.$route.query.initValue;
      this.initType = this.$route.query.initType;
    } else if (this.$route.query.initId && this.$route.query.initIndex) {
      this.initId = this.$route.query.initId;
      this.initIndex = this.$route.query.initIndex;
      this.initType = 'JUDGE';
    }
    else {
      this.initValue = '';
      this.initType = '';
    }
    if (!localStorage.getItem('detailLablePosition')) {
      localStorage.setItem('detailLablePosition', 'all');
    } else {
      this.settingForm.position = localStorage.getItem('detailLablePosition');
    }
    if (!localStorage.getItem('nextNum')) {
      localStorage.setItem('nextNum', 5);
    } else {
      this.settingForm.num = localStorage.getItem('nextNum');
    }
    if (!localStorage.getItem('nameShow')) {
      localStorage.setItem('nameShow', '1');
    } else {
      this.settingForm.name = localStorage.getItem('nameShow');
    }
    if (!localStorage.getItem('isMoveByParentNode')) {
      localStorage.setItem('isMoveByParentNode', 0);
    } else {
      this.G.isMoveByParentNode = JSON.parse(localStorage.getItem('isMoveByParentNode'));
    }
    this.GET_JSON();
  },
  mounted () {
    this.INIT_GRAPH();
  },
  methods: {
    // 显示隐藏名称
    toggle(){
      const targetNodeId = this.currentNode.id;
      // 获取relation-graph 实例
      const graphInstance = this.$refs.seeksRelationGraph.getInstance();
      // 调用实例方法获取节点对象
      const targetNode = graphInstance.getNodeById(targetNodeId);
      if( targetNode.data.label){
        targetNode.data.label='';
      }else{
        targetNode.data.label=targetNode.data.hideLabel;
      }
    },
    // 展开和收起
    async onNodeExpand(){
      const targetNodeId = this.currentNode.id;
      // 获取relation-graph 实例
      const graphInstance = this.$refs.seeksRelationGraph.getInstance();
      // 调用实例方法获取节点对象
      const targetNode = graphInstance.getNodeById(targetNodeId);
      // 根据节点对象属性判断节点是否已经展开
      if (targetNode.expanded === true) {
        // 调用实例方法收缩节点
        await graphInstance.collapseNode(targetNode); // 这个e可以不传，除非需要触发<relation-graph />组件的on-node-click事件且该事件中需要使用到event对象
      } else {
        // 调用实例方法展开节点
        await graphInstance.expandNode(targetNode);// 这个e可以不传，除非需要触发<relation-graph />组件的on-node-click事件且该事件中需要使用到event对象
      }
    },
    // 获取边的转折点关系
    GET_JSON () {
      get_edge_json().then(res => {
        this.handleList = res.data;
      });
    },
   
    // 线条去重函数
    LINE_DE_WEIGHT() { 
      let links = this.$refs.seeksRelationGraph.getInstance().getLinks();
      for (let i = 0; i < links.length; i++) {
        let arr = [];
        for (let j = 0; j < links[i].relations.length; j++) {
          let t = links[i].relations[j];
          if (arr.find((k) =>
            k.to === t.to && k.from === t.from && k.label === t.label
          )) {
            continue;
          }
          arr.push(t);
        }
        links[i].relations = arr;
      }
    },
    // 查询
    INIT_GRAPH (val) {
      this.loading = true;
      if (this.initType === 'ID') {
        // 告警
        alarm_data({
          _id: this.initValue
        }).then(res => {
          this.loading = false;
        }).catch(err => {
          this.loading = false;
        });
      } else if (this.initType === 'JUDGE') {
        // 告警研判
        alarm_judge({
          alarm_id: this.initId,
          alarm_index: this.initIndex
        }).then(res => {
          if (res.err === 0 && res.data.vertex.length > 0) {
            this.$refs.seeksRelationGraph.setJsonData(formatGraphData(res.data), (seeksRGGraph) => {
              // 关系图渲染完成时会调用
              this.$message.success('图谱渲染完成');
              console.warn('渲染完成：', seeksRGGraph);
              this.$nextTick(() => {
                this.$refs.seeksRelationGraph.refresh();
              });
            });
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
        });
      } else if (this.initType === 'SEARCH') {
        // 图探索
        if(val){
          if(this.searchValue){
            if(this.requestData.length){
              this.requestData.push(...this.searchValue.split('\n'));
            }else{
              this.requestData=[...this.searchValue.split('\n')];
            }
          }
        }else{
          this.requestData=Array.isArray(this.initValue)?this.initValue:[this.initValue];
        }
        search_node(this.requestData).then(res => {
          if (res.err === 0 && res.data.vertex.length > 0) {
            this.$refs.seeksRelationGraph.setJsonData(formatGraphData(res.data), (seeksRGGraph) => {
              // 关系图渲染完成时会调用
              this.$message.success('图谱渲染完成');
              for (let i of this.$refs.seeksRelationGraph.getGraphJsonData().nodes) {
                i.fixed = true;
                i.x = 100;
                i.y = 500;
              }
              this.$refs.seeksRelationGraph.refresh();
              this.GET_LABELS(res.data.vertex);
            });
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
        });
      } else {
        // 图数据
        get_data({
          str: this.initValue,
          type: this.initType
        }).then(res => {
          if (res.err === 0 && res.data.vertex.length > 0) {
            this.$refs.seeksRelationGraph.setJsonData(formatGraphData(res.data), (seeksRGGraph) => {
              // 关系图渲染完成时会调用
              this.$message.success('图谱渲染完成');
              console.warn('渲染完成：', seeksRGGraph);
            });
          }
          this.loading = false;
        }).catch(err => {
          this.loading = false;
        });
      }
    },
    // 获取标签
    GET_LABELS (nodes) {
      let arr = [];
      nodes.forEach(item => {
        arr.push({
          str: item.id,
          type: item.type
        });
      });
      tagLabels(arr).then(res => {
        let setData = {
          edge: res.data.edge,
          vertex: res.data.vertex
        };
        this.$refs.seeksRelationGraph.appendJsonData(formatGraphData(setData), true, (seeksRGGraph) => {
          // 关系图渲染完成时会调用
          this.$message.success('标签渲染完成');
          // console.warn('渲染完成：', this.$refs.seeksRelationGraph.getGraphJsonData())
        });
      });
    },
    onNodeClick (node, $event) {
      if (node.data.type === 'DOMAIN' || node.data.type === 'IP' || node.data.type === 'CERT' || node.data.type === 'ORG' || node.data.type === 'BLOCKCHAIN' || node.data.type === 'SSLFINGER') {
        this.detailType = node.data.type;
        this.detailValue = node.data.id;
        this.detailLabel = node.data.label;
        if (node.data.type === 'ORG') {
          this.detailValue = node.data.vid;
        }
        this.visibleShow = true;
      }
    },
    onLineClick (lineObject, $event) {
      console.log('左键点击线条:', lineObject);
    },
    showNodeMenus (nodeObject, $event) {

      this.currentNode = nodeObject;
      console.log('showNodeMenus:', this.currentNode, $event);
      let _base_position = this.$refs.mainbox.getBoundingClientRect();

      this.isShowNodeMenuPanel = true;
      this.nodeMenuPanelPosition.x = $event.clientX - _base_position.x;
      this.nodeMenuPanelPosition.y = $event.clientY - _base_position.y;
    },
    doAction (actionName) {
      this.$notify({
        title: '提示',
        message: '对节点【' + this.currentNode.text + '】进行了：' + actionName,
        type: 'success'
      });
      this.isShowNodeMenuPanel = false;
    },
    // 关联单选多选
    handleCheckAllChange (val) {
      console.log(val);
      this.relaArr = val ? this.allOptions : [];
      this.isIndeterminate = false;
    },
    handleCheckedCitiesChange (value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.relaOptions.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.relaOptions.length;
    },
    // 点击关联
    REAL () {
      this.isShowNodeMenuPanel = false;
      const model = this.currentNode.data;

      console.log(model);
      let direct = '';
      if (localStorage.getItem('detailLablePosition') === 'all') {
        direct = 'bothway';
      } else if (localStorage.getItem('detailLablePosition') === 'in') {
        direct = 'reverse';
      } else if (localStorage.getItem('detailLablePosition') === 'out') {
        direct = 'forward';
      } else {
        direct = 'bothway';
      }
      let newArr = [];
      this.handleSubmit = true;
      let str = model.id;

      let params = {
        str: str,
        type: model.type,
        direct
      };
      if (model.type === 'ORG') {
        params.str = model.vid;
      }

      visibleRelation(params).then(res => {
        console.log('关联边预查询：', res);
        if (res.err === 0) {
          this.handleSubmit = false;
          this.allOptions = res.data;
          let arr = res.data;
          for (let i = 0; i < arr.length; i++) {
            newArr.push({
              label: this.$store.state.long.Dict.nebula_type[arr[i]]?.desc || '未知（出错）',
              name: arr[i],
              num: this.settingForm.num,
              black: ''
            });
          }
        }
      });


      this.relaOptions = newArr;
      console.log("this.relaOptions", this.relaOptions);
      this.relaTitle = model.label;
      this.isIndeterminate = false;
      this.checkAll = false;
      this.relaArr = [];
      this.relaShow = true;
    },

    // 点击关联框中的确认
    CLICK_REAL () {
      this.relaLoading = true;
      let arr = [];
      for (let i = 0; i < this.relaOptions.length; i++) {
        for (let j = 0; j < this.relaArr.length; j++) {
          if (this.relaArr[j] === this.relaOptions[i].name) {
            // 剔除需要传到后端的前缀带r_的字符串
            if (this.relaOptions[i].name.substr(0, 2) === 'r_' && this.relaOptions[i].name != 'r_connect' && this.relaOptions[i].name != 'r_connect_mac' && this.relaOptions[i].name != 'r_cname' && this.relaOptions[i].name != 'r_client_query_dns_server' && this.relaOptions[i].name != 'r_blockchain_deal') {
              this.relaOptions[i].name = this.relaOptions[i].name.slice(2);
            }
            arr.push({
              edge: this.relaOptions[i].name,
              num: this.relaOptions[i].num,
              weight_limit: this.relaOptions[i].black === '' ? [0, 100] : this.relaOptions[i].black,
            });
          }
        }
      }
      let requestData = {
        str: this.currentNode.data.label,
        type: this.currentNode.data.type,
        edgeInfo: arr
      };
      if (this.currentNode.data.type === 'BLOCKCHAIN') {
        requestData.str = this.currentNode.data.id;
      }
      if (this.currentNode.data.type === 'ORG') {
        requestData.str = this.currentNode.data.vid;
        console.log('ORG关联：', this.currentNode.data);
      }
      get_next(requestData).then(res => {
        console.log(res);
        if (res.err === 0 && res.data.vertex.length > 0) {
          // 线条去重
          let setData = {
            edge: res.data.edge,
            vertex: res.data.vertex
          };
          this.$refs.seeksRelationGraph.appendJsonData(formatGraphData(setData), true, (seeksRGGraph) => {
            // 关系图渲染完成时会调用
            this.$message.success('新增节点');
            console.warn('渲染完成：', this.$refs.seeksRelationGraph.getGraphJsonData());
            let links = this.$refs.seeksRelationGraph.getInstance().getLinks();
            for (let i = 0; i < links.length; i++) {
              let arr = [];
              for (let j = 0; j < links[i].relations.length; j++) {
                let t = links[i].relations[j];
                if (arr.find((k) =>
                  k.to === t.to && k.from === t.from && k.label === t.label
                )) {
                  continue;
                }
                arr.push(t);
              }
              links[i].relations = arr;
            }
          });
        }
        this.relaLoading = false;
        this.relaShow = false;
      }).catch(err => {
        this.relaLoading = false;
      });
    },
    // 右键点击节点标记
    SIGN() { 
      console.log('当前点击标记的节点：', this.currentNode);
      this.isShowNodeMenuPanel = false;
      this.currentNode.data.sign = true;
      const allNodes = this.$refs.seeksRelationGraph.getInstance().getNodes();
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
      let signArr = [];
      for (let i = 0; i < allNodes.length; i++) { 
      
        if (allNodes[i].data.sign) { 
          signArr.push({
            vid: allNodes[i].data.id,
            type:allNodes[i].data.type
          });
        }
        
      }
      if (signArr.length > 1) { 
        focusTag(signArr).then(res => { 
          if (res.err === 0 && res.data.length > 0) { 
            let setData = {
              edge: res.data,
              vertex: []
            };
            // 先把其余变透明
            const allNodes = this.$refs.seeksRelationGraph.getInstance().getNodes();
            const allLinks = this.$refs.seeksRelationGraph.getInstance().getLinks();
            allNodes.forEach(item => {           
              item.opacity =  item.data.sign ? 1 : 0.1;
            });
            allLinks.forEach(item => {           
              if (item.fromNode.data.sign && item.toNode.data.sign) {
                item.isHide = false;
                item.relations.forEach(ri => { 
                  ri.color = '#116EF9';
                  ri.fontColor = '#116EF9';
                });
              } else { 
                item.isHide = true;
              }
            });
            this.$refs.seeksRelationGraph.getInstance().dataUpdated();
            this.$refs.seeksRelationGraph.appendJsonData(formatGraphData(setData), (seeksRGGraph) => {
              console.warn('添加比关注节点');
              this.$refs.seeksRelationGraph.getInstance().dataUpdated();
              this.LINE_DE_WEIGHT();
            });
          }
            
        });
      }
    },
    // 取消标记
    CANCEL_SIGN() { 
      this.isShowNodeMenuPanel = false;
      this.currentNode.data.sign = false;
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
    },
    // 清除标记
    RESET_SIGN() { 
      const allNodes = this.$refs.seeksRelationGraph.getInstance().getNodes();
      const allLinks = this.$refs.seeksRelationGraph.getInstance().getLinks();
      console.log('allNodes',allNodes);
      console.log('allLinks',allLinks);
      allNodes.forEach(item => {           
        item.opacity = 1;
        item.data.sign = false;
      });
      allLinks.forEach(item => {           
        item.isHide = false;
        item.relations.forEach(ri => { 
          ri.color = '#2C2C35';
          ri.fontColor = '#2C2C35';
        });
      });
      this.$refs.seeksRelationGraph.getInstance().dataUpdated();
    },
    // 工具栏打开设置菜单
    TOOL_MENU () {
      this.settingShow = true;
    },
    // 监听跟随开关
    NODE_FOLLOW (val) {
      console.log("监听跟随开关：", val);
    },
    // 顶部input输入时监听
    INPUT (val) {
      console.log(val.split('\n'));
      this.submitData = val.split('\n');
    },
    // 点击探索
    START_SEARCH (val) {
      this.initType = 'SEARCH';
      this.INIT_GRAPH(val);
    },
    // 清楚搜索
    RESET_SEARCH () {
      this.$router.push('/search');
    },
    // 详情框关闭
    DETAIL_CLOSE () {
      this.visibleShow = false;
    },
    // 点击查看标签
    VIEW_LABELS () {
      this.isShowNodeMenuPanel = false;
      let arr = [{
        str: this.currentNode.data.id,
        type: this.currentNode.data.type
      }];
      tagLabels(arr).then(res => {
        console.log(res);
        let setData = {
          edge: res.data.edge,
          vertex: res.data.vertex
        };
        // for (let i = 0; i < setData.edge.length; i++) {
        //   for (let j = 0; j < this.$refs.seeksRelationGraph.getGraphJsonData().links.length; j++) {
        //     if (setData.edge[i].from === this.$refs.seeksRelationGraph.getGraphJsonData().links[j].data.from && setData.edge[i].to === this.$refs.seeksRelationGraph.getGraphJsonData().links[j].data.to && setData.edge[i].label === this.$refs.seeksRelationGraph.getGraphJsonData().links[j].data.label) {
        //       console.log('发现重复线条，剔除：', setData.edge[i])
        //       setData.edge.splice(i, 1)
        //     }
        //   }
        // }
        this.$refs.seeksRelationGraph.appendJsonData(formatGraphData(setData), (seeksRGGraph) => {
          // 关系图渲染完成时会调用
          console.warn('渲染完成：', this.$refs.seeksRelationGraph.getGraphJsonData());
          let links = this.$refs.seeksRelationGraph.getInstance().getLinks();
          for (let i = 0; i < links.length; i++) {
            let arr = [];
            for (let j = 0; j < links[i].relations.length; j++) {
              let t = links[i].relations[j];
              if (arr.find((k) =>
                k.to === t.to && k.from === t.from && k.label === t.label
              )) {
                continue;
              }
              arr.push(t);
            }
            links[i].relations = arr;
          }
        });
      });
    },
    // 标签格式化
    LABEL_LV (item) {
      let type = 'info';
      if (
        item.blackList >= 1 &&
        item.blackList <= 100 &&
        item.whiteList !== 100
      ) {
        if (item.blackList >= 80) {
          type = "danger";
        } else {
          type = "warning";
        }
      }
      if (
        item.whiteList >= 1 &&
        item.whiteList <= 100 &&
        item.blackList === 0
      ) {
        if (item.whiteList === 100) {
          type = "success";
        } else {
          type = "";
        }
      }
      if (item.whiteList === 0 && item.blackList === 0) {
        type = "info";
      }
      return type;
    },
    // 删除标签
    DEL_LABELS (node) {
      console.log(node);
      this.$refs.seeksRelationGraph.removeNodeById(node.id);
    },
    // 删除节点
    DEL_NODE () {
      console.warn('删除节点：', this.currentNode);
      this.$refs.seeksRelationGraph.removeNodeById(this.currentNode.id);
      this.isShowNodeMenuPanel = false;
      this.$message.info('已删除节点（非物理删除）');
    },
    // 节点class判断
    NODE_CLASS (identity) {
      switch (identity) {
      case 'attacker':
        return 'attacker-node';
      case 'victim':
        return 'victim-node';
      case 'neutrality':
        return 'neutrality-node';
      default:
        return 'node';
      }
    },
    // 右键告警角色
    CAUTION () {
      this.isShowNodeMenuPanel = false;
      const model = this.currentNode.data;
      console.log('当前右键的Node：', model);
      this.cautionShow = true;
      if (model.identity === 'attacker') {
        this.cautionFrom.role = ['attacker'];
      }
      if (model.identity === 'victim') {
        this.cautionFrom.role = ['victim'];
      }
      this.cautionFrom.query_num = 5;
      this.cautionFrom.time_range = [moment().subtract(24, "hours").valueOf(), moment().valueOf()];
    },
    CAUTION_SUBMIT () {
      this.cautionFrom.ip_addr = this.currentNode.data.id;
      console.log(this.cautionFrom);
      judge_role({
        ip_addr: this.cautionFrom.ip_addr,
        role: this.cautionFrom.role,
        query_num: this.cautionFrom.query_num,
        time_range: {
          left: parseInt(this.cautionFrom.time_range[0] / 1000),
          right: parseInt(this.cautionFrom.time_range[1] / 1000)
        }
      }).then(res => {
        console.log(res);
        if (res.err === 0 && res.data.vertex.length > 0) {
          // 线条去重
          let setData = {
            edge: res.data.edge,
            vertex: res.data.vertex,
            label_vertex: res.data.label_vertex
          };
          this.$refs.seeksRelationGraph.appendJsonData(formatGraphData(setData), true, (seeksRGGraph) => {
            // 关系图渲染完成时会调用
            this.$message.success('新增节点');
            console.warn('渲染完成：', this.$refs.seeksRelationGraph.getGraphJsonData());
            let links = this.$refs.seeksRelationGraph.getInstance().getLinks();
            for (let i = 0; i < links.length; i++) {
              let arr = [];
              for (let j = 0; j < links[i].relations.length; j++) {
                let t = links[i].relations[j];
                if (arr.find((k) =>
                  k.to === t.to && k.from === t.from && k.label === t.label
                )) {
                  continue;
                }
                arr.push(t);
              }
              links[i].relations = arr;
            }
          });
          console.log(this.cautionFrom.role);
          if (this.cautionFrom.role[0] === 'attacker' && this.cautionFrom.role.length === 1) {
            this.currentNode.data.identity = 'attacker';
            this.currentNode.data.img = require('../../assets/graph/icon_attacker_wihte.svg');
          } else if (this.cautionFrom.role[0] === 'victim' && this.cautionFrom.role.length === 1) {
            this.currentNode.data.identity = 'victim';
            this.currentNode.data.img = require('../../assets/graph/icon_sufferer_wihte.svg');
          } else {
            this.currentNode.data.identity = 'neutrality';
            this.currentNode.data.img = require('../../assets/graph/faker.svg');
          }
          this.cautionShow = false;
        }
      });
    },
    // 刷新Dict
    GET_DICT () {
      request({
        url: "/dict",
        method: "GET",
      })
        .then((res) => {
          if (res.err == 0) {
            this.$store.commit("long/GET_Dictus", res.data);
          } else {
            console.log("err:" + res.err + ",msg:" + res.msg);
          }
        })
        .catch((err) => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.newgraph {
  width: 100%;
  height: 100%;
  position: relative;
  background: #eef0f5;

  .header {
    width: 100%;
    height: 32px;
    position: relative;
    top: 16px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    z-index: 2000;

    .tool {
      height: 100%;
      display: flex;
      align-items: center;

      .zoom {
        width: 108px;
        height: 100%;
        border-right: 2px solid #eef0f5;
        display: flex;
        justify-content: space-around;
        align-items: center;

        >img {
          width: 16px;
          height: 16px;
          cursor: pointer;
        }
      }

      .full {
        width: 32px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-right: 2px solid #eef0f5;

        >img {
          cursor: pointer;
        }
      }

      .layout {
        height: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-left: 10px;
        padding-right: 10px;
        border-right: 2px solid #eef0f5;

        >div:nth-of-type(1) {
          margin-left: 4px;
        }
      }
    }

    .search {
      display: flex;

      .full {
        width: 32px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10px;

        >img {
          cursor: pointer;
        }
      }

      ::v-deep .el-input__inner {
        width: 452px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #cecece;
        border-radius: 4px;
      }

      .el-button {
        background: #116ef9;
        border-radius: 4px;
        width: 88px;
        height: 32px;
        color: #ffffff;
        padding: 0;
        font-size: 14px;
        margin: 0 8px;
      }

      .el-button.clear {
        width: 60px;
        background: #ffffff;
        color: #2c2c35;
        border: 1px solid #cecece;
        border-radius: 4px;
        cursor: pointer;
      }

      .el-button.clear:hover {
        background-color: #b3d8ff;
      }

      .el-textarea {
        width: 290px;
      }

      ::v-deep .el-form-item__content {
        line-height: inherit;
      }
    }
  }

  .graph-box {
    height: calc(100vh - 50px);
    background: #eef0f5;
  }

  ::v-deep .rel-map {
    background: #eef0f5;
  }

  .type-box {
    position: absolute;
    left: 18px;
    bottom: 1px;
    z-index: 9999;

    &-cell {
      width: 105px;
      height: 20px;
      margin-bottom: 16px;
      font-size: 12px;
      color: #2c2c35;
      display: flex;
      align-items: center;
      cursor: context-menu;

      >div:nth-of-type(1) {
        width: 20px;
        height: 20px;
        margin-right: 6px;

        >img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

::v-deep .rel-node-shape-0:hover {
  box-shadow: none;
}

::v-deep .rel-node-shape-0 {
  width: auto;
  height: auto;
  padding: 0;
  background-color: transparent !important;
  #node{
    .sign{
      width:100%;
      height:100%;
      background-color: #8ABCFF;
      position: absolute;
      z-index:-1;
      border-radius: 50%;
      >div{
        width: 16px;
        height: 16px;
        border-radius: 50%;
        position: absolute;
        right:-8px;
        top:-5px;
        >img{
          width: 100%;
          height:100%;
        }
      }
    }
  }
  .node {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    border: 1px solid #767684;
    display: flex;
    justify-content: center;
    align-items: center;

    >img {
      width: 24px;
      height: 24px;
    }
  }

  .attacker-node {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #F91111;
    display: flex;
    justify-content: center;
    align-items: center;

    >img {
      width: 24px;
      height: 24px;
    }
  }

  .neutrality-node {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #8B11EB;
    display: flex;
    justify-content: center;
    align-items: center;

    >img {
      width: 24px;
      height: 24px;
    }
  }

  .victim-node {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #116ef9;
    display: flex;
    justify-content: center;
    align-items: center;

    >img {
      width: 24px;
      height: 24px;
    }
  }

  .folder-node {
    width: 42px;
    height: 32px;

    >img {
      width: 100%;
      height: 100%;
    }
  }

  .labels-node {
    max-width: 230px;
    min-width: 72px;
    min-height: 36px;
    background: #ffffff;
    border: 1px dashed #dee0e7;
    border-radius: 4px;
    padding: 8px;
    padding-bottom: 4px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    cursor: pointer;

    >section {
      height: 20px;
      line-height: 20px;
      background: #eef0f5;
      border-radius: 2px;
      font-size: 12px;
      color: #2c2c35;
      margin-right: 4px;
      margin-bottom: 4px;
      padding: 0 4px;
    }

    .del {
      width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 12px;
      background: #ffffff;
      border: 1px solid #dee0e7;
      display: none;
      position: absolute;
      right: -12px;
      top: -12px;

      >i {
        width: 100%;
        height: 100%;
        color: #000;
      }
    }

    .danger {
      background: #fce7e7;
      color: #a41818;
    }

    .warning {
      background: #f9eddf;
      color: #b76f1e;
    }

    .success {
      background: #e7f0fe;
      color: #1b428d;
    }
  }

  .labels-node:hover {
    .del {
      display: inline-block;
    }
  }

  .node-label {
    width: 80px;
    min-height: 20px;
    position: absolute;
    text-align: center;
    left: -10px;
    bottom: -14px;
    font-size: 12px;
    color: #2c2c35;
    overflow: hidden;
    /*隐藏*/
    white-space: nowrap;
    /*不换行*/
    text-overflow: ellipsis;
    /* 超出部分省略号 */
  }

  .node-label-hide {
    display: none;
  }
}

.clickmenu {
  width: 112px;
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  z-index: 9999;
  position: absolute;
  background: #ffffff;
  box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
  border-radius: 4px;

  >section {
    width: 100%;
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    cursor: pointer;
    font-size: 14px;
    color: 2c2c35;
  }

  >section:nth-of-type(3) {
    color: #f91111;
  }

  >section:hover {
    background: #e7f0fe;
  }
}

// 关联弹出框的样式
::v-deep .el-dialog__body {
  max-height: 650px;
  overflow-y: auto;
}

::v-deep .el-dialog__footer {
  border-top: 1px solid #f2f3f7;
  padding: 12px 24px;

  .el-button--primary {
    background-color: #116ef9;
    border-color: #116ef9;
    color: #FFFFFF;
  }
}

::v-deep .el-checkbox__label {
  font-size: 14px;
  color: #2c2c35;
}

::v-deep.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #116ef9;
  border-color: #116ef9;
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  color: #fff;
  background-color: #a0cfff;
  border-color: #a0cfff;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #116ef9;
  border-color: #116ef9;
}

::v-deep .el-checkbox-group {
  display: flex;
  flex-direction: column;
  font-size: 14px;

  .check-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    // .el-input-number__decrease,.el-input-number__increase{
    //   width: 20px;
    //   height: 22px;
    // }
    // .el-input--mini{
    //   width: 75px;
    //   height: 22px;
    //   .el-input--mini{
    //     height: 22px;
    //     line-height: 22px;
    //   }
    // }
  }

  >label {
    margin-bottom: 12px;
    color: #2c2c35;
  }

  .el-checkbox__label {
    color: #2c2c35;
  }
}

::v-deep .rela-dialog {
  .el-dialog {
    margin-top: 5vh !important;

    .el-dialog__footer {
      position: sticky;
      bottom: 0;
      z-index: 5000;
      background-color: #fff;

    }
  }
}

::v-deep .c-mini-toolbar>div:nth-of-type(1) {
  display: none;
}

.cautionDialog {

  .el-range-editor.el-input__inner {
    padding: 00 15px;
  }

  .el-checkbox-group {
    flex-direction: row;
  }
}

.caution-time {
  ::v-deep .el-button {
    border: 0;
  }
}
</style>
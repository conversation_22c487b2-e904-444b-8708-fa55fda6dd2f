<template>
  <div>
    <el-table
      :data="tableData"
      stripe
      style="overflow:auto"
      :default-sort = "{prop: 'session_cnt', order: 'descending'}"
      @sort-change="SORT_CHANGE"
    >
      <el-table-column
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="AppName"
        label="应用"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="dPort"
        label="端口"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="IPPro"
        label="IP-Pro"
        width="100"
        :formatter="IP_PRO"
      >
      </el-table-column>
      <el-table-column
        prop="ip_addr"
        label="IP地址"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="session_cnt"
        label="会话数"
        width="100"
        sortable="custom"
      >
      </el-table-column>
      <el-table-column
        prop="first_time"
        label="首次出现时间"
        :formatter="FIRST_DATE"
        width="105"
      >
      </el-table-column>
      <el-table-column
        prop="last_time"
        label="末次出现时间"
        :formatter="LAST_TIME"
        width="105"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import dayjs from 'dayjs'
export default {
  props: ['tableData'],
  methods: {
    SORT_CHANGE(field){
      let data ={
        order_field:field.prop,
        sort_order:''
      }
      if(field.prop === 'times') data.order_field = 'session_cnt'
      if(field.order === 'descending'){
        data.sort_order = 'desc'
      }else if (field.order === 'ascending'){
        data.sort_order = 'asc'
      }else{
        data.sort_order = 'desc'
      }
      this.$emit('SORT_CHANGE_GETDATA',data)
    },
    IP_PRO (row) {
      return this.$store.state.long.Dict.protocol_type[row.IPPro]?.protocol_type || 'N/A'
    },
    FIRST_DATE (row) {
      return dayjs(row.first_time * 1000).format("YYYY-MM-DD HH:mm:ss")
    },
    LAST_TIME (row) {
      return dayjs(row.last_time * 1000).format("YYYY-MM-DD HH:mm:ss")
    },
    SORT_CHANGE(filed){
      console.log(filed)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
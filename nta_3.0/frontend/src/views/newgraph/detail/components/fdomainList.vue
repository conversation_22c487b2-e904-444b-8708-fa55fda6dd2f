<template>
  <div>
    <el-table
      :data="tableData"
      stripe
      style="overflow:auto"
    >
      <el-table-column
        type="index"
        width="50"
      >
      </el-table-column>
      <el-table-column
        prop="fdomain_addr"
        label="锚域名"
      >
      </el-table-column>
   
    </el-table>
  </div>
</template>

<script>
export default {
  props: ['tableData'],
  methods: {
   
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table th > .cell {
  margin-top: 0;
}
</style>
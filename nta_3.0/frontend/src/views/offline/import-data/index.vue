<template>
  <el-drawer
    :title="headerTitle"
    :visible.sync="isShow"
    custom-class="drawer-detail"
    destroy-on-close
    size="480px"
    :wrapper-closable="false"
    @close="handleClose"
  >
    <div class="drawer-detail__content">
      <TabSecond
        v-model="activeIndex"
        :tab-second="tSecond"
        @tabClick="tabClick"
      />
      <el-form ref="pcapFormRef" :model="form" label-position="top">
        <Pcap
          ref="pcapRef"
          :lx-data="lxList[0]"
          :pcap-form="form"
          :tab-second-index="activeIndex"
        />
      </el-form>
    </div>
    <div class="drawer-detail__footer">
      <el-button @click="isShow = false">取消</el-button>
      <el-button type="primary" @click="handleImport"> 导入 </el-button>
    </div>
    <Confirm v-model="loading" />
  </el-drawer>
</template>

<script>
import mixins from "@/mixins";
import IndexMixin from "./mixins";
import TabSecond from "./component/TabSecond";
import Pcap from "./component/Pcap";
import Confirm from "./component/Confirm";
import api from "@/api/offline";
export default {
  name: "ImportData",
  components: {
    TabSecond,
    Pcap,
    Confirm,
  },
  mixins: [mixins, IndexMixin],
  props: {
    id:{
      type:[String,Number],
      default:''
    }
  },
  data() {
    return {
      form: {
        "task_name":"",
        "task_id": '', //任务id
        "batch_type": 1, //批次类型（1-服务器数据；2-数据上传；）
        "fullflow_state": 'ON', // 全流量留存
        "flowlog_state": 'ON', // 元数据留存
        "batch_description": "", //数据描述
        "file_path_list": [],
        "upload_mode":'服务器文件', // 服务器文件类型
        file:""
      },
      loading: false,
      activeIndex: 0,
    };
  },
  computed: {
    formRef() {
      return this.tabFirstIndex ? "certFormRef" : "pcapFormRef";
    }
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.id && this.getDetail();
        } 
      },
      immediate: true,
    }
  },
  methods: {
    tabClick(item) {
      this.form.batch_type=item.key;
      this.form.file_path_list = [];
      this.form.upload_mode = '服务器文件';
    },
    // pcap导入
    async handleImport() {
      try {
        this.loading = true;
        const params = {
          batch_description:this.form.batch_description,
          task_id: this.id,
          batch_type:this.form.batch_type,
          fullflow_state:this.form.fullflow_state,
          flowlog_state:this.form.flowlog_state,
        };
        if(!this.activeIndex){
          if(this.form.upload_mode==='服务器文件'){
            this.form.file_path_list= this.$refs.pcapRef.getCheckedKeys();
          }
          if (!this.form.file_path_list.length) {
            this.$message.error("请添加服务器文件路径");
            this.loading = false;
            return;
          }
          params.file_path_list=this.form.file_path_list.map(item=>{
            return {
              server_path:item.trim(),
              local_path:item.trim()
            };
          });
        }else{
          params.file_path_list=this.form.file_path_list.map(item=>{
            return {
              server_path:item.file_path,
              local_path:item.file_name
            };
          });
          if (!params.file_path_list.length) {
            this.$message.error("请添加pcap文件");
            this.loading = false;
            return;
          }
        }
        await api.offlineBatchAdd(params);
        this.isShow = false;
        this.$message.success("操作成功");
        this.loading = false;
        this.$emit("refresh");
      } catch (error) {
        this.loading = false;
      }
    },
    handleClose() {
      this.activeIndex = 0;
      this.form.file_path_list = [];
      this.form.upload_mode = "服务器文件";
      this.form.batch_type = 1;
      this.form.fullflow_state = "ON";
      this.form.flowlog_state = "ON";
      this.form.batch_description = "";
      this.isShow = false;
    },
    async getDetail() {
      const { data } = await api.getDetail(this.id);
      this.form.task_name = data.task_name;
    },
  },
};
</script>

<style lang="scss" scoped>
.drawer-detail__content {
  .tab-first {
    display: flex;
    .tab-first-item {
      margin-right: 32px;
      margin-bottom: 16px;
      cursor: pointer;
    }
    .active {
      color: #116ef9;
      position: relative;
      &::before {
        position: absolute;
        left: 0;
        top: -16px;
        content: "";
        display: inline-block;
        width: 24px;
        height: 2px;
        background: #116ef9;
        z-index: 9;
      }
    }
  }
}
</style>

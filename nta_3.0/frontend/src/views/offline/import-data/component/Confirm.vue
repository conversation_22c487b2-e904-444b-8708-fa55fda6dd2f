<template>
  <el-dialog
    append-to-body
    :show-close="false"
    center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    top="35vh"
    :visible.sync="isShow"
    width="290px"
  >
    <i class="el-icon-loading"></i>{{ message }}
  </el-dialog>
</template>

<script>
import mixins from "@/mixins";

export default {
  name: "Confirm",
  mixins: [mixins],
  props: {
    message: {
      type: String,
      default: "数据导入中，请耐心等待...",
    },
  },
};
</script>

<style lang="scss" scoped>
.el-icon-loading {
  color: #116ef9;
  margin: 0 8px;
}
::v-deep {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    text-align: center;
    padding: 25px;
  }
}
</style>
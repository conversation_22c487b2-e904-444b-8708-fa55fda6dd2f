<template>
  <div>
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        type="index"
        width="60"
      >
      </el-table-column>
      <el-table-column
        prop="batch_id"
        label="批次号"
        width="150"
      >
      </el-table-column>
      <el-table-column
        prop="batch_type"
        label="导入类型"
        width="150"
      >
        <template #default="{row}">
          {{ fn.batchType[row.batch_type] }}
        </template>
      </el-table-column>
      <el-table-column
        prop="file_path"
        label="文件来源"
      >
        <template #default="{row}">
          <div v-for="(item,index) in row.file_path&&row.file_path.split(',')" :key="`${item}-${index}`" style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{ item }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="pcap_num"
        label="pcap包数量"
      >
      </el-table-column>
      <el-table-column
        prop="start_time"
        label="开始时间"
      >
      </el-table-column>
      <el-table-column
        prop="end_time"
        label="结束时间"
      >
      </el-table-column>
      <el-table-column
        prop="batch_status"
        width="150"
        label="状态"
      >
        <template #default="{row}">
          <span :style="{color:fn.statesColors[row.batch_status]}"> {{ fn.states[row.batch_status] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        width="80"
        label="操作"
      >
        <template #default="{row}">
          <el-button v-if="row.batch_status===2||row.batch_status===1" type="text" @click="stop(row.batch_id)">取消</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="page.current_page"
      :page-sizes="sizes"
      :page-size="page.page_size"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>

<script>
import pagiNation from '@/mixins/pagiNation';
import fn from '../js';
import api from '@/api/offline';
export default {
  name:'Datamanagement',
  mixins:[pagiNation],
  props: {
    offlineId: {
      type: [Number,String],
      default: ''
    },
  },
  data() {
    return {
      tableLoading: false,
      fn,
      tableData: []
    };
  },
  watch: {
    offlineId:{
      handler(val){
        val&& this.getList();
      },
      immediate:true
    }
  },
  methods: {
    async  getList() {
      try {
        this.tableLoading = true;
        const params = {  ...this.page,task_id:this.offlineId };
        const res = await api.offlineBatchPage(params);
        this.total = res.data.total;
        this.tableData = res.data.data;
        this.tableLoading = false;
      } catch (error) {
        this.tableLoading = false;
      }
    },
    stop(batch_id){
      this.$confirm('取消导入将会删除已导入的数据，请确认是否取消导入?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await api.offlineBatchCancel({batch_id});
        this.$message({
          type: 'success',
          message: '取消导入成功!'
        });
        this.$emit('handleRefresh');
      }).catch((e) => {
        console.log(e);
      });
    }
  },
};
</script>

<style lang="scss" scoped>

</style>
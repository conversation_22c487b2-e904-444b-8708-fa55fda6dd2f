<template>
  <div class="task">
    <div class="top">
      <div class="top-left">
        <div class="name">{{ taskList.task_name }}</div>
        <div v-if="taskList.task_description" class="desc">{{ taskList.task_description }}</div>
      </div>
      <div class="top-right">
        <el-button type="text" icon="el-icon-refresh" @click="handleRefresh">刷新</el-button>
        <el-button type="text" icon="el-icon-upload2" @click="handleExport">数据导入</el-button>
        <el-button type="text" @click="update">配置</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-left">
        <div class="content-left-item">
          <div class="label">正在导入批次号</div>
          <div class="value">
            <div
              v-for="(item, index) in taskList.batch_progress_list"
              :key="index"
              class="value-item"
            >
              <div class="key">{{ item.batch_id }}</div>
              <div :class="['process',item.batch_progress?'':'acitve']">{{ (item.batch_progress*100).toFixed(0) }}%</div>
            </div>
          </div>
        </div>
        <div class="content-left-item" style="margin-top: 8px;">
          <div class="label">最近导入完成时间</div>
          <div class="value">{{ taskList.last_import_time || empty }}</div>
        </div>
      </div>
      <div class="content-right">
        <div class="content-right-item">
          <div class="label">数据总量：</div>
          <div class="value">{{ taskList.data_total || empty }}</div>
        </div>

        <div class="content-right-item">
          <div class="label">白名单过滤量：</div>
          <div class="value">{{ taskList.whitelist_filter_total || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">创建时间：</div>
          <div class="value">{{ taskList.create_time || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">会话总量：</div>
          <div class="value">{{ taskList.session_total || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">过滤数据量：</div>
          <div class="value">{{ taskList.filter_data_total || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">更新时间：</div>
          <div class="value">{{ taskList.update_time || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">告警总量：</div>
          <div class="value">{{ taskList.alarm_total || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">采集规则命中数据量：</div>
          <div class="value">{{ taskList.rule_hits_data_total || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">高危目标：</div>
          <div class="value">{{ taskList.high_target_total || empty }}</div>
        </div>
        <div class="content-right-item">
          <div class="label">批次数量：</div>
          <div class="value">{{ taskList.batch_num || empty }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import api from "@/api/offline";
export default {
  name: "TaskInfo",
  props: {
    id: {
      type: [String,Number],
      default: "",
    },
  },
  data() {
    return {
      taskList: {
        task_id: "", //任务id
        task_name: "", //任务名称
        task_description: "", //任务描述
        create_time: "", //创建时间
        update_time: "", //更新时间
        data_total: 0, //数据总量
        session_total: 0, //会话总量
        alarm_total: 0, //告警总量
        high_target_total: 0, //高危目标
        filter_data_total: 0, //过滤数据量
        rule_hits_data_total: 0, //采集规则命中数据量
        whitelist_filter_total: 0, //白名单过滤量
        batch_num: 0, //批次数量
        batch_progress_list: [], //正在导入批次列表
      },
      empty:'-',
    };
  },
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  methods: {
    update() {
      this.$emit("update", { id: this.taskList.task_id });
    },
    async getDetail() {
      try {
        this.$parent.isLoading=true;
        const { data } = await api.getDetail(this.id);
        this.taskList = data;
        this.taskList.data_total=data.data_total&&this.$unitValue(data.data_total);
        this.taskList.filter_data_total=data.filter_data_total&&this.$unitValue(data.filter_data_total);
        this.taskList.rule_hits_data_total=data.rule_hits_data_total&&this.$unitValue(data.rule_hits_data_total);
        this.taskList.whitelist_filter_total=data.whitelist_filter_total&&this.$unitValue(data.whitelist_filter_total);
        this.$parent.updateData=data?.task_id;
        this.$parent.isLoading=false;
      } catch (error) {
        this.$parent.isLoading=false;
      }
     
    },
    // 数据导入
    handleExport() {
      this.$emit('handleExport');
    },
    // 刷新
    handleRefresh(){
      this.$emit('handleRefresh');
    }
  },
};
</script>

<style lang="scss" scoped>
.task {
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    margin-bottom: 14px;
    &-left {
      display: flex;
      align-items: center;
      .name {
        font-weight: 600;
      }
      .desc {
        background: #f2f3f7;
        padding: 2px 8px;
        border-radius: 16px;
        margin-left: 10px;
      }
    }
  }

  .content {
    display: flex;

    &-left {
      width: 300px;

      &-item {
        display: flex;
        flex-direction: column;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .label,
        .value {
          height: 24px;
        }

        .value {
          display: flex;

          &-item {
            display: flex;
            height: 20px;
            line-height: 20px;
            align-items: center;

            &:not(:first-child) {
              margin-left: 12px;
            }

            .key {
              color: #2c2c35;
              font-weight: bold;
            }

            .process {
              background: #E7F0FE;
              color: #116EF9;
              border-radius: 2px;
              text-align: center;
              padding: 0 4px;
              margin-left: 6px;
              font-weight: bold;
            }
            .acitve{
              background: #F9EDDF;
              color: #B76F1E;
            }
          }
        }
      }
    }

    &-right {
      flex: 1;
      display: flex;
      flex-wrap: wrap;

      &-item {
        width: 33.3%;
        display: flex;
        height: 24px;
      }
    }

    .label {
      color: #767684;
    }

    .value {
      color: #2c2c35;
    }
  }
  .flex{
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
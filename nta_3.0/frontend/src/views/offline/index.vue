<template>
  <div class="offline">
    <div class="btns">
      <el-button type="primary" icon="el-icon-plus" @click="create">
        创建任务
      </el-button>
      <el-button plain @click="switchTask">任务管理</el-button>
    </div>
    <Empty v-if="isEmpty" class="content" />
    <div v-else v-loading="isLoading" class="content">
      <div class="content-info pd">
        <TaskInfo
          :id="updateData"
          ref="taskInfoRef"
          @handleRefresh="refresh"
          @update="update"
          @handleExport="handleExport"
        />
      </div>
      <div class="class-tab">
        <el-tabs v-model="activeTab" @tab-click="handleClick">
          <el-tab-pane
            v-for="(item, index) in Object.keys(tabs)"
            :key="index"
            :label="tabs[item]"
            :name="item"
          ></el-tab-pane>
        </el-tabs>
        <div style="padding: 0 16px 16px">
          <component
            :is="activeTab"
            ref="datamanagementRef"
            :is-off-line="true"
            :offline-id="updateData"
            @handleRefresh="refresh"
          ></component>
        </div>
      </div>
    </div>
    <TaskAdd
      :id="updateData"
      v-model="taskAddVisible"
      :title="title"
      @creatTask="creatTask"
    />
    <TaskSwitch
      v-model="visibleSwitch"
      header-title="任务管理"
      @handleSelectData="handleSelectData"
      @getFirstList="init"
    />
    <ImportData
      :id="updateData"
      v-model="visibleImport"
      header-title="数据导入"
      @refresh="refresh"
    />
  </div>
</template>

<script>
// import MissionPosture from "@/views/WorkBench/components/IntranetList";
import Datamanagement from "./component/Datamanagement";
import Filterrules from "@/views/WorkBench/components/FiltrateRule";
import Characteristicrules from "@/views/WorkBench/components/CollectRule";

import TaskInfo from "./component/TaskInfo";
import TaskSwitch from "./component/TaskSwitch";
import ImportData from "./import-data";
import Empty from "@/components/Empty";

import api from "@/api/offline";
export default {
  name: "Offline",
  components: {
    // MissionPosture,
    Datamanagement,
    Filterrules,
    Characteristicrules,
    TaskInfo,
    TaskAdd: () => import("./component/TaskAdd"),
    ImportData,
    TaskSwitch,
    Empty,
  },
  data() {
    return {
      tabs: {
        // MissionPosture: "任务态势",
        Datamanagement: "数据管理",
        Filterrules: "过滤规则",
        Characteristicrules: "采集规则",
      },
      activeTab: "Datamanagement",
      taskAddVisible: false,
      title: "创建任务",
      visibleSwitch: false,
      updateData: "",
      visibleImport: false,
      isEmpty: true,
      isLoading: false,
    };
  },
  created() {
    this.getLast();
  },
  methods: {
    // 查询当前任务
    async getLast() {
      const { data } = await api.getLast();
      if (data?.task_name) {
        this.isEmpty = false;
        this.updateData = data.task_id;
      } else {
        this.isEmpty = true;
      }
    },
    handleClick(tab) {
      this.activeTab = tab.name;
    },
    // 新增
    create() {
      this.title = "创建任务";
      this.taskAddVisible = true;
    },
    // 编辑
    update({ id }) {
      this.title = "任务配置";
      this.updateData = id;
      this.taskAddVisible = true;
    },
    creatTask(task_id) {
      if (task_id) {
        this.$refs.taskInfoRef.getDetail();
      } else {
        this.getLast();
      }
    },
    // 切换任务
    switchTask() {
      this.visibleSwitch = true;
    },
    handleSelectData(row) {
      this.updateData = row.task_id;
      this.$message.success("切换任务成功");
    },
    // 初始化
    async init() {
      this.getLast();
    },
    // 导入
    handleExport() {
      this.visibleImport = true;
      this.updateData = this.$refs.taskInfoRef.taskList?.task_id;
    },
    // 更新
    refresh() {
      this.$refs.taskInfoRef.getDetail();
      if (this.activeTab === "Datamanagement") {
        this.$refs.datamanagementRef.getList();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.pd {
  padding: 4px 16px 16px;
}
.offline {
  height: 100%;
  .btns {
    height: 44px;
    line-height: 30px;
  }
  .content {
    background: #fff;
    height: calc(100% - 60px);
    &-info {
      height: 154px;
      // border-bottom: 1px solid red;
    }
    ::v-deep .el-tabs__nav-wrap {
      padding-left: 16px;
    }
    .intranet-list {
      margin-top: 0;
    }
    .class-tab {
      background: #fff;
    }
  }
}
</style>
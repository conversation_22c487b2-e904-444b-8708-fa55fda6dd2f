export const targetType = {
  1: "IP",
  2: "AllDomain",
  3: "Finger",
  4: "Cert",
  5: "dPort",
  6: "Labels",
  7: "AppId",
  8: "SessionId",
  9: "Domain",
  10: "INDEX_DNS:Domain",
};
export const targetTypeValue = {
  IP: "IP",
  AllDomain: "域名",
  Finger: "指纹",
  Cert: "证书",
  dPort: "目的IP端口",
  Labels: "标签",
  AppId: "应用",
  SessionId: "会话",
  Domain: "域名",
  "INDEX_DNS:Domain": "DNS域名",
};
export const targetTypeNum = [
  { value: 8, name: "SessionId", zwname: "会话" },
  { value: 1, name: "IP", zwname: "IP" },
  { value: 2, name: "AllDomain", zwname: "域名" },
  { value: 3, name: "Finger", zwname: "指纹" },
  { value: 4, name: "Cert", zwname: "证书" },
  { value: 6, name: "Labels", zwname: "标签" },
  { value: 9, name: "Domain", zwname: "域名" },
  { value: 10, name: "INDEX_DNS:Domain", zwname: "DNS域名" },
];
export const esoptions =[
  {
    value: "huihua",
    label: "会话列表",
    children: [],
  },
  {
    value: "SSL",
    label: "元数据_SSL",
    children: [],
  },
  {
    value: "HTTP",
    label: "元数据_HTTP",
    children: [],
  },
  {
    value: "DNS",
    label: "元数据_DNS",
    children: [],
  },
];
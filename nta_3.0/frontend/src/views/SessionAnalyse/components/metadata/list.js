// 元数据RLogin
export const checkedHeadersRLogin= [
  "窗口行数",
  "终端类型",
  "终端速率",
  "客户端用户名",
  "服务端用户名",
  "密码"
];
export const tableHeaderRLogin= [
  "服务端IP",
  "窗口行数",
  "终端类型",
  "终端速率",
  "客户端用户名",
  "服务端用户名",
  "密码"
];
// 元数据Telnet
export const checkedHeadersTelnet= [
  "终端宽度",
  "终端高度",
  "终端类型",
  "终端速率",
  "用户名",
  "密码",
];
export const tableHeaderTelnet= [
  "服务端P",
  "终端宽度",
  "终端高度",
  "终端类型",
  "终端速率",
  "用户名",
  "密码",
];
  // 元数据Ssh
export const checkedHeadersSsh= [
  "客户端.版本",
  "客户端.cookie",
  "客户端.秘钥交换算法",
  "客户端.服务器主机密钥算法",
  "服务端.版本",
  "服务端.cookie",
  "服务端.秘钥交换算法",
  "服务端.服务器主机密钥算法",
];
export const tableHeaderSsh= [
  "客户端.版本",
  "客户端.cookie",
  "客户端.秘钥交换算法",
  "客户端.服务器主机密钥算法",
  "客户端.C2S加密算法",
  "客户端.S2C加密算法",
  "客户端.C2S认证码算法",
  "客户端.S2C认证码算法",
  "客户端.C2S压缩算法",
  "客户端.S2C压缩算法",
  "服务端.版本",
  "服务端.cookie",
  "服务端.秘钥交换算法",
  "服务端.服务器主机密钥算法",
  "服务端.C2S加密算法",
  "服务端.S2C加密算法",
  "服务端.C2S认证码算法",
  "服务端.S2C认证码算法",
  "服务端.C2S压缩算法",
  "服务端.S2C压缩算法",
];
  // 元数据Rdp
export const checkedHeadersRdp= [
  "服务端.配置选项",
  "服务端.安全协议选用",
  "客户端.配置选项",
  "客户端.安全协议支持"
];
export const tableHeaderRdp= [
  "服务端IP",
  "服务端.配置选项",
  "服务端.安全协议选用",
  "客户端.配置选项",
  "客户端.安全协议支持"
];
  // 元数据Vnc
export const checkedHeadersVnc= [
  "服务端.协议版本",
  "客户端.协议版本",
  "服务端.安全类型支持",
  "客户端.安全类型选用",
  "编码类型",
];
export const tableHeaderVnc= [
  "服务端IP",
  "服务端.协议版本",
  "客户端.协议版本",
  "服务端.安全类型支持",
  "客户端.安全类型选用",
  "桌面名称",
  "认证请求",
  "认证回复",
  "服务端认证结果",
  "编码类型",
  "共享桌面标志",
  "像素格式.每像素比特数",
  "像素格式.位深",
  "像素格式.大端标志",
  "像素格式.真彩标志",
];
  // 元数据Xdmcp
export const checkedHeadersXdmcp= [
  "协议版本",
  "连接类型",
  "连接地址",
  "连接状态",
  "客户端.授权类型支持",
  "服务端.授权类型选用",
];
export const tableHeaderXdmcp= [
  "服务端IP",
  "协议版本",
  "服务端.主机名",
  "连接类型",
  "连接地址",
  "连接状态",
  "XDMCP会话ID",
  "客户端.授权类型支持",
  "服务端.授权类型选用",
  "制造商显示器ID",
  "显示编号",
  "显示器类别"
];
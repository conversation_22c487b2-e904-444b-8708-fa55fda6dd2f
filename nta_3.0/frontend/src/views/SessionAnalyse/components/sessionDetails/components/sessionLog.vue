<template>
  <div class="sessionlog">
    <div v-if="activeDown === 'table'" class="log-value">
      <el-select v-model="logValue" placeholder="请选择">
        <el-option label="通用" :value="'1'"> </el-option>
        <el-option label="客户端/服务端" :value="'2'"> </el-option>
      </el-select>
    </div>

    <el-tabs v-model="activeDown" type="card">
      <el-tab-pane label="table" name="table">
        <section v-if="logValue === '1'" class="general">
          <div
            class="cell"
            style="
              height: 38px;
              color: #9999a1;
              background: #f7f8fa;
              margin-top: 10px;
            "
          >
            <div class="label">
              <span>名称</span>
            </div>
            <div class="content">通用</div>
          </div>
          <div v-for="(item, index) of genralList" :key="index" class="cell">
            <div class="label">
              <span>{{ item.label }}</span>
              <span>{{ item.key }}</span>
            </div>
            <div class="content">
              <div
                v-if="
                  item.key === 'PacketDistDiurList' ||
                    item.key === 'PacketDistDiurList'
                "
              >
                <div v-for="(x, index) in item.val" :key="index">{{ x }}</div>
              </div>
              <template v-else>
                {{ FMT_GENRA(item) || "-" }}
              </template>
            </div>
          </div>
        </section>
        <section v-else class="general">
          <div
            class="cell"
            style="
              height: 38px;
              color: #9999a1;
              background: #f7f8fa;
              margin-top: 10px;
            "
          >
            <div class="label">
              <span>名称</span>
            </div>
            <div class="content" style="flex: 164">服务端</div>
            <div class="content" style="flex: 164">客户端</div>
          </div>
          <div v-for="(item, index) of csList" :key="index" class="cell">
            <div class="label">
              <span>{{ FMT_CSLIST_LABEL(item.type) }}</span>
              <span>{{ item.type }}</span>
            </div>
            <div class="content" style="flex: 164">
              {{ item.server || "-" }}
            </div>
            <div class="content" style="flex: 164">
              {{ item.client || "-" }}
            </div>
          </div>
        </section>
      </el-tab-pane>
      <el-tab-pane label="json" name="json">
        <div>
          <el-card shadow="never" style="margin-top: 20px">
            <json-viewer :copyable="true" :value="pack_code" :expand-depth="5">
              <template slot="copy"> 复制 </template>
            </json-viewer>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import tagJAON from "@/assets/esMsgFiledExplain.json";
import { parseTime } from "@/utils";
export default {
  name: "SessionLog",
  props: {
    // eslint-disable-next-line vue/prop-name-casing
    session_id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      activeDown: "table",
      pack_code: {},
      data: [], // table 数据
      data1: [],
      proList: [],
      pkt1: [],
      timer: null,
      logValue: "1",
      csList: [],
    };
  },
  computed: {
    esoptions() {
      return this.$store.state.app.esoptions;
    },
    sessionLog() {
      return this.$store.state.conversational.sessionLog;
    },
    genralList() {
      return [
        {
          label: "应用ID",
          key: "AppId",
          val: this.pack_code?.AppId || "-",
        },
        {
          label: "应用",
          key: "AppName",
          val: this.pack_code?.AppName || "-",
        },
        {
          label: "长度分布",
          key: "BlockCipher",
          val: this.pack_code?.PktLenDist || "-",
        },
        {
          label: "入库时间",
          key: "CreateTime",
          val: parseTime(this.pack_code?.CreateTime) || "-",
        },
        {
          label: "目标IP属性位图",
          key: "DioSign",
          val: this.pack_code?.SbytesDiviDbytes || "-",
        },
        {
          label: "源包长分布",
          key: "PacketDistDiurList",
          val: this.FMT_DIST_LEN(this.pack_code?.sDistLen,'sDistLen') || "-",
        },
        {
          label: "目的包长分布",
          key: "PacketDistDiurList",
          val: this.FMT_DIST_LEN(this.pack_code?.dDistLen,'dDistLen') || "-",
        },
        {
          label: "协议层数",
          key: "ProListNum",
          val: this.pack_code?.ProListNum || "-",
        },
        {
          label: "代理IP标志",
          key: "ProxyIPInfor",
          val:
            this.pack_code?.ProxyIPInfor === 0
              ? "0"
              : this.pack_code?.ProxyIPInfor,
        },
        {
          label: "目的包数差异",
          key: "SbytesDiviDbytes",
          val: this.pack_code?.SbytesDiviDbytes || "-",
        },
        {
          label: "会话ID",
          key: "SessionId",
          val: this.pack_code?.SessionId || "-",
        },
        {
          label: "起始时间(纳秒)",
          key: "StartNSec",
          val: this.pack_code?.StartNSec || "-",
        },
        {
          label: "起始时间",
          key: "StartTime",
          val: parseTime(this.pack_code?.StartTime) || "-",
        },
        {
          label: "消息状态",
          key: "State",
          val: this.pack_code?.State || "-",
        },
        {
          label: "SYN包数",
          key: "SynNum",
          val: this.pack_code?.SynNum || "-",
        },
        {
          label: "SYN包序列号",
          key: "SynSeqList",
          val: this.pack_code?.SynSeqList || "-",
        },
        {
          label: "TCP聚合信息",
          key: "TCPGather",
          val: "-",
        },
        {
          label: "聚合字节数",
          key: "TCPGather.Bytes",
          val: this.pack_code?.TCPGather?.[0]?.Bytes || "-",
        },{
          label: "聚合包数",
          key: "TCPGather.PacketNum",
          val: this.pack_code?.TCPGather?.[0]?.PacketNum || "-",
        },{
          label: "PSH次数",
          key: "TCPGather.PSHNum",
          val: this.pack_code?.TCPGather?.[0]?.PSHNum || "-",
        },{
          label: "ACK次数",
          key: "TCPGather.ACK",
          val: this.pack_code?.TCPGather?.[0]?.ACK || "-",
        },
        {
          label: "最小序列号",
          key: "TCPGather.MinSeq",
          val: this.pack_code?.TCPGather?.[0]?.MinSeq || "-",
        },
        {
          label: "最大序列号",
          key: "TCPGather.MaxSeq",
          val: this.pack_code?.TCPGather?.[0]?.MaxSeq || "-",
        },
        {
          label: "批次ID",
          key: "TaskInfo.BatchNum",
          val: this.pack_code?.BatchNum || "-",
        },
        {
          label: "SSL字段",
          key: "SSL",
          val: "-",
        },
        {
          label: "ClientHello密码套件",
          key: "SSL.CH_Ciphersuit",
          val: this.pack_code?.SSL?.[0]?.CH_Ciphersuit,
        },
        {
          label: "ClientHello密码套件数量",
          key: "SSL.CH_CiphersuitNum",
          val: this.pack_code?.SSL?.[0]?.CH_CiphersuitNum,
        },
        {
          label: "服务器名",
          key: "SSL.CH_ServerName",
          val: this.pack_code?.SSL?.[0]?.CH_ServerName,
        },
        {
          label: "应用协议类型",
          key: "SSL.CH_ALPN",
          val: this.pack_code?.SSL?.[0]?.CH_ALPN,
        },
        {
          label: "客户端证书Hash列表",
          key: "SSL.sCertHash",
          val: this.pack_code?.SSL?.[0]?.sCertHash,
        },
        {
          label: "服务端证书Hash",
          key: "SSL.dCertHash",
          val: this.pack_code?.SSL?.[0]?.dCertHash,
        },
        {
          label: "DNS 字段",
          key: "DNS",
          val:  "-",
        },
        {
          label: "本次询问域名",
          key: "DNS.Domain",
          val: this.pack_code?.DNS?.[0]?.Domain || "-",
        },
        {
          label: "答复IP地址",
          key: "DNS.DomainIp",
          val: this.pack_code?.DNS?.[0]?.DomainIp || "-",
        },
        {
          label: " HTTP 字段",
          key: "HTTP",
          val:  "-",
        },
        {
          label: "URL",
          key: "HTTP.Url",
          val: this.pack_code?.HTTP?.[0]?.Url || "-",
        },
        {
          label: "ACT",
          key: "HTTP.Act",
          val: this.pack_code?.HTTP?.[0]?.Act || "-",
        },
        {
          label: "主站",
          key: "HTTP.Host",
          val: this.pack_code?.HTTP?.[0]?.Host || "-",
        },
        {
          label: "UA信息",
          key: "HTTP.User-Agent",
          val: this.pack_code?.HTTP?.[0]?.['User-Agent'] || "-",
        },
        {
          label: "回应类型",
          key: "HTTP.Response",
          val: this.pack_code?.HTTP?.[0]?.Response || "-",
        }
      ];
    },
  },
  watch: {
    session_id: {
      deep: true,
      immediate: true,
      handler(val) {
        this.timer = setTimeout(() => {
          this.initSessionLog();
        }, 1000);
      },
    },
  },
  beforeDestroy() {
    clearTimeout(this.timer);
  },
  methods: {
    edit(scope) {
      let edit = {
        amout: String(scope.row.amout),
        val: ["huihua", String(scope.row.editval)],
      };
      this.$store.commit("conversational/jsoneditData", edit);
    },
    initSessionLog() {
      this.data = [];
      if (this.sessionLog != undefined) {
        this.pack_code = this.sessionLog.common;
        this.csList = this.sessionLog.connect_info;
        let data = [];
        data.push(this.sessionLog);
        data.forEach((index, val) => {
          this.data1 = index;
        });
        // 知识库
        let tagDict1 = tagJAON.ssl;
        let tagDict2 = tagJAON.conn;
        // let tagDict3 =
        //   this.tagJAON.cert;
        let tagDict4 = tagJAON.dns;
        let tagDict5 = tagJAON.http;
        let tagDict = Object.assign(
          tagDict1,
          tagDict2,
          // tagDict3,
          tagDict4,
          tagDict5
        );
        let esoption = this.esoptions ? this.esoptions[0].children : "";
        for (let k in this.data1) {
          let item = {};
          item.edit = false;
          let Task = this.data1.TaskInfo;
          let Tcp = this.data1.TCPGather;
          let DNS = this.data1.DNS;
          if (k == "pkt") {
            item.amout = " ";
            item.name = " ";
          } else {
            item.amout = this.data1[k];
            item.name = k;
            if (k == "ProList") {
              item.amout = " ";
              item.name = " ";
            }
            if (k == "RuleInfor") {
              item.amout = " ";
              item.name = " ";
            }
            if (JSON.stringify(this.data1[k]) == "{}") {
              item.amout = "暂无数据";
            }
            if (Array.isArray(this.data1[k])) {
              item.amout = this.data1[k].join("--");
            }
            if (k == "sTCPFingerInfor") {
              item.amout = " ";
            }
            if (k == "TaskInfo") {
              item.amout = " ";
              item.name = " ";
            }
            if (k == "TCPGather") {
              item.amout = " ";
              // item.name = " ";
            }
            if (k == "DNS") {
              item.amout = " ";
              // item.name = " ";
            }
          }
          // 循环中文name
          for (let l in tagDict) {
            if (l == k) {
              if (k == "pkt") {
                item.ChineseName = " ";
              } else {
                item.ChineseName = tagDict[l].Name;
              }
            }
          }

          for (let P in tagDict1) {
            if (P == k) {
              item.ChineseName = tagDict1[P].Name;
            }
          }
          for (let et in esoption) {
            if (esoption[et].label === item.ChineseName) {
              item.edit = true;
              item.editval = esoption[et].value;
            }
          }
          data.push(item);
          // 当 k 等于 pkt 时，获取pkt 中的对象值
          if (k == "pkt") {
            let pktList = this.data1[k];
            this.pkt1 = pktList.Infor;
            for (let c in pktList) {
              let item1 = {};
              item1.edit = false;
              // 替换字符串中_
              let c1 = "pkt." + c.replace("_", ".");
              item1.name = c1;
              item1.amout = pktList[c];
              // for in 循环中文name
              for (let l in tagDict) {
                if (l == c1) {
                  item1.ChineseName = tagDict[l].Name;
                }
                if (c1 == "pkt.Infor") {
                  item1.name = "";
                  item1.amout = "";
                  item1.ChineseName = "";
                }
              }
              for (let et in esoption) {
                if (esoption[et].label === item1.ChineseName) {
                  item1.edit = true;
                  item1.editval = esoption[et].value;
                }
              }
              data.push(item1);
              if (c == "Infor") {
                for (let tag in this.pkt1) {
                  let pktInfor1 = this.pkt1[tag];
                  for (let tag1 in pktInfor1) {
                    let item2 = {};
                    item2.edit = false;
                    item2.name = "pkt.Infor." + tag1;
                    item2.amout = pktInfor1[tag1];
                    for (let M in tagDict) {
                      if (M == item2.name) {
                        item2.ChineseName = tagDict[M].Name;
                      }
                    }
                    for (let et in esoption) {
                      if (esoption[et].label === item2.ChineseName) {
                        item2.edit = true;
                        item2.editval = esoption[et].value;
                      }
                    }
                    data.push(item2);
                  }
                }
              }
            }
          }
          if (k == "ProList") {
            let proList = this.data1[k];
            for (let pro in proList) {
              let item2 = {};
              item2.edit = false;
              let pro1 = "ProList." + pro;
              item2.name = pro1;
              item2.amout = proList[pro];
              for (let z in tagDict) {
                if (z == pro1) {
                  item2.ChineseName = tagDict[z].Name;
                }
              }
              if (pro == "ProStack") {
                item2.amout = "";
              }
              for (let et in esoption) {
                if (esoption[et].label === item2.ChineseName) {
                  item2.edit = true;
                  item2.editval = esoption[et].value;
                }
              }
              data.push(item2);
              if (pro == "ProStack") {
                for (let Q in proList[pro]) {
                  let pList = proList[pro][Q];
                  for (let S in pList) {
                    let item4 = {};
                    item4.edir = false;
                    let S1 = "ProList.ProStack." + S;
                    item4.name = S1;
                    item4.amout = pList[S];
                    for (let l in tagDict) {
                      if (l == S1) {
                        item4.ChineseName = tagDict[l].Name;
                      }
                    }
                    for (let et in esoption) {
                      if (esoption[et].label === item4.ChineseName) {
                        item4.edit = true;
                        item4.editval = esoption[et].value;
                      }
                    }
                    data.push(item4);
                  }
                }
              }
            }
          }
          if (k == "RuleInfor") {
            let RuleInfor = this.data1[k];
            for (let Rule in RuleInfor) {
              let item3 = {};
              item3.edit = false;
              let Rule1 = "RuleInfor." + Rule;
              item3.name = Rule1;
              item3.amout = RuleInfor[Rule];
              for (let i in tagDict) {
                if (i == Rule1) {
                  item3.ChineseName = tagDict[i].Name;
                }
              }
              for (let et in esoption) {
                if (esoption[et].label === item3.ChineseName) {
                  item3.edit = true;
                  item3.editval = esoption[et].value;
                }
              }
              data.push(item3);
            }
          }

          if (k == "sTCPFingerInfor") {
            let Stco = this.data1[k];
            for (let St in Stco) {
              let item5 = {};
              item5.edit = false;
              let St1 = "sTotalsign." + St;
              item5.name = St1;
              item5.amout = Stco[St];
              for (let l in tagDict) {
                if (l == St1) {
                  item5.ChineseName = tagDict[l].Name;
                }
              }
              for (let et in esoption) {
                if (esoption[et].label === item5.ChineseName) {
                  item5.edit = true;
                  item5.editval = esoption[et].value;
                }
              }
              data.push(item5);
            }
          }
          if (k == "TaskInfo") {
            for (let Tas in Task) {
              let item6 = {};
              item6.edit = false;
              let Tas1 = "TaskInfo." + Tas;
              item6.name = Tas1;
              item6.amout = Task[Tas];
              for (let l in tagDict) {
                if (l == Tas1) {
                  item6.ChineseName = tagDict[l].Name;
                }
              }
              for (let P in tagDict1) {
                if (P == Tas1) {
                  item6.ChineseName = tagDict1[P].Name;
                }
              }
              for (let et in esoption) {
                if (esoption[et].label === item6.ChineseName) {
                  item6.edit = true;
                  item6.editval = esoption[et].value;
                }
              }
              data.push(item6);
            }
          }
          if (k == "TCPGather") {
            for (let T in Tcp) {
              for (let Tc in Tcp[T]) {
                let item7 = {};
                item7.edit = false;
                let Tc1 = "TCPGather." + Tc;
                item7.name = Tc1;
                item7.amout = Tcp[T][Tc];
                for (let l in tagDict) {
                  if (l == Tc1) {
                    item7.ChineseName = tagDict[l].Name;
                  }
                }
                for (let et in esoption) {
                  if (esoption[et].label === item7.ChineseName) {
                    item7.edit = true;
                    item7.editval = esoption[et].value;
                  }
                }
                data.push(item7);
              }
            }
          }
          if (k == "DNS") {
            for (let D in DNS) {
              for (let N in DNS[D]) {
                let item = {};
                let dnslist = "DNS." + N;
                item.name = dnslist;
                item.amout = DNS[D][N];
                let Answer = DNS[D];
                let answername;
                if (N == "Answer") {
                  for (let na in Answer.Answer) {
                    for (let ne in Answer.Answer[na]) {
                      answername = dnslist + "." + ne;
                      item.name = answername;
                      item.amout = Answer.Answer[na][ne];
                    }
                  }
                }
                for (let l in tagDict) {
                  if (l == dnslist) {
                    item.ChineseName = tagDict[l].Name;
                  }
                  if (l == answername) {
                    item.ChineseName = tagDict[l].Name;
                  }
                }
                data.push(item);
              }
            }
          }
          this.data = data;
        }
      } else {
        console.log("不存在");
      }
    },
    FMT_CSLIST_LABEL(type) {
      let obj = {
        Mac: "MAC",
        IP: "IP",
        Port: "端口",
        SSLFinger: "指纹",
        IpCountry: "IP所在国家",
        IpSubdivisions: "IP所在省份",
        IpCity: "IP所在城市",
        InitialTTL: "发送原始TTL",
        TTLMax: "发送最大TTL",
        TTLMin: "发送最小TTL",
        MaxHopCount: "发送TTL最大距离",
        MinHopCount: "发送TTL最小距离",
        DistLen: "包长分布",
        MaxLen: "发送最大包长",
        FinNum: "发送FIN次数",
        RSTNum: "发送RST次数",
        PSHNum: "发送PSH次数",
        Bytes: "发送字节数",
        PayloadBytes: "发送负载字节数",
        IpIdOffset: "发送IPID分布",
        Num: "发送包数",
        SYNNum: "发送SYN次数",
        SYNBytes: "发送SYN包长",
        PayloadNum: "发送负载包数",
        SbytesDiviDbytes: "目的包数差异",
      };
      if (obj[type]) return obj[type];
      return "";
    },
    FMT_GENRA(item) {
      if (item.label === "源包长分布" && item.val) {
        return Array.from(item.val).join("") || "-";
      }
      // if (item.label === "目的包长分布" && item.val) {
      //   return Array.from(item.val).join(" ") || "-";
      // }
      if (item.label === "SYN包序列号" && item.val) {
        return Array.from(item.val).join("-") || "-";
      }
      if (item.label === "TCP聚合" && Array.from(item.val).length > 0) {
        console.log(Array.from(item.val), "666");
        return Array.from(item.val).join("-") || "-";
      }
      if (item.val && item.val != []) {
        return item.val;
      } else {
        return "-";
      }
    },
    // 包长分布数据展示
    FMT_DIST_LEN(item,type) {
      let value = [
        "0~128",
        "129~256",
        "257~384",
        "385~512",
        "513~640",
        "641~768",
        "769~896",
        "897~1024",
      ];
      if (!item) {
        return "";
      } else {
        // this[type]=item.map((item, index) => `${value[index]}:${item}`);
        this.$emit('updateList',{item,type});
        return item.map((item, index) => `${value[index]}:${item}`);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sessionlog {
  .log-value {
    position: absolute;
    right: 0;
    z-index: 500;
  }
  .general {
    width: 100%;
    .cell {
      width: 100%;
      min-height: 60px;
      display: flex;
      font-size: 14px;
      padding-bottom: 8px;
      .label {
        height: 100%;
        width: 180px;
        display: flex;
        flex-direction: column;
        align-items: left;
        justify-content: space-evenly;
        > span:nth-of-type(2) {
          color: #9999a1;
        }
      }
      .content {
        word-break: break-all;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: left;
      }
      .content {
        flex: 328;
        height: 100%;
      }
    }
  }
  ::v-deep {
    .el-tabs__nav-scroll {
      padding-left: 0 !important;
    }
    .el-tabs__header {
      margin: 0 !important;
    }
    .el-tabs--card > .el-tabs__header {
      margin-left: 10px;
      border: 0;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__nav {
      height: 34px;
      border: 0;
      padding: 4px;
      background: #f2f3f7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item {
      border: 0;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
      width: 77px;
      height: 26px;
      font-size: 14px;
      background: #ffffff;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
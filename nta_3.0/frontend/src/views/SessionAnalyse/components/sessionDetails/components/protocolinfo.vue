<template>
  <div class="sessionlog">
    <el-tabs v-model="activeDown" type="card">
      <el-tab-pane label="table" name="table">
        <el-table :data="data" style="width: 100%" :show-header="false">
          <el-table-column prop="label"> </el-table-column>
          <el-table-column prop="key"> </el-table-column>
          <el-table-column prop="value" show-overflow-tooltip> 
            <template #default="{row}">
              <template v-if="row.isArr">
                <div v-for="(item,index) in row.value" :key="index">{{ item }}</div>
              </template>
              <span v-else>{{ row.value }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="json" name="json">
        <div>
          <el-card shadow="never" style="margin-top: 20px">
            <json-viewer
              :value="initSession"
              :expand-depth="5"
              :copyable="true"
            >
              <template slot="copy"> 复制 </template>
            </json-viewer>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import tagJAON from "@/assets/esMsgFiledExplain.json";

export default {
  data() {
    return {
      activeDown: "table",
      pro_info: {},
      data: [], // table 数据
      infodate: {},
      tagDict: {},
      tagJAON,
    };
  },
  computed: {
    sessionAgreement() {
      const result = this.$store.state.conversational.sessionAgreement;
      return result.metadata ? result.metadata[0] : result;
    },
    initSession() {
      const result = this.$store.state.conversational.sessionAgreement;
      return result.metadata ? result.metadata[0] : result;
    },
  },
  watch: {
    sessionAgreement: {
      handler(val) {
        if(typeof val==='string'){
          return;
        }
        if (val&&Object.keys(val).length) {
          this.initDict();
          this.initProtocolMetaData();
        }else{
          this.data=[];
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    initDict() {
      this.tagDict = {
        ...this.tagJAON.conn,
        ...this.tagJAON.metadata,
        ...this.tagJAON.dns,
        ...this.tagJAON.http,
        ...this.tagJAON.ssl,
        ...this.tagJAON.rlogin,
        ...this.tagJAON.telnet,
        ...this.tagJAON.ssh,
        ...this.tagJAON.rdp,
        ...this.tagJAON.vnc,
        ...this.tagJAON.xdmcp,
      };
    },
    initProtocolMetaData() {
      this.infodate = JSON.parse(
        JSON.stringify(this.sessionAgreement)
      );
      this.data = []; // 重置数据数组
      for (const key in this.infodate) {
        this.processValue(key, this.infodate[key]);
      }
    },
    processValue(key, value) {
      const tagDict = this.tagDict; // 修复this问题
      let item;
      if (typeof value === 'object' && value !== null&& !Array.isArray(value)){
        Object.keys(value).forEach(nestedKey => {
          item = {
            label: tagDict[`${key}.${nestedKey}`] ? tagDict[`${key}.${nestedKey}`].Name : (tagDict[key] ? tagDict[key].Name : ''),
            key: `${key}.${nestedKey}`,
            value: value[nestedKey] === null ? '-' : value[nestedKey],
          };
          this.processValue(`${key}.${nestedKey}`, value[nestedKey]); // 使用 this.processValue 进行递归调用
        });
      }else if(value !== null&& Array.isArray(value)){
        let obj={
          key:'',
          value:[]
        };
        value.forEach(item=>{
          if(item&&typeof item==='object'){
            this.processValue(key, item);
          }else{
            obj.key=key;
            obj.value.push(item);
          }
        });
        if(obj.key){
          this.data.push({
            label: tagDict[`${obj.key}`] ? tagDict[`${obj.key}`].Name : '',
            key:obj.key,
            value: value === null ? '-' : obj.value,
            isArr:true
          });
        }
      } else {
        item = {
          label: tagDict[key] ? tagDict[key].Name : '',
          key,
          value: value === null ? '-' : value,
        };
        this.data.push(item);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.sessionlog {
  ::v-deep {
    .el-tabs__header {
      margin: 0 !important;
    }
    .el-tabs--card > .el-tabs__header {
      margin-left: 10px;
      border: 0;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__nav {
      border: 0;
      background: #f2f3f7;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item {
      border: 0;
    }
    .el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
      width: 77px;
      height: 26px;
      background: #ffffff;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 10px !important;
    }
    .el-table__empty-block {
      width: 100% !important;
    }
  }
}
</style>
<template>
  <div class="box">
    <el-popover placement="bottom" trigger="click" :popper-append-to-body="true" :visible-arrow="false"
                popper-class="dialoguepopperbox"
    >
      <el-button slot="reference" class="headbtn" @click="openpopover">
        <svg-icon icon-class="jh-icon" style="margin-right: 5px" />聚合项
      </el-button>
      <div class="head">
        <el-input v-model="checkinput" size="mini" placeholder="搜索需要的聚合项"></el-input>
      </div>
      <div class="content">
        <el-checkbox-group v-model="checkedHeader" @change="configStotage">
          <el-checkbox v-for="item in tableHeader1" :key="item" :label="item" style="display: block">
            {{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="foot">
        <el-checkbox v-model="checkAll" style="margin-left: 16px" @change="searchButtonClick">
          全选
        </el-checkbox>
        <el-button style="margin-right: 16px" @click="resetButtonClick">
          重置
        </el-button>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name:'DialogueListFilter',
  props: ["name"],
  data() {
    return {
      checkinput: "",
      tableHeader1: [],
      tableHeaderdialogue: [
        "客户端IP",
        "服务端IP",
        "应用协议",
        "服务端端口",
        "客户端MAC",
        "客户端IP所在国家",
        "服务端MAC",
        "服务端IP所在国家",
        "会话数量",
        "发送负载字节数",
        "接收负载字节数",
      ],
      tableHeaderssl: [
        "客户端IP",
        "服务端IP",
        "应用协议",
        "服务端端口",
        "服务端证书Hash",
        "服务器名",
        "客户端指纹",
        "服务端指纹",
        "元数据_SSL条数",
      ],
      tableHeaderhttp: [
        "客户端IP",
        "服务端IP",
        "应用协议",
        "服务端端口",
        "网址(URL)",
        "主站",
        // "源端HTTP指纹(HTTP-UA)",
        "客户端.客户端信息",
        "元数据_HTTP条数",
      ],
      tableHeaderdns: [
        "客户端IP",
        "服务端IP",
        "应用协议",
        "服务端端口",
        "域名",
        "答复地址",
        "元数据_DNS条数",
      ],
      checkedHeader: [],
      checkedHeaderdialogue: [
        "客户端IP",
        "应用协议",
        "服务端端口",
        "服务端IP",
        "会话数量",
        "发送负载字节数",
        "接收负载字节数",
      ],
      checkedHeaderssl: [
        "客户端IP",
        "服务端IP",
        "应用协议",
        "服务端端口",
        "元数据_SSL条数",
      ],
      checkedHeaderhttp: [
        "客户端IP",
        "应用协议",
        "服务端端口",
        "服务端IP",
        "元数据_HTTP条数",
      ],
      checkedHeaderdns: [
        "客户端IP",
        "应用协议",
        "服务端端口",
        "服务端IP",
        "元数据_DNS条数",
      ],
      //   protocolInfofliter1: "",
      dialogueListfliter1: "",
      checkAll: false,
    };
  },
  computed: {
    dialogueListfliter() {
      return this.$store.state.conversational.dialogueListfliter;
    },
  },
  watch: {
    checkinput: {
      handler(val) {
        this.init(val);
      },
    },
    checkedHeader: {
      handler(val, old) {
        this.$emit("checkedHeaders1", this.checkedHeader);
      },
    },
    '$store.state.conversational.dialogueListfliter': {
      deep:true,
      immediate:true,
      handler(val, old) {
        this.dialogueListfliter1 = val;
        this.inittable();
        this.init();
      },
    },
  },
  mounted() {
    this.checkedHeader = this.checkedHeaderdialogue;
    this.tableHeader1 = this.tableHeaderdialogue;
    // if (window.localStorage.showHeader != undefined) {
    //   this.checkedHeader = JSON.parse(window.localStorage.showHeaderdialogue);
    // }
  },

  methods: {
    openpopover() {
      this.inittable();
      this.init();
    },
    inittable() {
      if (this.dialogueListfliter1 == "dialogueList") {
        this.checkedHeader = this.checkedHeaderdialogue;
        this.tableHeader1 = this.tableHeaderdialogue;
        if (window.localStorage.showHeaderdialogue != undefined) {
          this.checkedHeader = JSON.parse(
            window.localStorage.showHeaderdialogue
          );
        }
      }
      if (this.dialogueListfliter1 == "httpList") {
        this.checkedHeader = this.checkedHeaderhttp;
        this.tableHeader1 = this.tableHeaderhttp;
        if (window.localStorage.showHeaderhttp != undefined) {
          this.checkedHeader = JSON.parse(window.localStorage.showHeaderhttp);
        }
      }
      if (this.dialogueListfliter1 == "dnsList") {
        this.checkedHeader = this.checkedHeaderdns;
        this.tableHeader1 = this.tableHeaderdns;
        if (window.localStorage.showHeaderdns != undefined) {
          this.checkedHeader = JSON.parse(window.localStorage.showHeaderdns);
        }
      }
      if (this.dialogueListfliter1 == "sslList") {
        this.checkedHeader = this.checkedHeaderssl;
        this.tableHeader1 = this.tableHeaderssl;
        if (window.localStorage.showHeaderssl != undefined) {
          this.checkedHeader = JSON.parse(window.localStorage.showHeaderssl);
        }
      }
    },
    // 全选按钮
    searchButtonClick(val) {
      if (this.dialogueListfliter1 == "dialogueList") {
        this.checkedHeader = val
          ? this.tableHeaderdialogue
          : this.checkedHeaderdialogue;
        window.localStorage.showHeaderdialogue = JSON.stringify(
          this.checkedHeader
        );
      }
      if (this.dialogueListfliter1 == "httpList") {
        this.checkedHeader = val
          ? this.tableHeaderhttp
          : this.checkedHeaderhttp;
        window.localStorage.showHeaderhttp = JSON.stringify(this.checkedHeader);
      }
      if (this.dialogueListfliter1 == "dnsList") {
        this.checkedHeader = val ? this.tableHeaderdns : this.checkedHeaderdns;
        window.localStorage.showHeaderdns = JSON.stringify(this.checkedHeader);
      }
      if (this.dialogueListfliter1 == "sslList") {
        this.checkedHeader = val ? this.tableHeaderssl : this.checkedHeaderssl;
        window.localStorage.showHeaderssl = JSON.stringify(this.checkedHeader);
      }
    },
    // 重置按钮
    resetButtonClick() {
      if (this.dialogueListfliter1 == "dialogueList") {
        this.checkedHeader = this.checkedHeaderdialogue;
        window.localStorage.showHeaderdialogue = JSON.stringify(
          this.checkedHeader
        );
        this.checkAll = false;
      }
      if (this.dialogueListfliter1 == "httpList") {
        this.checkedHeader = this.checkedHeaderhttp;
        window.localStorage.showHeaderhttp = JSON.stringify(this.checkedHeader);
        this.checkAll = false;
      }
      if (this.dialogueListfliter1 == "dnsList") {
        this.checkedHeader = this.checkedHeaderdns;
        window.localStorage.showHeaderdns = JSON.stringify(this.checkedHeader);
        this.checkAll = false;
      }
      if (this.dialogueListfliter1 == "sslList") {
        this.checkedHeader = this.checkedHeaderssl;
        window.localStorage.showHeaderssl = JSON.stringify(this.checkedHeader);
        this.checkAll = false;
      }
    },
    init(data) {
      if (this.dialogueListfliter1 == "dialogueList") {
        this.tableHeader1 = [];
        var input = data;
        var items = this.tableHeaderdialogue;
        var items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderdialogue;
        }
      }
      if (this.dialogueListfliter1 == "httpList") {
        this.tableHeader1 = [];
        var input = data;
        var items = this.tableHeaderhttp;
        var items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderhttp;
        }
      }
      if (this.dialogueListfliter1 == "dnsList") {
        this.tableHeader1 = [];
        var input = data;
        var items = this.tableHeaderdns;
        var items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderdns;
        }
      }
      if (this.dialogueListfliter1 == "sslList") {
        this.tableHeader1 = [];
        var input = data;
        var items = this.tableHeaderssl;
        var items1;
        if (input) {
          items1 = items.filter((item) => {
            return Object.keys(item).some((key1) => {
              return item.match(input);
            });
          });
          this.tableHeader1 = items1;
        } else {
          this.tableHeader1 = this.tableHeaderssl;
        }
      }
    },
    configStotage() {
      if (this.dialogueListfliter1 == "dialogueList") {
        this.checkAll=this.checkedHeader.length===this.tableHeaderdialogue.length;
        window.localStorage.showHeaderdialogue = JSON.stringify(
          this.checkedHeader
        );
      }
      if (this.dialogueListfliter1 == "httpList") {
        this.checkAll=this.checkedHeader.length===this.tableHeaderhttp.length;
        window.localStorage.showHeaderhttp = JSON.stringify(this.checkedHeader);
      }
      if (this.dialogueListfliter1 == "dnsList") {
        this.checkAll=this.checkedHeader.length===this.tableHeaderdns.length;
        window.localStorage.showHeaderdns = JSON.stringify(this.checkedHeader);
      }
      if (this.dialogueListfliter1 == "sslList") {
        this.checkAll=this.checkedHeader.length===this.tableHeaderssl.length;
        window.localStorage.showHeaderssl = JSON.stringify(this.checkedHeader);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content {
  margin: 0 16px;
  height: 220px;
  overflow: auto;

  ::v-deep .el-checkbox {
    color: #2c2c35;
    margin-bottom: 8px;
  }

  ::v-deep .is-checked+.el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }
}

.headbtn {
  height: 32px;
  padding: 0 10px;
}

.foot {
  height: 48px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f8f8;
  border-radius: 0px 0px 2px 2px;

  .el-button {
    height: 24px;
    width: 58px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ::v-deep .el-checkbox {
    color: #2c2c35;
    // margin-bottom: 8px;
  }

  ::v-deep .is-checked+.el-checkbox__label {
    color: #2c2c35;
  }

  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #116ef9;
    border-color: #116ef9;
  }

  .el-button:focus,
  .el-button:hover {
    background-color: #116ef9;
    border-color: #116ef9;
    color: #ffffff;
  }
}

.box {
  width: 100%;
  height: 32px;

  //   ::v-deep {
  //     .el-button {
  //       display: flex;
  //       justify-content: center;
  //       align-items: center;
  //       width: 94px;
  //       height: 32px;
  //     }

  //     .el-checkbox-group {
  //       display: flex;
  //       flex-direction: column;
  //     }
  //   }
}
</style>
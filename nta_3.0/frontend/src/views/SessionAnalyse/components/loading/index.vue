<!--
 * @Author: liangjh <EMAIL>
 * @Date: 2022-08-16 13:17:07
 * @LastEditors: liangjh <EMAIL>
 * @LastEditTime: 2022-09-13 13:56:40
 * @FilePath: \probe_v3_202207d:\级客信安\Web\analyse_v3_202207\src\views\SessionAnalyse\components\loading\index.vue
 * @Description: 
 * 
 * Copyright (c) 2022 <NAME_EMAIL>, All Rights Reserved. 
-->
<template>
  <span
    :class="{ 'is-loading': flag }"
    v-loading="flag"
    element-loading-spinner="el-icon-loading"
    element-loading-background="inherit"
  >
    <span
      v-if="isLoading === undefined && num != null"
      style="color: #9999a1;"
      >{{ num }}</span
    >
  </span>
</template>

<script>
export default {
  props: ["num", "isLoading"],
  data() {
    return {
      flag: false,
    };
  },
  watch: {
    num: {
      handler(val) {
        if (this.isLoading !== undefined) return;
        if (val === undefined) return;
        this.flag = val === null ? true : false;
      },
      immediate: true,
    },
    isLoading: {
      handler(val) {
        if (val === undefined) return;
        this.flag = val === false ? true : false;
      },
      immediate: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.is-loading {
  display: inline-block;
  width: 16px;
  font-size: 14px;
  color: #9999a1;
  ::v-deep .el-loading-spinner {
    margin-top: -25px;
  }
}
</style>
<template>
  <div class="signchains">
    <section v-for="(item,index) of signChains" :key="index">
      <aside class="circle">{{ index + 1 }}</aside>
      <el-popover
        :ref="'popoverRef_'+item.cert_sha1"
        v-model="item.popoverShow"
        width="111"
        trigger="click"
        style="padding:0;"
      >
        <div class="pop-box">
          <div @click="()=>{
            QUICK_SHA1(item,false,'Cert')
          }"
          >
            正向检索
          </div>
          <div @click="QUICK_SHA1(item,true,'Cert')">反向检索</div>
        </div>
        <div slot="reference" class="sha1">{{ item.cert_sha1 }}</div>
      </el-popover>
    
      <div class="labels">
        <div v-for="(tag,index2) of FMT_Labels(item.Labels)" :key="index2" :class="tag.tag_level">
          {{ tag.tag_text }}
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>hain',
  props: {
    tag_target_type: {
      type: Number,
      default: 4
    },
    certInfo: {
      type: Object,
      default: ()=>{}
    },
  },
  computed: {
    signChains() {
      return this.certInfo.sign_chains;
    }
  },
  created() { 
    this.signChains.map(item => { item.popoverShow = false;});
  },
  methods: {
    FMT_Labels(labels) {
      let arr = [];
      for (let i of labels) { 
        i.popoverShow = false;
        switch (i.tag_level) {
        case 'danger':
          arr.push({...i,levelNum:0});
          break;
        case 'warning':
          arr.push({...i,levelNum:1});
          break;
        case 'positive':
          arr.push({...i,levelNum:2});
          break;
        case 'success':
          arr.push({...i,levelNum:3});
          break;
        case 'info':
          arr.push({...i,levelNum:4});
          break;
        default:
          arr.push({...i,levelNum:4});
          break;
        }
      }
      arr = arr.sort((a, b) => a.levelNum - b.levelNum);
      return arr;
    },
    // 证书快速检索
    async QUICK_SHA1(item, sort, type) {
      let obj={
        sort,
        sortname :type,
        cert:item.cert_sha1
      };
      this.$store.commit("conversational/getSessionQuickSearch", obj);
      document.body.click();
    },
  }
};
</script>

<style  lang="scss" scoped>
  .signchains{
    padding-left: 20px;
    >section{
      border-left: 1px dashed #D1E3FF;
      box-sizing: border-box;
      
      padding-left: 20px;
      position: relative;
    }
    >section:first-child{
      margin-top: 20px;
    }
    >section:last-child{
      border: 0;
    }
    
    .circle{
      width: 24px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      border-radius: 12px;
      opacity: 1;
      background: #E7F0FE;
      color:#116EF9;
      position: absolute;
      left: -12px;
      top: 0;
    }
    .sha1{
      width:100%;
      height: 24px;
      line-height: 24px;
      color:#116EF9;
      cursor: pointer;
      margin-bottom: 8px;
    }
    .sha1:hover{
      text-decoration: underline;
    }
    .labels{
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      >div{
        width: auto;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        background: #DEE0E7;
        align-items: center;
        padding: 0 4px;
        margin:2px;
        // cursor: pointer;
        color:#2C2C35;
        font-size: 12px;
      }
      // >div:hover{
      //   border: 1px solid rgb(71, 71, 238);
      // }
      .danger{
        background-color: #FCE7E7;
        color: #A41818;
      }
      .warning{
        background-color: #F9EDDF;
        color: #B76F1E;
      }
      .positive{
        background-color: #E7F0FE;
        color: #1B428D;
      }
      .success{
        background-color: #E0F5EE;
        color:#006157;
      }
    }
  }
  .pop-box{
      width: 100%;
      padding: 4px;
      box-sizing: border-box;
      >div{
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        cursor: pointer;
        border-radius: 4px;
      }
      >div:hover{
        background-color: #E7F0FE;
      }
    }
</style>Z
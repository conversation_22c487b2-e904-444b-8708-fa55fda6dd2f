<template>
  <div class="formbox-down">
    <graph
      :init-value="certInfo.basic_data.cert"
      :init-type="'CERT'"
    />
  </div>
</template>

<script>
import graph from "@/components/graph/index.vue";
export default {
  name:'GraphExploration',
  components: {
    graph,
  },
  props: {
    certInfo: {
      type: Object,
      default: ()=>{}
    },
  },
};
</script>

<style lang="scss" scoped>
.formbox-down{
   padding: 16px;
    width:100%;
    height: 520px;
    border-radius: 8px;
}
</style>
<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-07-25 11:03:41
 * @LastEditors: liangjh <EMAIL>
 * @LastEditTime: 2022-09-16 01:00:08
 * @FilePath: \probe_v3_202207d:\级客信安\Web\analyse_v3_202207\src\views\SessionAnalyse\components\details\Cciedetails.vue
 * @Description: 证书详情！！ 
 * 
 * Copyright (c) 2022 by lian<PERSON><PERSON><PERSON><PERSON> <EMAIL>, All Rights Reserved. 
-->
<template>
  <el-drawer
    title="证书详情"
    :visible.sync="certdrawer"
    :direction="direction"
    :before-close="handleClose"
    destroy-on-close
    size="40%"
  >
    <div class="formbox">
      <div class="formbox-top">
        <div class="title">
          <svg-icon icon-class="icon_certificate" />
          <span style="font-weight: bold;">{{ certInfo.cert }}</span>
          <img src="../../../../assets/images/unknown.png" />
        </div>
        <div class="location">
          <div>
            <!-- <svg-icon icon-class="icon-location" />  -->
            <span>父证书</span>
          </div>
          <span>{{ certInfo.fatherCert || '无' }}</span>
        </div>
        <div class="task">
          <div class="task-l">
            <svg-icon icon-class="icon-task" />
            <span>任务</span>
          </div>
          <span>{{ certInfo.taskname }}</span>
          <div class="task-r"></div>
        </div>
        <div class="tags">
          <div class="tags-l">
            <svg-icon icon-class="icon-label" />
            <span>标签</span>
          </div>
          <div class="tags-r">
            <div class="tags-r-tagbox" :class="showtag ? 'showmore' : ''">
              <span
                v-for="(item, index) in certInfo.labels"
                :key="index"
                :class="
                  item.tag_level == 'danger'
                    ? 'redTag'
                    : item.tag_level == 'warning'
                      ? 'yellowTag'
                      : item.tag_level == 'info'
                        ? 'grayTag'
                        : 'blueTag'
                "
              >
                {{ item.tag_text }}
              </span>
              <!-- <span class="addtag">+ 标签</span> -->
              <el-button
                class="cert_tag_button"
                size="small"
                icon="el-icon-plus"
                @click="opentagbox"
              >
                标签
              </el-button>
            </div>
          </div>
          <div
            v-if="Array.isArray(certInfo.labels) && certInfo.labels.length > 7"
            class="tags-d"
            @click="opnetaglist"
          >
            <span>展开</span>
            <svg-icon icon-class="menu-down" />
          </div>
        </div>
        <div class="remark">
          <div class="remark-l">
            <svg-icon icon-class="icon-remark" />
            <span>备注</span>
          </div>
          <div class="remarkbox">
            <div
              v-for="(item, index) in certInfo.remarks"
              :key="index"
              class="remarkbox-tag"
            >
              {{ item }}
              <svg-icon
                icon-class="icon_close"
                @click="deleteTag(item, index)"
              />
            </div>
            <div class="remarkbox-addtag">
              <el-input
                v-if="inputVisible"
                ref="saveTagInput"
                v-model="inputValue"
                resize="horizontal"
                @keyup.enter.native="$event.target.blur"
                @blur="handleInputConfirm"
              ></el-input>
              <el-button
                class="cert_tag_button"
                size="small"
                icon="el-icon-plus"
                @click="showInput"
              >
                备注
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="formbox-mid">
        <div
          v-for="(item, index) in certInfo.list"
          :key="index"
          class="formbox-mid-box"
        >
          <span class="top">{{ item.name }}</span>
          <span>{{ item.value }}</span>
        </div>
      </div>
      <div class="formbox-down">
        <graph
          v-if="certdrawer"
          :init-value="certInfo.cert"
          :init-type="'CERT'"
        />
      </div>
      <!-- <div class="indialog">
        <el-dialog
          v-dialogDrag
          :visible.sync="opentag"
          :modal="false"
          :close-on-click-modal="false"
          width="1000px"
          @opened="handleOpened"
        >
          <tagsdeta
            ref="tagbox"
            :display="(display = 'block')"
            :value="tagval"
            @getTagValue="getTagValue"
            @handleClose="display = 'none'"
            @cleartagdialog="cleartagdialog"
          ></tagsdeta>
        </el-dialog>
      </div> -->
      <!-- 标签库组件 -->
      <tag-view
        v-model="tagLibVisible"
        :tag_target_type="tag_target_type"
        :tag-labels="certInfo.labels"
        :is-show-aside="false"
        @modifyLabels="SUBMIT_TAGS"
      />
    </div>
  </el-drawer>
</template>

<script>
import graph from "../../../../components/graph/index.vue";
import { SetRemark, SetTags, GetIpInfo } from "@/api/sessionList/sessionlist";
import tagsdeta from "@/views/SessionAnalyse/components/tag/tags_deta";
import TagView from "@/components/TagView";
export default {
  name:'CcieDetails',
  components: {
    tagsdeta,
    graph,
    TagView
  },
  props: ["certInfo", "certdrawer","tag_target_type"],
  data() {
    return {
      list: [
        { name: "签发机构", val: "teredo.ipv6.microsoft.com" },
        { name: "所有者", val: "teredo.ipv6.microsoft.com" },
        { name: "签发时间", val: "2020-12-14 00:49:32 " },
        { name: "有效时间", val: "2020-12-14 00:49:32 " },
        { name: "服务器热度", val: "273" },
        { name: "客户端热度", val: "27" },
        { name: "首次出现时间", val: "2020-12-14  00:49:32 " },
        { name: "末次出现时间", val: "2020-12-14  00:49:32 " },
      ],
      taglist: [
        { name: "开放端口数", val: "134" },

        { name: "域名数", val: "1412" },
        { name: "域名数", val: "1412" },
        { name: "所属锚域名数111", val: "1412" },
        { name: "域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },
        { name: "所属锚域名数", val: "1412" },

        { name: "发送总字节", val: "7282.06 MB" },
        { name: "访问端口数", val: "32" },
        { name: "平均流量", val: "167.00 kbps" },
        { name: "接收总字节", val: "1532.00 MB" },
        { name: "首次出现时间", val: "2020-12-14  00:49:32 " },
        { name: "末次出现时间", val: "2020-12-14  00:49:32 " },
      ],
      // 控制显示标签
      showtag: false,
      // 备注输入框的字段
      inputVisible: false,
      inputValue: "",
      // 标签弹窗字段
      opentag: false,
      tagval: "",
      direction: "rtl",
      tagLibVisible: false,
    };
  },
  methods: {
    SUBMIT_TAGS(x,y,z){
      console.log(x,y,z);
      
    },
    // 控制显示标签
    opnetaglist() {
      this.showtag = !this.showtag;
    },
    showInput() {
      this.inputVisible = true;
      this.$nextTick(() => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 鼠标在输入框中失去焦点的回调
    handleInputConfirm() {
      if (this.inputValue == "") {
        this.inputVisible = false;
        return;
      }
      // 将新增的标签添加到备注列表中
      this.certInfo.remarks.push(this.inputValue);
      let param = {
        str: this.certInfo.cert,
        type: "CERT",
        remarks: this.certInfo.remarks,
      };
      SetRemark(param).then((res) => {
        if (res.err == 0) {
          this.$message.success("添加成功");
          this.inputValue == "";
        } else {
          this.$message.error("添加失败");
        }
      });
      this.inputVisible = false;
      this.inputValue = "";
    },
    // 删除备注中的标签
    deleteTag(item, index) {
      this.certInfo.remarks.splice(index, 1);
      let param = {
        str: this.certInfo.cert,
        type: "CERT",
        remarks: this.certInfo.remarks,
      };
      SetRemark(param).then((res) => {
        if (res.err == 0) {
          this.$message.success("删除成功");
        } else {
          this.$message.error("添加失败");
        }
      });
    },
    handleOpened() {
      // 获取到id返回新的数组
      let tag_text = this.certInfo.labels.map((item) => {
        return item.tag_text;
      });
      this.tagval = tag_text.join(",");
    },
    // 打开标签库
    opentagbox() {
      this.tagLibVisible = true;
      this.tagval = "";
    },
    cleartagdialog() {
      this.opentag = false;
    },
    // 获取到标签列表数组
    getTagValue(val,fn) {
      if (val.length != 0) {
        let newarr = [];
        val.forEach((item) => {
          newarr.push(item.tag_id + "");
        });
        this.certInfo.labels = val;
        let param = {
          str: this.certInfo.cert,
          type: "CERT",
          labels: newarr,
        };
        SetTags(param).then((res) => {
          if (res.err == 0) {
            this.$message.success("添加成功");
          } else {
            this.$message.error("添加失败");
          }
        });
      }
      fn(true);
    },
    // 关闭抽屉
    handleClose(done) {
      let that = this;
      that.$emit("certcloseDrawer");
    },
  },
};
</script>

<style lang="scss" scoped>
.formbox {
  padding: 16px;
  &-top {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    color: #2c2c35;
    div {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .title {
      margin-bottom: 12px;
      font-size: 16px;
      .svg-icon {
        margin-right: 10px;
        font-size: 22px;
      }
      span {
        margin-right: 10px;
      }
      img {
        width: 62px;
        height: 22px;
      }
    }
    .location {
      margin-bottom: 12px;
      div {
        color: #767684;
        width: 64px;
        .svg-icon {
          margin-right: 6px;
        }
      }
    }
    .task {
      margin-bottom: 12px;
      &-l {
        width: 64px;
        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }
      // &-r {
      // }
    }
    .tags {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;
      &-l {
        color: #767684;
        width: 64px;
        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }
      &-r {
        flex: 1;
        &-tagbox {
          display: flex;
          flex-wrap: wrap;
          align-items: flex-start;
        }
        .showmore {
          align-items: flex-start;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 这里设置需要显示几行文本 */
        }
        .redTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #a41818;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #fce7e7;
          line-height: 20px;
        }
        .blueTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #1b428d;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #e7f0fe;
          border-radius: 2px;
        }
        .yellowTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #b76f1e;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #f9eddf;
          border-radius: 2px;
        }
        .grayTag {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #2c2c35;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          background: #f2f3f7;
          border-radius: 2px;
        }
        .cert_tag_button {
          margin-right: 8px;
          margin-bottom: 8px;
          color: #116ef9;
          border-radius: 2px;
          padding: 0 4px;
          // height: 20px;
          line-height: 20px;
          background: #ffffff;
          border: 1px solid #116ef9;
        }
      }
      &-d {
        width: 46px;
        height: 22px;
        color: #116ef9;
        .svg-icon {
          font-size: 12px;
        }
        cursor: pointer;
      }
    }
    .remark {
      width: 100%;
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;
      &-l {
        color: #767684;
        width: 64px;
        // margin-right: 12px;
        .svg-icon {
          margin-right: 6px;
        }
      }
      .remarkbox {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        ::v-deep {
          .el-input__inner {
            margin-right: 5px;
            width: 100px;
            height: 22px !important;
          }
        }
        &-addtag {
          .cert_tag_button {
            color: #116ef9;
            border-radius: 2px;
            padding: 0 4px;
            background: #ffffff;
            line-height: 20px;
            border: 1px solid #116ef9;
          }
        }
        &-tag {
          padding: 0 4px;
          margin-bottom: 8px;
          background: #f2f3f7;
          border-radius: 2px;
          margin-right: 8px;
          .svg-icon {
            margin-left: 4px;
            cursor: pointer;
          }
        }
      }
    }
  }
  &-mid {
    padding-top: 12px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    &-box {
      padding-left: 11px;
      min-width: 230px;
      position: relative;
      display: flex;
      flex-direction: column;
      font-family: "Alibaba PuHuiTi";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #2c2c35;
      margin-bottom: 12px;
      .top {
        color: #767684;
      }
    }
    &-box::before {
      position: absolute;
      top: 10px;
      left: 0;
      content: "";
      height: 30px;
      border-left: 1px solid #dee0e7;
    }
  }
  &-down {
    width: 100%;
    height: 462px;
    background: #f7f8fa;
    border-radius: 8px;
  }
  .indialog {
    ::v-deep .el-dialog {
      height: 460px;

      .el-dialog__headerbtn {
        top: 10px;
      }

      .el-dialog {
        box-shadow: 0px 6px 18px rgba(45, 47, 51, 0.14);
        border-radius: 8px;
      }
      .el-dialog__body {
        padding: 0;
      }
      .el-dialog__header {
        padding: 12px 24px;
      }
    }
  }
}
</style>
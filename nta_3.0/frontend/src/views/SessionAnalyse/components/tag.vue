<template>
  <div class="taglist">
    <div class="head">
      <div v-for="(item, index) in customColors" :key="index" class="taglist-top">
        <div class="taglist-top-box">
          <div class="taglist-top-box-title">
            <span class="l">{{ item.tag_text }}</span>
            <div class="r">
              <el-popover placement="bottom" width="140" trigger="hover" :visible-arrow="false"
                          popper-class="sortpopover"
              >
                <span slot="reference">...</span>
                <div class="sortbtn">
                  <el-button @click="tagforwardsort(item, 'Labels')">
                    <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                  </el-button>
                  <el-button @click="tagreversesort(item, 'Labels')">
                    <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                  </el-button>
                </div>
              </el-popover>
            </div>
          </div>
          <div class="taglist-top-box-msg">
            <div>
              <span>连接数</span> <span class="num">{{ item.cnt }}</span>
            </div>
            <div>
              <span>威胁等级</span>
              <span class="num">{{ item.black_list }}</span>
            </div>
          </div>
          <div class="taglist-top-box-plan">
            <el-progress :percentage="item.black_list" :color="item.color" :format="format" :stroke-width="3">
            </el-progress>
          </div>
        </div>
      </div>
    </div>
    <div class="taglist-down">
      <div class="taglist-down-l">
        <!-- 标签对应连接数排序条形图 -->
        <article class="box">
          <section class="tag">
            <v-chart :option="tag_options" autoresize :update-options="{ notMerge: true }">
            </v-chart>
          </section>
        </article>
      </div>
      <div class="taglist-down-r">
        <el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange"
                  @sort-change="handleSortChange"
        >
          <!-- <el-table-column type="selection" width="100" align="center">
          </el-table-column> -->
          <el-table-column type="index" align="center" :index="indexMethod" label="序号" width="80">
          </el-table-column>
          <el-table-column prop="tag_text" align="center" min-width="100" label="标签">
            <template slot-scope="scope">
              <!-- <router-link
                :to="{
                  path: '/targetAnalysis_p2/tagDetails',
                  query: { tagID: scope.row.tag_id },
                }"
                target="_blank"
                class="link-type"
              >
              </router-link> -->
              <span>{{ scope.row.tag_text }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="black_list" align="center" label="威胁等级" min-width="100" sortable @sort-method="sortfn">
          </el-table-column>
          <el-table-column min-width="100" prop="cnt" align="center" label="连接数" sortable>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { tag_options } from "./tablechartsData";
import { gettaglist } from "@/api/Conversational/conversational";
export default {
  props: ["searchData"],
  data () {
    return {
      tag_options,
      total: 0,
      page: 1,
      limit: 100,
      top: 150,
      customColor: "#409eff",
      customColors: [],
      tableData: [],
      order_field: "cnt",
      asc: false,
      checkConfigList: [],
      chartData: [],
      tagdata: [],
    };
  },
  watch: {
    searchData: {
      handler (val) {
        this.page = 1;
        this.initData();
      },
      deep: true,
      // immediate:true
    },
  },
  methods: {
    indexMethod (index) {
      return (this.page - 1) * this.limit + index + 1;
    },
    initData () {
      let param1 = {
        current_page: 1,
        page_size: this.top,
        asc: false,
      };
      Object.assign(param1, this.searchData);
      let arr = {
        and: [],
        not: []
      };
      for (let i = 0; i < param1.query.length; i++) {
        for (let j = 0; j < param1.query[i].search.length; j++) {
          if (param1.query[i].bool_search === 'and') {
            if (param1.query[i].search[j].target === 'Labels') {
              arr.and.push(param1.query[i].search[j].val);
            }
          }
          if (param1.query[i].bool_search === 'not') {
            if (param1.query[i].search[j].target === 'Labels') {
              arr.not.push(param1.query[i].search[j].val);
            }
          }
        }
      }
      param1.tag_query = arr;
      gettaglist(param1).then((req) => {
        if (req.err === 0) {
          if (req.data.black_order_desc !== null) {
            this.tagdata = req.data.black_order_desc.slice(0, 10);
            this.tagdata.forEach((item) => {
              if (item.black_list > 79) {
                item.color = "#DF0C0C";
              } else if (item.black_list > 59) {
                item.color = "#FF8800";
              } else if (item.black_list > 39) {
                item.color = "#06C251";
              } else if (item.black_list > 19) {
                item.color = "#06C251";
              } else {
                item.color = "#06C251";
              }
              if (
                item.black_list >= 1 &&
                item.black_list <= 100 &&
                item.white_list !== 100
              ) {
                if (item.black_list >= 80) {
                  item.type = "danger";
                } else {
                  item.type = "warning";
                }
              }
              if (
                item.white_list >= 1 &&
                item.white_list <= 100 &&
                item.black_list === 0
              ) {
                if (item.white_list === 100) {
                  item.type = "success";
                } else {
                  item.type = "";
                }
              }
              if (
                item.white_list === 0 &&
                item.black_list === 0
              ) {
                item.type = "info";
              }
            });
            this.customColors = this.tagdata;
          }else{
            this.customColors=[];
          }
          if (req.data.cnt_order_desc !== null) {
            this.data = [[], []];
            this.tableData = req.data.cnt_order_desc || [];
            this.chartData = req.data.cnt_order_desc || [];
            this.chartData.slice(0, 25).forEach((item) => {
              if (item.cnt === 0) return false;
              this.data[0].push(item.tag_text);     
              this.data[1].push(item.cnt);
            });
            if (this.data[0].length <= 25) {
              this.tag_options.yAxis[0].data = this.data[0].reverse();
            }
            if (this.data[1].length <= 25) {
              this.tag_options.series[0].data = this.data[1].reverse();
            }
          }else{
            this.tag_options.yAxis[0].data=[];
            this.tag_options.series[0].data=[];
            this.tableData=[];
          }
          this.total = req.data.total > 1000 ? 1000 : req.data.total;
          this.$emit("update:fatherValue", req.data.total);
        } else {
          this.$message.error(req.msg);
          this.total = 0;
          this.$emit("update:fatherValue", 0);
        }
      });
    },
    format (percentage) {
      return percentage === "";
    },
    handleSizeChange (val) {
      this.limit = val;
      this.initData();
    },
    handleSelectionChange (val) {
      this.checkConfigList = val;
    },
    handleSortChange ({ column, prop, order }) {
      if (prop == "tag_text") {
        this.order_field = "tag_id";
      } else {
        this.order_field = prop;
      }
      this.asc = order === "descending" ? false : true;
    },
    // 正向检索
    tagforwardsort (data, sign) {
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向检索
    tagreversesort (data, sign) {
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    sortfn (a, b) {
    },
  },
};
</script>

<style lang="scss" scoped>
.taglist {
  .head {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  &-top {
    margin-bottom: 8px;

    &-box {
      width: 224px;
      height: 114px;
      background: #ffffff;
      border: 1px solid #f2f3f7;
      border-radius: 8px;
      padding: 0 16px;

      &-title {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        margin-bottom: 8px;

        .l {
          font-family: "Alibaba PuHuiTi";
          font-style: normal;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          color: #2c2c35;
          max-width: 80%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .r {
          font-size: 14px;
          cursor: pointer;
        }
      }

      &-msg {
        div {
          display: flex;
          justify-content: space-between;
          font-family: "Alibaba PuHuiTi";
          font-style: normal;
          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #9999a1;
          margin-bottom: 5px;

          .num {
            font-family: "Alibaba PuHuiTi";
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 22px;
            color: #2c2c35;
          }
        }
      }

      &-plan {
        ::v-deep .el-progress-bar__outer {
          width: 190px;
        }
      }
    }
  }

  &-down {
    display: flex;
    justify-content: space-between;

    &-l {
      width: 710px;
      height: 400px;
      background: #f7f8fa;
      border-radius: 8px;

      .box {
        .tag {
          width: 710px;
          height: 400px;
        }
      }
    }

    &-r {
      width: 468px;
      height: 400px;
      background: #f7f8fa;
      border-radius: 8px;
      overflow-y: auto;

      ::v-deep {
        .el-table__empty-block {
          background: #f7f8fa;
        }

        .el-table th,
        .el-table tr,
        .el-table td {
          background: #f7f8fa;
          border: 0 !important;
        }
      }
    }
  }
}
</style>
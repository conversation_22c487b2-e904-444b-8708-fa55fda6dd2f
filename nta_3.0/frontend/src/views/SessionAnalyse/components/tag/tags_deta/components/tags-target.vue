<template>
  <!-- 全部标签 -->
  <div>
    <span v-for="(item, index) in searchTagList" :key="item.tagid">
      <el-tooltip class="item" placement="top-start" disabled>
        <div slot="content">
          {{ item.tag_explain }}
        </div>
        <span class="tag-item">
          <div
            style="display: inline-block"
            @click="addTag(item.tagText, index, item.attribute_name)"
          >
            <svg-icon icon-class="seleted" class="icon-seleted" />
            <el-tag
              class="tag"
              :type="item.type"
              :effect="item.exist ? 'dark' : 'light'"
            >
              <div>{{ item.tagText }}</div>
            </el-tag>
          </div>
        </span>
      </el-tooltip>
    </span>
  </div>
</template>

<script>
import dict from "@/assets/dict.json";
export default {
  components: {},
  props: {
    searchTagList: {
      type: Array,
    },
    // tagList: {
    //   type: Array,
    // },
    // targetType: {
    //   type: Number,
    // },
  },
  data() {
    return {
      // activeName: "1",
    };
  },
  computed: {
    // tag_category
    taglistexist() {
      return this.$store.state.conversational.TagList;
    },
  },
  watch: {
    taglistexist: {
      handler(val) {
        this.searchTagList.forEach((item) => {
          if (item.tagText === val.v) {
            item.exist = !item.exist;
            console.log(item)
          }
        });
      },
    },
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    addTag(v, i, l) {
      let p = {
        v: v,
        i: i,
        l: l,
      };
      console.log(p)
      this.$store.commit("conversational/TagListData", p);
      let value = [];
      setTimeout(() => {
        this.$store.state.conversational.taglisttrue.forEach((item) => {
          if (item.tagText === v) {
            item.exist = !item.exist ? true : false;
          }
          if (item.exist) {
            if (!value.includes(item.tagText)) {
              value.push(item);
            }
          }
        });
        console.log(value)
        this.$emit("getTagValue", value);
      }, 100);
    },
  },
};
</script>

<style lang="scss" scoped>
.tag-content {
  margin: 24px 0 10px;
  max-height: 550px;
  overflow: auto;

  .tag-item {
    position: relative;
    margin: 0 3px;
    height: 20px;
    .tag.el-tag {
      margin: 3px 0;
      cursor: pointer;
      height: 20px;
      line-height: 19px;
    }
    ::v-deep {
      .el-tag {
        background: #E7F0FE;
        border-radius: 2px;
        color: #1B428D;
      }
      .el-tag--dark{
        background: #116EF9 !important;
        border-radius: 2px !important;
        color: #FFFFFF !important;
      }
      .el-tag.el-tag--warning {
        background: #F9EDDF;
        border-radius: 2px;
        color: #B76F1E;
      }
      .el-tag--dark.el-tag--warning{
        background: #FF8800 !important;
        border-radius: 2px !important;
        color: #FFFFFF !important;
      }
      .el-tag.el-tag--success {
        background: #E0F5EE;
        border-radius: 2px;
        color: #006157;
      }
      .el-tag--dark.el-tag--success{
        background: #008775 !important;
        border-radius: 2px !important;
        color: #FFFFFF !important;
      }

      .el-tag--dark.el-tag--danger{
        background: #DF0C0C !important;
        border-radius: 2px !important;
        color: #FFFFFF !important;
      }
      .el-tag.el-tag--danger {
        background: #FCE7E7;
        border-radius: 2px;
        color: #A41818;
      }

      .el-tag.el-tag--info {
        background: #F2F3F7;
        border-radius: 2px;
        color: #2C2C35;
      }
      .el-tag--dark.el-tag--info{
        background: #2C2C35 !important;
        border-radius: 2px !important;
        color: #FFFFFF !important;
      }
    }
  }

  .icon-seleted {
    display: none;
    position: absolute;
    width: 30px;
    height: 20px;
    right: -5px;
    top: -2px;
    cursor: pointer;
    // color: #666;
  }
  .search-box {
    margin-left: 10px;
    width: 400px;
  }
  
}
</style>



<template>
  <div class="list">
    <div class="list-top">
      <div class="list-top-l">已选择<span>0</span>条</div>
      <div class="list-top-r">
        <el-popover placement="bottom" trigger="click" :visible-arrow="false" popper-class="alldownbox">
          <div slot="reference" class="qlactive">
            <div class="alldown-l">批量检索</div>
            <div class="alldown-r">
              <svg-icon icon-class="del-down2" />
            </div>
          </div>
          <div class="alldownfoot">
            <el-button class="alldownfoot-t" @click="searchforwardSearch(search_list, 'Finger')">正向检索</el-button>
            <el-button class="alldownfoot-d" @click="searchreverseSearch(search_list, 'Finger')">反向检索</el-button>
          </div>
        </el-popover>

        <div class="globaldown">
          <el-tooltip content="到“下载列表”的“日志导出”中下载" placement="top" effect="dark">
            <el-button @click="logderive">
              <i class="el-icon--left">
                <svg-icon icon-class="globaldown" />
              </i>
              日志导出
            </el-button>
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="list-listview">
      <el-table ref="table" :data="connTableData" tooltip-effect="dark" stripe :default-sort="{ prop: 'begin_time', order: 'ascending' }"
                show-overflow-tooltip style="width: 100%" border
                @selection-change="handleSelectionChange" @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="80" align="center" fixed></el-table-column>
        <el-table-column prop="num" label="序号" type="index" width="100" :index="indexMethod" fixed align="center" />
        <el-table-column label="指纹" prop="FINGER" min-width="200" fixed align="center" sortable="custom">
          <template slot-scope="scope">
            <div class="sortbox">
              <div class="top">
                <el-tooltip :disabled="!scope.row.fingerprint" class="item" effect="light" placement="top"
                            popper-class="sessionidTooltip"
                >
                  <div slot="content">
                    {{ scope.row.fingerprint }}
                  </div>
                  <span>
                    {{
                      scope.row.fingerprint ? scope.row.fingerprint:
                      "--"
                    }}
                  </span>
                </el-tooltip>
              </div>
              <div class="down">
                <div class="sorttoole">
                  <el-popover placement="bottom" width="200" trigger="hover" popper-class="sortpopover">
                    <span slot="reference" class="sorttoole-r">...</span>
                    <div class="sortbtn">
                      <el-button @click="forwardSearch(scope.row, 'Finger')">
                        <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                      </el-button>
                      <el-button @click="reverseSearch(scope.row, 'Finger')">
                        <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                      </el-button>
                    </div>
                  </el-popover>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="150" prop="fingerprint_type" label="指纹类型" />
        <el-table-column min-width="150" prop="fingerprint_describe" label="指纹描述" />
        <el-table-column prop="tag" :width="260" label="标签">
          <template slot-scope="scope">
            <div class="taglist">
              <span v-for="(item, index) in scope.row.tag" :key="item.tag_id" class="tagbox">
                <el-popover placement="bottom" width="200" trigger="click" popper-class="sortpopover">
                  <el-tag slot="reference" class="tag" :type="item.type">
                    <div style="cursor: pointer">{{ item.tag_text }}</div>
                  </el-tag>
                  <div class="sortbtn">
                    <el-button @click="forwardSearch(item, 'Labels')">
                      <svg-icon icon-class="sort-up" style="margin-right: 15px" />正向检索
                    </el-button>
                    <el-button @click="reverseSearch(item, 'Labels')">
                      <svg-icon icon-class="sort-down" style="margin-right: 15px" />反向检索
                    </el-button>
                  </div>
                </el-popover>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="170" prop="dIp_num" label="客户端IP热度" />
        <el-table-column min-width="170" prop="sIp_num" label="服务端IP热度" />
        <el-table-column min-width="200" prop="cert_num" label="关联证书数量">
          <template slot="header" slot-scope="scope">
            关联证书数量
            <el-tooltip popper-class="atooltip" effect="dark" content="叶子证书数量" placement="top">
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column min-width="180" prop="session_num" label="会话数量" />
        <el-table-column min-width="170" prop="alarm_num" label="告警数量">
          <template slot="header" slot-scope="scope">
            告警数量
            <el-tooltip popper-class="atooltip" effect="dark" content="告警对象是该指纹的告警个数" placement="top">
              <i class="el-icon-warning-outline" style="margin-left: 6px"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column min-width="200" prop="first_time" label="首次出现时间">
        </el-table-column>
        <el-table-column min-width="200" prop="last_time" label="末次出现时间">
        </el-table-column>
      </el-table>
    </div>
    <div class="list-foot">
      <div class="list-foot-top">
        <tablescroll :table-ref="$refs.table"></tablescroll>
      </div>
      <div class="list-foot-down">
        <div class="list-foot-down-l">*会话展示上限为<span>10,000</span>条</div>

        <div class="list-foot-down-r">
          <el-pagination :current-page="currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="page_size"
                         layout="total, sizes, prev, pager, next, jumper" :total="total" @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import tablescroll from "../../../components/TableScroll/idnex.vue";
import { getFingerlist } from "@/api/sessionList/sessionlist";
import { alllogderive } from '@/api/Conversational/conversational';
export default {
  name: "IPList",
  components: {
    tablescroll,
  },
  props: ["searchData", "fatherValue"],
  data () {
    return {
      // 分页参数
      currentPage: 1,
      page_size: 10,
      total: 0,
      // ========
      connTableData: [

      ],
      activenum: 0,
      search_list: {},
      order: {
        order_prop: "",
        order_field: "",
        asc: true,
      },

    };
  },
  watch: {

    searchData: {
      handler (val) {
        this.currentPage = 1;
        // this.filterData = {}
        this.order = {
          order_prop: "begin_time",
          order_field: "EndTime",
          asc: true,
        };
        this.limit100 = [];
        this.pageRange = [1, 10];
        this.initData();
      },
      deep: true,
      immediate:true
    }
  },
  methods: {
    // 对传给后端的数据做处理
    formatParam () {
      let param = {
        current_page: this.currentPage,
        page_size: this.page_size,
        order_field: this.order.order_field,
        asc: this.order.asc,
        query: [],
      };
      if (this.searchData.query&& this.searchData.query.length > 0) {
        param.query = param.query.concat(this.searchData.query);
      }
      this.searchData.task_id
        ? (param.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.time_range = this.searchData.time_range)
        : null;
      if ("flow/fuzzy".includes(this.searchData.type)) {
        delete param.target_filter;
      }
      try {
        param.aggr_query = false;
        for (let i of param.query) {
          for (let j of i.search) {
            if (j.target === 'Labels') {
              param.aggr_query = true;
            }
          }
        }
        let arr = {
          and: [],
          not: []
        };
        for (let i = 0; i < param.query.length; i++) {
          for (let j = 0; j < param.query[i].search.length; j++) {
            if (param.query[i].bool_search === 'and') {
              if (param.query[i].search[j].target === 'Labels') {
                arr.and.push(param.query[i].search[j].val);
              }
            }
            if (param.query[i].bool_search === 'not') {
              if (param.query[i].search[j].target === 'Labels') {
                arr.not.push(param.query[i].search[j].val);
              }
            }
          }
        }
        param.tag_query = arr;
        this.sessionParam = param;
      } catch (error) {
        console.log(error);
      }
    },
    initData () {
      this.formatParam();
      getFingerlist(this.sessionParam).then((res) => {
        if (res.err === 0) {
          this.total_real = res.data.total;
          this.total = res.data.total > 10000 ? 10000 : res.data.total;
          this.$emit(
            "update:fatherValue",
            res.data.total
          );
          this.connTableData = this.listfn(res.data.records);
          // }
        }
      });
    },
    indexMethod (index) {
      return (this.currentPage - 1) * this.page_size + index + 1;
    },
    // 对列表进行数据处理
    listfn (val) {
      let tagarr = this.$store.state.conversational.taglist01;
      val.forEach(item => {
        let newtagarr = [];
        item.first_time = this.formatDate(item.first_time);
        item.last_time = this.formatDate(item.last_time);
        if (item.tag != null && item.tag != []) {
          item.tag.forEach(labelitem => {
            tagarr.forEach(tagitem => {
              if (labelitem.tag_id == tagitem.tag_id) {
                newtagarr.push(tagitem);
              }
            });
          });
        }
        item.labels = this.formatTag(newtagarr);
      });
      return val;
    },
    // 时间戳转时间单位秒
    formatDate (val) {
      let date = new Date(val * 1000);
      let Y = date.getFullYear() + "-";
      let M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      let D =
        (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + " ";
      let h =
        (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ":";
      let m =
        (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
        ":";
      let s =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      return Y + M + D + h + m + s;
    },
    handleSizeChange (val) {
      this.limit100 = [];
      this.pageRange = [1, 10];
      this.page_size = val;
      this.initData();
    },
    handleCurrentChange (val) {
      this.currentPage = val;
      this.initData();
    },
    indexMethod (index) {
      return (this.currentPage - 1) * this.page_size + index + 1;
    },
    // 对标签列表进行数据处理
    formatTag (arr) {
      arr.sort((a, b) => {
        if (a.black_list > b.black_list) {
          return b.black_list - a.black_list;
        } else if (a.black_list == 0 && b.black_list == 0) {
          return b.white_list - a.white_list;
        }
      });

      let tags = [];

      tags = arr.map((val) => {
        let type = "";
        if (
          val.black_list >= 1 &&
          val.black_list <= 100 &&
          val.white_list !== 100
        ) {
          if (val.black_list >= 80) {
            type = "danger";
          } else {
            type = "warning";
          }
        }
        if (
          val.white_list >= 1 &&
          val.white_list <= 100 &&
          val.black_list === 0
        ) {
          if (val.white_list === 100) {
            type = "success";
          } else {
            type = "";
          }
        }
        if (val.white_list === 0 && val.black_list === 0) {
          type = "info";
        }
        // let tag_info = [val.tag_text, type, val.tag_id || 0, val.type];
        // tags.push(tag_info)

        return {
          // id: val.tag_id || 0,
          tag_text: val.tag_text,
          type,
          targetType: val.type,
          tag_id: val.tag_id,
          tag_explain: val.tag_explain,
          attribute_name: val.attribute_name,
          exist: val.exist,
        };
      });
      // 标签去重
      let fjLists = [];

      const res = new Map();

      fjLists = tags.filter(
        (item) => !res.has(item.tagText) && res.set(item.tagText, 1)
      );
      return fjLists;
    },
    // 正向快速检索
    forwardSearch (data, sign) {
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 反向快速检索
    reverseSearch (data, sign) {
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 批量检索方法正向
    searchforwardSearch (data, sign) {
      if (this.activenum == 0) {
        this.$message({
          message: "请至少选择一条数据检索",
          type: "warning",
        });
        return;
      }
      data.sort = false;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 批量检索方法反向
    searchreverseSearch (data, sign) {
      if (this.activenum == 0) {
        this.$message({
          message: "请至少选择一条数据检索",
          type: "warning",
        });
        return;
      }
      data.sort = true;
      data.sortname = sign;
      this.$store.commit("conversational/getSessionQuickSearch", data);
    },
    // 多选方法
    handleSelectionChange (val) {
      this.activenum = val.length;
      let newarr = [];
      for (let i in val) {


        newarr.push(val[i].fingerprint);
      }
      let item = {};
      // 数组转为字符串
      let str = newarr.join(",");
      item.str = str;
      this.search_list = item;
    },
    // 排序判断
    handleSortChange (val) {
      this.order.order_prop = val.prop;
      this.order.order_field = val.prop;
      if (val.prop == "FINGER") {
        this.order.order_field = "FINGER";
      }
      if (val.prop == "begin_time") {
        this.order.order_field = "StartTime";
      }
      if (val.prop == "dst_port") {
        this.order.order_field = "dPort";
      }
      if (val.prop == "src_port") {
        this.order.order_field = "sPort";
      }
      if (val.prop == "src_ip") {
        this.order.order_field = "sIp";
      }
      if (val.prop == "ippro") {
        this.order.order_field = "IPPro";
      }
      if (val.prop == "dst_ip") {
        this.order.order_field = "dIp";
      }
      if (val.prop == "end_time") {
        this.order.order_field = "EndTime";
      }
      if (val.prop == "sBytes") {
        this.order.order_field = "pkt.sPayloadBytes";
      }
      if (val.prop == "dBytes") {
        this.order.order_field = "pkt.dPayloadBytes";
      }
      if (val.order == "ascending") {
        this.order.asc = true;
      } else if (val.order == "descending") {
        this.order.asc = false;
      } else {
        this.order.order_field = "";
      }
      this.limit100 = [];
      this.pageRange = [1, 1];
      this.initData();
    },
    //日志导出
    logderive () {
      if(!this.connTableData.length){
        this.$message.error('暂无数据可导出');
        return;
      }
      let param = {
        condition: {
          current_page: this.currentPage,
          page_size: this.page_size,
          order_field: this.order.order_field,
          asc: this.order.asc,
          query: [],
          aggr_query: true
        },
        user_id: 1,
        task_type: 9,
      };
      if (JSON.stringify(this.filterData) != "{}") {
        let arr = [];
        for (let i in this.filterData) {
          arr.push(this.filterData[i]);
        }
        param.condition.query = param.condition.query.concat(arr);
      }
      if (this.searchData.query.length > 0) {
        param.condition.query = param.condition.query.concat(
          this.searchData.query
        );
      }
      this.searchData.task_id
        ? (param.condition.task_id = this.searchData.task_id)
        : null;
      this.searchData.time_range
        ? (param.condition.time_range = this.searchData.time_range)
        : null;
      param.condition.aggr_query = false;
      for (let i of param.condition.query) {
        for (let j of i.search) {
          if (j.target === 'Labels') {
            param.condition.aggr_query = true;
          }
        }
      }
      let arr = {
        and: [],
        not: []
      };
      for (let i = 0; i < param.condition.query.length; i++) {
        for (let j = 0; j < param.condition.query[i].search.length; j++) {
          if (param.condition.query[i].bool_search === 'and') {
            if (param.condition.query[i].search[j].target === 'Labels') {
              arr.and.push(param.condition.query[i].search[j].val);
            }
          }
          if (param.condition.query[i].bool_search === 'not') {
            if (param.condition.query[i].search[j].target === 'Labels') {
              arr.not.push(param.condition.query[i].search[j].val);
            }
          }
        }
      }
      param.condition.tag_query = arr;
      alllogderive(param).then((res) => {
        if (res.err == 0) {
          this.$store.commit("conversational/setdowndot", true);
          this.$message("成功导出");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  &-top {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &-l {
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: #9999a1;
      margin-right: 8px;

      span {
        color: #116ef9;
      }

      ::v-deep {
        .el-button {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 94px;
          height: 32px;
        }

        .el-checkbox-group {
          display: flex;
          flex-direction: column;
        }
      }
    }

    &-r {
      display: flex;
      align-items: center;

      ::v-deep {
        .el-button {
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .Partdown {
        .el-button {
          width: 88px;
        }

        margin-right: 8px;
      }

      .alldown {
        cursor: not-allowed;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #c0c4cc;
        margin-right: 16px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 88px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #dcdfe6;
        }

        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive {
        cursor: pointer;
        position: relative;
        width: 119px;
        height: 32px;
        border: 1px solid #8abcff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        color: #4a97ff;
        margin-right: 8px;

        &-l {
          font-weight: 400;
          font-size: 14px;
          width: 88px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #8abcff;
        }

        &-r {
          width: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 400;
          font-size: 14px;
        }
      }

      .qlactive:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
      }

      .globaldown {
        .el-button {
          width: 108px;
        }
      }
    }
  }

  &-listview {
    margin-top: 12px;

    .sortbox {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      // position: relative;
      .top {
        margin-right: 5px;
      }

      .down {
        width: 10px;
        height: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .sorttoole {
        display: none;
        padding-bottom: 5px;
        color: #116ef9;
        // position: absolute;
        // top: -2px;
        // right: 0;
      }
    }

    .sortbox:hover {
      .sorttoole {
        display: block;
        cursor: pointer;
      }
    }

    ::v-deep {
      .el-tag {
        background: #e7f0fe;
        border-radius: 2px;
        color: #1b428d;
      }

      .el-tag.el-tag--warning {
        background: #f9eddf;
        border-radius: 2px;
        color: #b76f1e;
      }

      .el-tag.el-tag--success {
        background: #e0f5ee;
        border-radius: 2px;
        color: #006157;
      }

      .el-tag.el-tag--danger {
        background: #fce7e7;
        border-radius: 2px;
        color: #a41818;
      }

      .el-tag.el-tag--info {
        background: #f2f3f7;
        border-radius: 2px;
        color: #2c2c35;
      }
    }

    .taglist {
      .tagbox {
        margin-right: 4px;
      }
    }
  }

  &-foot {
    position: sticky;
    bottom: 0px;
    z-index: 999;
    padding: 10px 24px;
    width: 100%;
    background: #ffffff;
    border-radius: 8px;

    &-top {
      margin-bottom: 10px;
    }

    &-down {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-bottom: 20;

      &-l {
        font-size: 12px;
        color: #9999a1;

        span {
          color: #000;
        }
      }
    }
  }
}
</style>
<template>
  <div v-loading="targetLoading" class="caution">
    <article class="search-box">
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="mini"
        :inline="true"
        :rules="rules"
      >
        <section class="search-box-top">
          <div class="search-box-top-cell">
            <div class="row">
              <el-form-item label="告警名称：" prop="alarm_ids">
                <el-select
                  v-model="searchForm.alarm_ids"
                  multiple
                  filterable
                  collapse-tags
                  placeholder="请选择"
                  @change="IDS_CHANGE"
                >
                  <el-option
                    v-for="(item, index) in options"
                    :key="index"
                    :label="item.alarm_name"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="任务：" prop="task_ids">
                <el-select
                  v-model="searchForm.task_ids"
                  multiple
                  filterable
                  collapse-tags
                  placeholder="请选择"
                  @remove-tag="removeTag"
                >
                  <el-option
                    v-for="item in taskOptions"
                    :key="item.taskId"
                    :label="item.taskName"
                    :value="item.taskId"
                  >
                    <el-checkbox
                      v-model="item.check"
                      style="display: block"
                      @change="isChecked(item)"
                    >
                      {{ item.taskName }}
                    </el-checkbox>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button @click="SEARCH_RESET">重置</el-button>
                <el-button type="primary" icon="el-icon-search" @click="SEARCH">
                  检索
                </el-button>
              </el-form-item>
            </div>
          </div>
          <div class="search-box-top-cell">
            <div class="row">
              <el-form-item label="时间范围：" prop="time">
                <el-date-picker
                  v-model="searchForm.time"
                  value-format="timestamp"
                  type="datetimerange"
                  :picker-options="pickerOptions"
                  range-separator="—"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  align="right"
                  :editable="false"
                  @change="TIME_CHANGE"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="告警ID:" prop="alarm_id">
                <el-input
                  v-model="searchForm.alarm_id"
                  placeholder="请输入告警ID"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <div class="search-box-top-cell-coll" @click="PACK_SEARCH">
                  <div>{{ pack ? "收起" : "展开" }}</div>
                  <svg-icon v-if="pack === false" icon-class="coll1"></svg-icon>
                  <svg-icon v-else icon-class="coll2"></svg-icon>
                </div>
              </el-form-item>
            </div>
          </div>
        </section>
        <section v-if="pack === true" class="search-box-bottom">
          <div class="search-box-top-cell">
            <div class="row">
              <el-form-item label="告警对象：" prop="target_name">
                <el-input v-model="searchForm.target_name"></el-input>
              </el-form-item>
              <el-form-item label="受害方：" prop="victim">
                <el-input v-model="searchForm.victim"></el-input>
              </el-form-item>
              <el-form-item label="处理状态：" prop="alarm_status_list">
                <el-checkbox-group v-model="searchForm.alarm_status_list">
                  <el-checkbox :label="0">未处理</el-checkbox>
                  <el-checkbox :label="1">确认</el-checkbox>
                  <el-checkbox :label="2">误报</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </div>
          </div>
          <div class="search-box-top-cell">
            <div class="row">
              <el-form-item label="威胁级别：" prop="attack_level">
                <el-select
                  v-model="searchForm.attack_level"
                  placeholder="请选择"
                  multiple
                >
                  <el-option
                    v-for="(item, index) in levelOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="攻击方：" prop="attacker_ip">
                <el-input v-model="searchForm.attacker_ip"></el-input>
              </el-form-item>
              <el-form-item> </el-form-item>
            </div>
          </div>
        </section>
      </el-form>
    </article>
    <article class="caution-num">
      <div>
        <aside></aside>
        <div class="num-text-box">
          <div>告警总数</div>
          <div>{{ THOUSAND(numData.alarm_cnt) }}</div>
        </div>
      </div>
      <div>
        <aside></aside>
        <div
          id="num-main"
          class="num-text-box"
          :style="lv3 ? 'background: #FCE7E7;' : ''"
          @click="NUM_SELECT('91-100')"
        >
          <div style="color: #a41818; background-color: #fce7e7">高危</div>
          <div>{{ THOUSAND(numData.high_level) }}</div>
        </div>
      </div>
      <div>
        <aside></aside>
        <div
          id="num-main"
          class="num-text-box"
          :style="lv2 ? 'background: #F9EDDF;' : ''"
          @click="NUM_SELECT('81-90')"
        >
          <div style="color: #b76f1e; background-color: #f9eddf">中危</div>
          <div>{{ THOUSAND(numData.middle_level) }}</div>
        </div>
      </div>
      <div>
        <aside></aside>
        <div
          id="num-main"
          class="num-text-box"
          :style="lv1 ? 'background: #F9F3DF;' : ''"
          @click="NUM_SELECT('61-80')"
        >
          <div style="color: #c29217; background-color: #f9f3df">低危</div>
          <div>{{ THOUSAND(numData.low_level) }}</div>
        </div>
      </div>
      <div>
        <aside></aside>
        <div class="num-text-box">
          <div>未处理</div>
          <div>{{ THOUSAND(numData.alarm_status0) }}</div>
        </div>
      </div>
      <div>
        <aside></aside>
        <div class="num-text-box">
          <div>确认</div>
          <div>{{ THOUSAND(numData.alarm_status1) }}</div>
        </div>
      </div>
      <div>
        <aside></aside>
        <div class="num-text-box">
          <div>误报</div>
          <div>{{ THOUSAND(numData.alarm_status2) }}</div>
        </div>
      </div>
    </article>
    <article class="tabs">
      <el-tabs v-model="activeName">
        <el-tab-pane label="模型告警" name="first">
          <div slot="label">
            <span calss="tabs-title">模型告警</span>
            <span class="tabs-num">{{ tabsNum[0] }}</span>
          </div>
          <Model
            ref="first"
            search-type="模型"
            :search-form="searchForm"
            :setp-data="setpData"
            :knowledge="options"
            :tags-data="tagsData"
            @setTabsNum="SET_TAB_NUM"
            @search="SEARCH"
          />
        </el-tab-pane>
        <el-tab-pane label="防御告警" name="second">
          <div slot="label">
            <span calss="tabs-title">防御告警</span>
            <span class="tabs-num">{{ tabsNum[1] }}</span>
          </div>
          <Defense
            ref="second"
            search-type="防御"
            :search-form="searchForm"
            :setp-data="setpData"
            :knowledge="options"
            :tags-data="tagsData"
            @setTabsNum="SET_TAB_NUM"
            @search="SEARCH"
          />
        </el-tab-pane>
        <el-tab-pane label="规则告警" name="third">
          <div slot="label">
            <span calss="tabs-title">规则告警</span>
            <span class="tabs-num">{{ tabsNum[2] }}</span>
          </div>
          <Rule
            ref="third"
            search-type="规则"
            :search-form="searchForm"
            :setp-data="setpData"
            :knowledge="options"
            :tags-data="tagsData"
            @setTabsNum="SET_TAB_NUM"
            @search="SEARCH"
          />
        </el-tab-pane>
      </el-tabs>
    </article>
  </div>
</template>

<script>
import Model from "./components/model.vue";
import Defense from "./components/defense.vue";
import Rule from "./components/rule.vue";
import { knowledge, target_agg, type_agg, get_tags,dictAlarm} from "@/api/caution";
import { getTasklist } from "@/api/Conversational/conversational";
import { dictBook } from "@/api/user";
import { mapState } from "vuex";
export default {
  name: "Caution",
  components: {
    Model,
    Defense,
    Rule,
  },
  data() {
    return {
      alarm_model_attack_chain: [],
      activeName: "first",
      searchTime: "",
      searchForm: {
        task_ids: [],
        alarm_ids: [],
        time: [],
        left: "",
        right: "",
        target_name: "",
        victim: "",
        attack_level: [],
        attacker_ip: "",
        alarm_status_list: [0, 1, 2],
      },
      setpData: [],
      // 校验
      rules: {
        task_ids: [
          { required: true, message: "至少选择一个任务", trigger: "change" },
        ],
      },
      // 统计数据
      numData: {},
      lv1: false,
      lv2: false,
      lv3: false,
      // 告警知识库列表
      options: [
        {
          id: "N/A",
          alarm_name: "N/A",
        },
      ],
      taskOptions: [],
      // 威胁级别
      levelOptions: [
        {
          value: "61-80",
          label: "低危",
        },
        {
          value: "81-90",
          label: "中危",
        },
        {
          value: "91-100",
          label: "高危",
        },
      ],
      // 搜索loading
      targetLoading: false,
      checkList: [],
      tabsNum: [],
      // 时间日期选择快捷选项
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      // 展开 or 收起
      pack: false,
      // 标签库数据
      tagsData: [],
    };
  },
  computed: {
    ...mapState("long", ["Dict"]),
  },
  watch: {
    "searchForm.attack_level": {
      handler(newValue, oldValue) {
        if (newValue.indexOf("91-100") > -1) {
          this.lv3 = true;
        } else {
          this.lv3 = false;
        }
        if (newValue.indexOf("81-90") > -1) {
          this.lv2 = true;
        } else {
          this.lv2 = false;
        }
        if (newValue.indexOf("61-80") > -1) {
          this.lv1 = true;
        } else {
          this.lv1 = false;
        }
      },
      deep: true,
    },
    activeName(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs[val].GET_LIST();
        });
      }
    },
  },
  created() {
    this.getTask();
    this.dictBook();
    this.GET_KNOWLEDGE();
    this.GET_TAGS();
  },
  methods: {
    removeTag(tag){
      this.taskOptions.find(item=>item.taskId===tag).check=false;
    },
    PACK_SEARCH() {
      this.pack = !this.pack;
    },
    // 获取任务下拉列表
    async getTask() {
      try {
        const res = await getTasklist();
        this.taskOptions = res.data;
        // 如果任务为空时，去第一默认值
        if (this.searchForm.task_ids.length === 0) {
          this.searchForm.task_ids = [this.taskOptions[0].taskId];
          this.taskOptions[0].check = true;
        }
        this.SEARCH();
      } catch (error) {
        console.log(error);
      }
    },
    // 多选框触发
    isChecked(item) {
      if (item.check && this.searchForm.task_ids.indexOf(item.value) == -1) {
        this.searchForm.task_ids.push(item.taskId);
      } else if (!item.check) {
        this.searchForm.task_ids.forEach((elm, idx) => {
          if (elm == item.taskId) {
            this.searchForm.task_ids.splice(idx, 1);
          }
        });
      }
      let box = [];
      this.taskOptions.forEach((item) => {
        if (item.check) {
          box.push(item);
        }
      });
    },
    GET_KNOWLEDGE() {
      knowledge().then((res) => {
        if (res.err === 0) {
          this.options = res.data;
        }
      });
    },
    GET_TAGS() {
      get_tags().then((res) => {
        this.tagsData = res.data;
      });
    },
    async dictBook() {
      const res = await dictAlarm();
      this.alarm_model_attack_chain = res.data;
    },
    // 点击检索
    SEARCH() {
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.TYPE_AGG();
          this.targetLoading = true;
          let data = {
            task_ids: this.searchForm.task_ids,
            alarm_ids: this.searchForm.alarm_ids,
            target_name: this.searchForm.target_name,
            victim: this.searchForm.victim,
            attack_levels: this.searchForm.attack_level,
            attacker_ip: this.searchForm.attacker_ip,
            alarm_status_list: this.searchForm.alarm_status_list,
          };
          if (this.searchForm.time?.length > 1) {
            data.left = this.searchForm.time[0] / 1000;
            data.right = this.searchForm.time[1] / 1000;
          } else {
            data.left = "";
            data.right = "";
          }
          target_agg(data)
            .then((res) => {
              if (res.err === 0) {
                this.numData = res.data;
                this.$refs[this.activeName]?.GET_LIST();
                this.$refs[this.activeName].currentPage=1;
              }
              this.targetLoading = false;
            })
            .catch((err) => {
              this.targetLoading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 刷新tabs统计数字
    SET_TAB_NUM(tab) {
      switch (tab) {
      case 1:
        this.$set(this.tabsNum, 0, this.$refs.first.pageTotal);
        break;

      case 2:
        this.$set(this.tabsNum, 1, this.$refs.second.pageTotal);
        break;
      case 3:
        this.$set(this.tabsNum, 2, this.$refs.third.pageTotal);

        break;
      default:
        break;
      }
    },
    // 检索重置
    SEARCH_RESET() {
      this.$refs.searchForm.resetFields();
      this.searchForm.target_name="";
      this.searchForm.victim="";
      this.searchForm.attack_level=[];
      this.searchForm.alarm_status_list=[0,1,2];
      this.searchForm.attacker_ip="";
      this.searchForm.task_ids = [this.taskOptions[0].taskId];
      this.taskOptions.forEach(item=>item.check=false);
      this.taskOptions[0].check = true;
      this.$refs.first.STEP_RESET();
    },
    // 告警名称选择
    IDS_CHANGE(val) {
      this.$refs.first.IDS_CHANGE(val);
    },
    // 选择时间段触发
    TIME_CHANGE(val) {
      if (val === null) {
        this.searchForm.time = [];
        this.searchForm.left = "";
        this.searchForm.right = "";
        // this.$refs.searchForm.resetFields('time');
      } else {
        this.searchForm.left = val[0];
        this.searchForm.right = val[1];
      }
    },
    // 类型聚合更新
    TYPE_AGG() {
      let data = {
        task_ids: this.searchForm.task_ids,
        alarm_ids: this.searchForm.alarm_ids,
        target_name: this.searchForm.target_name,
        victim: this.searchForm.victim,
        attack_levels: this.searchForm.attack_level,
        attacker_ip: this.searchForm.attacker_ip,
        alarm_status_list: this.searchForm.alarm_status_list,
      };
      if (this.searchForm.time?.length > 1) {
        data.left = this.searchForm.time[0] / 1000;
        data.right = this.searchForm.time[1] / 1000;
      } else {
        data.left = 0;
        data.right = 0;
      }
      type_agg(data).then((res) => {
        if (res.err === 0) {
          if (res.data != 0) {
            let data;
            data = res.data;
            // 数据组装
            this.alarm_model_attack_chain.forEach((all) => {
              all.attack_chain_cnt=0;
              data.forEach((item) => {
                if (all.attack_chain_name === item.attack_chain_name) {
                  all.attack_chain_cnt = item.attack_chain_cnt;
                  all.alarm_knowledge_list.forEach((x) => {
                    x.alarm_knowledge_cnt=0;
                    item.alarm_knowledge_list.forEach((y) => {
                      if (x.alarm_knowledge_name === y.alarm_knowledge_name) {
                        x.alarm_knowledge_cnt = y.alarm_knowledge_cnt;
                      }
                    });
                  });
                }
              });
            });
            const setpData = this.alarm_model_attack_chain.map((i) => ({
              name: i.attack_chain_name,
              num: i.attack_chain_cnt,
              active: false,
              tags: i.alarm_knowledge_list
                .sort((a, b) => b.alarm_knowledge_cnt - a.alarm_knowledge_cnt)
                .map((item) => ({
                  name: item.alarm_knowledge_name,
                  id: item.alarm_knowledge_id,
                  active: this.searchForm.alarm_ids.includes(item.alarm_knowledge_id),
                  alarm_knowledge_cnt: item.alarm_knowledge_cnt,
                })),
            }));
            // 需要通过tags下面的active值来判断第一层的active状态
            setpData.forEach(item=>{
              item.active=item.tags.every(child=>child.active);
            });
            this.setpData = setpData;
          } else {
            this.tabsNum = [0, 0, 0];
            this.setpData = [];
          }
        }
      });
    },
    // 告警等级多选框与统计框联动
    NUM_SELECT(data) {
      let dataIndex = this.searchForm.attack_level.indexOf(data);
      if (dataIndex > -1) {
        this.searchForm.attack_level.splice(dataIndex, 1);
      } else {
        this.searchForm.attack_level.push(data);
      }
      this.$refs.first.currentPage = 1;
      this.$refs.second.currentPage = 1;
      this.$refs.third.currentPage = 1;
      this.SEARCH();
    },
    // 千分位加逗号
    THOUSAND(num) {
      if (num) {
        return num.toLocaleString();
      } else {
        return "-";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.caution {
  &-title {
    font-size: 14px;
    color: #2c2c35;
    margin: 16px 0;
    font-weight: 500;
  }

  .search-box {
    width: 100%;
    background: #ffffff;
    // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    .el-form {
      ::v-deep .el-input__inner {
        padding-right: 2px;
      }

      ::v-deep .el-form-item__label {
        font-size: 14px;
        color: #767684;
        font-weight: 500;
      }

      ::v-deep .el-range-input {
        font-size: 14px;
      }
    }

    section {
      padding: 20px 24px;
      width: 100%;
      height: 116px;
    }

    &-top {
      &-cell {
        height: 50%;
        .row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .el-form-item {
            flex: 1;
            .el-select,
            .el-input,
            .el-date-editor {
              width: 20vw;
            }
            &:last-child {
              text-align: right;
            }
          }
        }

        &-2 {
          background: green;
          color: #116ef9;
          width: 30%;
          > span:nth-of-type(2) {
            background: #116ef9;
            transform: matrix(1, 0, 0, -1, 0, 0);
          }
        }

        &-coll {
          width: 50px;
          color: #116ef9;
          font-size: 10px;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          cursor: pointer;

          > div {
            font-size: 14px;
            margin-right: 5px;
          }
        }

        &-btn {
          .el-button--default {
            width: 60px;
            height: 32px;
            background: #ffffff;
            border: 1px solid #dee0e7;
            border-radius: 4px;
            padding: 0;
          }

          .el-button--primary {
            width: 80px;
            height: 32px;
            background: #116ef9;
            border-radius: 4px;
            padding: 0;
          }
        }

        &-state {
          .el-form-item {
            margin: 0;

            .el-checkbox {
              color: #2c2c35;
            }

            ::v-deep.el-checkbox__input.is-checked + .el-checkbox__label {
              color: #2c2c35;
            }

            ::v-deep.el-checkbox__input.is-checked .el-checkbox__inner,
            .el-checkbox__input.is-indeterminate .el-checkbox__inner {
              background-color: #116ef9;
              border-color: #116ef9;
            }
          }
        }
      }
    }

    &-bottom {
      border-top: 1px solid #f2f3f7;
      box-sizing: border-box;
    }
  }

  .caution-num {
    width: 100%;
    height: 48px;
    background: #ffffff;
    // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    margin: 16px 0;
    display: flex;
    align-items: center;

    > div {
      height: 100%;
      flex: 3;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      > aside {
        width: 1px;
        height: 16px;
        background: #dee0e7;
        margin: 0 4px;
      }

      #num-main {
        cursor: pointer;
      }

      .num-text-box {
        width: 100%;
        height: 80%;
        box-sizing: border-box;
        // padding: 4px 4px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        border-radius: 4px;

        > div:nth-of-type(1) {
          font-size: 12px;
          color: #767684;
          margin: 0 8px 0 8px;
          padding: 3px 5px;
          border-radius: 2px;
        }

        > div:nth-of-type(2) {
          font-size: 16px;
          color: #2c2c35;
          font-weight: bold;
        }
      }
    }

    > div:nth-of-type(1) {
      > aside {
        width: 0;
      }
    }

    // > div::before {
    //   content: '';
    //   position: absolute;
    //   // left: -4px;
    //   width: 1px;
    //   height: 16px;
    //   // border: 1px solid #dee0e7;
    //   background: #dee0e7;
    // }
    > div:nth-of-type(1)::before {
      display: none;
    }

    > div:nth-of-type(1) {
      flex: 4;
    }

    > div:nth-of-type(2),
    div:nth-of-type(3),
    div:nth-of-type(4) {
      > div:nth-of-type(1) {
        font-size: 12px;

        padding: 2px 4px;
        border-radius: 2px;
      }
    }

    > div:nth-last-of-type(1) {
      border: 0;
    }
  }

  .tabs {
    width: 100%;
    min-height: 500px;
    background: #ffffff;
    // box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    ::v-deep .el-tabs__content {
      overflow: visible;
    }

    ::v-deep .el-tabs__nav-scroll {
      padding-left: 24px;
    }

    ::v-deep.el-tabs__item.is-active {
      color: #116ef9;
    }

    ::v-deep .el-tabs__active-bar {
      background-color: #116ef9;
      width: 40px !important;
    }

    .tabs-num {
      color: #9999a1;
    }

    ::v-deep .el-tabs__item {
      color: #2c2c35;
    }

    ::v-deep .el-tabs__nav-wrap::after {
      height: 1px;
    }

    ::v-deep .el-tabs__nav {
      height: 54px;
      display: flex;
      align-items: center;
    }
  }
}

.el-button--primary {
  border: 0;
  color: #ffffff;
}

::v-deep .el-select__tags-text {
  overflow: initial;
  text-overflow: initial;
}
::v-deep .el-form-item__label {
  padding: 0;
  width: 69px; // 统一label宽度
  text-align: left;
}
</style>
import Vue from 'vue';
import Vuex from 'vuex';
import getters from './getters';
import app from './modules/app';
import settings from './modules/settings';
import user from './modules/user';
import long from './modules/long';
import conversational from './modules/conversational';
// import persistedState from 'vuex-persistedstate';
// console.log(window.sessionStorage,'window.sessionStorage');

import ifonconduct from './modules/ifonconduct';
Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    conversational,
    long,
    app,
    settings,
    user,
    ifonconduct
  },
  getters,
  // plugins: [persistedState({
  //   storage: window.sessionStorage
  // })]
});

export default store;

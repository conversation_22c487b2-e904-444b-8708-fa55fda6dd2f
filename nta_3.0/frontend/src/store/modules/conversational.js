const conversational = {
  namespaced: true, //开启namespace:true，该模块就成为命名空间模块了
  state: {
    TagList: [],
    taskStatus: {},
    esoptions: '',
    esquery: '',
    taglisttrue: [],
    taglist01: [],
    taglist02: [],
    tagdata01: {},
    // 下载抽屉标识
    downDrawer: false,
    // 下载红点控制
    downdot: false,
    // 下载列表
    downLits: [],
    protocolInfofliter: '',
    dialogueListfliter: '',
    // 会话详情五元组数据
    sessiondetaildata: {},
    // 会话日志
    sessionLog: {},
    // 协议元数据
    sessionAgreement: {},
    // 包分析
    packethistogram: {},
    // 快速检索条件
    getQuickSearchData: {},
    // 时间快速检索
    timeQuickSearchData: '',
    jsonedit: '',
    requestsign: true,
    // ip详情数据
    ipdata: {}
  },
  mutations: {
    setrequestsign(state, val) {
      state.requestsign = val;
    },
    searchDataPos(state, val) {
      state.tagdata01 = val;
    },
    tagseachlist(state, val) {
      state.taglist01 = val;
    },
    all_tag_category(state, val) {
      state.taglist02 = val;
    },
    taglisttrueData(state, val) {
      state.taglisttrue = val;
    },
    TagListData(state, val) {
      state.TagList = val;
    },
    TASK_STATUS: (state, status) => {
      state.taskStatus = status;
    },
    // 存贮IP详情
    setIpdata(state, val) {
      state.ipdata = val;
    },
    esoptionsData(state, val) {
      state.esoptions = val;
    },

    esqueryData(state, val) {
      state.esquery = val;
    },
    SYSTEM_INFO: (state, info) => {
      state.system = info.name;
      state.isForensics = info.isForensics;
      state.taskStatus = info.task;
    },
    downDrawerData(state, val) {
      state.downDrawer = val;
    },
    setdowndot(state, val) {
      state.downdot = val;
    },
    getdownlist(state, val) {
      state.downLits = val;
    },
    // 获取点击的元数据名字标识
    protocolInfofliterData(state, val) {
      state.protocolInfofliter = val;
    },
    // 获取聚合项名字标识
    dialogueListFliterData(state, val) {
      state.dialogueListfliter = val;
    },
    // 获取会话详情信息
    sessionDetailData(state, val) {
      state.sessiondetaildata = val;
    },
    // 获取会话日志信息
    sessionDetailLog(state, val) {
      state.sessionLog = val;
    },
    // 获取到协议元数据
    sessionDetailAgreement(state, val) {
      state.sessionAgreement = val;
    },
    // 获取到包分析数据
    sessionDetailHistogram(state, val) {
      state.packethistogram = val;
    },
    // 获取会话列表中的快速检索条件
    getSessionQuickSearch(state, val) {
      state.getQuickSearchData = val;
    },
    // 获取到时间快速检索条件
    getTimeQuickSearch(state, val) {
      state.timeQuickSearchData = val;
    },
    jsoneditData(state, val) {
      state.jsonedit = val;
    },
    // 获取当前主从任务的信息
    getCurrentTask(state, val) {
      state.CurrentTask = val;
    }
  }
};

export default conversational;

import {
  login,
  logout,
  getInfo
} from '@/api/user';
import {
  getToken,
  setToken,
  removeToken
} from '@/utils/auth';
import {
  resetRouter
} from '@/router';

const getDefaultState = () => {
  return {
    token: getToken(),
    name: '',
    avatar: '',
    show_username:''
  };
};

const state = getDefaultState();

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState());
  },
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_NAME: (state, name) => {
    state.name = name;
  },
  SET_SHOW_USERNAME: (state, show_username) => {
    state.show_username = show_username;
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar;
  }
};

const actions = {
  // user login
  login({
    commit
  }, userInfo) {
    const {
      username,
      password
    } = userInfo;
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password }).then(response => {
        const { data } = response;
        commit('SET_TOKEN', data.token);
        commit('SET_NAME', data.username);
        commit('SET_SHOW_USERNAME', data.show_username);
        setToken(data.token);
        resolve();
      }).catch(error => {
        reject(error);
      });
    });
    // return new Promise((resolve, rejuect) => {
    //   setTimeout(() => {
    //     const data = {
    //       token: "admin-token"
    //     }
    //     commit('SET_TOKEN', data.token)
    //     setToken(data.token)
    //     resolve()
    //   }, 1000);
    // })


  },

  // get user info
  getInfo({
    commit,
    state
  }) {
    return new Promise((resolve, reject) => {
      // getInfo(state.token).then(response => {
      //   // const {
      //   //   data
      //   // } = response
      //   const data = {
      //     name:'super-admin',
      //     avatar:'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
      //   }
      //   if (!data) {
      //     return reject('Verification failed, please Login again.')
      //   }
      //   const {
      //     name,
      //     avatar
      //   } = data

      //   commit('SET_NAME', name)
      //   commit('SET_AVATAR', avatar)
      //   resolve(data)
      // }).catch(error => {
      //   reject(error)
      // })
      const data = {
        name: 'super-admin',
        avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif'
      };
      const {
        name,
        avatar
      } = data;
      commit('SET_NAME', name);
      commit('SET_AVATAR', avatar);
      resolve(data);
    });
  },

  // user logout
  logout({
    commit,
    state
  }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        removeToken(); // must remove  token  first
        resetRouter();
        commit('RESET_STATE');
        resolve();
      }).catch(error => {
        reject(error);
      });
    });
  },

  // remove token
  resetToken({
    commit
  }) {
    return new Promise(resolve => {
      removeToken(); // must remove  token  first
      commit('RESET_STATE');
      resolve();
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};

const ifonconduct = {
  namespaced: true, //开启namespace:true，该模块就成为命名空间模块了
  state: {
    dialogopne: false,
    drive: false,
    // 控制读取磁盘显示隐藏
    Readdata: false,
    driveData: {
      raid:''
    },
    diskstatus: {},
    clearstatus: {},
    // 重置次数统计
    num: 0,
    // 磁盘操作状态
    diskmode: "换盘模式",
  },
  getters: {

  },
  mutations: {
    resetNumber(state, val) {
      state.num = val;
      console.log(val, "78978989777777778778777879789");
    },
    getdailogopendata(state, val) {
      state.dialogopne = val;
    },
    Replacingdrive(state, val) {
      state.drive = val;
    },
    getdrivedata(state, val) {
      state.driveData = val;
    },
    getdiskstatus(state, val) {
      state.diskstatus = val;
    },
    getdiskclaerstatus(state, val) {
      state.clearstatus = val;
    },
    // 获取当前磁盘操作状态
    getdiskmode(state, val) {
      state.diskmode = val;
    },
    Readdiskdata(state, val) {
      state.Readdata = val;
    }

  },
  actions: {

  }
};

export default ifonconduct;

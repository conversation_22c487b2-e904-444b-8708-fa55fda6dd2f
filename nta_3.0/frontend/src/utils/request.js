import axios from 'axios';
import {
  MessageBox,
  Message
} from 'element-ui';
import store from '@/store';
import {
  getToken
} from '@/utils/auth';
let baseURL;
if (process.env.VUE_APP_BASE_API === '') {
  baseURL = `${window.location.origin}/api`;
} else {
  baseURL = process.env.VUE_APP_BASE_API;
}
const service = axios.create({
  baseURL: baseURL, // url = base url + request url
  timeout: 500000 // request timeout
});
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers['token'] = getToken();
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
service.interceptors.response.use(
  response => {
    const res = response.data;
    if (res.err !== 0) {
      if (res.err === 1002 || res.err === 1001) {
        // to re-login
        MessageBox.confirm('是否重新登录', '登录已失效', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          location.reload();
          store.dispatch('user/resetToken').then(() => {

          });
        });
      } else if (res.err == 40013) {
        Message({
          message: res.msg || 'Error:服务器错误',
          type: 'info',
          duration: 5 * 1000
        });
      } else {
        Message({
          message: res.msg || 'Error:服务器错误',
          type: 'error',
          duration: 5 * 1000
        });
      }


      return Promise.reject(new Error(res.msg || 'Error'));
    } else {
      return res;
    }
  },
  error => {
    console.log('err' + error); // for debug
    Message({
      message: error.msg || 'Error:服务器错误',
      type: 'error',
      duration: 5 * 1000
    });
    return Promise.reject(error);
  }
);

export default service;

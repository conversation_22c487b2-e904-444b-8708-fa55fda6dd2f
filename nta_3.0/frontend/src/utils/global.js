import dayjs from "dayjs";
// 处理时间 [ processing time ]
export function processingTime(value, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!value) return '-';
  if (String(value).length <= 10) {
    value = value * 1000;
  }
  return dayjs(value).format(format);
}
/**
 * 时间格式化02
 */
export function formatDateOne(value, format = "YYYY-MM-DD HH:mm:ss") {
  return value ? dayjs(value).format(format) : "-";
}

/**
 * 单位
 */
export function unitValue(value, digit = 2) {
  let units = ['字节','KB', 'MB', 'GB', 'TB', 'PB'];
  let unitIndex = 0;
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024;
    unitIndex++;
  }
  return `${value.toFixed(digit)} ${units[unitIndex]}`;
}
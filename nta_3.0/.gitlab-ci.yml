image: maven:3.9-eclipse-temurin-21

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository -Dhttps.protocols=TLSv1.2"
  HARBOR_USERNAME: $HARBOR_USERNAME
  HARBOR_PASSWORD: $HARBOR_PASSWORD
  DOCKER_REGISTRY: "hb.gs.lan"

# 定义缓存，加速构建
cache:
  paths:
    - .m2/repository/
    - target/

stages:
  - build
  - test
  - package
  - deploy

# 构建阶段：编译代码
build:
  stage: build
  script:
    - mvn clean compile

# 测试阶段：运行单元测试
test:
  stage: test
  script:
    - mvn test

# 打包阶段：构建Docker镜像和Helm chart
package:
  stage: package
  script:
    - mvn package
    - mvn docker:build
  artifacts:
    paths:
      - "*/target/*.jar"
      - "target/*.tgz"
    expire_in: 1 week

# 部署阶段：推送Docker镜像和Helm chart到Harbor
deploy:
  stage: deploy
  script:
    - echo "登录到Docker Registry..."
    - docker login -u $HARBOR_USERNAME -p $HARBOR_PASSWORD $DOCKER_REGISTRY
    - echo "推送Docker镜像..."
    - mvn docker:push
    - echo "推送Helm chart到Harbor..."
    - mvn deploy -DskipTests
  only:
    - master  # 只在master分支上执行部署
    - tags    # 或者在创建标签时执行部署
  when: manual  # 设置为手动触发，以便控制何时部署

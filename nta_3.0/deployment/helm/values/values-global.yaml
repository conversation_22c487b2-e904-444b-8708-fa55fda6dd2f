# values-global.yaml - 全局配置文件
# 包含全局设置和功能特性开关

# 功能特性开关 - 控制不同功能模块的启用/禁用
features:
  # 图谱功能
  graph:
    enabled: true
    # 图谱功能描述
    description: "网络流量图谱分析功能，提供网络实体关系可视化和分析能力"

  # 监控功能 - 包括 Prometheus、Grafana 和 AlertManager
  monitoring:
    enabled: true
    # 监控功能描述
    description: "系统监控功能，提供服务健康状态、性能指标和告警通知"

# Global values - 全局配置，用于整个chart
global:
  # Docker registry where images are stored
  registry: hb.gs.lan
  # Image tag to use for all services
  tag: 3.0.0
  # Namespace to deploy resources
  namespace: nta
  # Domain name for ingress
  domain: nta.geeksec.com
  # Helm files path - path to files directory relative to chart
  helmFilesPath: "files"
  # Resource defaults - 默认资源配置，可被服务特定配置覆盖
  resources:
    requests:
      memory: "768Mi"
      cpu: "300m"
    limits:
      memory: "1.5Gi"
      cpu: "800m"
  # Java options
  javaOpts: "-XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication"
  # Spring profiles
  springProfiles: "prod"
  # 时区设置
  timezone: "Asia/Shanghai"
  # 安全设置
  security:
    # 是否启用 TLS
    tlsEnabled: true
    # 是否启用 RBAC
    rbacEnabled: true

  # MinIO 全局配置 - 所有服务都可以使用
  minio:
    enabled: true
    endpoint: "http://minio.{{ .Release.Namespace }}.svc.cluster.local:9000"
    defaultBucket: "certificates"
    region: "us-east-1"
    pathStyleAccess: true

# 通用服务配置 - 适用于所有服务
services:
  common:
    # 自动缩扩容配置 - 默认配置，可被各服务覆盖
    autoscaling:
      enabled: false        # 默认不启用自动缩扩容
      minReplicas: 1        # 最小副本数
      maxReplicas: 5        # 最大副本数
      metrics:              # 触发缩扩容的指标
        cpu: 80             # CPU使用率目标值（百分比）
        memory: 80          # 内存使用率目标值（百分比）
      behavior:             # 缩扩容行为配置
        scaleUp:            # 扩容配置
          stabilizationWindowSeconds: 300  # 稳定窗口期（秒）
          percentValue: 100               # 百分比增长值
          podsValue: 4                    # 每次最多增加的Pod数
          periodSeconds: 60               # 策略周期（秒）
          selectPolicy: "Max"             # 策略选择（Max/Min/Disabled）
        scaleDown:          # 缩容配置
          stabilizationWindowSeconds: 300  # 稳定窗口期（秒）
          percentValue: 100               # 百分比减少值
          podsValue: 2                    # 每次最多减少的Pod数
          periodSeconds: 60               # 策略周期（秒）
          selectPolicy: "Max"             # 策略选择（Max/Min/Disabled）

    # Pod Disruption Budget配置 - 默认配置，可被各服务覆盖
    pdb:
      enabled: true         # 默认启用PDB
      maxUnavailable: 1     # 最大不可用Pod数量
      # 或者使用minAvailable: "50%"  # 最小可用Pod百分比

# 初始化配置
# 控制系统初始化作业的行为
initialization:
  enabled: true
  # 通用资源配置
  resources:
    requests:
      memory: "256Mi"
      cpu: "100m"
    limits:
      memory: "512Mi"
      cpu: "200m"
  # Elasticsearch初始化
  elasticsearch:
    enabled: true
    # 只初始化cert_system索引
    systemBuiltInCertificates:
      template: cert_template
  # Nebula Graph初始化

  # Doris初始化
  doris:
    enabled: true
    image:
      repository: nta/init-tools
      tag: latest
  # Redis初始化
  redis:
    enabled: true
    image:
      repository: nta/init-tools
      tag: latest
  # Kafka初始化
  kafka:
    enabled: true

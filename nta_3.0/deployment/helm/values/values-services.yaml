# values-services.yaml - 服务配置文件
# 包含所有微服务的配置信息

# Service-specific configurations
# Each service can override global settings and has its own specific configuration
services:
  frontend:
    enabled: true
    name: frontend
    port: 80
    replicas: 2
    image:
      repository: nta/frontend
    resources: {}
    # 启用自动缩扩容 - 前端服务需要根据用户访问量自动扩缩容
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      metrics:
        cpu: 70
        memory: 70
      # 自定义指标 - 可选，基于请求速率进行扩缩容
      custom:
        - name: http_requests_per_second
          targetValue: 100
      behavior:
        scaleUp:
          stabilizationWindowSeconds: 60  # 快速响应用户访问增长
        scaleDown:
          stabilizationWindowSeconds: 300  # 缓慢缩容，避免频繁波动

    # 前端服务的PDB配置 - 确保至少有50%的Pod可用
    pdb:
      enabled: true
      minAvailable: "50%"  # 确保至少有一半的Pod可用

  auth-service:
    enabled: true
    name: auth-service
    port: 8081
    replicas: 2
    image:
      repository: nta/auth-service
    database: auth_db
    resources: {}
    # 依赖服务配置 - 引用基础设施配置
    dependencies:
      - name: postgresql
        type: postgresql
        config: "{{ .Values.infrastructure.postgresql.dependency }}"

  graph-service:
    enabled: true
    name: graph-service
    port: 8083
    replicas: 2
    image:
      repository: nta/graph-service
    database: graph_db
    resources: {}
    # 依赖服务配置 - 引用基础设施配置
    dependencies:
      - name: postgresql
        type: postgresql
        config: "{{ .Values.infrastructure.postgresql.dependency }}"
      - name: nebula-graphd
        type: nebula
        config: "{{ .Values.infrastructure.nebula.dependency }}"
      - name: kafka
        type: kafka
        config: "{{ .Values.infrastructure.kafka.dependency }}"
    extraEnv:
      - name: KAFKA_CLIENT_USER
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: username
      - name: KAFKA_CLIENT_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: password
      - name: NEBULA_HOSTS
        value: "{{ .Release.Name }}-nebula-graphd-0.{{ .Release.Name }}-nebula-graphd.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.graphd.port }}"
      - name: NEBULA_META_HOSTS
        value: "{{ .Release.Name }}-nebula-metad-0.{{ .Release.Name }}-nebula-metad.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.metad.port }}"
      - name: NEBULA_STORAGE_HOSTS
        value: "{{ .Release.Name }}-nebula-storaged-0.{{ .Release.Name }}-nebula-storaged.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.storaged.port }},{{ .Release.Name }}-nebula-storaged-1.{{ .Release.Name }}-nebula-storaged.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.storaged.port }},{{ .Release.Name }}-nebula-storaged-2.{{ .Release.Name }}-nebula-storaged.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.nebula.storaged.port }}"
      - name: NEBULA_USERNAME
        value: "{{ .Values.infrastructure.nebula.credentials.username }}"
      - name: NEBULA_PASSWORD
        valueFrom:
          secretKeyRef:
            name: nebula-credentials
            key: password
      - name: NEBULA_SPACE
        value: "{{ .Values.infrastructure.nebula.space.name }}"

  task-service:
    enabled: true
    name: task-service
    port: 8086
    replicas: 2
    image:
      repository: nta/task-service
    database: task_db
    resources: {}
    # 依赖服务配置 - 引用基础设施配置
    dependencies:
      - name: postgresql
        type: postgresql
        config: "{{ .Values.infrastructure.postgresql.dependency }}"

  config-service:
    enabled: true
    name: config-service
    port: 8087
    replicas: 2
    image:
      repository: nta/config-service
    database: config_db
    resources: {}
    # 依赖服务配置 - 引用基础设施配置
    dependencies:
      - name: postgresql
        type: postgresql
        config: "{{ .Values.infrastructure.postgresql.dependency }}"

  system-service:
    enabled: true
    name: system-service
    port: 8088
    replicas: 2
    image:
      repository: nta/system-service
    database: system_db
    resources: {}
    # 依赖服务配置 - 引用基础设施配置
    dependencies:
      - name: postgresql
        type: postgresql
        config: "{{ .Values.infrastructure.postgresql.dependency }}"

  knowledge-base-service:
    enabled: true
    name: knowledge-base-service
    port: 8090
    replicas: 2
    image:
      repository: nta/knowledge-base
    database: nta_knowledge
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
    # 依赖服务配置 - 引用基础设施配置
    dependencies:
      - name: postgresql
        type: postgresql
        config: "{{ .Values.infrastructure.postgresql.dependency }}"
      - name: redis
        type: redis
        config: "{{ .Values.infrastructure.redis.dependency }}"
    # 启用自动缩扩容 - 知识库服务可能会有高频查询
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      metrics:
        cpu: 70
        memory: 80
      behavior:
        scaleUp:
          stabilizationWindowSeconds: 60
          podsValue: 2
        scaleDown:
          stabilizationWindowSeconds: 300
    # PDB配置 - 确保高可用
    pdb:
      enabled: true
      minAvailable: 1
    extraEnv:
      - name: DB_HOST
        value: "postgresql"
      - name: DB_PORT
        value: "5432"
      - name: DB_NAME
        value: "nta_knowledge"
      - name: DB_USERNAME
        valueFrom:
          secretKeyRef:
            name: postgresql-credentials
            key: username
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            name: postgresql-credentials
            key: password
      - name: REDIS_HOST
        value: "redis"
      - name: REDIS_PORT
        value: "6379"
      - name: REDIS_PASSWORD
        valueFrom:
          secretKeyRef:
            name: redis-credentials
            key: password

  # 新增服务配置
  alarm-service:
    enabled: true
    name: alarm-service
    port: 8091
    replicas: 2
    image:
      repository: nta/alarm-service
    database: alarm_db
    resources: {}
    # 依赖服务配置 - 引用基础设施配置
    dependencies:
      - name: postgresql
        type: postgresql
        config: "{{ .Values.infrastructure.postgresql.dependency }}"
      - name: kafka
        type: kafka
        config: "{{ .Values.infrastructure.kafka.dependency }}"
    # 告警服务需要连接Kafka进行消息处理，告警记录存储在PostgreSQL中
    extraEnv:
      - name: KAFKA_CLIENT_USER
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: username
      - name: KAFKA_CLIENT_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: password

  cert-service:
    enabled: true
    name: cert-service
    port: 8092
    replicas: 2
    image:
      repository: nta/cert-service
    database: nta  # 使用Doris的nta数据库
    resources: {}
    # 证书服务需要连接MinIO存储证书文件，连接Doris存储证书元数据，连接PostgreSQL存储标签数据
    extraEnv:
      - name: MINIO_ENDPOINT
        value: "{{ .Values.infrastructure.minio.endpoint }}"
      - name: MINIO_ACCESS_KEY
        valueFrom:
          secretKeyRef:
            name: minio-credentials
            key: access-key
      - name: MINIO_SECRET_KEY
        valueFrom:
          secretKeyRef:
            name: minio-credentials
            key: secret-key
      - name: MINIO_BUCKET
        value: "certificates"
      - name: DORIS_USERNAME
        valueFrom:
          secretKeyRef:
            name: doris-credentials
            key: username
      - name: DORIS_PASSWORD
        valueFrom:
          secretKeyRef:
            name: doris-credentials
            key: password
      - name: DB_USERNAME
        valueFrom:
          secretKeyRef:
            name: postgresql-credentials
            key: username
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            name: postgresql-credentials
            key: password

  metadata-service:
    enabled: true
    name: metadata-service
    port: 8093
    replicas: 2
    image:
      repository: nta/metadata-service
    database: metadata_db
    resources: {}
    # 元数据服务可能需要连接多种数据源
    extraEnv:
      - name: ELASTICSEARCH_HOST
        value: "elasticsearch"
      - name: ELASTICSEARCH_PORT
        value: "9200"

  monitor-service:
    enabled: true
    name: monitor-service
    port: 8094
    replicas: 2
    image:
      repository: nta/monitor-service
    database: monitor_db
    resources: {}
    # 监控服务需要自动缩扩容以应对监控数据的波动
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 6
      metrics:
        cpu: 75
        memory: 80
      behavior:
        scaleUp:
          stabilizationWindowSeconds: 120
          podsValue: 1
        scaleDown:
          stabilizationWindowSeconds: 300
    extraEnv:
      - name: KAFKA_CLIENT_USER
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: username
      - name: KAFKA_CLIENT_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: password
      - name: PROMETHEUS_ENDPOINT
        value: "http://prometheus:9090"

  rule-service:
    enabled: true
    name: rule-service
    port: 8095
    replicas: 2
    image:
      repository: nta/rule-service
    database: rule_db
    resources: {}
    # 规则服务需要连接Kafka处理规则引擎消息
    extraEnv:
      - name: KAFKA_CLIENT_USER
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: username
      - name: KAFKA_CLIENT_PASSWORD
        valueFrom:
          secretKeyRef:
            name: kafka-credentials
            key: password

  session-service:
    enabled: true
    name: session-service
    port: 8096
    replicas: 2
    image:
      repository: nta/session-service
    database: nta  # 使用Doris的nta数据库
    resources: {}
    # 会话服务查询Doris中的网络会话数据，PostgreSQL中的标签元数据，Redis用于缓存
    extraEnv:
      - name: REDIS_HOST
        value: "redis"
      - name: REDIS_PORT
        value: "6379"
      - name: REDIS_PASSWORD
        valueFrom:
          secretKeyRef:
            name: redis-credentials
            key: password
      - name: DORIS_USERNAME
        valueFrom:
          secretKeyRef:
            name: doris-credentials
            key: username
      - name: DORIS_PASSWORD
        valueFrom:
          secretKeyRef:
            name: doris-credentials
            key: password
      - name: DB_USERNAME
        valueFrom:
          secretKeyRef:
            name: postgresql-credentials
            key: username
      - name: DB_PASSWORD
        valueFrom:
          secretKeyRef:
            name: postgresql-credentials
            key: password

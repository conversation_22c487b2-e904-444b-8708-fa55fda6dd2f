# values-operators.yaml - Operator配置文件
# 包含所有Operator的配置信息

# ECK Operator配置 - 管理Elasticsearch集群
eck-operator:
  # 启用标志
  enabled: true
  # 监控命名空间 - 指定ECK Operator将管理哪些命名空间中的Elasticsearch资源
  # 可以是一个命名空间列表，例如 ["namespace1", "namespace2"]
  managedNamespaces: ["{{ .Release.Namespace }}"]
  # 资源限制
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  # 开启网络钩子
  webhook:
    enabled: true
  # 指标监控
  prometheus:
    enabled: true

# Flink Kubernetes Operator配置
flink-kubernetes-operator:
  # 启用标志 - 控制是否部署Flink Kubernetes Operator
  enabled: true
  # 监控命名空间 - 指定Flink Kubernetes Operator将监控哪些命名空间中的Flink资源
  # 可以是一个命名空间列表，例如 ["namespace1", "namespace2"]
  watchNamespaces: ["{{ .Release.Namespace }}"]
  # 资源限制
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  # 指标监控
  prometheus:
    enabled: true

# Kafka Operator配置
strimzi-kafka-operator:
  # 启用标志 - 控制是否部署Strimzi Kafka Operator
  enabled: true
  # 监控命名空间 - 指定Strimzi Kafka Operator将监控哪些命名空间中的Kafka资源
  # 可以是一个命名空间列表，例如 ["namespace1", "namespace2"]
  watchNamespaces: ["{{ .Release.Namespace }}"]
  # 资源限制
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  # 指标监控
  prometheus:
    enabled: true

# Nebula Operator配置 - 管理Nebula Graph集群
nebula-operator:
  # 启用标志 - 控制是否部署Nebula Operator
  enabled: true
  # 控制器管理器配置 - 负责管理Nebula Graph集群的生命周期
  controllerManager:
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"
  # 准入控制器配置 - 负责验证和修改Nebula Graph资源
  admissionWebhook:
    create: true
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
  # 调度器配置 - 负责调度Nebula Graph Pod
  scheduler:
    create: true
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"

# Doris Operator配置 - 管理Doris集群
doris-operator:
  # 启用标志 - 控制是否部署Doris Operator
  enabled: true
  # Doris Operator配置 - 负责管理Doris集群的生命周期
  dorisOperator:
    image:
      repository: apache/doris
      tag: operator-latest
      imagePullPolicy: IfNotPresent
    nodeSelector: {}
    nodeAffinity: {}
    enableWebhook: true

# MinIO Operator配置 - 基于 Bitnami MinIO Operator Chart
minio-operator:
  # 启用标志 - 控制是否部署MinIO Operator
  enabled: true

  # Bitnami MinIO Operator 配置
  operator:
    # 资源配置 - 控制MinIO Operator的资源限制
    resources:
      requests:
        memory: "256Mi"
        cpu: "200m"
      limits:
        memory: "512Mi"
        cpu: "500m"

    # 节点选择器
    nodeSelector: {}

    # 亲和性配置
    affinity: {}

    # 容忍度配置
    tolerations: []

    # 监控配置
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true
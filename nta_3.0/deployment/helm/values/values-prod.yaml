# values-prod.yaml - 生产环境配置
# 生产环境默认为离线环境，针对高性能和大数据量优化

# 生产环境 PostgreSQL 配置
infrastructure:
  postgresql:
    enabled: true
    instances: 3  # 生产环境高可用配置

    # 基础配置
    primary:
      persistence:
        size: "500Gi"  # 生产环境大存储
        storageClass: "local-storage"  # 生产环境使用本地存储（离线）
      resources:
        requests:
          memory: "16Gi"  # 生产环境高内存配置
          cpu: "8000m"
        limits:
          memory: "32Gi"
          cpu: "16000m"
    
    # PostgreSQL优化参数（生产环境高性能配置）
    postgresql:
      parameters:
        # 高性能内存配置
        shared_buffers: "8GB"  # 生产环境大内存缓冲
        work_mem: "512MB"
        maintenance_work_mem: "2GB"
        effective_cache_size: "24GB"
        
        # WAL配置（生产环境优化）
        wal_buffers: "128MB"  # 生产环境更大的WAL缓冲
        max_wal_size: "16GB"
        min_wal_size: "4GB"
        checkpoint_completion_target: "0.9"
        checkpoint_timeout: "15min"
        
        # 高并发配置
        max_connections: "500"  # 生产环境支持更多连接
        max_worker_processes: "32"
        max_parallel_workers: "32"
        max_parallel_maintenance_workers: "16"

        # 高性能I/O配置
        effective_io_concurrency: "200"
        random_page_cost: "1.1"
        seq_page_cost: "1.0"
        
        # 生产环境数据安全配置
        synchronous_commit: "on"  # 生产环境保证数据安全
        fsync: "on"  # 生产环境保持开启
        full_page_writes: "on"
        
        # 统计信息
        track_activities: "on"
        track_counts: "on"
        track_io_timing: "on"
        
        # 日志配置
        log_statement: "mod"  # 记录修改语句
        log_min_duration_statement: "1000"  # 生产环境记录超过1秒的查询
        
        # 时区设置
        timezone: "Asia/Shanghai"
    
    # 生产环境特定配置
    offline:
      enabled: true
      # CSV文件处理策略
      csvHandling:
        singleConfigMap: false  # 生产环境使用分片方案
        multiConfigMap: true
        tempStorageSize: "50Gi"  # 生产环境更大的临时存储

    # 大文件存储配置
    largeData:
      storage:
        size: "100Gi"  # 大文件存储大小
        storageClass: "local-storage"  # 使用本地存储

    # 高可用配置
    replica:
      enabled: true
      replicas: 2

    # 备份配置（生产环境）
    backup:
      enabled: true  # 生产环境启用备份
      schedule: "0 2 * * *"  # 每天凌晨2点备份
      retention: "30d"  # 保留30天

    # 监控配置
    metrics:
      enabled: true
      serviceMonitor:
        enabled: true  # 生产环境启用监控

    # 节点亲和性
    nodeSelector:
      node-type: "database"

    # 容忍度
    tolerations:
      - key: "database"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

# 全局配置
global:
  helmFilesPath: "files"  # CSV文件路径

# 生产环境网络配置
networkPolicy:
  enabled: true  # 生产环境启用网络策略

# 镜像配置（生产环境使用本地镜像）
images:
  postgresql:
    repository: "postgres"
    tag: "15-alpine"
    pullPolicy: "IfNotPresent"  # 生产环境使用本地镜像
  
  alpine:
    repository: "alpine"
    tag: "3.18"
    pullPolicy: "IfNotPresent"

# 存储类配置（生产环境本地存储）
storageClass:
  name: "local-storage"
  provisioner: "kubernetes.io/no-provisioner"
  volumeBindingMode: "WaitForFirstConsumer"

# 生产环境监控配置
monitoring:
  enabled: true
  prometheus:
    enabled: true
  grafana:
    enabled: true
  alerting:
    enabled: true

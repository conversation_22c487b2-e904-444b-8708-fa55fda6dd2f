# values-istio.yaml - Istio配置文件
# 包含Istio和网络配置信息

# Istio基础组件配置
# Istio Base - 基础组件
istio-base:
  defaultRevision: default

# Istiod - 控制平面
istiod:
  pilot:
    autoscaleEnabled: true
    autoscaleMin: 1
    autoscaleMax: 3
    resources:
      requests:
        cpu: 500m
        memory: 2Gi
      limits:
        cpu: 1000m
        memory: 4Gi

# Istio Ingress Gateway - 入口网关
istio-ingress:
  autoscaleEnabled: true
  autoscaleMin: 1
  autoscaleMax: 3
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 2000m
      memory: 1024Mi
  service:
    type: LoadBalancer
    ports:
      - name: http2
        port: 80
        targetPort: 8080
      - name: https
        port: 443
        targetPort: 8443

# Istio应用配置
# 控制服务网格和流量管理
istio:
  enabled: true
  # Istio Gateway配置
  gateway:
    enabled: true
    name: "nta-gateway"
    # 服务配置
    service:
      type: LoadBalancer
      ports:
        - name: http
          port: 80
          targetPort: 80
        - name: https
          port: 443
          targetPort: 443
    # 资源配置
    resources:
      requests:
        memory: "128Mi"
        cpu: "100m"
      limits:
        memory: "256Mi"
        cpu: "200m"
    # 标签配置
    labels:
      app: "nta-gateway"
      istio: "ingressgateway"
    # 选择器配置
    selector:
      app: "nta-gateway"
      istio: "ingressgateway"

  # VirtualService配置
  virtualServices:
    # 前端服务VirtualService
    frontend:
      enabled: true
      name: "frontend-vs"
      hosts:
        - "{{ .Values.global.domain }}"
      gateways:
        - "{{ .Release.Namespace }}/{{ .Values.istio.gateway.name }}"
      http:
        - match:
            - uri:
                prefix: "/"
          route:
            - destination:
                host: "frontend"
                port:
                  number: 80

    # API服务VirtualService
    api:
      enabled: true
      name: "api-vs"
      hosts:
        - "api.{{ .Values.global.domain }}"
      gateways:
        - "{{ .Release.Namespace }}/{{ .Values.istio.gateway.name }}"
      http:
        - match:
            - uri:
                prefix: "/auth"
          route:
            - destination:
                host: "auth-service"
                port:
                  number: 8081
        - match:
            - uri:
                prefix: "/analysis"
          route:
            - destination:
                host: "analysis-service"
                port:
                  number: 8082
        - match:
            - uri:
                prefix: "/graph"
          route:
            - destination:
                host: "graph-service"
                port:
                  number: 8083
        - match:
            - uri:
                prefix: "/search"
          route:
            - destination:
                host: "search-service"
                port:
                  number: 8084
        - match:
            - uri:
                prefix: "/notification"
          route:
            - destination:
                host: "notification-service"
                port:
                  number: 8085
        - match:
            - uri:
                prefix: "/task"
          route:
            - destination:
                host: "task-service"
                port:
                  number: 8086
        - match:
            - uri:
                prefix: "/config"
          route:
            - destination:
                host: "config-service"
                port:
                  number: 8087
        - match:
            - uri:
                prefix: "/system"
          route:
            - destination:
                host: "system-service"
                port:
                  number: 8088
        - match:
            - uri:
                prefix: "/security"
          route:
            - destination:
                host: "security-service"
                port:
                  number: 8089

# Strimzi Kafka Operator 使用指南

本文档提供了在NTA项目中使用Strimzi Kafka Operator管理Kafka集群的详细指南。

## 概述

Strimzi Kafka Operator是一个开源的Kubernetes Operator，用于在Kubernetes环境中部署和管理Apache Kafka集群。在NTA项目中，我们使用Strimzi Kafka Operator 0.45.0来管理Kafka集群，实现了声明式配置和自动化的生命周期管理。

我们使用的Kafka版本是3.9.0，通过Kafka CR的`spec.kafka.version: 3.9.0`指定。

## 系统组件

当前系统中的主要组件包括：

- **Strimzi Kafka Operator**：作为Helm依赖引入，负责管理Kafka集群
- **Kafka CR**：用于声明式定义Kafka集群
- **KafkaTopic CR**：用于声明式定义Kafka主题
- **KafkaUser CR**：用于声明式定义Kafka用户和权限

## 配置说明

在`values.yaml`中，Strimzi Kafka Operator的配置位于`infrastructure.kafka.operator`部分：

```yaml
infrastructure:
  kafka:
    operator:
      enabled: true
      # 自定义Operator配置
      watchNamespaces: ["{{ .Values.global.namespace }}"]

      # Kafka集群配置
      kafka:
        name: "kafka"
        version: "3.9.0"
        replicas: 3
        listeners:
          - name: plain
            port: 9092
            type: internal
            tls: false
            authentication:
              type: scram-sha-512
          - name: tls
            port: 9093
            type: internal
            tls: true
            authentication:
              type: scram-sha-512
        config:
          auto.create.topics.enable: true
          offsets.topic.replication.factor: 2
          transaction.state.log.replication.factor: 2
          transaction.state.log.min.isr: 2
          default.replication.factor: 2
          min.insync.replicas: 2
          inter.broker.protocol.version: "3.9"
          log.retention.hours: 168  # 7 days
        storage:
          type: jbod
          volumes:
            - id: 0
              type: persistent-claim
              size: "20Gi"
              deleteClaim: false
        metricsConfig:
          enabled: true
```

## 常用操作

### 查看Kafka集群状态

```bash
kubectl get kafka -n nta
```

### 查看Kafka主题

```bash
kubectl get kafkatopics -n nta
```

### 查看Kafka用户

```bash
kubectl get kafkausers -n nta
```

### 查看Kafka集群详细信息

```bash
kubectl describe kafka kafka -n nta
```

### 查看Kafka Pod

```bash
kubectl get pods -l strimzi.io/cluster=kafka -n nta
```

### 查看Kafka日志

```bash
# 查看特定Kafka Pod的日志
kubectl logs kafka-0 -n nta
```

### 创建新的Kafka主题

```bash
cat <<EOF | kubectl apply -f -
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaTopic
metadata:
  name: my-new-topic
  namespace: nta
  labels:
    strimzi.io/cluster: kafka
spec:
  partitions: 3
  replicas: 2
  config:
    retention.ms: 604800000
    segment.bytes: 1073741824
EOF
```

### 创建新的Kafka用户

```bash
cat <<EOF | kubectl apply -f -
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: my-new-user
  namespace: nta
  labels:
    strimzi.io/cluster: kafka
spec:
  authentication:
    type: scram-sha-512
  authorization:
    type: simple
    acls:
      - resource:
          type: topic
          name: my-new-topic
          patternType: literal
        operation: All
      - resource:
          type: group
          name: my-consumer-group
          patternType: literal
        operation: Read
EOF
```

## 监控和故障排除

### 查看Operator日志

```bash
kubectl logs -l name=strimzi-cluster-operator -n nta
```

### 查看Kafka指标

如果启用了指标监控，可以通过Prometheus查看Kafka指标。

### 常见问题排查

1. **Kafka Pod无法启动**
   - 检查PVC是否创建成功
   - 检查资源限制是否合理
   - 查看Pod事件和日志

2. **认证问题**
   - 检查KafkaUser资源是否正确创建
   - 检查客户端配置是否正确

3. **主题创建失败**
   - 检查KafkaTopic资源是否正确配置
   - 检查Kafka集群状态

## 最佳实践

1. **使用声明式配置**：所有Kafka配置都应该在Kafka CR中定义
2. **版本控制**：将Kafka CR纳入版本控制系统
3. **资源限制**：为Kafka设置合理的资源限制
4. **监控**：配置Prometheus和Grafana监控Kafka集群
5. **高可用性**：使用至少3个副本确保高可用性
6. **安全性**：启用TLS和认证机制

## Strimzi Kafka Operator的主要特性

| 功能 | 描述 |
|------|------|
| 集群管理 | 通过Kubernetes自定义资源进行声明式管理 |
| 配置管理 | 集中在Kafka CR中，便于维护 |
| 用户管理 | 通过KafkaUser资源管理用户和权限 |
| 主题管理 | 通过KafkaTopic资源管理主题 |
| 升级策略 | 支持滚动升级和滚动重启 |
| 监控集成 | 与Prometheus和Grafana集成 |
| 安全性 | 支持TLS、SASL和OAuth认证 |

## 参考文档

- [Strimzi Kafka Operator官方文档](https://strimzi.io/docs/operators/latest/overview.html)
- [Strimzi Kafka Operator GitHub](https://github.com/strimzi/strimzi-kafka-operator)
- [Strimzi Kafka Operator Helm Chart](https://github.com/strimzi/strimzi-kafka-operator/tree/main/helm-charts)
- [Apache Kafka 3.9.0文档](https://kafka.apache.org/39/documentation.html)
- [Strimzi Kafka Operator OperatorHub页面](https://operatorhub.io/operator/strimzi-kafka-operator)

# Flink Kubernetes Operator 使用指南

本文档提供了在NTA项目中使用Flink Kubernetes Operator管理Flink作业的详细指南。

## 概述

Flink Kubernetes Operator是Apache Flink官方提供的Kubernetes扩展，用于在Kubernetes环境中原生管理Flink应用程序。在NTA项目中，我们使用Flink Kubernetes Operator 1.11.0来管理所有Flink作业，实现了声明式配置和自动化的生命周期管理。

我们使用的Flink版本是1.20，通过FlinkDeployment资源的`flinkVersion: v1_20`指定。

## 系统组件

当前系统中的主要组件包括：

- **Flink Kubernetes Operator**：作为Helm依赖引入，负责管理所有Flink作业
- **FlinkDeployment资源**：用于声明式定义Flink作业
- **ServiceAccount和RBAC**：为Operator提供必要的权限

## 配置说明

在`values.yaml`中，Flink Kubernetes Operator的配置位于`infrastructure.flink.operator`部分：

```yaml
infrastructure:
  flink:
    enabled: true
    mode: application
    storage:
      size: "20Gi"
    operator:
      enabled: true
      watchNamespaces: ["{{ .Values.global.namespace }}"]
      defaultSavepointConfig:
        schedule: "0 0 * * *"  # 每天午夜触发savepoint
        history: 3  # 保留的savepoint数量

    jobs:
      traffic-etl-processor:
        enabled: true
        className: "com.geeksec.nta.trafficetl.job.TrafficEtlPipeline"
        parallelism: 4
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
```

## 常用操作

### 查看所有Flink作业

```bash
kubectl get flinkdeployments -n nta
```

### 查看特定Flink作业的详细信息

```bash
kubectl describe flinkdeployment flink-traffic-etl-processor -n nta
```

### 查看Flink作业的日志

```bash
# 查看JobManager日志
kubectl logs -n nta -l component=jobmanager,app=flink-traffic-etl-processor

# 查看TaskManager日志
kubectl logs -n nta -l component=taskmanager,app=flink-traffic-etl-processor
```

### 手动触发savepoint

```bash
kubectl patch flinkdeployment flink-traffic-etl-processor -n nta --type=merge -p '{"spec":{"job":{"savepointTriggerNonce": 1}}}'
```

每次增加`savepointTriggerNonce`的值，都会触发一次新的savepoint创建。

### 升级Flink作业

修改FlinkDeployment资源的镜像版本或配置，Operator会自动创建savepoint，然后使用新配置重启作业：

```bash
kubectl patch flinkdeployment flink-traffic-etl-processor -n nta --type=merge -p '{"spec":{"image": "hb.gs.lan/nta/traffic-etl-processor:1.0.1"}}'
```

### 停止Flink作业

```bash
kubectl patch flinkdeployment flink-traffic-etl-processor -n nta --type=merge -p '{"spec":{"job":{"state": "suspended"}}}'
```

### 启动已停止的Flink作业

```bash
kubectl patch flinkdeployment flink-traffic-etl-processor -n nta --type=merge -p '{"spec":{"job":{"state": "running"}}}'
```

### 删除Flink作业

```bash
kubectl delete flinkdeployment flink-traffic-etl-processor -n nta
```

## 监控和故障排除

### 查看Operator日志

```bash
kubectl logs -n nta -l app.kubernetes.io/name=flink-kubernetes-operator
```

### 查看Flink作业事件

```bash
kubectl get events -n nta --field-selector involvedObject.kind=FlinkDeployment,involvedObject.name=flink-traffic-etl-processor
```

### 访问Flink UI

Flink UI可以通过以下方式访问：

```bash
kubectl port-forward -n nta svc/flink-traffic-etl-processor-rest 8081:8081
```

然后在浏览器中访问 [http://localhost:8081](http://localhost:8081)

## 最佳实践

1. **使用声明式配置**：所有Flink配置都应该在FlinkDeployment资源中定义
2. **版本控制**：将FlinkDeployment资源纳入版本控制系统
3. **资源限制**：为Flink作业设置合理的资源限制
4. **监控**：配置Prometheus和Grafana监控Flink作业
5. **高可用性**：为重要的Flink作业配置高可用性

## Flink Kubernetes Operator的主要特性

| 功能 | 描述 |
|------|------|
| 作业管理 | 通过Kubernetes自定义资源进行声明式管理 |
| 配置管理 | 集中在FlinkDeployment资源中，便于维护 |
| savepoint管理 | 内置自动savepoint功能，支持定时创建和历史管理 |
| 升级策略 | 自动处理，支持多种升级模式（savepoint、stateless等） |
| 故障恢复 | 强大的自动恢复机制，支持多种恢复策略 |
| 可观测性 | 丰富的状态信息和事件，与Kubernetes监控集成 |
| Kubernetes集成 | 原生集成，充分利用Kubernetes生态系统 |

## 参考文档

- [Flink Kubernetes Operator 1.11.0官方文档](https://nightlies.apache.org/flink/flink-kubernetes-operator-docs-release-1.11/)
- [Flink Kubernetes Operator GitHub](https://github.com/apache/flink-kubernetes-operator)
- [Flink 1.20官方文档](https://nightlies.apache.org/flink/flink-docs-release-1.20/)
- [Flink Kubernetes Operator下载页面](https://downloads.apache.org/flink/flink-kubernetes-operator-1.11.0/)

# ECK Operator (Elastic Cloud on Kubernetes) 使用指南

本文档提供了在NTA项目中使用ECK Operator管理Elasticsearch集群的详细指南。

## 概述

ECK (Elastic Cloud on Kubernetes) Operator是Elastic官方提供的Kubernetes Operator，用于在Kubernetes环境中部署和管理Elasticsearch、<PERSON><PERSON>、APM Server等Elastic Stack组件。在NTA项目中，我们使用ECK Operator 2.16.1来管理Elasticsearch集群，实现了声明式配置和自动化的生命周期管理。

我们使用的Elasticsearch版本是7.17.14，通过Elasticsearch CR的`spec.version: 7.17.14`指定。

## 系统组件

当前系统中的主要组件包括：

- **ECK Operator**：作为Helm依赖引入，负责管理Elasticsearch集群
- **Elasticsearch CR**：用于声明式定义Elasticsearch集群
- **IndexTemplate CR**：用于声明式定义索引模板
- **Beat CR**：用于声明式定义Beats组件（可选）

## 配置说明

在`values.yaml`中，ECK Operator的配置位于`eck-operator`部分：

```yaml
eck-operator:
  enabled: true
  managedNamespaces: ["{{ .Release.Namespace }}"]
  resources:
    requests:
      memory: "512Mi"
      cpu: "200m"
    limits:
      memory: "1Gi"
      cpu: "500m"
  webhook:
    enabled: true
  prometheus:
    enabled: true
```

Elasticsearch集群配置位于`infrastructure.elasticsearch`部分：

```yaml
infrastructure:
  elasticsearch:
    enabled: true
    version: "7.17.14"
    replicas: 3
    storage:
      size: "20Gi"
      storageClass: ""
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"
    config:
      node.store.allow_mmap: false
      xpack.security.enabled: true
      xpack.security.transport.ssl.enabled: true
      xpack.security.http.ssl.enabled: false
```

## 常用操作

### 查看Elasticsearch集群状态

```bash
kubectl get elasticsearch -n nta
```

### 查看Elasticsearch集群详细信息

```bash
kubectl describe elasticsearch elasticsearch -n nta
```

### 查看Elasticsearch Pod

```bash
kubectl get pods -l elasticsearch.k8s.elastic.co/cluster-name=elasticsearch -n nta
```

### 查看Elasticsearch日志

```bash
# 查看特定Elasticsearch Pod的日志
kubectl logs elasticsearch-es-default-0 -n nta
```

### 获取Elasticsearch密码

```bash
# 获取elastic用户密码
kubectl get secret elasticsearch-es-elastic-user -n nta -o go-template='{{.data.elastic | base64decode}}'
```

### 访问Elasticsearch API

```bash
# 端口转发
kubectl port-forward svc/elasticsearch-es-http 9200:9200 -n nta

# 测试连接（在另一个终端）
curl -u "elastic:$(kubectl get secret elasticsearch-es-elastic-user -n nta -o go-template='{{.data.elastic | base64decode}}')" \
  -k "https://localhost:9200"
```

### 创建索引模板

```bash
cat <<EOF | kubectl apply -f -
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: nta-logs-template
  namespace: nta
spec:
  elasticsearchRef:
    name: elasticsearch
  name: nta-logs
  indexPatterns:
  - "nta-logs-*"
  template:
    settings:
      number_of_shards: 1
      number_of_replicas: 1
    mappings:
      properties:
        timestamp:
          type: date
        message:
          type: text
        level:
          type: keyword
EOF
```

### 扩展Elasticsearch集群

```bash
kubectl patch elasticsearch elasticsearch -n nta --type=merge -p '{"spec":{"nodeSets":[{"count":5,"name":"default"}]}}'
```

## 监控和故障排除

### 查看ECK Operator日志

```bash
kubectl logs -l control-plane=elastic-operator -n elastic-system
```

### 查看Elasticsearch集群事件

```bash
kubectl get events -n nta --field-selector involvedObject.kind=Elasticsearch,involvedObject.name=elasticsearch
```

### 检查集群健康状态

```bash
# 通过API检查集群健康
kubectl exec -it elasticsearch-es-default-0 -n nta -- \
  curl -u "elastic:$ELASTIC_PASSWORD" \
  -k "https://localhost:9200/_cluster/health?pretty"
```

### 常见问题排查

1. **Pod无法启动**
   - 检查PVC是否创建成功
   - 检查资源限制是否合理
   - 查看Pod事件和日志

2. **集群状态为红色**
   - 检查节点状态
   - 检查分片分配
   - 查看集群日志

3. **认证问题**
   - 检查用户密码是否正确
   - 检查TLS配置

## 最佳实践

1. **使用声明式配置**：所有Elasticsearch配置都应该在Elasticsearch CR中定义
2. **版本控制**：将Elasticsearch CR纳入版本控制系统
3. **资源规划**：为Elasticsearch设置合理的内存和存储资源
4. **监控**：配置Prometheus和Grafana监控Elasticsearch集群
5. **备份**：定期创建快照备份数据
6. **安全性**：启用TLS和认证机制

## ECK Operator的主要特性

| 功能 | 描述 |
|------|------|
| 集群管理 | 通过Kubernetes自定义资源进行声明式管理 |
| 配置管理 | 集中在Elasticsearch CR中，便于维护 |
| 用户管理 | 自动生成用户和密码，支持RBAC |
| 证书管理 | 自动生成和轮换TLS证书 |
| 升级策略 | 支持滚动升级和版本管理 |
| 监控集成 | 与Prometheus和Grafana集成 |
| 备份恢复 | 支持快照和恢复功能 |

## 数据管理

### 索引生命周期管理

```yaml
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexLifecyclePolicy
metadata:
  name: nta-logs-policy
  namespace: nta
spec:
  elasticsearchRef:
    name: elasticsearch
  policy:
    phases:
      hot:
        actions:
          rollover:
            max_size: 10gb
            max_age: 7d
      warm:
        min_age: 7d
        actions:
          allocate:
            number_of_replicas: 0
      delete:
        min_age: 30d
```

### 快照备份

```yaml
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: SnapshotRepository
metadata:
  name: nta-backup-repo
  namespace: nta
spec:
  elasticsearchRef:
    name: elasticsearch
  type: fs
  settings:
    location: "/usr/share/elasticsearch/backup"
```

## 性能调优

### JVM堆内存配置

```yaml
spec:
  nodeSets:
  - name: default
    config:
      node.store.allow_mmap: false
    podTemplate:
      spec:
        containers:
        - name: elasticsearch
          env:
          - name: ES_JAVA_OPTS
            value: "-Xms2g -Xmx2g"
          resources:
            requests:
              memory: 4Gi
            limits:
              memory: 4Gi
```

### 存储优化

```yaml
spec:
  nodeSets:
  - name: default
    volumeClaimTemplates:
    - metadata:
        name: elasticsearch-data
      spec:
        accessModes:
        - ReadWriteOnce
        resources:
          requests:
            storage: 100Gi
        storageClassName: fast-ssd
```

## 参考文档

- [ECK Operator官方文档](https://www.elastic.co/guide/en/cloud-on-k8s/current/index.html)
- [ECK Operator GitHub](https://github.com/elastic/cloud-on-k8s)
- [Elasticsearch 7.17文档](https://www.elastic.co/guide/en/elasticsearch/reference/7.17/index.html)
- [ECK Operator Helm Chart](https://github.com/elastic/helm-charts/tree/main/elasticsearch)

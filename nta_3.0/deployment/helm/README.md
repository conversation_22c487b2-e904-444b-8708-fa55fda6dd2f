# NTA 3.0 Helm 部署指南

本文档提供了使用 Helm 在 Kubernetes 集群上部署 NTA 3.0 平台的指南。

## 目录结构

```text
deployment/helm/
├── Chart.yaml              # Helm Chart 元数据和依赖定义
├── values.yaml             # 主要配置值文件（简化版）
├── values/                 # 拆分后的配置值文件
│   ├── values-global.yaml        # 全局配置
│   ├── values-services.yaml      # 服务配置
│   ├── values-infrastructure.yaml # 基础设施配置
│   ├── values-flink.yaml         # Flink 配置
│   ├── values-monitoring.yaml    # 监控配置
│   ├── values-istio.yaml         # Istio 配置和组件
│   └── values-operators.yaml     # Operator 配置
├── docs/                   # 文档目录
│   ├── features.md               # 功能特性说明
│   ├── flink-operator-guide.md   # Flink Kubernetes Operator 使用指南
│   ├── strimzi-kafka-operator-guide.md # Strimzi Kafka Operator 使用指南
│   ├── eck-operator-guide.md     # ECK Operator 使用指南
│   ├── nebula-operator-guide.md  # Nebula Operator 使用指南
│   ├── doris-operator-guide.md   # Doris Operator 使用指南
│   └── minio-operator-guide.md   # MinIO Operator 使用指南
├── files/                  # 配置文件和数据
│   ├── sql/                      # 数据库初始化脚本
│   ├── es-templates/             # Elasticsearch 模板
│   ├── es-data/                  # Elasticsearch 初始化数据
│   └── csv/                      # CSV 数据文件
└── templates/              # Helm 模板目录
    ├── NOTES.txt           # 安装后显示的提示信息
    ├── _credentials.tpl    # 凭证相关模板
    ├── _deployment.tpl     # 通用部署模板
    ├── _flink_deployment.tpl # Flink 部署模板
    ├── _helpers.tpl        # 通用模板辅助函数
    ├── _kafka.tpl          # Kafka 相关模板
    ├── _service.tpl        # 服务模板
    ├── configmaps/         # ConfigMap 资源
    ├── deployments/        # Deployment 资源
    ├── doris/              # Doris 相关资源
    ├── elasticsearch/      # Elasticsearch 相关资源
    ├── istio/              # Istio 相关资源
    ├── jobs/               # 初始化作业
    ├── minio/              # MinIO 相关资源
    ├── monitoring/         # 监控相关资源
    ├── nebula/             # Nebula Graph 相关资源
    ├── persistentvolumeclaims/ # PVC 资源
    ├── rbac/               # RBAC 相关资源
    ├── secrets/            # Secret 资源
    ├── services/           # Service 资源
    ├── strimzi/            # Strimzi Kafka 相关资源
    └── namespace.yaml      # 命名空间定义
```

## 部署前提条件

- Kubernetes 1.24+
- Helm 3.10+
- 至少 32GB 内存
- 至少 200GB 存储空间

## 快速开始

### 1. 添加必要的 Helm 仓库

```bash
# 基础设施组件
helm repo add bitnami https://charts.bitnami.com/bitnami

# Kafka Operator
helm repo add strimzi https://strimzi.io/charts/

# Flink Kubernetes Operator
helm repo add flink-operator-repo https://downloads.apache.org/flink/flink-kubernetes-operator-1.11.0/

# Istio 组件
helm repo add istio https://istio-release.storage.googleapis.com/charts

# Prometheus 和 Grafana
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add grafana https://grafana.github.io/helm-charts

# Nebula Graph
helm repo add nebula-operator https://vesoft-inc.github.io/nebula-operator/charts

# Doris
helm repo add doris https://charts.selectdb.com

helm repo update
```

### 2. 安装 NTA 3.0 Platform

```bash
# 创建命名空间
kubectl create namespace nta

# 安装 Chart（使用所有配置文件）
helm install nta . -n nta \
  -f values/values-global.yaml \
  -f values/values-services.yaml \
  -f values/values-infrastructure.yaml \
  -f values/values-flink.yaml \
  -f values/values-monitoring.yaml \
  -f values/values-istio.yaml \
  -f values/values-operators.yaml

# 或者使用默认配置安装（仅使用主 values.yaml）
helm install nta . -n nta
```

## 配置说明

### 配置文件结构

为了避免单个 values.yaml 文件过长，我们将配置拆分为多个文件：

1. **values.yaml**: 主配置文件，包含基本配置和组件开关
2. **values/values-global.yaml**: 全局配置，包括全局变量和通用设置
3. **values/values-services.yaml**: 所有微服务的配置
4. **values/values-infrastructure.yaml**: 基础设施组件配置（MySQL、Redis、Elasticsearch等）
5. **values/values-flink.yaml**: Flink 配置和作业
6. **values/values-monitoring.yaml**: 监控配置（Prometheus、Grafana）
7. **values/values-istio.yaml**: Istio 配置（包括基础组件和网络配置）
8. **values/values-operators.yaml**: 所有 Operator 的配置（ECK、Flink、Strimzi、Nebula、Doris、MinIO）

### 主要配置项

配置文件包含以下主要配置部分：

1. **功能特性配置 (features)**
   - 控制不同功能模块的启用/禁用
   - 包括图谱功能等业务模块
   - 位于 `values-global.yaml`

2. **全局配置 (global)**
   - 镜像仓库、标签、命名空间等全局设置
   - 资源默认值和 Java 选项
   - 安全设置（TLS、RBAC）
   - 位于 `values-global.yaml`

3. **服务配置 (services)**
   - 各微服务的配置（副本数、端口、资源等）
   - 服务特定的环境变量和数据库配置
   - 位于 `values-services.yaml`

4. **基础设施配置 (infrastructure)**
   - MySQL、Redis、Elasticsearch 等数据库配置
   - Kafka、Nebula Graph、Doris、MinIO 等基础设施组件配置
   - 包含基础设施组件的连接信息和资源配置
   - 位于 `values-infrastructure.yaml`

5. **Flink 配置**
   - Flink 和 Flink Kubernetes Operator 配置
   - Flink 作业配置
   - 位于 `values-flink.yaml`

6. **Istio 配置 (istio)**
   - 服务网格和 API 网关配置
   - Istio 基础组件配置（istio-base、istiod、istio-ingress）
   - 位于 `values-istio.yaml`

7. **监控配置 (monitoring)**
   - Prometheus 和 Grafana 配置
   - 位于 `values-monitoring.yaml`

8. **Operator 配置**
   - 各种 Operator 的配置（ECK、Flink、Strimzi、Nebula、Doris、MinIO）
   - 仅包含 Operator 本身的配置，不包含被管理的资源配置
   - 位于 `values-operators.yaml`

### 配置文件结构和最佳实践

#### Flink 配置

Flink 配置分为两部分：

1. **Flink 集群配置**
   - 定义在 `_flink_deployment.tpl` 中
   - 包括检查点、高可用性、重启策略等 Flink 核心配置
   - 从 `values.yaml` 的 `infrastructure.flink.configuration` 部分读取配置

2. **Flink 作业配置**
   - 定义在 `configmaps/flink-config.yaml` 中
   - 包括 Kafka、Elasticsearch 等外部系统的连接配置
   - 从 `values.yaml` 的 `infrastructure.flink.jobs.<job-name>.config` 部分读取配置

**注意事项：**

- Flink 配置文件通过 ConfigMap 挂载到容器的 `/opt/flink/usrlib/classes/config.properties` 路径
- 使用 Flink Kubernetes Operator 管理 Flink 作业生命周期
- Savepoint 和 Checkpoint 存储在持久化卷上

#### Kafka 配置

Kafka 使用 Strimzi Operator 进行管理：

1. **Kafka 集群配置**
   - 定义在 `strimzi/kafka.yaml` 中
   - 使用 SASL_PLAINTEXT 或 SASL_SSL 进行认证

2. **Kafka 用户和权限配置**
   - 定义在 `strimzi/kafka-user.yaml` 中
   - 包括主题访问权限和消费组权限

### 自定义配置

有两种方式可以自定义配置：

1. **创建自定义配置文件**

   创建自定义配置文件 `custom-values.yaml` 来覆盖默认配置：

   ```bash
   helm install nta . -n nta -f custom-values.yaml
   # 或
   helm upgrade nta . -n nta -f custom-values.yaml
   ```

2. **修改特定配置文件**

   如果只需要修改特定部分的配置，可以只指定相应的 values 文件：

   ```bash
   # 只修改服务配置
   helm upgrade nta . -n nta \
     -f values/values-services.yaml \
     -f values.yaml

   # 只修改基础设施配置
   helm upgrade nta . -n nta \
     -f values/values-infrastructure.yaml \
     -f values.yaml
   ```

## 维护指南

### 常用命令

```bash
# 升级部署（使用所有配置文件）
helm upgrade nta . -n nta \
  -f values/values-global.yaml \
  -f values/values-services.yaml \
  -f values/values-infrastructure.yaml \
  -f values/values-flink.yaml \
  -f values/values-monitoring.yaml \
  -f values/values-istio.yaml \
  -f values/values-operators.yaml

# 或者使用默认配置升级
helm upgrade nta . -n nta

# 回滚部署
helm rollback nta <revision> -n nta

# 卸载部署
helm uninstall nta -n nta

# 查看部署状态
kubectl get pods -n nta
```

### 日志查看

```bash
# 查看特定 Pod 的日志
kubectl logs -n nta <pod-name>

# 查看特定服务的所有 Pod 日志
kubectl logs -n nta -l app=<service-name>
```

## 故障排除

### 常见问题排查

1. **Pod 无法启动**

   ```bash
   kubectl describe pod <pod-name> -n nta
   kubectl logs <pod-name> -n nta
   ```

2. **服务不可用**

   ```bash
   kubectl get svc -n nta
   kubectl get endpoints -n nta
   kubectl get virtualservice -n nta  # 如果启用了 Istio
   ```

3. **Flink 作业问题**

   ```bash
   kubectl get flinkdeployment -n nta
   kubectl describe flinkdeployment <job-name> -n nta
   ```

4. **Kafka 问题**

   ```bash
   kubectl get kafka -n nta
   kubectl describe kafka <kafka-name> -n nta
   ```

## 安全配置

系统使用以下安全机制：

1. **网络安全**
   - Istio 服务网格提供 mTLS 通信
   - Kubernetes 网络策略限制 Pod 间通信

2. **认证与授权**
   - Kubernetes RBAC 控制资源访问
   - 服务间通信使用 SA-Token 认证

3. **数据安全**
   - 敏感配置使用 Kubernetes Secrets 存储
   - Kafka 使用 SASL_PLAINTEXT 或 SASL_SSL 认证
   - 数据库使用密码认证和 TLS 加密

## Operator 使用指南

NTA 3.0 平台使用多个 Kubernetes Operator 来管理基础设施组件，提供了声明式配置和自动化运维能力。

### 主要 Operator

| Operator | 版本 | 管理组件 | 文档链接 |
|----------|------|----------|----------|
| Flink Kubernetes Operator | 1.11.0 | Flink 作业 | [Flink Operator 指南](docs/flink-operator-guide.md) |
| Strimzi Kafka Operator | 0.45.0 | Kafka 集群 | [Strimzi Kafka Operator 指南](docs/strimzi-kafka-operator-guide.md) |
| Elastic Cloud on Kubernetes | 2.16.1 | Elasticsearch 集群 | [ECK Operator 指南](docs/eck-operator-guide.md) |
| Nebula Operator | 1.8.2 | Nebula Graph 集群 | [Nebula Operator 指南](docs/nebula-operator-guide.md) |
| Doris Operator | 25.4.0 | Doris 集群 | [Doris Operator 指南](docs/doris-operator-guide.md) |
| Bitnami MinIO Operator | 0.2.1 | MinIO 集群 | [MinIO 使用指南](docs/minio-operator-guide.md) |

### 详细指南

- **[Flink Kubernetes Operator 使用指南](docs/flink-operator-guide.md)**: 详细介绍了如何使用 Flink Kubernetes Operator 管理 Flink 作业，包括作业部署、监控、故障排除等。

- **[Strimzi Kafka Operator 使用指南](docs/strimzi-kafka-operator-guide.md)**: 详细介绍了如何使用 Strimzi Kafka Operator 管理 Kafka 集群，包括集群配置、主题管理、用户权限等。

- **[ECK Operator 使用指南](docs/eck-operator-guide.md)**: 详细介绍了如何使用 Elastic Cloud on Kubernetes Operator 管理 Elasticsearch 集群，包括集群部署、索引管理、备份恢复等。

- **[Nebula Operator 使用指南](docs/nebula-operator-guide.md)**: 详细介绍了如何使用 Nebula Operator 管理 Nebula Graph 集群，包括图空间管理、数据导入、查询优化等。

- **[Doris Operator 使用指南](docs/doris-operator-guide.md)**: 详细介绍了如何使用 Doris Operator 管理 Apache Doris 集群，包括数据库管理、表设计、数据导入等。

- **[MinIO Operator 使用指南](docs/minio-operator-guide.md)**: 详细介绍了如何使用 MinIO Operator 管理 MinIO 对象存储集群，包括存储桶管理、访问控制、备份策略等。

### Operator 配置

所有 Operator 的配置都集中在 `values/values-operators.yaml` 文件中，包括：

- Operator 的启用/禁用开关
- Operator 的版本和镜像配置
- Operator 的监控命名空间配置
- Operator 的资源限制配置

## Elasticsearch 初始化

本项目使用了 Elastic Cloud on Kubernetes (ECK) Operator 来管理 Elasticsearch 集群及其索引和模板，提供了更优雅的管理方式。

### ECK Operator

ECK 是 Elastic 官方提供的 Kubernetes Operator，用于管理 Elasticsearch、Kibana 等 Elastic Stack 组件。它提供了以下功能：

1. **集群管理** - 自动化部署和管理 Elasticsearch 集群
2. **索引模板管理** - 通过 CRD 管理索引模板
3. **索引生命周期管理** - 自动化索引创建、备份和维护
4. **安全管理** - 自动生成证书和管理用户认证

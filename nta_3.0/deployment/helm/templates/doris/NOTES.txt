{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.operator.enabled }}
Apache Doris has been deployed using Doris Operator.

Doris Cluster Name: {{ .Values.infrastructure.doris.cluster.name }}

To access the Doris FE service from within the cluster:
  - FE HTTP: {{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.doris.fe.httpPort }}
  - FE Query: {{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.doris.fe.queryPort }}

To access the Doris BE service from within the cluster:
  - BE HTTP: {{ .Values.infrastructure.doris.cluster.name }}-be.{{ .Release.Namespace }}.svc:{{ .Values.infrastructure.doris.be.httpPort }}

To connect to Doris using MySQL client:
  mysql -h {{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc -P {{ .Values.infrastructure.doris.fe.queryPort }} -u {{ .Values.infrastructure.doris.credentials.username }} -p

To check the status of the Doris cluster:
  kubectl get doriscluster {{ .Values.infrastructure.doris.cluster.name }} -n {{ .Release.Namespace }}

To check the status of the Doris pods:
  kubectl get pods -l app.kubernetes.io/instance={{ .Values.infrastructure.doris.cluster.name }} -n {{ .Release.Namespace }}

{{- if .Values.initialization.doris.enabled }}
Doris initialization:
  A job has been created to initialize Doris with SQL scripts from {{ .Values.global.helmFilesPath }}/sql/doris/.
  To check the status of the initialization job:
    kubectl get job {{ .Release.Name }}-doris-init -n {{ .Release.Namespace }}
  To view the logs of the initialization job:
    kubectl logs job/{{ .Release.Name }}-doris-init -n {{ .Release.Namespace }}

  The following SQL scripts will be executed in order:
    - 00-init-schema.sql: Creates databases and tables for NTA analysis
    - 01-sample-data.sql: Inserts sample data for testing
{{- end }}
{{- end }}

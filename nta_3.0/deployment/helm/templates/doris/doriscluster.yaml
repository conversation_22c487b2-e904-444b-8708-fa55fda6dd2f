{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.operator.enabled }}
apiVersion: doris.apache.com/v1
kind: DorisCluster
metadata:
  name: {{ .Values.infrastructure.doris.cluster.name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ .Values.infrastructure.doris.cluster.name }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  enabledCn: {{ .Values.infrastructure.doris.cluster.enabledCn }}
  enabledBroker: {{ .Values.infrastructure.doris.cluster.enabledBroker }}
  {{- if and .Values.infrastructure.doris.credentials.username .Values.infrastructure.doris.credentials.password }}
  authSecret:
    username: {{ .Values.infrastructure.doris.credentials.username | b64enc }}
    password: {{ .Values.infrastructure.doris.credentials.password.value | b64enc }}
  {{- end }}
  feSpec:
    replicas: {{ .Values.infrastructure.doris.fe.replicas }}
    image:
      repository: {{ .Values.infrastructure.doris.fe.image.repository }}
      tag: {{ .Values.infrastructure.doris.fe.image.tag }}
    service:
      type: ClusterIP
    configMap:
      {{- toYaml .Values.infrastructure.doris.fe.configMap | nindent 6 }}
    resource:
      {{- toYaml .Values.infrastructure.doris.fe.resources | nindent 6 }}
    persistentVolumeClaim:
      {{- toYaml .Values.infrastructure.doris.fe.persistentVolumeClaim | nindent 6 }}
  beSpec:
    replicas: {{ .Values.infrastructure.doris.be.replicas }}
    image:
      repository: {{ .Values.infrastructure.doris.be.image.repository }}
      tag: {{ .Values.infrastructure.doris.be.image.tag }}
    service:
      type: ClusterIP
    configMap:
      {{- toYaml .Values.infrastructure.doris.be.configMap | nindent 6 }}
    resource:
      {{- toYaml .Values.infrastructure.doris.be.resources | nindent 6 }}
    persistentVolumeClaim:
      {{- toYaml .Values.infrastructure.doris.be.persistentVolumeClaim | nindent 6 }}
  {{- if .Values.infrastructure.doris.cluster.enabledCn }}
  cnSpec:
    replicas: {{ .Values.infrastructure.doris.cn.replicas }}
    image:
      repository: {{ .Values.infrastructure.doris.cn.image.repository }}
      tag: {{ .Values.infrastructure.doris.cn.image.tag }}
    service:
      type: ClusterIP
    configMap:
      {{- toYaml .Values.infrastructure.doris.cn.configMap | nindent 6 }}
    resource:
      {{- toYaml .Values.infrastructure.doris.cn.resources | nindent 6 }}
    persistentVolumeClaim:
      {{- toYaml .Values.infrastructure.doris.cn.persistentVolumeClaim | nindent 6 }}
  {{- end }}
  {{- if .Values.infrastructure.doris.cluster.enabledBroker }}
  brokerSpec:
    replicas: {{ .Values.infrastructure.doris.broker.replicas }}
    image:
      repository: {{ .Values.infrastructure.doris.broker.image.repository }}
      tag: {{ .Values.infrastructure.doris.broker.image.tag }}
    configMap:
      {{- toYaml .Values.infrastructure.doris.broker.configMap | nindent 6 }}
    resource:
      {{- toYaml .Values.infrastructure.doris.broker.resources | nindent 6 }}
    persistentVolumeClaim:
      {{- toYaml .Values.infrastructure.doris.broker.persistentVolumeClaim | nindent 6 }}
  {{- end }}
{{- end }}

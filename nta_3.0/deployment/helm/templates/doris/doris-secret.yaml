{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.credentials.password }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.infrastructure.doris.cluster.name }}-doris-user
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: doris-credentials
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
type: Opaque
data:
  username: {{ .Values.infrastructure.doris.credentials.username | b64enc }}
  password: {{ .Values.infrastructure.doris.credentials.password.value | b64enc }}
{{- end }}

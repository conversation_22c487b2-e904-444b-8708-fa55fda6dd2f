{{/*
Update credentials in extraEnv
This template handles both username and password environment variables for all services.
For usernames, it sets the value directly from the Values file.
For passwords, it references the appropriate secret.
*/}}
{{- define "nta.updateCredentials" -}}
{{- $extraEnv := .extraEnv -}}
{{- $updatedExtraEnv := list -}}

{{- range $env := $extraEnv -}}
  {{/* Kafka credentials */}}
  {{- if eq $env.name "KAFKA_CLIENT_USER" -}}
    {{- $updatedEnv := dict "name" "KAFKA_CLIENT_USER" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "kafka" "root" $)) "key" (include "nta.credentialsUsernameSecretKey" (dict "type" "kafka" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}
  {{- else if eq $env.name "KAFKA_CLIENT_PASSWORD" -}}
    {{- $updatedEnv := dict "name" "KAFKA_CLIENT_PASSWORD" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "kafka" "root" $)) "key" (include "nta.credentialsSecretKey" (dict "type" "kafka" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}

  {{/* PostgreSQL credentials */}}
  {{- else if eq $env.name "DB_USERNAME" -}}
    {{- $updatedEnv := dict "name" "DB_USERNAME" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $)) "key" (include "nta.credentialsUsernameSecretKey" (dict "type" "postgresql" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}
  {{- else if eq $env.name "DB_PASSWORD" -}}
    {{- $updatedEnv := dict "name" "DB_PASSWORD" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $)) "key" (include "nta.credentialsSecretKey" (dict "type" "postgresql" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}

  {{/* Nebula credentials */}}
  {{- else if eq $env.name "NEBULA_USER" -}}
    {{- $updatedEnv := dict "name" "NEBULA_USER" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "nebula" "root" $)) "key" (include "nta.credentialsUsernameSecretKey" (dict "type" "nebula" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}
  {{- else if eq $env.name "NEBULA_PASSWORD" -}}
    {{- $updatedEnv := dict "name" "NEBULA_PASSWORD" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "nebula" "root" $)) "key" (include "nta.credentialsSecretKey" (dict "type" "nebula" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}

  {{/* Elasticsearch credentials */}}
  {{- else if eq $env.name "ELASTICSEARCH_USER" -}}
    {{- $updatedEnv := dict "name" "ELASTICSEARCH_USER" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "elasticsearch" "root" $)) "key" (include "nta.credentialsUsernameSecretKey" (dict "type" "elasticsearch" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}
  {{- else if eq $env.name "ELASTICSEARCH_PASSWORD" -}}
    {{- $updatedEnv := dict "name" "ELASTICSEARCH_PASSWORD" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "elasticsearch" "root" $)) "key" (include "nta.credentialsSecretKey" (dict "type" "elasticsearch" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}

  {{/* Doris credentials */}}
  {{- else if eq $env.name "DORIS_USERNAME" -}}
    {{- $updatedEnv := dict "name" "DORIS_USERNAME" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "doris" "root" $)) "key" (include "nta.credentialsUsernameSecretKey" (dict "type" "doris" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}
  {{- else if eq $env.name "DORIS_PASSWORD" -}}
    {{- $updatedEnv := dict "name" "DORIS_PASSWORD" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "doris" "root" $)) "key" (include "nta.credentialsSecretKey" (dict "type" "doris" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}

  {{/* Redis credentials */}}
  {{- else if eq $env.name "REDIS_PASSWORD" -}}
    {{- $updatedEnv := dict "name" "REDIS_PASSWORD" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "redis" "root" $)) "key" (include "nta.credentialsSecretKey" (dict "type" "redis" "root" $)))) -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $updatedEnv -}}

  {{/* Default case - keep the original environment variable */}}
  {{- else -}}
    {{- $updatedExtraEnv = append $updatedExtraEnv $env -}}
  {{- end -}}
{{- end -}}

{{- $updatedExtraEnv | toYaml -}}
{{- end -}}

{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "nta.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "nta.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "nta.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "nta.labels" -}}
helm.sh/chart: {{ include "nta.chart" . }}
{{ include "nta.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "nta.selectorLabels" -}}
app.kubernetes.io/name: {{ include "nta.name" . }}

{{/*
Knowledge Base fullname
*/}}
{{- define "knowledgeBase.fullname" -}}
{{- printf "%s-%s" .Release.Name "knowledge-base" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Knowledge Base labels
*/}}
{{- define "knowledgeBase.labels" -}}
{{ include "nta.labels" . }}
app.kubernetes.io/component: knowledge-base
{{- end }}

{{/*
Knowledge Base selector labels
*/}}
{{- define "knowledgeBase.selectorLabels" -}}
app.kubernetes.io/name: {{ include "nta.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
app.kubernetes.io/component: knowledge-base
{{- end }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Generic credentials secret name helper
Usage: include "nta.credentialsSecretName" (dict "type" "kafka|postgresql|redis|elasticsearch|nebula|doris｜minio" "root" $)

The standard naming convention is: <release-name>-<service-name>
*/}}
{{- define "nta.credentialsSecretName" -}}
{{- $type := .type -}}
{{- $root := .root -}}

{{/* Kafka credentials */}}
{{- if eq $type "kafka" -}}
  {{/* Strimzi operator creates secrets with this naming pattern */}}
  {{ $root.Values.infrastructure.kafka.operator.kafka.name }}-{{ $root.Values.infrastructure.kafka.credentials.username }}

{{/* PostgreSQL credentials */}}
{{- else if eq $type "postgresql" -}}
  {{/* Standard naming convention for CloudNativePG */}}
  {{ $root.Release.Name }}-postgresql-app

{{/* Redis credentials */}}
{{- else if eq $type "redis" -}}
  {{/* Standard naming convention for Bitnami Redis chart */}}
  {{ $root.Release.Name }}-redis

{{/* Elasticsearch credentials */}}
{{- else if eq $type "elasticsearch" -}}
  {{/* ECK operator creates secrets with this naming pattern */}}
  {{ $root.Values.infrastructure.elasticsearch.cluster.name }}-es-elastic-user

{{/* Nebula credentials */}}
{{- else if eq $type "nebula" -}}
  {{/* Standard naming convention for Nebula operator */}}
  {{ $root.Values.infrastructure.nebula.cluster.name }}-nebula-user

{{/* Doris credentials */}}
{{- else if eq $type "doris" -}}
  {{/* Standard naming convention for Doris operator */}}
  {{ $root.Values.infrastructure.doris.cluster.name }}-doris-user

{{/* MinIO credentials */}}
{{- else if eq $type "minio" -}}
  {{/* Standard naming convention for MinIO */}}
  {{ $root.Release.Name }}-minio
{{- end -}}
{{- end -}}

{{/*
Generic credentials secret key helper
Usage: include "nta.credentialsSecretKey" (dict "type" "kafka|postgresql|redis|elasticsearch|nebula|doris｜minio" "root" $)

This template returns the appropriate secret key for each service type.
For most services, the standard key is "password".
Some services like Elasticsearch may have custom keys.
*/}}
{{- define "nta.credentialsSecretKey" -}}
{{- $type := .type -}}
{{- $root := .root -}}

{{- if eq $type "elasticsearch" -}}
  {{/* ECK operator uses "elastic" as the key */}}
  elastic
{{- else if eq $type "minio" -}}
  {{/* MinIO uses "secretKey" as the key */}}
  secretKey
{{- else -}}
  {{/* Standard key for all other services */}}
  password
{{- end -}}
{{- end -}}

{{/*
Generic credentials username secret key helper
Usage: include "nta.credentialsUsernameSecretKey" (dict "type" "kafka|postgresql|redis|elasticsearch|nebula|doris｜minio" "root" $)

This template returns the appropriate username secret key for each service type.
For most services, the standard key is "username".
*/}}
{{- define "nta.credentialsUsernameSecretKey" -}}
{{- $type := .type -}}
{{- $root := .root -}}

{{/* Standard key for all services */}}
username
{{- end -}}

{{/*
PostgreSQL configuration helper
Usage: include "nta.postgresqlConfig" .
*/}}
{{- define "nta.postgresqlConfig" -}}
{{- $config := .Values.infrastructure.postgresql -}}
host: {{ $config.host | quote }}
port: {{ $config.port | quote }}
{{- end -}}
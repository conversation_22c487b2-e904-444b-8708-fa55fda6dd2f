{{- if .Values.services.knowledge-base.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: knowledge-base
  namespace: {{ .Values.global.namespace | default "nta" }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.services.knowledge-base.replicaCount }}
  selector:
    matchLabels:
      app: knowledge-base
  template:
    metadata:
      labels:
        app: knowledge-base
        {{- include "nta.selectorLabels" . | nindent 8 }}
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
    spec:
      containers:
        - name: knowledge-base
          image: "{{ .Values.global.registry }}/{{ .Values.services.knowledge-base.image.repository }}:{{ .Values.services.knowledge-base.image.tag | default .Values.global.tag }}"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: {{ .Values.services.knowledge-base.service.port }}
              protocol: TCP
          env:
            - name: SPRING_CONFIG_ADDITIONAL_LOCATION
              value: "file:/config/"
            - name: SPRING_CONFIG_IMPORT
              value: "optional:file:/config/application.yml"
            - name: DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: knowledge-base-secret
                  key: db-username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: knowledge-base-secret
                  key: db-password
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: knowledge-base-secret
                  key: redis-password
          volumeMounts:
            - name: config-volume
              mountPath: /config/application.yml
              subPath: application.yml
          resources:
            {{- toYaml .Values.services.knowledge-base.resources | nindent 12 }}
          livenessProbe:
            httpGet:
              path: /knowledge-base/actuator/health
              port: {{ .Values.services.knowledge-base.service.port }}
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
          readinessProbe:
            httpGet:
              path: /knowledge-base/actuator/health
              port: {{ .Values.services.knowledge-base.service.port }}
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
      volumes:
        - name: config-volume
          configMap:
            name: knowledge-base-config
            items:
              - key: application.yml
                path: application.yml
{{- end }}

{{- if .Values.features.graph.enabled -}}
{{- $serviceValues := .Values.services.graph-service -}}
{{- $serviceConfig := dict "name" $serviceValues.name -}}
{{- $_ := set $serviceConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $serviceConfig "replicas" $serviceValues.replicas -}}
{{- $_ := set $serviceConfig "image" $serviceValues.image -}}
{{- $_ := set $serviceConfig "registry" .Values.global.registry -}}
{{- $_ := set $serviceConfig "tag" .Values.global.tag -}}
{{- $_ := set $serviceConfig "port" $serviceValues.port -}}
{{- $_ := set $serviceConfig "springProfiles" .Values.global.springProfiles -}}
{{- $_ := set $serviceConfig "postgresql" .Values.infrastructure.postgresql -}}
{{- $_ := set $serviceConfig "redis" .Values.infrastructure.redis -}}
{{- $_ := set $serviceConfig "database" $serviceValues.database -}}
{{- $_ := set $serviceConfig "javaOpts" .Values.global.javaOpts -}}
{{- $_ := set $serviceConfig "resources" (default .Values.global.resources $serviceValues.resources) -}}

{{- $extraEnv := default list $serviceValues.extraEnv -}}
{{- if .Values.infrastructure.doris.enabled -}}
{{- $dorisEnv := list
  (dict "name" "DORIS_USERNAME" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "doris" "root" $)) "key" (include "nta.credentialsUsernameSecretKey" (dict "type" "doris" "root" $)))))
  (dict "name" "DORIS_PASSWORD" "valueFrom" (dict "secretKeyRef" (dict "name" (include "nta.credentialsSecretName" (dict "type" "doris" "root" $)) "key" (include "nta.credentialsSecretKey" (dict "type" "doris" "root" $)))))
-}}
{{- $extraEnv = concat $extraEnv $dorisEnv -}}
{{- end -}}

{{- $_ := set $serviceConfig "extraEnv" $extraEnv -}}

{{- include "nta.deployment" $serviceConfig -}}
{{- end -}}

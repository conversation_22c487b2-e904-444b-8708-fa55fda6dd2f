# ServiceAccount for NTA microservices
apiVersion: v1
kind: ServiceAccount
metadata:
  name: nta-service-account
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
automountServiceAccountToken: true
---
# ServiceAccount for NTA discovery client
apiVersion: v1
kind: ServiceAccount
metadata:
  name: nta-discovery-client
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: discovery
automountServiceAccountToken: true

# ClusterRoleBinding for NTA service account
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: nta-service-account-binding
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
subjects:
- kind: ServiceAccount
  name: nta-service-account
  namespace: {{ .Values.global.namespace }}
roleRef:
  kind: ClusterRole
  name: nta-microservice
  apiGroup: rbac.authorization.k8s.io
---
# ClusterRoleBinding for NTA discovery client
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: nta-discovery-client-binding
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
subjects:
- kind: ServiceAccount
  name: nta-discovery-client
  namespace: {{ .Values.global.namespace }}
roleRef:
  kind: ClusterRole
  name: nta-service-discovery
  apiGroup: rbac.authorization.k8s.io
---
# RoleBinding for namespace-specific resources (if needed)
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: nta-namespace-access
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
subjects:
- kind: ServiceAccount
  name: nta-service-account
  namespace: {{ .Values.global.namespace }}
- kind: ServiceAccount
  name: nta-discovery-client
  namespace: {{ .Values.global.namespace }}
roleRef:
  kind: ClusterRole
  name: nta-config-management
  apiGroup: rbac.authorization.k8s.io

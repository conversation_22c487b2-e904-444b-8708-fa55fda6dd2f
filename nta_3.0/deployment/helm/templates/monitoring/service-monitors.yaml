{{- if .Values.features.monitoring.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: nta-services-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/part-of: nta
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: http
      path: /actuator/prometheus
      interval: 30s
      scrapeTimeout: 10s
---
{{- if and .Values.infrastructure.kafka.enabled .Values.infrastructure.kafka.operator.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: kafka-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      strimzi.io/cluster: {{ .Values.infrastructure.kafka.operator.kafka.name }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: metrics
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
---
{{- if .Values.infrastructure.elasticsearch.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: elasticsearch-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: elasticsearch
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: metrics
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
---
{{- if .Values.infrastructure.redis.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: redis-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: redis
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: metrics
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
---
{{- if .Values.infrastructure.mysql.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: mysql-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: mysql
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: metrics
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
---
{{- if and .Values.infrastructure.flink.enabled .Values.infrastructure.flink.operator.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: flink-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: flink-kubernetes-operator
      app.kubernetes.io/instance: {{ .Release.Name }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: metrics
      path: /metrics
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
---
{{- if .Values.infrastructure.nebula.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: nebula-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: nebula-cluster
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: metrics
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
---
{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.metrics.enabled .Values.infrastructure.doris.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: doris-monitor
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Release.Name }}
    release: {{ .Release.Name }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: doris
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  endpoints:
    - port: metrics
      interval: 30s
      scrapeTimeout: 10s
{{- end }}
{{- end }}

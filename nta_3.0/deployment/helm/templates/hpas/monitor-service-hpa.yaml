{{- if and .Values.services.monitor-service.enabled .Values.services.monitor-service.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: monitor-service-hpa
  namespace: {{ .Values.global.namespace | default "nta" }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: monitor-service
  minReplicas: {{ .Values.services.monitor-service.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.services.monitor-service.autoscaling.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.services.monitor-service.autoscaling.metrics.cpu }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .Values.services.monitor-service.autoscaling.metrics.memory }}
  behavior:
    scaleUp:
      stabilizationWindowSeconds: {{ .Values.services.monitor-service.autoscaling.behavior.scaleUp.stabilizationWindowSeconds }}
      policies:
      - type: Pods
        value: {{ .Values.services.monitor-service.autoscaling.behavior.scaleUp.podsValue }}
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: {{ .Values.services.monitor-service.autoscaling.behavior.scaleDown.stabilizationWindowSeconds }}
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
{{- end }}

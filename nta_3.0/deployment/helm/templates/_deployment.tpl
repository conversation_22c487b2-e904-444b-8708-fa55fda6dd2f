{{- define "nta.deployment" -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .name }}
  namespace: {{ .namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  replicas: {{ .replicas }}
  selector:
    matchLabels:
      app: {{ .name }}
      {{- include "nta.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: {{ .name }}
        {{- include "nta.selectorLabels" . | nindent 8 }}
    spec:
      # 使用ServiceAccount进行Kubernetes API访问
      serviceAccountName: nta-service-account
      {{- if .dependencies }}
      # 等待依赖服务就绪的 initContainer
      initContainers:
      {{- range $dependency := .dependencies }}
      {{- $depConfig := tpl $dependency.config $ | fromYaml }}
      - name: wait-for-{{ $dependency.name }}
        {{- if eq $dependency.type "postgresql" }}
        image: postgres:15-alpine
        command: ['sh', '-c', 'until pg_isready -h {{ $depConfig.connection.host }} -p {{ $depConfig.connection.port }}; do echo "等待 {{ $dependency.name }} 服务就绪..."; sleep {{ $depConfig.healthCheck.periodSeconds | default 5 }}; done; echo "{{ $dependency.name }} 服务已就绪"']
        {{- else if eq $dependency.type "redis" }}
        image: redis:7-alpine
        command: ['sh', '-c', 'until redis-cli -h {{ $depConfig.connection.host }} -p {{ $depConfig.connection.port }} ping | grep -q PONG; do echo "等待 {{ $dependency.name }} 服务就绪..."; sleep {{ $depConfig.healthCheck.periodSeconds | default 3 }}; done; echo "{{ $dependency.name }} 服务已就绪"']
        {{- else if eq $dependency.type "doris" }}
        image: mysql:8.0
        command: ['sh', '-c', 'until mysql -h {{ $depConfig.connection.feHost }} -P {{ $depConfig.connection.fePort }} -u {{ $depConfig.credentials.username }} -p{{ $depConfig.credentials.password.value }} -e "SELECT 1" > /dev/null 2>&1; do echo "等待 {{ $dependency.name }} 服务就绪..."; sleep {{ $depConfig.healthCheck.periodSeconds | default 15 }}; done; echo "{{ $dependency.name }} 服务已就绪"']
        {{- else if eq $dependency.type "nebula" }}
        image: {{ $.Values.global.registry }}/proxy_cache/busybox:1.36
        command: ['sh', '-c', 'until nc -z {{ $depConfig.connection.graphdHost }} {{ $depConfig.connection.graphdPort }}; do echo "等待 {{ $dependency.name }} 服务就绪..."; sleep {{ $depConfig.healthCheck.periodSeconds | default 10 }}; done; echo "{{ $dependency.name }} 服务已就绪"']
        {{- else if eq $dependency.type "kafka" }}
        image: {{ $.Values.global.registry }}/proxy_cache/busybox:1.36
        command: ['sh', '-c', 'until nc -z {{ $depConfig.connection.host }} {{ $depConfig.connection.port }}; do echo "等待 {{ $dependency.name }} 服务就绪..."; sleep {{ $depConfig.healthCheck.periodSeconds | default 10 }}; done; echo "{{ $dependency.name }} 服务已就绪"']
        {{- else }}
        # 通用 TCP 连接检查
        image: {{ $.Values.global.registry }}/proxy_cache/busybox:1.36
        command: ['sh', '-c', 'until nc -z {{ $depConfig.connection.host }} {{ $depConfig.connection.port }}; do echo "等待 {{ $dependency.name }} 服务就绪..."; sleep 5; done; echo "{{ $dependency.name }} 服务已就绪"']
        {{- end }}
        {{- if $depConfig.credentials }}
        env:
        {{- if $depConfig.credentials.username }}
        - name: {{ $dependency.name | upper }}_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{ $depConfig.credentials.username.secretName }}
              key: {{ $depConfig.credentials.username.secretKey }}
        {{- end }}
        {{- if $depConfig.credentials.password }}
        - name: {{ $dependency.name | upper }}_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ $depConfig.credentials.password.secretName }}
              key: {{ $depConfig.credentials.password.secretKey }}
        {{- end }}
        {{- end }}
      {{- end }}
      {{- end }}
      containers:
      - name: {{ .name }}
        image: {{ .registry }}/{{ .image.repository }}:{{ .tag }}
        ports:
        - containerPort: {{ .port }}
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: {{ .springProfiles | quote }}
        - name: SPRING_CONFIG_IMPORT
          value: "configtree:/config/"
        # Kubernetes集成环境变量
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: KUBERNETES_SERVICE_NAME
          value: {{ .name }}
        - name: KUBERNETES_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: KUBERNETES_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
              key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "postgresql" "root" $) }}
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
              key: {{ include "nta.credentialsSecretKey" (dict "type" "postgresql" "root" $) }}
        {{- if .extraEnv }}
        {{- include "nta.updateCredentials" . | nindent 8 }}
        {{- end }}
        resources:
          {{- if .resources }}
          {{- toYaml .resources | nindent 10 }}
          {{- else }}
          {{- toYaml $.Values.global.resources | nindent 10 }}
          {{- end }}
        command: ["java", {{ .javaOpts | quote }}, "-jar", "/app/app.jar"]
        volumeMounts:
        - name: config-volume
          mountPath: /config
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: {{ .port }}
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: {{ .port }}
          initialDelaySeconds: 60
          periodSeconds: 15
      volumes:
      - name: config-volume
        configMap:
          name: {{ .name }}-config
{{- end -}}

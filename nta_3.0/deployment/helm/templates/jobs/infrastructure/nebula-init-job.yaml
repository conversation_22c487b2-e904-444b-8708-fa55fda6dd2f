{{- if .Values.features.graph.enabled -}}
apiVersion: batch/v1
kind: Job
metadata:
  name: nebula-init-job
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "10"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  backoffLimit: 5
  activeDeadlineSeconds: 1200
  template:
    metadata:
      labels:
        app: nebula-init-job
        {{- include "nta.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      initContainers:
        - name: wait-for-nebula
          image: busybox:1.28
          command: ['sh', '-c', 'until nc -z -w1 $NEBULA_HOST $NEBULA_PORT; do echo "waiting for nebula-graphd..."; sleep 2; done;']
          env:
            - name: NEBULA_HOST
              value: {{ printf "%s-nebula-graphd-0.%s-nebula-graphd.%s.svc" .Release.Name .Release.Name .Release.Namespace }}
            - name: NEBULA_PORT
              value: "{{ .Values.infrastructure.nebula.graphd.port }}"
      containers:
        - name: nebula-console
          image: {{ .Values.global.registry }}/vesoft/nebula-console:v3.6.0
          command: ["/usr/local/bin/nebula-console"]
          args:
            - "-addr"
            - "$(NEBULA_HOST)"
            - "-port"
            - "$(NEBULA_PORT)"
            - "-u"
            - "$(NEBULA_USER)"
            - "-p"
            - "$(NEBULA_PASSWORD)"
            - "-e"
            - "CREATE SPACE IF NOT EXISTS {{ .Values.infrastructure.nebula.space.name }} (partition_num={{ .Values.infrastructure.nebula.space.partitionNum }}, replica_factor={{ .Values.infrastructure.nebula.space.replicaFactor }}, vid_type={{ .Values.infrastructure.nebula.space.vidType }});"
            - "-e"
            - "sleep 10;"  # 等待图空间创建完成
            - "-f"
            - "/scripts/nebula_init.ngql"
          env:
            - name: NEBULA_HOST
              value: {{ printf "%s-nebula-graphd-0.%s-nebula-graphd.%s.svc" .Release.Name .Release.Name .Release.Namespace }}
            - name: NEBULA_PORT
              value: "{{ .Values.infrastructure.nebula.graphd.port }}"
            - name: NEBULA_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "nebula" "root" $) }}
                  key: {{ include "nta.credentialsUsernameSecretKey" (dict "type" "nebula" "root" $) }}
            - name: NEBULA_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "nebula" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "nebula" "root" $) }}
          volumeMounts:
            - name: init-scripts
              mountPath: /scripts
      volumes:
        - name: init-scripts
          configMap:
            name: nebula-init-config
            defaultMode: 0755
{{- end -}}

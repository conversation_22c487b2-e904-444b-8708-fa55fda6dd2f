{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL基础数据导入Job（小文件和配置数据）
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-postgresql-basic-data-import
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "10"  # 在基础初始化之后执行
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  backoffLimit: 3
  activeDeadlineSeconds: 3600  # 1小时超时，适应大数据量导入
  template:
    metadata:
      labels:
        app: postgresql-basic-data-import-job
        {{- include "nta.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      initContainers:
        - name: wait-for-postgresql-init
          image: postgres:15-alpine
          command:
            - /bin/sh
            - -c
            - |
              # 等待基础初始化完成
              until pg_isready -h {{ .Release.Name }}-postgresql-rw -p 5432 -U postgres; do
                echo "Waiting for PostgreSQL to be ready..."
                sleep 5
              done
              
              # 检查基础表是否已创建
              until PGPASSWORD=$POSTGRES_PASSWORD psql -h {{ .Release.Name }}-postgresql-rw -p 5432 -U postgres -d nta_knowledge -c "SELECT 1 FROM domain_knowledge LIMIT 1;" 2>/dev/null; do
                echo "Waiting for domain_knowledge table to be created..."
                sleep 10
              done
              
              echo "PostgreSQL and base tables are ready for data import!"
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "postgresql" "root" $) }}
      containers:
        - name: postgresql-data-import
          image: postgres:15-alpine
          command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "Starting PostgreSQL data import using COPY mechanism..."
              
              # 连接到nta_knowledge数据库
              export PGHOST="{{ .Release.Name }}-postgresql-rw"
              export PGPORT="5432"
              export PGUSER="postgres"
              export PGDATABASE="nta_knowledge"
              
              # 检查CSV文件是否存在
              if [ ! -f /csv/domain_knowledge.csv ]; then
                echo "Error: CSV file not found at /csv/domain_knowledge.csv"
                exit 1
              fi
              
              # 获取CSV文件信息
              CSV_SIZE=$(wc -c < /csv/domain_knowledge.csv)
              CSV_LINES=$(wc -l < /csv/domain_knowledge.csv)
              echo "CSV file size: $CSV_SIZE bytes"
              echo "CSV file lines: $CSV_LINES"
              
              # 清空现有数据
              echo "Truncating existing data..."
              psql -c "TRUNCATE TABLE domain_knowledge CASCADE;"
              
              # 使用COPY命令导入数据
              echo "Importing data using COPY command..."
              START_TIME=$(date +%s)
              
              psql -c "\COPY domain_knowledge (
                domain, tranco_rank, tranco_list_date,
                registrar_name, contact_email, whois_server, name_servers,
                created_date, updated_date, expires_date, status,
                registrant_name, registrant_organization, registrant_country, registrant_email,
                admin_contact_name, admin_contact_organization, admin_contact_email,
                is_mining_domain, is_video_domain, is_malicious_domain,
                threat_category, threat_confidence_score
              ) FROM '/csv/domain_knowledge.csv' WITH (FORMAT csv, HEADER false, NULL '');"
              
              END_TIME=$(date +%s)
              DURATION=$((END_TIME - START_TIME))
              
              # 更新统计信息
              echo "Updating table statistics..."
              psql -c "ANALYZE domain_knowledge;"
              
              # 显示导入结果
              TOTAL_COUNT=$(psql -t -c "SELECT COUNT(*) FROM domain_knowledge;" | tr -d ' ')
              MALICIOUS_COUNT=$(psql -t -c "SELECT COUNT(*) FROM domain_knowledge WHERE is_malicious_domain = TRUE;" | tr -d ' ')
              MINING_COUNT=$(psql -t -c "SELECT COUNT(*) FROM domain_knowledge WHERE is_mining_domain = TRUE;" | tr -d ' ')
              VIDEO_COUNT=$(psql -t -c "SELECT COUNT(*) FROM domain_knowledge WHERE is_video_domain = TRUE;" | tr -d ' ')
              TRANCO_COUNT=$(psql -t -c "SELECT COUNT(*) FROM domain_knowledge WHERE tranco_rank IS NOT NULL;" | tr -d ' ')
              
              echo "=== 域名知识数据导入完成 ==="
              echo "导入耗时: ${DURATION}秒"
              echo "总记录数: $TOTAL_COUNT"
              echo "恶意域名: $MALICIOUS_COUNT"
              echo "挖矿域名: $MINING_COUNT"
              echo "视频网站: $VIDEO_COUNT"
              echo "Tranco排名: $TRANCO_COUNT"
              echo "================================"
              
              # 验证数据完整性
              if [ "$TOTAL_COUNT" -eq "0" ]; then
                echo "Error: No data was imported!"
                exit 1
              fi
              
              echo "Data import completed successfully!"
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "postgresql" "root" $) }}
          volumeMounts:
            - name: postgresql-csv-data
              mountPath: /csv
          resources:
            requests:
              memory: "512Mi"
              cpu: "200m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
      volumes:
        - name: postgresql-csv-data
          configMap:
            name: postgresql-csv-data
{{- end }}

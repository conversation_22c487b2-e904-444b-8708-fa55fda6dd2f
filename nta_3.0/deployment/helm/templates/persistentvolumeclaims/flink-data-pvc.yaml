{{- if .Values.infrastructure.flink.enabled -}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: flink-data-pvc
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: {{ .Values.infrastructure.flink.storageClass }}
  resources:
    requests:
      storage: {{ .Values.infrastructure.flink.storage.size }}
{{- end -}}

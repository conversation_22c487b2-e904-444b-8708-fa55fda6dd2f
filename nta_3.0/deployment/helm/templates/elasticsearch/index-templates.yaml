{{- if and .Values.infrastructure.elasticsearch.enabled .Values.infrastructure.elasticsearch.operator.enabled -}}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: cert-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: cert_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/cert_template.json" | fromJson | toJson }}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: connect-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: connect_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/connect_template.json" | fromJson | toJson }}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: ssl-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: ssl_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/ssl_template.json" | fromJson | toJson }}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: http-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: http_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/http_template.json" | fromJson | toJson }}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: dns-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: dns_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/dns_template.json" | fromJson | toJson }}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: alarm-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: alarm_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/alarm_template.json" | fromJson | toJson }}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: es-index-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: es_index_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/es_index_template.json" | fromJson | toJson }}
---
apiVersion: elasticsearch.k8s.elastic.co/v1alpha1
kind: IndexTemplate
metadata:
  name: ssh-template
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  name: ssh_template
  elasticsearch:
    name: {{ .Values.infrastructure.elasticsearch.cluster.name }}
  body: {{ $.Files.Get "files/es-templates/ssh_template.json" | fromJson | toJson }}
{{- end -}}

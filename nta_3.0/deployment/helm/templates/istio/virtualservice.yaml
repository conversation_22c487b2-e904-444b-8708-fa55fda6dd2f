{{- if .Values.istio.enabled -}}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: nta-virtualservice
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  hosts:
  - {{ .Values.global.domain | quote }}
  gateways:
  - nta-gateway
  http:
  - match:
    - uri:
        prefix: /auth
    route:
    - destination:
        host: auth-service
        port:
          number: {{ .Values.services.auth-service.port }}
    timeout: {{ index .Values.istio.services "auth-service" "timeout" | default .Values.istio.global.timeout }}
    retries:
      attempts: {{ index .Values.istio.services "auth-service" "retries" "attempts" | default .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ index .Values.istio.services "auth-service" "retries" "perTryTimeout" | default .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}

  - match:
    - uri:
        prefix: /analyze
    - uri:
        prefix: /metadata
    - uri:
        prefix: /aggr
    - uri:
        prefix: /tag
    route:
    - destination:
        host: analysis-service
        port:
          number: {{ .Values.services.analysis-service.port }}
    timeout: {{ index .Values.istio.services "analysis-service" "timeout" | default .Values.istio.global.timeout }}
    retries:
      attempts: {{ index .Values.istio.services "analysis-service" "retries" "attempts" | default .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ index .Values.istio.services "analysis-service" "retries" "perTryTimeout" | default .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}

  - match:
    - uri:
        prefix: /atlas
    route:
    - destination:
        host: graph-service
        port:
          number: {{ .Values.services.graph-service.port }}
    timeout: {{ index .Values.istio.services "graph-service" "timeout" | default .Values.istio.global.timeout }}
    retries:
      attempts: {{ index .Values.istio.services "graph-service" "retries" "attempts" | default .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ index .Values.istio.services "graph-service" "retries" "perTryTimeout" | default .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}

  - match:
    - uri:
        prefix: /search
    route:
    - destination:
        host: search-service
        port:
          number: {{ .Values.services.search-service.port }}
    timeout: {{ .Values.istio.global.timeout }}
    retries:
      attempts: {{ .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}

  - match:
    - uri:
        prefix: /task
    route:
    - destination:
        host: task-service
        port:
          number: {{ .Values.services.task-service.port }}
    timeout: {{ .Values.istio.global.timeout }}
    retries:
      attempts: {{ .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}

  - match:
    - uri:
        prefix: /system
    route:
    - destination:
        host: system-service
        port:
          number: {{ .Values.services.system-service.port }}
    timeout: {{ .Values.istio.global.timeout }}
    retries:
      attempts: {{ .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}

  - match:
    - uri:
        prefix: /security
    route:
    - destination:
        host: security-service
        port:
          number: {{ .Values.services.security-service.port }}
    timeout: {{ .Values.istio.global.timeout }}
    retries:
      attempts: {{ .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}

  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: frontend
        port:
          number: {{ .Values.services.frontend.port }}
    timeout: {{ .Values.istio.global.timeout }}
    retries:
      attempts: {{ .Values.istio.global.retries.attempts }}
      perTryTimeout: {{ .Values.istio.global.retries.perTryTimeout }}
      retryOn: {{ .Values.istio.global.retries.retryOn }}
{{- end -}}

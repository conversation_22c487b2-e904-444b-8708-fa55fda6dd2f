{{- if .Values.istio.enabled -}}
{{- range $serviceName, $serviceConfig := .Values.services }}
{{- if $serviceConfig.enabled }}
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{ $serviceName }}
  namespace: {{ $.Values.global.namespace }}
  labels:
    {{- include "nta.labels" $ | nindent 4 }}
spec:
  host: {{ $serviceName }}
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: {{ index $.Values.istio.services $serviceName "circuitBreaker" "maxConnections" | default $.Values.istio.global.circuitBreaker.maxConnections }}
      http:
        maxRequestsPerConnection: 10
        http1MaxPendingRequests: {{ index $.Values.istio.services $serviceName "circuitBreaker" "maxPendingRequests" | default $.Values.istio.global.circuitBreaker.maxPendingRequests }}
        maxRetries: {{ index $.Values.istio.services $serviceName "circuitBreaker" "maxRetries" | default $.Values.istio.global.circuitBreaker.maxRetries }}
    outlierDetection:
      consecutiveErrors: {{ index $.Values.istio.services $serviceName "circuitBreaker" "consecutiveErrors" | default $.Values.istio.global.circuitBreaker.consecutiveErrors }}
      interval: {{ index $.Values.istio.services $serviceName "circuitBreaker" "interval" | default $.Values.istio.global.circuitBreaker.interval }}
      baseEjectionTime: {{ index $.Values.istio.services $serviceName "circuitBreaker" "baseEjectionTime" | default $.Values.istio.global.circuitBreaker.baseEjectionTime }}
{{- end }}
{{- end }}
{{- end -}}

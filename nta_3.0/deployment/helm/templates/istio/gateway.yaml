{{- if .Values.istio.enabled -}}
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: nta-gateway
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  selector:
    istio: ingressgateway # 使用默认的Istio入口网关
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - {{ .Values.global.domain | quote }}
    {{- if .Values.global.security.tlsEnabled }}
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - {{ .Values.global.domain | quote }}
    tls:
      mode: SIMPLE
      credentialName: "nta-tls-cert"
    {{- end }}
{{- end -}}

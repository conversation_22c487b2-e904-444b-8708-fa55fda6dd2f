{{- if .Values.infrastructure.postgresql.enabled }}
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: {{ .Release.Name }}-postgresql
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  instances: {{ .Values.infrastructure.postgresql.instances | default 1 }}
  
  # PostgreSQL配置
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "1GB"
      effective_cache_size: "3GB"
      maintenance_work_mem: "512MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "64MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"
      work_mem: "256MB"
      min_wal_size: "2GB"
      max_wal_size: "8GB"
      max_worker_processes: "16"
      max_parallel_workers_per_gather: "8"
      max_parallel_workers: "16"
      max_parallel_maintenance_workers: "8"
      # 启用扩展
      shared_preload_libraries: "pg_stat_statements"
      # 时区设置
      timezone: "Asia/Shanghai"
      # 日志配置
      log_destination: "stderr"
      logging_collector: "on"
      log_directory: "log"
      log_filename: "postgresql-%Y-%m-%d_%H%M%S.log"
      log_statement: "all"
      log_min_duration_statement: "1000"

  # 主节点配置
  primary:
    initdb:
      database: postgres
      owner: postgres
      secret:
        name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
      # 初始化脚本
      postInitApplicationSQL:
        - |
          -- 创建应用数据库
          CREATE DATABASE IF NOT EXISTS push_database;
          CREATE DATABASE IF NOT EXISTS auth_db;
          CREATE DATABASE IF NOT EXISTS th_analysis;
          CREATE DATABASE IF NOT EXISTS nta_knowledge;
          
          -- 创建应用用户
          CREATE USER IF NOT EXISTS nta_user WITH PASSWORD '{{ .Values.infrastructure.postgresql.credentials.password.value }}';
          
          -- 授权
          GRANT ALL PRIVILEGES ON DATABASE push_database TO nta_user;
          GRANT ALL PRIVILEGES ON DATABASE auth_db TO nta_user;
          GRANT ALL PRIVILEGES ON DATABASE th_analysis TO nta_user;
          GRANT ALL PRIVILEGES ON DATABASE nta_knowledge TO nta_user;

  # 存储配置
  storage:
    size: {{ .Values.infrastructure.postgresql.primary.persistence.size | default "50Gi" }}
    storageClass: {{ .Values.infrastructure.postgresql.primary.persistence.storageClass | default "" }}

  # 资源配置
  resources:
    requests:
      memory: {{ .Values.infrastructure.postgresql.primary.resources.requests.memory | default "4Gi" }}
      cpu: {{ .Values.infrastructure.postgresql.primary.resources.requests.cpu | default "2000m" }}
    limits:
      memory: {{ .Values.infrastructure.postgresql.primary.resources.limits.memory | default "8Gi" }}
      cpu: {{ .Values.infrastructure.postgresql.primary.resources.limits.cpu | default "4000m" }}

  # 监控配置
  monitoring:
    enabled: {{ .Values.infrastructure.postgresql.metrics.enabled | default true }}
    podMonitorEnabled: {{ .Values.infrastructure.postgresql.metrics.serviceMonitor.enabled | default true }}

  # 备份配置
  {{- if .Values.infrastructure.postgresql.backup.enabled }}
  backup:
    retentionPolicy: {{ .Values.infrastructure.postgresql.backup.retentionPolicy | default "30d" }}
    barmanObjectStore:
      destinationPath: {{ .Values.infrastructure.postgresql.backup.destinationPath | default "s3://postgresql-backup" }}
      s3Credentials:
        accessKeyId:
          name: {{ .Values.infrastructure.postgresql.backup.s3Credentials.secretName }}
          key: {{ .Values.infrastructure.postgresql.backup.s3Credentials.accessKeyIdKey }}
        secretAccessKey:
          name: {{ .Values.infrastructure.postgresql.backup.s3Credentials.secretName }}
          key: {{ .Values.infrastructure.postgresql.backup.s3Credentials.secretAccessKeyKey }}
      wal:
        retention: {{ .Values.infrastructure.postgresql.backup.walRetention | default "5d" }}
      data:
        retention: {{ .Values.infrastructure.postgresql.backup.dataRetention | default "30d" }}
  {{- end }}

  # 高可用配置
  {{- if gt (.Values.infrastructure.postgresql.instances | int) 1 }}
  replica:
    enabled: true
  {{- end }}

  # 节点亲和性
  {{- if .Values.infrastructure.postgresql.nodeSelector }}
  nodeSelector:
    {{- toYaml .Values.infrastructure.postgresql.nodeSelector | nindent 4 }}
  {{- end }}

  # 容忍度
  {{- if .Values.infrastructure.postgresql.tolerations }}
  tolerations:
    {{- toYaml .Values.infrastructure.postgresql.tolerations | nindent 4 }}
  {{- end }}

  # Pod反亲和性
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              postgresql: {{ .Release.Name }}-postgresql
          topologyKey: kubernetes.io/hostname

---
# 数据文件存储PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ .Release.Name }}-postgresql-data-files
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.infrastructure.postgresql.dataFiles.storage | default "10Gi" }}
  {{- if .Values.infrastructure.postgresql.dataFiles.storageClass }}
  storageClass: {{ .Values.infrastructure.postgresql.dataFiles.storageClass }}
  {{- end }}

---
# PostgreSQL基础结构初始化Job
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-postgresql-schema-init
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "5"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  backoffLimit: 5
  activeDeadlineSeconds: 1800
  template:
    metadata:
      labels:
        app: postgresql-schema-init-job
        {{- include "nta.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      initContainers:
        - name: wait-for-postgresql
          image: postgres:15-alpine
          command:
            - /bin/sh
            - -c
            - |
              until pg_isready -h {{ .Release.Name }}-postgresql-rw -p 5432 -U postgres; do
                echo "Waiting for PostgreSQL to be ready..."
                sleep 5
              done
              echo "PostgreSQL is ready!"
      containers:
        - name: postgresql-schema-init
          image: postgres:15-alpine
          command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "Initializing PostgreSQL database schemas..."
              
              # 执行结构创建脚本（01-03开头的脚本）
              for script in $(ls -1 /scripts/0[1-3]*.sql | sort); do
                echo "Executing schema script: $script..."
                PGPASSWORD=$POSTGRES_PASSWORD psql -h {{ .Release.Name }}-postgresql-rw -p 5432 -U postgres -f $script
                echo "Completed $script"
              done
              
              echo "PostgreSQL schema initialization completed successfully."
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "postgresql" "root" $) }}
          volumeMounts:
            - name: postgresql-init-scripts
              mountPath: /scripts
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
      volumes:
        - name: postgresql-init-scripts
          configMap:
            name: postgresql-init-scripts

---
# PostgreSQL初始化脚本ConfigMap（仅包含结构脚本）
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-init-scripts
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  {{- range $path, $_ := .Files.Glob (printf "%s/sql/postgresql/0[1-3]*.sql" .Values.global.helmFilesPath) }}
  {{ base $path }}: |-
{{ $.Files.Get $path | indent 4 }}
  {{- end }}
  {{- range $path, $_ := .Files.Glob (printf "%s/sql/postgresql/*-copy.sql" .Values.global.helmFilesPath) }}
  {{ base $path }}: |-
{{ $.Files.Get $path | indent 4 }}
  {{- end }}
{{- end }}

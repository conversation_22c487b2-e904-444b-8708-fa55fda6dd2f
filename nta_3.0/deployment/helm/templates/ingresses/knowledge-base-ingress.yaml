{{- if and .Values.services.knowledge-base.enabled .Values.istio.enabled }}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: knowledge-base
  namespace: {{ .Values.global.namespace | default "nta" }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  hosts:
    - knowledge-base.nta.local
  gateways:
    - {{ .Release.Namespace }}/nta-gateway
  http:
    - route:
        - destination:
            host: knowledge-base-service
            port:
              number: {{ .Values.services.knowledge-base.service.port }}
{{- end }}

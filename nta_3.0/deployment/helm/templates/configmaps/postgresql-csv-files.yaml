{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL CSV文件ConfigMap（离线环境）
# 注意：此ConfigMap可能会很大，如果超过Kubernetes限制，请考虑使用initContainer + emptyDir方案
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-csv-files
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
  annotations:
    description: "PostgreSQL CSV数据文件 - 离线环境部署"
binaryData:
  # 包含所有CSV文件
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{- $filesize := len ($.Files.Get $path) }}
  {{- $filesizeMB := div $filesize 1048576 }}
  # {{ $filename }} ({{ $filesizeMB }}MB)
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}
{{- end }}

---
{{- if .Values.infrastructure.postgresql.enabled }}
# 如果ConfigMap过大，使用此替代方案
# PostgreSQL CSV文件分片ConfigMap 1
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-csv-files-part1
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    csv-part: "1"
  annotations:
    description: "PostgreSQL CSV数据文件 - 第1部分（小文件）"
binaryData:
  # 只包含小文件（<1MB）
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{- $filesize := len ($.Files.Get $path) }}
  {{- if lt $filesize 1048576 }}  {{/* 1MB = 1048576 bytes */}}
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}
  {{- end }}
{{- end }}

---
{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL CSV文件分片ConfigMap 2
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-csv-files-part2
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    csv-part: "2"
  annotations:
    description: "PostgreSQL CSV数据文件 - 第2部分（中等文件 1-10MB）"
binaryData:
  # 包含中等文件（1-10MB）
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{- $filesize := len ($.Files.Get $path) }}
  {{- if and (ge $filesize 1048576) (lt $filesize 10485760) }}  {{/* 1MB-10MB */}}
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}
  {{- end }}
{{- end }}

---
{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL CSV文件分片ConfigMap 3
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-csv-files-part3
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    csv-part: "3"
  annotations:
    description: "PostgreSQL CSV数据文件 - 第3部分（大文件 10-50MB）"
binaryData:
  # 包含大文件（10-50MB）
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{- $filesize := len ($.Files.Get $path) }}
  {{- if and (ge $filesize 10485760) (lt $filesize 52428800) }}  {{/* 10MB-50MB */}}
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}
  {{- end }}
{{- end }}

---
{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL CSV文件分片ConfigMap 4
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-csv-files-part4
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    csv-part: "4"
  annotations:
    description: "PostgreSQL CSV数据文件 - 第4部分（超大文件 >50MB）"
binaryData:
  # 包含超大文件（>50MB）
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{- $filesize := len ($.Files.Get $path) }}
  {{- if ge $filesize 52428800 }}  {{/* >50MB */}}
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}
  {{- end }}
{{- end }}

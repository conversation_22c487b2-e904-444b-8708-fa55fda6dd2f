{{- if .Values.services.monitor-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: monitor-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 监控服务配置

    # 服务器配置
    server:
      port: 8094
      servlet:
        context-path: /monitor

    # 应用名称
    spring:
      application:
        name: monitor-service

      # 数据库连接配置
      datasource:
        url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.monitor-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        driver-class-name: org.postgresql.Driver

      # Kafka配置
      kafka:
        bootstrap-servers: {{ include "nta.kafkaConfig" . | fromYaml | get "bootstrapServers" }}
        consumer:
          group-id: monitor-service-group
          auto-offset-reset: latest
          key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        producer:
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.apache.kafka.common.serialization.StringSerializer

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.monitor.entity

    # Prometheus配置
    prometheus:
      endpoint: ${PROMETHEUS_ENDPOINT}
      # 查询配置
      query:
        timeout: 30000
        max-samples: 50000
      # 指标配置
      metrics:
        # 默认采集间隔
        default-interval: 60
        # 数据保留天数
        retention-days: 30

    # 监控服务特定配置
    monitoring:
      # 数据采集配置
      collection:
        # 采集间隔（秒）
        interval: 60
        # 批量大小
        batch-size: 1000
        # 并发线程数
        threads: 4
      # 告警配置
      alerting:
        enabled: true
        # 告警规则检查间隔
        check-interval: 30
        # 告警通知延迟
        notification-delay: 300
      # 数据存储配置
      storage:
        # 数据压缩
        compression: true
        # 分区策略
        partition-strategy: daily
        # 自动清理过期数据
        auto-cleanup: true

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      alarm-service:
        url: http://alarm-service:8091
{{- end -}}

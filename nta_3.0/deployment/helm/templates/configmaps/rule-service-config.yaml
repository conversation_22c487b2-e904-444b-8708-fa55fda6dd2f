{{- if .Values.services.rule-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: rule-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 规则服务配置

    # 服务器配置
    server:
      port: 8095
      servlet:
        context-path: /rule

    # 应用名称
    spring:
      application:
        name: rule-service

      # 数据库连接配置
      datasource:
        url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.rule-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        driver-class-name: org.postgresql.Driver

      # Kafka配置
      kafka:
        bootstrap-servers: {{ include "nta.kafkaConfig" . | fromYaml | get "bootstrapServers" }}
        consumer:
          group-id: rule-service-group
          auto-offset-reset: latest
          key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        producer:
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.apache.kafka.common.serialization.StringSerializer

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.rule.entity

    # 规则引擎配置
    rule-engine:
      # 支持的规则格式
      supported-formats:
        - json
        - yaml
        - drools
      # 执行配置
      execution:
        # 超时时间（毫秒）
        timeout: 30000
        # 最大并发执行数
        max-concurrent: 100
        # 线程池大小
        thread-pool-size: 10
      # 缓存配置
      cache:
        enabled: true
        # 规则缓存时间（秒）
        rule-ttl: 3600
        # 最大缓存数量
        max-size: 1000
      # 性能监控
      monitoring:
        enabled: true
        # 记录执行时间阈值（毫秒）
        slow-execution-threshold: 1000

    # 规则服务特定配置
    rules:
      # 规则验证
      validation:
        enabled: true
        strict-mode: true
      # 版本管理
      versioning:
        enabled: true
        # 保留历史版本数
        max-versions: 10
      # 热更新
      hot-reload:
        enabled: true
        # 检查间隔（秒）
        check-interval: 60

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      alarm-service:
        url: http://alarm-service:8091
{{- end -}}

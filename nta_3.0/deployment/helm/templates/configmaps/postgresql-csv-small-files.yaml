{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL小CSV文件ConfigMap（仅包含小于1MB的文件）
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-csv-small-files
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
binaryData:
  # 只包含小文件，大文件通过其他方式处理
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{- $filesize := len ($.Files.Get $path) }}
  {{- if lt $filesize 1048576 }}  {{/* 1MB = 1048576 bytes */}}
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}
  {{- end }}
{{- end }}

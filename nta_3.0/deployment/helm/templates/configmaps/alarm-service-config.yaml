{{- if .Values.services.alarm-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: alarm-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 告警服务配置

    # 服务器配置
    server:
      port: 8091
      servlet:
        context-path: /alarm

    # 应用名称
    spring:
      application:
        name: alarm-service

      # 数据库连接配置
      datasource:
        url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.alarm-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        driver-class-name: org.postgresql.Driver

      # Kafka配置
      kafka:
        bootstrap-servers: {{ include "nta.kafkaConfig" . | fromYaml | get "bootstrapServers" }}
        consumer:
          group-id: alarm-service-group
          auto-offset-reset: latest
          key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        producer:
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.apache.kafka.common.serialization.StringSerializer

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.alarm.entity

    # 告警记录主要存储在PostgreSQL中，通过Flink CDC同步到Doris进行分析

    # 告警服务特定配置
    alarm:
      # 告警规则检查间隔（秒）
      rule-check-interval: 60
      # 告警记录存储配置
      storage:
        # 告警记录主存储在PostgreSQL中
        records-storage: postgresql
        # 数据通过Flink CDC自动同步到Doris ODS层进行分析
      # 告警通知渠道
      notification:
        enabled: true
        channels:
          - email
          - webhook
      # 告警等级配置
      severity:
        low:
          color: "#36a64f"
        medium:
          color: "#ff9500"
        high:
          color: "#ff0000"
        critical:
          color: "#8b0000"

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      rule-service:
        url: http://rule-service:8095
{{- end -}}

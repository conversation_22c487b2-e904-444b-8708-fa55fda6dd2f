{{- if .Values.services.knowledge-base.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: knowledge-base-config
  namespace: {{ .Values.global.namespace | default "nta" }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    server:
      port: {{ .Values.services.knowledge-base.service.port }}
      servlet:
        context-path: /knowledge-base
    
    spring:
      application:
        name: knowledge-base-service
      
      profiles:
        active: prod
      
      datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.knowledge-base.config.dbName }}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        
        druid:
          initial-size: 5
          min-idle: 5
          max-active: 20
          max-wait: 60000
      
      data:
        redis:
          host: {{ include "nta.redisConfig" . | fromYaml | get "host" }}
          port: {{ include "nta.redisConfig" . | fromYaml | get "port" }}
          password: ${REDIS_PASSWORD}
          database: {{ .Values.services.knowledge-base.config.redisDatabase | default 0 }}
          timeout: 5000ms
      
      cache:
        type: caffeine
        caffeine:
          spec: maximumSize=10000,expireAfterWrite=30m
    
    logging:
      level:
        com.geeksec.knowledgebase: INFO
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: when-authorized
    
    knowledge-base:
      cache:
        threat-intelligence:
          ttl: {{ .Values.services.knowledge-base.config.cache.threatIntelligenceTtl | default "1h" }}
          max-size: {{ .Values.services.knowledge-base.config.cache.threatIntelligenceMaxSize | default 10000 }}
        certificate-labels:
          ttl: {{ .Values.services.knowledge-base.config.cache.certificateLabelsTtl | default "30m" }}
          max-size: {{ .Values.services.knowledge-base.config.cache.certificateLabelsMaxSize | default 5000 }}
{{- end }}

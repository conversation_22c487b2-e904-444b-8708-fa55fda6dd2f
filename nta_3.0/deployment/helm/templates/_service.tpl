{{- define "nta.service" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ .name }}
  namespace: {{ .namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/part-of: nta
  {{- if and $.Values.monitoring.enabled (index $.Values "monitoring" "prometheus-stack" "enabled") }}
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/path: "/actuator/prometheus"
    prometheus.io/port: "{{ .port }}"
  {{- end }}
spec:
  selector:
    app: {{ .name }}
    {{- include "nta.selectorLabels" . | nindent 4 }}
  ports:
  - port: {{ .port }}
    targetPort: {{ .port }}
  type: ClusterIP
{{- end -}}

-- 攻击阶段数据导入脚本
-- 从 CSV 文件导入 attack_stage_details 表数据

-- 清空现有数据（如果需要）
-- TRUNCATE TABLE attack_stage_details;

-- 从 CSV 文件导入数据
-- 注意：需要根据实际的 CSV 文件路径调整
COPY attack_stage_details (
    attack_stage,
    chinese_name,
    english_name,
    description,
    typical_techniques,
    severity_level,
    urgency_description,
    defense_recommendations
) FROM '/path/to/csv/attack_stage_details.csv'
WITH (
    FORMAT CSV,
    HEADER true,
    DELIMITER ',',
    QUOTE '"',
    ESCAPE '"'
);

-- 验证导入的数据
SELECT 
    attack_stage,
    chinese_name,
    english_name,
    severity_level,
    array_length(typical_techniques, 1) as technique_count,
    array_length(defense_recommendations, 1) as recommendation_count
FROM attack_stage_details
ORDER BY severity_level;

-- 显示导入统计
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT attack_stage) as unique_stages,
    MIN(severity_level) as min_severity,
    MAX(severity_level) as max_severity,
    AVG(severity_level) as avg_severity
FROM attack_stage_details;

-- 验证枚举值的完整性
SELECT 
    '缺失的枚举值:' as info,
    unnest(enum_range(NULL::attack_stage_enum)) as enum_value
WHERE unnest(enum_range(NULL::attack_stage_enum)) NOT IN (
    SELECT attack_stage FROM attack_stage_details
);

-- 验证数据质量
SELECT 
    attack_stage,
    chinese_name,
    CASE 
        WHEN chinese_name IS NULL OR chinese_name = '' THEN '缺少中文名称'
        WHEN english_name IS NULL OR english_name = '' THEN '缺少英文名称'
        WHEN description IS NULL OR description = '' THEN '缺少描述'
        WHEN typical_techniques IS NULL OR array_length(typical_techniques, 1) = 0 THEN '缺少典型技术'
        WHEN defense_recommendations IS NULL OR array_length(defense_recommendations, 1) = 0 THEN '缺少防护建议'
        WHEN severity_level < 1 OR severity_level > 10 THEN '严重程度超出范围'
        ELSE '数据完整'
    END as data_quality_check
FROM attack_stage_details
ORDER BY attack_stage;

-- 显示导入完成信息
DO $$
BEGIN
    RAISE NOTICE '=== 攻击阶段数据导入完成 ===';
    RAISE NOTICE '✓ 数据已从 CSV 文件导入';
    RAISE NOTICE '✓ 数据质量检查完成';
    RAISE NOTICE '✓ 枚举值完整性验证完成';
END $$;

-- push_database 数据库初始化脚本 (PostgreSQL版本)

-- 系统信息表
DROP TABLE IF EXISTS sys_info CASCADE;
CREATE TABLE sys_info (
    cpu_status REAL,
    mem_status VARCHAR(255),
    fdisk_status VARCHAR(255),
    timeS VARCHAR(255),
    run_time INTEGER,
    cpu_average REAL,
    hostname VARCHAR(255),
    osinfo VARCHAR(255),
    kernel_info VARCHAR(255),
    cpu_info VARCHAR(255),
    cpu_ker INTEGER NOT NULL DEFAULT 0,
    swap_status VARCHAR(255),
    sum_mem VARCHAR(255),
    free_mem VARCHAR(255),
    sum_fdisk VARCHAR(255),
    free_fdisk VARCHAR(255),
    ps_info VARCHAR(4096) DEFAULT '0',
    io_state VARCHAR(255) DEFAULT '0',
    device_id INTEGER DEFAULT 0,
    privileged_time BIGINT DEFAULT 0,
    id SERIAL PRIMARY KEY
);

COMMENT ON TABLE sys_info IS '系统信息表';
COMMENT ON COLUMN sys_info.cpu_status IS 'cpu占用率';
COMMENT ON COLUMN sys_info.mem_status IS '内存使用率';
COMMENT ON COLUMN sys_info.fdisk_status IS '硬盘使用率';
COMMENT ON COLUMN sys_info.timeS IS '时间';
COMMENT ON COLUMN sys_info.run_time IS '系统运行时间';
COMMENT ON COLUMN sys_info.cpu_average IS 'CPU平均负载';
COMMENT ON COLUMN sys_info.hostname IS '主机名称';
COMMENT ON COLUMN sys_info.osinfo IS '操作系统';
COMMENT ON COLUMN sys_info.kernel_info IS '内核信息';
COMMENT ON COLUMN sys_info.cpu_info IS 'cpu类型';
COMMENT ON COLUMN sys_info.cpu_ker IS 'cpu核心数';
COMMENT ON COLUMN sys_info.swap_status IS '虚拟内存使用率';
COMMENT ON COLUMN sys_info.ps_info IS '进程信息';
COMMENT ON COLUMN sys_info.io_state IS 'io占用率';
COMMENT ON COLUMN sys_info.device_id IS '设备ID';
COMMENT ON COLUMN sys_info.privileged_time IS '授权时间';

-- 任务统计表
DROP TABLE IF EXISTS task_statistic CASCADE;
CREATE TABLE task_statistic (
    bps BIGINT,
    bps_in INTEGER,
    bps_out BIGINT,
    pps BIGINT,
    pps_in BIGINT,
    pps_out BIGINT,
    conn INTEGER,
    conn_in INTEGER,
    conn_out INTEGER,
    pps_ipv4 INTEGER,
    pps_ipv6 INTEGER,
    pps_notip INTEGER,
    pps_tcp INTEGER,
    pps_udp INTEGER,
    pps_ipother INTEGER,
    create_time INTEGER,
    device_id INTEGER DEFAULT 0,
    task_id INTEGER,
    id SERIAL PRIMARY KEY
);

COMMENT ON TABLE task_statistic IS '任务统计表';
COMMENT ON COLUMN task_statistic.bps IS '当前流量bps';
COMMENT ON COLUMN task_statistic.bps_in IS '当前流量bps(进：包的目的IP为内网)';
COMMENT ON COLUMN task_statistic.bps_out IS '当前流量bps(出：包的目的IP不是内网)';
COMMENT ON COLUMN task_statistic.pps IS '当前流量pps';
COMMENT ON COLUMN task_statistic.pps_in IS '当前流量pps(进：包的目的IP为内网)';
COMMENT ON COLUMN task_statistic.pps_out IS '当前流量pps(出：包的目的IP不是内网)';
COMMENT ON COLUMN task_statistic.conn IS '当前并发连接数';
COMMENT ON COLUMN task_statistic.conn_in IS '当前并发连接数(进：会话服务端IP为内网)';
COMMENT ON COLUMN task_statistic.conn_out IS '当前并发连接数(出：会话服务端IP不是内网)';
COMMENT ON COLUMN task_statistic.device_id IS '设备ID';
COMMENT ON COLUMN task_statistic.task_id IS '任务ID';

-- 磁盘字段表
DROP TABLE IF EXISTS tb_disk_field CASCADE;
CREATE TABLE tb_disk_field (
    id SERIAL PRIMARY KEY,
    field INTEGER NOT NULL DEFAULT 0
);

COMMENT ON TABLE tb_disk_field IS '磁盘字段表';
COMMENT ON COLUMN tb_disk_field.field IS '0 读盘模式， 1 换盘模式';

-- 磁盘类型表
DROP TABLE IF EXISTS tb_disk_type CASCADE;
CREATE TABLE tb_disk_type (
    id SERIAL PRIMARY KEY,
    state INTEGER NOT NULL DEFAULT 0,
    type INTEGER NOT NULL,
    start_time BIGINT NOT NULL,
    end_time BIGINT
);

COMMENT ON TABLE tb_disk_type IS '磁盘类型表';
COMMENT ON COLUMN tb_disk_type.state IS '0 进行中，1 已完成';
COMMENT ON COLUMN tb_disk_type.type IS '1 重组 2 挂载';

-- 线路推送表
DROP TABLE IF EXISTS tb_line_push CASCADE;
CREATE TABLE tb_line_push (
    total_bytes BIGINT,
    bps BIGINT,
    total_pkts INTEGER,
    pps INTEGER,
    ts_start INTEGER,
    ts_run INTEGER,
    time BIGINT,
    device_id INTEGER DEFAULT 0,
    task_id INTEGER,
    id BIGSERIAL PRIMARY KEY
);

COMMENT ON TABLE tb_line_push IS '线路推送表';
COMMENT ON COLUMN tb_line_push.total_bytes IS '线路分析总字节数byte';
COMMENT ON COLUMN tb_line_push.bps IS '线路分析流量大小bps';
COMMENT ON COLUMN tb_line_push.total_pkts IS '线路分析总包数';
COMMENT ON COLUMN tb_line_push.pps IS '线路分析pps';
COMMENT ON COLUMN tb_line_push.ts_start IS '线路分析启动时间(时间戳)';
COMMENT ON COLUMN tb_line_push.ts_run IS '线路分析运行时间(秒)';
COMMENT ON COLUMN tb_line_push.time IS '创建时间';
COMMENT ON COLUMN tb_line_push.device_id IS '设备ID';
COMMENT ON COLUMN tb_line_push.task_id IS '任务ID';

-- MAC通信表
DROP TABLE IF EXISTS tb_mac_mac_communication CASCADE;
CREATE TABLE tb_mac_mac_communication (
    mac_1 VARCHAR(20),
    mac_2 VARCHAR(20),
    pkts INTEGER,
    ts_first INTEGER,
    ts_last INTEGER,
    time BIGINT,
    device_id INTEGER DEFAULT 0,
    task_id INTEGER,
    id BIGSERIAL PRIMARY KEY
);

COMMENT ON TABLE tb_mac_mac_communication IS 'MAC通信表';
COMMENT ON COLUMN tb_mac_mac_communication.mac_1 IS 'mac地址';
COMMENT ON COLUMN tb_mac_mac_communication.mac_2 IS 'mac地址';
COMMENT ON COLUMN tb_mac_mac_communication.pkts IS '5分钟的收包数';
COMMENT ON COLUMN tb_mac_mac_communication.ts_first IS '通联最早发现时间';
COMMENT ON COLUMN tb_mac_mac_communication.ts_last IS '通联最晚活动时间';
COMMENT ON COLUMN tb_mac_mac_communication.time IS '创建时间';
COMMENT ON COLUMN tb_mac_mac_communication.device_id IS '设备ID';
COMMENT ON COLUMN tb_mac_mac_communication.task_id IS '任务ID';

-- 创建索引
CREATE INDEX idx_tb_line_push_time ON tb_line_push(time);
CREATE INDEX idx_tb_mac_mac_communication_time ON tb_mac_mac_communication(time);

-- 产品信息表
DROP TABLE IF EXISTS tb_product_info CASCADE;
CREATE TABLE tb_product_info (
    product VARCHAR(64) NOT NULL,
    version VARCHAR(64) NOT NULL,
    SN VARCHAR(64) NOT NULL,
    device_id BIGINT DEFAULT 0
);

COMMENT ON TABLE tb_product_info IS '产品信息表';
COMMENT ON COLUMN tb_product_info.product IS '产品型号信息 版本信息_TH-sha1[0-7]_DNS';
COMMENT ON COLUMN tb_product_info.version IS '软件版本';
COMMENT ON COLUMN tb_product_info.SN IS 'SN码';
COMMENT ON COLUMN tb_product_info.device_id IS '设备ID';

-- 示例数据插入
-- INSERT INTO tb_product_info VALUES('probe_xxxxxx_xxxxxx_xxxxx','v5.1.0','xxxxxxxxxxxxxxxxxx',0);

-- nta 数据库初始化脚本
-- 创建枚举类型（系统元数据）
-- 威胁类型枚举
CREATE TYPE threat_type_enum AS ENUM (
    'MALWARE',
    'APT',
    'BOTNET',
    'PHISHING',
    'C2',
    'MINING',
    'RANSOMWARE',
    'TROJAN',
    'BACKDOOR',
    'EXPLOIT',
    'OTHER'
);

-- 威胁等级枚举 (0-4级)
CREATE TYPE threat_level_enum AS ENUM ('NONE', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- 检测器类型枚举
CREATE TYPE detector_type_enum AS ENUM (
    'CERTIFICATE',
    'NETWORK',
    'DNS',
    'HTTP',
    'SSL',
    'FINGERPRINT',
    'C2',
    'APT',
    'MALWARE',
    'BRUTEFORCE',
    'ANOMALY'
);

-- 域名类型枚举
CREATE TYPE domain_type_enum AS ENUM (
    'MALICIOUS',
    'BENIGN',
    'SUSPICIOUS',
    'WHITELIST',
    'BLACKLIST',
    'CDN'
);

-- 指纹类型枚举
CREATE TYPE fingerprint_type_enum AS ENUM (
    'HTTP',
    'SSL',
    'SSH',
    'FTP',
    'SMTP',
    'DNS',
    'TLS_JA3',
    'TLS_JA3S'
);

-- 标签来源枚举
CREATE TYPE label_source_enum AS ENUM (
    'SYSTEM',
    -- 系统内置标签
    'RULE',
    -- 规则标签（来自检测规则）
    'USER'
    -- 用户自定义标签
);

-- 标签目标类型枚举
CREATE TYPE label_target_type_enum AS ENUM (
    'IP',           -- IP地址标签
    'APPLICATION',  -- 应用标签
    'DOMAIN',       -- 域名标签
    'CERTIFICATE',  -- 证书标签
    'SESSION',      -- 会话标签
    'FINGERPRINT',  -- 指纹标签
    'MAC',          -- MAC地址标签
    'PORT'          -- 端口标签
);

-- 标签分类枚举（整合所有标签类型，包括证书标签）
CREATE TYPE label_category_enum AS ENUM (
    -- 实际使用的标签类别
    'THREAT',
    -- 威胁（533个标签）
    'KNOWLEDGE_BASE',
    -- 知识库（390个标签）
    'HIGH_DIMENSION_LABEL',
    -- 高维度标签（87个标签）
    'PROXY',
    -- 代理（56个标签）
    'ENCRYPTED_TRAFFIC_DETECTION',
    -- 加密流量检测（22个标签）
    'LEGITIMACY',
    -- 合法性（22个标签）
    'BASIC_ATTRIBUTES',
    -- 基础属性（11个标签）
    'APT',
    -- APT（5个标签）
    'BEHAVIOR_DESCRIPTION',
    -- 行为描述（3个标签）
    'BEHAVIOR_DETECTION_MODULE',
    -- 行为检测模块（2个标签）
    'COMMAND_CONTROL',
    -- 命令与控制（1个标签）
    'FINGERPRINT_DESCRIPTION',
    -- 指纹描述（1个标签）
    -- 证书标签类别
    'SECURITY',
    -- 安全
    'TRUST',
    -- 信任
    'USAGE',
    -- 使用
    'VALIDATION',
    -- 验证
    'MALICIOUS',
    -- 恶意
    -- 预留类别
    'REMOTE_CONTROL',
    -- 远程控制
    'FUNCTION_DESCRIPTION',
    -- 功能描述
    'ATTACK_INTRUSION',
    -- 攻击入侵
    'IDENTITY_SPOOFING',
    -- 身份欺骗
    'CIRCUMVENTION',
    -- 翻墙上网
    'MAN_IN_MIDDLE',
    -- 中间人
    'PRIVATE_DETECTION' -- 私有检测
);

-- 告警统计表
DROP TABLE IF EXISTS alarm_statistics CASCADE;

CREATE TABLE alarm_statistics (
    hour_times INTEGER NOT NULL,
    cyber_kill_chain cyber_kill_chain_enum NOT NULL,
    num INTEGER NOT NULL DEFAULT 0,
    tkey BIGINT PRIMARY KEY
);

COMMENT ON TABLE alarm_statistics IS '告警数量统计表';

COMMENT ON COLUMN alarm_statistics.hour_times IS '时间按一小时为粒度';

COMMENT ON COLUMN alarm_statistics.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';

COMMENT ON COLUMN alarm_statistics.num IS '当前时间段的告警数量';

-- 应用协议信息表
DROP TABLE IF EXISTS application_protocol_info CASCADE;

CREATE TABLE application_protocol_info (
    id SERIAL PRIMARY KEY,
    port INTEGER,
    appid INTEGER,
    Ippro INTEGER,
    remark TEXT NOT NULL
);

COMMENT ON TABLE application_protocol_info IS '应用协议信息表';

COMMENT ON COLUMN application_protocol_info.port IS '端口';

COMMENT ON COLUMN application_protocol_info.appid IS 'app ID';

COMMENT ON COLUMN application_protocol_info.Ippro IS '17 udp 6 tcp';

-- Cyber Kill Chain枚举（基于Lockheed Martin Cyber Kill Chain模型）
DROP TYPE IF EXISTS cyber_kill_chain_enum CASCADE;

CREATE TYPE cyber_kill_chain_enum AS ENUM (
    'RECONNAISSANCE',
    -- 侦察探测
    'WEAPONIZATION',
    -- 武器化
    'DELIVERY',
    -- 投递
    'EXPLOITATION',
    -- 漏洞利用
    'INSTALLATION',
    -- 安装植入
    'COMMAND_AND_CONTROL',
    -- 命令控制
    'ACTIONS_ON_OBJECTIVES',
    -- 目标行动
    'OTHER',
    -- 其他
    'UNKNOWN' -- 未知
);

-- 规则来源枚举
DROP TYPE IF EXISTS rule_source_enum CASCADE;

CREATE TYPE rule_source_enum AS ENUM (
    'SYSTEM',  -- 系统内置 (0)
    'USER'     -- 用户自定义 (1)
);

-- 采集模式枚举
DROP TYPE IF EXISTS capture_mode_enum CASCADE;

CREATE TYPE capture_mode_enum AS ENUM (
    'SINGLE_PACKET',        -- 单包 (1)
    'CONNECTION',           -- 连接 (2)
    'SOURCE_IP',           -- 获取源IP (3)
    'DEST_IP',             -- 获取目的IP (4)
    'SOURCE_IP_PORT',      -- 获取源IP+Port (5)
    'DEST_IP_PORT',        -- 获取目的IP+Port (6)
    'SOURCE_DEST_IP',      -- 获取源IP+目的IP (7)
    'DNS_CLIENT_IP',       -- 获取DNS 客户端IP (8)
    'DNS_A_RECORD_IP',     -- 获取DNS A记录端IP (9)
    'DNS_CLIENT_A_RECORD'  -- 获取DNS 客户端+A记录 (10)
);

-- 过滤条件枚举
DROP TYPE IF EXISTS filter_criteria_enum CASCADE;

CREATE TYPE filter_criteria_enum AS ENUM (
    'PORT',                -- 按端口过滤 (0)
    'INTERNET_PROTOCOL',   -- 按互联网协议过滤 (1)
    'SUBNET'               -- 按子网过滤 (2)
);

COMMENT ON TYPE cyber_kill_chain_enum IS 'Cyber Kill Chain枚举，基于Lockheed Martin标准杀伤链模型';

-- Cyber Kill Chain详情表（提供枚举值的详细信息）
DROP TABLE IF EXISTS cyber_kill_chain_details CASCADE;

CREATE TABLE cyber_kill_chain_details (
    cyber_kill_chain cyber_kill_chain_enum PRIMARY KEY,
    chinese_name VARCHAR(64) NOT NULL,
    english_name VARCHAR(64) NOT NULL,
    description TEXT DEFAULT '',
    typical_techniques TEXT [] DEFAULT '{}',
    defense_recommendations TEXT [] DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON
TABLE cyber_kill_chain_details IS 'Cyber Kill Chain详情表，提供枚举值的详细信息';

COMMENT ON COLUMN cyber_kill_chain_details.cyber_kill_chain IS 'Cyber Kill Chain阶段枚举值（主键）';

COMMENT ON COLUMN cyber_kill_chain_details.chinese_name IS '阶段中文名称';

COMMENT ON COLUMN cyber_kill_chain_details.english_name IS '阶段英文名称';

COMMENT ON COLUMN cyber_kill_chain_details.description IS '阶段详细描述';

COMMENT ON COLUMN cyber_kill_chain_details.typical_techniques IS '典型攻击技术列表';

COMMENT ON COLUMN cyber_kill_chain_details.defense_recommendations IS '防护建议列表';

COMMENT ON COLUMN cyber_kill_chain_details.created_at IS '创建时间';

COMMENT ON COLUMN cyber_kill_chain_details.updated_at IS '更新时间';

-- 图探索历史表
DROP TABLE IF EXISTS graph_exploration_history CASCADE;

CREATE TABLE graph_exploration_history (
    id SERIAL PRIMARY KEY,
    atlas_condition TEXT,
    created_at TIMESTAMP
);

-- 任务批次插件关联表
DROP TABLE IF EXISTS task_batch_plugin CASCADE;

CREATE TABLE task_batch_plugin (
    id SERIAL PRIMARY KEY,
    batch_id INTEGER NOT NULL,
    plugin_id INTEGER NOT NULL,
    should_log_def INTEGER NOT NULL
);

COMMENT ON TABLE task_batch_plugin IS '任务批次插件关联表';

COMMENT ON COLUMN task_batch_plugin.batch_id IS '批次ID';

COMMENT ON COLUMN task_batch_plugin.plugin_id IS '插件ID';

COMMENT ON COLUMN task_batch_plugin.should_log_def IS '开启的插件iD默认值，1 为开启 0 位关闭';

-- DDoS防护配置表
DROP TABLE IF EXISTS ddos_protection_config CASCADE;

CREATE TABLE ddos_protection_config (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    doss_json JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash VARCHAR(255) NOT NULL,
    state INTEGER NOT NULL DEFAULT 1
);

-- DDoS防护状态表
DROP TABLE IF EXISTS ddos_protection_state CASCADE;

CREATE TABLE ddos_protection_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    ddos_param_json JSON NOT NULL
);

COMMENT ON TABLE ddos_protection_state IS 'DDoS防护状态表';

COMMENT ON COLUMN ddos_protection_state.task_id IS '任务ID';

COMMENT ON COLUMN ddos_protection_state.state IS '0表示停用，1表示开启';

-- 设备IP映射表
DROP TABLE IF EXISTS device_ip_mapping CASCADE;

CREATE TABLE device_ip_mapping (
    device_id VARCHAR(64) PRIMARY KEY,
    ip VARCHAR(64) NOT NULL
);

COMMENT ON COLUMN device_ip_mapping.ip IS 'IP';

-- DNS服务器配置表
DROP TABLE IF EXISTS dns_server_config CASCADE;

CREATE TABLE dns_server_config (
    tkey BIGINT PRIMARY KEY,
    ip VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source INTEGER NOT NULL,
    client_dis VARCHAR(2048) NOT NULL,
    task_id INTEGER NOT NULL
);

COMMENT ON COLUMN dns_server_config.ip IS 'IP';

COMMENT ON COLUMN dns_server_config.source IS '来源';

COMMENT ON COLUMN dns_server_config.client_dis IS 'client';

COMMENT ON COLUMN dns_server_config.task_id IS '任务ID';

-- 注意：域名Alexa排名表和域名WHOIS信息表已迁移到 knowledgebase 数据库的 domain_knowledge 表中
-- 威胁知识告警表
DROP TABLE IF EXISTS threat_knowledge_alarm CASCADE;

CREATE TABLE threat_knowledge_alarm (
    knowledge_alarm_id INTEGER NOT NULL,
    alarm_name VARCHAR(64) NOT NULL,
    cyber_kill_chain cyber_kill_chain_enum NOT NULL DEFAULT 'UNKNOWN',
    attack_time VARCHAR(256) DEFAULT '',
    relation_label_id VARCHAR(2048) DEFAULT '',
    exclude_label_id VARCHAR(2048) DEFAULT '',
    black_list INTEGER NOT NULL DEFAULT 0,
    remark VARCHAR(2048) DEFAULT '',
    id BIGSERIAL PRIMARY KEY,
    UNIQUE (knowledge_alarm_id)
);

COMMENT ON TABLE threat_knowledge_alarm IS '威胁知识告警表';

COMMENT ON COLUMN threat_knowledge_alarm.knowledge_alarm_id IS '告警ID';

COMMENT ON COLUMN threat_knowledge_alarm.alarm_name IS '告警名称';

COMMENT ON COLUMN threat_knowledge_alarm.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';

COMMENT ON COLUMN threat_knowledge_alarm.relation_label_id IS '可能关联的告警名称，逗号分割';

COMMENT ON COLUMN threat_knowledge_alarm.exclude_label_id IS '排除的告警名称，逗号分割';

COMMENT ON COLUMN threat_knowledge_alarm.black_list IS '黑名单权重';

COMMENT ON COLUMN threat_knowledge_alarm.remark IS '备注说明';

-- 内部证书白名单表
DROP TABLE IF EXISTS internal_certificate_whitelist CASCADE;

CREATE TABLE internal_certificate_whitelist (
    cert_sha1 VARCHAR(64) PRIMARY KEY,
    task_id INTEGER NOT NULL,
    link_ip TEXT NOT NULL,
    remark TEXT NOT NULL
);

COMMENT ON TABLE internal_certificate_whitelist IS '内部证书白名单表';

COMMENT ON COLUMN internal_certificate_whitelist.cert_sha1 IS '证书sha1';

COMMENT ON COLUMN internal_certificate_whitelist.task_id IS '任务ID';

COMMENT ON COLUMN internal_certificate_whitelist.link_ip IS '关联ip';

COMMENT ON COLUMN internal_certificate_whitelist.remark IS '备注';

-- 内部域名白名单表
DROP TABLE IF EXISTS internal_domain_whitelist CASCADE;

CREATE TABLE internal_domain_whitelist (
    domain_name VARCHAR(255) PRIMARY KEY,
    task_id INTEGER NOT NULL,
    type INTEGER NOT NULL DEFAULT 1,
    link_ip TEXT NOT NULL,
    remark TEXT NOT NULL
);

COMMENT ON TABLE internal_domain_whitelist IS '内部域名白名单表';

COMMENT ON COLUMN internal_domain_whitelist.domain_name IS '域名';

COMMENT ON COLUMN internal_domain_whitelist.task_id IS '任务ID';

COMMENT ON COLUMN internal_domain_whitelist.type IS '0代表精确域名，1代表N级域名';

COMMENT ON COLUMN internal_domain_whitelist.link_ip IS '关联ip';

COMMENT ON COLUMN internal_domain_whitelist.remark IS '备注';

-- 内部IP白名单表
DROP TABLE IF EXISTS internal_ip_whitelist CASCADE;

CREATE TABLE internal_ip_whitelist (
    ip VARCHAR(255) PRIMARY KEY,
    task_id INTEGER NOT NULL,
    remark TEXT NOT NULL
);

COMMENT ON TABLE internal_ip_whitelist IS '内部IP白名单表';

COMMENT ON COLUMN internal_ip_whitelist.ip IS 'ip';

COMMENT ON COLUMN internal_ip_whitelist.task_id IS '任务ID';

COMMENT ON COLUMN internal_ip_whitelist.remark IS '备注';

-- 创建索引
CREATE INDEX idx_alarm_statistics_hour_times ON alarm_statistics (hour_times);

CREATE INDEX idx_alarm_statistics_attack_stage ON alarm_statistics (attack_stage);

CREATE INDEX idx_application_protocol_info_port ON application_protocol_info (port);

CREATE INDEX idx_application_protocol_info_appid ON application_protocol_info (appid);

-- 注意：已迁移到 knowledgebase 数据库的表的索引已在对应数据库中创建
CREATE INDEX idx_dns_server_config_ip ON dns_server_config (ip);

CREATE INDEX idx_dns_server_config_task_id ON dns_server_config (task_id);

CREATE UNIQUE INDEX idx_threat_knowledge_alarm_id ON threat_knowledge_alarm (knowledge_alarm_id);

-- 系统元数据表索引
-- IP标签表索引
CREATE INDEX idx_ip_labels_category ON ip_labels (category);

CREATE INDEX idx_ip_labels_status ON ip_labels (status);

CREATE INDEX idx_ip_labels_attack_stage ON ip_labels (attack_stage);

CREATE INDEX idx_ip_labels_created_by ON ip_labels (created_by);

-- 应用标签表索引
CREATE INDEX idx_application_labels_category ON application_labels (category);

CREATE INDEX idx_application_labels_status ON application_labels (status);

CREATE INDEX idx_application_labels_attack_stage ON application_labels (attack_stage);

CREATE INDEX idx_application_labels_created_by ON application_labels (created_by);

-- 域名标签表索引
CREATE INDEX idx_domain_labels_category ON domain_labels (category);

CREATE INDEX idx_domain_labels_status ON domain_labels (status);

CREATE INDEX idx_domain_labels_attack_stage ON domain_labels (attack_stage);

CREATE INDEX idx_domain_labels_created_by ON domain_labels (created_by);

-- 证书标签表索引
CREATE INDEX idx_certificate_labels_category ON certificate_labels (category);

CREATE INDEX idx_certificate_labels_status ON certificate_labels (status);

CREATE INDEX idx_certificate_labels_attack_stage ON certificate_labels (attack_stage);

CREATE INDEX idx_certificate_labels_created_by ON certificate_labels (created_by);

-- 会话标签表索引
CREATE INDEX idx_session_labels_category ON session_labels (category);

CREATE INDEX idx_session_labels_status ON session_labels (status);

CREATE INDEX idx_session_labels_attack_stage ON session_labels (attack_stage);

CREATE INDEX idx_session_labels_created_by ON session_labels (created_by);

-- 指纹标签表索引
CREATE INDEX idx_fingerprint_labels_category ON fingerprint_labels (category);

CREATE INDEX idx_fingerprint_labels_status ON fingerprint_labels (status);

CREATE INDEX idx_fingerprint_labels_attack_stage ON fingerprint_labels (attack_stage);

CREATE INDEX idx_fingerprint_labels_created_by ON fingerprint_labels (created_by);

CREATE INDEX idx_alarm_knowledge_id ON alarm_knowledge (knowledge_id);

CREATE INDEX idx_alarm_knowledge_active ON alarm_knowledge (is_active);

CREATE INDEX idx_detector_config_name ON detector_config (detector_name);

CREATE INDEX idx_detector_config_type ON detector_config (detector_type);

CREATE INDEX idx_detector_config_enabled ON detector_config (enabled);

-- 协议表索引
CREATE INDEX idx_network_protocols_id ON network_protocols (id);

CREATE INDEX idx_network_protocols_protocol_name ON network_protocols (protocol_name);

CREATE INDEX idx_network_protocols_category ON network_protocols (category);

CREATE INDEX idx_network_protocols_protocol_type ON network_protocols (protocol_type);

CREATE INDEX idx_internet_protocols_protocol_number ON internet_protocols (protocol_number);

CREATE INDEX idx_internet_protocols_keyword ON internet_protocols (keyword);

-- 检测规则配置表索引
CREATE INDEX idx_detection_rule_configs_task_id ON detection_rule_configs (task_id);

CREATE INDEX idx_detection_rule_configs_rule_state ON detection_rule_configs (rule_state);

CREATE INDEX idx_detection_rule_configs_attack_stage ON detection_rule_configs (attack_stage);

CREATE INDEX idx_detection_rule_configs_rule_level ON detection_rule_configs (rule_level);

-- 检测规则统计表索引
CREATE INDEX idx_detection_rule_statistics_last_hit_time ON detection_rule_statistics (last_hit_time);

CREATE INDEX idx_detection_rule_statistics_total_sum_bytes ON detection_rule_statistics (total_sum_bytes);

-- 威胁检测算法配置表索引
CREATE INDEX idx_threat_detection_algorithm_configs_state ON threat_detection_algorithm_configs (state);

CREATE INDEX idx_threat_detection_algorithm_configs_algorithm_type ON threat_detection_algorithm_configs (algorithm_type);

CREATE INDEX idx_threat_detection_algorithm_configs_algorithm_name ON threat_detection_algorithm_configs (algorithm_name);

-- 威胁检测算法统计表索引
CREATE INDEX idx_threat_detection_algorithm_statistics_last_size_time ON threat_detection_algorithm_statistics (last_size_time);

CREATE INDEX idx_threat_detection_algorithm_statistics_total_sum_bytes ON threat_detection_algorithm_statistics (total_sum_bytes);

-- DDoS防护统计表
DROP TABLE IF EXISTS ddos_protection_statistics CASCADE;

CREATE TABLE ddos_protection_statistics (
    id SERIAL PRIMARY KEY,
    ddos_type INTEGER NOT NULL,
    task_id INTEGER NOT NULL,
    packet_num INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE ddos_protection_statistics IS 'DDoS防护统计信息，按时间统计';

COMMENT ON COLUMN ddos_protection_statistics.ddos_type IS 'DDoS类型';

COMMENT ON COLUMN ddos_protection_statistics.task_id IS '任务ID';

COMMENT ON COLUMN ddos_protection_statistics.packet_num IS '包数量';

COMMENT ON COLUMN ddos_protection_statistics.create_time IS '创建时间';

-- 数据导出任务表
DROP TABLE IF EXISTS data_export_task CASCADE;

CREATE TABLE data_export_task (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT,
    show_query VARCHAR(2048),
    type INTEGER NOT NULL DEFAULT 0,
    session_id TEXT,
    state INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status INTEGER NOT NULL DEFAULT 1,
    task_id VARCHAR(200)
);

COMMENT ON TABLE data_export_task IS '数据导出任务表';

COMMENT ON COLUMN data_export_task.created_by IS '创建者用户ID';

COMMENT ON COLUMN data_export_task.path IS '文件路径';

COMMENT ON COLUMN data_export_task.query IS 'ES 下载 检索条件';

COMMENT ON COLUMN data_export_task.show_query IS '前端展示条件';

COMMENT ON COLUMN data_export_task.type IS '全量下载为1，部分下载为0';

COMMENT ON COLUMN data_export_task.session_id IS 'session 列表信息';

COMMENT ON COLUMN data_export_task.state IS '0 准备数据 1可下载 2重新下载 3已删除 4待删除';

COMMENT ON COLUMN data_export_task.status IS '数据状态 0 删除 1存在';

COMMENT ON COLUMN data_export_task.task_id IS '任务ID（数组）';

-- 数据导出任务注册表
DROP TABLE IF EXISTS data_export_task_register CASCADE;

CREATE TABLE data_export_task_register (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT NOT NULL,
    type INTEGER NOT NULL,
    download_count INTEGER NOT NULL DEFAULT 0,
    delete_time TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type INTEGER NOT NULL DEFAULT 1,
    status INTEGER NOT NULL DEFAULT 1,
    error_msg VARCHAR(255)
);

COMMENT ON TABLE data_export_task_register IS '数据导出任务注册表';

COMMENT ON COLUMN data_export_task_register.created_by IS '创建者用户ID';

COMMENT ON COLUMN data_export_task_register.path IS '日志保存路径';

COMMENT ON COLUMN data_export_task_register.query IS '查询条件';

COMMENT ON COLUMN data_export_task_register.type IS '日志状态，1 待执行，2 准备数据，3 待下载，4 已删除，-1错误';

COMMENT ON COLUMN data_export_task_register.download_count IS '下载次数';

COMMENT ON COLUMN data_export_task_register.task_type IS '1 会话分析、2 会话聚合、3 元数据_SSL、4 元数据_HTTP、5 元数据_DNS';

COMMENT ON COLUMN data_export_task_register.status IS '0 已删除 1 存在';

COMMENT ON COLUMN data_export_task_register.error_msg IS '任务失败的原因';

-- Elasticsearch字段配置表
DROP TABLE IF EXISTS elasticsearch_field_config CASCADE;

CREATE TABLE elasticsearch_field_config (
    id SERIAL PRIMARY KEY,
    es_field TEXT NOT NULL
);

COMMENT ON TABLE elasticsearch_field_config IS 'Elasticsearch字段配置表';

-- 流量过滤统计表
DROP TABLE IF EXISTS traffic_filter_statistics CASCADE;

CREATE TABLE traffic_filter_statistics (
    id SERIAL PRIMARY KEY,
    filter_id INTEGER NOT NULL,
    batch_id INTEGER NOT NULL,
    packet_num INTEGER NOT NULL,
    bytes BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE traffic_filter_statistics IS '流量过滤统计表';

COMMENT ON COLUMN traffic_filter_statistics.filter_id IS '过滤ID';

COMMENT ON COLUMN traffic_filter_statistics.batch_id IS '批次ID';

COMMENT ON COLUMN traffic_filter_statistics.packet_num IS '过滤的包数';

COMMENT ON COLUMN traffic_filter_statistics.bytes IS '过滤掉的字节数';

COMMENT ON COLUMN traffic_filter_statistics.create_time IS '创建时间';

-- 过滤规则表
DROP TABLE IF EXISTS filter_rule CASCADE;

CREATE TABLE filter_rule (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    filter_json JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash VARCHAR(255) NOT NULL,
    criteria filter_criteria_enum NOT NULL,
    active BOOLEAN NOT NULL DEFAULT true
);

COMMENT ON TABLE filter_rule IS '过滤规则表';

COMMENT ON COLUMN filter_rule.task_id IS '任务ID';

COMMENT ON COLUMN filter_rule.ip IS 'IP地址';

COMMENT ON COLUMN filter_rule.filter_json IS '过滤规则JSON字符串';

COMMENT ON COLUMN filter_rule.created_at IS '创建时间';

COMMENT ON COLUMN filter_rule.updated_at IS '更新时间';

COMMENT ON COLUMN filter_rule.hash IS '规则hash值，用于校验重复';

COMMENT ON COLUMN filter_rule.criteria IS '过滤条件，使用filter_criteria_enum枚举';

COMMENT ON COLUMN filter_rule.active IS '是否激活 true:正常 false:删除';

-- 过滤模式表
DROP TABLE IF EXISTS filter_mode CASCADE;

CREATE TABLE filter_mode (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    drop_mode BOOLEAN NOT NULL
);

COMMENT ON TABLE filter_mode IS '过滤模式表';

COMMENT ON COLUMN filter_mode.task_id IS '任务ID';

COMMENT ON COLUMN filter_mode.drop_mode IS '是否丢弃模式 false表示保留，true表示丢弃';

-- 内部网络段表
DROP TABLE IF EXISTS internal_network_segment CASCADE;

CREATE TABLE internal_network_segment (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    inter_ip VARCHAR(50) NOT NULL,
    ip_mask VARCHAR(50) NOT NULL,
    mac VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internal_network_segment IS '内部网络段表';

COMMENT ON COLUMN internal_network_segment.task_id IS '任务id';

COMMENT ON COLUMN internal_network_segment.inter_ip IS '内网ip地址';

COMMENT ON COLUMN internal_network_segment.ip_mask IS '子网掩码';

COMMENT ON COLUMN internal_network_segment.mac IS 'mac地址';

COMMENT ON COLUMN internal_network_segment.last_modified_time IS '最后修改时间';

-- 网络拓扑分析表
DROP TABLE IF EXISTS network_topology_analysis CASCADE;

CREATE TABLE network_topology_analysis (
    task_id INTEGER NOT NULL,
    type_name VARCHAR(255) NOT NULL PRIMARY KEY,
    text TEXT NOT NULL,
    type INTEGER NOT NULL
);

COMMENT ON TABLE network_topology_analysis IS '网络拓扑分析表';

COMMENT ON COLUMN network_topology_analysis.task_id IS '任务ID';

COMMENT ON COLUMN network_topology_analysis.type_name IS '线路分析的json';

COMMENT ON COLUMN network_topology_analysis.text IS '内网网段数量';

COMMENT ON COLUMN network_topology_analysis.type IS 'Mac数量';

-- 网络拓扑分析参数表
DROP TABLE IF EXISTS network_topology_analysis_param CASCADE;

CREATE TABLE network_topology_analysis_param (
    task_id INTEGER NOT NULL,
    type VARCHAR(255) NOT NULL PRIMARY KEY,
    max_segment_num INTEGER NOT NULL,
    max_mac_num INTEGER NOT NULL
);

COMMENT ON TABLE network_topology_analysis_param IS '网络拓扑分析参数表';

COMMENT ON COLUMN network_topology_analysis_param.task_id IS '任务ID';

COMMENT ON COLUMN network_topology_analysis_param.type IS 'LINE:线路分析参数';

COMMENT ON COLUMN network_topology_analysis_param.max_segment_num IS '内网网段数量';

COMMENT ON COLUMN network_topology_analysis_param.max_mac_num IS 'Mac数量';

-- 日志插件配置表
DROP TABLE IF EXISTS log_plugin_config CASCADE;

CREATE TABLE log_plugin_config (
    id BIGSERIAL PRIMARY KEY,
    plug_name TEXT NOT NULL,
    plug_type VARCHAR(255) NOT NULL,
    plug_json JSON NOT NULL,
    plug_remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    plug_hash VARCHAR(255) NOT NULL
);

-- 初始化DDoS防护状态数据
INSERT INTO
    ddos_protection_state (
        id,
        task_id,
        state,
        ddos_param_json
    )
VALUES (
        1001,
        1,
        1,
        '{"Type":["IntraIP","LegalIP","All"],"IPv4Num":500000,"IPv6Num":1000,"RenewTime":6,"Param":{"TimeInterval_Judge":6,"BasicLine_bps":1000,"BasicLine_PacketNum":600,"CheckSum":[256,256],"DDOS_CheckSum":[4,4],"DDOS_SYN":4,"DDOS_FIN":4,"DDOS_DNS":4,"DDOS_ICMP":4,"DDOS_IGMP":4,"DDOS_UDP":4,"DDOS_Frag":4,"DDOS_Multicast":4,"MaxOffset_IP":1250}}'
    ),
    (
        1002,
        0,
        1,
        '{"Type":["IntraIP","LegalIP","All"],"IPv4Num":500000,"IPv6Num":1000,"RenewTime":6,"Param":{"TimeInterval_Judge":6,"BasicLine_bps":1000,"BasicLine_PacketNum":600,"CheckSum":[256,256],"DDOS_CheckSum":[4,4],"DDOS_SYN":4,"DDOS_FIN":4,"DDOS_DNS":4,"DDOS_ICMP":4,"DDOS_IGMP":4,"DDOS_UDP":4,"DDOS_Frag":4,"DDOS_Multicast":4,"MaxOffset_IP":1250}}'
    );

-- 威胁检测算法配置表
DROP TABLE IF EXISTS threat_detection_algorithm_configs CASCADE;

CREATE TABLE threat_detection_algorithm_configs (
    algorithm_id INTEGER PRIMARY KEY,
    algorithm_name VARCHAR(1024) NOT NULL,
    algorithm_type VARCHAR(512) NOT NULL,
    description VARCHAR(2048) NOT NULL,
    algorithm_hash VARCHAR(255),
    algorithm_version VARCHAR(255),
    algorithm_path VARCHAR(255),
    state INTEGER NOT NULL DEFAULT 1,
    retain_metadata BOOLEAN NOT NULL DEFAULT false,
    retain_pcap BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_detection_algorithm_configs IS '威胁检测算法配置表';

COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_id IS '算法ID';

COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_name IS '算法名称';

COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_type IS '算法类型（协议识别、特征识别、行为识别、LSTM神经网络、随机森林等）';

COMMENT ON COLUMN threat_detection_algorithm_configs.description IS '算法描述';

COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_hash IS '算法文件哈希';

COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_path IS '算法文件路径';

COMMENT ON COLUMN threat_detection_algorithm_configs.state IS '算法状态（1=启用，0=禁用）';

COMMENT ON COLUMN threat_detection_algorithm_configs.retain_metadata IS '是否留存流量元数据';

COMMENT ON COLUMN threat_detection_algorithm_configs.retain_pcap IS '是否留存原始流量数据（PCAP文件）';

-- 威胁检测算法统计表
DROP TABLE IF EXISTS threat_detection_algorithm_statistics CASCADE;

CREATE TABLE threat_detection_algorithm_statistics (
    algorithm_id INTEGER PRIMARY KEY REFERENCES threat_detection_algorithm_configs (algorithm_id),
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON
TABLE threat_detection_algorithm_statistics IS '威胁检测算法统计表';

COMMENT ON COLUMN threat_detection_algorithm_statistics.algorithm_id IS '算法ID';

COMMENT ON COLUMN threat_detection_algorithm_statistics.total_sum_bytes IS '算法命中数据总量';

COMMENT ON COLUMN threat_detection_algorithm_statistics.last_hit_time IS '最新命中时间';

-- 网络设备配置表
DROP TABLE IF EXISTS network_device_config CASCADE;

CREATE TABLE network_device_config (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    device_name TEXT NOT NULL,
    mac VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_device_config IS '网络设备配置表';

COMMENT ON COLUMN network_device_config.task_id IS '任务ID';

COMMENT ON COLUMN network_device_config.device_name IS '设备名称';

COMMENT ON COLUMN network_device_config.mac IS 'mac';

-- 网络流量配置表
DROP TABLE IF EXISTS network_flow_config CASCADE;

CREATE TABLE network_flow_config (
    id SERIAL PRIMARY KEY,
    pcie_id VARCHAR(24) NOT NULL,
    flow_name TEXT NOT NULL,
    network_type TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_flow_config IS '网络流量配置表';

COMMENT ON COLUMN network_flow_config.pcie_id IS '任务ID';

COMMENT ON COLUMN network_flow_config.flow_name IS '任务名称';

COMMENT ON COLUMN network_flow_config.network_type IS '任务备注';

-- 网络防护状态表
DROP TABLE IF EXISTS network_protection_state CASCADE;

CREATE TABLE network_protection_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    network_param_json JSON NOT NULL
);

COMMENT ON TABLE network_protection_state IS '网络防护状态表';

COMMENT ON COLUMN network_protection_state.task_id IS '任务ID';

COMMENT ON COLUMN network_protection_state.state IS '0表示停用，1表示开启';

-- 网络防护统计表
DROP TABLE IF EXISTS network_protection_statistics CASCADE;

CREATE TABLE network_protection_statistics (
    id SERIAL PRIMARY KEY,
    type INTEGER NOT NULL,
    batch_id INTEGER NOT NULL,
    packet_num INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_protection_statistics IS '网络防护统计表';

COMMENT ON COLUMN network_protection_statistics.batch_id IS '批次ID';

COMMENT ON COLUMN network_protection_statistics.packet_num IS '过滤的包数';

-- 数据包缓存统计表
DROP TABLE IF EXISTS packet_buffer_statistics CASCADE;

CREATE TABLE packet_buffer_statistics (
    id SERIAL PRIMARY KEY,
    all_pb_num BIGINT,
    all_pb_bytes BIGINT,
    num_30s_pb BIGINT,
    bytes_30s_pb BIGINT,
    num_30s_pcap BIGINT,
    bytes_30s_pcap BIGINT,
    task_id INTEGER
);

COMMENT ON TABLE packet_buffer_statistics IS '数据包缓存统计表';

COMMENT ON COLUMN packet_buffer_statistics.all_pb_num IS '启动后日志提取总条目数';

COMMENT ON COLUMN packet_buffer_statistics.all_pb_bytes IS '启动后日志提取总字节数';

-- 分析插件表
DROP TABLE IF EXISTS analysis_plugin CASCADE;

CREATE TABLE analysis_plugin (
    plugin_id INTEGER PRIMARY KEY,
    plugin_name VARCHAR(255),
    plugin_type INTEGER,
    remark VARCHAR(1024)
);

COMMENT ON TABLE analysis_plugin IS '分析插件表';

COMMENT ON COLUMN analysis_plugin.plugin_id IS '插件Id';

COMMENT ON COLUMN analysis_plugin.plugin_name IS '插件名称';

COMMENT ON COLUMN analysis_plugin.plugin_type IS '插件类型 1 全流量 2 协议解析';

COMMENT ON COLUMN analysis_plugin.remark IS '模型描述';

-- 检测规则配置表
DROP TABLE IF EXISTS detection_rule_configs CASCADE;

CREATE TABLE detection_rule_configs (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    label_id INTEGER NOT NULL UNIQUE,
    threat_level threat_level_enum NOT NULL,
    rule_name TEXT NOT NULL,
    rule_desc TEXT NOT NULL,
    rule_enabled BOOLEAN NOT NULL DEFAULT true,
    rule_source rule_source_enum NOT NULL DEFAULT 'SYSTEM',
    capture_mode capture_mode_enum NOT NULL,
    rule_json JSON NOT NULL,
    rule_hash VARCHAR(255) NOT NULL,
    cyber_kill_chain cyber_kill_chain_enum DEFAULT 'OTHER',
    traffic_rate_limit_bps BIGINT NOT NULL DEFAULT 0,
    traffic_retention_limit_bytes BIGINT NOT NULL DEFAULT -1,
    retain_metadata BOOLEAN NOT NULL DEFAULT false,
    retain_pcap BOOLEAN NOT NULL DEFAULT false,
    lib_respond_enabled BOOLEAN NOT NULL DEFAULT false,
    lib_respond_lib VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_config VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_session_end BIGINT NOT NULL DEFAULT 0,
    lib_respond_pkt_num BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_configs IS '检测规则配置表';

COMMENT ON COLUMN detection_rule_configs.label_id IS '标签ID（规则命中时打上的标签）';

COMMENT ON COLUMN detection_rule_configs.threat_level IS '威胁等级，使用threat_level_enum枚举';

COMMENT ON COLUMN detection_rule_configs.rule_name IS '规则名称（告警类型）';

COMMENT ON COLUMN detection_rule_configs.rule_desc IS '规则描述';

COMMENT ON COLUMN detection_rule_configs.rule_enabled IS '是否启用规则 true:生效 false:失效';

COMMENT ON COLUMN detection_rule_configs.rule_source IS '规则来源，使用rule_source_enum枚举';

COMMENT ON COLUMN detection_rule_configs.capture_mode IS '采集模式，使用capture_mode_enum枚举';

COMMENT ON COLUMN detection_rule_configs.rule_json IS '规则JSON配置';

COMMENT ON COLUMN detection_rule_configs.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';

COMMENT ON COLUMN detection_rule_configs.traffic_rate_limit_bps IS '流量留存限速，单位：字节/秒';

COMMENT ON COLUMN detection_rule_configs.traffic_retention_limit_bytes IS '流量留存上限，单位：字节（负数表示不限）';

COMMENT ON COLUMN detection_rule_configs.retain_metadata IS '是否留存流量元数据';

COMMENT ON COLUMN detection_rule_configs.retain_pcap IS '是否留存原始流量数据（PCAP文件）';

COMMENT ON COLUMN detection_rule_configs.lib_respond_enabled IS '是否开启动态库响应';

-- 检测规则统计表
DROP TABLE IF EXISTS detection_rule_statistics CASCADE;

CREATE TABLE detection_rule_statistics (
    rule_id INTEGER PRIMARY KEY REFERENCES detection_rule_configs (rule_id),
    rule_size BIGINT NOT NULL DEFAULT 0,
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_statistics IS '检测规则统计表';

COMMENT ON COLUMN detection_rule_statistics.rule_id IS '规则ID';

COMMENT ON COLUMN detection_rule_statistics.rule_size IS '规则数据量';

COMMENT ON COLUMN detection_rule_statistics.total_sum_bytes IS '命中数据总量';

COMMENT ON COLUMN detection_rule_statistics.last_hit_time IS '最新命中时间';

-- 检测规则库配置表
DROP TABLE IF EXISTS detection_rule_library_config CASCADE;

CREATE TABLE detection_rule_library_config (
    id BIGSERIAL PRIMARY KEY,
    lib_path TEXT NOT NULL,
    config_path TEXT NOT NULL
);

COMMENT ON TABLE detection_rule_library_config IS '检测规则库配置表';

-- 会话分析表
DROP TABLE IF EXISTS session_analysis CASCADE;

CREATE TABLE session_analysis (
    session_id VARCHAR(255) PRIMARY KEY,
    black_list INTEGER NOT NULL,
    white_list INTEGER NOT NULL,
    remark VARCHAR(4096) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统状态配置表
DROP TABLE IF EXISTS system_state_config CASCADE;

CREATE TABLE system_state_config (
    id BIGSERIAL PRIMARY KEY,
    type_name VARCHAR(255) NOT NULL,
    type_value TEXT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE system_state_config IS '系统状态配置表';

COMMENT ON COLUMN system_state_config.type_name IS '功能状态简写';

COMMENT ON COLUMN system_state_config.type_value IS '功能状态详细说明';

COMMENT ON COLUMN system_state_config.field_name IS '字段名称';

-- 目标组表
DROP TABLE IF EXISTS target_group CASCADE;

CREATE TABLE target_group (
    id SERIAL PRIMARY KEY,
    name VARCHAR(256) NOT NULL
);

COMMENT ON TABLE target_group IS '目标组表';

COMMENT ON COLUMN target_group.name IS '目标组名称';

-- 目标备注表
DROP TABLE IF EXISTS target_remark CASCADE;

CREATE TABLE target_remark (
    id SERIAL PRIMARY KEY,
    target_key VARCHAR(200) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    remark VARCHAR(200) NOT NULL
);

COMMENT ON TABLE target_remark IS '目标备注表';

COMMENT ON COLUMN target_remark.target_key IS '目标key';

COMMENT ON COLUMN target_remark.target_type IS '目标类型';

COMMENT ON COLUMN target_remark.remark IS '备注';

-- 任务分析表
DROP TABLE IF EXISTS task_analysis CASCADE;

CREATE TABLE task_analysis (
    id SERIAL PRIMARY KEY,
    created_by INTEGER DEFAULT 1,
    task_id INTEGER,
    task_name TEXT NOT NULL,
    task_remark TEXT,
    task_state VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type INTEGER,
    task_status INTEGER DEFAULT 1
);

COMMENT ON TABLE task_analysis IS '任务分析表';

COMMENT ON COLUMN task_analysis.created_by IS '创建者ID';

COMMENT ON COLUMN task_analysis.task_id IS '任务ID';

COMMENT ON COLUMN task_analysis.task_name IS '任务名称';

-- 任务批次表
DROP TABLE IF EXISTS task_batch CASCADE;

CREATE TABLE task_batch (
    batch_id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    batch_remark TEXT,
    fullflow_state VARCHAR(3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    batch_status INTEGER DEFAULT 1
);

COMMENT ON TABLE task_batch IS '任务批次表';

COMMENT ON COLUMN task_batch.batch_id IS '自增批次ID';

COMMENT ON COLUMN task_batch.task_id IS '任务ID';

COMMENT ON COLUMN task_batch.batch_remark IS '批次描述';

COMMENT ON COLUMN task_batch.fullflow_state IS '全流量留存，ON启用，OFF停用';

-- 离线任务批次文件表
DROP TABLE IF EXISTS offline_task_batch_file CASCADE;

CREATE TABLE offline_task_batch_file (
    id SERIAL PRIMARY KEY,
    task_id BIGINT,
    batch_id BIGINT,
    batch_type INTEGER DEFAULT 0,
    file_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE offline_task_batch_file IS '离线任务批次文件表';

COMMENT ON COLUMN offline_task_batch_file.task_id IS '任务ID';

COMMENT ON COLUMN offline_task_batch_file.batch_id IS '批次ID';

COMMENT ON COLUMN offline_task_batch_file.batch_type IS '批次类型（1-服务器数据；2-数据上传；）';

-- 任务内部IP配置表
DROP TABLE IF EXISTS task_internal_ip CASCADE;

CREATE TABLE task_internal_ip (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    netmask VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE task_internal_ip IS '任务内部IP配置表';

COMMENT ON COLUMN task_internal_ip.task_id IS '任务ID';

COMMENT ON COLUMN task_internal_ip.ip IS 'ip 网段信息';

COMMENT ON COLUMN task_internal_ip.netmask IS '网段循序';

-- 系统字典表
DROP TABLE IF EXISTS system_dictionary CASCADE;

CREATE TABLE system_dictionary (
    id SERIAL PRIMARY KEY,
    valset_id VARCHAR(255),
    val_id VARCHAR(255),
    value VARCHAR(255)
);

COMMENT ON TABLE system_dictionary IS '系统字典表';

-- 流量白名单表
DROP TABLE IF EXISTS traffic_whitelist CASCADE;

CREATE TABLE traffic_whitelist (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(50) NOT NULL DEFAULT '',
    port INTEGER NOT NULL DEFAULT -1,
    app_id INTEGER NOT NULL DEFAULT -1,
    rule_id INTEGER NOT NULL DEFAULT -1,
    rule_level INTEGER NOT NULL DEFAULT -1,
    rule_name TEXT NOT NULL DEFAULT '',
    rule_desc TEXT NOT NULL DEFAULT '',
    rule_state VARCHAR(255) NOT NULL DEFAULT '生效',
    rule_size BIGINT NOT NULL DEFAULT 0,
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_size_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    capture_mode INTEGER NOT NULL DEFAULT 0,
    rule_json JSON NOT NULL DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rule_hash VARCHAR(255) NOT NULL DEFAULT '',
    cyber_kill_chain cyber_kill_chain_enum NOT NULL DEFAULT 'OTHER',
    task_id INTEGER NOT NULL DEFAULT 0
);

COMMENT ON TABLE traffic_whitelist IS '流量白名单表';

COMMENT ON COLUMN traffic_whitelist.server_ip IS '服务器IP';

COMMENT ON COLUMN traffic_whitelist.port IS '端口';

COMMENT ON COLUMN traffic_whitelist.app_id IS '应用id';

COMMENT ON COLUMN traffic_whitelist.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';

-- 流量白名单日志表
DROP TABLE IF EXISTS traffic_whitelist_log CASCADE;

CREATE TABLE traffic_whitelist_log (
    id SERIAL PRIMARY KEY,
    last_filter VARCHAR(50) NOT NULL,
    last_total VARCHAR(50) NOT NULL,
    white_list_id INTEGER NOT NULL
);

COMMENT ON TABLE traffic_whitelist_log IS '流量白名单日志表';

COMMENT ON COLUMN traffic_whitelist_log.last_filter IS '上次过滤日志数量';

COMMENT ON COLUMN traffic_whitelist_log.last_total IS '上次全部日志数量';

COMMENT ON COLUMN traffic_whitelist_log.white_list_id IS '外键 white_list 的主键';

-- 流量白名单状态表
DROP TABLE IF EXISTS traffic_whitelist_state CASCADE;

CREATE TABLE traffic_whitelist_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    UNIQUE (task_id)
);

-- 硬盘模式配置表
DROP TABLE IF EXISTS disk_mode_config CASCADE;

CREATE TABLE disk_mode_config ( id SERIAL PRIMARY KEY, field INTEGER );

COMMENT ON TABLE disk_mode_config IS '硬盘模式配置表';

COMMENT ON COLUMN disk_mode_config.id IS '唯一识别ID';

COMMENT ON COLUMN disk_mode_config.field IS '硬盘模式（读盘模式&换盘模式）';

-- 统一标签表
CREATE TABLE labels (
    -- 基础标识字段
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,                    -- 标签名称（英文标识，如 "malicious_ip"）
    display_name VARCHAR(255) NOT NULL,            -- 显示名称（用户友好名称，如 "恶意IP"）
    description TEXT,                              -- 详细描述
    remark TEXT,                                   -- 备注信息

-- 分类字段
target_type label_target_type_enum NOT NULL, -- 标签目标类型
category label_category_enum, -- 标签类别（威胁、合法性等）
source label_source_enum NOT NULL DEFAULT 'SYSTEM', -- 标签来源

-- 评分字段（0-100）
threat_level INTEGER DEFAULT 0 CHECK (
    threat_level >= 0
    AND threat_level <= 100
),
trust_level INTEGER DEFAULT 0 CHECK (
    trust_level >= 0
    AND trust_level <= 100
),
default_threat_level INTEGER DEFAULT 0 CHECK (
    default_threat_level >= 0
    AND default_threat_level <= 100
),
default_trust_level INTEGER DEFAULT 0 CHECK (
    default_trust_level >= 0
    AND default_trust_level <= 100
),

-- 业务字段
cyber_kill_chain cyber_kill_chain_enum, -- Cyber Kill Chain阶段
color VARCHAR(7) DEFAULT '#666666', -- 标签颜色（十六进制，用于UI显示）
sort_order INTEGER DEFAULT 0, -- 排序顺序

-- 状态字段
is_active BOOLEAN NOT NULL DEFAULT true, -- 是否激活
version INTEGER NOT NULL DEFAULT 1, -- 版本号（乐观锁）

-- 审计字段
created_by INTEGER, -- 创建者用户ID
created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

-- 约束
CONSTRAINT uk_labels_name_target_type UNIQUE (name, target_type), -- 同类型下标签名称唯一
    CONSTRAINT ck_labels_threat_trust CHECK (threat_level + trust_level <= 100) -- 威胁+信任不超过100
);

-- 创建索引
CREATE INDEX idx_labels_target_type ON labels (target_type);

CREATE INDEX idx_labels_category ON labels (category);

CREATE INDEX idx_labels_is_active ON labels (is_active);

CREATE INDEX idx_labels_created_at ON labels (created_at);

CREATE INDEX idx_labels_target_category ON labels (target_type, category);

CREATE INDEX idx_labels_source ON labels (source);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_labels_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_labels_updated_at
    BEFORE UPDATE ON labels
    FOR EACH ROW
    EXECUTE FUNCTION update_labels_updated_at();

-- 表注释
COMMENT ON TABLE labels IS '统一标签表，支持多种目标类型的标签管理';

COMMENT ON COLUMN labels.id IS '标签唯一标识';

COMMENT ON COLUMN labels.name IS '标签名称（英文标识）';

COMMENT ON COLUMN labels.display_name IS '显示名称（用户友好名称）';

COMMENT ON COLUMN labels.description IS '标签详细描述';

COMMENT ON COLUMN labels.remark IS '备注信息';

COMMENT ON COLUMN labels.target_type IS '标签目标类型';

COMMENT ON COLUMN labels.category IS '标签类别';

COMMENT ON COLUMN labels.source IS '标签来源：SYSTEM-系统内置, USER-用户自定义';

COMMENT ON COLUMN labels.threat_level IS '威胁等级（0-100）';

COMMENT ON COLUMN labels.trust_level IS '信任等级（0-100）';

COMMENT ON COLUMN labels.default_threat_level IS '默认威胁等级（0-100）';

COMMENT ON COLUMN labels.default_trust_level IS '默认信任等级（0-100）';

COMMENT ON COLUMN labels.attack_stage IS '攻击阶段';

COMMENT ON COLUMN labels.color IS '标签颜色（十六进制）';

COMMENT ON COLUMN labels.sort_order IS '排序顺序';

COMMENT ON COLUMN labels.is_active IS '是否激活';

COMMENT ON COLUMN labels.version IS '版本号（乐观锁）';

COMMENT ON COLUMN labels.created_by IS '创建者用户ID';

COMMENT ON COLUMN labels.created_at IS '创建时间';

COMMENT ON COLUMN labels.updated_at IS '更新时间';

COMMENT ON TABLE application_labels IS '应用标签表';

COMMENT ON COLUMN application_labels.source IS '标签来源：SYSTEM-系统内置, USER-用户自定义';

COMMENT ON TABLE domain_labels IS '域名标签表';

COMMENT ON COLUMN domain_labels.source IS '标签来源：SYSTEM-系统内置, USER-用户自定义';

-- 告警白名单表
DROP TABLE IF EXISTS alarm_whitelist CASCADE;

CREATE TABLE alarm_whitelist (
    id SERIAL PRIMARY KEY,
    victim VARCHAR(256),
    attacker VARCHAR(256),
    label VARCHAR(256)
);

COMMENT ON TABLE alarm_whitelist IS '告警白名单表';

COMMENT ON COLUMN alarm_whitelist.victim IS '受害者IP';

COMMENT ON COLUMN alarm_whitelist.attacker IS '攻击者IP';

COMMENT ON COLUMN alarm_whitelist.label IS '告警相关标签';

-- AI检测模型开关表
DROP TABLE IF EXISTS ai_detection_model_switch CASCADE;

CREATE TABLE ai_detection_model_switch (
    id SERIAL PRIMARY KEY,
    model_name VARCHAR(256),
    switch INTEGER
);

COMMENT ON TABLE ai_detection_model_switch IS 'AI检测模型开关表';

COMMENT ON COLUMN ai_detection_model_switch.model_name IS '模型名称';

COMMENT ON COLUMN ai_detection_model_switch.switch IS '模型开关';

-- 告警输出配置表
DROP TABLE IF EXISTS alarm_output_config CASCADE;

CREATE TABLE alarm_output_config (
    id SERIAL PRIMARY KEY,
    tool VARCHAR(256),
    status INTEGER,
    ip VARCHAR(256),
    port VARCHAR(256)
);

COMMENT ON TABLE alarm_output_config IS '告警输出配置表';

COMMENT ON COLUMN alarm_output_config.tool IS '告警外发组件';

COMMENT ON COLUMN alarm_output_config.status IS '组件启用状态';

COMMENT ON COLUMN alarm_output_config.ip IS '组件IP';

COMMENT ON COLUMN alarm_output_config.port IS '组件端口';

-- 证书检测模型映射表
DROP TABLE IF EXISTS cert_label_model_mapping CASCADE;

CREATE TABLE cert_label_model_mapping (
    id BIGSERIAL PRIMARY KEY,
    label_id INTEGER REFERENCES labels (id),
    model_id BIGINT NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cert_label_model_mapping IS '证书检测模型映射表';

-- 告警类型定义表
DROP TABLE IF EXISTS alarm_types CASCADE;

CREATE TABLE alarm_types (
    id BIGSERIAL PRIMARY KEY,
    knowledge_id BIGINT UNIQUE NOT NULL,
    alarm_name VARCHAR(255) NOT NULL,
    alarm_principle TEXT,
    cyber_kill_chain cyber_kill_chain_enum,
    threat_level threat_level_enum,
    include_labels INTEGER [],
    exclude_labels INTEGER [],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_types IS '告警类型定义表';

-- 检测器配置表
DROP TABLE IF EXISTS detector_config CASCADE;

CREATE TABLE detector_config (
    id BIGSERIAL PRIMARY KEY,
    detector_name VARCHAR(255) UNIQUE NOT NULL,
    detector_type detector_type_enum NOT NULL,
    description TEXT,
    config_json JSONB NOT NULL,
    thresholds JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 50,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version VARCHAR(20) DEFAULT '1.0'
);

COMMENT ON TABLE detector_config IS '检测器配置表';

-- 协议表（系统元数据）
-- 网络协议表
DROP TABLE IF EXISTS network_protocols CASCADE;

CREATE TABLE network_protocols (
    id INTEGER PRIMARY KEY,
    protocol_name TEXT NOT NULL,
    display_name TEXT,
    category TEXT,
    description TEXT,
    protocol_type INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_protocols IS '网络协议表（系统元数据）';

COMMENT ON COLUMN network_protocols.id IS '协议ID';

COMMENT ON COLUMN network_protocols.protocol_name IS '协议名称';

COMMENT ON COLUMN network_protocols.display_name IS '显示名称';

COMMENT ON COLUMN network_protocols.category IS '协议分类';

COMMENT ON COLUMN network_protocols.description IS '协议描述';

COMMENT ON COLUMN network_protocols.protocol_type IS '协议类型 1 为连接 2 为单包 3 tcp/udp负载';

-- 互联网协议表
DROP TABLE IF EXISTS internet_protocols CASCADE;

CREATE TABLE internet_protocols (
    protocol_number INTEGER PRIMARY KEY,
    keyword VARCHAR(255) NOT NULL,
    protocol_description VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internet_protocols IS '互联网协议表（IANA分配的IP协议号）';

COMMENT ON COLUMN internet_protocols.protocol_number IS '协议号（0-255）';

COMMENT ON COLUMN internet_protocols.keyword IS '协议关键字';

COMMENT ON COLUMN internet_protocols.protocol_description IS '协议描述';

-- 用户查询历史表
DROP TABLE IF EXISTS user_query_history CASCADE;

CREATE TABLE user_query_history (
    id SERIAL PRIMARY KEY,
    created_by INTEGER,
    user_name VARCHAR(128) NOT NULL,
    condition_text TEXT,
    query_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE user_query_history IS '用户查询历史表';

COMMENT ON COLUMN user_query_history.created_by IS '创建者用户ID';

COMMENT ON COLUMN user_query_history.user_name IS '查询用户名称';

COMMENT ON COLUMN user_query_history.condition_text IS '查询条件';

-- 用户查询模板表
DROP TABLE IF EXISTS user_query_template CASCADE;

CREATE TABLE user_query_template (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    user_name VARCHAR(32) NOT NULL,
    template_text TEXT,
    template_name VARCHAR(128),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE user_query_template IS '用户查询模板表';

-- 用户显示偏好配置表 (通用配置表，支持多模块)
DROP TABLE IF EXISTS user_display_preferences CASCADE;

CREATE TABLE user_display_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    module VARCHAR(50) NOT NULL,        -- 模块名：'certificate', 'session', 'alert', 'traffic' 等
    config_type VARCHAR(50) NOT NULL,   -- 配置类型：'table_view', 'chart_view', 'filter_view' 等
    config_json JSON NOT NULL,          -- 配置内容
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,                 -- 创建者用户ID
    updated_by INTEGER,                 -- 更新者用户ID
    UNIQUE(user_id, module, config_type)
);

COMMENT ON TABLE user_display_preferences IS '用户显示偏好配置表 - 存储用户在各模块中的界面显示配置';
COMMENT ON COLUMN user_display_preferences.user_id IS '用户ID';
COMMENT ON COLUMN user_display_preferences.module IS '模块名称，如：certificate, session, alert';
COMMENT ON COLUMN user_display_preferences.config_type IS '配置类型，如：table_view, chart_view';
COMMENT ON COLUMN user_display_preferences.config_json IS '配置内容JSON，包含列配置、排序、分页等';
COMMENT ON COLUMN user_display_preferences.created_by IS '配置创建者用户ID';
COMMENT ON COLUMN user_display_preferences.updated_by IS '配置更新者用户ID';

-- 创建索引以支持高效查询
CREATE INDEX idx_user_display_preferences_user_id ON user_display_preferences (user_id);
CREATE INDEX idx_user_display_preferences_module ON user_display_preferences (module);
CREATE INDEX idx_user_display_preferences_user_module ON user_display_preferences (user_id, module);
CREATE INDEX idx_user_display_preferences_created_at ON user_display_preferences (created_at);

-- 创建更新时间触发器
CREATE TRIGGER update_user_display_preferences_updated_at
    BEFORE UPDATE ON user_display_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 迁移现有的 certificate_log_template 数据到新表
-- 注意：这个迁移脚本假设旧表存在，如果不存在会被忽略
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'certificate_log_template') THEN
        INSERT INTO user_display_preferences (user_id, module, config_type, config_json, created_at, updated_at, created_by)
        SELECT
            created_by as user_id,
            'certificate' as module,
            'table_view' as config_type,
            template_json as config_json,
            created_at,
            updated_at,
            created_by
        FROM certificate_log_template
        WHERE created_by IS NOT NULL AND template_json IS NOT NULL;

        -- 删除旧表
        DROP TABLE IF EXISTS certificate_log_template CASCADE;
    END IF;
END $$;

-- 告警扩展目标表
DROP TABLE IF EXISTS alarm_extended_target CASCADE;

CREATE TABLE alarm_extended_target (
    id BIGSERIAL PRIMARY KEY,
    alarm_id INTEGER,
    batch_id INTEGER,
    label_id INTEGER,
    target_ip VARCHAR(256),
    target_port INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_extended_target IS '告警扩展目标表';

COMMENT ON COLUMN alarm_extended_target.alarm_id IS '告警id';

COMMENT ON COLUMN alarm_extended_target.batch_id IS '批次id';

COMMENT ON COLUMN alarm_extended_target.label_id IS '标签id';

-- 批次离线线程表
DROP TABLE IF EXISTS batch_offline_thread CASCADE;

CREATE TABLE batch_offline_thread (
    id SERIAL PRIMARY KEY,
    task_id BIGINT,
    batch_id BIGINT,
    service_id INTEGER DEFAULT 0,
    thread_status INTEGER DEFAULT 1
);

COMMENT ON TABLE batch_offline_thread IS '批次离线线程表';

COMMENT ON COLUMN batch_offline_thread.task_id IS '任务ID';

COMMENT ON COLUMN batch_offline_thread.batch_id IS '批次ID';

COMMENT ON COLUMN batch_offline_thread.service_id IS '服务ID';

-- 告警订阅相关表结构

-- 1. 订阅规则表
DROP TABLE IF EXISTS alarm_subscription CASCADE;

CREATE TABLE alarm_subscription (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL,
    subscription_name VARCHAR(100) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    priority_level INT DEFAULT 1,

    -- 匹配规则 (JSON格式存储)
    match_rules JSONB,

    -- 通知设置
    notification_channels JSONB,
    frequency_type VARCHAR(20) DEFAULT 'REAL_TIME',
    frequency_config JSONB,

    -- 免打扰设置
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_config JSONB,

    -- 统计信息
    trigger_count INT DEFAULT 0,
    last_triggered_time TIMESTAMP,

    -- 审计字段
    created_by VARCHAR(32),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_alarm_subscription_user_id ON alarm_subscription (user_id);
CREATE INDEX idx_alarm_subscription_enabled ON alarm_subscription (enabled);
CREATE INDEX idx_alarm_subscription_created_time ON alarm_subscription (created_time);

COMMENT ON TABLE alarm_subscription IS '告警订阅规则表';
COMMENT ON COLUMN alarm_subscription.id IS '订阅ID';
COMMENT ON COLUMN alarm_subscription.user_id IS '用户ID';
COMMENT ON COLUMN alarm_subscription.subscription_name IS '订阅名称';
COMMENT ON COLUMN alarm_subscription.description IS '订阅描述';
COMMENT ON COLUMN alarm_subscription.enabled IS '是否启用';
COMMENT ON COLUMN alarm_subscription.priority_level IS '优先级(1-5)';
COMMENT ON COLUMN alarm_subscription.match_rules IS '匹配规则列表';
COMMENT ON COLUMN alarm_subscription.notification_channels IS '通知渠道配置';
COMMENT ON COLUMN alarm_subscription.frequency_type IS '通知频率类型';
COMMENT ON COLUMN alarm_subscription.frequency_config IS '频率控制配置';
COMMENT ON COLUMN alarm_subscription.quiet_hours_enabled IS '是否启用免打扰';
COMMENT ON COLUMN alarm_subscription.quiet_hours_config IS '免打扰时间配置';
COMMENT ON COLUMN alarm_subscription.trigger_count IS '触发次数';
COMMENT ON COLUMN alarm_subscription.last_triggered_time IS '最后触发时间';
COMMENT ON COLUMN alarm_subscription.created_by IS '创建人';
COMMENT ON COLUMN alarm_subscription.created_time IS '创建时间';
COMMENT ON COLUMN alarm_subscription.updated_by IS '更新人';
COMMENT ON COLUMN alarm_subscription.updated_time IS '更新时间';

-- 2. 通知模板表
DROP TABLE IF EXISTS notification_template CASCADE;

CREATE TABLE notification_template (
    id VARCHAR(32) PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,

    -- 模板内容
    subject_template TEXT,
    content_template TEXT NOT NULL,
    template_variables JSONB,

    -- 审计字段
    created_by VARCHAR(32),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_notification_template_type ON notification_template (template_type);
CREATE INDEX idx_notification_template_default ON notification_template (is_default);

COMMENT ON TABLE notification_template IS '通知模板表';
COMMENT ON COLUMN notification_template.id IS '模板ID';
COMMENT ON COLUMN notification_template.template_name IS '模板名称';
COMMENT ON COLUMN notification_template.template_type IS '模板类型(EMAIL/KAFKA)';
COMMENT ON COLUMN notification_template.is_default IS '是否为默认模板';
COMMENT ON COLUMN notification_template.subject_template IS '主题模板';
COMMENT ON COLUMN notification_template.content_template IS '内容模板';
COMMENT ON COLUMN notification_template.template_variables IS '模板变量说明';
COMMENT ON COLUMN notification_template.created_by IS '创建人';
COMMENT ON COLUMN notification_template.created_time IS '创建时间';
COMMENT ON COLUMN notification_template.updated_by IS '更新人';
COMMENT ON COLUMN notification_template.updated_time IS '更新时间';

-- 3. 通知发送记录表
DROP TABLE IF EXISTS notification_log CASCADE;

CREATE TABLE notification_log (
    id VARCHAR(32) PRIMARY KEY,
    subscription_id VARCHAR(32) NOT NULL,
    alarm_id VARCHAR(32) NOT NULL,
    channel_type VARCHAR(20) NOT NULL,
    recipient VARCHAR(200) NOT NULL,

    -- 发送状态
    send_status VARCHAR(20) NOT NULL,
    send_time TIMESTAMP NOT NULL,
    error_message TEXT,
    retry_count INT DEFAULT 0,

    -- 内容信息
    subject VARCHAR(500),
    content TEXT
);

CREATE INDEX idx_notification_log_subscription_id ON notification_log (subscription_id);
CREATE INDEX idx_notification_log_alarm_id ON notification_log (alarm_id);
CREATE INDEX idx_notification_log_send_time ON notification_log (send_time);
CREATE INDEX idx_notification_log_send_status ON notification_log (send_status);

COMMENT ON TABLE notification_log IS '通知发送记录表';
COMMENT ON COLUMN notification_log.id IS '记录ID';
COMMENT ON COLUMN notification_log.subscription_id IS '订阅ID';
COMMENT ON COLUMN notification_log.alarm_id IS '告警ID';
COMMENT ON COLUMN notification_log.channel_type IS '通知渠道类型';
COMMENT ON COLUMN notification_log.recipient IS '接收者';
COMMENT ON COLUMN notification_log.send_status IS '发送状态(SUCCESS/FAILED/PENDING)';
COMMENT ON COLUMN notification_log.send_time IS '发送时间';
COMMENT ON COLUMN notification_log.error_message IS '错误信息';
COMMENT ON COLUMN notification_log.retry_count IS '重试次数';
COMMENT ON COLUMN notification_log.subject IS '通知主题';
COMMENT ON COLUMN notification_log.content IS '通知内容';

-- 插入默认通知模板
INSERT INTO notification_template (id, template_name, template_type, is_default, subject_template, content_template, template_variables) VALUES
('default_email_template', '默认邮件模板', 'EMAIL', TRUE,
 '【NTA安全告警】${alarmType} - ${alarmLevel}',
 '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>安全告警通知</title>
</head>
<body>
    <h2>安全告警通知</h2>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr><td><strong>告警类型</strong></td><td>${alarmType}</td></tr>
        <tr><td><strong>告警级别</strong></td><td>${alarmLevel}</td></tr>
        <tr><td><strong>告警时间</strong></td><td>${alarmTime}</td></tr>
        <tr><td><strong>源IP</strong></td><td>${srcIp}</td></tr>
        <tr><td><strong>目标IP</strong></td><td>${dstIp}</td></tr>
        <tr><td><strong>告警描述</strong></td><td>${description}</td></tr>
    </table>
    <p>请及时处理该安全告警。</p>
</body>
</html>',
 '{"alarmType": "告警类型", "alarmLevel": "告警级别", "alarmTime": "告警时间", "srcIp": "源IP", "dstIp": "目标IP", "description": "告警描述"}'::jsonb),

('default_kafka_template', '默认Kafka模板', 'KAFKA', TRUE,
 '${alarmType}告警',
 '{
    "alarmId": "${alarmId}",
    "alarmType": "${alarmType}",
    "alarmLevel": "${alarmLevel}",
    "alarmTime": "${alarmTime}",
    "srcIp": "${srcIp}",
    "dstIp": "${dstIp}",
    "description": "${description}",
    "notificationTime": "${notificationTime}"
}',
 '{"alarmId": "告警ID", "alarmType": "告警类型", "alarmLevel": "告警级别", "alarmTime": "告警时间", "srcIp": "源IP", "dstIp": "目标IP", "description": "告警描述", "notificationTime": "通知时间"}'::jsonb);

-- 创建更新时间触发器函数
CREATE
OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $ $ BEGIN NEW.updated_at = CURRENT_TIMESTAMP;

RETURN NEW;

END;

$ $ language 'plpgsql';

-- 为系统元数据表创建更新时间触发器
CREATE TRIGGER update_certificate_labels_updated_at BEFORE
UPDATE
    ON certificate_labels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_knowledge_updated_at BEFORE
UPDATE
    ON alarm_knowledge FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_detector_config_updated_at BEFORE
UPDATE
    ON detector_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为协议表创建更新时间触发器
CREATE TRIGGER update_network_protocols_updated_at BEFORE
UPDATE
    ON network_protocols FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_internet_protocols_updated_at BEFORE
UPDATE
    ON internet_protocols FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 告警服务相关表
-- ========================================

-- 告警规则表
DROP TABLE IF EXISTS alarm_rules CASCADE;

CREATE TABLE alarm_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    condition_expression TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'MEDIUM',
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    description TEXT
);

COMMENT ON TABLE alarm_rules IS '告警规则表';

COMMENT ON COLUMN alarm_rules.rule_name IS '规则名称';

COMMENT ON COLUMN alarm_rules.rule_type IS '规则类型';

COMMENT ON COLUMN alarm_rules.condition_expression IS '条件表达式';

COMMENT ON COLUMN alarm_rules.severity IS '严重程度: LOW, MEDIUM, HIGH, CRITICAL';

-- 告警记录表（业务主存储）
DROP TABLE IF EXISTS alarm_records CASCADE;

CREATE TABLE alarm_records (
    id SERIAL PRIMARY KEY,
    rule_id INTEGER REFERENCES alarm_rules(id),
    alarm_title VARCHAR(255) NOT NULL,
    alarm_content TEXT,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'OPEN',
    source_data JSONB,
    triggered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP,
    resolved_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

-- 扩展字段，用于分析
alarm_type VARCHAR(100),
attack_stage VARCHAR(100),
threat_type VARCHAR(100),
confidence DOUBLE PRECISION,

-- 网络相关信息
src_ip INET,
src_port INTEGER,
dst_ip INET,
dst_port INTEGER,
protocol VARCHAR(50),

-- 关联信息
session_id VARCHAR(255),
    task_id VARCHAR(100),
    data_source VARCHAR(100)
);

COMMENT ON TABLE alarm_records IS '告警记录表（业务主存储）';

COMMENT ON COLUMN alarm_records.rule_id IS '关联的告警规则ID';

COMMENT ON COLUMN alarm_records.alarm_title IS '告警标题';

COMMENT ON COLUMN alarm_records.alarm_content IS '告警详细内容';

COMMENT ON COLUMN alarm_records.severity IS '严重程度: LOW, MEDIUM, HIGH, CRITICAL';

COMMENT ON COLUMN alarm_records.status IS '告警状态: OPEN, ACKNOWLEDGED, RESOLVED, FALSE_POSITIVE';

COMMENT ON COLUMN alarm_records.source_data IS '源数据信息 (JSON格式)';

COMMENT ON COLUMN alarm_records.triggered_at IS '告警触发时间';

COMMENT ON COLUMN alarm_records.resolved_at IS '告警解决时间';

COMMENT ON COLUMN alarm_records.resolved_by IS '解决人员';

-- 创建索引以支持高效查询和CDC
CREATE INDEX idx_alarm_records_rule_id ON alarm_records (rule_id);

CREATE INDEX idx_alarm_records_triggered_at ON alarm_records (triggered_at);

CREATE INDEX idx_alarm_records_status ON alarm_records (status);

CREATE INDEX idx_alarm_records_severity ON alarm_records (severity);

CREATE INDEX idx_alarm_records_updated_at ON alarm_records (updated_at);

-- 为CDC创建复制标识
ALTER TABLE alarm_records REPLICA IDENTITY FULL;

-- ========================================
-- 证书服务相关表
-- ========================================

-- 证书标签关联表
DROP TABLE IF EXISTS cert_labels CASCADE;

CREATE TABLE cert_labels (
    id SERIAL PRIMARY KEY,
    cert_hash VARCHAR(64) NOT NULL, -- 对应 Doris dim_cert 表的 der_sha1 字段
    label_id INTEGER NOT NULL REFERENCES labels (id) ON DELETE CASCADE, -- 引用统一标签表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER, -- 创建者用户ID
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER, -- 更新者用户ID
    UNIQUE (cert_hash, label_id) -- 同一证书不能重复关联同一个标签
);

COMMENT ON TABLE cert_labels IS '证书标签关联表 - 存储证书与标签的关联关系';

COMMENT ON COLUMN cert_labels.cert_hash IS '证书哈希值，对应 Doris dim_cert.der_sha1';

COMMENT ON COLUMN cert_labels.label_id IS '标签ID，引用 labels 表';

COMMENT ON COLUMN cert_labels.created_by IS '关联创建者用户ID';

COMMENT ON COLUMN cert_labels.updated_by IS '关联更新者用户ID';

-- 创建索引以支持高效查询
CREATE INDEX idx_cert_labels_cert_hash ON cert_labels (cert_hash);

CREATE INDEX idx_cert_labels_label_id ON cert_labels (label_id);

CREATE INDEX idx_cert_labels_created_at ON cert_labels (created_at);

CREATE INDEX idx_cert_labels_created_by ON cert_labels (created_by);

-- 创建更新时间触发器
CREATE TRIGGER update_cert_labels_updated_at
    BEFORE UPDATE ON cert_labels
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 会话标签关联表
DROP TABLE IF EXISTS session_labels CASCADE;
CREATE TABLE session_labels (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(64) NOT NULL,  -- 对应 Doris dwd_session_logs 表的 session_id 字段
    label_id INTEGER NOT NULL REFERENCES labels(id) ON DELETE CASCADE,  -- 引用统一标签表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,  -- 创建者用户ID
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER,  -- 更新者用户ID
    UNIQUE(session_id, label_id)  -- 同一会话不能重复关联同一个标签
);

COMMENT ON TABLE session_labels IS '会话标签关联表 - 存储会话与标签的关联关系';
COMMENT ON COLUMN session_labels.session_id IS '会话ID，对应 Doris dwd_session_logs.session_id';
COMMENT ON COLUMN session_labels.label_id IS '标签ID，引用 labels 表';
COMMENT ON COLUMN session_labels.created_by IS '关联创建者用户ID';
COMMENT ON COLUMN session_labels.updated_by IS '关联更新者用户ID';

-- 创建索引以支持高效查询
CREATE INDEX idx_session_labels_session_id ON session_labels(session_id);
CREATE INDEX idx_session_labels_label_id ON session_labels(label_id);
CREATE INDEX idx_session_labels_created_at ON session_labels(created_at);
CREATE INDEX idx_session_labels_created_by ON session_labels(created_by);

-- 创建更新时间触发器
CREATE TRIGGER update_session_labels_updated_at
    BEFORE UPDATE ON session_labels
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 元数据服务相关表
-- ========================================

-- 元数据定义表
DROP TABLE IF EXISTS metadata_definitions CASCADE;

CREATE TABLE metadata_definitions (
    id SERIAL PRIMARY KEY,
    metadata_key VARCHAR(255) UNIQUE NOT NULL,
    metadata_name VARCHAR(255) NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    description TEXT,
    default_value TEXT,
    validation_rules JSONB,
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE metadata_definitions IS '元数据定义表';

COMMENT ON COLUMN metadata_definitions.data_type IS '数据类型: STRING, INTEGER, BOOLEAN, JSON, ARRAY';

-- 元数据值表
DROP TABLE IF EXISTS metadata_values CASCADE;

CREATE TABLE metadata_values (
    id SERIAL PRIMARY KEY,
    metadata_key VARCHAR(255) NOT NULL,
    entity_type VARCHAR(100) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    metadata_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (
        metadata_key,
        entity_type,
        entity_id
    )
);

COMMENT ON TABLE metadata_values IS '元数据值表';

COMMENT ON COLUMN metadata_values.entity_type IS '实体类型';

COMMENT ON COLUMN metadata_values.entity_id IS '实体ID';

-- ========================================
-- 监控服务相关表
-- ========================================

-- 监控指标定义表
DROP TABLE IF EXISTS monitor_metrics CASCADE;

CREATE TABLE monitor_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(255) UNIQUE NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    description TEXT,
    unit VARCHAR(20),
    collection_interval INTEGER DEFAULT 60,
    retention_days INTEGER DEFAULT 30,
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE monitor_metrics IS '监控指标定义表';

COMMENT ON COLUMN monitor_metrics.metric_type IS '指标类型: COUNTER, GAUGE, HISTOGRAM';

COMMENT ON COLUMN monitor_metrics.collection_interval IS '采集间隔(秒)';

-- 监控数据表
DROP TABLE IF EXISTS monitor_data CASCADE;

CREATE TABLE monitor_data (
    id BIGSERIAL PRIMARY KEY,
    metric_name VARCHAR(255) NOT NULL,
    metric_value DOUBLE PRECISION NOT NULL,
    labels JSONB,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE monitor_data IS '监控数据表';

COMMENT ON COLUMN monitor_data.labels IS '标签信息';

-- 创建时间分区索引
CREATE INDEX idx_monitor_data_timestamp ON monitor_data (timestamp);

CREATE INDEX idx_monitor_data_metric_name ON monitor_data (metric_name);

-- ========================================
-- 规则服务相关表
-- ========================================

-- 规则定义表
DROP TABLE IF EXISTS rule_definitions CASCADE;

CREATE TABLE rule_definitions (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_content TEXT NOT NULL,
    rule_format VARCHAR(20) NOT NULL DEFAULT 'JSON',
    priority INTEGER DEFAULT 0,
    enabled BOOLEAN NOT NULL DEFAULT true,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

COMMENT ON TABLE rule_definitions IS '规则定义表';

COMMENT ON COLUMN rule_definitions.rule_format IS '规则格式: JSON, YAML, DROOLS';

COMMENT ON COLUMN rule_definitions.priority IS '优先级，数值越大优先级越高';

-- 规则执行记录表
DROP TABLE IF EXISTS rule_executions CASCADE;

CREATE TABLE rule_executions (
    id BIGSERIAL PRIMARY KEY,
    rule_id INTEGER REFERENCES rule_definitions (id),
    input_data JSONB,
    output_data JSONB,
    execution_time_ms INTEGER,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE rule_executions IS '规则执行记录表';

COMMENT ON COLUMN rule_executions.status IS '执行状态: SUCCESS, FAILED, TIMEOUT';
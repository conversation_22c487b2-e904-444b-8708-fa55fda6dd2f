-- 知识库数据库初始化脚本

-- 创建表结构

-- 威胁情报表
CREATE TABLE threat_intelligence (
    id BIGSERIAL PRIMARY KEY,
    threat_type threat_type_enum NOT NULL,
    threat_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    severity threat_level_enum NOT NULL,
    attack_vectors JSONB,
    impact_scope TEXT,
    detection_principle TEXT,
    technical_background TEXT,
    related_cves JSONB,
    iocs JSONB,
    target_indicators JSONB, -- 目标指标（IP、域名、证书hash等）
    target_type VARCHAR(100), -- 目标类型(domain/cert/ip/url等)
    tags TEXT[], -- 威胁标签数组
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    source VARCHAR(100),
    confidence_score INTEGER DEFAULT 50
);

COMMENT ON TABLE threat_intelligence IS '威胁情报表';
COMMENT ON COLUMN threat_intelligence.target_indicators IS '目标指标，包含IP、域名、证书hash等信息的JSON对象';
COMMENT ON COLUMN threat_intelligence.target_type IS '目标类型(domain/cert/ip/url等)';
COMMENT ON COLUMN threat_intelligence.tags IS '威胁标签数组';

-- 地理位置数据表
CREATE TABLE geo_ip_data (
    id BIGSERIAL PRIMARY KEY,
    ip_start INET NOT NULL,
    ip_end INET NOT NULL,
    country_code CHAR(2),
    country_name VARCHAR(100),
    region_name VARCHAR(100),
    city_name VARCHAR(100),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    asn INTEGER,
    asn_org VARCHAR(255),
    is_anonymous_proxy BOOLEAN DEFAULT FALSE,
    is_satellite_provider BOOLEAN DEFAULT FALSE,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- IP地理位置缓存表
CREATE TABLE ip_geolocation_cache (
    task_id INTEGER NOT NULL,
    ip VARCHAR(42) NOT NULL,
    net_mesh VARCHAR(42) NOT NULL,
    country VARCHAR(128) NOT NULL,
    province VARCHAR(128) NOT NULL,
    city VARCHAR(128) NOT NULL,
    isp VARCHAR(128) NOT NULL,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (ip, net_mesh)
);

COMMENT ON TABLE ip_geolocation_cache IS 'IP地理位置缓存表';
COMMENT ON COLUMN ip_geolocation_cache.ip IS 'IP地址';
COMMENT ON COLUMN ip_geolocation_cache.net_mesh IS 'IP源码';
COMMENT ON COLUMN ip_geolocation_cache.country IS '国家';
COMMENT ON COLUMN ip_geolocation_cache.province IS '省份';
COMMENT ON COLUMN ip_geolocation_cache.city IS '城市';
COMMENT ON COLUMN ip_geolocation_cache.isp IS 'ISP';

-- MAC厂商知识库
CREATE TABLE mac_vendor_knowledge (
    id BIGSERIAL PRIMARY KEY,
    registry VARCHAR(255),
    assignment VARCHAR(255),
    organization_name TEXT,
    organization_address TEXT,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE mac_vendor_knowledge IS 'MAC厂商知识库';
COMMENT ON COLUMN mac_vendor_knowledge.registry IS 'MA-L为mac的前6位，MA-M为mac的前7位，MA-S为mac的前9位';
COMMENT ON COLUMN mac_vendor_knowledge.assignment IS '匹配mac前9位，匹配mac前7位，匹配mac前6位';
COMMENT ON COLUMN mac_vendor_knowledge.organization_name IS '厂家信息';
COMMENT ON COLUMN mac_vendor_knowledge.organization_address IS '厂家地址';

-- 统一域名知识表
CREATE TABLE domain_knowledge (
    id BIGSERIAL PRIMARY KEY,
    domain VARCHAR(255) UNIQUE NOT NULL,

    -- 排名信息
    tranco_rank INTEGER,
    tranco_list_date DATE,
    alexa_rank INTEGER, -- 保留Alexa排名字段用于数据迁移

    -- WHOIS信息
    registrar_name VARCHAR(500),
    contact_email VARCHAR(255),
    whois_server VARCHAR(255),
    name_servers TEXT[],
    created_date TIMESTAMP,
    updated_date TIMESTAMP,
    expires_date TIMESTAMP,
    status TEXT[],
    registrant_name VARCHAR(255),
    registrant_organization VARCHAR(500),
    registrant_country VARCHAR(100),
    registrant_email VARCHAR(255),
    admin_contact_name VARCHAR(255),
    admin_contact_organization VARCHAR(500),
    admin_contact_email VARCHAR(255),
    whois_summary VARCHAR(500), -- WHOIS摘要信息

    -- 域名分类标签
    is_mining_domain BOOLEAN DEFAULT FALSE,
    is_video_domain BOOLEAN DEFAULT FALSE,
    is_malicious_domain BOOLEAN DEFAULT FALSE,
    is_cdn_domain BOOLEAN DEFAULT FALSE,
    is_dynamic_domain BOOLEAN DEFAULT FALSE,

    -- 威胁情报信息
    threat_category VARCHAR(100),
    threat_confidence_score INTEGER DEFAULT 0,
    threat_first_seen TIMESTAMP,
    threat_last_seen TIMESTAMP,

    -- 元数据
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

COMMENT ON TABLE domain_knowledge IS '统一域名知识表';
COMMENT ON COLUMN domain_knowledge.whois_summary IS 'WHOIS摘要信息';

-- CDN云服务商知识库
CREATE TABLE cdn_cloud_knowledge (
    id BIGSERIAL PRIMARY KEY,
    cdn VARCHAR(255) NOT NULL,
    cdn_tag VARCHAR(255),
    organization VARCHAR(255),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cdn_cloud_knowledge IS 'CDN云服务商知识库';
COMMENT ON COLUMN cdn_cloud_knowledge.cdn IS 'CDN域名或IP';
COMMENT ON COLUMN cdn_cloud_knowledge.cdn_tag IS 'CDN标签';
COMMENT ON COLUMN cdn_cloud_knowledge.organization IS '组织名称';

-- 域名排行榜知识库
CREATE TABLE domain_ranking_knowledge (
    id BIGSERIAL PRIMARY KEY,
    global_rank INTEGER NOT NULL,
    tld_rank INTEGER,
    domain VARCHAR(255) NOT NULL,
    tld VARCHAR(10),
    ref_sub_nets BIGINT,
    ref_ips BIGINT,
    idn_domain VARCHAR(255),
    idn_tld VARCHAR(255),
    prev_global_rank INTEGER,
    prev_tld_rank INTEGER,
    prev_ref_sub_nets BIGINT,
    prev_ref_ips BIGINT,
    ranking_source VARCHAR(50) DEFAULT 'trusted_1m', -- 'trusted_1m', 'top_1m', 'tranco'
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE domain_ranking_knowledge IS '域名排行榜知识库';
COMMENT ON COLUMN domain_ranking_knowledge.global_rank IS '全球排名';
COMMENT ON COLUMN domain_ranking_knowledge.tld_rank IS '顶级域名排名';
COMMENT ON COLUMN domain_ranking_knowledge.domain IS '域名';
COMMENT ON COLUMN domain_ranking_knowledge.tld IS '顶级域名';
COMMENT ON COLUMN domain_ranking_knowledge.ranking_source IS '排名来源';

-- 公共后缀列表
CREATE TABLE public_suffix_list (
    id BIGSERIAL PRIMARY KEY,
    suffix VARCHAR(255) UNIQUE NOT NULL,
    is_wildcard BOOLEAN DEFAULT FALSE,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- TLD注册信息表
CREATE TABLE tld_registry_info (
    id BIGSERIAL PRIMARY KEY,
    tld VARCHAR(100) UNIQUE NOT NULL,

    is_new_gtld BOOLEAN DEFAULT FALSE,

    -- New gTLD注册信息
    registry_operator VARCHAR(500),
    application_id VARCHAR(50),
    contract_signature_date DATE,
    delegation_date DATE,
    status VARCHAR(50) DEFAULT 'delegated',
    u_label VARCHAR(255), -- 国际化域名的Unicode标签

    -- 基本描述信息
    description TEXT,

    -- 元数据
    data_source VARCHAR(50) DEFAULT 'ICANN',
    last_updated DATE,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 指纹库表
CREATE TABLE fingerprint_library (
    id BIGSERIAL PRIMARY KEY,
    fingerprint_type fingerprint_type_enum NOT NULL,
    fingerprint_value VARCHAR(1024) NOT NULL,
    service_name VARCHAR(255),
    service_version VARCHAR(100),
    vendor VARCHAR(255),
    description TEXT,
    confidence_score INTEGER DEFAULT 50,
    is_malicious BOOLEAN DEFAULT FALSE,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 流量指纹知识库（兼容NTA 2.0）
CREATE TABLE traffic_fingerprint_knowledge (
    id BIGSERIAL PRIMARY KEY,
    finger_es VARCHAR(100),
    finger_content TEXT,
    ja3_hash VARCHAR(100),
    finger_name VARCHAR(100),
    finger_type VARCHAR(100),
    es_type VARCHAR(200), -- ES中的源目的指纹
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE traffic_fingerprint_knowledge IS '流量指纹知识库';
COMMENT ON COLUMN traffic_fingerprint_knowledge.finger_es IS 'Finger_ES_ID';
COMMENT ON COLUMN traffic_fingerprint_knowledge.finger_content IS '流量中提取字段';
COMMENT ON COLUMN traffic_fingerprint_knowledge.ja3_hash IS '指纹JA3Hash';
COMMENT ON COLUMN traffic_fingerprint_knowledge.finger_name IS '指纹名称';
COMMENT ON COLUMN traffic_fingerprint_knowledge.finger_type IS '指纹类型';
COMMENT ON COLUMN traffic_fingerprint_knowledge.es_type IS 'ES中的源目的指纹';

-- 创建索引
CREATE INDEX idx_threat_intelligence_type ON threat_intelligence(threat_type);
CREATE INDEX idx_threat_intelligence_severity ON threat_intelligence(severity);
CREATE INDEX idx_threat_intelligence_active ON threat_intelligence(is_active);

CREATE INDEX idx_geo_ip_range ON geo_ip_data USING GIST (inet_range(ip_start, ip_end));

CREATE INDEX idx_domain_knowledge_domain ON domain_knowledge(domain);
CREATE INDEX idx_domain_knowledge_tranco_rank ON domain_knowledge(tranco_rank);
CREATE INDEX idx_domain_knowledge_mining ON domain_knowledge(is_mining_domain);
CREATE INDEX idx_domain_knowledge_video ON domain_knowledge(is_video_domain);
CREATE INDEX idx_domain_knowledge_malicious ON domain_knowledge(is_malicious_domain);
CREATE INDEX idx_domain_knowledge_cdn ON domain_knowledge(is_cdn_domain);
CREATE INDEX idx_domain_knowledge_dynamic ON domain_knowledge(is_dynamic_domain);
CREATE INDEX idx_domain_knowledge_active ON domain_knowledge(is_active);

CREATE INDEX idx_public_suffix_suffix ON public_suffix_list(suffix);

CREATE INDEX idx_tld_registry_tld ON tld_registry_info(tld);
CREATE INDEX idx_tld_registry_new_gtld ON tld_registry_info(is_new_gtld);
CREATE INDEX idx_tld_registry_status ON tld_registry_info(status);
CREATE INDEX idx_tld_registry_operator ON tld_registry_info(registry_operator);

CREATE INDEX idx_fingerprint_type_value ON fingerprint_library(fingerprint_type, fingerprint_value);

-- 注意：应用协议知识库、单协议知识库、IP协议知识库的索引已迁移到 02-nta.sql

CREATE INDEX idx_ip_geolocation_cache_ip ON ip_geolocation_cache(ip);
CREATE INDEX idx_ip_geolocation_cache_task_id ON ip_geolocation_cache(task_id);

CREATE INDEX idx_mac_vendor_knowledge_assignment ON mac_vendor_knowledge(assignment);
CREATE INDEX idx_mac_vendor_knowledge_organization_name ON mac_vendor_knowledge(organization_name);

CREATE INDEX idx_cdn_cloud_knowledge_cdn ON cdn_cloud_knowledge(cdn);
CREATE INDEX idx_cdn_cloud_knowledge_cdn_tag ON cdn_cloud_knowledge(cdn_tag);

CREATE INDEX idx_domain_ranking_knowledge_global_rank ON domain_ranking_knowledge(global_rank);
CREATE INDEX idx_domain_ranking_knowledge_domain ON domain_ranking_knowledge(domain);
CREATE INDEX idx_domain_ranking_knowledge_ranking_source ON domain_ranking_knowledge(ranking_source);

CREATE INDEX idx_traffic_fingerprint_knowledge_finger_es ON traffic_fingerprint_knowledge(finger_es);
CREATE INDEX idx_traffic_fingerprint_knowledge_ja3_hash ON traffic_fingerprint_knowledge(ja3_hash);
CREATE INDEX idx_traffic_fingerprint_knowledge_finger_type ON traffic_fingerprint_knowledge(finger_type);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_threat_intelligence_updated_at
    BEFORE UPDATE ON threat_intelligence
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fingerprint_library_updated_at
    BEFORE UPDATE ON fingerprint_library
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_domain_knowledge_updated_at
    BEFORE UPDATE ON domain_knowledge
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tld_registry_info_updated_at
    BEFORE UPDATE ON tld_registry_info
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 注意：应用协议知识库、单协议知识库、IP协议知识库的更新时间触发器已迁移到 02-nta.sql

CREATE TRIGGER update_ip_geolocation_cache_updated_at
    BEFORE UPDATE ON ip_geolocation_cache
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mac_vendor_knowledge_updated_time
    BEFORE UPDATE ON mac_vendor_knowledge
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

CREATE TRIGGER update_cdn_cloud_knowledge_updated_time
    BEFORE UPDATE ON cdn_cloud_knowledge
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

CREATE TRIGGER update_domain_ranking_knowledge_updated_time
    BEFORE UPDATE ON domain_ranking_knowledge
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

CREATE TRIGGER update_traffic_fingerprint_knowledge_updated_time
    BEFORE UPDATE ON traffic_fingerprint_knowledge
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

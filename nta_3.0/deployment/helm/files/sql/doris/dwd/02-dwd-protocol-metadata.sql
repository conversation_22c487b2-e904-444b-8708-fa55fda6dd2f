-- 使用数据库
USE nta;

-- ========== DWD层协议查询辅助视图和示例 ==========
--
-- 说明：本文件包含基于新的单表+JSON数组架构的查询视图和示例
-- 主表：dwd_session_logs (在01-dwd-session-logs.sql中定义)
--
-- 设计优势：
-- 1. 单表存储，避免复杂JOIN操作
-- 2. JSON数组支持一对多关系
-- 3. 列式存储对NULL值友好，减少存储浪费
-- 4. 查询灵活，支持JSON函数进行复杂过滤

-- ========== 协议明细视图 ==========

-- HTTP协议明细视图
CREATE VIEW dwd_http_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(http_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(http_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(http_detail, '$.method') as http_method,
  JSON_EXTRACT(http_detail, '$.url') as http_url,
  JSON_EXTRACT(http_detail, '$.host') as http_host,
  JSON_EXTRACT(http_detail, '$.status_code') as http_status_code,
  JSON_EXTRACT(http_detail, '$.user_agent') as http_user_agent,
  JSON_EXTRACT(http_detail, '$.request_headers') as http_request_headers,
  JSON_EXTRACT(http_detail, '$.response_headers') as http_response_headers,
  JSON_EXTRACT(http_detail, '$.request_size') as http_request_size,
  JSON_EXTRACT(http_detail, '$.response_size') as http_response_size
FROM dwd_session_logs
LATERAL VIEW EXPLODE(http_protocols) tmp AS http_detail
WHERE http_protocols IS NOT NULL AND SIZE(http_protocols) > 0;

-- SSL协议明细视图
CREATE VIEW dwd_ssl_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(ssl_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(ssl_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(ssl_detail, '$.version') as ssl_version,
  JSON_EXTRACT(ssl_detail, '$.cipher_suite') as ssl_cipher_suite,
  JSON_EXTRACT(ssl_detail, '$.server_name') as ssl_server_name,
  JSON_EXTRACT(ssl_detail, '$.cert_subject') as ssl_cert_subject,
  JSON_EXTRACT(ssl_detail, '$.cert_issuer') as ssl_cert_issuer,
  JSON_EXTRACT(ssl_detail, '$.cert_serial') as ssl_cert_serial,
  JSON_EXTRACT(ssl_detail, '$.cert_not_before') as ssl_cert_not_before,
  JSON_EXTRACT(ssl_detail, '$.cert_not_after') as ssl_cert_not_after,
  JSON_EXTRACT(ssl_detail, '$.cert_sha1') as ssl_cert_sha1,
  JSON_EXTRACT(ssl_detail, '$.cert_sha256') as ssl_cert_sha256,
  JSON_EXTRACT(ssl_detail, '$.ja3') as ssl_ja3,
  JSON_EXTRACT(ssl_detail, '$.ja3s') as ssl_ja3s,
  JSON_EXTRACT(ssl_detail, '$.is_self_signed') as ssl_is_self_signed
FROM dwd_session_logs
LATERAL VIEW EXPLODE(ssl_protocols) tmp AS ssl_detail
WHERE ssl_protocols IS NOT NULL AND SIZE(ssl_protocols) > 0;

-- DNS协议明细视图
CREATE VIEW dwd_dns_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(dns_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(dns_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(dns_detail, '$.transaction_id') as dns_transaction_id,
  JSON_EXTRACT(dns_detail, '$.query_name') as dns_query_name,
  JSON_EXTRACT(dns_detail, '$.query_type') as dns_query_type,
  JSON_EXTRACT(dns_detail, '$.query_class') as dns_query_class,
  JSON_EXTRACT(dns_detail, '$.response_code') as dns_response_code,
  JSON_EXTRACT(dns_detail, '$.answer_count') as dns_answer_count,
  JSON_EXTRACT(dns_detail, '$.resolved_addresses') as dns_resolved_addresses,
  JSON_EXTRACT(dns_detail, '$.ttl') as dns_ttl,
  JSON_EXTRACT(dns_detail, '$.is_recursive') as dns_is_recursive
FROM dwd_session_logs
LATERAL VIEW EXPLODE(dns_protocols) tmp AS dns_detail
WHERE dns_protocols IS NOT NULL AND SIZE(dns_protocols) > 0;

-- SSH协议明细视图
CREATE VIEW dwd_ssh_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(ssh_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(ssh_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(ssh_detail, '$.protocol_version') as ssh_protocol_version,
  JSON_EXTRACT(ssh_detail, '$.client_software') as ssh_client_software,
  JSON_EXTRACT(ssh_detail, '$.server_software') as ssh_server_software,
  JSON_EXTRACT(ssh_detail, '$.kex_algorithm') as ssh_kex_algorithm,
  JSON_EXTRACT(ssh_detail, '$.host_key_algorithm') as ssh_host_key_algorithm,
  JSON_EXTRACT(ssh_detail, '$.encryption_algorithm') as ssh_encryption_algorithm,
  JSON_EXTRACT(ssh_detail, '$.mac_algorithm') as ssh_mac_algorithm,
  JSON_EXTRACT(ssh_detail, '$.compression_algorithm') as ssh_compression_algorithm,
  JSON_EXTRACT(ssh_detail, '$.hassh_client') as ssh_hassh_client,
  JSON_EXTRACT(ssh_detail, '$.hassh_server') as ssh_hassh_server,
  JSON_EXTRACT(ssh_detail, '$.auth_methods') as ssh_auth_methods,
  JSON_EXTRACT(ssh_detail, '$.auth_success') as ssh_auth_success
FROM dwd_session_logs
LATERAL VIEW EXPLODE(ssh_protocols) tmp AS ssh_detail
WHERE ssh_protocols IS NOT NULL AND SIZE(ssh_protocols) > 0;

-- VNC协议明细视图
CREATE VIEW dwd_vnc_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(vnc_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(vnc_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(vnc_detail, '$.fb_width') as vnc_fb_width,
  JSON_EXTRACT(vnc_detail, '$.fb_height') as vnc_fb_height,
  JSON_EXTRACT(vnc_detail, '$.desktop_name') as vnc_desktop_name,
  JSON_EXTRACT(vnc_detail, '$.server_protocol_ver') as vnc_server_protocol_ver,
  JSON_EXTRACT(vnc_detail, '$.client_protocol_ver') as vnc_client_protocol_ver,
  JSON_EXTRACT(vnc_detail, '$.auth_result') as vnc_auth_result,
  JSON_EXTRACT(vnc_detail, '$.encoding_types') as vnc_encoding_types,
  JSON_EXTRACT(vnc_detail, '$.bits_per_pixel') as vnc_bits_per_pixel,
  JSON_EXTRACT(vnc_detail, '$.depth') as vnc_depth,
  JSON_EXTRACT(vnc_detail, '$.big_endian') as vnc_big_endian,
  JSON_EXTRACT(vnc_detail, '$.true_color') as vnc_true_color
FROM dwd_session_logs
LATERAL VIEW EXPLODE(vnc_protocols) tmp AS vnc_detail
WHERE vnc_protocols IS NOT NULL AND SIZE(vnc_protocols) > 0;

-- RDP协议明细视图
CREATE VIEW dwd_rdp_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(rdp_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(rdp_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(rdp_detail, '$.desktop_width') as rdp_desktop_width,
  JSON_EXTRACT(rdp_detail, '$.desktop_height') as rdp_desktop_height,
  JSON_EXTRACT(rdp_detail, '$.client_name') as rdp_client_name,
  JSON_EXTRACT(rdp_detail, '$.encryption_method') as rdp_encryption_method,
  JSON_EXTRACT(rdp_detail, '$.color_depth') as rdp_color_depth,
  JSON_EXTRACT(rdp_detail, '$.keyboard_layout') as rdp_keyboard_layout,
  JSON_EXTRACT(rdp_detail, '$.client_build') as rdp_client_build
FROM dwd_session_logs
LATERAL VIEW EXPLODE(rdp_protocols) tmp AS rdp_detail
WHERE rdp_protocols IS NOT NULL AND SIZE(rdp_protocols) > 0;

-- S7协议明细视图
CREATE VIEW dwd_s7_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(s7_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(s7_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(s7_detail, '$.tpkt_version') as s7_tpkt_version,
  JSON_EXTRACT(s7_detail, '$.cotp_type') as s7_cotp_type,
  JSON_EXTRACT(s7_detail, '$.s7_type') as s7_type,
  JSON_EXTRACT(s7_detail, '$.s7_function') as s7_function,
  JSON_EXTRACT(s7_detail, '$.system_type') as s7_system_type,
  JSON_EXTRACT(s7_detail, '$.system_group_function') as s7_system_group_function,
  JSON_EXTRACT(s7_detail, '$.system_sub_function') as s7_system_sub_function,
  JSON_EXTRACT(s7_detail, '$.dst_ref') as s7_dst_ref,
  JSON_EXTRACT(s7_detail, '$.src_ref') as s7_src_ref,
  JSON_EXTRACT(s7_detail, '$.pdu_size') as s7_pdu_size,
  JSON_EXTRACT(s7_detail, '$.src_connect_type') as s7_src_connect_type,
  JSON_EXTRACT(s7_detail, '$.src_rack') as s7_src_rack,
  JSON_EXTRACT(s7_detail, '$.src_slot') as s7_src_slot,
  JSON_EXTRACT(s7_detail, '$.dst_connect_type') as s7_dst_connect_type,
  JSON_EXTRACT(s7_detail, '$.dst_rack') as s7_dst_rack,
  JSON_EXTRACT(s7_detail, '$.dst_slot') as s7_dst_slot,
  JSON_EXTRACT(s7_detail, '$.packet_c2s') as s7_packet_c2s
FROM dwd_session_logs
LATERAL VIEW EXPLODE(s7_protocols) tmp AS s7_detail
WHERE s7_protocols IS NOT NULL AND SIZE(s7_protocols) > 0;

-- Modbus协议明细视图
CREATE VIEW dwd_modbus_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(modbus_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(modbus_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(modbus_detail, '$.trans_id') as modbus_trans_id,
  JSON_EXTRACT(modbus_detail, '$.protocol_id') as modbus_protocol_id,
  JSON_EXTRACT(modbus_detail, '$.slave_id') as modbus_slave_id,
  JSON_EXTRACT(modbus_detail, '$.func_code') as modbus_func_code,
  JSON_EXTRACT(modbus_detail, '$.packet_c2s') as modbus_packet_c2s
FROM dwd_session_logs
LATERAL VIEW EXPLODE(modbus_protocols) tmp AS modbus_detail
WHERE modbus_protocols IS NOT NULL AND SIZE(modbus_protocols) > 0;

-- IEC104协议明细视图
CREATE VIEW dwd_iec104_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(iec104_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(iec104_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(iec104_detail, '$.trans_id') as iec104_trans_id,
  JSON_EXTRACT(iec104_detail, '$.protocol_id') as iec104_protocol_id,
  JSON_EXTRACT(iec104_detail, '$.slave_id') as iec104_slave_id,
  JSON_EXTRACT(iec104_detail, '$.func_code') as iec104_func_code
FROM dwd_session_logs
LATERAL VIEW EXPLODE(iec104_protocols) tmp AS iec104_detail
WHERE iec104_protocols IS NOT NULL AND SIZE(iec104_protocols) > 0;

-- EIP协议明细视图
CREATE VIEW dwd_eip_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(eip_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(eip_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(eip_detail, '$.trans_id') as eip_trans_id,
  JSON_EXTRACT(eip_detail, '$.protocol_id') as eip_protocol_id,
  JSON_EXTRACT(eip_detail, '$.slave_id') as eip_slave_id,
  JSON_EXTRACT(eip_detail, '$.func_code') as eip_func_code
FROM dwd_session_logs
LATERAL VIEW EXPLODE(eip_protocols) tmp AS eip_detail
WHERE eip_protocols IS NOT NULL AND SIZE(eip_protocols) > 0;

-- OPC协议明细视图
CREATE VIEW dwd_opc_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(opc_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(opc_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(opc_detail, '$.trans_id') as opc_trans_id,
  JSON_EXTRACT(opc_detail, '$.protocol_id') as opc_protocol_id,
  JSON_EXTRACT(opc_detail, '$.slave_id') as opc_slave_id,
  JSON_EXTRACT(opc_detail, '$.func_code') as opc_func_code
FROM dwd_session_logs
LATERAL VIEW EXPLODE(opc_protocols) tmp AS opc_detail
WHERE opc_protocols IS NOT NULL AND SIZE(opc_protocols) > 0;

-- ESP协议明细视图
CREATE VIEW dwd_esp_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(esp_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(esp_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(esp_detail, '$.protocol_family') as esp_protocol_family,
  JSON_EXTRACT(esp_detail, '$.communication_rate') as esp_communication_rate,
  JSON_EXTRACT(esp_detail, '$.direction') as esp_direction,
  JSON_EXTRACT(esp_detail, '$.encapsulation_mode') as esp_encapsulation_mode,
  JSON_EXTRACT(esp_detail, '$.esp_spi') as esp_spi,
  JSON_EXTRACT(esp_detail, '$.esp_seq') as esp_seq,
  JSON_EXTRACT(esp_detail, '$.esp_data_len') as esp_data_len,
  JSON_EXTRACT(esp_detail, '$.esp_data') as esp_data
FROM dwd_session_logs
LATERAL VIEW EXPLODE(esp_protocols) tmp AS esp_detail
WHERE esp_protocols IS NOT NULL AND SIZE(esp_protocols) > 0;

-- L2TP协议明细视图
CREATE VIEW dwd_l2tp_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(l2tp_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(l2tp_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(l2tp_detail, '$.protocol_family') as l2tp_protocol_family,
  JSON_EXTRACT(l2tp_detail, '$.communication_rate') as l2tp_communication_rate,
  JSON_EXTRACT(l2tp_detail, '$.direction') as l2tp_direction,
  JSON_EXTRACT(l2tp_detail, '$.protocol_version') as l2tp_protocol_version,
  JSON_EXTRACT(l2tp_detail, '$.framing_capabilities') as l2tp_framing_capabilities,
  JSON_EXTRACT(l2tp_detail, '$.bearer_capabilities') as l2tp_bearer_capabilities,
  JSON_EXTRACT(l2tp_detail, '$.server_hostname') as l2tp_server_hostname,
  JSON_EXTRACT(l2tp_detail, '$.client_hostname') as l2tp_client_hostname,
  JSON_EXTRACT(l2tp_detail, '$.server_vendorname') as l2tp_server_vendorname,
  JSON_EXTRACT(l2tp_detail, '$.client_vendorname') as l2tp_client_vendorname,
  JSON_EXTRACT(l2tp_detail, '$.calling_number') as l2tp_calling_number,
  JSON_EXTRACT(l2tp_detail, '$.proxy_authen_type') as l2tp_proxy_authen_type,
  JSON_EXTRACT(l2tp_detail, '$.proxy_authen_name') as l2tp_proxy_authen_name,
  JSON_EXTRACT(l2tp_detail, '$.is_negotiate_success') as l2tp_is_negotiate_success
FROM dwd_session_logs
LATERAL VIEW EXPLODE(l2tp_protocols) tmp AS l2tp_detail
WHERE l2tp_protocols IS NOT NULL AND SIZE(l2tp_protocols) > 0;

-- TELNET协议明细视图
CREATE VIEW dwd_telnet_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(telnet_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(telnet_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(telnet_detail, '$.term_width') as telnet_term_width,
  JSON_EXTRACT(telnet_detail, '$.term_height') as telnet_term_height,
  JSON_EXTRACT(telnet_detail, '$.term_type') as telnet_term_type,
  JSON_EXTRACT(telnet_detail, '$.username') as telnet_username
FROM dwd_session_logs
LATERAL VIEW EXPLODE(telnet_protocols) tmp AS telnet_detail
WHERE telnet_protocols IS NOT NULL AND SIZE(telnet_protocols) > 0;

-- RLOGIN协议明细视图
CREATE VIEW dwd_rlogin_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(rlogin_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(rlogin_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(rlogin_detail, '$.rows') as rlogin_rows,
  JSON_EXTRACT(rlogin_detail, '$.columns') as rlogin_columns,
  JSON_EXTRACT(rlogin_detail, '$.term_type') as rlogin_term_type,
  JSON_EXTRACT(rlogin_detail, '$.client_username') as rlogin_client_username,
  JSON_EXTRACT(rlogin_detail, '$.server_username') as rlogin_server_username,
  JSON_EXTRACT(rlogin_detail, '$.terminal_speed') as rlogin_terminal_speed
FROM dwd_session_logs
LATERAL VIEW EXPLODE(rlogin_protocols) tmp AS rlogin_detail
WHERE rlogin_protocols IS NOT NULL AND SIZE(rlogin_protocols) > 0;

-- ICMP协议明细视图
CREATE VIEW dwd_icmp_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(icmp_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(icmp_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(icmp_detail, '$.msg_type') as icmp_msg_type,
  JSON_EXTRACT(icmp_detail, '$.info_code') as icmp_info_code,
  JSON_EXTRACT(icmp_detail, '$.echo_seq_num') as icmp_echo_seq_num,
  JSON_EXTRACT(icmp_detail, '$.response_time') as icmp_response_time,
  JSON_EXTRACT(icmp_detail, '$.payload_size') as icmp_payload_size,
  JSON_EXTRACT(icmp_detail, '$.identifier') as icmp_identifier
FROM dwd_session_logs
LATERAL VIEW EXPLODE(icmp_protocols) tmp AS icmp_detail
WHERE icmp_protocols IS NOT NULL AND SIZE(icmp_protocols) > 0;

-- NTP协议明细视图
CREATE VIEW dwd_ntp_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(ntp_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(ntp_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(ntp_detail, '$.version') as ntp_version,
  JSON_EXTRACT(ntp_detail, '$.client_stratum') as ntp_client_stratum,
  JSON_EXTRACT(ntp_detail, '$.server_stratum') as ntp_server_stratum,
  JSON_EXTRACT(ntp_detail, '$.client_reference_id') as ntp_client_reference_id,
  JSON_EXTRACT(ntp_detail, '$.server_reference_id') as ntp_server_reference_id,
  JSON_EXTRACT(ntp_detail, '$.precision') as ntp_precision,
  JSON_EXTRACT(ntp_detail, '$.root_delay') as ntp_root_delay,
  JSON_EXTRACT(ntp_detail, '$.root_dispersion') as ntp_root_dispersion
FROM dwd_session_logs
LATERAL VIEW EXPLODE(ntp_protocols) tmp AS ntp_detail
WHERE ntp_protocols IS NOT NULL AND SIZE(ntp_protocols) > 0;

-- XDMCP协议明细视图
CREATE VIEW dwd_xdmcp_protocol_details AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  duration,
  JSON_EXTRACT(xdmcp_detail, '$.sequence') as protocol_sequence,
  JSON_EXTRACT(xdmcp_detail, '$.timestamp') as protocol_timestamp,
  JSON_EXTRACT(xdmcp_detail, '$.version') as xdmcp_version,
  JSON_EXTRACT(xdmcp_detail, '$.display_number') as xdmcp_display_number,
  JSON_EXTRACT(xdmcp_detail, '$.session_id') as xdmcp_session_id,
  JSON_EXTRACT(xdmcp_detail, '$.hostname') as xdmcp_hostname,
  JSON_EXTRACT(xdmcp_detail, '$.status') as xdmcp_status,
  JSON_EXTRACT(xdmcp_detail, '$.authentication_name') as xdmcp_authentication_name
FROM dwd_session_logs
LATERAL VIEW EXPLODE(xdmcp_protocols) tmp AS xdmcp_detail
WHERE xdmcp_protocols IS NOT NULL AND SIZE(xdmcp_protocols) > 0;

-- ========== 协议统计汇总视图 ==========

-- 协议统计汇总视图
CREATE VIEW dwd_session_protocol_summary AS
SELECT
  session_id,
  src_ip,
  dst_ip,
  src_port,
  dst_port,
  begin_time,
  end_time,
  duration,

  -- 协议类型统计
  CASE WHEN http_protocols IS NOT NULL AND SIZE(http_protocols) > 0 THEN 1 ELSE 0 END as has_http,
  CASE WHEN ssl_protocols IS NOT NULL AND SIZE(ssl_protocols) > 0 THEN 1 ELSE 0 END as has_ssl,
  CASE WHEN dns_protocols IS NOT NULL AND SIZE(dns_protocols) > 0 THEN 1 ELSE 0 END as has_dns,
  CASE WHEN ssh_protocols IS NOT NULL AND SIZE(ssh_protocols) > 0 THEN 1 ELSE 0 END as has_ssh,
  CASE WHEN vnc_protocols IS NOT NULL AND SIZE(vnc_protocols) > 0 THEN 1 ELSE 0 END as has_vnc,
  CASE WHEN rdp_protocols IS NOT NULL AND SIZE(rdp_protocols) > 0 THEN 1 ELSE 0 END as has_rdp,
  CASE WHEN telnet_protocols IS NOT NULL AND SIZE(telnet_protocols) > 0 THEN 1 ELSE 0 END as has_telnet,
  CASE WHEN rlogin_protocols IS NOT NULL AND SIZE(rlogin_protocols) > 0 THEN 1 ELSE 0 END as has_rlogin,
  CASE WHEN icmp_protocols IS NOT NULL AND SIZE(icmp_protocols) > 0 THEN 1 ELSE 0 END as has_icmp,
  CASE WHEN ntp_protocols IS NOT NULL AND SIZE(ntp_protocols) > 0 THEN 1 ELSE 0 END as has_ntp,
  CASE WHEN xdmcp_protocols IS NOT NULL AND SIZE(xdmcp_protocols) > 0 THEN 1 ELSE 0 END as has_xdmcp,
  CASE WHEN s7_protocols IS NOT NULL AND SIZE(s7_protocols) > 0 THEN 1 ELSE 0 END as has_s7,
  CASE WHEN modbus_protocols IS NOT NULL AND SIZE(modbus_protocols) > 0 THEN 1 ELSE 0 END as has_modbus,
  CASE WHEN iec104_protocols IS NOT NULL AND SIZE(iec104_protocols) > 0 THEN 1 ELSE 0 END as has_iec104,
  CASE WHEN eip_protocols IS NOT NULL AND SIZE(eip_protocols) > 0 THEN 1 ELSE 0 END as has_eip,
  CASE WHEN opc_protocols IS NOT NULL AND SIZE(opc_protocols) > 0 THEN 1 ELSE 0 END as has_opc,
  CASE WHEN esp_protocols IS NOT NULL AND SIZE(esp_protocols) > 0 THEN 1 ELSE 0 END as has_esp,
  CASE WHEN l2tp_protocols IS NOT NULL AND SIZE(l2tp_protocols) > 0 THEN 1 ELSE 0 END as has_l2tp,

  -- 协议数量统计
  COALESCE(SIZE(http_protocols), 0) as http_count,
  COALESCE(SIZE(ssl_protocols), 0) as ssl_count,
  COALESCE(SIZE(dns_protocols), 0) as dns_count,
  COALESCE(SIZE(ssh_protocols), 0) as ssh_count,
  COALESCE(SIZE(vnc_protocols), 0) as vnc_count,
  COALESCE(SIZE(rdp_protocols), 0) as rdp_count,
  COALESCE(SIZE(telnet_protocols), 0) as telnet_count,
  COALESCE(SIZE(rlogin_protocols), 0) as rlogin_count,
  COALESCE(SIZE(icmp_protocols), 0) as icmp_count,
  COALESCE(SIZE(ntp_protocols), 0) as ntp_count,
  COALESCE(SIZE(xdmcp_protocols), 0) as xdmcp_count,
  COALESCE(SIZE(s7_protocols), 0) as s7_count,
  COALESCE(SIZE(modbus_protocols), 0) as modbus_count,
  COALESCE(SIZE(iec104_protocols), 0) as iec104_count,
  COALESCE(SIZE(eip_protocols), 0) as eip_count,
  COALESCE(SIZE(opc_protocols), 0) as opc_count,
  COALESCE(SIZE(esp_protocols), 0) as esp_count,
  COALESCE(SIZE(l2tp_protocols), 0) as l2tp_count,

  -- 总协议数量
  (COALESCE(SIZE(http_protocols), 0) +
   COALESCE(SIZE(ssl_protocols), 0) +
   COALESCE(SIZE(dns_protocols), 0) +
   COALESCE(SIZE(ssh_protocols), 0) +
   COALESCE(SIZE(vnc_protocols), 0) +
   COALESCE(SIZE(rdp_protocols), 0) +
   COALESCE(SIZE(telnet_protocols), 0) +
   COALESCE(SIZE(rlogin_protocols), 0) +
   COALESCE(SIZE(icmp_protocols), 0) +
   COALESCE(SIZE(ntp_protocols), 0) +
   COALESCE(SIZE(xdmcp_protocols), 0) +
   COALESCE(SIZE(s7_protocols), 0) +
   COALESCE(SIZE(modbus_protocols), 0) +
   COALESCE(SIZE(iec104_protocols), 0) +
   COALESCE(SIZE(eip_protocols), 0) +
   COALESCE(SIZE(opc_protocols), 0) +
   COALESCE(SIZE(esp_protocols), 0) +
   COALESCE(SIZE(l2tp_protocols), 0)) as total_protocol_count,

  -- 协议类型列表（用于快速查看会话包含哪些协议）
  CONCAT_WS(',',
    CASE WHEN http_protocols IS NOT NULL AND SIZE(http_protocols) > 0 THEN 'HTTP' ELSE NULL END,
    CASE WHEN ssl_protocols IS NOT NULL AND SIZE(ssl_protocols) > 0 THEN 'SSL' ELSE NULL END,
    CASE WHEN dns_protocols IS NOT NULL AND SIZE(dns_protocols) > 0 THEN 'DNS' ELSE NULL END,
    CASE WHEN ssh_protocols IS NOT NULL AND SIZE(ssh_protocols) > 0 THEN 'SSH' ELSE NULL END,
    CASE WHEN vnc_protocols IS NOT NULL AND SIZE(vnc_protocols) > 0 THEN 'VNC' ELSE NULL END,
    CASE WHEN rdp_protocols IS NOT NULL AND SIZE(rdp_protocols) > 0 THEN 'RDP' ELSE NULL END,
    CASE WHEN telnet_protocols IS NOT NULL AND SIZE(telnet_protocols) > 0 THEN 'TELNET' ELSE NULL END,
    CASE WHEN rlogin_protocols IS NOT NULL AND SIZE(rlogin_protocols) > 0 THEN 'RLOGIN' ELSE NULL END,
    CASE WHEN icmp_protocols IS NOT NULL AND SIZE(icmp_protocols) > 0 THEN 'ICMP' ELSE NULL END,
    CASE WHEN ntp_protocols IS NOT NULL AND SIZE(ntp_protocols) > 0 THEN 'NTP' ELSE NULL END,
    CASE WHEN xdmcp_protocols IS NOT NULL AND SIZE(xdmcp_protocols) > 0 THEN 'XDMCP' ELSE NULL END,
    CASE WHEN s7_protocols IS NOT NULL AND SIZE(s7_protocols) > 0 THEN 'S7' ELSE NULL END,
    CASE WHEN modbus_protocols IS NOT NULL AND SIZE(modbus_protocols) > 0 THEN 'MODBUS' ELSE NULL END,
    CASE WHEN iec104_protocols IS NOT NULL AND SIZE(iec104_protocols) > 0 THEN 'IEC104' ELSE NULL END,
    CASE WHEN eip_protocols IS NOT NULL AND SIZE(eip_protocols) > 0 THEN 'EIP' ELSE NULL END,
    CASE WHEN opc_protocols IS NOT NULL AND SIZE(opc_protocols) > 0 THEN 'OPC' ELSE NULL END,
    CASE WHEN esp_protocols IS NOT NULL AND SIZE(esp_protocols) > 0 THEN 'ESP' ELSE NULL END,
    CASE WHEN l2tp_protocols IS NOT NULL AND SIZE(l2tp_protocols) > 0 THEN 'L2TP' ELSE NULL END
  ) as protocol_types,

  create_time,
  update_time
FROM dwd_session_logs;

-- ========== 常用查询示例 ==========

-- 示例1：查询特定会话的所有协议信息
-- SELECT session_id, protocol_types, total_protocol_count, http_count, ssl_count, dns_count
-- FROM dwd_session_protocol_summary
-- WHERE session_id = 'your_session_id';

-- 示例2：查询包含特定域名的HTTP会话
-- SELECT session_id, src_ip, dst_ip, http_method, http_url, http_host, http_status_code
-- FROM dwd_http_protocol_details
-- WHERE http_host LIKE '%example.com%';

-- 示例3：查询SSL证书即将过期的会话
-- SELECT session_id, src_ip, dst_ip, ssl_server_name, ssl_cert_subject, ssl_cert_not_after
-- FROM dwd_ssl_protocol_details
-- WHERE ssl_cert_not_after < DATE_ADD(NOW(), INTERVAL 30 DAY);

-- 示例4：查询DNS查询失败的会话
-- SELECT session_id, src_ip, dst_ip, dns_query_name, dns_response_code
-- FROM dwd_dns_protocol_details
-- WHERE dns_response_code != 'NOERROR';

-- 示例5：统计各协议类型的会话数量
-- SELECT
--   SUM(has_http) as http_sessions,
--   SUM(has_ssl) as ssl_sessions,
--   SUM(has_dns) as dns_sessions,
--   SUM(has_ssh) as ssh_sessions,
--   SUM(has_vnc) as vnc_sessions,
--   SUM(has_rdp) as rdp_sessions,
--   COUNT(*) as total_sessions
-- FROM dwd_session_protocol_summary;

-- 示例6：查询S7工控协议会话
-- SELECT session_id, src_ip, dst_ip, s7_function, s7_pdu_size, s7_src_rack, s7_src_slot
-- FROM dwd_s7_protocol_details
-- WHERE s7_function = 240;

-- 示例6a：查询Modbus工控协议会话
-- SELECT session_id, src_ip, dst_ip, modbus_func_code, modbus_slave_id
-- FROM dwd_modbus_protocol_details
-- WHERE modbus_func_code = 3;

-- 示例6b：查询ESP协议会话
-- SELECT session_id, src_ip, dst_ip, esp_protocol_family, esp_encapsulation_mode, esp_spi
-- FROM dwd_esp_protocol_details
-- WHERE esp_encapsulation_mode = 1;

-- 示例6c：查询L2TP协议会话
-- SELECT session_id, src_ip, dst_ip, l2tp_server_hostname, l2tp_client_hostname, l2tp_is_negotiate_success
-- FROM dwd_l2tp_protocol_details
-- WHERE l2tp_is_negotiate_success = 1;

-- 示例7：使用JSON函数查询HTTP协议中的特定请求头
-- SELECT session_id, src_ip, dst_ip,
--        JSON_EXTRACT(http_protocols, '$[*].request_headers.User-Agent') as user_agents
-- FROM dwd_session_logs
-- WHERE http_protocols IS NOT NULL
--   AND JSON_EXTRACT(http_protocols, '$[*].request_headers.User-Agent') LIKE '%Chrome%';

-- 示例8：查询包含多种协议的复杂会话
-- SELECT session_id, src_ip, dst_ip, protocol_types, total_protocol_count
-- FROM dwd_session_protocol_summary
-- WHERE total_protocol_count > 5
-- ORDER BY total_protocol_count DESC;

-- ========== 数据插入示例 ==========

-- 示例9：插入HTTP协议数据到会话表
-- INSERT INTO dwd_session_logs (
--   session_id, src_ip, dst_ip, src_port, dst_port, protocol, begin_time,
--   http_protocols
-- ) VALUES (
--   'session_001', '*************', '*************', 12345, 80, 6, '2024-01-01 12:00:00',
--   [
--     '{"sequence":1,"timestamp":"2024-01-01 12:00:01","method":"GET","url":"/api/users","host":"api.example.com","status_code":200,"user_agent":"Mozilla/5.0...","request_headers":{"Accept":"application/json"},"response_headers":{"Content-Type":"application/json"},"request_size":512,"response_size":1024}',
--     '{"sequence":2,"timestamp":"2024-01-01 12:00:05","method":"POST","url":"/api/login","host":"api.example.com","status_code":401,"user_agent":"Mozilla/5.0...","request_headers":{"Content-Type":"application/json"},"response_headers":{"Content-Type":"application/json"},"request_size":256,"response_size":128}'
--   ]
-- );

-- 示例10：插入SSL协议数据到会话表
-- INSERT INTO dwd_session_logs (
--   session_id, src_ip, dst_ip, src_port, dst_port, protocol, begin_time,
--   ssl_protocols
-- ) VALUES (
--   'session_002', '*************', '*************', 12346, 443, 6, '2024-01-01 12:01:00',
--   [
--     '{"sequence":1,"timestamp":"2024-01-01 12:01:01","version":"TLS1.3","cipher_suite":"TLS_AES_256_GCM_SHA384","server_name":"secure.example.com","cert_subject":"CN=secure.example.com,O=Example Inc","cert_issuer":"CN=Let\'s Encrypt Authority","cert_serial":"03A1B2C3D4E5F6","cert_not_before":"2024-01-01","cert_not_after":"2024-12-31","cert_sha1":"A1B2C3D4E5F6...","cert_sha256":"D4E5F6G7H8I9...","ja3":"769,47-53-5-10-49161-49162...","ja3s":"769,47,65281","is_self_signed":false}'
--   ]
-- );

-- ========== 性能优化建议 ==========

-- 1. 索引建议：
-- - 在 session_id 上创建布隆过滤器索引（已配置）
-- - 在 src_ip, dst_ip 上创建布隆过滤器索引（已配置）
-- - 考虑在协议JSON数组的常用字段上创建倒排索引

-- 2. 分区策略：
-- - 按时间分区（已配置动态分区）
-- - 考虑按协议类型进行二级分区（如果查询模式支持）

-- 3. 查询优化：
-- - 使用视图简化复杂查询
-- - 利用JSON函数进行高效的协议数据过滤
-- - 合理使用LATERAL VIEW EXPLODE展开数组数据

-- 4. 存储优化：
-- - 使用ZSTD压缩（已配置）
-- - 合理设置副本数（已配置为3）
-- - 定期清理过期数据

-- ========== 监控查询示例 ==========

-- 示例11：协议分布统计
-- SELECT
--   DATE(begin_time) as date,
--   SUM(has_http) as http_sessions,
--   SUM(has_ssl) as ssl_sessions,
--   SUM(has_dns) as dns_sessions,
--   SUM(has_ssh) as ssh_sessions,
--   COUNT(*) as total_sessions
-- FROM dwd_session_protocol_summary
-- WHERE begin_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
-- GROUP BY DATE(begin_time)
-- ORDER BY date DESC;

-- 示例12：异常协议检测
-- SELECT session_id, src_ip, dst_ip, protocol_types, total_protocol_count
-- FROM dwd_session_protocol_summary
-- WHERE total_protocol_count > 10  -- 协议数量异常多的会话
--    OR (has_ssh = 1 AND has_http = 1)  -- 同时包含SSH和HTTP的可疑会话
-- ORDER BY total_protocol_count DESC;

-- 示例13：SSL证书监控
-- SELECT
--   ssl_server_name,
--   ssl_cert_not_after,
--   DATEDIFF(ssl_cert_not_after, NOW()) as days_until_expiry,
--   COUNT(*) as session_count
-- FROM dwd_ssl_protocol_details
-- WHERE ssl_cert_not_after < DATE_ADD(NOW(), INTERVAL 90 DAY)  -- 90天内过期
-- GROUP BY ssl_server_name, ssl_cert_not_after
-- ORDER BY days_until_expiry ASC;



















-- 使用数据库
USE nta;

-- ODS层 - SSL/TLS协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_ssl_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- SSL specific fields
  ssl_version INT COMMENT "SSL版本",
  ssl_c_version INT COMMENT "客户端SSL版本",
  ssl_hello_c_version INT COMMENT "客户端Hello版本",
  ssl_hello_c_time INT COMMENT "客户端Hello时间",
  ssl_hello_c_random STRING COMMENT "客户端Hello随机数",
  ssl_hello_c_sessionid STRING COMMENT "客户端Hello会话ID",
  ssl_hello_c_sessionidlen INT COMMENT "客户端Hello会话ID长度",
  ssl_hello_c_ciphersuit STRING COMMENT "客户端Hello加密套件",
  ssl_hello_c_ciphersuitnum INT COMMENT "客户端Hello加密套件数量",
  ssl_hello_c_compressionmethod STRING COMMENT "客户端Hello压缩方法",
  ssl_hello_c_compressionmethodlen INT COMMENT "客户端Hello压缩方法长度",
  ssl_hello_c_extentionnum INT COMMENT "客户端Hello扩展数量",
  ssl_hello_c_extention STRING COMMENT "客户端Hello扩展",
  ssl_hello_c_alpn STRING COMMENT "客户端Hello ALPN",
  ssl_hello_c_servername STRING COMMENT "客户端Hello服务器名称(SNI)",
  ssl_hello_c_servernametype INT COMMENT "客户端Hello服务器名称类型",
  ssl_hello_c_sessionticket STRING COMMENT "客户端Hello会话票据",

  ssl_cert_c_num INT COMMENT "客户端证书数量",
  ssl_cert_c_hash STRING COMMENT "客户端证书哈希",

  ssl_hello_s_version INT COMMENT "服务端Hello版本",
  ssl_hello_s_time INT COMMENT "服务端Hello时间",
  ssl_hello_s_random STRING COMMENT "服务端Hello随机数",
  ssl_hello_s_sessionid STRING COMMENT "服务端Hello会话ID",
  ssl_hello_s_sessionidlen INT COMMENT "服务端Hello会话ID长度",
  ssl_hello_s_cipersuite STRING COMMENT "服务端Hello加密套件",
  ssl_hello_s_compressionmethod STRING COMMENT "服务端Hello压缩方法",
  ssl_hello_s_extentionnum INT COMMENT "服务端Hello扩展数量",
  ssl_hello_s_extention STRING COMMENT "服务端Hello扩展",
  ssl_hello_s_alpn STRING COMMENT "服务端Hello ALPN",
  ssl_hello_s_sessionticket STRING COMMENT "服务端Hello会话票据",

  ssl_cert_s_num INT COMMENT "服务端证书数量",
  ssl_cert_s_hash STRING COMMENT "服务端证书哈希",

  ssl_s_newsessionticket_lifetime INT COMMENT "服务端新会话票据生命周期",
  ssl_s_newsessionticket_ticket STRING COMMENT "服务端新会话票据",
  ssl_s_newsessionticket_ticketlen INT COMMENT "服务端新会话票据长度",

  ssl_c_keyexchangelen INT COMMENT "客户端密钥交换长度",
  ssl_c_keyexchange STRING COMMENT "客户端密钥交换",
  ssl_s_keyexchangelen INT COMMENT "服务端密钥交换长度",
  ssl_s_keyexchange STRING COMMENT "服务端密钥交换",

  ssl_c_finger BIGINT COMMENT "SSL客户端指纹",
  ssl_s_finger BIGINT COMMENT "SSL服务端指纹",

  -- JA3 fingerprints
  cli_ja3 STRING COMMENT "客户端JA3指纹",
  srv_ja3 STRING COMMENT "服务端JA3S指纹",

  -- 基本扩展字段
  con_type INT COMMENT "内容类型",
  ale_lev INT COMMENT "告警级别",
  ale_des INT COMMENT "告警描述",
  hand_sha_type INT COMMENT "握手类型",
  cli_rand STRING COMMENT "客户端随机生成的参与主密钥计算的序列",
  srv_name STRING COMMENT "服务器名",
  srv_name_attr INT COMMENT "服务器名属性",
  srv_gmt_uni_time BIGINT COMMENT "服务端GMT时间",
  srv_rand STRING COMMENT "服务端随机生成的参与主密钥计算的序列",
  srv_cert_len INT COMMENT "服务端证书长度",
  cert_res_type INT COMMENT "证书请求类型",
  cli_cert_len INT COMMENT "客户端证书长度",

  -- 密钥交换信息字段
  rsa_mod_of_srv_key_exc STRING COMMENT "服务端密钥交换信息RSA模数",
  rsa_exp_of_srv_key_exc BIGINT COMMENT "服务端密钥交换信息RSA指数",
  dh_mod_of_srv_key_exc STRING COMMENT "服务端密钥交换信息DH模数",
  dh_gen_of_srv_key_exc STRING COMMENT "服务端密钥交换信息DH底数",
  srv_dh_pub_key STRING COMMENT "服务端密钥交换信息DH公钥",
  pre_mas_key_encry_by_rsa STRING COMMENT "RSA加密的预主密钥",
  cli_dh_pub_key STRING COMMENT "客户端发出的DH公钥",

  -- 扩展类型字段
  ext_type_in_ssl INT COMMENT "扩展类型",

  -- 椭圆曲线相关字段
  cli_ell_cur_poi_for INT COMMENT "客户端椭圆曲线点格式",
  cli_ell_cur INT COMMENT "客户端椭圆曲线名称",
  srv_ell_cur_poi_for INT COMMENT "服务端椭圆曲线点格式",
  srv_ell_cur INT COMMENT "服务端椭圆曲线名称",
  srv_ell_cur_dh_pub_key STRING COMMENT "服务端椭圆曲线DH值",
  cli_ell_cur_dh_pub_key STRING COMMENT "客户端椭圆曲线DH值",

  -- 时间和长度字段
  srv_gmt_uni__time BIGINT COMMENT "客户端响应服务端证书请求时发出的证书长度",
  cli_hand_sk_len INT COMMENT "客户端握手过程字节数",
  srv_hand_sk_len INT COMMENT "服务端握手过程字节数",

  -- 扩展序列字段
  cli_ext STRING COMMENT "客户端扩展类型组成的序列串",
  srv_ext STRING COMMENT "服务端扩展类型组成的序列串",
  cli_ext_grease INT COMMENT "客户端是否发送了GREASE扩展",

  -- 会话票据相关字段
  cli_sess_ticket STRING COMMENT "客户端用以识别活跃或可恢复会话的ticket",
  srv_sess_ticket STRING COMMENT "服务端用以识别活跃或可恢复会话的ticket",

  -- 认证和曲线组字段
  auth_tag INT COMMENT "是否双向认证",
  ec_groups_cli STRING COMMENT "客户端支持的椭圆曲线组的序列",
  ec_poi_for_by_serv STRING COMMENT "服务端支持的椭圆曲线点序列",

  -- TLS标签字段
  etags STRING COMMENT "TLS异常标签",
  ttags STRING COMMENT "TLS威胁标签",

  -- 曲线和签名字段
  ecdh_cur_type STRING COMMENT "曲线类型",
  ecdh_sig STRING COMMENT "ECDH签名",
  dhep_len STRING COMMENT "参数p长度",
  dheg_len STRING COMMENT "参数g长度",

  -- 公钥相关字段
  enc_pub_key STRING COMMENT "加密后的公钥",
  enc_pub_key_len STRING COMMENT "加密后的公钥长度",

  -- 扩展长度字段
  cli_ext_len STRING COMMENT "客户端扩展长度",
  srv_ext_len STRING COMMENT "服务端扩展长度",
  ecdh_pub_key_len STRING COMMENT "椭圆曲线DH值长度",

  -- 服务名和票据字段
  nam_type STRING COMMENT "服务名类型",
  nam_len STRING COMMENT "服务名长度",
  tic_dat STRING COMMENT "票据数据",

  -- 密码套件字段
  cip_sui_num INT COMMENT "密码套件个数",

  -- 签名Hash算法字段
  ecdh_sig_hash STRING COMMENT "ECDH签名Hash&算法",
  dhe_sig_hash STRING COMMENT "DHE签名Hash&算法",
  rsa_sig_hash STRING COMMENT "RSA签名Hash&算法",

  -- GREASE扩展标识字段
  grease_flag STRING COMMENT "GREASE扩展标识",

  -- 模数和指数长度字段
  rsa_mod_len STRING COMMENT "模数长度",
  rsa_exp_len STRING COMMENT "指数长度",

  -- 签名字段
  rsa_sig STRING COMMENT "RSA签名",
  dhe_sig STRING COMMENT "DH签名",

  -- DH公钥字段
  dhe_pub_key_len STRING COMMENT "DH公钥长度",
  dhe_pub_key STRING COMMENT "DH公钥",

  -- 签名算法字段
  sig_alg_type STRING COMMENT "签名算法类型",
  sig_alg STRING COMMENT "签名算法",
  sig_hash_alg STRING COMMENT "签名哈希算法",

  -- 指纹相关字段
  joy STRING COMMENT "客户端joy指纹",
  joys STRING COMMENT "服务端joys指纹",

  -- 标签字段
  starttls STRING COMMENT "starttls出现标签",
  cert_non_flag STRING COMMENT "无证书标签",
  joy_fp STRING COMMENT "Joy指纹",
  cert_intact_flag STRING COMMENT "数据完整性",
  cert_path STRING COMMENT "证书路径",
  sess_sec_flag STRING COMMENT "会话安全性标签",

  -- 全文和证书字段
  full_text STRING COMMENT "全文（证书数据）",
  cert_exist INT COMMENT "证书存在是否，1：存在；0：不存在",
  extend_ec_groups_client STRING COMMENT "客户端支持的椭圆曲线组的序列",
  leaf_cert_days_remaining INT COMMENT "叶子证书的剩余有效天数",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, ssl_hello_c_servername, ssl_cert_s_hash)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);

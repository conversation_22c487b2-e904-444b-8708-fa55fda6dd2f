-- 使用数据库
USE nta;

-- ODS层 - L2TP协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_l2tp_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- L2TP specific fields (l2tp_msg)
  protocol_family INT COMMENT "协议编号",
  communication_rate DOUBLE COMMENT "通信速率",
  direction INT COMMENT "数据流方向",
  protocol_version INT COMMENT "协议版本",
  framing_capabilities INT COMMENT "帧类型",
  bearer_capabilities INT COMMENT "接入方式",
  server_hostname VARCHAR(255) COMMENT "服务器主机名称",
  client_hostname VARCHAR(255) COMMENT "客户端主机名称",
  server_vendorname VARCHAR(255) COMMENT "服务器供应商名称",
  client_vendorname VARCHAR(255) COMMENT "客户端供应商名称",
  calling_number VARCHAR(64) COMMENT "呼叫方的电话号码",
  proxy_authen_type INT COMMENT "代理认证类型",
  proxy_authen_name VARCHAR(255) COMMENT "代理认证名",
  is_negotiate_success INT COMMENT "协商是否成功",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, protocol_family)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);

-- 使用数据库
USE nta;

-- ODS层 - SSH协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_ssh_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- 客户端KEX (ssh_kex_msg client)
  client_protocol STRING COMMENT "客户端协议版本",
  client_cookie STRING COMMENT "客户端cookie值",
  client_kex_algorithms ARRAY<STRING> COMMENT "客户端支持的密钥交换算法列表",
  client_server_host_key_algorithms ARRAY<STRING> COMMENT "客户端支持的服务器主机密钥算法列表",
  client_encryption_algorithms_client_to_server ARRAY<STRING> COMMENT "客户端支持的C2S加密算法列表",
  client_encryption_algorithms_server_to_client ARRAY<STRING> COMMENT "客户端支持的S2C加密算法列表",
  client_mac_algorithms_client_to_server ARRAY<STRING> COMMENT "客户端支持的C2S消息认证码算法列表",
  client_mac_algorithms_server_to_client ARRAY<STRING> COMMENT "客户端支持的S2C消息认证码算法列表",
  client_compression_algorithms_client_to_server ARRAY<STRING> COMMENT "客户端支持的C2S压缩算法列表",
  client_compression_algorithms_server_to_client ARRAY<STRING> COMMENT "客户端支持的S2C压缩算法列表",

  -- 服务端KEX (ssh_kex_msg server)
  server_protocol STRING COMMENT "服务端协议版本",
  server_cookie STRING COMMENT "服务端cookie值",
  server_kex_algorithms ARRAY<STRING> COMMENT "服务端支持的密钥交换算法列表",
  server_server_host_key_algorithms ARRAY<STRING> COMMENT "服务端支持的服务器主机密钥算法列表",
  server_encryption_algorithms_client_to_server ARRAY<STRING> COMMENT "服务端支持的C2S加密算法列表",
  server_encryption_algorithms_server_to_client ARRAY<STRING> COMMENT "服务端支持的S2C加密算法列表",
  server_mac_algorithms_client_to_server ARRAY<STRING> COMMENT "服务端支持的C2S消息认证码算法列表",
  server_mac_algorithms_server_to_client ARRAY<STRING> COMMENT "服务端支持的S2C消息认证码算法列表",
  server_compression_algorithms_client_to_server ARRAY<STRING> COMMENT "服务端支持的C2S压缩算法列表",
  server_compression_algorithms_server_to_client ARRAY<STRING> COMMENT "服务端支持的S2C压缩算法列表",

  -- DH相关字段
  dh_e STRING COMMENT "DH client e",
  dh_f STRING COMMENT "DH server f",
  dh_gex_min STRING COMMENT "DH GEX Min",
  dh_gex_nbits STRING COMMENT "DH GEX Number of Bits",
  dh_gex_max STRING COMMENT "DH GEX Max",
  dh_gex_p STRING COMMENT "DH GEX modulus (P)",
  dh_gex_g STRING COMMENT "DH GEX base (G)",

  -- ECDH相关字段
  ecdh_q_c STRING COMMENT "ECDH client's ephemeral public key (Q_C)",
  ecdh_q_s STRING COMMENT "ECDH server's ephemeral public key (Q_S)",

  -- 主机密钥相关字段
  host_key_type STRING COMMENT "主机密钥类型",
  host_key_rsa_e STRING COMMENT "RSA public exponent (e)",
  host_key_rsa_n STRING COMMENT "RSA modulus (N)",
  host_key_ecdsa_id STRING COMMENT "ECDSA elliptic curve identifier",
  host_key_ecdsa_q STRING COMMENT "ECDSA public key (Q)",
  host_key_dsa_p STRING COMMENT "DSA prime modulus (p)",
  host_key_dsa_q STRING COMMENT "DSA prime divisor (q)",
  host_key_dsa_g STRING COMMENT "DSA subgroup generator (g)",
  host_key_dsa_y STRING COMMENT "DSA public key (y)",
  host_key_eddsa_key STRING COMMENT "EdDSA public key",

  -- KEX签名相关字段
  kex_h_sig_type STRING COMMENT "KEX H signature type",
  kex_h_sig STRING COMMENT "KEX H signature",

  -- 指纹相关字段
  srvhostkeyfp256 STRING COMMENT "服务端主机密钥指纹",
  hassh STRING COMMENT "客户端HASSH指纹",
  srv_hassh STRING COMMENT "服务端HASSH指纹",
  ssh_key_fingerprint_md5_server STRING COMMENT "key exchage字段md5哈希值",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, srvhostkeyfp256)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);

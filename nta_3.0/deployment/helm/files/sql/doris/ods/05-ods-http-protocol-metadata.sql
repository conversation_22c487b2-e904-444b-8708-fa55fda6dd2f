-- 使用数据库
USE nta;

-- ODS层 - HTTP协议元数据表 (原始数据)
CREATE TABLE IF NOT EXISTS ods_http_protocol_metadata (
  -- 公共字段 (Comm_msg)
  session_id VARCHAR(64) NOT NULL COMMENT "会话ID",
  begin_time DATETIME NOT NULL COMMENT "会话起始时间",
  src_ip VARCHAR(50) NOT NULL COMMENT "源IP地址",
  dst_ip VARCHAR(50) NOT NULL COMMENT "目标IP地址",
  src_port INT COMMENT "源端口",
  dst_port INT COMMENT "目标端口",
  ippro INT COMMENT "IP协议号",
  server_ip VARCHAR(64) COMMENT "服务器IP",
  app_id INT COMMENT "应用ID",
  app_name VARCHAR(128) COMMENT "应用名称",
  thread_id INT COMMENT "线程ID",
  task_id INT COMMENT "任务ID",
  begin_nsec INT COMMENT "会话起始时间纳秒数",
  batch_id INT COMMENT "批次ID",

  -- HTTP基本字段
  act VARCHAR(16) COMMENT "请求动作",
  url VARCHAR(2048) COMMENT "URL",
  host VARCHAR(256) COMMENT "主机名",
  response VARCHAR(64) COMMENT "应答动作",
  http_c_finger BIGINT COMMENT "HTTP请求指纹",
  http_s_finger BIGINT COMMENT "HTTP应答指纹",

  -- HTTP头部键值对（使用ARRAY<STRUCT>结构）
  http_client_kv ARRAY<STRUCT<key:VARCHAR(256), val:VARCHAR(2048)>> COMMENT "HTTP请求KV提取",
  http_client_title ARRAY<VARCHAR(1024)> COMMENT "HTTP Title数组",
  http_server_kv ARRAY<STRUCT<key:VARCHAR(256), val:VARCHAR(2048)>> COMMENT "HTTP响应KV提取",
  http_server_title ARRAY<VARCHAR(1024)> COMMENT "HTTP Title数组",

  -- 系统字段
  create_time DATETIME DEFAULT NOW() COMMENT "记录创建时间"
)
DUPLICATE KEY(session_id, begin_time, src_ip, dst_ip, src_port, dst_port, url, host)
PARTITION BY RANGE(begin_time) (
  PARTITION p_default VALUES LESS THAN (MAXVALUE)
)
DISTRIBUTED BY HASH(session_id) BUCKETS 32
PROPERTIES(
  "replication_num" = "3",
  "storage_medium" = "SSD",
  "storage_cooldown_time" = "30 DAY",
  "dynamic_partition.enable" = "true",
  "dynamic_partition.time_unit" = "DAY",
  "dynamic_partition.start" = "-90",
  "dynamic_partition.end" = "3",
  "dynamic_partition.prefix" = "p",
  "dynamic_partition.buckets" = "32",
  "dynamic_partition.create_history_partition" = "true",
  "compression" = "zstd"
);

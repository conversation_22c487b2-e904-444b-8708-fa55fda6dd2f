cyber_kill_chain,chinese_name,english_name,description,typical_techniques,defense_recommendations
RECONNAISSANCE,侦察探测,Reconnaissance,"攻击者收集目标组织的信息，包括网络架构、系统配置、人员信息等。信息一般通过互联网进行收集（内容包括网站、邮箱、电话、社会工程学等一切可能相关的情报）","{网络扫描,端口扫描,域名查询,社会工程学,开源情报收集}","{加强网络边界监控,限制信息泄露,部署蜜罐系统,监控异常扫描活动}"
WEAPONIZATION,武器化,Weaponization,"攻击者创建恶意载荷，将漏洞利用代码与恶意软件结合形成武器。这个阶段通常在攻击者的基础设施中进行，难以直接检测","{恶意软件开发,漏洞利用工具,载荷封装,免杀处理,木马制作}","{威胁情报收集,恶意软件检测,沙箱分析,特征库更新}"
DELIVERY,投递,Delivery,"攻击者通过各种方式将恶意载荷传输到目标环境。这是攻击者首次与目标系统接触的阶段","{钓鱼邮件,恶意附件,水坑攻击,USB投递,供应链攻击}","{邮件安全网关,Web内容过滤,USB端口控制,用户安全培训}"
EXPLOITATION,漏洞利用,Exploitation,"攻击者利用系统或应用程序漏洞执行恶意代码。一般会利用应用程序或操作系统的漏洞或缺陷等","{漏洞利用,缓冲区溢出,代码注入,权限提升,零日攻击}","{及时补丁管理,漏洞扫描评估,入侵检测系统,行为监控分析}"
INSTALLATION,安装植入,Installation,"攻击者在目标系统设置木马、后门等，一定期限内在目标系统营造活动环境的阶段。攻击者在受害者系统上安装恶意软件、后门或其他持久化机制","{后门安装,Webshell植入,服务安装,注册表修改,计划任务}","{终端安全防护,文件完整性监控,权限管理控制,系统基线检查}"
COMMAND_AND_CONTROL,命令控制,Command and Control,"攻击者建立目标系统攻击路径的阶段。一般使用自动和手工相结合的方式进行，一旦攻击路径确立后，攻击者将能够控制目标系统。攻击者建立与被感染系统的通信通道，实现远程控制","{C2通信,DNS隧道,HTTP/HTTPS通信,加密通道,代理通信}","{网络流量监控,DNS安全防护,代理服务器控制,异常通信检测}"
ACTIONS_ON_OBJECTIVES,目标行动,Actions on Objectives,"攻击者达到预期目标的阶段。攻击目标呈现多样化，可能包括侦察、敏感信息收集、数据破坏、系统摧毁等。攻击者执行最终的攻击目标，如数据窃取、系统破坏、横向移动等","{数据窃取,数据销毁,横向移动,权限维持,勒索加密}","{数据丢失防护,特权账户监控,横向移动检测,应急响应处置}"
OTHER,其他,Other,"监管需求的告警和证书异常类。注意：""其他""不属于攻击链流程中","{合规检查,证书管理,监管要求}","{合规性检查,证书生命周期管理,监管要求落实}"
UNKNOWN,未知,Unknown,无法确定攻击阶段的告警类型,"{未分类告警}","{告警分析,威胁分类,专家研判}"

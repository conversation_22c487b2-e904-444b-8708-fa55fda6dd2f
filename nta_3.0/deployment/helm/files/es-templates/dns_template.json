{"order": 0, "index_patterns": ["dns*"], "settings": {"index": {"max_result_window": 1000000, "max_terms_count": 1000000, "refresh_interval": "30s", "number_of_shards": "1", "translog.durability": "async", "translog.flush_threshold_size": "1024mb", "translog.sync_interval": "120s", "number_of_replicas": "0"}}, "mappings": {"properties": {"Add": {"type": "long"}, "Ans": {"type": "long"}, "Answer": {"properties": {"class": {"type": "long"}, "data_len": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "ttl": {"type": "long"}, "type": {"type": "long"}, "value": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "DomainIp": {"type": "keyword", "index": true}, "AppName": {"type": "keyword", "index": true}, "Auth": {"type": "long"}, "Domain": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "Flags": {"type": "long"}, "ProxyIP": {"type": "keyword", "index": true}, "Hkey": {"type": "keyword", "index": true}, "Que": {"type": "long"}, "Query": {"properties": {"class": {"type": "long"}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "type": {"type": "long"}}}, "SessionId": {"type": "keyword", "index": true}, "StartNSec": {"type": "long"}, "StartTime": {"type": "long"}, "TaskId": {"type": "long"}, "dIp": {"type": "ip"}, "dPort": {"type": "long"}, "es_key": {"type": "keyword", "index": true}, "sIp": {"type": "ip"}, "sPort": {"type": "long"}}}, "aliases": {"add": {"index": "dns_*", "alias": "dns"}}}
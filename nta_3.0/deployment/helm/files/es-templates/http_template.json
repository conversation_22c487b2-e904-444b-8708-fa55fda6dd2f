{"order": 0, "index_patterns": ["http*"], "settings": {"index": {"max_result_window": 1000000, "max_terms_count": 1000000, "refresh_interval": "30s", "number_of_shards": "1", "translog.durability": "async", "translog.flush_threshold_size": "1024mb", "translog.sync_interval": "120s", "number_of_replicas": "0"}}, "mappings": {"properties": {"Act": {"type": "keyword", "index": true}, "BatchNum": {"type": "long"}, "Client": {"properties": {"Accept": {"type": "keyword", "index": true}, "Content-Type": {"type": "keyword", "index": true}, "Cookie": {"type": "keyword", "index": true}, "Title": {"type": "keyword", "index": true}, "Host": {"type": "keyword", "index": true}, "User-Agent": {"type": "keyword", "index": true}}}, "ProxyIP": {"type": "keyword", "index": true}, "Hkey": {"type": "keyword", "index": true}, "Host": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "Response": {"type": "keyword", "index": true}, "Server": {"properties": {"Accept-Ranges": {"type": "keyword", "index": true}, "Content-Encoding": {"type": "keyword", "index": true}, "Content-Length": {"type": "keyword", "index": true}, "Title": {"type": "keyword", "index": true}, "Content-Type": {"type": "keyword", "index": true}}}, "ServerIP": {"type": "keyword", "index": true}, "SessionId": {"type": "keyword", "index": true}, "StartNSec": {"type": "long"}, "StartTime": {"type": "long"}, "TaskId": {"type": "long"}, "Url": {"type": "keyword", "index": true}, "dHTTPFinger": {"type": "keyword", "index": true}, "dIp": {"type": "ip"}, "dPort": {"type": "long"}, "es_key": {"type": "keyword", "index": true}, "sHTTPFinger": {"type": "keyword", "index": true}, "sIp": {"type": "ip"}, "sPort": {"type": "long"}}}, "aliases": {"add": {"index": "http_*", "alias": "http"}}}
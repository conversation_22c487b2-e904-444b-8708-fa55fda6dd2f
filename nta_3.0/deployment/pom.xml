<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>nta-platform</artifactId>
        <groupId>com.geeksec</groupId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>deployment</artifactId>
    <name>deployment</name>
    <description>Deployment Module for NTA Platform</description>

    <properties>
        <helm.chart.dir>${project.basedir}/helm</helm.chart.dir>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>com.kiwigrid</groupId>
                <artifactId>helm-maven-plugin</artifactId>
                <configuration>
                    <chartDirectory>${helm.chart.dir}</chartDirectory>
                    <chartVersion>${project.version}</chartVersion>
                    <uploadRepoStable>
                        <name>harbor-helm</name>
                        <url>https://${harbor.helm.repo}</url>
                        <username>${harbor.helm.username}</username>
                        <password>${harbor.helm.password}</password>
                        <type>CHARTMUSEUM</type>
                    </uploadRepoStable>
                </configuration>
                <executions>
                    <execution>
                        <id>helm-lint</id>
                        <goals>
                            <goal>lint</goal>
                        </goals>
                        <phase>verify</phase>
                    </execution>
                    <execution>
                        <id>helm-package</id>
                        <goals>
                            <goal>package</goal>
                        </goals>
                        <phase>package</phase>
                    </execution>
                    <execution>
                        <id>helm-upload</id>
                        <goals>
                            <goal>upload</goal>
                        </goals>
                        <phase>deploy</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

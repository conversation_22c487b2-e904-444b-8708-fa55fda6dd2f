# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

#####################################################################
## BE 配置
#####################################################################

# 存储路径
storage_root_path = /doris/be/storage

# 优先网络
priority_networks = *********/8

# RPC 端口
be_port = 9060

# HTTP 端口
webserver_port = 8040

# 心跳服务端口
heartbeat_service_port = 9050

# Brpc 端口
brpc_port = 8060

# 日志配置
sys_log_level = INFO
sys_log_dir = /doris/be/log
sys_log_roll_num = 10
sys_log_verbose_modules = *

# 内存限制
mem_limit = 80%

# 并发数
num_threads_per_core = 3
num_threads_per_disk = 3
num_cores = 4

# 存储介质类型
storage_medium_migrate_count = 1
min_percentage_of_available_mem_to_process = 10%

# Flink Configuration

# Metrics Reporter Configuration
metrics.reporter.prometheus.class: org.apache.flink.metrics.prometheus.PrometheusReporter
metrics.reporter.prometheus.port: 9249
metrics.reporter.prometheus.pushgateway.address: pushgateway:9091
metrics.reporter.prometheus.interval: 15 SECONDS
metrics.reporter.prometheus.deleteOnShutdown: false

# JobManager Configuration
jobmanager.rpc.address: flink-jobmanager
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 1600m

# TaskManager Configuration
taskmanager.memory.process.size: 1728m
taskmanager.numberOfTaskSlots: 1

# High Availability Configuration
high-availability: zookeeper
high-availability.storageDir: file:///flink/ha/
high-availability.zookeeper.quorum: zookeeper:2181
high-availability.cluster-id: /flink

# State Backend Configuration
state.backend: filesystem
state.checkpoints.dir: file:///flink/checkpoints
state.savepoints.dir: file:///flink/savepoints

# Rest Configuration
rest.address: 0.0.0.0
rest.port: 8081
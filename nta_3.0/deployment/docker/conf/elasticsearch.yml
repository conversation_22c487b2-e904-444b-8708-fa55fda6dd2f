cluster.name: nta-elasticsearch
node.name: elasticsearch

# 网络设置
network.host: 0.0.0.0
http.port: 9200

# 数据和日志路径
path.data: /opt/bitnami/elasticsearch/data
path.logs: /opt/bitnami/elasticsearch/logs

# 内存设置
bootstrap.memory_lock: true

# 安全设置
xpack.security.enabled: false

# 发现设置
discovery.type: single-node

# 跨域设置
http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-methods: OPTIONS, HEAD, GET, POST, PUT, DELETE
http.cors.allow-headers: X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization

# 索引设置
action.auto_create_index: true

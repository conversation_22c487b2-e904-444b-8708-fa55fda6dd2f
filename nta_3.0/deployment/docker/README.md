# NTA Platform Docker 部署指南

本文档提供了使用 Docker Compose 部署 NTA 平台的指南。

## 目录结构

```bash
docker/
├── docker-compose.yml     # 生产环境配置
├── docker-compose.dev.yml # 开发环境配置
├── .env                   # 环境变量配置
├── .env.example           # 环境变量配置示例
├── conf/                  # 配置文件目录
│   ├── elasticsearch.yml  # Elasticsearch 配置
│   ├── my_custom.cnf      # MySQL 配置
│   ├── nginx.conf         # Nginx 配置
│   ├── fe.conf            # Doris FE 配置
│   ├── be.conf            # Doris BE 配置
│   ├── flink-conf.yaml    # Flink 配置
│   └── ssl/               # SSL 证书目录
├── prometheus/            # 监控配置
│   ├── alertmanager.yml   # AlertManager 配置
│   ├── prometheus.yml     # Prometheus 配置
│   ├── rules/             # 告警规则目录
│   └── grafana/           # Grafana 配置目录
└── scripts/               # 部署脚本
```

## 部署前提条件

- Docker Engine 20.10.0+
- Docker Compose 2.0.0+
- 至少 16GB 内存
- 至少 100GB 磁盘空间

## 快速开始

### 1. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的环境变量
```

### 2. 创建数据目录

```bash
mkdir -p /data/nta/{redis,mysql,elasticsearch/data,kafka,nebula/{data/{meta,storage},logs/{meta,storage,graph}},doris/{fe,be}}
```

### 3. 启动服务

#### 生产环境

```bash
docker-compose up -d
```

#### 开发环境

```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

## 服务说明

### 基础设施服务

| 服务 | 端口 | 说明 |
|------|------|------|
| Redis | 6379 | 缓存和会话管理 |
| MySQL | 3306 | 关系型数据库 |
| Elasticsearch | 9200 | 全文搜索和日志分析 |
| Kafka | 9092 | 消息流处理（KRaft 模式） |
| Nebula Graph | 9669, 9559, 9779 | 图数据库 |
| Apache Doris | 8030, 9030, 8040 | OLAP 分析和数据仓库 |

### 微服务

| 服务 | 端口 | 说明 |
|------|------|------|
| auth-service | 8081 | 认证授权服务 |
| analysis-service | 8082 | 分析服务 |
| graph-service | 8083 | 图数据服务 |
| search-service | 8085 | 搜索服务 |
| notification-service | 8086 | 通知服务 |
| task-service | 8087 | 任务管理服务 |
| config-service | 8088 | 配置管理服务 |
| system-service | 8089 | 系统管理服务 |

### 监控服务

| 服务 | 端口 | 说明 |
|------|------|------|
| Prometheus | 9090 | 监控系统 |
| Grafana | 3000 | 可视化仪表盘 |
| AlertManager | 9093 | 告警管理 |
| Node Exporter | 9100 | 主机监控 |
| Redis Exporter | 9121 | Redis 监控 |
| MySQL Exporter | 9104 | MySQL 监控 |
| Doris Exporter | 9177 | Doris 监控 |

## 维护指南

### 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service-name]

# 重启服务
docker-compose restart [service-name]

# 停止服务
docker-compose down
```

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查日志: `docker-compose logs [service-name]`
   - 检查端口冲突
   - 检查资源限制

2. **数据库连接问题**
   - 检查数据库服务状态
   - 验证连接配置
   - 检查网络连接

3. **性能问题**
   - 检查资源使用情况: `docker stats`
   - 调整资源限制
   - 优化配置参数

### 数据备份

```bash
# MySQL 备份
docker-compose exec mysql mysqldump -u root -p[password] [database] > backup.sql

# Elasticsearch 备份
curl -X PUT "localhost:9200/_snapshot/my_backup/snapshot_1?wait_for_completion=true"

# Doris 备份
# 备份元数据
docker-compose exec doris-fe /opt/apache-doris/fe/bin/backup_meta.sh
# 备份数据
docker-compose exec doris-be /opt/apache-doris/be/bin/backup_data.sh
```

## 监控和安全

### 监控访问

- Prometheus: `http://localhost:9090`
- Grafana: `http://localhost:3000` (默认账号: admin/admin)

### 安全建议

1. 修改默认密码
2. 配置 SSL 证书
3. 限制网络访问
4. 定期更新镜像
5. 监控异常行为

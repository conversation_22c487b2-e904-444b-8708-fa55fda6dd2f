#!/bin/bash
# 启动 NTA Platform 服务

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -d, --dev                  使用开发环境配置"
    echo "  -i, --init                 初始化数据目录"
    echo "  -h, --help                 显示帮助信息"
    exit 0
}

# 默认值
DEV_MODE=false
INIT=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case "$1" in
        -d|--dev)
            DEV_MODE=true
            shift
            ;;
        -i|--init)
            INIT=true
            shift
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            ;;
    esac
done

# 加载环境变量
source "${DOCKER_DIR}/.env"

# 初始化数据目录
if [ "${INIT}" = "true" ]; then
    echo "=== 初始化数据目录 ==="
    mkdir -p ${PERMANENT_DATA_PATH}/redis
    mkdir -p ${PERMANENT_DATA_PATH}/mysql
    mkdir -p ${REMOVABLE_DATA_PATH}/elasticsearch/data
    mkdir -p ${PERMANENT_DATA_PATH}/zookeeper
    mkdir -p ${REMOVABLE_DATA_PATH}/kafka
    mkdir -p ${REMOVABLE_DATA_PATH}/nebula/data/meta
    mkdir -p ${REMOVABLE_DATA_PATH}/nebula/data/storage
    mkdir -p ${REMOVABLE_DATA_PATH}/nebula/logs/meta
    mkdir -p ${REMOVABLE_DATA_PATH}/nebula/logs/storage
    mkdir -p ${REMOVABLE_DATA_PATH}/nebula/logs/graph
    
    # 设置目录权限
    chmod -R 777 ${PERMANENT_DATA_PATH}
    chmod -R 777 ${REMOVABLE_DATA_PATH}
    
    echo "数据目录初始化完成"
    echo
fi

# 启动服务
cd "${DOCKER_DIR}"

if [ "${DEV_MODE}" = "true" ]; then
    echo "=== 以开发模式启动 NTA Platform ==="
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
else
    echo "=== 以生产模式启动 NTA Platform ==="
    docker-compose up -d
fi

echo "=== NTA Platform 启动完成 ==="

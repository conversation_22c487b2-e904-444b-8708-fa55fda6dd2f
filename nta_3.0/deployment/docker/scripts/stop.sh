#!/bin/bash
# 停止 NTA Platform 服务

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -d, --dev                  使用开发环境配置"
    echo "  -c, --clean                停止后清理容器和网络"
    echo "  -h, --help                 显示帮助信息"
    exit 0
}

# 默认值
DEV_MODE=false
CLEAN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case "$1" in
        -d|--dev)
            DEV_MODE=true
            shift
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            ;;
    esac
done

# 停止服务
cd "${DOCKER_DIR}"

if [ "${DEV_MODE}" = "true" ]; then
    echo "=== 停止开发模式的 NTA Platform ==="
    if [ "${CLEAN}" = "true" ]; then
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
    else
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml stop
    fi
else
    echo "=== 停止生产模式的 NTA Platform ==="
    if [ "${CLEAN}" = "true" ]; then
        docker-compose down
    else
        docker-compose stop
    fi
fi

echo "=== NTA Platform 已停止 ==="

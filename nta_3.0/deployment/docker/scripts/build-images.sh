#!/bin/bash
# 构建所有微服务的 Docker 镜像

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
source "${SCRIPT_DIR}/../.env"

# 设置默认值
REGISTRY=${REGISTRY:-hb.gs.lan}
TAG=${TAG:-latest}
PUSH=${PUSH:-false}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -r, --registry REGISTRY    Docker 镜像仓库地址 (默认: ${REGISTRY})"
    echo "  -t, --tag TAG              Docker 镜像标签 (默认: ${TAG})"
    echo "  -p, --push                 构建后推送镜像到仓库"
    echo "  -h, --help                 显示帮助信息"
    exit 0
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case "$1" in
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "未知选项: $1"
            show_help
            ;;
    esac
done

echo "=== 构建 NTA Platform 微服务镜像 ==="
echo "Registry: ${REGISTRY}"
echo "Tag: ${TAG}"
echo "Push: ${PUSH}"
echo

# 构建微服务镜像
build_service() {
    local service=$1
    echo "=== 构建 ${service} ==="
    cd "${PROJECT_ROOT}/services/${service}"

    # 使用 Maven 构建 JAR 包并构建 Docker 镜像
    if [ "${PUSH}" = "true" ]; then
        mvn clean package docker:build docker:push -Ddocker.registry=${REGISTRY} -Ddocker.image.prefix=nta -Dproject.version=${TAG}
    else
        mvn clean package docker:build -Ddocker.registry=${REGISTRY} -Ddocker.image.prefix=nta -Dproject.version=${TAG}
    fi

    echo "=== ${service} 构建完成 ==="
    echo
}

# 构建所有微服务
services=(
    "auth-service"
    "analysis-service"
    "graph-service"
    "search-service"
    "notification-service"
    "task-service"
    "config-service"
    "system-service"
    "security-service"
)

for service in "${services[@]}"; do
    build_service "${service}"
done

# 构建前端
echo "=== 构建前端 ==="
cd "${PROJECT_ROOT}/frontend"

# 检查前端目录是否存在
if [ -d "${PROJECT_ROOT}/frontend" ]; then
    docker build -t "${REGISTRY}/nta/frontend:${TAG}" .

    if [ "${PUSH}" = "true" ]; then
        echo "推送前端镜像到 ${REGISTRY}..."
        docker push "${REGISTRY}/nta/frontend:${TAG}"
    fi

    echo "=== 前端构建完成 ==="
else
    echo "=== 前端目录不存在，跳过构建 ==="
fi
echo

echo "=== 所有镜像构建完成 ==="
if [ "${PUSH}" = "true" ]; then
    echo "所有镜像已推送到 ${REGISTRY}"
fi

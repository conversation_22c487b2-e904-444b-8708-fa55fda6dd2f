package com.geeksec.common.concurrent;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池工具类
 * <p>
 * 提供创建和管理线程池的通用方法，支持自定义线程池配置和命名线程工厂。
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public final class ThreadPoolUtils {

    private ThreadPoolUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 创建任务处理线程池
     * 
     * @param poolName 线程池名称
     * @return 线程池执行器
     */
    public static ThreadPoolExecutor createTaskProcessingPool(String poolName) {
        return new ThreadPoolExecutor(
                20,
                50,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                new NamedThreadFactory(poolName)
        );
    }

    /**
     * 创建自定义线程池
     * 
     * @param corePoolSize 核心线程数
     * @param maximumPoolSize 最大线程数
     * @param keepAliveTime 空闲时间
     * @param unit 时间单位
     * @param poolName 线程池名称
     * @return 线程池执行器
     */
    public static ThreadPoolExecutor createCustomPool(int corePoolSize, 
                                                     int maximumPoolSize, 
                                                     long keepAliveTime, 
                                                     TimeUnit unit, 
                                                     String poolName) {
        return new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime,
                unit,
                new LinkedBlockingQueue<>(),
                new NamedThreadFactory(poolName)
        );
    }

    /**
     * 命名线程工厂
     */
    private static class NamedThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        NamedThreadFactory(String poolName) {
            this.namePrefix = poolName + "-thread-";
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            if (t.isDaemon()) {
                t.setDaemon(false);
            }
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
}

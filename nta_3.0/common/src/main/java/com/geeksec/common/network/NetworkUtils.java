package com.geeksec.common.infrastructure.network;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 网络基础设施工具类
 *
 * 专注于网络基础概念：IP地址、端口、主机名、网络连通性等
 * 不依赖重型外部库，保持轻量级
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
public final class NetworkUtils {

    private NetworkUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 检查字符串是否为有效的IPv4地址
     *
     * @param ip IP地址字符串
     * @return 如果是有效的IPv4地址则返回true
     */
    public static boolean isValidIpv4(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr instanceof Inet4Address && ip.equals(addr.getHostAddress());
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为有效的IPv6地址
     *
     * @param ip IP地址字符串
     * @return 如果是有效的IPv6地址则返回true
     */
    public static boolean isValidIpv6(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr instanceof Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为有效的IP地址（IPv4或IPv6）
     *
     * @param ip IP地址字符串
     * @return 如果是有效的IP地址则返回true
     */
    public static boolean isValidIp(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            // 确保输入的字符串确实是IP地址格式，而不是主机名
            // 对于IPv4，直接比较字符串；对于IPv6，InetAddress会规范化格式
            return ip.equals(addr.getHostAddress()) || addr instanceof Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检查IP地址是否为私有地址
     *
     * @param ip IP地址字符串
     * @return 如果是私有地址则返回true
     */
    public static boolean isPrivateIp(String ip) {
        if (!isValidIpv4(ip)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr.isSiteLocalAddress();
        } catch (UnknownHostException e) {
            log.warn("解析IP地址失败: {}", ip, e);
            return false;
        }
    }

    /**
     * 检查IP地址是否为回环地址
     *
     * @param ip IP地址字符串
     * @return 如果是回环地址则返回true
     */
    public static boolean isLoopbackIp(String ip) {
        if (!isValidIp(ip)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr.isLoopbackAddress();
        } catch (UnknownHostException e) {
            log.warn("解析IP地址失败: {}", ip, e);
            return false;
        }
    }

    /**
     * 检查端口号是否有效
     *
     * @param port 端口号
     * @return 如果端口号有效则返回true
     */
    public static boolean isValidPort(int port) {
        return port >= 1 && port <= 65535;
    }

    /**
     * 检查端口号字符串是否有效
     *
     * @param portStr 端口号字符串
     * @return 如果端口号有效则返回true
     */
    public static boolean isValidPort(String portStr) {
        if (StringUtils.isEmpty(portStr)) {
            return false;
        }
        try {
            int port = Integer.parseInt(portStr);
            return isValidPort(port);
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 获取本机IP地址
     *
     * @return 本机IP地址，获取失败返回null
     */
    public static String getLocalIp() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("获取本机IP地址失败", e);
            return null;
        }
    }

    /**
     * 获取本机主机名
     *
     * @return 本机主机名，获取失败返回null
     */
    public static String getLocalHostName() {
        try {
            InetAddress addr = InetAddress.getLocalHost();
            return addr.getHostName();
        } catch (UnknownHostException e) {
            log.warn("获取本机主机名失败", e);
            return null;
        }
    }

    /**
     * 检查主机是否可达
     *
     * @param host    主机地址
     * @param timeout 超时时间（毫秒）
     * @return 如果可达则返回true
     */
    public static boolean isReachable(String host, int timeout) {
        if (StringUtils.isEmpty(host)) {
            return false;
        }

        try {
            InetAddress addr = InetAddress.getByName(host);
            return addr.isReachable(timeout);
        } catch (Exception e) {
            log.warn("检查主机可达性失败: {}", host, e);
            return false;
        }
    }

    /**
     * 解析主机名到IP地址
     *
     * @param hostname 主机名
     * @return IP地址，解析失败返回null
     */
    public static String resolveHostname(String hostname) {
        if (StringUtils.isEmpty(hostname)) {
            return null;
        }

        try {
            InetAddress addr = InetAddress.getByName(hostname);
            return addr.getHostAddress();
        } catch (UnknownHostException e) {
            log.warn("解析主机名失败: {}", hostname, e);
            return null;
        }
    }

    /**
     * 反向解析IP地址到主机名
     *
     * @param ip IP地址
     * @return 主机名，解析失败返回null
     */
    public static String reverseResolve(String ip) {
        if (!isValidIp(ip)) {
            return null;
        }

        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr.getHostName();
        } catch (UnknownHostException e) {
            log.warn("反向解析IP地址失败: {}", ip, e);
            return null;
        }
    }

    /**
     * 检查URL是否有效
     *
     * @param url URL字符串
     * @return 如果URL有效则返回true
     */
    public static boolean isValidUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }

        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            return false;
        }
    }

    /**
     * 从URL中提取主机名
     *
     * @param url URL字符串
     * @return 主机名，提取失败返回null
     */
    public static String extractHostFromUrl(String url) {
        if (!isValidUrl(url)) {
            return null;
        }

        try {
            URL urlObj = new URL(url);
            return urlObj.getHost();
        } catch (MalformedURLException e) {
            log.warn("从URL提取主机名失败: {}", url, e);
            return null;
        }
    }

    /**
     * 从URL中提取端口号
     *
     * @param url URL字符串
     * @return 端口号，如果URL中没有指定端口则返回-1，提取失败返回-1
     */
    public static int extractPortFromUrl(String url) {
        if (!isValidUrl(url)) {
            return -1;
        }

        try {
            URL urlObj = new URL(url);
            return urlObj.getPort();
        } catch (MalformedURLException e) {
            log.warn("从URL提取端口号失败: {}", url, e);
            return -1;
        }
    }
}

package com.geeksec.common.constants;

/**
 * 网络相关常量类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class NetworkConstants {

    private NetworkConstants() {
        // 工具类，禁止实例化
    }

    /** 本地回环地址 */
    public static final String LOCALHOST = "127.0.0.1";
    
    /** IPv6本地回环地址 */
    public static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";
    
    /** 默认端口 */
    public static final int DEFAULT_PORT = 8080;
    
    /** HTTP默认端口 */
    public static final int HTTP_PORT = 80;
    
    /** HTTPS默认端口 */
    public static final int HTTPS_PORT = 443;
}

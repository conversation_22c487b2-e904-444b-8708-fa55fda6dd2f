package com.geeksec.common.utils.crypto;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * 哈希工具类
 * 
 * 支持MD5、SHA-1、SHA-256等多种哈希算法
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class HashUtils {

    private HashUtils() {
        // 工具类，禁止实例化
    }

    /** 十六进制字符 */
    private static final char[] HEX_CHARS = "0123456789abcdef".toCharArray();

    /** 默认盐值长度 */
    private static final int DEFAULT_SALT_LENGTH = 16;

    /**
     * 计算字符串的MD5哈希值
     *
     * @param input 输入字符串
     * @return MD5哈希值（小写十六进制）
     */
    public static String md5(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        return md5(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 计算字节数组的MD5哈希值
     *
     * @param input 输入字节数组
     * @return MD5哈希值（小写十六进制）
     */
    public static String md5(byte[] input) {
        return hash(input, "MD5");
    }

    /**
     * 计算带盐值的MD5哈希值
     *
     * @param input 输入字符串
     * @param salt 盐值
     * @return MD5哈希值（小写十六进制）
     */
    public static String md5WithSalt(String input, String salt) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        return md5(input + salt);
    }

    /**
     * 验证MD5哈希值
     *
     * @param input 原始输入
     * @param hash 待验证的哈希值
     * @return 是否匹配
     */
    public static boolean verifyMd5(String input, String hash) {
        return StringUtils.equals(md5(input), hash);
    }

    /**
     * 验证带盐值的MD5哈希值
     *
     * @param input 原始输入
     * @param salt 盐值
     * @param hash 待验证的哈希值
     * @return 是否匹配
     */
    public static boolean verifyMd5WithSalt(String input, String salt, String hash) {
        return StringUtils.equals(md5WithSalt(input, salt), hash);
    }

    /**
     * 计算字符串的SHA-1哈希值
     *
     * @param input 输入字符串
     * @return SHA-1哈希值（小写十六进制）
     */
    public static String sha1(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        return sha1(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 计算字节数组的SHA-1哈希值
     *
     * @param input 输入字节数组
     * @return SHA-1哈希值（小写十六进制）
     */
    public static String sha1(byte[] input) {
        return hash(input, "SHA-1");
    }

    /**
     * 计算字符串的SHA-256哈希值
     *
     * @param input 输入字符串
     * @return SHA-256哈希值（小写十六进制）
     */
    public static String sha256(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        return sha256(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 计算字节数组的SHA-256哈希值
     *
     * @param input 输入字节数组
     * @return SHA-256哈希值（小写十六进制）
     */
    public static String sha256(byte[] input) {
        return hash(input, "SHA-256");
    }

    /**
     * 计算字符串的SHA-512哈希值
     *
     * @param input 输入字符串
     * @return SHA-512哈希值（小写十六进制）
     */
    public static String sha512(String input) {
        if (StringUtils.isEmpty(input)) {
            return "";
        }
        return sha512(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 计算字节数组的SHA-512哈希值
     *
     * @param input 输入字节数组
     * @return SHA-512哈希值（小写十六进制）
     */
    public static String sha512(byte[] input) {
        return hash(input, "SHA-512");
    }

    /**
     * 通用哈希计算方法
     *
     * @param input 输入字节数组
     * @param algorithm 哈希算法名称
     * @return 哈希值（小写十六进制）
     */
    public static String hash(byte[] input, String algorithm) {
        if (input == null || input.length == 0) {
            return "";
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            byte[] hashBytes = digest.digest(input);
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("不支持的哈希算法: {}", algorithm, e);
            throw new RuntimeException("不支持的哈希算法: " + algorithm, e);
        }
    }

    /**
     * 生成随机盐值
     *
     * @return 随机盐值（十六进制字符串）
     */
    public static String generateSalt() {
        return generateSalt(DEFAULT_SALT_LENGTH);
    }

    /**
     * 生成指定长度的随机盐值
     *
     * @param length 盐值长度（字节数）
     * @return 随机盐值（十六进制字符串）
     */
    public static String generateSalt(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("盐值长度必须大于0");
        }

        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[length];
        random.nextBytes(salt);
        return bytesToHex(salt);
    }

    /**
     * 将字节数组转换为十六进制字符串
     *
     * @param bytes 字节数组
     * @return 十六进制字符串（小写）
     */
    public static String bytesToHex(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return "";
        }

        char[] hexChars = new char[bytes.length * 2];
        for (int i = 0; i < bytes.length; i++) {
            int v = bytes[i] & 0xFF;
            hexChars[i * 2] = HEX_CHARS[v >>> 4];
            hexChars[i * 2 + 1] = HEX_CHARS[v & 0x0F];
        }
        return new String(hexChars);
    }

    /**
     * 将十六进制字符串转换为字节数组
     *
     * @param hex 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexToBytes(String hex) {
        if (StringUtils.isEmpty(hex)) {
            return new byte[0];
        }

        if (hex.length() % 2 != 0) {
            throw new IllegalArgumentException("十六进制字符串长度必须为偶数");
        }

        byte[] bytes = new byte[hex.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int index = i * 2;
            bytes[i] = (byte) Integer.parseInt(hex.substring(index, index + 2), 16);
        }
        return bytes;
    }

    /**
     * 快速MD5计算（用于兼容原有代码）
     *
     * @param input 输入字符串
     * @return MD5哈希值
     */
    public static String quickMd5(String input) {
        return md5(input);
    }

    /**
     * 文件内容哈希计算（通过字节数组）
     *
     * @param fileBytes 文件字节数组
     * @param algorithm 哈希算法
     * @return 文件哈希值
     */
    public static String hashFile(byte[] fileBytes, String algorithm) {
        return hash(fileBytes, algorithm);
    }
}

package com.geeksec.common.storage.minio;

import io.minio.BucketExistsArgs;
import io.minio.DownloadObjectArgs;
import io.minio.GetObjectArgs;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.StatObjectArgs;
import io.minio.UploadObjectArgs;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * MinIO 工具类
 * 提供 MinIO 对象存储的基础操作
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class MinioUtils {

    /**
     * 配置属性
     */
    private static Properties config = new Properties();

    /**
     * MinIO客户端实例
     */
    private static volatile MinioClient minioClient;

    /**
     * 配置键常量
     */
    private static final String MINIO_ENDPOINT = "minio.endpoint";
    private static final String MINIO_ACCESS_KEY = "minio.accessKey";
    private static final String MINIO_SECRET_KEY = "minio.secretKey";
    private static final String MINIO_REGION = "minio.region";

    /**
     * 默认配置值
     */
    private static final String DEFAULT_ENDPOINT = "http://localhost:9000";
    private static final String DEFAULT_REGION = "us-east-1";

    private MinioUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 初始化配置
     *
     * @param properties 配置属性
     */
    public static void initConfig(Properties properties) {
        config = properties;
        // 重置客户端，使用新配置
        minioClient = null;
    }

    /**
     * 获取MinIO客户端实例（单例模式）
     *
     * @return MinioClient实例
     * @throws RuntimeException 如果配置不正确或连接失败
     */
    private static MinioClient getMinioClient() {
        if (minioClient == null) {
            synchronized (MinioUtils.class) {
                if (minioClient == null) {
                    try {
                        String endpoint = config.getProperty(MINIO_ENDPOINT, DEFAULT_ENDPOINT);
                        String accessKey = config.getProperty(MINIO_ACCESS_KEY);
                        String secretKey = config.getProperty(MINIO_SECRET_KEY);
                        String region = config.getProperty(MINIO_REGION, DEFAULT_REGION);

                        if (accessKey == null || secretKey == null) {
                            throw new IllegalArgumentException("MinIO访问密钥和秘密密钥不能为空");
                        }

                        minioClient = MinioClient.builder()
                                .endpoint(endpoint)
                                .credentials(accessKey, secretKey)
                                .region(region)
                                .build();

                        log.info("MinIO客户端初始化成功: {}", endpoint);
                    } catch (Exception e) {
                        log.error("MinIO客户端初始化失败", e);
                        throw new RuntimeException("MinIO客户端初始化失败", e);
                    }
                }
            }
        }
        return minioClient;
    }

    /**
     * 测试连接
     *
     * @return 连接是否成功
     */
    public static boolean testConnection() {
        try {
            MinioClient client = getMinioClient();
            // 尝试列出存储桶来测试连接
            client.listBuckets();
            log.info("MinIO连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("MinIO连接测试失败", e);
            return false;
        }
    }

    /**
     * 检查存储桶是否存在
     *
     * @param bucketName 存储桶名称
     * @return 是否存在
     */
    public static boolean bucketExists(String bucketName) {
        try {
            MinioClient client = getMinioClient();
            boolean exists = client.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());
            log.debug("检查存储桶是否存在: {}, 结果: {}", bucketName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查存储桶失败: {}", bucketName, e);
            return false;
        }
    }

    /**
     * 创建存储桶
     *
     * @param bucketName 存储桶名称
     * @return 是否成功
     */
    public static boolean createBucket(String bucketName) {
        try {
            MinioClient client = getMinioClient();

            // 先检查存储桶是否已存在
            if (bucketExists(bucketName)) {
                log.info("存储桶已存在: {}", bucketName);
                return true;
            }

            // 创建存储桶
            client.makeBucket(MakeBucketArgs.builder()
                    .bucket(bucketName)
                    .build());

            log.info("创建存储桶成功: {}", bucketName);
            return true;
        } catch (Exception e) {
            log.error("创建存储桶失败: {}", bucketName, e);
            return false;
        }
    }

    /**
     * 上传文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param filePath  文件路径
     * @return 是否成功
     */
    public static boolean uploadFile(String bucketName, String objectName, String filePath) {
        try {
            MinioClient client = getMinioClient();

            // 确保存储桶存在
            if (!bucketExists(bucketName)) {
                if (!createBucket(bucketName)) {
                    log.error("无法创建存储桶: {}", bucketName);
                    return false;
                }
            }

            // 上传文件
            client.uploadObject(UploadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .filename(filePath)
                    .build());

            log.info("上传文件成功: bucket={}, object={}, file={}", bucketName, objectName, filePath);
            return true;
        } catch (Exception e) {
            log.error("上传文件失败: bucket={}, object={}, file={}", bucketName, objectName, filePath, e);
            return false;
        }
    }

    /**
     * 下载文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param filePath  下载路径
     * @return 是否成功
     */
    public static boolean downloadFile(String bucketName, String objectName, String filePath) {
        try {
            MinioClient client = getMinioClient();

            // 检查对象是否存在
            if (!fileExists(bucketName, objectName)) {
                log.warn("对象不存在: bucket={}, object={}", bucketName, objectName);
                return false;
            }

            // 下载文件
            client.downloadObject(DownloadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .filename(filePath)
                    .build());

            log.info("下载文件成功: bucket={}, object={}, file={}", bucketName, objectName, filePath);
            return true;
        } catch (Exception e) {
            log.error("下载文件失败: bucket={}, object={}, file={}", bucketName, objectName, filePath, e);
            return false;
        }
    }

    /**
     * 删除文件
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否成功
     */
    public static boolean deleteFile(String bucketName, String objectName) {
        try {
            MinioClient client = getMinioClient();

            // 删除对象
            client.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());

            log.info("删除文件成功: bucket={}, object={}", bucketName, objectName);
            return true;
        } catch (Exception e) {
            log.error("删除文件失败: bucket={}, object={}", bucketName, objectName, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 是否存在
     */
    public static boolean fileExists(String bucketName, String objectName) {
        try {
            MinioClient client = getMinioClient();

            // 通过获取对象状态来检查文件是否存在
            client.statObject(StatObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());

            log.debug("文件存在: bucket={}, object={}", bucketName, objectName);
            return true;
        } catch (Exception e) {
            // 如果对象不存在，statObject会抛出异常
            log.debug("文件不存在: bucket={}, object={}", bucketName, objectName);
            return false;
        }
    }

    /**
     * 获取文件URL
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件URL
     */
    public static String getFileUrl(String bucketName, String objectName) {
        try {
            MinioClient client = getMinioClient();

            // 生成预签名URL，有效期7天
            String url = client.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .method(Method.GET)
                    .bucket(bucketName)
                    .object(objectName)
                    .expiry(7, TimeUnit.DAYS)
                    .build());

            log.debug("获取文件URL成功: bucket={}, object={}, url={}", bucketName, objectName, url);
            return url;
        } catch (Exception e) {
            log.error("获取文件URL失败: bucket={}, object={}", bucketName, objectName, e);
            return null;
        }
    }

    /**
     * 获取文件流
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 文件输入流
     */
    public static InputStream getFileStream(String bucketName, String objectName) {
        try {
            MinioClient client = getMinioClient();

            // 获取对象输入流
            InputStream stream = client.getObject(GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .build());

            log.debug("获取文件流成功: bucket={}, object={}", bucketName, objectName);
            return stream;
        } catch (Exception e) {
            log.error("获取文件流失败: bucket={}, object={}", bucketName, objectName, e);
            return null;
        }
    }
}

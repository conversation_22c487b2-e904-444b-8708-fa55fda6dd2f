package com.geeksec.common.utils.math;

import java.math.BigDecimal;

/**
 * 通用数学计算工具类
 * <p>
 * 提供数论相关的计算方法，如最大公约数、最小公倍数和质数判断。
 * 此类遵循单一职责原则，专注于通用的数学计算。
 *
 * <AUTHOR>
 */
public final class MathUtils {

    private MathUtils() {
        throw new AssertionError("工具类不应被实例化");
    }

    /**
     * 判断一个数字是否为 null 或零。
     *
     * @param number 要检查的数字
     * @return 如果为 null 或零则返回 true
     */
    public static boolean isZeroOrNull(Number number) {
        if (number == null) {
            return true;
        }
        return BigDecimal.ZERO.compareTo(new BigDecimal(number.toString())) == 0;
    }

    /**
     * 检查一个数字是否在指定的闭区间 [min, max] 内。
     *
     * @param number 要检查的数字
     * @param min    区间的最小值
     * @param max    区间的最大值
     * @return 如果在区间内则返回 true
     */
    public static boolean isBetween(Number number, Number min, Number max) {
        if (number == null || min == null || max == null) {
            return false;
        }
        BigDecimal val = new BigDecimal(number.toString());
        BigDecimal minVal = new BigDecimal(min.toString());
        BigDecimal maxVal = new BigDecimal(max.toString());
        return val.compareTo(minVal) >= 0 && val.compareTo(maxVal) <= 0;
    }

    /**
     * 使用欧几里得算法计算两个整数的最大公约数 (GCD)。
     *
     * @param a 第一个整数
     * @param b 第二个整数
     * @return a 和 b 的最大公约数
     */
    public static int gcd(int a, int b) {
        while (b != 0) {
            int temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }

    /**
     * 计算两个整数的最小公倍数 (LCM)。
     *
     * @param a 第一个整数
     * @param b 第二个整数
     * @return a 和 b 的最小公倍数
     */
    public static int lcm(int a, int b) {
        if (a == 0 || b == 0) {
            return 0;
        }
        return Math.abs(a * b) / gcd(a, b);
    }

    /**
     * 判断一个整数是否为质数。
     *
     * @param n 要判断的整数
     * @return 如果是质数则返回 true
     */
    public static boolean isPrime(int n) {
        if (n <= 1) return false;
        if (n <= 3) return true;
        if (n % 2 == 0 || n % 3 == 0) return false;
        for (int i = 5; i * i <= n; i = i + 6) {
            if (n % i == 0 || n % (i + 2) == 0) {
                return false;
            }
        }
        return true;
    }
}

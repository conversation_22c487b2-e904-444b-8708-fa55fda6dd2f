package com.geeksec.common.database.nebula;

import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.exception.AuthFailedException;
import com.vesoft.nebula.client.graph.exception.ClientServerIncompatibleException;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.exception.NotValidConnectionException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;

import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * Nebula连接管理器
 * 提供Nebula图数据库连接和操作功能
 *
 * <p>支持的配置项：</p>
 * <ul>
 *   <li>nebula.hosts - 服务器地址列表，格式：host1:port1,host2:port2</li>
 *   <li>nebula.username - 用户名，默认：root</li>
 *   <li>nebula.password - 密码，默认：nebula</li>
 *   <li>nebula.space - 图空间名称，默认：nta</li>
 *   <li>nebula.pool.min.size - 连接池最小连接数，默认：0</li>
 *   <li>nebula.pool.max.size - 连接池最大连接数，默认：10</li>
 *   <li>nebula.pool.timeout - 连接超时时间（毫秒），默认：0（无超时）</li>
 *   <li>nebula.pool.idle.time - 空闲时间（毫秒），默认：0（无限制）</li>
 *   <li>nebula.pool.interval.idle - 空闲检查间隔（毫秒），默认：-1（不检查）</li>
 *   <li>nebula.pool.wait.time - 等待时间（毫秒），默认：0（无等待）</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Slf4j
public class NebulaConnectionManager {

    /**
     * 配置属性
     */
    private static Properties config = new Properties();

    /**
     * Nebula连接池
     */
    private static NebulaPool nebulaPool = null;

    /**
     * 私有构造函数，防止实例化
     */
    private NebulaConnectionManager() {
        // 工具类，不允许实例化
    }

    /**
     * 初始化配置
     *
     * @param properties 配置属性
     */
    public static void initConfig(Properties properties) {
        config = properties;
        // 重新初始化连接池
        closePool();
    }

    /**
     * 获取连接池配置
     *
     * @return NebulaPoolConfig 连接池配置
     */
    private static NebulaPoolConfig getPoolConfig() {
        NebulaPoolConfig poolConfig = new NebulaPoolConfig();

        // 设置连接池参数
        poolConfig.setMinConnSize(Integer.parseInt(config.getProperty("nebula.pool.min.size", "0")));
        poolConfig.setMaxConnSize(Integer.parseInt(config.getProperty("nebula.pool.max.size", "10")));
        poolConfig.setTimeout(Integer.parseInt(config.getProperty("nebula.pool.timeout", "0")));
        poolConfig.setIdleTime(Integer.parseInt(config.getProperty("nebula.pool.idle.time", "0")));
        poolConfig.setIntervalIdle(Integer.parseInt(config.getProperty("nebula.pool.interval.idle", "-1")));
        poolConfig.setWaitTime(Integer.parseInt(config.getProperty("nebula.pool.wait.time", "0")));

        return poolConfig;
    }

    /**
     * 解析主机地址列表
     *
     * @return List<HostAddress> 主机地址列表
     */
    private static List<HostAddress> parseHostAddresses() {
        String hosts = config.getProperty("nebula.hosts", "127.0.0.1:9669");
        return Arrays.stream(hosts.split(","))
                .map(String::trim)
                .map(host -> {
                    String[] parts = host.split(":");
                    String hostname = parts[0];
                    int port = parts.length > 1 ? Integer.parseInt(parts[1]) : 9669;
                    return new HostAddress(hostname, port);
                })
                .toList();
    }

    /**
     * 初始化连接池
     *
     * @return NebulaPool 连接池实例
     * @throws UnknownHostException 主机地址解析异常
     * @throws ClientServerIncompatibleException 客户端服务器不兼容异常
     */
    public static synchronized NebulaPool initPool() throws UnknownHostException, ClientServerIncompatibleException {
        if (nebulaPool != null) {
            return nebulaPool;
        }

        try {
            List<HostAddress> addresses = parseHostAddresses();
            NebulaPoolConfig poolConfig = getPoolConfig();

            nebulaPool = new NebulaPool();
            boolean success = nebulaPool.init(addresses, poolConfig);

            if (success) {
                log.info("Nebula连接池初始化成功，地址：{}", addresses);
                return nebulaPool;
            } else {
                log.error("Nebula连接池初始化失败");
                throw new RuntimeException("Nebula连接池初始化失败");
            }
        } catch (Exception e) {
            log.error("初始化Nebula连接池时发生异常", e);
            throw e;
        }
    }

    /**
     * 获取连接池
     *
     * @return NebulaPool 连接池实例
     * @throws UnknownHostException 主机地址解析异常
     * @throws ClientServerIncompatibleException 客户端服务器不兼容异常
     */
    public static NebulaPool getPool() throws UnknownHostException, ClientServerIncompatibleException {
        if (nebulaPool == null) {
            return initPool();
        }
        return nebulaPool;
    }

    /**
     * 关闭连接池
     */
    public static synchronized void closePool() {
        if (nebulaPool != null) {
            try {
                nebulaPool.close();
                log.info("Nebula连接池已关闭");
            } catch (Exception e) {
                log.error("关闭Nebula连接池时发生异常", e);
            } finally {
                nebulaPool = null;
            }
        }
    }

    /**
     * 获取会话
     *
     * @return Session 会话实例
     * @throws NotValidConnectionException 连接无效异常
     * @throws IOErrorException IO异常
     * @throws AuthFailedException 认证失败异常
     * @throws UnknownHostException 主机地址解析异常
     * @throws ClientServerIncompatibleException 客户端服务器不兼容异常
     */
    public static Session getSession() throws NotValidConnectionException, IOErrorException, AuthFailedException,
            UnknownHostException, ClientServerIncompatibleException {
        NebulaPool pool = getPool();
        String username = config.getProperty("nebula.username", "root");
        String password = config.getProperty("nebula.password", "nebula");

        return pool.getSession(username, password, false);
    }

    /**
     * 获取会话并使用指定的图空间
     *
     * @return Session 会话实例
     * @throws NotValidConnectionException 连接无效异常
     * @throws IOErrorException IO异常
     * @throws AuthFailedException 认证失败异常
     * @throws UnknownHostException 主机地址解析异常
     * @throws ClientServerIncompatibleException 客户端服务器不兼容异常
     */
    public static Session getSessionWithSpace() throws NotValidConnectionException, IOErrorException, AuthFailedException,
            UnknownHostException, ClientServerIncompatibleException {
        Session session = getSession();
        String space = config.getProperty("nebula.space", "nta");

        try {
            ResultSet resultSet = session.execute("USE " + space);
            if (!resultSet.isSucceeded()) {
                log.error("切换到图空间 {} 失败: {}", space, resultSet.getErrorMessage());
                session.release();
                throw new RuntimeException("切换到图空间失败: " + resultSet.getErrorMessage());
            }
            log.debug("成功切换到图空间: {}", space);
            return session;
        } catch (Exception e) {
            session.release();
            throw e;
        }
    }

    /**
     * 执行nGQL查询
     *
     * @param nGQL nGQL查询语句
     * @return ResultSet 查询结果
     * @throws Exception 执行异常
     */
    public static ResultSet executeQuery(String nGQL) throws Exception {
        Session session = null;
        try {
            session = getSessionWithSpace();
            ResultSet resultSet = session.execute(nGQL);

            if (!resultSet.isSucceeded()) {
                log.error("执行nGQL查询失败: {}, 错误信息: {}", nGQL, resultSet.getErrorMessage());
                throw new RuntimeException("nGQL查询执行失败: " + resultSet.getErrorMessage());
            }

            log.debug("nGQL查询执行成功: {}", nGQL);
            return resultSet;
        } finally {
            if (session != null) {
                session.release();
            }
        }
    }

    /**
     * 测试连接
     *
     * @return 连接是否成功
     */
    public static boolean testConnection() {
        Session session = null;
        try {
            session = getSession();
            String space = config.getProperty("nebula.space", "nta");

            // 尝试切换到指定的图空间
            ResultSet resultSet = session.execute("USE " + space);
            if (!resultSet.isSucceeded()) {
                log.error("测试连接失败，无法切换到图空间 {}: {}", space, resultSet.getErrorMessage());
                return false;
            }

            // 执行简单查询验证连接
            resultSet = session.execute("SHOW SPACES");
            if (!resultSet.isSucceeded()) {
                log.error("测试连接失败，无法执行查询: {}", resultSet.getErrorMessage());
                return false;
            }

            log.info("Nebula连接测试成功");
            return true;
        } catch (Exception e) {
            log.error("Nebula连接测试失败", e);
            return false;
        } finally {
            if (session != null) {
                try {
                    session.release();
                } catch (Exception e) {
                    log.error("释放Nebula会话时发生异常", e);
                }
            }
        }
    }

    /**
     * 创建Flink Nebula图连接提供者
     * 用于Flink作业中的Nebula Sink
     *
     * @return NebulaGraphConnectionProvider 图连接提供者
     */
    public static NebulaGraphConnectionProvider createFlinkGraphConnectionProvider() {
        String graphAddr = config.getProperty("nebula.graph.addr", "127.0.0.1:9669");
        String metaAddr = config.getProperty("nebula.meta.addr", "127.0.0.1:9559");
        
        NebulaClientOptions options = new NebulaClientOptions.NebulaClientOptionsBuilder()
                .setGraphAddress(graphAddr)
                .setMetaAddress(metaAddr)
                .build();

        return new NebulaGraphConnectionProvider(options);
    }

    /**
     * 创建Flink Nebula元数据连接提供者
     * 用于Flink作业中的Nebula Sink
     *
     * @return NebulaMetaConnectionProvider 元数据连接提供者
     */
    public static NebulaMetaConnectionProvider createFlinkMetaConnectionProvider() {
        String metaAddr = config.getProperty("nebula.meta.addr", "127.0.0.1:9559");
        
        NebulaClientOptions options = new NebulaClientOptions.NebulaClientOptionsBuilder()
                .setMetaAddress(metaAddr)
                .build();

        return new NebulaMetaConnectionProvider(options);
    }
}

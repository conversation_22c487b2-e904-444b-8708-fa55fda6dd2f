package com.geeksec.common.exceptions;

import com.geeksec.common.enums.ErrorCode;

/**
 * 业务异常类
 * 
 * 用于处理业务逻辑相关的异常情况
 * 通常用于业务规则验证失败、数据状态异常等场景
 *
 * <AUTHOR>
 * @since 3.0.0
 */
public class BusinessException extends BaseException {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     * @param message    自定义错误消息
     */
    public BusinessException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     * @param cause      异常原因
     */
    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误状态码
     * @param message    自定义错误消息
     * @param cause      异常原因
     */
    public BusinessException(ErrorCode errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }
}

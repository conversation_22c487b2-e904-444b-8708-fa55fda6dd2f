package com.geeksec.common.constants;

/**
 * 验证相关常量类
 * 包含各种正则表达式常量
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class ValidationConstants {

    private ValidationConstants() {
        // 工具类，禁止实例化
    }

    /** 邮箱正则 */
    public static final String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    
    /** 手机号正则 */
    public static final String MOBILE = "^1[3-9]\\d{9}$";
    
    /** IPv4地址正则 */
    public static final String IPV4 = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";
    
    /** 域名正则 */
    public static final String DOMAIN = "^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$";
    
    /** URL正则 */
    public static final String URL = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";
    
    /** 身份证号正则 */
    public static final String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
}

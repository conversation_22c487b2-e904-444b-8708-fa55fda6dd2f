package com.geeksec.common.constants;

/**
 * 缓存相关常量类
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class CacheConstants {

    private CacheConstants() {
        // 工具类，禁止实例化
    }

    /** 默认缓存过期时间（秒） */
    public static final long DEFAULT_EXPIRE = 3600L;
    
    /** 短期缓存过期时间（5分钟） */
    public static final long SHORT_EXPIRE = 300L;
    
    /** 长期缓存过期时间（24小时） */
    public static final long LONG_EXPIRE = 86400L;
    
    /** 永不过期 */
    public static final long NEVER_EXPIRE = -1L;
}

package com.geeksec.common.database.postgresql;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * PostgreSQL数据库连接管理器
 * 提供PostgreSQL数据库连接和查询执行功能
 *
 * <AUTHOR>
 */
@Slf4j
public class PostgreSQLConnectionManager {

    /**
     * 配置属性
     */
    private static Properties config = new Properties();

    /**
     * 私有构造函数，防止实例化
     */
    private PostgreSQLConnectionManager() {
        // 工具类，不允许实例化
    }

    /**
     * 初始化配置
     *
     * @param properties 配置属性
     */
    public static void initConfig(Properties properties) {
        config = properties;
    }



    /**
     * 获取数据库连接
     *
     * @return 数据库连接
     * @throws SQLException SQL异常
     */
    public static Connection getConnection() throws SQLException {
        String database = config.getProperty("postgresql.database", "postgres");
        return getConnection(database);
    }



    /**
     * 获取指定数据库的连接
     *
     * @param database 数据库名称
     * @return 数据库连接
     * @throws SQLException SQL异常
     */
    public static Connection getConnection(String database) throws SQLException {
        String host = config.getProperty("postgresql.host", "localhost");
        String port = config.getProperty("postgresql.port", "5432");
        String username = config.getProperty("postgresql.username");
        String password = config.getProperty("postgresql.password");

        String url = String.format("jdbc:postgresql://%s:%s/%s", host, port, database);
        
        Properties props = new Properties();
        props.setProperty("user", username);
        props.setProperty("password", password);
        props.setProperty("ssl", "false");
        props.setProperty("connectTimeout", config.getProperty("postgresql.connection.timeout", "30"));

        return DriverManager.getConnection(url, props);
    }



    /**
     * 关闭连接（实现接口）
     *
     * @param connection 要关闭的连接
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                if (!connection.isClosed()) {
                    connection.close();
                    log.debug("PostgreSQL连接已关闭");
                }
            } catch (SQLException e) {
                log.warn("关闭PostgreSQL连接时发生错误", e);
            }
        }
    }

    /**
     * 执行查询语句
     *
     * @param sql 查询SQL语句
     * @return 查询结果列表
     */
    public static List<Map<String, Object>> executeQuery(String sql) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
            
        } catch (SQLException e) {
            log.error("执行查询失败: {}", sql, e);
        }
        
        return results;
    }

    /**
     * 执行带参数的查询语句
     *
     * @param sql    查询SQL语句
     * @param params 参数列表
     * @return 查询结果列表
     */
    public static List<Map<String, Object>> executeQuery(String sql, Object... params) {
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            
            try (ResultSet rs = pstmt.executeQuery()) {
                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                while (rs.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = rs.getObject(i);
                        row.put(columnName, value);
                    }
                    results.add(row);
                }
            }
            
        } catch (SQLException e) {
            log.error("执行带参数查询失败: {}", sql, e);
        }
        
        return results;
    }

    /**
     * 执行更新语句（INSERT、UPDATE、DELETE）
     *
     * @param sql 更新SQL语句
     * @return 影响的行数
     */
    public static int executeUpdate(String sql) {
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            
            return stmt.executeUpdate(sql);
            
        } catch (SQLException e) {
            log.error("执行更新失败: {}", sql, e);
            return -1;
        }
    }

    /**
     * 执行带参数的更新语句
     *
     * @param sql    更新SQL语句
     * @param params 参数列表
     * @return 影响的行数
     */
    public static int executeUpdate(String sql, Object... params) {
        try (Connection conn = getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            // 设置参数
            for (int i = 0; i < params.length; i++) {
                pstmt.setObject(i + 1, params[i]);
            }
            
            return pstmt.executeUpdate();
            
        } catch (SQLException e) {
            log.error("执行带参数更新失败: {}", sql, e);
            return -1;
        }
    }

    /**
     * 批量执行更新语句
     *
     * @param sqls SQL语句列表
     * @return 每个语句影响的行数数组
     */
    public static int[] executeBatch(List<String> sqls) {
        try (Connection conn = getConnection();
             Statement stmt = conn.createStatement()) {
            
            conn.setAutoCommit(false);
            
            for (String sql : sqls) {
                stmt.addBatch(sql);
            }
            
            int[] results = stmt.executeBatch();
            conn.commit();
            
            return results;
            
        } catch (SQLException e) {
            log.error("批量执行更新失败", e);
            return new int[0];
        }
    }

    /**
     * 测试数据库连接
     *
     * @return 连接是否成功
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            log.error("测试数据库连接失败", e);
            return false;
        }
    }

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 表是否存在
     */
    public static boolean tableExists(String tableName) {
        String sql = "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)";
        List<Map<String, Object>> results = executeQuery(sql, tableName);
        
        if (!results.isEmpty()) {
            Object exists = results.get(0).get("exists");
            return Boolean.TRUE.equals(exists);
        }
        
        return false;
    }

    /**
     * 获取表的列信息
     *
     * @param tableName 表名
     * @return 列信息列表
     */
    public static List<Map<String, Object>> getTableColumns(String tableName) {
        String sql = "SELECT column_name, data_type, is_nullable, column_default " +
                    "FROM information_schema.columns " +
                    "WHERE table_name = ? " +
                    "ORDER BY ordinal_position";
        
        return executeQuery(sql, tableName);
    }
}

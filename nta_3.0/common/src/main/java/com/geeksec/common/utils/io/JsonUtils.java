package com.geeksec.common.utils.io;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 
 * 基于Jackson提供JSON处理的通用方法
 * 包括对象序列化、反序列化、类型转换等功能
 *
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public final class JsonUtils {

    private JsonUtils() {
        // 工具类，禁止实例化
    }

    /** 全局ObjectMapper实例 */
    private static final ObjectMapper OBJECT_MAPPER = createObjectMapper();

    /**
     * 创建配置好的ObjectMapper实例
     * 
     * @return ObjectMapper实例
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册Java 8时间模块
        mapper.registerModule(new JavaTimeModule());
        
        // 配置序列化选项
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        
        // 配置反序列化选项
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        
        return mapper;
    }

    /**
     * 获取ObjectMapper实例
     * 
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    // ==================== 序列化方法 ====================

    /**
     * 将对象转换为JSON字符串
     *
     * @param object 要转换的对象
     * @return JSON字符串，如果转换失败则返回null
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON字符串失败: {}", object.getClass().getSimpleName(), e);
            return null;
        }
    }

    /**
     * 将对象转换为格式化的JSON字符串（美化输出）
     *
     * @param object 要转换的对象
     * @return 格式化的JSON字符串，如果转换失败则返回null
     */
    public static String toPrettyJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象转格式化JSON字符串失败: {}", object.getClass().getSimpleName(), e);
            return null;
        }
    }

    /**
     * 将对象转换为字节数组
     *
     * @param object 要转换的对象
     * @return 字节数组，如果转换失败则返回null
     */
    public static byte[] toJsonBytes(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsBytes(object);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON字节数组失败: {}", object.getClass().getSimpleName(), e);
            return null;
        }
    }

    // ==================== 反序列化方法 ====================

    /**
     * 将JSON字符串转换为指定类型的对象
     *
     * @param jsonString JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T fromJsonString(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON字符串转对象失败: {} -> {}", jsonString, clazz.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 将JSON字符串转换为指定类型的对象（使用TypeReference）
     *
     * @param jsonString JSON字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T fromJsonString(String jsonString, TypeReference<T> typeReference) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, typeReference);
        } catch (JsonProcessingException e) {
            log.error("JSON字符串转对象失败: {} -> {}", jsonString, typeReference.getType(), e);
            return null;
        }
    }

    /**
     * 将字节数组转换为指定类型的对象
     *
     * @param jsonBytes JSON字节数组
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象，如果转换失败则返回null
     */
    public static <T> T fromJsonBytes(byte[] jsonBytes, Class<T> clazz) {
        if (jsonBytes == null || jsonBytes.length == 0) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonBytes, clazz);
        } catch (Exception e) {
            log.error("JSON字节数组转对象失败: {} -> {}", new String(jsonBytes), clazz.getSimpleName(), e);
            return null;
        }
    }

    // ==================== 便捷方法 ====================

    /**
     * 将JSON字符串转换为Map
     *
     * @param jsonString JSON字符串
     * @return Map对象，如果转换失败则返回null
     */
    public static Map<String, Object> toMap(String jsonString) {
        return fromJsonString(jsonString, new TypeReference<Map<String, Object>>() {});
    }

    /**
     * 将JSON字符串转换为List
     *
     * @param jsonString JSON字符串
     * @param elementClass 列表元素类型
     * @param <T> 泛型类型
     * @return List对象，如果转换失败则返回null
     */
    public static <T> List<T> toList(String jsonString, Class<T> elementClass) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, 
                    OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, elementClass));
        } catch (JsonProcessingException e) {
            log.error("JSON字符串转List失败: {} -> List<{}>", jsonString, elementClass.getSimpleName(), e);
            return null;
        }
    }

    /**
     * 对象深拷贝（通过JSON序列化/反序列化实现）
     *
     * @param object 要拷贝的对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 拷贝后的对象，如果拷贝失败则返回null
     */
    public static <T> T deepCopy(Object object, Class<T> clazz) {
        if (object == null) {
            return null;
        }
        String jsonString = toJsonString(object);
        return fromJsonString(jsonString, clazz);
    }

    /**
     * 检查字符串是否为有效的JSON格式
     *
     * @param jsonString 要检查的字符串
     * @return 如果是有效的JSON则返回true
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }
        try {
            OBJECT_MAPPER.readTree(jsonString);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /**
     * 合并两个JSON对象（第二个对象的属性会覆盖第一个对象的同名属性）
     *
     * @param json1 第一个JSON字符串
     * @param json2 第二个JSON字符串
     * @return 合并后的JSON字符串，如果合并失败则返回null
     */
    public static String mergeJson(String json1, String json2) {
        try {
            Map<String, Object> map1 = toMap(json1);
            Map<String, Object> map2 = toMap(json2);
            
            if (map1 == null) {
                return json2;
            }
            if (map2 == null) {
                return json1;
            }
            
            map1.putAll(map2);
            return toJsonString(map1);
        } catch (Exception e) {
            log.error("JSON合并失败: {} + {}", json1, json2, e);
            return null;
        }
    }

    /**
     * 从JSON字符串中提取指定路径的值
     *
     * @param jsonString JSON字符串
     * @param path JSON路径（如 "user.name"）
     * @return 提取的值，如果提取失败则返回null
     */
    public static Object extractValue(String jsonString, String path) {
        try {
            Map<String, Object> map = toMap(jsonString);
            if (map == null) {
                return null;
            }
            
            String[] keys = path.split("\\.");
            Object current = map;
            
            for (String key : keys) {
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(key);
                } else {
                    return null;
                }
            }
            
            return current;
        } catch (Exception e) {
            log.error("JSON值提取失败: {} -> {}", jsonString, path, e);
            return null;
        }
    }
}
